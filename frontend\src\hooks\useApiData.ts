'use client';

import { useState, useEffect, useCallback } from 'react';
import { enhancedFetchFromApi } from '@/lib/enhancedApiUtils';
import { useError } from '@/contexts/ErrorContext';
import { AxiosRequestConfig } from 'axios';

interface UseApiDataOptions<T> {
  endpoint: string;
  params?: AxiosRequestConfig['params'];
  initialData?: T;
  enabled?: boolean;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  errorMessage?: string;
  retryOnError?: boolean;
  retryDelay?: number;
  maxRetries?: number;
}

interface UseApiDataResult<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  isRefetching: boolean;
}

/**
 * A custom hook for fetching data from the API with error handling and retries
 */
function useApiData<T>({
  endpoint,
  params,
  initialData = null as unknown as T,
  enabled = true,
  onSuccess,
  onError,
  errorMessage = 'Failed to fetch data',
  retryOnError = false,
  retryDelay = 3000,
  maxRetries = 3,
}: UseApiDataOptions<T>): UseApiDataResult<T> {
  const [data, setData] = useState<T | null>(initialData);
  const [isLoading, setIsLoading] = useState<boolean>(enabled);
  const [error, setError] = useState<Error | null>(null);
  const [retryCount, setRetryCount] = useState<number>(0);
  const [isRefetching, setIsRefetching] = useState<boolean>(false);
  const { addErrorLog } = useError();

  const fetchData = useCallback(async (isRefetch = false) => {
    if (isRefetch) {
      setIsRefetching(true);
    } else {
      setIsLoading(true);
    }
    
    setError(null);

    try {
      const options: AxiosRequestConfig = {};
      if (params) {
        options.params = params;
      }

      const result = await enhancedFetchFromApi<T>(endpoint, options, false);
      
      setData(result);
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      
      setError(error);
      addErrorLog(error, `useApiData(${endpoint})`);
      
      if (onError) {
        onError(error);
      }
      
      // Handle retry logic
      if (retryOnError && retryCount < maxRetries) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          fetchData(isRefetch);
        }, retryDelay);
      }
    } finally {
      if (isRefetch) {
        setIsRefetching(false);
      } else {
        setIsLoading(false);
      }
    }
  }, [endpoint, params, onSuccess, onError, retryOnError, retryCount, maxRetries, retryDelay, addErrorLog]);

  // Initial data fetch
  useEffect(() => {
    if (enabled) {
      fetchData();
    }
    
    // Reset retry count when endpoint or params change
    return () => {
      setRetryCount(0);
    };
  }, [enabled, fetchData]);

  // Function to manually refetch data
  const refetch = useCallback(async () => {
    setRetryCount(0); // Reset retry count on manual refetch
    await fetchData(true);
  }, [fetchData]);

  return { data, isLoading, error, refetch, isRefetching };
}

export default useApiData;
