'use client';

import React, { useEffect, useState, Suspense } from 'react';
import Link from 'next/link';
import { getStrapiContent } from '@/lib/strapi';

// Define types for condition data (copied from original page.tsx)
interface StrapiCondition {
  id: string;
  name: string;
  slug: string;
  // Add other fields if needed from Strapi
}

interface TransformedCondition {
  id: string;
  name: string;
  slug: string;
}

// Props for the ConditionsList component
interface ConditionsListProps {
  initialConditions?: TransformedCondition[];
}

// Transform Strapi condition data (copied from original page.tsx)
function transformConditionData(strapiCondition: StrapiCondition): TransformedCondition | null {
  if (!strapiCondition || !strapiCondition.name || !strapiCondition.slug) {
    console.warn(`Skipping invalid condition: ID ${strapiCondition?.id}`);
    return null;
  }
  return {
    id: strapiCondition.id,
    name: strapiCondition.name,
    slug: strapiCondition.slug,
  };
}

export default function ConditionsList({ initialConditions }: ConditionsListProps) {
  const [conditions, setConditions] = useState<TransformedCondition[]>(initialConditions || []);
  const [loading, setLoading] = useState(true); // Start true, effect will set to false if initial data is used
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (initialConditions && initialConditions.length > 0) {
      // Use initial data if provided
      setConditions(initialConditions);
      setLoading(false);
      return; // Don't fetch
    }

    // If no initial data, fetch from client
    async function fetchConditionsData() {
      console.log("Fetching conditions client-side...");
      setLoading(true);
      setError(null);
      try {
        const conditionResponse = await getStrapiContent.conditions.getAll({});
        const strapiConditions = conditionResponse?.data || [];
        const transformed = strapiConditions
          .map((condition: any) => transformConditionData(condition as StrapiCondition))
          .filter((cond: TransformedCondition | null): cond is TransformedCondition => cond !== null);
        
        if (transformed.length > 0) {
          setConditions(transformed);
        } else {
          console.warn("Client fetched conditions but processed list empty. Using empty list.");
          setConditions([]);
        }
      } catch (err) {
        console.error("Client error fetching conditions:", err);
        setError("Failed to load conditions.");
        setConditions([]);
      } finally {
        setLoading(false);
      }
    }
    fetchConditionsData();
  }, [initialConditions]); // Re-run if initialConditions prop changes

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"></div>
        ))}
      </div>
    );
  }

  if (error) {
    return <div className="text-center py-4 text-red-500">{error}</div>;
  }
  
  if (conditions.length === 0) {
    return <div className="text-center py-4 text-gray-500">No health conditions available at the moment.</div>;
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {conditions.map((condition: TransformedCondition) => (
        <Link
          key={condition.id}
          href={`/conditions/${condition.slug}`}
          className="bg-white hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors"
        >
          <span className="text-gray-800 font-medium">{condition.name}</span>
        </Link>
      ))}
    </div>
  );
}
