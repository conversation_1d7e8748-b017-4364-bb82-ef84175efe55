'use client';

import { AxiosError } from 'axios';

/**
 * Formats an API error for display to users
 */
export function formatApiErrorForUser(error: unknown): string {
  if (error instanceof AxiosError) {
    // Handle Axios errors
    if (!error.response) {
      return 'Network error. Please check your connection and try again.';
    }

    const status = error.response.status;
    
    // Handle common HTTP status codes
    if (status === 400) {
      return 'The request was invalid. Please check your input and try again.';
    } else if (status === 401) {
      return 'You need to be logged in to access this resource.';
    } else if (status === 403) {
      return 'You don\'t have permission to access this resource.';
    } else if (status === 404) {
      return 'The requested resource was not found.';
    } else if (status === 429) {
      return 'Too many requests. Please try again later.';
    } else if (status >= 500) {
      return 'The server encountered an error. Please try again later.';
    }

    // Try to extract error message from response
    const data = error.response.data;
    if (data?.error?.message) {
      return data.error.message;
    }
  }

  // Handle generic errors
  if (error instanceof Error) {
    return error.message;
  }

  // Fallback for unknown errors
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Determines if an error is a network error
 */
export function isNetworkError(error: unknown): boolean {
  if (error instanceof AxiosError) {
    return !error.response && !!error.request;
  }
  return false;
}

/**
 * Determines if an error is a server error (5xx)
 */
export function isServerError(error: unknown): boolean {
  if (error instanceof AxiosError && error.response) {
    return error.response.status >= 500;
  }
  return false;
}

/**
 * Determines if an error is a client error (4xx)
 */
export function isClientError(error: unknown): boolean {
  if (error instanceof AxiosError && error.response) {
    return error.response.status >= 400 && error.response.status < 500;
  }
  return false;
}

/**
 * Determines if an error is a specific HTTP status code
 */
export function isStatusCode(error: unknown, statusCode: number): boolean {
  if (error instanceof AxiosError && error.response) {
    return error.response.status === statusCode;
  }
  return false;
}

/**
 * Safely executes a function and returns a result or error
 */
export async function tryCatch<T>(
  fn: () => Promise<T>
): Promise<{ data: T | null; error: Error | null }> {
  try {
    const data = await fn();
    return { data, error: null };
  } catch (err) {
    const error = err instanceof Error ? err : new Error(String(err));
    return { data: null, error };
  }
}
