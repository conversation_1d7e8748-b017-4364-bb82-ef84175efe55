import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ClinicsList from '@/components/clinics/ClinicsList';

// Mock the getStrapiContent module
jest.mock('@/lib/strapi', () => ({
  getStrapiContent: {
    clinics: {
      getAll: jest.fn(),
    },
  },
}));

// Import the mocked module
import { getStrapiContent } from '@/lib/strapi';

describe('ClinicsList Component', () => {
  // Create a new QueryClient for each test
  const createTestQueryClient = () => new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state initially', () => {
    // Mock the API call to return a promise that never resolves
    (getStrapiContent.clinics.getAll as jest.Mock).mockReturnValue(new Promise(() => {}));
    
    render(
      <QueryClientProvider client={createTestQueryClient()}>
        <ClinicsList />
      </QueryClientProvider>
    );
    
    // Check that the loading state is rendered
    expect(screen.getByText(/Loading/i)).toBeInTheDocument();
  });

  it('renders clinics when data is loaded', async () => {
    // Mock the API call to return sample data
    const mockClinics = [
      {
        id: '1',
        name: 'Test Clinic 1',
        slug: 'test-clinic-1',
        description: 'Test description 1',
      },
      {
        id: '2',
        name: 'Test Clinic 2',
        slug: 'test-clinic-2',
        description: 'Test description 2',
      },
    ];
    
    (getStrapiContent.clinics.getAll as jest.Mock).mockResolvedValue(mockClinics);
    
    render(
      <QueryClientProvider client={createTestQueryClient()}>
        <ClinicsList />
      </QueryClientProvider>
    );
    
    // Check that the clinics are rendered
    expect(await screen.findByText('Test Clinic 1')).toBeInTheDocument();
    expect(screen.getByText('Test Clinic 2')).toBeInTheDocument();
  });

  it('renders error state when API call fails', async () => {
    // Mock the API call to throw an error
    (getStrapiContent.clinics.getAll as jest.Mock).mockRejectedValue(new Error('API error'));
    
    render(
      <QueryClientProvider client={createTestQueryClient()}>
        <ClinicsList />
      </QueryClientProvider>
    );
    
    // Check that the error state is rendered
    expect(await screen.findByText(/Error/i)).toBeInTheDocument();
  });

  it('renders empty state when no clinics are returned', async () => {
    // Mock the API call to return an empty array
    (getStrapiContent.clinics.getAll as jest.Mock).mockResolvedValue([]);
    
    render(
      <QueryClientProvider client={createTestQueryClient()}>
        <ClinicsList />
      </QueryClientProvider>
    );
    
    // Check that the empty state is rendered
    expect(await screen.findByText(/No clinics found/i)).toBeInTheDocument();
  });
});
