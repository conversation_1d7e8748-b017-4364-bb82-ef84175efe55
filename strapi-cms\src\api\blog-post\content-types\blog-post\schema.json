{"kind": "collectionType", "collectionName": "blog_posts", "info": {"singularName": "blog-post", "pluralName": "blog-posts", "displayName": "Blog Post", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "title", "required": true}, "publishDate": {"type": "date"}, "featuredImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "excerpt": {"type": "text"}, "content": {"type": "richtext", "required": true}, "blog_categories": {"type": "relation", "relation": "manyToMany", "target": "api::blog-category.blog-category", "inversedBy": "blog_posts"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "author_blogs": {"type": "relation", "relation": "manyToMany", "target": "api::author.author", "mappedBy": "blog_posts"}, "blog_tags": {"type": "relation", "relation": "manyToMany", "target": "api::blog-tag.blog-tag", "mappedBy": "blog_posts"}, "isFeatured": {"type": "boolean", "default": false}}}