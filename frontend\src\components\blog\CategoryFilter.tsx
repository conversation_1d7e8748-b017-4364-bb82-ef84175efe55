"use client";

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

interface Category {
  id: string;
  name: string;
  slug: string;
  count: number;
}

interface CategoryFilterProps {
  categories: Category[];
}

const CategoryFilter = ({ categories }: CategoryFilterProps) => {
  const searchParams = useSearchParams();
  const currentCategory = searchParams.get('category') || '';

  return (
    <div className="flex flex-wrap items-center gap-3">
      <span className="font-medium text-gray-700">Browse by:</span>
      <Link
        href="/blog"
        className={`px-3 py-1 rounded-full text-sm ${!currentCategory ? 'bg-emerald-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors'}`}
      >
        All
      </Link>
      {categories
        .filter(category => category.count > 0)
        .map(category => (
          <Link
            key={category.id}
            href={`/blog?category=${category.slug}`}
            className={`px-3 py-1 rounded-full text-sm ${currentCategory === category.slug ? 'bg-emerald-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors'}`}
          >
            {category.name}
          </Link>
        ))}
    </div>
  );
};

export default CategoryFilter;
