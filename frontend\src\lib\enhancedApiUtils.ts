import axios, { AxiosRequestConfig, AxiosError } from 'axios';
import { fetchFromApi, postTo<PERSON>pi, putToApi, deleteFrom<PERSON><PERSON> } from './apiUtils';

// Maximum number of retries for transient errors
const MAX_RETRIES = 2;

// Delay between retries in milliseconds (exponential backoff)
const getRetryDelay = (retryCount: number) => Math.pow(2, retryCount) * 1000;

// Error types that might be transient and worth retrying
const TRANSIENT_ERROR_CODES = [408, 429, 500, 502, 503, 504];

/**
 * Determines if an error is likely transient and worth retrying
 */
const isTransientError = (error: AxiosError): boolean => {
  if (!error.response) {
    // Network errors are often transient
    return true;
  }
  
  return TRANSIENT_ERROR_CODES.includes(error.response.status);
};

/**
 * Enhanced version of fetch<PERSON>rom<PERSON><PERSON> with retry logic for transient errors
 */
export const enhancedFetchFromApi = async <T>(
  endpoint: string,
  options: AxiosRequestConfig = {},
  isServerSide = typeof window === 'undefined',
  retryCount = 0
): Promise<T> => {
  try {
    return await fetchFromApi<T>(endpoint, options, isServerSide);
  } catch (error) {
    if (
      axios.isAxiosError(error) && 
      isTransientError(error) && 
      retryCount < MAX_RETRIES
    ) {
      // Wait before retrying (exponential backoff)
      const delay = getRetryDelay(retryCount);
      console.warn(`Retrying API request to ${endpoint} after ${delay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      
      // Retry the request
      return enhancedFetchFromApi<T>(endpoint, options, isServerSide, retryCount + 1);
    }
    
    // Format error for better debugging
    const formattedError = formatApiError(error, endpoint);
    throw formattedError;
  }
};

/**
 * Enhanced version of postToApi with retry logic for transient errors
 */
export const enhancedPostToApi = async <T>(
  endpoint: string,
  data: any,
  options: AxiosRequestConfig = {},
  isServerSide = typeof window === 'undefined',
  retryCount = 0
): Promise<T> => {
  try {
    return await postToApi<T>(endpoint, data, options, isServerSide);
  } catch (error) {
    if (
      axios.isAxiosError(error) && 
      isTransientError(error) && 
      retryCount < MAX_RETRIES
    ) {
      const delay = getRetryDelay(retryCount);
      console.warn(`Retrying API POST to ${endpoint} after ${delay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return enhancedPostToApi<T>(endpoint, data, options, isServerSide, retryCount + 1);
    }
    
    const formattedError = formatApiError(error, endpoint);
    throw formattedError;
  }
};

/**
 * Formats API errors for better debugging and user feedback
 */
export const formatApiError = (error: any, endpoint: string): Error => {
  let errorMessage = `API Error (${endpoint}): `;
  
  if (axios.isAxiosError(error)) {
    if (error.response) {
      // Server responded with an error status
      const status = error.response.status;
      const data = error.response.data;
      
      errorMessage += `${status} - ${data?.error?.message || 'Unknown server error'}`;
      
      // Create a custom error with additional properties
      const customError = new Error(errorMessage);
      (customError as any).status = status;
      (customError as any).endpoint = endpoint;
      (customError as any).responseData = data;
      
      return customError;
    } else if (error.request) {
      // Request was made but no response received
      errorMessage += 'No response received from server';
      
      const customError = new Error(errorMessage);
      (customError as any).endpoint = endpoint;
      (customError as any).isNetworkError = true;
      
      return customError;
    }
  }
  
  // For non-Axios errors
  errorMessage += error.message || 'Unknown error';
  return new Error(errorMessage);
};

// Export other enhanced API utilities as needed
export const enhancedPutToApi = async <T>(
  endpoint: string,
  data: any,
  options: AxiosRequestConfig = {},
  isServerSide = typeof window === 'undefined'
): Promise<T> => {
  try {
    return await putToApi<T>(endpoint, data, options, isServerSide);
  } catch (error) {
    const formattedError = formatApiError(error, endpoint);
    throw formattedError;
  }
};

export const enhancedDeleteFromApi = async <T>(
  endpoint: string,
  options: AxiosRequestConfig = {},
  isServerSide = typeof window === 'undefined'
): Promise<T> => {
  try {
    return await deleteFromApi<T>(endpoint, options, isServerSide);
  } catch (error) {
    const formattedError = formatApiError(error, endpoint);
    throw formattedError;
  }
};
