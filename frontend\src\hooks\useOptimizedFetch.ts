'use client';

import { useState, useEffect } from 'react';
import { AxiosRequestConfig } from 'axios';
import { fetchWithOptimization, fetchContentType } from '@/lib/optimizedFetch';

/**
 * Hook for fetching data with optimized caching
 * 
 * @param endpoint API endpoint
 * @param options Axios request options
 * @param ttl Cache TTL in milliseconds
 * @returns Object with data, loading state, error, and refetch function
 */
export function useOptimizedFetch<T>(
  endpoint: string,
  options: AxiosRequestConfig = {},
  ttl: number = 5 * 60 * 1000 // 5 minutes default
) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = async (bypassCache = false) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await fetchWithOptimization<T>(endpoint, options, ttl, bypassCache);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [endpoint, JSON.stringify(options)]);

  const refetch = () => fetchData(true);

  return { data, isLoading, error, refetch };
}

/**
 * Hook for fetching content type data with appropriate cache times
 * 
 * @param contentType Content type to fetch
 * @param options Axios request options
 * @returns Object with data, loading state, error, and refetch function
 */
export function useContentType<T>(
  contentType: string,
  options: AxiosRequestConfig = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = async (bypassCache = false) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await fetchContentType<T>(contentType, options);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [contentType, JSON.stringify(options)]);

  const refetch = () => fetchData(true);

  return { data, isLoading, error, refetch };
}

/**
 * Hook for fetching global settings
 */
export function useGlobalSettings() {
  return useContentType('global-setting', { params: { populate: '*' } });
}

/**
 * Hook for fetching categories
 */
export function useCategories(pageSize = 10) {
  return useContentType('categories', { 
    params: { 
      pagination: { pageSize },
      populate: '*'
    } 
  });
}

/**
 * Hook for fetching specialties
 */
export function useSpecialties(pageSize = 10) {
  return useContentType('specialties', { 
    params: { 
      pagination: { pageSize },
      populate: '*'
    } 
  });
}

/**
 * Hook for fetching featured clinics
 */
export function useFeaturedClinics(pageSize = 4) {
  return useContentType('clinics', { 
    params: { 
      filters: { isFeatured: { $eq: true } },
      pagination: { pageSize },
      populate: '*'
    } 
  });
}

/**
 * Hook for fetching featured practitioners
 */
export function useFeaturedPractitioners(pageSize = 4) {
  return useContentType('practitioners', { 
    params: { 
      filters: { isFeatured: { $eq: true } },
      pagination: { pageSize },
      populate: '*'
    } 
  });
}

/**
 * Hook for fetching featured blog posts
 */
export function useFeaturedBlogPosts(pageSize = 3) {
  return useContentType('blog-posts', { 
    params: { 
      filters: { isFeatured: { $eq: true } },
      sort: 'publishDate:desc',
      pagination: { pageSize },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        }
      }
    } 
  });
}

/**
 * Hook for fetching latest blog posts
 */
export function useLatestBlogPosts(pageSize = 3) {
  return useContentType('blog-posts', { 
    params: { 
      sort: 'publishDate:desc',
      pagination: { pageSize },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        }
      }
    } 
  });
}

/**
 * Hook for fetching popular blog posts
 */
export function usePopularBlogPosts(pageSize = 3) {
  return useContentType('blog-posts', { 
    params: { 
      sort: 'viewCount:desc',
      pagination: { pageSize },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        }
      }
    } 
  });
}
