import React from 'react';
import { render, screen } from '@testing-library/react';
import BlogPostHeader from '@/components/blog/BlogPostHeader';
import { BlogPost } from '@/lib/blogUtils';

// Mock formatDate function
jest.mock('@/lib/blogUtils', () => ({
  ...jest.requireActual('@/lib/blogUtils'),
  formatDate: jest.fn(() => 'June 15, 2023')
}));

describe('BlogPostHeader Component', () => {
  const mockPost: BlogPost = {
    id: '1',
    title: 'Test Blog Post',
    slug: 'test-blog-post',
    content: '<p>Test content</p>',
    excerpt: 'Test excerpt',
    featured_image: 'https://example.com/image.jpg',
    published_at: '2023-06-15T10:30:00Z',
    reading_time: 5,
    author: {
      id: '2',
      name: 'Test Author',
      slug: 'test-author',
      profile_picture: 'https://example.com/profile.jpg',
      bio: 'Author bio'
    },
    categories: [
      {
        id: '3',
        name: 'Test Category',
        slug: 'test-category'
      },
      {
        id: '4',
        name: 'Another Category',
        slug: 'another-category'
      }
    ],
    tags: ['tag1', 'tag2'],
    related_posts: []
  };

  test('renders the blog post title', () => {
    render(<BlogPostHeader post={mockPost} />);
    expect(screen.getByText('Test Blog Post')).toBeInTheDocument();
  });

  test('renders the formatted date', () => {
    render(<BlogPostHeader post={mockPost} />);
    expect(screen.getByText('June 15, 2023')).toBeInTheDocument();
  });

  test('renders the author name with link', () => {
    render(<BlogPostHeader post={mockPost} />);
    const authorLink = screen.getByText('Test Author');
    expect(authorLink).toBeInTheDocument();
    expect(authorLink.closest('a')).toHaveAttribute('href', '/blog/authors/test-author');
  });

  test('renders the reading time', () => {
    render(<BlogPostHeader post={mockPost} />);
    expect(screen.getByText('5 min read')).toBeInTheDocument();
  });

  test('renders all categories with links', () => {
    render(<BlogPostHeader post={mockPost} />);
    
    const category1 = screen.getByText('Test Category');
    expect(category1).toBeInTheDocument();
    expect(category1.closest('a')).toHaveAttribute('href', '/blog/categories/test-category');
    
    const category2 = screen.getByText('Another Category');
    expect(category2).toBeInTheDocument();
    expect(category2.closest('a')).toHaveAttribute('href', '/blog/categories/another-category');
  });

  test('renders the featured image when provided', () => {
    render(<BlogPostHeader post={mockPost} />);
    const image = screen.getByAltText('Test Blog Post');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/image.jpg');
  });

  test('renders a placeholder when featured image is not provided', () => {
    const postWithoutImage = {
      ...mockPost,
      featured_image: null
    };
    
    render(<BlogPostHeader post={postWithoutImage} />);
    expect(screen.getByText('Featured Image')).toBeInTheDocument();
  });

  test('does not render categories section when no categories are provided', () => {
    const postWithoutCategories = {
      ...mockPost,
      categories: []
    };
    
    render(<BlogPostHeader post={postWithoutCategories} />);
    // Check that the categories wrapper is not rendered
    expect(screen.queryByText('Test Category')).not.toBeInTheDocument();
    expect(screen.queryByText('Another Category')).not.toBeInTheDocument();
  });
});
