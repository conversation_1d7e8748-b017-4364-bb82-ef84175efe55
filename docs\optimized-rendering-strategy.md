# Optimized Rendering Strategy for Natural Healing Now

This document outlines the rendering strategy implemented for the Natural Healing Now website to optimize performance and reduce server-side rendering.

## Overview

The website uses a combination of rendering strategies to optimize performance:

1. **Incremental Static Regeneration (ISR) with On-Demand Revalidation**
   - Pages are statically generated at build time
   - Content is revalidated only when triggered by webhooks from Strapi
   - No automatic revalidation period (revalidate: false)

2. **Static Generation (SSG)**
   - Used for truly static pages that rarely change
   - Generated at build time

3. **Server Actions/API Routes**
   - Used for search/filtering functionality
   - Handles dynamic data needs without full SSR

## Implementation Details

### 1. ISR with On-Demand Revalidation

All dynamic content pages use ISR with on-demand revalidation:

```typescript
// Enable ISR with on-demand revalidation
// No revalidation period - will only revalidate when triggered by webhook
export const revalidate = false;
// Allow dynamic params to be generated on-demand
export const dynamicParams = true;
```

This approach is used for:
- Blog posts (`/blog/[slug]`)
- Blog category pages (`/blog/categories/[slug]`)
- Clinic pages (`/clinics/[slug]`)
- Practitioner pages (`/practitioners/[slug]`)
- Specialty pages (`/specialities/[slug]`)
- Condition pages (`/conditions/[slug]`)
- Homepage

### 2. Caching Strategy

The application uses React Query with optimized cache settings:

```typescript
// Default stale time (24 hours)
export const DEFAULT_STALE_TIME = 24 * 60 * 60 * 1000;

// Default cache time (7 days)
export const DEFAULT_CACHE_TIME = 7 * 24 * 60 * 60 * 1000;
```

Content-specific cache times:

| Content Type | Stale Time | Cache Time |
|--------------|------------|------------|
| Global Settings | Infinity | 7 days |
| Categories | Infinity | 7 days |
| Specialties | Infinity | 7 days |
| Clinics | Infinity | 7 days |
| Practitioners | Infinity | 7 days |
| Blog Posts | Infinity | 7 days |

### 3. On-Demand Revalidation

Content is revalidated when it changes in Strapi using webhooks:

1. Strapi webhooks are configured to call the `/api/revalidate` endpoint
2. The revalidation API validates the request and revalidates the affected pages
3. Only the specific pages that need updating are revalidated

Example webhook payload:
```json
{
  "token": "your_revalidation_token",
  "contentType": "blog-post",
  "id": "123",
  "slug": "example-post"
}
```

### 4. Server Actions for Dynamic Data

For truly dynamic data needs (search, filtering), the application uses Server Actions:

```typescript
// Example server action for search
export async function searchClinics(query: string) {
  "use server";
  
  // Fetch data from Strapi
  const results = await fetchAPI('/clinics', {
    params: {
      filters: {
        $or: [
          { name: { $containsi: query } },
          { description: { $containsi: query } }
        ]
      }
    }
  });
  
  return results;
}
```

## Benefits

This rendering strategy provides several benefits:

1. **Reduced Server Load**: Pages are generated statically and served from the CDN
2. **Improved Performance**: Users get fast, cached content
3. **Lower Costs**: Fewer server-side renders means lower Vercel compute costs
4. **Better SEO**: Static pages are fully rendered for search engines
5. **Resilience**: The site works even if Strapi is temporarily unavailable

## Webhook Setup

To enable on-demand revalidation, Strapi webhooks must be configured to call the revalidation API. See [Strapi Webhook Setup](./strapi-webhook-setup.md) for detailed instructions.

## Monitoring and Troubleshooting

Monitor the effectiveness of this strategy by:

1. Checking Vercel Analytics for page performance metrics
2. Monitoring Strapi webhook logs to ensure they're firing correctly
3. Checking the Next.js logs for revalidation events

If pages aren't updating:
1. Verify the webhook is configured correctly
2. Check that the revalidation token matches
3. Ensure the revalidation API is being called successfully
