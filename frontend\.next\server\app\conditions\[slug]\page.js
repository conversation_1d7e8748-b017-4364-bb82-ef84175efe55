"use strict";(()=>{var e={};e.id=5355,e.ids=[5355],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},14874:(e,t,i)=>{i.r(t),i.d(t,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=i(65239),r=i(48088),n=i(31369),o=i(30893),s={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>o[e]);i.d(t,s);let l={children:["",{children:["conditions",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,22277)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\conditions\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(i.bind(i,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(i.bind(i,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\conditions\\[slug]\\page.tsx"],d={require:i,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/conditions/[slug]/page",pathname:"/conditions/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},22277:(e,t,i)=>{i.r(t),i.d(t,{default:()=>$,dynamic:()=>m,dynamicParams:()=>f,generateMetadata:()=>w,generateStaticParams:()=>x,revalidate:()=>h});var a=i(37413),r=i(4536),n=i.n(r),o=i(73993),s=i(14007),l=i(58446),c=i(39916),d=i(10592),p=i(82158),u=i(74124),g=i(69290);let m="force-static",h=43200,f=!0;async function x(){try{let e=await l.$.conditions.getAllSlugs({cache:"force-cache",next:{revalidate:43200,tags:["strapi-conditions-slugs"]}});if(e&&e.data&&Array.isArray(e.data))return console.log(`Pre-rendering ${e.data.length} condition detail pages`),e.data.filter(e=>null!==e&&"string"==typeof e.slug).map(e=>({slug:e.slug}));return[]}catch(e){return console.error("Error fetching condition slugs for generateStaticParams:",e),[]}}let y="https://nice-badge-2130241d6c.strapiapp.com",b=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";console.log(`Using site URL for canonical URLs: ${b}`);let v=e=>{if(console.log("getStrapiMediaUrl input:",JSON.stringify({type:typeof e,isNull:null===e,isUndefined:void 0===e,value:e},null,2)),!e)return null;if("string"==typeof e)return(console.log("URL is a string:",e),e.startsWith("http://")||e.startsWith("https://"))?e:`${y}${e}`;if(e&&"object"==typeof e){console.log("URL is an object with keys:",Object.keys(e));let t=e.url||e.data?.attributes?.url||e.data?.url||null;if(t)return(console.log("Extracted URL from object:",t),t.startsWith("http://")||t.startsWith("https://"))?t:`${y}${t}`}return console.warn("Could not extract URL from:",e),null};async function j(e){try{let t=await l.$.conditions.getBySlug(e,{next:{tags:["strapi-conditions-list",`strapi-condition-${e}`],revalidate:43200},cache:"force-cache"});if(t?.data&&Array.isArray(t.data)&&t.data.length>0)return t.data[0];return null}catch(t){return console.error(`Error fetching condition with slug ${e}:`,t),null}}async function w({params:e}){let t,i=await e,a=await j(i.slug);if(!a)return{title:"Condition Not Found | Natural Healing Now",description:"The requested condition could not be found."};let r=a?.attributes||a||{},n=r.seo,o=`${r.name||a.name||"Condition"} | Natural Healing Now`,s=r.description||a.description||`Learn about ${r.name||a.name||"this condition"} and find related clinics and practitioners.`,l=n?.metaTitle||o,c=n?.metaDescription||s,d=v(n?.metaImage),u=`/conditions/${a.slug}`,g=n?.canonicalURL||(b?`${b}${u}`:u);n?.openGraph?.image?t=(0,p.Rb)(v(n.openGraph.image)):n?.metaImage&&(t=(0,p.Rb)(d)),console.log("Condition og:image:",{openGraphImage:n?.openGraph?.image,metaImage:n?.metaImage,ogImageUrl:t});let m=n?.openGraph?{title:n.openGraph.title||l,description:n.openGraph.description||c,url:n.openGraph.url||g,type:n.openGraph.type||"website",siteName:n.openGraph.siteName||"Natural Healing Now",...t&&{images:[{url:t}]}}:{title:l,description:c,url:g,type:"website",siteName:"Natural Healing Now",...t&&{images:[{url:t}]}};return{title:l,description:c,alternates:{canonical:g},openGraph:m,twitter:{card:"summary_large_image",title:n?.openGraph?.title||l,description:n?.openGraph?.description||c,images:t?[t]:[]}}}async function $({params:e,searchParams:t}){let i=await e,r=i.slug,p=await t,m=p?.query||"",h=p?.location||"",f=Number(p?.page)||1,x="practitioners"===(p?.tab||"clinics")?"practitioners":"clinics",y=await j(r);y||(0,c.notFound)();let{name:b="Unnamed Condition",description:w="No description available.",featuredImage:$=null}=y,N=v($),P=0,C=0,q=1,S=m||h?{cache:"no-store"}:{next:{tags:["strapi-conditions-list",`strapi-condition-${r}`],revalidate:43200},cache:"force-cache"};try{let e=await l.$.clinics.getAll({conditionSlug:r,pagination:{page:1,pageSize:1}});P=e?.meta?.pagination?.total||0;let t=await l.$.practitioners.getAll({conditionSlug:r,pagination:{page:1,pageSize:1}});C=t?.meta?.pagination?.total||0,console.log(`Counts for condition ${r}: Clinics=${P}, Practitioners=${C}`)}catch(e){console.error("Error fetching counts for condition page:",e)}let _=[],I=1;try{console.log(`Fetching clinics for condition ${r}, activeTab: ${x}, query: ${m}, location: ${h}, currentPage: ${f}`);let e=await l.$.clinics.getAll({conditionSlug:r,query:"clinics"===x?m:"",location:"clinics"===x?h:"",page:"clinics"===x?f:1,...S});console.log(`Clinic response data length for condition ${r}: ${e?.data?.length||0}`),_=(e?.data||[]).map(e=>{let t=e.attributes||e;e?.id&&!t.id&&(t.id=e.id);if(console.log("Raw clinic data structure:",JSON.stringify({hasData:!!t,id:t?.id,name:t?.name,keys:t?Object.keys(t):[]},null,2)),!t)return console.warn("Skipping clinic due to missing data."),null;if(!t.id||!t.name)return console.warn(`Skipping invalid clinic data: ID ${t?.id}`),null;let i=v(t.logo),a=v(t.featuredImage);return{id:String(t.id),name:t.name,slug:t.slug||`clinic-${t.id}`,description:t.description,logo:i,featuredImage:a,address:t.address||{city:"Unknown",stateProvince:"N/A"},contactInfo:t.contactInfo,isVerified:t.isVerified||!1}}).filter(Boolean),console.log(`Transformed clinics array length for condition ${r}: ${_.length}`),I=e?.meta?.pagination?.pageCount||1}catch(e){console.error(`Error fetching clinics for condition ${r}:`,e)}let A=[],k=1;try{console.log(`Fetching practitioners for condition ${r}, activeTab: ${x}, query: ${m}, location: ${h}, currentPage: ${f}`);let e=await l.$.practitioners.getAll({conditionSlug:r,query:"practitioners"===x?m:"",location:"practitioners"===x?h:"",page:"practitioners"===x?f:1,...S});if(console.log(`Practitioner raw response data length for condition ${r}: ${e?.data?.length||0}`),e?.data&&e.data.length>0){let t=e.data[0];console.log(`First raw practitioner item structure for condition ${r}:`,JSON.stringify({id:t.id,hasAttributes:!!t.attributes,attributesType:t.attributes?typeof t.attributes:"N/A",keys:Object.keys(t),attributeKeys:t.attributes?Object.keys(t.attributes):[]},null,2))}A=(e?.data||[]).map(e=>{let t=e.attributes||e;return e?.id&&!t.id&&(t.id=e.id),t.id&&t.name||console.warn(`Practitioner data missing id or name for condition ${r}. ID: ${t.id}, Name: ${t.name}. Full item:`,JSON.stringify(e)),(console.log("Raw practitioner data structure:",JSON.stringify({hasData:!!t,id:t?.id,name:t?.name,keys:t?Object.keys(t):[]},null,2)),t)?t.id&&t.name?{id:String(t.id),name:t.name,slug:t.slug||`practitioner-${t.id}`,title:t.title,qualifications:t.qualifications,profilePicture:v(t.profilePicture),isVerified:t.isVerified||!1,bio:t.bio}:(console.warn(`Skipping invalid practitioner data: ID ${t?.id}`),null):(console.warn("Skipping practitioner due to missing data."),null)}).filter(e=>null!==e),console.log(`Transformed practitioners array length for condition ${r}: ${A.length}`),k=e?.meta?.pagination?.pageCount||1}catch(e){console.error(`Error fetching practitioners for condition ${r}:`,e)}q="clinics"===x?I:k;let G=(y?.attributes||y||{}).seo,R=null;if(G?.structuredData){if("string"==typeof G.structuredData)R=G.structuredData;else if("object"==typeof G.structuredData)try{R=JSON.stringify(G.structuredData)}catch(e){console.error("Failed to stringify structuredData object:",e)}}return R||(R=JSON.stringify({"@context":"https://schema.org","@type":"MedicalCondition",name:b,description:w,url:`${process.env.NEXT_PUBLIC_SITE_URL||""}/conditions/${r}`,mainEntityOfPage:{"@type":"WebPage","@id":`${process.env.NEXT_PUBLIC_SITE_URL||""}/conditions/${r}`}})),(0,a.jsxs)(a.Fragment,{children:[R&&(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:R}}),(0,a.jsx)("div",{className:"bg-gray-100 py-3",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(n(),{href:"/",className:"hover:text-emerald-600",children:"Home"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)("span",{className:"text-gray-800",children:b})," "]})})}),(0,a.jsxs)("div",{className:"bg-emerald-600 text-white py-12 relative overflow-hidden",children:[N&&(0,a.jsx)("div",{className:"absolute inset-0 opacity-20 bg-cover bg-center",style:{backgroundImage:`url(${N})`}}),(0,a.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:b}),w&&(0,a.jsx)("p",{className:"text-lg max-w-3xl mb-4",children:w}),(0,a.jsxs)("div",{className:"flex gap-4 text-sm",children:[(0,a.jsxs)("span",{children:[P," Related Clinics"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[C," Related Practitioners"]})]})]})]}),(0,a.jsx)("div",{className:"bg-white shadow-sm sticky top-0 z-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(d.default,{placeholder:`Search clinics/practitioners for ${b}...`,paramName:"query"})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(d.default,{placeholder:"City, state, or zip code",paramName:"location",icon:(0,a.jsx)(o.HzC,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})})}),(0,a.jsx)("div",{children:(0,a.jsxs)("button",{className:"w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200",children:[(0,a.jsx)(o.K7R,{}),(0,a.jsx)("span",{children:"Filters"})]})})]})})}),(0,a.jsx)("div",{className:"py-8 bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)(u.default,{slug:i.slug,pageType:"conditions",clinicCount:P,practitionerCount:C,initialTab:x}),(0,a.jsx)(g.default,{clinics:_,practitioners:A,totalPages:q,initialTab:x})]})}),(0,a.jsx)(s.A,{})]})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},94735:e=>{e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[7719,1330,3376,6391,2975,255,8446,270,7424,7685],()=>i(14874));module.exports=a})();