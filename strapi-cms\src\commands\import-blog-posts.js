/**
 * `import-blog-posts` command
 *
 * This command imports blog posts from a CSV file into the Strapi database.
 */

module.exports = {
  command: 'import-blog-posts',
  description: 'Import blog posts from CSV file',
  run: async ({ strapi }) => {
    try {
      const importBlogPosts = require('../../scripts/import-blog-posts');
      await importBlogPosts(strapi);
      process.exit(0);
    } catch (error) {
      console.error('Import failed:', error);
      process.exit(1);
    }
  }
};
