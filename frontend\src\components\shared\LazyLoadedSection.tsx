'use client';

import { useRef, useState, useEffect } from 'react';

interface LazyLoadedSectionProps {
  /**
   * The content to be lazily loaded
   */
  children: React.ReactNode;
  
  /**
   * Threshold for intersection observer (0-1)
   * 0 means the section starts loading as soon as any part enters the viewport
   * 1 means the section only loads when fully in the viewport
   */
  threshold?: number;
  
  /**
   * Root margin for intersection observer
   * Margin around the root element (viewport by default)
   * e.g., "100px" will start loading when within 100px of the viewport
   */
  rootMargin?: string;
  
  /**
   * Placeholder to show while loading
   */
  placeholder?: React.ReactNode;
  
  /**
   * Whether to disable lazy loading
   */
  disabled?: boolean;
  
  /**
   * Additional class name for the section
   */
  className?: string;
  
  /**
   * ID for the section
   */
  id?: string;
}

/**
 * LazyLoadedSection - A component that lazily loads its children when scrolled into view
 * 
 * This component uses the Intersection Observer API to detect when the section
 * is visible in the viewport, and only then renders its children.
 * 
 * @example
 * ```tsx
 * <LazyLoadedSection
 *   threshold={0.1}
 *   rootMargin="200px"
 *   placeholder={<SkeletonLoader type="card" count={3} />}
 * >
 *   <HeavyComponent />
 * </LazyLoadedSection>
 * ```
 */
const LazyLoadedSection: React.FC<LazyLoadedSectionProps> = ({
  children,
  threshold = 0.1,
  rootMargin = '200px',
  placeholder,
  disabled = false,
  className = '',
  id,
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(disabled);
  
  useEffect(() => {
    // If disabled, always show content
    if (disabled) {
      setIsVisible(true);
      return;
    }
    
    const currentRef = sectionRef.current;
    if (!currentRef) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Once visible, no need to observe anymore
          observer.unobserve(currentRef);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );
    
    observer.observe(currentRef);
    
    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [disabled, threshold, rootMargin]);
  
  return (
    <div ref={sectionRef} className={className} id={id}>
      {isVisible ? children : placeholder || null}
    </div>
  );
};

export default LazyLoadedSection;
