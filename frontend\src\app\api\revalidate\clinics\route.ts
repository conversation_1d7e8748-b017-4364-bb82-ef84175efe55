import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag, revalidatePath } from 'next/cache';

// Ensure the secret token is set in environment variables
const REVALIDATE_SECRET = process.env.STRAPI_REVALIDATE_SECRET || process.env.REVALIDATE_TOKEN || process.env.PREVIEW_SECRET;

export async function POST(request: NextRequest) {
  if (!REVALIDATE_SECRET) {
    console.error('CRITICAL: STRAPI_REVALIDATE_SECRET, REVALIDATE_TOKEN, or PREVIEW_SECRET is not set. Revalidation endpoint is disabled.');
    return NextResponse.json({ message: 'Revalidation secret not configured.' }, { status: 500 });
  }

  // Check for the secret token in various possible headers
  const secretFromHeader = request.headers.get('X-Revalidate-Secret') ||
                          request.headers.get('x-revalidate-secret') ||
                          (request.headers.get('Authorization')?.replace('Bearer ', '')) ||
                          (request.headers.get('authorization')?.replace('Bearer ', '')) ||
                          request.headers.get('x-webhook-secret');

  // Also check for token in the request body
  let secretFromBody;
  let body;
  try {
    // Clone the request to avoid consuming the body
    const clonedRequest = request.clone();
    body = await clonedRequest.json();
    secretFromBody = body.token || body.secret;
    console.log('Webhook payload:', JSON.stringify(body, null, 2));
  } catch (e) {
    console.log('No JSON body or failed to parse body');
    body = {};
  }

  // Check URL params for secret (for GET requests)
  const url = new URL(request.url);
  const secretFromUrl = url.searchParams.get('secret');

  // Use any available secret
  const providedSecret = secretFromHeader || secretFromBody || secretFromUrl;

  if (providedSecret !== REVALIDATE_SECRET) {
    console.warn('Invalid revalidation attempt: Incorrect or missing authentication.');
    console.log('Received headers:', Object.fromEntries(request.headers.entries()));
    return NextResponse.json({ message: 'Invalid token' }, { status: 401 });
  }

  try {
    // Log the raw request for debugging
    console.log('Received webhook request for clinics revalidation');

    // Extract data from Strapi webhook payload
    const { event, model, entry } = body || {};

    // Log the extracted data
    console.log('Webhook event:', event);
    console.log('Webhook model:', model);
    console.log('Webhook entry:', entry ? `ID: ${entry.id}` : 'No entry data');

    // Check if this is a clinic-related event
    // Strapi 5 might send the model as 'api::clinic.clinic' or just 'clinic'
    const isClinicModel = !model || // If no model specified, assume it's for clinics
                          model === 'clinic' ||
                          model === 'api::clinic.clinic' ||
                          (typeof model === 'string' && model.includes('clinic'));

    if (!isClinicModel && model) { // Only skip if model is specified and not clinic-related
      console.log(`Revalidation skipped: Not a clinic model. Received: ${model}`);
      return NextResponse.json({ message: `Revalidation skipped: Not a clinic model. Received: ${model}` }, { status: 200 });
    }

    const tagsToRevalidate: string[] = [];

    // Handle specific clinic if slug is available
    if (entry && entry.slug) {
      const clinicSlug = entry.slug;
      const clinicTag = `strapi-clinic-${clinicSlug}`;
      tagsToRevalidate.push(clinicTag);
      console.log(`Adding tag for specific clinic: ${clinicTag} (slug: ${clinicSlug})`);
    }

    // Always revalidate the list of clinic slugs
    tagsToRevalidate.push('strapi-clinics-slugs');
    console.log('Adding tag: strapi-clinics-slugs');

    // Revalidate general clinic listing pages
    tagsToRevalidate.push('strapi-clinics-list');
    console.log('Adding tag: strapi-clinics-list');

    // Revalidate all paginated clinic list pages (assuming max 10 pages)
    for (let i = 1; i <= 10; i++) {
      tagsToRevalidate.push(`strapi-clinics-page-${i}`);
    }
    console.log('Adding tags for paginated clinic pages (1-10)');

    // Revalidate specialties and conditions used on the clinics page
    tagsToRevalidate.push('strapi-specialties');
    tagsToRevalidate.push('strapi-conditions');
    tagsToRevalidate.push('strapi-clinics-locations');
    console.log('Adding tags for specialties, conditions, and locations');

    // Revalidate both by tag and by path for maximum compatibility
    if (tagsToRevalidate.length > 0) {
      console.log('Revalidating tags:', tagsToRevalidate.join(', '));

      // Revalidate by tags
      for (const tag of tagsToRevalidate) {
        try {
          revalidateTag(tag);
          console.log(`Successfully revalidated tag: ${tag}`);
        } catch (error) {
          console.error(`Error revalidating tag ${tag}:`, error);
        }
      }

      // Also revalidate by path for good measure
      try {
        revalidatePath('/clinics');
        console.log('Successfully revalidated path: /clinics');

        // If we have a specific clinic slug, revalidate its page too
        if (entry && entry.slug) {
          revalidatePath(`/clinics/${entry.slug}`);
          console.log(`Successfully revalidated path: /clinics/${entry.slug}`);
        }
      } catch (error) {
        console.error('Error revalidating paths:', error);
      }

      return NextResponse.json({
        revalidated: true,
        revalidatedTags: tagsToRevalidate,
        revalidatedPaths: entry && entry.slug ? ['/clinics', `/clinics/${entry.slug}`] : ['/clinics'],
        timestamp: new Date().toISOString()
      });
    } else {
      console.log('No tags to revalidate.');
      return NextResponse.json({
        revalidated: false,
        message: 'No tags to revalidate.'
      });
    }
  } catch (error: any) {
    console.error('Error during clinics revalidation:', error);
    return NextResponse.json({
      message: 'Error revalidating clinics',
      error: error.message
    }, { status: 500 });
  }
}

// Optional: GET handler for testing or manual trigger if needed (secure appropriately)
export async function GET(request: NextRequest) {
  if (!REVALIDATE_SECRET) {
    return NextResponse.json({ message: 'Revalidation secret not configured.' }, { status: 500 });
  }

  const secretFromHeader = request.headers.get('X-Revalidate-Secret') || request.headers.get('x-revalidate-secret');
  const requestUrl = new URL(request.url);
  const tag = requestUrl.searchParams.get('tag');
  const slug = requestUrl.searchParams.get('slug');
  const secretFromQuery = requestUrl.searchParams.get('secret');

  // Check authentication
  if (secretFromHeader !== REVALIDATE_SECRET && secretFromQuery !== REVALIDATE_SECRET) {
    console.warn('Invalid GET revalidation attempt: Incorrect or missing authentication.');
    return NextResponse.json({ message: 'Invalid token' }, { status: 401 });
  }

  try {
    const tagsToRevalidate: string[] = [];
    const pathsToRevalidate: string[] = [];

    // If a specific tag is provided, revalidate just that tag
    if (tag) {
      tagsToRevalidate.push(tag);
    } else {
      // Otherwise revalidate all clinic-related tags
      tagsToRevalidate.push('strapi-clinics-list');
      tagsToRevalidate.push('strapi-clinics-slugs');

      // If a slug is provided, revalidate that specific clinic
      if (slug) {
        tagsToRevalidate.push(`strapi-clinic-${slug}`);
        pathsToRevalidate.push(`/clinics/${slug}`);
      } else {
        // Otherwise revalidate all paginated clinic list pages
        for (let i = 1; i <= 10; i++) {
          tagsToRevalidate.push(`strapi-clinics-page-${i}`);
        }

        // Also revalidate specialties and conditions used on the clinics page
        tagsToRevalidate.push('strapi-specialties');
        tagsToRevalidate.push('strapi-conditions');
        tagsToRevalidate.push('strapi-clinics-locations');
      }
    }

    // Always revalidate the clinics list path
    pathsToRevalidate.push('/clinics');

    // Perform the revalidation
    for (const tagToRevalidate of tagsToRevalidate) {
      revalidateTag(tagToRevalidate);
      console.log(`Manual revalidation successful for tag: ${tagToRevalidate}`);
    }

    for (const pathToRevalidate of pathsToRevalidate) {
      revalidatePath(pathToRevalidate);
      console.log(`Manual revalidation successful for path: ${pathToRevalidate}`);
    }

    return NextResponse.json({
      revalidated: true,
      revalidatedTags: tagsToRevalidate,
      revalidatedPaths: pathsToRevalidate,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error(`Error during manual revalidation:`, error);
    return NextResponse.json({ message: 'Error revalidating', error: error.message }, { status: 500 });
  }
}