# Holistic Health Directory Project Structure

## Overview

This document outlines the file structure of the Holistic Health Directory project, a web-based platform that connects users with clinics and practitioners specializing in holistic health therapies. The project includes directory functionality, blog integration, SEO optimization, and mapping features.

## Project Structure

```
/
├── api_reference.md           # API documentation and reference
├── changelog.md              # Project changelog
├── Project_requirement.md    # Project requirements and specifications
├── package-lock.json         # NPM package lock file
│
├── backend/                  # Backend services using
 Strapi CMS
│   ├── api/                  # API endpoints and controllers
│   ├── tests/                # Backend tests (unit, integration)
│   └── config/               # Backend configuration files
│
├── frontend/                 # Next.js frontend application
│   ├── app/                  # Next.js app directory (App Router)
│   │   ├── auth/             # Authentication pages
│   │   ├── blog/             # Blog pages
│   │   ├── categories/       # Category pages
│   │   ├── clinics/          # Clinic profile pages
│   │   ├── conditions/       # Condition-specific pages
│   │   ├── practitioners/    # Practitioner profile pages
│   │   ├── search/           # Search functionality
│   │   ├── services/         # Service-specific pages
│   │   ├── specialties/      # Specialty-specific pages
│   │   ├── test/             # Test pages
│   │   ├── layout.tsx        # Root layout component
│   │   └── page.tsx          # Home page component
│   │
│   ├── components/           # Reusable React components
│   │   ├── blog/             # Blog-related components
│   │   ├── categories/       # Category-related components
│   │   ├── clinics/          # Clinic-related components
│   │   ├── layout/           # Layout components
│   │   ├── maps/             # Google Maps integration components
│   │   ├── practitioners/    # Practitioner-related components
│   │   └── ui/               # UI components
│   │
│   ├── lib/                  # Utility libraries and functions
│   │   ├── api/              # API client and utilities
│   │   ├── hooks/            # Custom React hooks
│   │   ├── supabase/         # Supabase client and utilities
│   │   ├── schemas/          # Data validation schemas (e.g., Zod, Yup)
│   │   └── utils/            # Helper functions
│   │
│   ├── tests/                # Frontend tests (unit, integration, e2e)
│   │
│   ├── next-env.d.ts         # TypeScript declarations for Next.js
│   └── next.config.js        # Next.js configuration
│
└── temp/                     # Temporary files
```

## Technology Stack

- **Frontend:** Next.js with TypeScript
- **Backend:** Strapi (Headless CMS)
- **Database:** Supabase (PostgreSQL)
- **Authentication:** Supabase Auth
- **Maps Integration:** Google Maps API
- **Hosting:**
  - Frontend: Vercel
  - Backend: Cloud provider (AWS, DigitalOcean, etc.)
  - Database: Supabase

## Key Features

1. **Directory Functionality**
   - Clinic and practitioner profiles
   - Search and filtering
   - Category browsing

2. **Mapping & Location Search**
   - Google Maps integration on clinic profiles
   - Interactive maps on category pages
   - Location-based search (city/zip code)

3. **Blog Integration**
   - Article publishing
   - Categorization and tagging
   - Author profiles

4. **SEO & Structured Data**
   - Customizable SEO fields
   - JSON-LD schema with dynamic variables
   - SEO optimization for all pages

5. **User Authentication**
   - Secure login/registration
   - Role-based access control
   - Profile management