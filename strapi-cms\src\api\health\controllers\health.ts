/**
 * Health check controller
 * Provides a dedicated endpoint for health checks
 */

export default {
  /**
   * Health check endpoint
   * Returns a 200 OK response to indicate the service is healthy
   */
  check: async (ctx) => {
    try {
      // Return a simple 200 OK response
      ctx.body = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
      };
    } catch (err) {
      ctx.body = err;
    }
  }
};
