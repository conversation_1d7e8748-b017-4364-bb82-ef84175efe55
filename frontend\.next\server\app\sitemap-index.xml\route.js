(()=>{var e={};e.id=2296,e.ids=[2296],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53410:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>m,serverHooks:()=>u,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var n={};s.r(n),s.d(n,{GET:()=>l});var o=s(96559),r=s(48088),i=s(37719);let a=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";process.env.NEXT_PUBLIC_SITE_URL,console.log(`Using site URL for sitemap index: ${a}`);let p=a.endsWith("/")?a.slice(0,-1):a;async function l(e){try{console.log("Generating sitemap index...");let e=`<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${p}/sitemap.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${p}/sitemap-blog.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${p}/sitemap-clinics.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${p}/sitemap-practitioners.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
</sitemapindex>`;return new Response(e,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600","X-Content-Type-Options":"nosniff"}})}catch(e){return console.error("Error generating sitemap index:",e),new Response(`<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${p}/sitemap.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
</sitemapindex>`,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"no-cache","X-Content-Type-Options":"nosniff"}})}}let m=new o.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/sitemap-index.xml/route",pathname:"/sitemap-index.xml",filename:"route",bundlePath:"app/sitemap-index.xml/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\sitemap-index.xml\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:u}=m;function x(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,s)=>{"use strict";e.exports=s(44870)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[7719],()=>s(53410));module.exports=n})();