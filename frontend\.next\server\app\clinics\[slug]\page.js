(()=>{var e={};e.id=5900,e.ids=[5900],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},2629:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\LazyImage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\LazyImage.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12045:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\MarkdownContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\MarkdownContent.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},12753:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A,dynamic:()=>j,dynamicParams:()=>y,generateMetadata:()=>P,generateStaticParams:()=>b,revalidate:()=>N});var s=r(37413),i=r(4536),a=r.n(i),n=r(73993),l=r(8742);function o(e){return(0,l.k5)({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"M12 1 3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm-2 16-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"},child:[]}]})(e)}var c=r(58446),d=r(39916),p=r(19945);r(61120);let m=e=>{if(!e)return"N/A";try{let t=new Date(`1970-01-01T${e}Z`);if(isNaN(t.getTime()))return console.warn(`Invalid time string received: ${e}`),"Invalid Time";return t.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0,timeZone:"UTC"})}catch(t){return console.error(`Error formatting time string "${e}":`,t),"Error"}},u={Monday:1,Tuesday:2,Wednesday:3,Thursday:4,Friday:5,Saturday:6,Sunday:7},h=({hours:e})=>{if(!e||0===e.length)return null;let t=[...e].sort((e,t)=>u[e.day]-u[t.day]);return(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,s.jsx)("h3",{className:"font-bold text-gray-800 mb-4",children:"Opening Hours"}),(0,s.jsx)("ul",{className:"space-y-2",children:t.map(e=>(0,s.jsxs)("li",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-600 w-1/3",children:e.day}),e.closed?(0,s.jsx)("span",{className:"font-medium text-red-600",children:"Closed"}):(0,s.jsxs)("span",{className:"font-medium text-gray-800 text-right",children:[m(e.openTime)," - ",m(e.closeTime)]})]},e.id||e.day))})]})};var x=r(12045),g=r(2629),f=r(82158);async function b(){try{let e=await c.$.clinics.getAllSlugs({cache:"force-cache",next:{revalidate:43200,tags:["strapi-clinics-slugs"]}});if(e&&e.data&&Array.isArray(e.data))return console.log(`Pre-rendering ${e.data.length} clinic detail pages`),e.data.filter(e=>e&&"string"==typeof e.slug).map(e=>({slug:e.slug}));return console.error("Failed to fetch clinic slugs or data is not in expected format:",e),[]}catch(e){return console.error("Error fetching clinic slugs for generateStaticParams:",e),[]}}let j="force-static",N=43200,y=!0,v=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";console.log(`Using site URL for canonical URLs: ${v}`);let w=(e,t,r="Unknown")=>{if(!e)return null;if(e.includes("media.strapiapp.com")){let s=e.startsWith("http")?e:`https://${e}`;return console.log(`Processed ${t} URL:`,{itemName:r,original:e,processed:s}),s}{if(e.startsWith("http"))return e;let s=`https://nice-badge-2130241d6c.strapiapp.com${e.startsWith("/")?"":"/"}${e}`;return console.log(`Processed ${t} URL:`,{itemName:r,original:e,processed:s}),s}};async function _(e){try{let t=await c.$.clinics.getBySlug(e,{next:{tags:["strapi-clinics-list",`strapi-clinic-${e}`],revalidate:43200},cache:"force-cache"});if(t?.data&&t.data.length>0)return t.data[0];return null}catch(t){return console.error(`Error fetching clinic with slug ${e}:`,t),null}}async function P({params:e}){let t,{slug:r}=e,s=await _(r);if(!s)return{title:"Clinic Not Found | Natural Healing Now",description:"The requested clinic could not be found."};"https://nice-badge-2130241d6c.strapiapp.com".replace("/api","");let i=s.seo,a=`${s.name} | Natural Healing Now`,n=s.description||`Learn more about ${s.name}, a clinic offering holistic health services.`,l=i?.metaTitle||a,o=i?.metaDescription||n,c=`/clinics/${s.slug}`,d=i?.canonicalURL||(v?`${v}${c}`:c),p=i?.openGraph?.ogImage?.url,m=i?.metaImage?.url,u=s.logo?.url;p?t=(0,f.Rb)(p):m?t=(0,f.Rb)(m):u&&(t=(0,f.Rb)(u)),console.log("Clinic og:image:",{ogImageFromSeo:p,metaImageUrl:m,clinicLogoUrl:u,imageUrl:t});let h=t?[{url:t}]:[],x="website",g=i?.openGraph?.ogType;return g&&["article","website","book","profile","music.song","music.album","music.playlist","music.radio_station","video.movie","video.episode","video.tv_show","video.other"].includes(g)&&(x=g),{title:l,description:o,alternates:{canonical:d},openGraph:i?.openGraph?{title:i.openGraph.ogTitle||l,description:i.openGraph.ogDescription||o,url:i.openGraph.ogUrl||d,type:x,images:h}:{title:l,description:o,url:d,type:x,images:h},twitter:{card:"summary_large_image",title:i?.openGraph?.ogTitle||l,description:i?.openGraph?.ogDescription||o,images:h}}}async function A({params:e}){let{slug:t}=e,r=await _(t);r||(0,d.notFound)();let{name:i="Unnamed Clinic",description:l="No description available.",logo:c=null,featured_image:m=null,videoEmbed:u=null,address:f={city:"N/A",stateProvince:"N/A",postalCode:"",country:""},location:b=null,contactInfo:j=null,appointment_options:N=null,payment_methods:y=null,openingHours:v=null,services:P=null,specialties:A=null,conditions:C=null,practitioners:I=null,isVerified:E=!1,seo:U=null}=r,$=f?(f.streetAddress1||f.streetAddress||"").trim():"",L=f?`${f.city}, ${f.stateProvince} ${f.postalCode}`.trim():"",T=$&&L?`${$}, ${L}`:$||L,R=c?.url?w(c.url,"clinic-logo",i):null,S=u?.url?`<iframe src="${u.url.replace("watch?v=","embed/")}" title="${i} Video" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: 0;"></iframe>`:u?.oembed?.html||null,k=null;if(U?.structuredData){if("string"==typeof U.structuredData)k=U.structuredData;else if("object"==typeof U.structuredData)try{k=JSON.stringify(U.structuredData)}catch(e){console.error("Failed to stringify structuredData object:",e)}}return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-gray-100 py-3",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)(a(),{href:"/",className:"hover:text-emerald-600",children:"Home"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)(a(),{href:"/clinics",className:"hover:text-emerald-600",children:"Clinics"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)("span",{className:"text-gray-800",children:i})]})})}),(0,s.jsx)("div",{className:"bg-white border-b",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center",children:[(0,s.jsxs)("div",{className:"mb-4 md:mb-0 md:mr-6",children:[" ",(0,s.jsx)(p.default,{src:R||"",alt:`${i} logo`,fallbackChar:i.charAt(0)})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-x-2 mb-2",children:[" ",(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:i}),E&&(0,s.jsxs)("div",{className:"flex items-center gap-x-1 bg-emerald-100 text-emerald-700 px-2 py-0.5 rounded-full text-sm font-medium",children:[(0,s.jsx)(o,{color:"#009967",size:16}),(0,s.jsx)("span",{children:"VERIFIED"})]})]}),T&&(0,s.jsxs)("div",{className:"flex items-center text-gray-600 mb-1",children:[(0,s.jsx)(n.HzC,{className:"mr-2 text-emerald-500 flex-shrink-0"}),(0,s.jsx)("span",{children:T})]}),j?.phoneNumber&&(0,s.jsxs)("div",{className:"flex items-center text-gray-600 mb-1",children:[(0,s.jsx)(n.QFc,{className:"mr-2 text-emerald-500 flex-shrink-0"}),(0,s.jsx)("a",{href:`tel:${j.phoneNumber.replace(/[^\d+]/g,"")}`,className:"hover:text-emerald-600",children:j.phoneNumber})]}),j?.websiteUrl&&(0,s.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,s.jsx)(n.VeH,{className:"mr-2 text-emerald-500 flex-shrink-0"}),(0,s.jsx)("a",{href:j.websiteUrl,target:"_blank",rel:"nofollow noopener noreferrer",className:"text-emerald-600 hover:text-emerald-700 break-all",children:"Visit Website"})]})]})]})})}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:["About ",i]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,s.jsx)(x.default,{content:l||"",applyNoFollow:!1}),S&&(0,s.jsx)("div",{className:"mt-6 rounded-lg overflow-hidden",style:{maxWidth:"100%"},children:(0,s.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0},dangerouslySetInnerHTML:{__html:S}})})]})]}),P&&P.length>0&&(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Services"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,s.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:P.map(e=>(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-gray-700",children:e.name})]},e.id))})})]}),A&&A.length>0&&(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Specialties"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,s.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:A.map(e=>(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"}),(0,s.jsx)(a(),{href:`/specialities/${e.slug}`,className:"no-underline hover:text-emerald-600",children:e.name})]},e.id))})})]}),C&&C.length>0&&(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Conditions Treated"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,s.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:C.map(e=>(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"}),(0,s.jsx)(a(),{href:`/conditions/${e.slug}`,className:"no-underline hover:text-emerald-600",children:e.name})]},e.id))})})]}),I&&I.length>0&&(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Practitioners"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,s.jsx)("ul",{className:"divide-y divide-gray-200",children:I.map(e=>(0,s.jsx)("li",{className:"py-4 first:pt-0 last:pb-0",children:(0,s.jsxs)(a(),{href:`/practitioners/${e.slug}`,className:"flex items-center hover:bg-gray-50 p-2 rounded-lg transition-colors",children:[(0,s.jsx)("div",{className:`${e.profilePicture?.url?"":"bg-emerald-100"} h-12 w-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0 overflow-hidden relative`,children:e.profilePicture?.url?(0,s.jsx)(g.default,{src:w(e.profilePicture.url,"practitioner-profile",e.name)||"",alt:`${e.name} profile picture`,width:48,height:48,fillContainer:!0,className:"object-cover rounded-full",showPlaceholder:!0}):(0,s.jsx)("span",{className:"text-emerald-700 font-semibold",children:e.name.charAt(0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-800",children:e.name}),e.title&&(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.title})]})]})},e.id))})})]})]}),(0,s.jsxs)("div",{children:[N&&N.length>0&&(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,s.jsx)("h3",{className:"font-bold text-gray-800 mb-4",children:"Appointment Options"}),(0,s.jsx)("ul",{className:"space-y-2",children:N.map(e=>(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"}),(0,s.jsx)("span",{children:e.name})]},e.id))})]}),y&&y.length>0&&(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,s.jsx)("h3",{className:"font-bold text-gray-800 mb-4",children:"Accepted Payment Methods"}),(0,s.jsx)("ul",{className:"space-y-2",children:y.map(e=>(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"}),(0,s.jsx)("span",{children:e.name})]},e.id))})]}),j&&(j.phoneNumber||j.emailAddress||T)&&(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,s.jsx)("h3",{className:"font-bold text-gray-800 mb-4",children:"Contact"}),(0,s.jsxs)("div",{className:"space-y-3",children:[j.phoneNumber&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-600 mb-1",children:"Phone"}),(0,s.jsx)("a",{href:`tel:${j.phoneNumber.replace(/[^\d+]/g,"")}`,className:"font-medium text-emerald-600 hover:text-emerald-700",children:j.phoneNumber})]}),j.emailAddress&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-600 mb-1",children:"Email"}),(0,s.jsx)("a",{href:`mailto:${j.emailAddress}`,className:"font-medium text-emerald-600 hover:text-emerald-700 break-all",children:j.emailAddress})]}),f&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-600 mb-1",children:"Address"}),(0,s.jsxs)("p",{className:"font-medium",children:[f.streetAddress1||f.streetAddress||""," ",(0,s.jsx)("br",{})," ",`${f.city}, ${f.stateProvince} ${f.postalCode}`," "]})," "]})]})]}),v&&v.length>0&&(0,s.jsx)(h,{hours:v}),b?.googlePlaceId&&process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY&&(0,s.jsxs)("section",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"font-bold text-gray-800 mb-4",children:"Location Map"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",children:(0,s.jsx)("iframe",{width:"100%",height:"450",style:{border:0},loading:"lazy",allowFullScreen:!0,referrerPolicy:"no-referrer-when-downgrade",src:`https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&q=place_id:${b.googlePlaceId}`})})]})]})]})}),k&&(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:k}})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19945:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\clinics\\\\ClinicLogo.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\clinics\\ClinicLogo.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36463:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n});var s=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let i={enabled:!1,level:"info",prefix:"[NHN]"};function a(e,t,...r){if(!i.enabled)return;let n=Object.values(s),l=n.indexOf(i.level);if(n.indexOf(e)>=l){let s=i.prefix?`${i.prefix} `:"",a=`${s}${t}`;switch(e){case"debug":console.debug(a,...r);break;case"info":console.info(a,...r);break;case"warn":console.warn(a,...r);break;case"error":console.error(a,...r)}}}let n={debug:function(e,...t){a("debug",e,...t)},info:function(e,...t){a("info",e,...t)},warn:function(e,...t){a("warn",e,...t)},error:function(e,...t){a("error",e,...t)},configure:function(e){i={...i,...e}}}},36538:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,12045)),Promise.resolve().then(r.bind(r,19945)),Promise.resolve().then(r.bind(r,2629))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76706:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,90612)),Promise.resolve().then(r.bind(r,85399)),Promise.resolve().then(r.bind(r,24587))},76760:e=>{"use strict";e.exports=require("node:path")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82158:(e,t,r)=>{"use strict";r.d(t,{Jf:()=>m,Rb:()=>u,Z5:()=>d,tz:()=>p});var s=r(36463);let i={NEXT_PUBLIC_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_MEDIA_URL:"https://nice-badge-2130241d6c.media.strapiapp.com",NEXT_PUBLIC_SITE_URL:process.env.NEXT_PUBLIC_SITE_URL,IMAGE_HOSTNAME:process.env.IMAGE_HOSTNAME,NODE_ENV:"production"},a=i.NEXT_PUBLIC_STRAPI_API_URL||i.NEXT_PUBLIC_API_URL||("development"===i.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.strapiapp.com"),n=(()=>{if(i.NEXT_PUBLIC_STRAPI_MEDIA_URL)return o(c(i.NEXT_PUBLIC_STRAPI_MEDIA_URL));if(i.IMAGE_HOSTNAME)return o(c(i.IMAGE_HOSTNAME));try{let e=new URL(a);if(e.hostname.endsWith("strapiapp.com"))return`${o(e.protocol)}//${e.hostname.replace("strapiapp.com","media.strapiapp.com")}`;return o(c(a))}catch(e){return"development"===i.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.media.strapiapp.com"}})(),l=i.NEXT_PUBLIC_SITE_URL||(i.NEXT_PUBLIC_API_URL&&i.NEXT_PUBLIC_API_URL.includes("strapiapp.com")?i.NEXT_PUBLIC_API_URL.replace(".strapiapp.com",".vercel.app"):"https://naturalhealingnow.vercel.app");function o(e){return e?e.replace(/^http:/,"https:"):e}function c(e){return e&&e.endsWith("/")?e.slice(0,-1):e}function d(e,t={debug:!1}){if(t.debug&&s.Ay.debug("getStrapiMediaUrl input:",{type:typeof e,isNull:null===e,isUndefined:void 0===e,value:e}),!e)return null;let r=null;if("string"==typeof e?r=e:"object"==typeof e&&(r=e.url||e.data?.attributes?.url||e.data?.url||null),!r)return t.debug,s.Ay.warn("Could not extract initial URL from mediaInput in getStrapiMediaUrl",{mediaInput:e}),null;let i=m(r);return i?i.startsWith("http://")||i.startsWith("https://")?i:n?`${n}${i.startsWith("/")?"":"/"}${i}`:(s.Ay.warn("STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl",{sanitizedUrl:i}),`${a}${i.startsWith("/")?"":"/"}${i}`):(t.debug,s.Ay.warn("URL became empty after sanitization in getStrapiMediaUrl",{originalUrl:r}),null)}function p(e){if(!e||!e.profilePicture)return null;let t=e.profilePicture,r=t.url||t.data?.attributes?.url||t.data?.url||t.formats?.thumbnail?.url;return r?d(r):d(t)}function m(e){let t;if("string"==typeof e&&(e.startsWith("https://")||e.startsWith("http://")||e.startsWith("/")))return e.startsWith("http://")?e.replace(/^http:/,"https:"):e;if(!e)return"";if("object"==typeof e&&e.url&&"string"==typeof e.url)t=e.url;else{if("string"!=typeof e)return s.Ay.warn("Invalid input type for sanitizeUrl. Expected string or object with url property.",{inputType:typeof e}),"";t=e}(t=t.trim()).toLowerCase().startsWith("undefined")&&(t=t.substring(9),s.Ay.info('Removed "undefined" prefix from URL',{original:e,new:t}));let r=a.replace(/^https?:\/\//,"").split("/")[0],i=n.replace(/^https?:\/\//,"").split("/")[0];if(r&&i&&t.includes(r)&&t.includes(i)){let e=RegExp(`(https?://)?(${r})(/*)(https?://)?(${i})`,"gi"),a=`https://${i}`;if(e.test(t)){let n=t;t=t.replace(e,a),s.Ay.info("Fixed concatenated Strapi domains",{original:n,fixed:t,apiDomain:r,mediaDomain:i})}}if(t.includes("https//")){let e=t;t=t.replace(/https\/\//g,"https://"),s.Ay.info("Fixed missing colon in URL (https//)",{original:e,fixed:t})}if(t.startsWith("//")?t=`https:${t}`:(t.includes("media.strapiapp.com")||t.includes(i))&&!t.startsWith("http")?t=`https://${t}`:(t.startsWith("localhost")||t.startsWith(r.split(".")[0]))&&(t=`https://${t}`),t.startsWith("/"))return t;if(t.startsWith("http://")||t.startsWith("https://"))try{return new URL(t),t}catch(e){if(s.Ay.error("URL parsing failed after sanitization attempts",{url:t,error:e}),!t.includes("://")&&!t.includes("."))return t;return""}return n&&t&&!t.includes("://")?(s.Ay.debug("Assuming relative media path, prepending STRAPI_MEDIA_URL",{path:t}),`/${t}`):(s.Ay.warn("sanitizeUrl could not produce a valid absolute or relative URL",{originalInput:e,finalSanitized:t}),t)}function u(e){if(!e)return;let t=m(e);if(t){if(t.startsWith("http://")||t.startsWith("https://"))return t.replace(/^http:/,"https:");if(n){let r=`${n}${t.startsWith("/")?"":"/"}${t}`;return s.Ay.debug("Constructed OG image URL from relative path",{original:e,final:r}),r.replace(/^http:/,"https:")}if(s.Ay.warn("Could not determine OG image URL confidently",{originalUrl:e,processedUrl:t}),a)return`${a}${t.startsWith("/")?"":"/"}${t}`.replace(/^http:/,"https:")}}"development"===i.NODE_ENV&&s.Ay.debug("Media Utils Initialized:",{EFFECTIVE_STRAPI_URL:a,STRAPI_MEDIA_URL:n,SITE_URL:l})},83997:e=>{"use strict";e.exports=require("tty")},85399:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(60687),i=r(43210),a=r(30474),n=r(28136);let l=({src:e,alt:t,fallbackChar:r,containerClassName:l,imageClassName:o="h-full w-full"})=>{let c=e?"h-24 w-24 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden":"bg-emerald-100 h-24 w-24 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden",[d,p]=(0,i.useState)("object-contain"),m=e?(0,n.Jf)(e):"";e&&m!==e&&console.log("ClinicLogo URL sanitized:",{original:e,sanitized:m});let[u,h]=(0,i.useState)(!1);return(0,s.jsx)("div",{className:l||c,children:m&&!u?(0,s.jsx)(a.default,{src:m,alt:t,width:96,height:96,className:`${o} ${d}`,onLoad:e=>{let{naturalWidth:t,naturalHeight:r}=e.currentTarget;if(0===r)return void p("object-contain");let s=t/r;s>1&&s<=2?p("object-cover"):p("object-contain")},onError:()=>{console.error("ClinicLogo image failed to load:",{src:m,alt:t}),h(!0)},priority:!0}):(0,s.jsx)("span",{className:"text-emerald-700 font-bold text-3xl",children:r})})}},86512:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>o});var s=r(65239),i=r(48088),a=r(31369),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let o={children:["",{children:["clinics",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,12753)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\clinics\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\clinics\\[slug]\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/clinics/[slug]/page",pathname:"/clinics/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},90612:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(60687),i=r(84189),a=r(3832),n=r(43210),l=r(66501);async function o(e,t){try{let r=await fetch("/api/analytics/post-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:e,postSlug:t})});if(!r.ok)throw Error(`Failed to track post view: ${r.statusText}`)}catch(e){l.Ay.error("Error tracking post view:",e)}}let c=({content:e,postId:t,postSlug:r,applyNoFollow:l=!0})=>((0,n.useEffect)(()=>{if(t&&r){let e=setTimeout(()=>{o(t,r)},2e3);return()=>clearTimeout(e)}},[t,r]),(0,s.jsxs)("div",{className:"mb-8",children:[" ",(0,s.jsx)(i.oz,{components:{h1:({node:e,...t})=>(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mt-8 mb-4",...t}),h2:({node:e,...t})=>(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mt-6 mb-3",...t}),h3:({node:e,...t})=>(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mt-5 mb-2",...t}),h4:({node:e,...t})=>(0,s.jsx)("h4",{className:"text-lg font-bold text-gray-800 mt-4 mb-2",...t}),p:({node:e,...t})=>(0,s.jsx)("p",{className:"text-gray-700 mb-4",...t}),a:({node:e,href:t,...r})=>{let i=t&&(t.startsWith("http://")||t.startsWith("https://")),a="";return i&&(l&&(a+="nofollow "),a+="noopener noreferrer"),(0,s.jsx)("a",{className:"text-emerald-600 hover:text-emerald-700 underline",href:t,rel:a.trim()||void 0,target:i?"_blank":void 0,...r})},ul:({node:e,...t})=>(0,s.jsx)("ul",{className:"list-disc pl-6 mb-4",...t}),ol:({node:e,...t})=>(0,s.jsx)("ol",{className:"list-decimal pl-6 mb-4",...t}),li:({node:e,...t})=>(0,s.jsx)("li",{className:"mb-1",...t}),blockquote:({node:e,...t})=>(0,s.jsx)("blockquote",{className:"border-l-4 border-emerald-500 pl-4 italic my-4",...t})},rehypePlugins:[a.A],children:e})]}))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,1330,3376,6391,2975,5373,8446,270],()=>r(86512));module.exports=s})();