# Natural Healing Now - Frontend

This is the frontend application for Natural Healing Now, built with Next.js 15.

## Environment Variables

The application requires certain environment variables to be set for proper functionality. Create a `.env.local` file in the root of the frontend directory with the following variables:

```
# Strapi Configuration
# --------------------
# The public URL of your Strapi backend API
# IMPORTANT: In production, this MUST be set to your Strapi Cloud URL
# DO NOT use localhost in production!
NEXT_PUBLIC_API_URL=https://nice-badge-2130241d6c.strapiapp.com

# Strapi API Token for authenticated requests (needed for content fetching)
# Create this token in your Strapi Admin Panel (Settings -> API Tokens)
STRAPI_API_TOKEN=your_strapi_api_token

# Site Configuration
# ------------------
# The name of your website, displayed in the header/logo area
NEXT_PUBLIC_SITE_NAME="Natural Healing Now"
```

### Important Notes for Production Deployment

When deploying to Vercel or another hosting provider, make sure to:

1. Set the `NEXT_PUBLIC_API_URL` to your Strapi Cloud URL (e.g., `https://nice-badge-2130241d6c.strapiapp.com`)
2. Set the `STRAPI_API_TOKEN` to a valid API token from your Strapi admin panel
3. Set the `NEXT_PUBLIC_SITE_NAME` to your site name

## Development

To run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Build

To build the application for production:

```bash
npm run build
# or
yarn build
```

## Troubleshooting

### Images Not Loading

If images are not loading in production:

1. Check that `NEXT_PUBLIC_API_URL` is set correctly in your environment variables
2. Verify that the Strapi media URLs are accessible
3. Check the browser console for any errors related to image loading

### API Requests Failing

If API requests are failing:

1. Ensure `NEXT_PUBLIC_API_URL` points to your Strapi instance
2. Check that `STRAPI_API_TOKEN` is valid and has the necessary permissions
3. Verify that your Strapi instance is running and accessible

### Local Development Issues

For local development:

1. Make sure your local Strapi instance is running on the port specified in `NEXT_PUBLIC_API_URL`
2. Check that you have the correct API token in your `.env.local` file
3. Restart the development server after making changes to environment variables
