/**
 * Server-side optimized data fetching utilities
 * This file provides functions to reduce API calls to Strapi on the server
 */
import { cache } from 'react';
import { AxiosRequestConfig } from 'axios';
import { fetchWithOptimization, fetchContentType } from './optimizedFetch';

/**
 * Cached server-side fetch function
 * This will deduplicate requests within the same render pass
 * Using longer cache times with on-demand revalidation
 */
export const fetchFromServerCached = cache(async <T>(
  endpoint: string,
  options: AxiosRequestConfig = {},
  ttl: number = 7 * 24 * 60 * 60 * 1000 // 7 days default
): Promise<T> => {
  return fetchWithOptimization<T>(endpoint, options, ttl, false, true);
});

/**
 * Cached function to fetch global settings
 */
export const getGlobalSettings = cache(async () => {
  return fetchContentType('global-setting', {
    params: {
      populate: '*',
    }
  }, true);
});

/**
 * Cached function to fetch categories
 */
export const getCategories = cache(async (pageSize = 10) => {
  return fetchContentType('categories', {
    params: {
      pagination: { pageSize },
      populate: '*',
    }
  }, true);
});

/**
 * Cached function to fetch specialties
 */
export const getSpecialties = cache(async (pageSize = 10) => {
  return fetchContentType('specialties', {
    params: {
      pagination: { pageSize },
      populate: '*',
    }
  }, true);
});

/**
 * Cached function to fetch clinics
 */
export const getClinics = cache(async (options: {
  page?: number;
  pageSize?: number;
  featured?: boolean;
  location?: string;
  query?: string;
  specialtySlug?: string;
} = {}) => {
  const { page = 1, pageSize = 10, featured, location, query, specialtySlug } = options;

  const params: Record<string, any> = {
    pagination: { page, pageSize },
    populate: '*',
  };

  // Add filters based on options
  const filters: Record<string, any> = {};

  if (featured) {
    filters.isFeatured = { $eq: true };
  }

  if (location) {
    filters.$or = [
      { city: { $containsi: location } },
      { state: { $containsi: location } },
      { zipCode: { $containsi: location } }
    ];
  }

  if (query) {
    filters.$or = [
      ...(filters.$or || []),
      { name: { $containsi: query } },
      { description: { $containsi: query } }
    ];
  }

  if (specialtySlug) {
    filters.specialties = {
      slug: { $eq: specialtySlug }
    };
  }

  if (Object.keys(filters).length > 0) {
    params.filters = filters;
  }

  return fetchContentType('clinics', { params }, true);
});

/**
 * Cached function to fetch practitioners
 */
export const getPractitioners = cache(async (options: {
  page?: number;
  pageSize?: number;
  featured?: boolean;
  location?: string;
  query?: string;
  specialtySlug?: string;
} = {}) => {
  const { page = 1, pageSize = 10, featured, location, query, specialtySlug } = options;

  const params: Record<string, any> = {
    pagination: { page, pageSize },
    populate: '*',
  };

  // Add filters based on options
  const filters: Record<string, any> = {};

  if (featured) {
    filters.isFeatured = { $eq: true };
  }

  if (location) {
    filters.clinic = {
      $or: [
        { city: { $containsi: location } },
        { state: { $containsi: location } },
        { zipCode: { $containsi: location } }
      ]
    };
  }

  if (query) {
    filters.$or = [
      { firstName: { $containsi: query } },
      { lastName: { $containsi: query } },
      { bio: { $containsi: query } }
    ];
  }

  if (specialtySlug) {
    filters.specialties = {
      slug: { $eq: specialtySlug }
    };
  }

  if (Object.keys(filters).length > 0) {
    params.filters = filters;
  }

  return fetchContentType('practitioners', { params }, true);
});

/**
 * Cached function to fetch blog posts
 */
export const getBlogPosts = cache(async (options: {
  page?: number;
  pageSize?: number;
  featured?: boolean;
  categorySlug?: string;
  query?: string;
} = {}) => {
  const { page = 1, pageSize = 10, featured, categorySlug, query } = options;

  try {
    const params: Record<string, any> = {
      sort: 'publishDate:desc',
      pagination: { page, pageSize },
      populate: {
        featuredImage: true,
        author: {
          populate: {
            profilePicture: true,
          },
        },
        categories: true,
      },
    };

    // Add filters based on options
    const filters: Record<string, any> = {};

    if (featured) {
      filters.isFeatured = { $eq: true };
    }

    if (categorySlug) {
      filters.categories = {
        slug: { $eq: categorySlug },
      };
    }

    if (query) {
      filters.$or = [
        { title: { $containsi: query } },
        { excerpt: { $containsi: query } },
        { content: { $containsi: query } },
      ];
    }

    if (Object.keys(filters).length > 0) {
      params.filters = filters;
    }

    // Try both 'blog-posts' and 'blogs' endpoints
    try {
      return await fetchContentType('blog-posts', { params }, true);
    } catch (error) {
      console.log('Error fetching from blog-posts, trying blogs endpoint');
      return await fetchContentType('blogs', { params }, true);
    }
  } catch (error) {
    console.error('Error in getBlogPosts:', error);
    return { data: [] };
  }
});
