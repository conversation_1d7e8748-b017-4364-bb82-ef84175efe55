'use client';

interface EmailContactDetailProps {
  emailAddress: string | undefined;
}

const EmailContactDetail = ({ emailAddress }: EmailContactDetailProps) => {
  if (!emailAddress) {
    return null;
  }

  return (
    <div>
      <p className="text-gray-600 mb-1">Email</p>
      <a
        href={`mailto:${emailAddress}`}
        className="font-medium text-emerald-600 hover:text-emerald-700 break-all"
      >
        {emailAddress}
      </a>
    </div>
  );
};

export default EmailContactDetail;
