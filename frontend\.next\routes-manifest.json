{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/providers/specialty/:slug*", "destination": "/categories/:slug*", "statusCode": 308, "regex": "^(?!/_next)/providers/specialty(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/providers/:slug*", "destination": "/clinics/:slug*", "statusCode": 308, "regex": "^(?!/_next)/providers(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(self)"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/images/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, stale-while-revalidate=43200"}, {"key": "Vary", "value": "Accept"}], "regex": "^/images(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/_next/image/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=604800, stale-while-revalidate=43200"}, {"key": "Vary", "value": "Accept"}], "regex": "^/_next/image(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/_next/static/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, stale-while-revalidate=86400"}], "regex": "^/_next/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/static/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=3600"}], "regex": "^/robots\\.txt(?:/)?$"}, {"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=3600"}], "regex": "^/sitemap\\.xml(?:/)?$"}, {"source": "/sitemap-index.xml", "headers": [{"key": "Content-Type", "value": "application/xml; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=3600"}], "regex": "^/sitemap-index\\.xml(?:/)?$"}, {"source": "/sitemap-:type.xml", "headers": [{"key": "Content-Type", "value": "application/xml; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=3600"}], "regex": "^/sitemap-([^/]+?)\\.xml(?:/)?$"}], "dynamicRoutes": [{"page": "/api/clinics/[slug]", "regex": "^/api/clinics/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/clinics/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/blog/authors/[slug]", "regex": "^/blog/authors/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/blog/authors/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/blog/categories/[slug]", "regex": "^/blog/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/blog/categories/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/blog/tags/[slug]", "regex": "^/blog/tags/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/blog/tags/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/blog/[slug]", "regex": "^/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/blog/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/categories/[slug]", "regex": "^/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/categories/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/categories/[slug]/[cityStateSlug]", "regex": "^/categories/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug", "nxtPcityStateSlug": "nxtPcityStateSlug"}, "namedRegex": "^/categories/(?<nxtPslug>[^/]+?)/(?<nxtPcityStateSlug>[^/]+?)(?:/)?$"}, {"page": "/clinics/[slug]", "regex": "^/clinics/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/clinics/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/conditions/[slug]", "regex": "^/conditions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/conditions/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/practitioners/[slug]", "regex": "^/practitioners/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/practitioners/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/specialities/[slug]", "regex": "^/specialities/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/specialities/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about-us", "regex": "^/about\\-us(?:/)?$", "routeKeys": {}, "namedRegex": "^/about\\-us(?:/)?$"}, {"page": "/account", "regex": "^/account(?:/)?$", "routeKeys": {}, "namedRegex": "^/account(?:/)?$"}, {"page": "/affiliate-disclosure", "regex": "^/affiliate\\-disclosure(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate\\-disclosure(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/blog/authors", "regex": "^/blog/authors(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/authors(?:/)?$"}, {"page": "/blog/categories", "regex": "^/blog/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/categories(?:/)?$"}, {"page": "/blog/sitemap.xml", "regex": "^/blog/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/sitemap\\.xml(?:/)?$"}, {"page": "/blog/tags", "regex": "^/blog/tags(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/tags(?:/)?$"}, {"page": "/categories", "regex": "^/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/categories(?:/)?$"}, {"page": "/clinics", "regex": "^/clinics(?:/)?$", "routeKeys": {}, "namedRegex": "^/clinics(?:/)?$"}, {"page": "/clinics/sitemap.xml", "regex": "^/clinics/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/clinics/sitemap\\.xml(?:/)?$"}, {"page": "/conditions", "regex": "^/conditions(?:/)?$", "routeKeys": {}, "namedRegex": "^/conditions(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/cors-test", "regex": "^/cors\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/cors\\-test(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/optimized-client-example", "regex": "^/optimized\\-client\\-example(?:/)?$", "routeKeys": {}, "namedRegex": "^/optimized\\-client\\-example(?:/)?$"}, {"page": "/optimized-example", "regex": "^/optimized\\-example(?:/)?$", "routeKeys": {}, "namedRegex": "^/optimized\\-example(?:/)?$"}, {"page": "/practitioners", "regex": "^/practitioners(?:/)?$", "routeKeys": {}, "namedRegex": "^/practitioners(?:/)?$"}, {"page": "/practitioners/sitemap.xml", "regex": "^/practitioners/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/practitioners/sitemap\\.xml(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}, {"page": "/signin", "regex": "^/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/signin(?:/)?$"}, {"page": "/signup", "regex": "^/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/signup(?:/)?$"}, {"page": "/sitemap-blog.xml", "regex": "^/sitemap\\-blog\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\-blog\\.xml(?:/)?$"}, {"page": "/sitemap-clinics.xml", "regex": "^/sitemap\\-clinics\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\-clinics\\.xml(?:/)?$"}, {"page": "/sitemap-index.xml", "regex": "^/sitemap\\-index\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\-index\\.xml(?:/)?$"}, {"page": "/sitemap-practitioners.xml", "regex": "^/sitemap\\-practitioners\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\-practitioners\\.xml(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/sitemaps.xml", "regex": "^/sitemaps\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemaps\\.xml(?:/)?$"}, {"page": "/specialities", "regex": "^/specialities(?:/)?$", "routeKeys": {}, "namedRegex": "^/specialities(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/test.xml", "regex": "^/test\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\.xml(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/robots.txt", "destination": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$"}, {"source": "/sitemap-:type.xml", "destination": "/sitemap-:type.xml", "regex": "^/sitemap-([^/]+?)\\.xml(?:/)?$"}, {"source": "/sitemap-index.xml", "destination": "/sitemap-index.xml", "regex": "^/sitemap-index\\.xml(?:/)?$"}, {"source": "/sitemaps.xml", "destination": "/sitemap-index.xml", "regex": "^/sitemaps\\.xml(?:/)?$"}, {"source": "/api/image-proxy/:path*", "destination": "/api/image-proxy/:path*", "regex": "^/api/image-proxy(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}