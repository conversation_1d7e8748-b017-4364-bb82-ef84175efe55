# Optimizing ISR Revalidation

This document explains the changes made to optimize the Incremental Static Regeneration (ISR) revalidation process in the Natural Healing Now website.

## Problem

The category pages were being revalidated too frequently, even when unrelated content was updated. This was causing unnecessary server-side rendering and API calls to Strapi.

The logs showed that the `/categories` and `/categories/[slug]` pages were being re-rendered frequently, even though they should only be revalidated when category content is updated.

## Root Cause

The issue was caused by several factors:

1. **Overly Broad Revalidation**: The general revalidation handler (`/api/revalidate/route.ts`) was revalidating the global `strapi-content` tag and the homepage (`/`) for all content types.

2. **Missing Category-Specific Handler**: There was no specific revalidation handler for categories, so all category updates were being processed by the general handler.

3. **Aggressive `revalidateContent` Function**: The `revalidateContent` function in `@/lib/serverFetch` was revalidating the global `strapi-content` tag for all content types.

4. **Non-Specific Cache Tags**: The category pages were not using specific cache tags that could be targeted for revalidation.

## Solution

We implemented the following changes to optimize the revalidation process:

### 1. Created a Category-Specific Revalidation Handler

We created a new file at `frontend/src/app/api/revalidate/categories/route.ts` that handles category-specific revalidation. This handler only revalidates category-specific tags and paths.

```typescript
// frontend/src/app/api/revalidate/categories/route.ts
export async function POST(request: NextRequest) {
  // ...
  
  // Ensure this webhook is for the 'category' model
  if (model !== 'category') {
    return NextResponse.json({ message: 'Revalidation skipped: Not a category model.' }, { status: 200 });
  }
  
  const tagsToRevalidate: string[] = [];
  
  // Always revalidate the general categories list tag
  tagsToRevalidate.push('strapi-categories');
  tagsToRevalidate.push('strapi-categories-slugs');
  
  // If we have a specific category entry with a slug, revalidate its specific tag
  if (entry && entry.slug) {
    const categorySlug = entry.slug;
    
    // Revalidate the specific category tag
    tagsToRevalidate.push(`strapi-category-${categorySlug}`);
    
    // Revalidate tags for clinics and practitioners in this category
    tagsToRevalidate.push(`strapi-category-${categorySlug}-clinics`);
    tagsToRevalidate.push(`strapi-category-${categorySlug}-practitioners`);
  }
  
  // Revalidate all tags
  for (const tag of tagsToRevalidate) {
    revalidateTag(tag);
  }
  
  // ...
}
```

### 2. Updated the Webhook Configuration

We updated the webhook configuration to point the category webhook to the new specific endpoint:

```javascript
// scripts/configure-strapi-webhooks.js
{
  name: 'Revalidate Categories',
  url: `${SITE_URL}/api/revalidate/categories`,
  headers: {
    'Content-Type': 'application/json',
    'X-Revalidate-Secret': REVALIDATE_TOKEN
  },
  events: [
    'entry.create',
    'entry.update',
    'entry.delete',
    'entry.publish',
    'entry.unpublish'
  ],
  contentType: 'category',
  body: {
    token: REVALIDATE_TOKEN,
    model: 'category',
    entry: {
      id: '{{entry.id}}',
      slug: '{{entry.slug}}'
    }
  }
}
```

### 3. Updated the General Revalidation Handler

We modified the general revalidation handler to be less aggressive with global revalidations:

```typescript
// frontend/src/app/api/revalidate/route.ts

// We no longer revalidate the global content tag to avoid excessive revalidations
// revalidateTag('strapi-content');
// revalidated.push('tag:strapi-content');
logger.info('Skipping global content tag revalidation to avoid excessive revalidations');

// Only revalidate the homepage for content types that are displayed on the homepage
if (['blog-post', 'clinic', 'practitioner', 'category', 'specialty'].includes(contentType)) {
  logger.info('Revalidating homepage because content type is displayed there');
  revalidatePath('/');
  revalidated.push('path:/');
} else {
  logger.info('Skipping homepage revalidation for content type not displayed on homepage');
}
```

### 4. Updated the `revalidateContent` Function

We modified the `revalidateContent` function to be more targeted and less aggressive with global revalidations:

```typescript
// frontend/src/lib/serverFetch.ts
export function revalidateContent(contentType: string, id?: string | number): void {
  // Log the revalidation request
  console.log(`Revalidating content: ${contentType}${id ? ` with ID ${id}` : ''}`);

  // Revalidate specific content type tags
  const tags = generateCacheTags(contentType, id);
  tags.forEach(tag => revalidateTag(tag));

  // For certain content types, revalidate related content
  if (contentType === 'blog-post') {
    // Revalidate blog-related tags
    revalidateTag('strapi-blog-posts');
    revalidateTag('strapi-blog-posts-slugs');

    // If we have an ID or slug, revalidate the specific blog post page
    if (id) {
      revalidateTag(`strapi-blog-post-${id}`);
    }
  } 
  else if (contentType === 'category') {
    // Revalidate category-related tags
    revalidateTag('strapi-categories');
    revalidateTag('strapi-categories-slugs');

    // If we have an ID or slug, revalidate the specific category page
    if (id) {
      revalidateTag(`strapi-category-${id}`);
    }
  }
  // ... other content types ...
  else {
    // For any other content type, only revalidate its specific tags
    // We don't revalidate the global content tag anymore to avoid excessive revalidations
    console.log(`Revalidating unknown content type: ${contentType}`);
  }
}
```

### 5. Updated the Category Pages to Use More Specific Cache Tags

We updated the category pages to use more specific cache tags:

```typescript
// frontend/src/app/categories/page.tsx
// Use on-demand revalidation with specific cache tags
export const revalidate = false;
```

```typescript
// frontend/src/lib/strapi.ts
// Define cache tags for this request to enable targeted revalidation
const cacheTags = ['strapi-categories', 'strapi-categories-slugs'];

// Add page number to cache tags
cacheTags.push(`strapi-categories-page-${page}`);

// Add query to cache tags if provided
if (query) {
  cacheTags.push(`strapi-categories-query-${query}`);
}

// Fetch categories with the constructed query parameters and cache tags
return fetchAPI(`/categories`, { 
  params: queryParams,
  next: {
    tags: cacheTags
  }
});
```

## Testing

We created a script to test the revalidation process:

```javascript
// scripts/test-revalidation.js
async function testCategoryRevalidation() {
  console.log('\n🧪 Testing category-specific revalidation endpoint...');
  
  try {
    const response = await axios.post(`${SITE_URL}/api/revalidate/categories`, {
      token: REVALIDATE_TOKEN,
      model: 'category',
      entry: {
        id: 'test-category',
        slug: 'test-category'
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-Revalidate-Secret': REVALIDATE_TOKEN
      }
    });
    
    console.log('✅ Category-specific revalidation successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('❌ Category-specific revalidation failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    return false;
  }
}
```

## Expected Results

With these changes, we expect:

1. Category pages will only be revalidated when category content is updated.
2. Other content types will not trigger revalidation of category pages.
3. The revalidation process will be more targeted and efficient.
4. Server-side rendering and API calls to Strapi will be reduced.

## Next Steps

1. Monitor the revalidation logs to verify that the changes are working as expected.
2. Consider implementing similar specific revalidation handlers for other content types.
3. Further optimize the revalidation process by using more specific cache tags.
