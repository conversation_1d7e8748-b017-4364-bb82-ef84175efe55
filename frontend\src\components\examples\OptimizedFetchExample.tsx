'use client';

import { useEffect } from 'react';
import { 
  useGlobalSettings, 
  useCategories, 
  useFeaturedClinics, 
  useFeaturedPractitioners, 
  useFeaturedBlogPosts 
} from '@/hooks/useOptimizedFetch';
import { prefetchCommonData } from '@/lib/optimizedFetch';

/**
 * Example component demonstrating optimized data fetching
 */
export default function OptimizedFetchExample() {
  // Prefetch common data on component mount
  useEffect(() => {
    prefetchCommonData();
  }, []);
  
  // Use optimized hooks for data fetching
  const { data: globalSettings, isLoading: isLoadingGlobal } = useGlobalSettings();
  const { data: categories, isLoading: isLoadingCategories } = useCategories(5);
  const { data: clinics, isLoading: isLoadingClinics } = useFeaturedClinics(3);
  const { data: practitioners, isLoading: isLoadingPractitioners } = useFeaturedPractitioners(3);
  const { data: blogPosts, isLoading: isLoadingBlogPosts } = useFeaturedBlogPosts(3);
  
  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-4">Optimized Data Fetching Example</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Global Settings */}
        <div className="p-4 border rounded">
          <h3 className="text-lg font-semibold mb-2">Global Settings</h3>
          {isLoadingGlobal ? (
            <p>Loading...</p>
          ) : (
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(globalSettings, null, 2)}
            </pre>
          )}
        </div>
        
        {/* Categories */}
        <div className="p-4 border rounded">
          <h3 className="text-lg font-semibold mb-2">Categories</h3>
          {isLoadingCategories ? (
            <p>Loading...</p>
          ) : (
            <ul className="list-disc pl-5">
              {categories?.data?.map((category: any) => (
                <li key={category.id || category.documentId}>
                  {category.name || category.attributes?.name}
                </li>
              ))}
            </ul>
          )}
        </div>
        
        {/* Clinics */}
        <div className="p-4 border rounded">
          <h3 className="text-lg font-semibold mb-2">Featured Clinics</h3>
          {isLoadingClinics ? (
            <p>Loading...</p>
          ) : (
            <ul className="list-disc pl-5">
              {clinics?.data?.map((clinic: any) => (
                <li key={clinic.id || clinic.documentId}>
                  {clinic.name || clinic.attributes?.name}
                </li>
              ))}
            </ul>
          )}
        </div>
        
        {/* Practitioners */}
        <div className="p-4 border rounded">
          <h3 className="text-lg font-semibold mb-2">Featured Practitioners</h3>
          {isLoadingPractitioners ? (
            <p>Loading...</p>
          ) : (
            <ul className="list-disc pl-5">
              {practitioners?.data?.map((practitioner: any) => {
                const firstName = practitioner.firstName || practitioner.attributes?.firstName;
                const lastName = practitioner.lastName || practitioner.attributes?.lastName;
                return (
                  <li key={practitioner.id || practitioner.documentId}>
                    {firstName} {lastName}
                  </li>
                );
              })}
            </ul>
          )}
        </div>
        
        {/* Blog Posts */}
        <div className="p-4 border rounded">
          <h3 className="text-lg font-semibold mb-2">Featured Blog Posts</h3>
          {isLoadingBlogPosts ? (
            <p>Loading...</p>
          ) : (
            <ul className="list-disc pl-5">
              {blogPosts?.data?.map((post: any) => (
                <li key={post.id || post.documentId}>
                  {post.title || post.attributes?.title}
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
      
      <div className="mt-4 p-4 bg-blue-50 rounded">
        <h3 className="text-lg font-semibold mb-2">How This Works</h3>
        <p className="mb-2">This component demonstrates several optimization techniques:</p>
        <ul className="list-disc pl-5">
          <li>In-memory caching of API responses</li>
          <li>Request deduplication to prevent duplicate API calls</li>
          <li>Content-type specific cache durations</li>
          <li>Prefetching of common data</li>
          <li>Optimized hooks for specific content types</li>
        </ul>
      </div>
    </div>
  );
}
