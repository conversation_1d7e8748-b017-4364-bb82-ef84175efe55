(()=>{var e={};e.id=8717,e.ids=[8717],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26746:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>l});var s=t(65239),n=t(48088),o=t(31369),a=t(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);t.d(r,i);let l={children:["",{children:["cors-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69991)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\cors-test\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\cors-test\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/cors-test/page",pathname:"/cors-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28418:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\examples\\\\CorsTestComponent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\examples\\CorsTestComponent.tsx","default")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45562:(e,r,t)=>{Promise.resolve().then(t.bind(t,28418))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69991:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>o});var s=t(37413),n=t(28418);let o={title:"CORS Test Page",description:"A page to test and debug CORS issues with Strapi"};function a(){return(0,s.jsx)("div",{className:"container mx-auto py-8",children:(0,s.jsx)(n.default,{})})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85730:(e,r,t)=>{Promise.resolve().then(t.bind(t,91988))},91988:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var s=t(60687),n=t(43210),o=t(73076),a=t(51060);let i=async(e,r={})=>{try{let{params:t,...s}=r,n=`/api/strapi-proxy?endpoint=${encodeURIComponent(e)}`,o=new URLSearchParams;return t&&Object.entries(t).forEach(([e,r])=>{"object"==typeof r&&null!==r?o.append(e,JSON.stringify(r)):o.append(e,String(r))}),(await a.A.get(n,{...s,params:o})).data}catch(r){throw console.error(`Error fetching from proxy (${e}):`,r),r.response&&(console.error("Response data:",r.response.data),console.error("Response status:",r.response.status)),r}};function l(){let[e,r]=(0,n.useState)(null),[t,a]=(0,n.useState)(null),[l,d]=(0,n.useState)(!1),[c,p]=(0,n.useState)(null),[u,x]=(0,n.useState)(null),[h,m]=(0,n.useState)(!1),{data:g,isLoading:b,error:f,usingProxy:j}=function(e,r={}){let[t,s]=(0,n.useState)(null),[a,l]=(0,n.useState)(!0),[d,c]=(0,n.useState)(null),[p,u]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let t=!0;return(async()=>{l(!0),c(null);try{let n=await (0,o.f)(e,r);t&&(s(n),l(!1))}catch(n){if(console.log("Direct Strapi request failed, trying proxy...",n),n.message?.includes("CORS")||n.message?.includes("Network Error")||n.message?.includes("Failed to fetch"))try{u(!0);let n=await i(e,r);t&&(s(n),l(!1))}catch(e){console.error("Proxy request also failed:",e),t&&(c(e instanceof Error?e:Error(String(e))),l(!1))}else t&&(c(n instanceof Error?n:Error(String(n))),l(!1))}})(),()=>{t=!1}},[e,JSON.stringify(r)]),{data:t,isLoading:a,error:d,usingProxy:p}}("/clinics",{params:{filters:{isFeatured:{$eq:!0}},populate:"*"}}),y=async()=>{d(!0),a(null),r(null);try{let e=await fetch("https://nice-badge-2130241d6c.strapiapp.com/api/clinics?filters[isFeatured][$eq]=true&populate=*");if(!e.ok)throw Error(`HTTP error! Status: ${e.status}`);let t=await e.json();r(t)}catch(e){console.error("Direct fetch error:",e),a(e.message||"An error occurred")}finally{d(!1)}},v=async()=>{m(!0),x(null),p(null);try{let e=await fetch("/api/strapi-proxy?endpoint=/clinics&filters[isFeatured][$eq]=true&populate=*");if(!e.ok)throw Error(`HTTP error! Status: ${e.status}`);let r=await e.json();p(r)}catch(e){console.error("Proxy fetch error:",e),x(e.message||"An error occurred")}finally{m(!1)}};return(0,s.jsxs)("div",{className:"p-6 max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"CORS Test Component"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"border p-4 rounded-lg",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Direct Fetch Test"}),(0,s.jsx)("button",{onClick:y,disabled:l,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mb-4 disabled:opacity-50",children:l?"Loading...":"Test Direct Fetch"}),t&&(0,s.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:[(0,s.jsx)("p",{className:"font-bold",children:"Error:"}),(0,s.jsx)("p",{children:t})]}),e&&(0,s.jsxs)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:[(0,s.jsx)("p",{className:"font-bold",children:"Success!"}),(0,s.jsxs)("p",{children:["Found ",e.data?.length||0," clinics"]})]})]}),(0,s.jsxs)("div",{className:"border p-4 rounded-lg",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Proxy Fetch Test"}),(0,s.jsx)("button",{onClick:v,disabled:h,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mb-4 disabled:opacity-50",children:h?"Loading...":"Test Proxy Fetch"}),u&&(0,s.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:[(0,s.jsx)("p",{className:"font-bold",children:"Error:"}),(0,s.jsx)("p",{children:u})]}),c&&(0,s.jsxs)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:[(0,s.jsx)("p",{className:"font-bold",children:"Success!"}),(0,s.jsxs)("p",{children:["Found ",c.data?.length||0," clinics"]})]})]}),(0,s.jsxs)("div",{className:"border p-4 rounded-lg",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Fallback Hook Test"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("p",{className:"font-semibold",children:"Status:"}),(0,s.jsx)("p",{children:b?"Loading...":j?"Using Proxy":"Direct Success"})]}),f&&(0,s.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:[(0,s.jsx)("p",{className:"font-bold",children:"Error:"}),(0,s.jsx)("p",{children:f.message})]}),g&&(0,s.jsxs)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:[(0,s.jsx)("p",{className:"font-bold",children:"Success!"}),(0,s.jsxs)("p",{children:["Found ",g.data?.length||0," clinics"]}),(0,s.jsxs)("p",{className:"text-sm mt-2",children:["Method: ",j?"Proxy":"Direct"]})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-gray-100 rounded-lg",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment Info"}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"NEXT_PUBLIC_API_URL:"})," ","https://nice-badge-2130241d6c.strapiapp.com"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Browser Origin:"})," ","Server-side rendering"]})]})]})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,1330,3376,6391,2975,9298,8446,270,3076],()=>t(26746));module.exports=s})();