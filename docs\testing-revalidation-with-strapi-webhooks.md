# Testing Revalidation with Strapi Webhooks

This document explains how to test the revalidation API with the Strapi webhook payload format.

## Understanding Strapi Webhook Payloads

Strapi webhooks send a payload with the following structure:

```json
{
  "event": "entry.update",
  "createdAt": "2020-01-10T08:58:26.563Z",
  "model": "api::blog-post.blog-post",
  "entry": {
    "id": 1,
    "title": "My Blog Post",
    "slug": "my-blog-post",
    "content": "This is my blog post content",
    "createdAt": "2020-01-10T08:47:36.264Z",
    "updatedAt": "2020-01-10T08:58:26.210Z"
  }
}
```

Our revalidation API has been updated to handle this payload format. It extracts the content type from the `model` field and the ID and slug from the `entry` field.

## Authentication Methods

Strapi webhooks can be authenticated in two ways:

### 1. Server Configuration (Recommended)

The most secure way is to configure default headers in your Strapi server configuration:

```javascript
// ./config/server.js
module.exports = {
  webhooks: {
    defaultHeaders: {
      Authorization: "Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
    },
  },
};
```

### 2. Individual Webhook Headers

If you don't have access to the server configuration, you can set the Authorization header for each webhook in the Strapi admin panel:

- Key: `Authorization`
- Value: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`

## Testing with Curl

You can test the revalidation API with the Strapi webhook payload format using curl:

```bash
curl -X POST https://www.naturalhealingnow.com/api/revalidate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=" \
  -d '{
    "event": "entry.update",
    "model": "api::blog-post.blog-post",
    "entry": {
      "id": 123,
      "slug": "my-blog-post"
    }
  }'
```

## Testing with the Test Script

You can also use the test script to test the revalidation API with the Strapi webhook payload format:

```bash
node scripts/test-revalidation.js
```

The test script has been updated to include test cases for the Strapi webhook payload format with proper authentication.

## Verifying Revalidation

After sending a revalidation request, you can verify that the revalidation was successful by:

1. Checking the Vercel logs to see if the revalidation API was called
2. Checking the Vercel logs to see which pages were revalidated
3. Visiting the revalidated pages to ensure they show the updated content

## Troubleshooting

If the revalidation isn't working as expected, check the following:

1. **Webhook URL**: Make sure the webhook URL is correct and accessible from the internet.
2. **Authorization Header**: Verify that the Authorization header is correctly set with the Bearer token.
3. **Content Type Format**: Ensure the `model` field in the webhook payload follows the format `api::content-type.content-type`.
4. **Events**: Make sure the correct events are selected for each webhook.
5. **Server Configuration**: If using server configuration, ensure your server has been restarted after making changes.

## Strapi Webhook Payload Examples

Here are some examples of Strapi webhook payloads for different content types:

### Blog Post

```json
{
  "event": "entry.update",
  "createdAt": "2020-01-10T08:58:26.563Z",
  "model": "api::blog-post.blog-post",
  "entry": {
    "id": 123,
    "title": "My Blog Post",
    "slug": "my-blog-post"
  }
}
```

### Practitioner

```json
{
  "event": "entry.update",
  "createdAt": "2020-01-10T08:58:26.563Z",
  "model": "api::practitioner.practitioner",
  "entry": {
    "id": 456,
    "name": "Dr. John Doe",
    "slug": "dr-john-doe"
  }
}
```

### Clinic

```json
{
  "event": "entry.update",
  "createdAt": "2020-01-10T08:58:26.563Z",
  "model": "api::clinic.clinic",
  "entry": {
    "id": 789,
    "name": "Acme Clinic",
    "slug": "acme-clinic"
  }
}
```

### Category

```json
{
  "event": "entry.update",
  "createdAt": "2020-01-10T08:58:26.563Z",
  "model": "api::category.category",
  "entry": {
    "id": 101,
    "name": "Acupuncture",
    "slug": "acupuncture"
  }
}
```

### Specialty

```json
{
  "event": "entry.update",
  "createdAt": "2020-01-10T08:58:26.563Z",
  "model": "api::specialty.specialty",
  "entry": {
    "id": 202,
    "name": "Fertility",
    "slug": "fertility"
  }
}
```

## Available Events

Strapi webhooks can be triggered by the following events:

- `entry.create`: Triggered when a Content Type entry is created.
- `entry.update`: Triggered when a Content Type entry is updated.
- `entry.delete`: Triggered when a Content Type entry is deleted.
- `entry.publish`: Triggered when a Content Type entry is published (only when draft/publish is enabled).
- `entry.unpublish`: Triggered when a Content Type entry is unpublished (only when draft/publish is enabled).

## Conclusion

By understanding the Strapi webhook payload format and how our revalidation API handles it, you can effectively test and troubleshoot the revalidation process. This ensures that your website is always up-to-date with the latest content from Strapi.
