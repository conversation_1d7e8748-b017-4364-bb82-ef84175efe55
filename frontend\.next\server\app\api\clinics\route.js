"use strict";(()=>{var e={};e.id=8729,e.ids=[8729],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64463:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>u});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),p=t(5409);async function u(e){let{searchParams:r}=new URL(e.url),t=parseInt(r.get("page")||"1",10),s=parseInt(r.get("pageSize")||"10",10),n=r.get("category")||void 0,o=r.get("specialty")||void 0,i=r.get("condition")||void 0,u=r.get("search")||void 0;if(isNaN(t)||t<1)return a.NextResponse.json({error:"Invalid page number"},{status:400});if(isNaN(s)||s<1||s>100)return a.NextResponse.json({error:"Invalid page size (must be between 1 and 100)"},{status:400});try{let e=await (0,p.cX)(t,s,n,o,i,u);if(e.error){let r=e.error?.status||500,t=e.error?.message||"Failed to fetch clinics list";return a.NextResponse.json({error:t},{status:r})}return a.NextResponse.json(e)}catch(r){console.error("Error fetching clinics list in API route:",r);let e=r instanceof Error?r.message:"Failed to fetch clinics list";return a.NextResponse.json({error:e},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/clinics/route",pathname:"/api/clinics",filename:"route",bundlePath:"app/api/clinics/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\clinics\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:g}=c;function x(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,580,8137],()=>t(64463));module.exports=s})();