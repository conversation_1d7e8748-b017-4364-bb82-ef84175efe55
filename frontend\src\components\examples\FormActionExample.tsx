'use client';

import { useActionState } from 'next/navigation';
import { useState } from 'react';
import { FiAlertCircle, FiCheckCircle } from 'react-icons/fi';

// This would be imported from your server actions file
// For demonstration purposes, we're defining it here
const submitContactForm = async (prevState: any, formData: FormData) => {
  // Simulate server processing time
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Validate form data
  const name = formData.get('name');
  const email = formData.get('email');
  const message = formData.get('message');
  
  if (!name || typeof name !== 'string' || name.trim().length < 2) {
    return { success: false, message: 'Name is required (min 2 characters)' };
  }
  
  if (!email || typeof email !== 'string' || !email.includes('@')) {
    return { success: false, message: 'Valid email is required' };
  }
  
  if (!message || typeof message !== 'string' || message.trim().length < 10) {
    return { success: false, message: 'Message is required (min 10 characters)' };
  }
  
  // If validation passes, return success
  return { 
    success: true, 
    message: 'Your message has been sent successfully!' 
  };
};

const initialState = {
  success: null as boolean | null,
  message: '',
};

/**
 * Example form component using Next.js 15 Server Actions pattern
 */
export default function FormActionExample() {
  const [state, formAction, isPending] = useActionState(submitContactForm, initialState);
  const [submitted, setSubmitted] = useState(false);
  
  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-semibold mb-4">Contact Form</h2>
      
      {state?.success === true && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-start">
          <FiCheckCircle className="text-green-500 mt-0.5 mr-2 flex-shrink-0" />
          <p className="text-green-700">{state.message}</p>
        </div>
      )}
      
      {state?.success === false && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
          <FiAlertCircle className="text-red-500 mt-0.5 mr-2 flex-shrink-0" />
          <p className="text-red-700">{state.message}</p>
        </div>
      )}
      
      <form action={formAction} className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"
            required
          />
        </div>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"
            required
          />
        </div>
        
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
            Message
          </label>
          <textarea
            id="message"
            name="message"
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"
            required
          ></textarea>
        </div>
        
        <button
          type="submit"
          disabled={isPending}
          className={`w-full py-2 px-4 rounded-md text-white font-medium ${
            isPending
              ? 'bg-emerald-400 cursor-not-allowed'
              : 'bg-emerald-600 hover:bg-emerald-700'
          }`}
        >
          {isPending ? 'Sending...' : 'Send Message'}
        </button>
      </form>
    </div>
  );
}
