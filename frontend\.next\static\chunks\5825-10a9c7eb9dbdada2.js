(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5825],{2634:()=>{},6500:(t,e,a)=>{"use strict";a.d(e,{$:()=>p,f:()=>c});var i=a(6052),n=a.n(i),o=a(1890);let s="https://nice-badge-2130241d6c.strapiapp.com";s||console.error("CRITICAL ERROR: NEXT_PUBLIC_API_URL is not defined in environment variables");let r=s||"";console.log("Using Strapi API URL:",r||"No API URL found in environment variables");let l=r?r.replace("strapiapp.com","media.strapiapp.com"):"";l&&console.log("Strapi Media URL:",l);let c=async function(t){var e,a,i,s,l,c;let p=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},g=Math.random().toString(36).substring(2,8),u=t.startsWith("/")?t:"/".concat(t),d="".concat(r,"/api").concat(u);try{let t=o.env.STRAPI_API_TOKEN,r={"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}},l="no-store";l=p.cache?p.cache:(null==(e=p.next)?void 0:e.revalidate)!==void 0&&0===p.next.revalidate?"no-store":"force-cache";let c={method:p.method||"GET",headers:{...r,...p.headers||{}},cache:l,next:{tags:(null==(a=p.next)?void 0:a.tags)||[],...(null==(i=p.next)?void 0:i.revalidate)!==void 0&&{revalidate:p.next.revalidate}}};if((null==(s=p.next)?void 0:s.revalidate)===!1&&c.next&&(c.next.revalidate=!1),("GET"===c.method||"HEAD"===c.method)&&p.params){let t=n().stringify(p.params,{encodeValuesOnly:!0});t&&(d="".concat(d,"?").concat(t))}else p.params&&"GET"!==c.method&&"HEAD"!==c.method&&(c.body=JSON.stringify(p.params));let u=await fetch(d,c);if(!u.ok){let t;try{t=await u.json()}catch(e){t={message:u.statusText,details:await u.text().catch(()=>"")}}console.error("[".concat(g,"] API Error (").concat(d,"): Status ").concat(u.status),t);let e=Error("API Error: ".concat(u.status," ").concat(u.statusText));throw e.response={status:u.status,data:t},e}return await u.json()}catch(t){throw console.error("[".concat(g,"] API Error (").concat(d,"):"),t.message||"Unknown error"),(null==(c=t.response)?void 0:c.status)&&console.error("[".concat(g,"] Status:"),t.response.status),t.message&&t.message.includes("ECONNREFUSED")&&(console.error("[".concat(g,"] CRITICAL ERROR: Connection refused for ").concat(d,".")),(d.includes("127.0.0.1")||d.includes("localhost"))&&console.error("[".concat(g,"] Attempting to connect to localhost. Check NEXT_PUBLIC_API_URL in Vercel environment variables if this is a deployed environment."))),(null==(l=t.response)?void 0:l.status)===400&&console.error("[".concat(g,"] Bad Request (400) - Check query parameters for Strapi v5 compatibility.")),t.requestId=g,t}},p={clinics:{getAll:async function(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{query:a,location:i,specialtySlug:n,conditionSlug:o,categorySlug:s,page:r=1,pageSize:l=12,cache:p,next:g,...u}=e,d={...u,populate:u.populate||"*",publicationState:"live",pagination:{page:r,pageSize:l},filters:{...u.filters||{}}},v={...u.filters||{}},h=[];if(a&&h.push({$or:[{name:{$containsi:a}},{services:{name:{$containsi:a}}},{specialties:{name:{$containsi:a}}},{conditions:{name:{$containsi:a}}},{categories:{name:{$containsi:a}}},{description:{$containsi:a}}]}),i&&h.push({address:{$or:[{streetAddress1:{$containsi:i}},{city:{$containsi:i}},{stateProvince:{$containsi:i}},{postalCode:{$containsi:i}}]}}),n&&h.push({specialties:{slug:{$eq:n}}}),o&&h.push({conditions:{slug:{$eq:o}}}),s&&h.push({categories:{slug:{$eq:s}}}),h.length>1?v.$and=h:1===h.length&&Object.assign(v,h[0]),d.filters=v,n){let t=["strapi-clinics","strapi-specialties","strapi-specialty-".concat(n),"strapi-specialty-".concat(n,"-clinics")];t.push("strapi-specialty-".concat(n,"-page-").concat(r)),a&&t.push("strapi-specialty-".concat(n,"-query-").concat(a)),i&&t.push("strapi-specialty-".concat(n,"-location-").concat(i));let e={tags:t};return(null==g?void 0:g.revalidate)!==void 0&&(e.revalidate=g.revalidate),c("/clinics",{params:d,cache:p,next:e})}if(s){let t=["strapi-clinics","strapi-categories","strapi-category-".concat(s),"strapi-category-".concat(s,"-clinics")];t.push("strapi-category-".concat(s,"-page-").concat(r)),a&&t.push("strapi-category-".concat(s,"-query-").concat(a)),i&&t.push("strapi-category-".concat(s,"-location-").concat(i));let e={tags:t};return(null==g?void 0:g.revalidate)!==void 0&&(e.revalidate=g.revalidate),c("/clinics",{params:d,cache:p,next:e})}let f=["strapi-clinics-list"],y=(null==(t=d.pagination)?void 0:t.page)||1;f.push("strapi-clinics-page-".concat(y)),a&&f.push("strapi-clinics-query-".concat(a)),i&&f.push("strapi-clinics-location-".concat(i));let m={tags:[...(null==g?void 0:g.tags)||[],...f]};return(null==g?void 0:g.revalidate)!==void 0&&(m.revalidate=g.revalidate),c("/clinics",{params:d,cache:p,next:m})},getAllSlugs:async function(){var t,e,a;let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=["strapi-clinics-slugs",...(null==(t=i.next)?void 0:t.tags)||[]];return c("/clinics",{params:{fields:["slug"],pagination:{pageSize:250}},cache:i.cache||"force-cache",next:{tags:n,revalidate:null!=(a=null==(e=i.next)?void 0:e.revalidate)?a:43200,...i.next||{}}})},getBySlug:async function(t){var e,a,i;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=["strapi-clinics","strapi-clinic-".concat(t),...(null==(e=n.next)?void 0:e.tags)||[]];return c("/clinics",{params:{filters:{slug:{$eq:t}},populate:{logo:!0,address:!0,contactInfo:!0,location:!0,openingHours:!0,services:!0,specialties:!0,conditions:!0,practitioners:{populate:{profilePicture:!0}},appointment_options:!0,payment_methods:!0,seo:!0}},cache:n.cache||"force-cache",next:{...n.next||{},tags:o,revalidate:null!=(i=null==(a=n.next)?void 0:a.revalidate)?i:43200}})},getFeatured:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return c("/clinics",{params:{filters:{isFeatured:{$eq:!0}},populate:"*"},...t})},getByCategorySlug:async t=>{let{slug:e,query:a="",location:i="",page:n=1,pageSize:o=12}=t;try{console.log("Fetching clinics for category slug: ".concat(e,", page: ").concat(n,", query: ").concat(a,", location: ").concat(i));let t=[];t.push({categories:{slug:{$eq:e}}}),a&&t.push({$or:[{name:{$containsi:a}},{services:{name:{$containsi:a}}},{specialties:{name:{$containsi:a}}},{conditions:{name:{$containsi:a}}}]}),i&&t.push({address:{$or:[{streetAddress1:{$containsi:i}},{city:{$containsi:i}},{stateProvince:{$containsi:i}},{postalCode:{$containsi:i}}]}});let s=t.length>1?{$and:t}:{categories:{slug:{$eq:e}}},r=["strapi-clinics","strapi-categories","strapi-category-".concat(e),"strapi-category-".concat(e,"-clinics"),"strapi-category-".concat(e,"-page-").concat(n)];return a&&r.push("strapi-category-".concat(e,"-query-").concat(a)),i&&r.push("strapi-category-".concat(e,"-location-").concat(i)),c("/clinics",{params:{filters:s,pagination:{page:n,pageSize:o},populate:{logo:!0,featuredImage:!0,address:!0,contactInfo:!0,categories:!0},publicationState:"live"},next:{tags:r}})}catch(t){throw console.error("Error in getByCategorySlug for clinics with slug ".concat(e,":"),t),t}}},practitioners:{getAll:async function(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{query:a,location:i,specialtySlug:n,conditionSlug:o,categorySlug:s,page:r=1,pageSize:l=12,cache:p,next:g,...u}=e,d={...u,populate:u.populate||"*",publicationState:"live",pagination:{page:r,pageSize:l},filters:{...u.filters||{}}},v={...u.filters||{}},h=[];if(a&&h.push({$or:[{name:{$containsi:a}},{specialties:{name:{$containsi:a}}},{conditions:{name:{$containsi:a}}}]}),n&&h.push({specialties:{slug:{$eq:n}}}),o&&h.push({conditions:{slug:{$eq:o}}}),s&&h.push({categories:{slug:{$eq:s}}}),h.length>1?v.$and=h:1===h.length&&Object.assign(v,h[0]),d.filters=v,n){let t=["strapi-practitioners","strapi-specialties","strapi-specialty-".concat(n),"strapi-specialty-".concat(n,"-practitioners")];t.push("strapi-specialty-".concat(n,"-page-").concat(r)),a&&t.push("strapi-specialty-".concat(n,"-query-").concat(a)),i&&t.push("strapi-specialty-".concat(n,"-location-").concat(i));let e={tags:t};return(null==g?void 0:g.revalidate)!==void 0&&(e.revalidate=g.revalidate),c("/practitioners",{params:d,cache:p,next:e})}if(s){let t=["strapi-practitioners","strapi-categories","strapi-category-".concat(s),"strapi-category-".concat(s,"-practitioners")];t.push("strapi-category-".concat(s,"-page-").concat(r)),a&&t.push("strapi-category-".concat(s,"-query-").concat(a)),i&&t.push("strapi-category-".concat(s,"-location-").concat(i));let e={tags:t};return(null==g?void 0:g.revalidate)!==void 0&&(e.revalidate=g.revalidate),c("/practitioners",{params:d,cache:p,next:e})}let f=["strapi-practitioners-list"],y=(null==(t=d.pagination)?void 0:t.page)||1;f.push("strapi-practitioners-page-".concat(y)),a&&f.push("strapi-practitioners-query-".concat(a)),i&&f.push("strapi-practitioners-location-".concat(i));let m={tags:[...(null==g?void 0:g.tags)||[],...f]};return(null==g?void 0:g.revalidate)!==void 0&&(m.revalidate=g.revalidate),c("/practitioners",{params:d,cache:p,next:m})},getAllSlugs:async function(){var t,e,a,i;let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=[],s=1,r=!0,l={cache:n.cache||"force-cache",next:{revalidate:null!=(a=null==(t=n.next)?void 0:t.revalidate)?a:43200,tags:["strapi-practitioners-slugs",...(null==(e=n.next)?void 0:e.tags)||[]],...n.next||{}}};for(l.next.tags&&(l.next.tags=Array.from(new Set(l.next.tags)));r;)try{let t=await c("/practitioners",{params:{fields:["slug"],pagination:{page:s,pageSize:100}},cache:l.cache,next:l.next});if((null==t?void 0:t.data)&&Array.isArray(t.data)){t.data.forEach(t=>{t&&t.slug&&o.push({slug:t.slug})});let e=null==(i=t.meta)?void 0:i.pagination;t.data.length<100||!e||s>=e.pageCount?r=!1:s++}else r=!1}catch(t){console.error("Error fetching page ".concat(s," of practitioner slugs:"),t),r=!1}return{data:o}},getBySlug:async function(t){var e,a,i;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=["strapi-practitioner","strapi-practitioner-".concat(t)];return c("/practitioners",{params:{filters:{slug:{$eq:t}},populate:{profilePicture:!0,contactInfo:!0,specialties:!0,conditions:!0,clinics:!0,seo:!0}},cache:n.cache||"force-cache",next:{...n.next||{},tags:[...o,...(null==(e=n.next)?void 0:e.tags)||[]],revalidate:null!=(i=null==(a=n.next)?void 0:a.revalidate)?i:43200}})},getFeatured:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return c("/practitioners",{params:{filters:{isFeatured:{$eq:!0}},populate:"*"},...t})},getByCategorySlug:async t=>{let{slug:e,query:a="",location:i="",page:n=1,pageSize:o=12}=t;try{let t=[];t.push({categories:{slug:{$eq:e}}}),a&&t.push({$or:[{name:{$containsi:a}},{title:{$containsi:a}},{qualifications:{$containsi:a}},{specialties:{name:{$containsi:a}}},{conditions:{name:{$containsi:a}}}]}),i&&t.push({address:{$or:[{streetAddress1:{$containsi:i}},{city:{$containsi:i}},{stateProvince:{$containsi:i}},{postalCode:{$containsi:i}}]}});let s=t.length>1?{$and:t}:{categories:{slug:{$eq:e}}},r=["strapi-practitioners","strapi-categories","strapi-category-".concat(e),"strapi-category-".concat(e,"-practitioners"),"strapi-category-".concat(e,"-page-").concat(n)];return a&&r.push("strapi-category-".concat(e,"-query-").concat(a)),i&&r.push("strapi-category-".concat(e,"-location-").concat(i)),c("/practitioners",{params:{filters:s,pagination:{page:n,pageSize:o},populate:{profilePicture:!0,contactInfo:!0,specialties:!0,conditions:!0,categories:!0},publicationState:"live"},next:{tags:r}})}catch(t){throw console.error("Error in getByCategorySlug for practitioners with slug ".concat(e,":"),t),t}}},categories:{getAll:async function(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{query:a,location:i,page:n=1,pageSize:o=12,cache:s,next:r,...l}=e,p={...l,populate:l.populate||{icon:!0,featuredImage:!0},publicationState:"live",pagination:{page:n,pageSize:o},filters:{...l.filters||{}}};a&&p.filters&&(p.filters.name={$containsi:a});let g=["strapi-categories","strapi-categories-slugs"];(null==(t=p.pagination)?void 0:t.page)&&g.push("strapi-categories-page-".concat(p.pagination.page)),a&&g.push("strapi-categories-query-".concat(a));let u={tags:[...(null==r?void 0:r.tags)||[],...g]};return(null==r?void 0:r.revalidate)!==void 0&&(u.revalidate=r.revalidate),c("/categories",{params:p,cache:s,next:u})},getBySlug:async function(t){var e;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=["strapi-categories","strapi-category-".concat(t),"strapi-category-".concat(t,"-clinics"),"strapi-category-".concat(t,"-practitioners")];return c("/categories",{params:{filters:{slug:{$eq:t}},populate:{seo:{populate:{openGraph:{populate:{ogImage:!0}},metaImage:!0}},icon:!0,featuredImage:!0,clinics:{populate:{logo:!0,featuredImage:!0,address:!0,contactInfo:!0}},practitioners:{populate:{profilePicture:!0,contactInfo:!0}}},publicationState:"live"},next:{...a.next||{},tags:[...i,...(null==(e=a.next)?void 0:e.tags)||[]]}})},getAllSlugs:async function(){var t,e,a;let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return c("/categories",{params:{fields:["slug"],pagination:{pageSize:1e3}},cache:i.cache||"force-cache",next:{...i.next||{},tags:["strapi-categories-slugs",...(null==(t=i.next)?void 0:t.tags)||[]],revalidate:null!=(a=null==(e=i.next)?void 0:e.revalidate)?a:43200}})},getFooterCategories:async function(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return c("/categories",{params:{filters:{showInFooter:{$eq:!0}},populate:"*",publicationState:"live"},next:{...e.next||{},tags:["strapi-categories","strapi-categories-footer",...(null==(t=e.next)?void 0:t.tags)||[]]}})}},blog:{getPosts:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let e={publicationState:"live"},a=1,i=10;t.pagination?(e.pagination=t.pagination,a=t.pagination.page||1,i=t.pagination.pageSize||10):(t.page||t.pageSize)&&(a=t.page||1,i=t.pageSize||10,e.pagination={page:a,pageSize:i}),t.sort?e.sort=t.sort:e.sort=["publishDate:desc"],t.filters?e.filters=t.filters:t.categorySlug?e.filters={...e.filters||{},blog_categories:{slug:{$eq:t.categorySlug}}}:t.tagSlug?e.filters={...e.filters||{},blog_tags:{slug:{$eq:t.tagSlug}}}:t.query&&(e.filters={...e.filters||{},$or:[{title:{$containsi:t.query}},{excerpt:{$containsi:t.query}}]}),t.populate?e.populate=t.populate:e.populate={featuredImage:!0,author_blogs:{populate:{profilePicture:!0}},blog_categories:!0,blog_tags:!0};let n=["strapi-blog-posts"];return t.categorySlug&&(n.push("strapi-category-".concat(t.categorySlug)),n.push("strapi-category-".concat(t.categorySlug,"-page-").concat(a))),t.tagSlug&&(n.push("strapi-tag-".concat(t.tagSlug)),n.push("strapi-tag-".concat(t.tagSlug,"-page-").concat(a))),t.query&&n.push("strapi-blog-query-".concat(t.query)),await c("/blog-posts",{params:e,next:{tags:n}})}catch(t){throw console.error("Error in blog.getPosts:",t),t.response&&(console.error("Response status:",t.response.status),console.error("Response data:",t.response.data),console.error("Response headers:",t.response.headers)),t}},getAllSlugs:async function(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return c("/blog-posts",{params:{fields:["slug"],pagination:{pageSize:1e3}},next:{...e.next||{},tags:["strapi-blog-posts-slugs",...(null==(t=e.next)?void 0:t.tags)||[]]}})},getPostBySlug:async function(t){var e;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=["strapi-blog-posts","strapi-blog-post-".concat(t)];return c("/blog-posts",{params:{filters:{slug:{$eq:t}},populate:{seo:{populate:{metaImage:!0,openGraph:!0}},featuredImage:!0,author_blogs:{fields:["id","name","slug","bio"],populate:{profilePicture:!0}},blog_categories:{fields:["id","name","slug"]},blog_tags:{fields:["id","name","slug"]}}},next:{...a.next||{},tags:[...i,...(null==(e=a.next)?void 0:e.tags)||[]]}})},getCategories:async function(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{next:i,...n}=e;return c("/blog-categories",{params:{...n,publicationState:"live",populate:n.populate||"*"},next:{...a.next||{},...i||{},tags:["strapi-categories","strapi-blog-categories",...(null==(t=a.next)?void 0:t.tags)||[],...(null==i?void 0:i.tags)||[]]}})},getCategoryBySlug:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{var i,n;let a=(null==(i=e.pagination)?void 0:i.page)||1,o=(null==(n=e.pagination)?void 0:n.pageSize)||12,s=e.sort||"publishDate:desc",r=["strapi-categories","strapi-blog-categories","strapi-category-".concat(t),"strapi-category-".concat(t,"-page-").concat(a)],l={filters:{slug:{$eq:t}},pagination:{page:1,pageSize:1},sort:[s],populate:{blog_posts:{fields:["title","slug","excerpt","publishDate","publishedAt"],populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}},pagination:{page:a,pageSize:o},sort:[s]},seo:!0}};return await c("/blog-categories",{params:l,next:{tags:r}})}catch(i){console.error("Error in getCategoryBySlug for slug ".concat(t,":"),i);try{let{next:i,...n}=e;return await c("/blog-categories",{params:{...n,filters:{slug:{$eq:t}},populate:{blog_posts:{populate:["featuredImage","author_blogs.profilePicture"]},seo:!0}},next:{...a.next||{},...i||{}}})}catch(e){throw console.error("Fallback also failed for slug ".concat(t,":"),e),e}}},getTags:async function(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return c("/blog-tags",{params:{populate:"*"},next:{...e.next||{},tags:["strapi-tags","strapi-blog-tags",...(null==(t=e.next)?void 0:t.tags)||[]]}})},getTagBySlug:async function(t){var e;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=["strapi-tags","strapi-blog-tags","strapi-tag-".concat(t)];return c("/blog-tags",{params:{filters:{slug:{$eq:t}},populate:{blog_posts:{populate:{featuredImage:{fields:["url","alternativeText","width","height","formats"]},author_blogs:{populate:{profilePicture:{fields:["url","alternativeText","width","height","formats"]}}}}}}},next:{...a.next||{},tags:[...i,...(null==(e=a.next)?void 0:e.tags)||[]]}})},getAuthors: <AUTHORS>