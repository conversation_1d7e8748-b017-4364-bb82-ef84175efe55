{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9f600e88.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9f600e88-module__gNV4tq__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9f600e88.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22global-error.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/app/global-error.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Inter } from 'next/font/google';\nimport './globals.css';\n\nconst inter = Inter({ subsets: ['latin'] });\n\ninterface GlobalErrorProps {\n  error: Error & { digest?: string };\n  reset: () => void;\n}\n\n/**\n * Global error page for the entire Next.js application\n * This is used by Next.js when an error occurs in the root layout\n */\nexport default function GlobalError({ error, reset }: GlobalErrorProps) {\n  return (\n    <html lang=\"en\">\n      <body className={inter.className}>\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50 px-4\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-md p-8\">\n            <div className=\"flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full bg-red-100\">\n              <svg className=\"w-6 h-6 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"></path>\n              </svg>\n            </div>\n            \n            <h1 className=\"text-xl font-semibold text-center text-gray-900 mb-2\">\n              Something went wrong!\n            </h1>\n            \n            <p className=\"text-gray-600 text-center mb-6\">\n              We're sorry, but there was a critical error loading the application.\n            </p>\n            \n            <div className=\"flex justify-center\">\n              <button\n                onClick={reset}\n                className=\"px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors\"\n              >\n                Try Again\n              </button>\n            </div>\n            \n            {process.env.NODE_ENV === 'development' && (\n              <div className=\"mt-6 p-3 bg-gray-100 rounded overflow-auto max-h-40\">\n                <p className=\"text-sm font-mono text-red-600\">{error.message}</p>\n                {error.stack && (\n                  <pre className=\"mt-2 text-xs text-gray-700 whitespace-pre-wrap\">\n                    {error.stack.split('\\n').slice(0, 3).join('\\n')}\n                  </pre>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;AA8Ca;;;AA9Cb;;;;AAiBe,SAAS,YAAY,EAAE,KAAK,EAAE,KAAK,EAAoB;IACpE,qBACE,6LAAC;QAAK,MAAK;kBACT,cAAA,6LAAC;YAAK,WAAW,4IAAA,CAAA,UAAK,CAAC,SAAS;sBAC9B,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAuB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCAAY,OAAM;0CAChG,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAY;oCAAI,GAAE;;;;;;;;;;;;;;;;sCAIzE,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAIrE,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAI9C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;wBAKF,oDAAyB,+BACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAkC,MAAM,OAAO;;;;;;gCAC3D,MAAM,KAAK,kBACV,6LAAC;oCAAI,WAAU;8CACZ,MAAM,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9D;KA5CwB", "debugId": null}}]}