"use client";

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { getStrapiContent } from '@/lib/strapi';
import ClinicCard from '@/components/clinics/ClinicCard';
import PractitionerCard from '@/components/practitioners/PractitionerCard';
import BlogPostCard from '@/components/blog/BlogPostCard';
import CategoryCard from '@/components/categories/CategoryCard';
import SearchInputCallback from '@/components/shared/SearchInputCallback';
import Pagination from '@/components/shared/Pagination';
import Link from 'next/link';
import { FiSearch } from 'react-icons/fi';

// Define types for our search results
type SearchResults = {
  clinics: any[];
  practitioners: any[];
  blogPosts: any[];
  categories: any[];
  specialties: any[];
  conditions: any[];
  totalResults: number;
};

export default function SearchPage() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const page = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = 6; // Number of results per content type

  const [searchQuery, setSearchQuery] = useState(query);
  const [results, setResults] = useState<SearchResults>({
    clinics: [],
    practitioners: [],
    blogPosts: [],
    categories: [],
    specialties: [],
    conditions: [],
    totalResults: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to map blog post data from Strapi v5 to our component format
  const mapBlogPostData = (post: any) => {
    if (!post) return null;

    const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || '';

    // Extract author data if available
    const authorData = post.author_blogs?.[0] || null;

    // Helper function to safely get the profile picture URL
    const getProfilePictureUrl = (authorData: any) => {
      if (!authorData) return null;

      // Case 1: Direct URL in profilePicture.url
      if (authorData.profilePicture?.url) {
        return `${strapiBaseUrl}${authorData.profilePicture.url}`;
      }

      // Case 2: Nested data structure with data.attributes
      if (authorData.profilePicture?.data?.attributes?.url) {
        return `${strapiBaseUrl}${authorData.profilePicture.data.attributes.url}`;
      }

      // Case 3: Direct formats for responsive images
      if (authorData.profilePicture?.formats?.thumbnail?.url) {
        return `${strapiBaseUrl}${authorData.profilePicture.formats.thumbnail.url}`;
      }

      // Case 4: Nested formats
      if (authorData.profilePicture?.data?.attributes?.formats?.thumbnail?.url) {
        return `${strapiBaseUrl}${authorData.profilePicture.data.attributes.formats.thumbnail.url}`;
      }

      return null;
    };

    const profilePictureUrl = getProfilePictureUrl(authorData);

    const author = authorData ? {
      name: authorData.name || 'Unknown Author',
      slug: authorData.slug || '',
      profile_picture: profilePictureUrl
    } : null;

    // Extract featured image URL
    const featuredImageUrl = post.featuredImage?.url
      ? `${strapiBaseUrl}${post.featuredImage.url}`
      : null;

    // Handle documentId as the primary ID in Strapi v5
    const id = post.id || post.documentId || '';

    // Create the blog post object without view_count directly in the object
    const blogPost = {
      id,
      title: post.title || 'Untitled Post',
      slug: post.slug || `post-${id}`,
      excerpt: post.excerpt || null,
      featured_image: featuredImageUrl,
      publish_date: post.publishDate || post.createdAt || new Date().toISOString(),
      content: post.content || '',
      reading_time: post.readingTime || 2,
      isFeatured: post.isFeatured || false,
      author
    };

    // Add view_count as a non-enumerable property so it's not rendered directly
    Object.defineProperty(blogPost, 'view_count', {
      value: post.view_count || 0,
      enumerable: false
    });

    return blogPost;
  };

  // Handle search submission
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
  };

  useEffect(() => {
    const fetchSearchResults = async () => {
      if (!query) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Fetch results from different content types
        const [
          clinicsData,
          practitionersData,
          blogPostsData,
          categoriesData,
          specialtiesData,
          conditionsData
        ] = await Promise.all([
          getStrapiContent.clinics.getAll({
            query,
            page,
            pageSize,
            populate: '*'
          }),
          getStrapiContent.practitioners.getAll({
            query,
            page,
            pageSize,
            populate: '*'
          }),
          getStrapiContent.blog.getPosts({
            filters: {
              $or: [
                { title: { $containsi: query } },
                { excerpt: { $containsi: query } },
                { content: { $containsi: query } }
              ]
            },
            pagination: { page, pageSize },
            populate: {
              featuredImage: true,
              author_blogs: {
                populate: {
                  profilePicture: true
                }
              }
            }
          }),
          getStrapiContent.categories.getAll({
            query,
            page,
            pageSize,
            populate: '*'
          }),
          getStrapiContent.specialties.getAll({
            query,
            page,
            pageSize,
            populate: '*'
          }),
          getStrapiContent.conditions.getAll({
            query,
            page,
            pageSize,
            populate: '*'
          })
        ]);

        // Process blog posts data
        const processedBlogPosts = blogPostsData?.data
          ? blogPostsData.data.map(mapBlogPostData).filter(post => post !== null)
          : [];

        // Calculate total results
        const totalResults = (
          (clinicsData?.data?.length || 0) +
          (practitionersData?.data?.length || 0) +
          (processedBlogPosts?.length || 0) +
          (categoriesData?.data?.length || 0) +
          (specialtiesData?.data?.length || 0) +
          (conditionsData?.data?.length || 0)
        );

        setResults({
          clinics: clinicsData?.data || [],
          practitioners: practitionersData?.data || [],
          blogPosts: processedBlogPosts,
          categories: categoriesData?.data || [],
          specialties: specialtiesData?.data || [],
          conditions: conditionsData?.data || [],
          totalResults
        });
      } catch (err) {
        console.error('Error fetching search results:', err);
        setError('Failed to load search results. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSearchResults();
  }, [query, page, pageSize]);

  // Calculate total pages for pagination
  const totalPages = Math.ceil(results.totalResults / (pageSize * 6)); // 6 content types

  return (
    <>
      {/* Search Header */}
      <div className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-6">Search Results</h1>
            <div className="max-w-2xl mx-auto">
              <SearchInputCallback
                placeholder="Search for clinics, practitioners, or health topics..."
                onSearch={handleSearch}
                buttonText="Search"
                buttonClassName="bg-emerald-800 text-white hover:bg-emerald-900"
                className="shadow-lg"
                initialValue={query}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Search Results */}
      <div className="container mx-auto px-4 py-12">
        {isLoading ? (
          // Loading state
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500"></div>
          </div>
        ) : error ? (
          // Error state
          <div className="text-center p-8 bg-red-50 rounded-lg text-red-600">
            {error}
          </div>
        ) : results.totalResults === 0 ? (
          // No results state
          <div className="text-center p-8 bg-gray-50 rounded-lg">
            <FiSearch className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h2 className="text-2xl font-bold text-gray-800 mb-2">No results found</h2>
            <p className="text-gray-600 mb-6">
              We couldn't find any matches for "{query}". Please try a different search term.
            </p>
            <Link
              href="/"
              className="inline-block bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium"
            >
              Return to Homepage
            </Link>
          </div>
        ) : (
          // Results state
          <div className="space-y-12">
            {/* Results summary */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                Found {results.totalResults} results for "{query}"
              </h2>
            </div>

            {/* Clinics Section */}
            {results.clinics.length > 0 && (
              <div className="mb-12">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-800">Clinics</h2>
                  <Link href={`/clinics?query=${encodeURIComponent(query)}`} className="text-emerald-600 hover:text-emerald-700">
                    View all clinic results
                  </Link>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {results.clinics.map(clinic => (
                    <ClinicCard
                      key={clinic.id}
                      clinic={{
                        id: clinic.id,
                        name: clinic.name || 'Unnamed Clinic',
                        slug: clinic.slug || `clinic-${clinic.id}`,
                        description: clinic.description,
                        logo: clinic.logo?.url
                          ? `${process.env.NEXT_PUBLIC_STRAPI_API_URL}${clinic.logo.url}`
                          : null,
                        featuredImage: clinic.featuredImage?.url
                          ? `${process.env.NEXT_PUBLIC_STRAPI_API_URL}${clinic.featuredImage.url}`
                          : null,
                        address: clinic.address,
                        contactInfo: clinic.contactInfo,
                        isVerified: clinic.isVerified || false
                      }}
                      showContactInfo={false}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Practitioners Section */}
            {results.practitioners.length > 0 && (
              <div className="mb-12">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-800">Practitioners</h2>
                  <Link href={`/practitioners?query=${encodeURIComponent(query)}`} className="text-emerald-600 hover:text-emerald-700">
                    View all practitioner results
                  </Link>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {results.practitioners.map(practitioner => (
                    <PractitionerCard
                      key={practitioner.id}
                      practitioner={{
                        id: practitioner.id,
                        name: practitioner.name || 'Unnamed Practitioner',
                        slug: practitioner.slug || `practitioner-${practitioner.id}`,
                        bio: practitioner.bio,
                        profilePicture: practitioner.profilePicture?.url
                          ? `${process.env.NEXT_PUBLIC_STRAPI_API_URL}${practitioner.profilePicture.url}`
                          : null,
                        qualifications: practitioner.qualifications,
                        contactInfo: practitioner.contactInfo,
                        isVerified: practitioner.isVerified || false
                      }}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Blog Posts Section */}
            {results.blogPosts.length > 0 && (
              <div className="mb-12">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-800">Blog Posts</h2>
                  <Link href={`/blog?query=${encodeURIComponent(query)}`} className="text-emerald-600 hover:text-emerald-700">
                    View all blog results
                  </Link>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {results.blogPosts.map(post => (
                    <BlogPostCard
                      key={post.id}
                      post={post}
                      showReadingTime={true}
                      showShareButton={true}
                      showBadge={true}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Categories Section */}
            {results.categories.length > 0 && (
              <div className="mb-12">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-800">Categories</h2>
                  <Link href={`/categories?query=${encodeURIComponent(query)}`} className="text-emerald-600 hover:text-emerald-700">
                    View all category results
                  </Link>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                  {results.categories.map(category => (
                    <CategoryCard
                      key={category.id}
                      category={{
                        id: category.id,
                        name: category.name || 'Unnamed Category',
                        slug: category.slug || `category-${category.id}`,
                        description: category.description,
                        icon: category.icon?.url
                          ? `${process.env.NEXT_PUBLIC_STRAPI_API_URL}${category.icon.url}`
                          : null,
                        featured_image: category.featuredImage?.url
                          ? `${process.env.NEXT_PUBLIC_STRAPI_API_URL}${category.featuredImage.url}`
                          : null
                      }}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-12 flex justify-center">
                <Pagination totalPages={totalPages} />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Call to Action */}
      <div className="bg-gray-50 py-12">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Can't find what you're looking for?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Browse our categories or contact us for personalized assistance with your natural healing journey.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/categories"
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium"
            >
              Explore Categories
            </Link>
            <Link
              href="/contact"
              className="bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-medium"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
