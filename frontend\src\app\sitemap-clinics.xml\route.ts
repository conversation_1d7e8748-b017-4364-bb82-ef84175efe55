import { getStrapiContent } from '@/lib/strapi';

// Define the site URL from environment variable
// We need an absolute URL for sitemaps to work properly
// For sitemaps, we should use the frontend URL, not the Strapi API URL
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log a warning if SITE_URL is not set
if (!process.env.NEXT_PUBLIC_SITE_URL && !process.env.NEXT_PUBLIC_API_URL) {
  console.warn('WARNING: Neither NEXT_PUBLIC_SITE_URL nor NEXT_PUBLIC_API_URL environment variables are set. Using default vercel.app URL as a placeholder.');
}

// Log the URL being used for debugging
// console.log(`Using site URL for clinics sitemap: ${SITE_URL}`);

// Ensure SITE_URL doesn't have a trailing slash
const normalizedSiteUrl = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;

// Define the page size for pagination - increased to ensure all clinics are included
const PAGE_SIZE = 1000; // Increased to handle all clinics in fewer API calls

// Define the type for sitemap entries
interface SitemapEntry {
  url: string;
  lastModified: Date;
  changeFrequency: string;
  priority: number;
}

// This is a Route Handler for Next.js App Router
// See: https://nextjs.org/docs/app/building-your-application/routing/route-handlers
export async function GET(_request: Request): Promise<Response> {
  // Initialize the sitemap array
  let sitemapEntries: SitemapEntry[] = [];

  try {
    console.log('Generating clinics sitemap...');

    // First, get the total count of clinics
    const countResponse = await getStrapiContent.clinics.getAll({
      pagination: { pageSize: 1, page: 1 },
      fields: ['id'], // Only fetch IDs for counting
      sort: ['id:asc'], // Consistent sorting
      filters: { isActive: { $eq: true } }, // Filter by isActive: true
    });

    const totalClinics = countResponse?.meta?.pagination?.total || 0;
    const totalPages = Math.ceil(totalClinics / PAGE_SIZE);

    console.log(`Found ${totalClinics} total clinics, will fetch in ${totalPages} pages with page size ${PAGE_SIZE}`);
    console.log(`API URL being used: ${process.env.NEXT_PUBLIC_API_URL || 'Not set'}`);
    console.log(`Site URL being used: ${SITE_URL}`);

    if (totalClinics === 0) {
      console.warn('No clinics found in the database. Check Strapi connection and data.');
    }

    // Log pagination details from the count response
    // if (countResponse?.meta?.pagination) {
    //   console.log('Pagination details:', JSON.stringify(countResponse.meta.pagination));
    // }

    // Fetch clinics page by page
    for (let page = 1; page <= totalPages; page++) {
      console.log(`Fetching clinics page ${page} of ${totalPages}...`);

      try {
        // Fetch clinics with minimal fields to reduce payload size
        const clinicsResponse = await getStrapiContent.clinics.getAll({
          pagination: { pageSize: PAGE_SIZE, page },
          fields: ['slug', 'updatedAt', 'createdAt', 'isActive'], // Fetch isActive to confirm
          // Explicitly set sort to ensure consistent ordering
          sort: ['id:asc'], // Sort by ID to ensure we get all clinics in a consistent order
          filters: { isActive: { $eq: true } }, // Filter by isActive: true
          // publicationState: 'live', // Removing this as isActive should be the determinant
        });

        // Log the raw response for debugging - REDUCED
        // console.log(`Raw response structure for page ${page}:`,
        //   JSON.stringify({
        //     meta: clinicsResponse?.meta,
        //     dataLength: clinicsResponse?.data?.length,
        //     firstItem: clinicsResponse?.data?.[0] ?
        //       {
        //         id: clinicsResponse.data[0].id,
        //         documentId: clinicsResponse.data[0].documentId,
        //         slug: clinicsResponse.data[0].slug ||
        //               (clinicsResponse.data[0].attributes ? clinicsResponse.data[0].attributes.slug : undefined),
        //         hasAttributes: !!clinicsResponse.data[0].attributes
        //       } :
        //       'none',
        //     lastItem: clinicsResponse?.data?.length > 0 ?
        //       {
        //         id: clinicsResponse.data[clinicsResponse.data.length - 1].id,
        //         documentId: clinicsResponse.data[clinicsResponse.data.length - 1].documentId,
        //         slug: clinicsResponse.data[clinicsResponse.data.length - 1].slug ||
        //               (clinicsResponse.data[clinicsResponse.data.length - 1].attributes ?
        //                clinicsResponse.data[clinicsResponse.data.length - 1].attributes.slug : undefined)
        //       } :
        //       'none'
        //   })
        // );

        // Log pagination details from this response - REDUCED
        // if (clinicsResponse?.meta?.pagination) {
        //   console.log(`Pagination details for page ${page}:`, JSON.stringify(clinicsResponse.meta.pagination));
        // }

        const clinics = clinicsResponse?.data || [];
        console.log(`Retrieved ${clinics.length} clinics on page ${page}`);

        // Check if we got fewer clinics than expected (except for the last page)
        if (clinics.length < PAGE_SIZE && page < totalPages) {
          console.warn(`Warning: Got only ${clinics.length} clinics on page ${page}, expected ${PAGE_SIZE}. This may indicate a pagination issue.`);
        }

        // Process each clinic
        for (const clinic of clinics) {
          // Extract the slug - handle both Strapi v4 and v5 response formats
          let slug = null;

          // Direct access (Strapi v5 format)
          if (clinic.slug) {
            slug = clinic.slug;
          }
          // Attributes wrapper (Strapi v4 format)
          else if (clinic.attributes && clinic.attributes.slug) {
            slug = clinic.attributes.slug;
          }
          // Try to find slug in any property if not found in standard locations - AVOID HEAVY DEBUGGING IN PROD SITEMAP
          // else {
            // Log the clinic object to help debug
            // console.log(`Clinic without standard slug location:`, JSON.stringify(clinic));

            // Try to find a property that might be the slug
            // for (const key in clinic) {
            //   if (typeof clinic[key] === 'string' &&
            //       (key.toLowerCase().includes('slug') ||
            //        (clinic[key].length > 0 &&
            //         clinic[key].length < 100 &&
            //         /^[a-z0-9-]+$/.test(clinic[key])))) {
            //     slug = clinic[key];
            //     console.log(`Found potential slug in property ${key}: ${slug}`);
            //     break;
            //   }
            // }
          // }

          if (!slug) {
            // console.warn(`Found clinic without slug: ${JSON.stringify({ // REDUCED LOGGING
            //   id: clinic.id || 'unknown',
            //   documentId: clinic.documentId || 'unknown',
            //   keys: Object.keys(clinic)
            // })}`);
            // console.warn(`Found clinic without slug. ID: ${clinic.id || 'unknown'}, DocID: ${clinic.documentId || 'unknown'}`);
            // Log the full clinic object if slug is not found to help debug its structure
            // Also log isActive status if available
            console.warn(`Clinic object for which slug was not found (ID: ${clinic.id || 'unknown'}, isActive: ${clinic.isActive ?? 'N/A'}):`, JSON.stringify(clinic, null, 2));
            continue; // Skip this clinic
          }

          // Get the last modified date
          let updatedAt;
          if (clinic.updatedAt) {
            updatedAt = new Date(clinic.updatedAt);
          } else if (clinic.attributes && clinic.attributes.updatedAt) {
            updatedAt = new Date(clinic.attributes.updatedAt);
          } else if (clinic.createdAt) {
            updatedAt = new Date(clinic.createdAt);
          } else if (clinic.attributes && clinic.attributes.createdAt) {
            updatedAt = new Date(clinic.attributes.createdAt);
          } else {
            updatedAt = new Date(); // Fallback to current date
          }

          // Add to sitemap entries
          sitemapEntries.push({
            url: `${normalizedSiteUrl}/clinics/${slug}`,
            lastModified: updatedAt,
            changeFrequency: 'weekly',
            priority: 0.8,
          });

          // Log each clinic we're adding to help with debugging - REDUCED
          // console.log(`Added clinic to sitemap: ${slug}`);
        }

        // console.log(`Total entries after page ${page}: ${sitemapEntries.length}`); // REDUCED

      } catch (pageError) {
        console.error(`Error fetching clinics page ${page}:`, pageError);
        // Continue with the next page instead of failing the entire sitemap
      }
    }

    console.log(`Total clinic entries in sitemap: ${sitemapEntries.length}`);

    // Add the clinics index page to the sitemap
    sitemapEntries.unshift({
      url: `${normalizedSiteUrl}/clinics`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    });

    console.log(`Final sitemap entries count (including index page): ${sitemapEntries.length}`);

    // Log a summary of the sitemap generation process
    console.log(`Sitemap generation summary:
    - Total clinics found: ${totalClinics}
    - Total pages fetched: ${totalPages}
    - Page size used: ${PAGE_SIZE}
    - Final sitemap entries: ${sitemapEntries.length}
    - Sitemap URL: ${normalizedSiteUrl}/sitemap-clinics.xml
    `);

    // Convert the sitemap to XML with proper formatting
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries.map((entry) => `  <url>
    <loc>${entry.url}</loc>
    <lastmod>${entry.lastModified.toISOString()}</lastmod>
    <changefreq>${entry.changeFrequency}</changefreq>
    <priority>${entry.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

    // Validate the XML structure before returning
    try {
      // Simple validation - check for basic XML structure issues
      if (!xml.startsWith('<?xml') || !xml.includes('<urlset') || !xml.includes('</urlset>')) {
        console.error('Generated XML appears to be malformed');
        throw new Error('Malformed XML');
      }

      // Set cache control headers to ensure the sitemap is not cached for too long
      return new Response(xml, {
        headers: {
          'Content-Type': 'application/xml; charset=utf-8',
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
          'X-Content-Type-Options': 'nosniff',
        },
      });
    } catch (xmlError) {
      console.error('Error validating XML:', xmlError);
      // Fall back to a simpler XML structure
      const fallbackXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${normalizedSiteUrl}/clinics</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;

      return new Response(fallbackXml, {
        headers: {
          'Content-Type': 'application/xml; charset=utf-8',
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
          'X-Content-Type-Options': 'nosniff',
        },
      });
    }
  } catch (error) {
    console.error('Error generating clinics sitemap:', error);

    // If we have some entries, use them even if there was an error
    if (sitemapEntries.length > 0) {
      console.log(`Returning partial sitemap with ${sitemapEntries.length} entries despite error`);

      // Add the clinics index page if it's not already there
      if (!sitemapEntries.some(entry => entry.url === `${normalizedSiteUrl}/clinics`)) {
        sitemapEntries.unshift({
          url: `${normalizedSiteUrl}/clinics`,
          lastModified: new Date(),
          changeFrequency: 'daily',
          priority: 0.9,
        });
      }

      const partialXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries.map((entry) => `  <url>
    <loc>${entry.url}</loc>
    <lastmod>${entry.lastModified.toISOString()}</lastmod>
    <changefreq>${entry.changeFrequency}</changefreq>
    <priority>${entry.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

      return new Response(partialXml, {
        headers: {
          'Content-Type': 'application/xml; charset=utf-8',
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
          'X-Content-Type-Options': 'nosniff',
        },
      });
    }

    // Return a basic XML in case of error with no entries
    console.log('Returning fallback sitemap with only the clinics index page');
    const errorXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${normalizedSiteUrl}/clinics</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;

    return new Response(errorXml, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'no-cache', // Don't cache error responses
        'X-Content-Type-Options': 'nosniff',
      },
    });
  }
}
