// Defines the structure of the payload received from Strapi webhooks.
// This can vary based on your Strapi version and specific webhook configuration.
// It's a good practice to inspect actual webhook payloads from Strapi to refine this.

export interface StrapiWebhookPayload {
  event: StrapiWebhookEvent; // e.g., 'entry.create', 'entry.update', 'entry.delete'
  createdAt: string; // ISO date string
  model: string; // The API ID (singular) of the content type, e.g., 'clinic', 'article'
  entry: StrapiWebhookEntry; // The actual content entry that was changed
  // Strapi might include other fields like `media` for media events.
}

// The type of event that triggered the webhook
export type StrapiWebhookEvent =
  | 'entry.create'
  | 'entry.update'
  | 'entry.delete'
  | 'entry.publish'
  | 'entry.unpublish'
  | 'media.create'
  | 'media.update'
  | 'media.delete';
  // Add other events as needed

// Represents the entry data within the webhook payload.
// This is a generic representation; specific fields will depend on the content type (`model`).
// It's often a partial representation of your full content type.
export interface StrapiWebhookEntry {
  id: number;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string | null;
  // Common fields you might expect, like slug, for routing/tagging
  slug?: string;
  // Relations might be included, often as simple objects with IDs or slugs
  // Example: categories might be an array of objects like { id: number, slug: string }
  categories?: Array<{ id: number; slug?: string; [key: string]: any }>;
  clinic?: { id: number; slug?: string; [key: string]: any }; // Example for practitioner linked to a clinic
  // Add other common fields or relations you expect in webhook payloads
  // The actual structure depends heavily on what Strapi sends.
  // Use `any` for other fields that vary by model, or create more specific entry types.
  [key: string]: any;
}

// Example of a more specific entry type if needed:
// export interface ClinicWebhookEntry extends StrapiWebhookEntry {
//   name: string;
//   slug: string;
//   // other clinic-specific fields that Strapi sends in webhooks
// }
