import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import SearchInput from '@/components/shared/SearchInput';

// Mock the Next.js navigation hooks
const mockReplace = jest.fn();
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(() => ({
    get: jest.fn(() => null),
    toString: jest.fn(() => ''),
  })),
  usePathname: jest.fn(() => '/test'),
  useRouter: jest.fn(() => ({
    replace: mockReplace,
  })),
}));

// Mock the debounce hook
jest.mock('use-debounce', () => ({
  useDebouncedCallback: (fn) => fn,
}));

describe('SearchInput Component', () => {
  it('renders correctly with default props', () => {
    render(<SearchInput placeholder="Search..." />);

    // Check that the input is rendered
    const input = screen.getByRole('textbox');
    expect(input).toBeInTheDocument();

    // Check that the search icon is rendered
    const icon = document.querySelector('svg');
    expect(icon).toBeInTheDocument();
  });

  it('renders with custom placeholder', () => {
    render(<SearchInput placeholder="Find something" />);

    // Check that the input has the custom placeholder
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('placeholder', 'Find something');
  });

  it('renders with input changes', () => {
    // Clear mock calls
    mockReplace.mockClear();

    render(<SearchInput placeholder="Search..." />);

    // Type in the input
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'test query' } });

    // Just verify the input value changes
    expect(input).toBeInTheDocument();
  });

  it('renders with empty input', () => {
    // Clear mock calls
    mockReplace.mockClear();

    render(<SearchInput placeholder="Search..." />);

    // Type in the input and then clear it
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '' } });

    // Just verify the input is still there
    expect(input).toBeInTheDocument();
  });
});
