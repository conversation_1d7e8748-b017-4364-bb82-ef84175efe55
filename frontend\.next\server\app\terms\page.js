(()=>{var e={};e.id=7066,e.ids=[7066],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,generateMetadata:()=>i});var s=r(37413),o=r(58446),n=r(12045),a=r(39916);async function i(){let e="Terms of Service",t="Read our Terms of Service.";try{let r=await o.$.termsOfService.get(),s=r?.data;if(!s)return{title:e,description:t};let n=s?.seo,a=n?.metaTitle||s?.title||e,i=n?.metaDescription||t;return{title:a,description:i,...n?.canonicalURL&&{alternates:{canonical:n.canonicalURL}},...n?.metaRobots&&{robots:n.metaRobots}}}catch(t){return console.error("Error fetching Terms of Service metadata:",t),{title:e,description:"Error loading page information."}}}async function l(){let e=null;try{let t=await o.$.termsOfService.get();e=t?.data}catch(e){console.error("Failed to fetch Terms of Service page data:",e)}e||(console.error("Terms of Service data object is null or undefined after fetch."),(0,a.notFound)()),e.content||console.error("Terms of Service data fetched, but content field is missing or empty."),e.seo?.metaTitle||e.title;let t=e.content;return(0,s.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,s.jsx)("div",{className:"prose lg:prose-xl max-w-none mx-auto",children:t?(0,s.jsx)(n.default,{content:t}):(0,s.jsx)("p",{children:"Terms of Service content is not available at the moment."})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12045:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\MarkdownContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\MarkdownContent.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40921:(e,t,r)=>{Promise.resolve().then(r.bind(r,12045))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90612:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(60687),o=r(84189),n=r(3832),a=r(43210),i=r(66501);async function l(e,t){try{let r=await fetch("/api/analytics/post-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:e,postSlug:t})});if(!r.ok)throw Error(`Failed to track post view: ${r.statusText}`)}catch(e){i.Ay.error("Error tracking post view:",e)}}let c=({content:e,postId:t,postSlug:r,applyNoFollow:i=!0})=>((0,a.useEffect)(()=>{if(t&&r){let e=setTimeout(()=>{l(t,r)},2e3);return()=>clearTimeout(e)}},[t,r]),(0,s.jsxs)("div",{className:"mb-8",children:[" ",(0,s.jsx)(o.oz,{components:{h1:({node:e,...t})=>(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mt-8 mb-4",...t}),h2:({node:e,...t})=>(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mt-6 mb-3",...t}),h3:({node:e,...t})=>(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mt-5 mb-2",...t}),h4:({node:e,...t})=>(0,s.jsx)("h4",{className:"text-lg font-bold text-gray-800 mt-4 mb-2",...t}),p:({node:e,...t})=>(0,s.jsx)("p",{className:"text-gray-700 mb-4",...t}),a:({node:e,href:t,...r})=>{let o=t&&(t.startsWith("http://")||t.startsWith("https://")),n="";return o&&(i&&(n+="nofollow "),n+="noopener noreferrer"),(0,s.jsx)("a",{className:"text-emerald-600 hover:text-emerald-700 underline",href:t,rel:n.trim()||void 0,target:o?"_blank":void 0,...r})},ul:({node:e,...t})=>(0,s.jsx)("ul",{className:"list-disc pl-6 mb-4",...t}),ol:({node:e,...t})=>(0,s.jsx)("ol",{className:"list-decimal pl-6 mb-4",...t}),li:({node:e,...t})=>(0,s.jsx)("li",{className:"mb-1",...t}),blockquote:({node:e,...t})=>(0,s.jsx)("blockquote",{className:"border-l-4 border-emerald-500 pl-4 italic my-4",...t})},rehypePlugins:[n.A],children:e})]}))},90916:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=r(65239),o=r(48088),n=r(31369),a=r(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);r.d(t,i);let l={children:["",{children:["terms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6492)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\terms\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\terms\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/terms/page",pathname:"/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},93721:(e,t,r)=>{Promise.resolve().then(r.bind(r,90612))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,1330,3376,6391,2975,5373,8446,270],()=>r(90916));module.exports=s})();