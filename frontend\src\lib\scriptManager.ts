'use client';

/**
 * Script loading strategies
 */
export enum ScriptStrategy {
  /**
   * Load the script before any Next.js code and before any page hydration occurs
   */
  BEFORE_INTERACTIVE = 'beforeInteractive',
  
  /**
   * Load the script early but after some hydration on the page occurs
   */
  AFTER_INTERACTIVE = 'afterInteractive',
  
  /**
   * Load the script later during browser idle time
   */
  LAZY_ONLOAD = 'lazyOnload',
  
  /**
   * Load the script in a web worker (experimental)
   */
  WORKER = 'worker',
}

/**
 * Script configuration
 */
export interface ScriptConfig {
  /**
   * Script source URL
   */
  src?: string;
  
  /**
   * Inline script content
   */
  content?: string;
  
  /**
   * Unique identifier for the script
   */
  id: string;
  
  /**
   * Loading strategy
   */
  strategy?: ScriptStrategy;
  
  /**
   * Whether to load the script on all pages
   */
  global?: boolean;
  
  /**
   * Callback to execute when the script has loaded
   */
  onLoad?: () => void;
  
  /**
   * Callback to execute when the script fails to load
   */
  onError?: (error: Error) => void;
  
  /**
   * Additional script attributes
   */
  attributes?: Record<string, string>;
}

/**
 * Registry of scripts to be loaded
 */
const scriptRegistry: Record<string, ScriptConfig> = {};

/**
 * Register a script to be loaded
 * 
 * @param config Script configuration
 */
export function registerScript(config: ScriptConfig): void {
  scriptRegistry[config.id] = config;
}

/**
 * Get all registered scripts
 * 
 * @returns Array of script configurations
 */
export function getRegisteredScripts(): ScriptConfig[] {
  return Object.values(scriptRegistry);
}

/**
 * Get a specific registered script by ID
 * 
 * @param id Script ID
 * @returns Script configuration or undefined if not found
 */
export function getRegisteredScript(id: string): ScriptConfig | undefined {
  return scriptRegistry[id];
}

/**
 * Unregister a script
 * 
 * @param id Script ID
 */
export function unregisterScript(id: string): void {
  delete scriptRegistry[id];
}

/**
 * Check if a script is registered
 * 
 * @param id Script ID
 * @returns True if the script is registered
 */
export function isScriptRegistered(id: string): boolean {
  return !!scriptRegistry[id];
}
