import { cache } from 'react';
import { fetchFromStrapi, StrapiResponse, createErrorResponse } from '../strapiClient'; // Import createErrorResponse
import { Clinic, Practitioner } from '@/types/strapi-types'; // Assuming your types are in this path

// Cached function to get a single clinic by slug with all necessary relations populated
export const getClinicBySlug = cache(
  async (slug: string): Promise<StrapiResponse<Clinic | null>> => {
    const response = await fetchFromStrapi<Clinic[]>('clinics', {
      filters: {
        slug: { $eq: slug },
      },
      populate: {
        logo: { fields: ['url', 'alternativeText', 'width', 'height'] },
        address: { fields: ['street', 'city', 'state', 'zipCode', 'country'] }, // Specify fields
        contactInfo: { fields: ['phone', 'email', 'website'] }, // Specify fields
        location: { fields: ['latitude', 'longitude', 'mapLink'] }, // Specify fields
        services: { fields: ['name', 'slug'] },
        specialties: { fields: ['name', 'slug'] },
        conditions: { fields: ['name', 'slug'] },
        categories: { fields: ['name', 'slug'] },
        practitioners: {
          fields: ['name', 'slug', 'title'], // Select specific fields for practitioners
          populate: {
            profilePicture: { fields: ['url', 'alternativeText', 'width', 'height'] },
            specialties: { fields: ['name', 'slug'] }, // Populate practitioner specialties
          },
        },
        appointment_options: { fields: ['name', 'description'] },
        payment_methods: { fields: ['name'] },
        seo: { populate: { metaImage: { fields: ['url', 'alternativeText'] } } },
        // Add any other relations or components you need for a single clinic page
        // e.g. openingHours: true,
      },
      fields: ['name', 'slug', 'description', 'rating', 'publishedAt'], // Ensure all top-level fields are selected
    }, {
      next: {
        tags: [`strapi-clinic-${slug}`, 'strapi-clinics'], // Tag for targeted revalidation
        revalidate: 1800, // 30 minutes, or as needed
      },
    });

    if (response.error || !response.data || response.data.length === 0) {
      return response.error 
        ? { data: null, error: response.error } 
        : createErrorResponse<Clinic | null>(404, 'NotFound', 'Clinic not found');
    }
    // Strapi returns an array even for unique filters, so take the first item
    return { data: response.data[0], meta: response.meta };
  }
);

// Cached function to get a list of clinics, optimized for listing pages
export const getClinicsList = cache(
  async (
    page: number = 1,
    pageSize: number = 10,
    categorySlug?: string,
    specialtySlug?: string,
    conditionSlug?: string,
    searchTerm?: string
  ): Promise<StrapiResponse<Clinic[]>> => {
    const filters: Record<string, any> = {};
    if (categorySlug) filters.categories = { slug: { $eq: categorySlug } };
    if (specialtySlug) filters.specialties = { slug: { $eq: specialtySlug } };
    if (conditionSlug) filters.conditions = { slug: { $eq: conditionSlug } };
    if (searchTerm) filters.name = { $containsi: searchTerm }; // Case-insensitive search on name

    const cacheTags = [
      'strapi-clinics', // General tag for all clinic lists
      ...(categorySlug ? [`strapi-clinics-category-${categorySlug}`] : []),
      ...(specialtySlug ? [`strapi-clinics-specialty-${specialtySlug}`] : []),
      ...(conditionSlug ? [`strapi-clinics-condition-${conditionSlug}`] : []),
      `strapi-clinics-page-${page}`, // Tag for specific page
    ];

    return fetchFromStrapi<Clinic[]>('clinics', {
      filters,
      populate: {
        // Populate only what's essential for a list item card
        logo: { fields: ['url', 'alternativeText', 'width', 'height'] },
        location: { fields: ['city', 'state'] }, // Assuming location component has city/state
        specialties: { fields: ['name', 'slug'] }, // For displaying a few key specialties
        categories: { fields: ['name', 'slug'] }, // For displaying primary category
      },
      fields: ['name', 'slug', 'description', 'rating', 'publishedAt'], // Core fields for listing
      sort: ['name:asc'], // Default sort order
      pagination: { page, pageSize, withCount: true },
    }, {
      next: {
        tags: cacheTags,
        revalidate: 1800, // 30 minutes, or as needed
      },
    });
  }
);

// Example function to fetch a small list of popular/featured clinics for sidebars or homepage sections
export const getFeaturedClinics = cache(
  async (limit: number = 5): Promise<StrapiResponse<Clinic[]>> => {
    return fetchFromStrapi<Clinic[]>('clinics', {
      filters: { isFeatured: { $eq: true } }, // Assuming you have an 'isFeatured' boolean field
      populate: {
        logo: { fields: ['url', 'alternativeText'] },
        categories: { fields: ['name', 'slug'] },
      },
      fields: ['name', 'slug', 'description'],
      pagination: { pageSize: limit, page: 1 },
      sort: ['rating:desc', 'name:asc'], // Example: sort by rating then name
    }, {
      next: {
        tags: ['strapi-featured-clinics', 'strapi-clinics'],
        revalidate: 3600, // 1 hour
      },
    });
  }
);

// You can add more specific fetch functions here as needed, e.g., getClinicsByPractitioner, etc.
