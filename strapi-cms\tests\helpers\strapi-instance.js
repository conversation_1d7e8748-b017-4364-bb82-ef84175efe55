/**
 * This file provides a mock Strapi instance for testing
 * It's used when we need to test with the Strapi server directly
 */

// Mock Strapi server for testing
const mockStrapiServer = {
  httpServer: {
    address: () => ({ port: 1337 }),
  },
};

// Mock Strapi instance
const mockStrapi = {
  server: mockStrapiServer,
  plugins: {
    'users-permissions': {
      services: {
        user: {
          deleteOne: async (params) => {
            console.log(`Mock: Deleting user with params:`, params);
            return true;
          },
        },
      },
    },
  },
};

// Export the mock Strapi instance
module.exports = mockStrapi;
