'use client';

import React from 'react'; // Import React
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useSearchParams, usePathname, useRouter } from 'next/navigation';
import { useDebouncedCallback } from 'use-debounce';
import { FiSearch } from 'react-icons/fi'; // Using FiSearch as seen in clinics/page.tsx

interface SearchInputProps {
  placeholder: string;
  paramName?: string; // Optional: allows specifying a different URL param name (defaults to 'query')
  icon?: React.ReactNode; // Optional: allows passing a custom icon component
}

export default function SearchInput({ placeholder, paramName = 'query', icon }: SearchInputProps) { // Destructure icon prop
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();

  const handleSearch = useDebouncedCallback((term: string) => {
    console.log(`Searching... ${term}`); // For debugging
    const params = new URLSearchParams(searchParams);
    params.set('page', '1'); // Reset page to 1 on new search

    if (term) {
      params.set(paramName, term);
    } else {
      params.delete(paramName);
    }
    replace(`${pathname}?${params.toString()}`);
  }, 500); // Increased debounce to 500ms

  return (
    <div className="relative flex flex-1 flex-shrink-0">
      <label htmlFor={paramName} className="sr-only">
        Search
      </label>
      <input
        id={paramName} // Add id for label association
        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500" // Style copied from clinics/page.tsx
        placeholder={placeholder}
        onChange={(e) => {
          handleSearch(e.target.value);
        }}
        defaultValue={searchParams.get(paramName)?.toString()} // Read initial value from URL params
      />
      {/* Render provided icon or default FiSearch */}
      {icon ? icon : <FiSearch className="absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900" />}
    </div>
  );
}
