'use client'; // Required for useState and useEffect

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { sanitizeUrl } from '@/lib/mediaUtils';

interface ClinicLogoProps {
  src: string;
  alt: string;
  fallbackChar: string; // Character to display if no logo
  containerClassName?: string; // Optional class for the container div
  imageClassName?: string; // Optional class for the image itself (base styling)
}

const ClinicLogo: React.FC<ClinicLogoProps> = ({
  src,
  alt,
  fallbackChar,
  containerClassName, // We'll set this dynamically based on whether src exists
  imageClassName = 'h-full w-full', // Default image style
}) => {
  // Set default container class based on whether there's an image
  const defaultContainerClass = src
    ? 'h-24 w-24 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden' // No background when logo exists
    : 'bg-emerald-100 h-24 w-24 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden'; // Background when no logo

  // Use provided containerClassName or default
  const finalContainerClassName = containerClassName || defaultContainerClass;
  const [objectFitClass, setObjectFitClass] = useState('object-contain'); // Default to contain

  // We need the actual image dimensions to decide cover vs contain.
  // next/image's onLoad can provide this, but it requires rendering the image first.
  // A simpler approach for now is to use a hidden Image component to get dimensions,
  // or rely on a predefined aspect ratio if available from the API (which it isn't here).
  // Let's use a temporary solution with onLoad.

  const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const { naturalWidth, naturalHeight } = event.currentTarget;

    // Avoid division by zero if height is 0
    if (naturalHeight === 0) {
      setObjectFitClass('object-contain');
      return;
    }

    const aspectRatio = naturalWidth / naturalHeight;
    const WIDE_THRESHOLD = 2.0; // Adjust this threshold as needed

    // Use 'cover' only for moderately landscape images
    // Use 'contain' for portrait, square, and very wide images
    if (aspectRatio > 1 && aspectRatio <= WIDE_THRESHOLD) {
      setObjectFitClass('object-cover'); // Moderate Landscape
    } else {
      setObjectFitClass('object-contain'); // Portrait, Square, or Very Wide Landscape
    }
  };

  // Sanitize the URL to fix any issues
  const sanitizedSrc = src ? sanitizeUrl(src) : '';

  // Log in production for debugging
  if (process.env.NODE_ENV === 'production' && src && sanitizedSrc !== src) {
    console.log('ClinicLogo URL sanitized:', {
      original: src,
      sanitized: sanitizedSrc
    });
  }

  // State to track image loading errors
  const [hasError, setHasError] = useState(false);

  // Handle image load error
  const handleError = () => {
    // Log the error in production for debugging
    if (process.env.NODE_ENV === 'production') {
      console.error('ClinicLogo image failed to load:', {
        src: sanitizedSrc,
        alt: alt
      });
    }
    setHasError(true);
  };

  return (
    <div className={finalContainerClassName}>
      {sanitizedSrc && !hasError ? (
        <Image
          src={sanitizedSrc}
          alt={alt}
          width={96} // Corresponds to w-24 (24 * 4px = 96px) - required by next/image
          height={96} // Corresponds to h-24 - required by next/image
          className={`${imageClassName} ${objectFitClass}`} // Combine base and dynamic class
          onLoad={handleImageLoad}
          onError={handleError}
          priority // Prioritize loading the logo as it's likely above the fold
        />
      ) : (
        <span className="text-emerald-700 font-bold text-3xl">
          {fallbackChar}
        </span>
      )}
    </div>
  );
};

export default ClinicLogo;
