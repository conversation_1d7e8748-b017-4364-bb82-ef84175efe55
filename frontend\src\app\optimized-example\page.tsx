/**
 * Example page demonstrating optimized data fetching from Strapi
 */
import { Suspense } from 'react';
import { getGlobalSettings, getCategories, getClinics, getPractitioners, getBlogPosts } from '@/lib/serverOptimizedFetch';
import OptimizationNav from '@/components/examples/OptimizationNav';

/**
 * Optimized data fetching example page
 */
export default async function OptimizedExamplePage() {
  // Use a try-catch block to handle any errors at the page level
  try {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-4">Optimized Data Fetching Example</h1>
        <OptimizationNav />

        <div className="mb-8">
          <p className="text-lg">
            This page demonstrates server-side optimized data fetching from Strapi.
            The data is cached using React's cache function and Next.js built-in caching.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Global Settings */}
          <Suspense fallback={<SectionSkeleton title="Global Settings" />}>
            <GlobalSettingsSection />
          </Suspense>

          {/* Categories */}
          <Suspense fallback={<SectionSkeleton title="Categories" />}>
            <CategoriesSection />
          </Suspense>

          {/* Featured Clinics */}
          <Suspense fallback={<SectionSkeleton title="Featured Clinics" />}>
            <FeaturedClinicsSection />
          </Suspense>

          {/* Featured Practitioners */}
          <Suspense fallback={<SectionSkeleton title="Featured Practitioners" />}>
            <FeaturedPractitionersSection />
          </Suspense>

          {/* Latest Blog Posts */}
          <Suspense fallback={<SectionSkeleton title="Latest Blog Posts" />}>
            <LatestBlogPostsSection />
          </Suspense>
        </div>

        <div className="mt-8 p-6 bg-blue-50 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">How This Reduces API Calls</h2>

          <p className="mb-2">This implementation reduces API calls to Strapi in several ways:</p>

          <ol className="list-decimal pl-5 space-y-2">
            <li>
              <strong>React's cache function:</strong> Deduplicates requests within the same render pass.
            </li>
            <li>
              <strong>Content-type specific caching:</strong> Different cache durations based on
              how frequently content changes (e.g., global settings are cached longer than blog posts).
            </li>
            <li>
              <strong>Suspense and streaming:</strong> Allows for parallel data fetching and progressive rendering.
            </li>
            <li>
              <strong>Server-side rendering:</strong> Data is fetched once on the server and sent to the client.
            </li>
          </ol>

          <p className="mt-4">
            For more details, see the documentation in <code>frontend/src/docs/OPTIMIZED_API_CALLS.md</code>.
          </p>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error rendering OptimizedExamplePage:", error);
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-4">Optimized Data Fetching Example</h1>
        <OptimizationNav />

        <div className="bg-red-50 p-6 rounded-lg my-8 text-red-600">
          <h2 className="text-xl font-semibold mb-2">Error Loading Page</h2>
          <p>There was an error loading the optimized example page. Please try again later.</p>
        </div>

        <div className="mt-8 p-6 bg-blue-50 rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">How This Reduces API Calls</h2>

          <p className="mb-2">This implementation reduces API calls to Strapi in several ways:</p>

          <ol className="list-decimal pl-5 space-y-2">
            <li>
              <strong>React's cache function:</strong> Deduplicates requests within the same render pass.
            </li>
            <li>
              <strong>Content-type specific caching:</strong> Different cache durations based on
              how frequently content changes (e.g., global settings are cached longer than blog posts).
            </li>
            <li>
              <strong>Suspense and streaming:</strong> Allows for parallel data fetching and progressive rendering.
            </li>
            <li>
              <strong>Server-side rendering:</strong> Data is fetched once on the server and sent to the client.
            </li>
          </ol>

          <p className="mt-4">
            For more details, see the documentation in <code>frontend/src/docs/OPTIMIZED_API_CALLS.md</code>.
          </p>
        </div>
      </div>
    );
  }
}

/**
 * Global Settings Section
 */
async function GlobalSettingsSection() {
  try {
    const globalSettings = await getGlobalSettings();

    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Global Settings</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
          {JSON.stringify(globalSettings, null, 2)}
        </pre>
      </div>
    );
  } catch (error) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Global Settings</h2>
        <div className="bg-red-50 p-4 rounded text-red-500">
          Error loading global settings
        </div>
      </div>
    );
  }
}

/**
 * Categories Section
 */
async function CategoriesSection() {
  try {
    const categories = await getCategories(5);

    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Categories</h2>
        <ul className="list-disc pl-5">
          {categories.data?.map((category: any) => (
            <li key={category.id || category.documentId}>
              {category.name || category.attributes?.name}
            </li>
          ))}
        </ul>
      </div>
    );
  } catch (error) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Categories</h2>
        <div className="bg-red-50 p-4 rounded text-red-500">
          Error loading categories
        </div>
      </div>
    );
  }
}

/**
 * Featured Clinics Section
 */
async function FeaturedClinicsSection() {
  try {
    const clinics = await getClinics({ featured: true, pageSize: 5 });

    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Featured Clinics</h2>
        <ul className="list-disc pl-5">
          {clinics.data?.map((clinic: any) => (
            <li key={clinic.id || clinic.documentId}>
              {clinic.name || clinic.attributes?.name}
            </li>
          ))}
        </ul>
      </div>
    );
  } catch (error) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Featured Clinics</h2>
        <div className="bg-red-50 p-4 rounded text-red-500">
          Error loading clinics
        </div>
      </div>
    );
  }
}

/**
 * Featured Practitioners Section
 */
async function FeaturedPractitionersSection() {
  try {
    const practitioners = await getPractitioners({ featured: true, pageSize: 5 });

    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Featured Practitioners</h2>
        <ul className="list-disc pl-5">
          {practitioners.data?.map((practitioner: any) => {
            const firstName = practitioner.firstName || practitioner.attributes?.firstName;
            const lastName = practitioner.lastName || practitioner.attributes?.lastName;
            return (
              <li key={practitioner.id || practitioner.documentId}>
                {firstName} {lastName}
              </li>
            );
          })}
        </ul>
      </div>
    );
  } catch (error) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Featured Practitioners</h2>
        <div className="bg-red-50 p-4 rounded text-red-500">
          Error loading practitioners
        </div>
      </div>
    );
  }
}

/**
 * Latest Blog Posts Section
 */
async function LatestBlogPostsSection() {
  try {
    const blogPosts = await getBlogPosts({ pageSize: 5 });

    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Latest Blog Posts</h2>
        <ul className="list-disc pl-5">
          {blogPosts.data?.map((post: any) => (
            <li key={post.id || post.documentId}>
              {post.title || post.attributes?.title}
            </li>
          ))}
        </ul>
      </div>
    );
  } catch (error) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Latest Blog Posts</h2>
        <div className="bg-red-50 p-4 rounded text-red-500">
          Error loading blog posts
        </div>
      </div>
    );
  }
}

/**
 * Section Skeleton for loading state
 */
function SectionSkeleton({ title }: { title: string }) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md animate-pulse">
      <h2 className="text-2xl font-semibold mb-4">{title}</h2>
      <div className="space-y-3">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
      </div>
    </div>
  );
}
