(()=>{var e={};e.id=4217,e.ids=[4217],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5158:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.default,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>d});var s=t(65239),n=t(48088),o=t(31369),i=t(30893),a={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>i[e]);t.d(r,a);let d={children:["",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,45029)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\signin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\signin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/signin/page",pathname:"/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19769:(e,r,t)=>{Promise.resolve().then(t.bind(t,81121))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45029:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>o});var s=t(37413),n=t(81121);let o={title:"Sign In - Natural Healing Now",description:"Sign in to your Natural Healing Now account"};function i(){return(0,s.jsx)("div",{className:"container mx-auto py-12 px-4",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)(n.default,{})})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56217:(e,r,t)=>{Promise.resolve().then(t.bind(t,82223))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81121:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\auth\\\\SignInForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\auth\\SignInForm.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},82223:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var s=t(60687),n=t(43210),o=t(6363),i=t(16189),a=t(85814),d=t.n(a);function l(){let[e,r]=(0,n.useState)(""),[t,a]=(0,n.useState)(""),[l,u]=(0,n.useState)(""),[c,p]=(0,n.useState)(!1),{signIn:m}=(0,o.A)(),x=(0,i.useRouter)(),g=async r=>{r.preventDefault(),u(""),p(!0);try{let{error:r}=await m(e,t);r?u(r.message||"Failed to sign in"):x.push("/")}catch(e){console.error("Error during sign in:",e),u("An unexpected error occurred")}finally{p(!1)}};return(0,s.jsxs)("div",{className:"max-w-md w-full mx-auto p-6 bg-white rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-gray-800",children:"Sign In"}),l&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md",children:l}),(0,s.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,s.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>r(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)(d(),{href:"/forgot-password",className:"text-sm text-emerald-600 hover:text-emerald-700",children:"Forgot password?"})]}),(0,s.jsx)("input",{id:"password",type:"password",value:t,onChange:e=>a(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"})]}),(0,s.jsx)("button",{type:"submit",disabled:c,className:"w-full bg-emerald-600 text-white py-2 px-4 rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Signing in...":"Sign In"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)(d(),{href:"/signup",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"Sign up"})]})})]})}},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,1330,3376,6391,2975,8446,270],()=>t(5158));module.exports=s})();