{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:a11y": "jest --testPathPattern=a11y"}, "dependencies": {"@heroicons/react": "^2.2.0", "@next/third-parties": "^15.3.2", "@react-google-maps/api": "^2.20.6", "@tanstack/react-query": "^5.75.5", "@tanstack/react-query-devtools": "^5.75.7", "@vercel/analytics": "^1.5.0", "axios": "^1.9.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "next": "^15.3.2", "next-seo": "^6.6.0", "nodemailer": "^7.0.3", "qs": "^6.14.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "server-only": "^0.0.1", "use-debounce": "^10.0.4"}, "devDependencies": {"@axe-core/playwright": "^4.10.1", "@eslint/eslintrc": "^3", "@playwright/test": "^1.44.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/qs": "^6.9.18", "@types/react": "^19", "@types/react-dom": "^19", "axe-core": "^4.8.5", "eslint": "^9", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.2.7", "tailwindcss": "^4", "typescript": "^5"}}