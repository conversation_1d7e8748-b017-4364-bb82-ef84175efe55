/**
 * Utility functions for building optimized Strapi queries
 * These functions help reduce the amount of data transferred by only requesting what's needed
 */

/**
 * Build a fields parameter to select only specific fields
 * This reduces the amount of data transferred
 * 
 * @param fields Array of field names to include
 * @returns Fields parameter object for Strapi
 */
export function selectFields(fields: string[]): { fields: string[] } {
  return { fields };
}

/**
 * Build a populate parameter for specific relations
 * This allows selective population of only needed relations
 * 
 * @param relations Object mapping relation names to their population options
 * @returns Populate parameter object for Strapi
 */
export function populateRelations(relations: Record<string, boolean | object>): { populate: Record<string, boolean | object> } {
  return { populate: relations };
}

/**
 * Build a pagination parameter
 * 
 * @param page Page number (1-based)
 * @param pageSize Number of items per page
 * @returns Pagination parameter object for Strapi
 */
export function paginate(page: number, pageSize: number): { pagination: { page: number; pageSize: number } } {
  return { pagination: { page, pageSize } };
}

/**
 * Build a sort parameter
 * 
 * @param fields Array of field names with optional direction (e.g., 'name:asc')
 * @returns Sort parameter object for Strapi
 */
export function sort(fields: string[]): { sort: string[] } {
  return { sort: fields };
}

/**
 * Build a filter parameter for equality
 * 
 * @param field Field name
 * @param value Value to compare
 * @returns Filter parameter object for Strapi
 */
export function filterEquals(field: string, value: any): { filters: Record<string, { $eq: any }> } {
  return { filters: { [field]: { $eq: value } } };
}

/**
 * Build a filter parameter for contains (case-insensitive)
 * 
 * @param field Field name
 * @param value Value to search for
 * @returns Filter parameter object for Strapi
 */
export function filterContains(field: string, value: string): { filters: Record<string, { $containsi: string }> } {
  return { filters: { [field]: { $containsi: value } } };
}

/**
 * Build a filter parameter for multiple conditions (OR)
 * 
 * @param conditions Array of condition objects
 * @returns Filter parameter object for Strapi
 */
export function filterOr(conditions: Record<string, any>[]): { filters: { $or: Record<string, any>[] } } {
  return { filters: { $or: conditions } };
}

/**
 * Build a filter parameter for multiple conditions (AND)
 * 
 * @param conditions Array of condition objects
 * @returns Filter parameter object for Strapi
 */
export function filterAnd(conditions: Record<string, any>[]): { filters: { $and: Record<string, any>[] } } {
  return { filters: { $and: conditions } };
}

/**
 * Merge multiple parameter objects into a single object
 * 
 * @param params Array of parameter objects
 * @returns Merged parameter object
 */
export function mergeParams(...params: Record<string, any>[]): Record<string, any> {
  return params.reduce((result, param) => {
    Object.entries(param).forEach(([key, value]) => {
      if (key === 'filters' && result.filters) {
        // Merge filters
        result.filters = { ...result.filters, ...value };
      } else if (key === 'populate' && result.populate) {
        // Merge populate
        result.populate = { ...result.populate, ...value };
      } else if (key === 'fields' && result.fields) {
        // Merge fields
        result.fields = [...result.fields, ...value];
      } else {
        // For other keys, just override
        result[key] = value;
      }
    });
    return result;
  }, {});
}

/**
 * Build optimized parameters for fetching a list of clinics
 */
export function buildClinicListParams(options: {
  page?: number;
  pageSize?: number;
  featured?: boolean;
  location?: string;
  query?: string;
  specialtySlug?: string;
} = {}) {
  const { page = 1, pageSize = 10, featured, location, query, specialtySlug } = options;
  
  const params: Record<string, any>[] = [
    paginate(page, pageSize),
    sort(['name:asc']),
    // Only select necessary fields for list view
    selectFields(['name', 'slug', 'address', 'city', 'state', 'zipCode', 'phone', 'email', 'website', 'description', 'isFeatured']),
    // Only populate necessary relations
    populateRelations({
      logo: true,
      featuredImage: true,
      specialties: {
        fields: ['name', 'slug']
      }
    })
  ];
  
  // Add filters based on options
  const filters: Record<string, any>[] = [];
  
  if (featured) {
    filters.push({ isFeatured: { $eq: true } });
  }
  
  if (location) {
    filters.push({
      $or: [
        { city: { $containsi: location } },
        { state: { $containsi: location } },
        { zipCode: { $containsi: location } }
      ]
    });
  }
  
  if (query) {
    filters.push({
      $or: [
        { name: { $containsi: query } },
        { description: { $containsi: query } }
      ]
    });
  }
  
  if (specialtySlug) {
    filters.push({
      specialties: {
        slug: { $eq: specialtySlug }
      }
    });
  }
  
  if (filters.length > 0) {
    if (filters.length === 1) {
      params.push({ filters: filters[0] });
    } else {
      params.push(filterAnd(filters));
    }
  }
  
  return mergeParams(...params);
}

/**
 * Build optimized parameters for fetching a list of practitioners
 */
export function buildPractitionerListParams(options: {
  page?: number;
  pageSize?: number;
  featured?: boolean;
  location?: string;
  query?: string;
  specialtySlug?: string;
} = {}) {
  const { page = 1, pageSize = 10, featured, location, query, specialtySlug } = options;
  
  const params: Record<string, any>[] = [
    paginate(page, pageSize),
    sort(['lastName:asc']),
    // Only select necessary fields for list view
    selectFields(['firstName', 'lastName', 'title', 'slug', 'bio', 'isFeatured']),
    // Only populate necessary relations
    populateRelations({
      profilePicture: true,
      specialties: {
        fields: ['name', 'slug']
      },
      clinic: {
        fields: ['name', 'slug', 'city', 'state']
      }
    })
  ];
  
  // Add filters based on options
  const filters: Record<string, any>[] = [];
  
  if (featured) {
    filters.push({ isFeatured: { $eq: true } });
  }
  
  if (location && location.trim() !== '') {
    filters.push({
      clinic: {
        $or: [
          { city: { $containsi: location } },
          { state: { $containsi: location } },
          { zipCode: { $containsi: location } }
        ]
      }
    });
  }
  
  if (query && query.trim() !== '') {
    filters.push({
      $or: [
        { firstName: { $containsi: query } },
        { lastName: { $containsi: query } },
        { bio: { $containsi: query } }
      ]
    });
  }
  
  if (specialtySlug) {
    filters.push({
      specialties: {
        slug: { $eq: specialtySlug }
      }
    });
  }
  
  if (filters.length > 0) {
    if (filters.length === 1) {
      params.push({ filters: filters[0] });
    } else {
      params.push(filterAnd(filters));
    }
  }
  
  return mergeParams(...params);
}
