REST API reference
The REST API allows accessing the content-types through API endpoints. Strapi automatically creates API endpoints when a content-type is created. API parameters can be used when querying API endpoints to refine the results.

This section of the documentation is for the REST API reference. We also have guides available for specific use cases.

Prerequisites
All content types are private by default and need to be either made public or queries need to be authenticated with the proper permissions. See the Quick Start Guide, the user guide for the Users & Permissions feature, and API tokens configuration documentation for more details.

 Note
By default, the REST API responses only include top-level fields and does not populate any relations, media fields, components, or dynamic zones. Use the populate parameter to populate specific fields. Ensure that the find permission is given to the field(s) for the relation(s) you populate.

 Strapi Client
The Strapi Client library simplifies interactions with your Strapi back end, providing a way to fetch, create, update, and delete content.

Endpoints
For each Content-Type, the following endpoints are automatically generated:

Plural API ID vs. Singular API ID:
Collection type
Single type
Method	URL	Description
GET	/api/:pluralApiId	Get a list of document
POST	/api/:pluralApiId	Create a document
GET	/api/:pluralApiId/:documentId	Get a document
PUT	/api/:pluralApiId/:documentId	Update a document
DELETE	/api/:pluralApiId/:documentId	Delete a document
Real-world examples of endpoints:
 Upload API
The Upload package (which powers the Media Library feature) has a specific API accessible through its /api/upload endpoints.

 Note
Components don't have API endpoints.

Requests
 Strapi 5 vs. Strapi v4
Strapi 5's Content API includes 2 major differences with Strapi v4:

The response format has been flattened, which means attributes are no longer nested in a data.attributes object and are directly accessible at the first level of the data object (e.g., a content-type's "title" attribute is accessed with data.title).
Strapi 5 now uses documents  and documents are accessed by their documentId.
Requests return a response as an object which usually includes the following keys:

data: the response data itself, which could be:

a single document, as an object with the following keys:
id (integer)
documentId (string), which is the unique identifier to use when querying a given document,
the attributes (each attribute's type depends on the attribute, see models attributes documentation for details)
meta (object)
a list of documents, as an array of objects
a custom response
meta (object): information about pagination, publication state, available locales, etc.

error (object, optional): information about any error thrown by the request

 Note
Some plugins (including Users & Permissions and Upload) may not follow this response format.

Get documents
Returns documents matching the query filters (see API parameters documentation).

 Tip: Strapi 5 vs. Strapi 4
In Strapi 5 the response format has been flattened, and attributes are directly accessible from the data object instead of being nested in data.attributes.

You can pass an optional header while you're migrating to Strapi 5 (see the related breaking change).

Example request
GET http://localhost:1337/api/restaurants

Example response
{
  "data": [
    {
      "id": 2,
      "documentId": "hgv1vny5cebq2l3czil1rpb3",
      "Name": "BMK Paris Bamako",
      "Description": null,
      "createdAt": "2024-03-06T13:42:05.098Z",
      "updatedAt": "2024-03-06T13:42:05.098Z",
      "publishedAt": "2024-03-06T13:42:05.103Z",
      "locale": "en"
    },
    {
      "id": 4,
      "documentId": "znrlzntu9ei5onjvwfaalu2v",
      "Name": "Biscotte Restaurant",
      "Description": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "text": "Welcome to Biscotte restaurant! Restaurant Biscotte offers a cuisine based on fresh, quality products, often local, organic when possible, and always produced by passionate producers."
            }
          ]
        }
      ],
      "createdAt": "2024-03-06T13:43:30.172Z",
      "updatedAt": "2024-03-06T13:43:30.172Z",
      "publishedAt": "2024-03-06T13:43:30.175Z",
      "locale": "en"
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 2
    }
  }
}


Get a document
Returns a document by documentId.

 Strapi 5 vs. Strapi v4
In Strapi 5, a specific document is reached by its documentId.

Example request
GET http://localhost:1337/api/restaurants/j964065dnjrdr4u89weh79xl

Example response
{
  "data": {
    "id": 6,
    "documentId": "znrlzntu9ei5onjvwfaalu2v",
    "Name": "Biscotte Restaurant",
    "Description": [
      {
        "type": "paragraph",
        "children": [
          {
            "type": "text",
            "text": "Welcome to Biscotte restaurant! Restaurant Biscotte offers a cuisine bassics, such as 4 Formaggi or Calzone, and our original creations such as Do Luigi or Nduja."
          }
        ]
      }
    ],
    "createdAt": "2024-02-27T10:19:04.953Z",
    "updatedAt": "2024-03-05T15:52:05.591Z",
    "publishedAt": "2024-03-05T15:52:05.600Z",
    "locale": "en"
  },
  "meta": {}
}



Create a document
Creates a document and returns its value.

If the Internationalization (i18n) plugin is installed, it's possible to use POST requests to the REST API to create localized documents.

 Note
While creating a document, you can define its relations and their order (see Managing relations through the REST API for more details).

Example request
POST http://localhost:1337/api/restaurants

{ 
  "data": {
    "Name": "Restaurant D",
    "Description": [ // uses the "Rich text (blocks)" field type
      {
        "type": "paragraph",
        "children": [
          {
            "type": "text",
            "text": "A very short description goes here."
          }
        ]
      }
    ]
  }
}

Example response
{
  "data": {
    "documentId": "bw64dnu97i56nq85106yt4du",
    "Name": "Restaurant D",
    "Description": [
      {
        "type": "paragraph",
        "children": [
          {
            "type": "text",
            "text": "A very short description goes here."
          }
        ]
      }
    ],
    "createdAt": "2024-03-05T16:44:47.689Z",
    "updatedAt": "2024-03-05T16:44:47.689Z",
    "publishedAt": "2024-03-05T16:44:47.687Z",
    "locale": "en"
  },
  "meta": {}
}

Update a document
Partially updates a document by id and returns its value.

Send a null value to clear fields.

 NOTES
Even with the Internationalization (i18n) plugin installed, it's currently not possible to update the locale of a document.
While updating a document, you can define its relations and their order (see Managing relations through the REST API for more details).
Example request
PUT http://localhost:1337/api/restaurants/hgv1vny5cebq2l3czil1rpb3

{ 
  "data": {
    "Name": "BMK Paris Bamako", // we didn't change this field but still need to include it
    "Description": [ // uses the "Rich text (blocks)" field type
      {
        "type": "paragraph",
        "children": [
          {
            "type": "text",
            "text": "A very short description goes here."
          }
        ]
      }
    ]
  }
}


Example response
{
  "data": {
    "id": 9,
    "documentId": "hgv1vny5cebq2l3czil1rpb3",
    "Name": "BMK Paris Bamako",
    "Description": [
      {
        "type": "paragraph",
        "children": [
          {
            "type": "text",
            "text": "A very short description goes here."
          }
        ]
      }
    ],
    "createdAt": "2024-03-06T13:42:05.098Z",
    "updatedAt": "2024-03-06T14:16:56.883Z",
    "publishedAt": "2024-03-06T14:16:56.895Z",
    "locale": "en"
  },
  "meta": {}
}

Delete a document
Deletes a document.

DELETE requests only send a 204 HTTP status code on success and do not return any data in the response body.

Example request
DELETE http://localhost:1337/api/restaurants/bw64dnu97i56nq85106yt4du

REST API parameters
API parameters can be used with the REST API to filter, sort, and paginate results and to select fields and relations to populate. Additionally, specific parameters related to optional Strapi features can be used, like the publication state and locale of a content-type.

The following API parameters are available:

Operator	Type	Description
filters	Object	Filter the response
locale	String	Select a locale
status	String	Select the Draft & Publish status
populate	String or Object	Populate relations, components, or dynamic zones
fields	Array	Select only specific fields to display
sort	String or Array	Sort the response
pagination	Object	Page through entries
Query parameters use the LHS bracket syntax (i.e. they are encoded using square brackets []).

 Tip
A wide range of REST API parameters can be used and combined to query your content, which can result in long and complex query URLs.
👉 You can use Strapi's interactive query builder tool to build query URLs more conveniently. 🤗

REST API: Filters
The REST API offers the ability to filter results found with its "Get entries" method.
Using optional Strapi features can provide some more filters:

If the Internationalization (i18n) plugin is enabled on a content-type, it's possible to filter by locale.
If the Draft & Publish is enabled, it's possible to filter based on a published (default) or draft status.
 Tip
Strapi takes advantage of the ability of the `qs` library to parse nested objects to create more complex queries.

Use qs directly to generate complex queries instead of creating them manually. Examples in this documentation showcase how you can use qs.

You can also use the interactive query builder if you prefer playing with our online tool instead of generating queries with qs on your machine.

Queries can accept a filters parameter with the following syntax:

GET /api/:pluralApiId?filters[field][operator]=value

The following operators are available:

Operator	Description
$eq	Equal
$eqi	Equal (case-insensitive)
$ne	Not equal
$nei	Not equal (case-insensitive)
$lt	Less than
$lte	Less than or equal to
$gt	Greater than
$gte	Greater than or equal to
$in	Included in an array
$notIn	Not included in an array
$contains	Contains
$notContains	Does not contain
$containsi	Contains (case-insensitive)
$notContainsi	Does not contain (case-insensitive)
$null	Is null
$notNull	Is not null
$between	Is between
$startsWith	Starts with
$startsWithi	Starts with (case-insensitive)
$endsWith	Ends with
$endsWithi	Ends with (case-insensitive)
$or	Joins the filters in an "or" expression
$and	Joins the filters in an "and" expression
$not	Joins the filters in an "not" expression
 Caution
By default, the filters can only be used from find endpoints generated by the Content-type Builder and the CLI.

Example: Find users having 'John' as a first name
You can use the $eq filter operator to find an exact match.


Find users having 'John' as first name
GET /api/users?filters[username][$eq]=John

JavaScript query (built with the qs library):
Example response
{
  "data": [
    {
      "id": 1,
      "documentId": "znrlzntu9ei5onjvwfaalu2v",
      "username": "John",
      "email": "<EMAIL>",
      "provider": "local",
      "confirmed": true,
      "blocked": false,
      "createdAt": "2021-12-03T20:08:17.740Z",
      "updatedAt": "2021-12-03T20:08:17.740Z"
    }
  ],
  "meta": {
  "pagination": {
    "page": 1,
    "pageSize": 25,
    "pageCount": 1,
    "total": 1
  }
}

Example: Find multiple restaurants with ids 3, 6,8
You can use the $in filter operator with an array of values to find multiple exact values.


Find multiple restaurants with ids 3, 6, 8
GET /api/restaurants?filters[id][$in][0]=6&filters[id][$in][1]=8

JavaScript query (built with the qs library):
Example response
{
  "data": [
    {
      "id": 6,
      "documentId": "ethwxjxtvuxl89jq720e38uk",
      "name": "test6",
      // ...
    },
    {
      "id": 8,
      "documentId": "cf07g1dbusqr8mzmlbqvlegx",
      "name": "test8",
      // ...
    },
  ],
  "meta": {
    // ...
  }
}

Complex filtering
Complex filtering is combining multiple filters using advanced methods such as combining $and & $or. This allows for more flexibility to request exactly the data needed.


Find books with 2 possible dates and a specific author
GET /api/books?filters[$or][0][date][$eq]=2020-01-01&filters[$or][1][date][$eq]=2020-01-02&filters[author][name][$eq]=Kai%20doe

JavaScript query (built with the qs library):
Example response
{
  "data": [
    {
      "id": 1,
      "documentId": "rxngxzclq0zdaqtvz67hj38d",
      "name": "test1",
      "date": "2020-01-01",
      // ...
    },
    {
      "id": 2,
      "documentId": "kjkhff4e269a50b4vi16stst",
      "name": "test2",
      "date": "2020-01-02",
      // ...
    }
  ],
  "meta": {
    // ...
  }
}

Deep filtering
Deep filtering is filtering on a relation's fields.

 Note
Relations, media fields, components, and dynamic zones are not populated by default. Use the populate parameter to populate these content structures (see populate documentation)
You can filter what you populate, you can also filter nested relations, but you can't use filters for polymorphic content structures (such as media fields and dynamic zones).
 Caution
Querying your API with deep filters may cause performance issues. If one of your deep filtering queries is too slow, we recommend building a custom route with an optimized version of the query.

 Deep filtering with the various APIs
For examples of how to deep filter with the various APIs, please refer to this blog article.

Find restaurants owned by a chef who belongs to a 5-star restaurant
GET /api/restaurants?filters[chef][restaurants][stars][$eq]=5

JavaScript query (built with the qs library):
Example response
{
  "data": [
    {
      "id": 1,
      "documentId": "cvsz61qg33rtyv1qljb1nrtg",
      "name": "GORDON RAMSAY STEAK",
      "stars": 5
      // ...
    },
    {
      "id": 2,
      "documentId": "uh17h7ibw0g8thit6ivi71d8",
      "name": "GORDON RAMSAY BURGER",
      "stars": 5
      // ...
    }
  ],
  "meta": {
    // ...
  }
}


REST API: locale
The Internationalization (i18n) feature adds new abilities to the REST API.

Prerequisites
To work with API content for a locale, please ensure the locale has been already added to Strapi in the admin panel.

The locale API parameter can be used to work with documents only for a specified locale. locale takes a locale code as a value (see full list of available locales).

 Tip
If the locale parameter is not defined, it will be set to the default locale. en is the default locale when a new Strapi project is created, but another locale can be set as the default locale in the admin panel.

For instance, by default, a GET request to /api/restaurants will return the same response as a request to /api/restaurants?locale=en.

The following table lists the new possible use cases added by i18n to the REST API and gives syntax examples (you can click on requests to jump to the corresponding section with more details):

For collection types
For single types
Use case	Syntax example
and link for more information
Get all documents in a specific locale	GET /api/restaurants?locale=fr
Get a specific locale version for a document	GET /api/restaurants/abcdefghijklmno456?locale=fr
Create a new document for the default locale	POST /api/restaurants
+ pass attributes in the request body
Create a new document for a specific locale	POST /api/restaurants
+ pass attributes and locale in the request body
Create a new, or update an existing, locale version for an existing document	PUT /api/restaurants/abcdefghijklmno456?locale=fr
+ pass attributes in the request body
Delete a specific locale version of a document	DELETE /api/restaurants/abcdefghijklmno456?locale=fr
GET Get all documents in a specific locale
Example request
GET http://localhost:1337/api/restaurants?locale=fr

Example response
{
  "data": [
    {
      "id": 5,
      "documentId": "h90lgohlzfpjf3bvan72mzll",
      "Title": "Meilleures pizzas",
      "Body": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "text": "On déguste les meilleures pizzas de la ville à la Pizzeria Arrivederci."
            }
          ]
        }
      ],
      "createdAt": "2024-03-06T22:08:59.643Z",
      "updatedAt": "2024-03-06T22:10:21.127Z",
      "publishedAt": "2024-03-06T22:10:21.130Z",
      "locale": "fr"
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 1
    }
  }
}


GET Get a document in a specific locale
To get a specific document in a given locale, add the locale parameter to the query:

Use case	Syntax format and link for more information
In a collection type	GET /api/content-type-plural-name/document-id?locale=locale-code
In a single type	GET /api/content-type-singular-name?locale=locale-code
Collection types
To get a specific document in a collection type in a given locale, add the locale parameter to the query, after the documentId:

Example request
GET /api/restaurants/lr5wju2og49bf820kj9kz8c3?locale=fr

Example response
{
  "data": [
    {
      "id": 22,
      "documentId": "lr5wju2og49bf820kj9kz8c3",
      "Name": "Biscotte Restaurant",
      "Description": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "text": "Bienvenue au restaurant Biscotte! Le Restaurant Biscotte propose une cuisine à base de produits frais et de qualité, souvent locaux, biologiques lorsque cela est possible, et toujours produits par des producteurs passionnés."
            }
          ]
        }
      ],
      // …
      "locale": "fr"
    },
    // …
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 3
    }
  }
}


Single types
To get a specific single type document in a given locale, add the locale parameter to the query, after the single type name:

Example request
GET /api/homepage?locale=fr

Example response
{
  "data": {
    "id": 10,
    "documentId": "ukbpbnu8kbutpn98rsanyi50",
    "Title": "Page d'accueil",
    "Body": null,
    "createdAt": "2024-03-07T13:28:26.349Z",
    "updatedAt": "2024-03-07T13:28:26.349Z",
    "publishedAt": "2024-03-07T13:28:26.353Z",
    "locale": "fr"
  },
  "meta": {}
}

POST Create a new localized document for a collection type
To create a localized document from scratch, send a POST request to the Content API. Depending on whether you want to create it for the default locale or for another locale, you might need to pass the locale parameter in the request's body

Use case	Syntax format and link for more information
Create for the default locale	POST /api/content-type-plural-name
Create for a specific locale	POST /api/content-type-plural-name
+ pass locale in request body
For the default locale
If no locale has been passed in the request body, the document is created using the default locale for the application:

Example request
POST http://localhost:1337/api/restaurants

{
  "data": {
    "Name": "Oplato",
  }
}

Example response
{
  "data": {
    "id": 13,
    "documentId": "jae8klabhuucbkgfe2xxc5dj",
    "Name": "Oplato",
    "Description": null,
    "createdAt": "2024-03-06T22:19:54.646Z",
    "updatedAt": "2024-03-06T22:19:54.646Z",
    "publishedAt": "2024-03-06T22:19:54.649Z",
    "locale": "en"
  },
  "meta": {}
}

For a specific locale
To create a localized entry for a locale different from the default one, add the locale attribute to the body of the POST request:

Example request
POST http://localhost:1337/api/restaurants

{
  "data": {
    "Name": "She's Cake",
    "locale": "fr"
  }
}


Example response
{
  "data": {
    "id": 15,
    "documentId": "ldcmn698iams5nuaehj69j5o",
    "Name": "She's Cake",
    "Description": null,
    "createdAt": "2024-03-06T22:21:18.373Z",
    "updatedAt": "2024-03-06T22:21:18.373Z",
    "publishedAt": "2024-03-06T22:21:18.378Z",
    "locale": "en"
  },
  "meta": {}
}

PUT Create a new, or update an existing, locale version for an existing document
With PUT requests sent to an existing document, you can:

create another locale version of the document,
or update an existing locale version of the document.
Send the PUT request to the appropriate URL, adding the locale=your-locale-code parameter to the query URL and passing attributes in a data object in the request's body:

Use case	Syntax format and link for more information
In a collection type	PUT /api/content-type-plural-name/document-id?locale=locale-code
In a single type	PUT /api/content-type-singular-name?locale=locale-code
 Caution
When creating a localization for existing localized entries, the body of the request can only accept localized fields.

 Tip
The Content-Type should have the createLocalization permission enabled, otherwise the request will return a 403: Forbidden status.

 Note
It is not possible to change the locale of an existing localized entry. When updating a localized entry, if you set a locale attribute in the request body it will be ignored.

In a collection type
To create a new locale for an existing document in a collection type, add the locale parameter to the query, after the documentId, and pass data to the request's body:

Example request: Creating a French locale for an existing restaurant
PUT http://localhost:1337/api/restaurants/lr5wju2og49bf820kj9kz8c3?locale=fr

{
  data: {
    "Name": "She's Cake in French",
  }
}

Example response
{
  "data": {
    "id": 19,
    "documentId": "lr5wju2og49bf820kj9kz8c3",
    "Name": "She's Cake in French",
    "Description": null,
    "createdAt": "2024-03-07T12:13:09.551Z",
    "updatedAt": "2024-03-07T12:13:09.551Z",
    "publishedAt": "2024-03-07T12:13:09.554Z",
    "locale": "fr"
  },
  "meta": {}
}

In a single type
To create a new locale for an existing single type document, add the locale parameter to the query, after the single type name, and pass data to the request's body:

Example: Create a FR locale for an existing Homepage single type
PUT http://localhost:1337/api/homepage?locale=fr

{
  "data": {
    "Title": "Page d'accueil"
  }
}

Example response
{
  "data": {
    "id": 10,
    "documentId": "ukbpbnu8kbutpn98rsanyi50",
    "Title": "Page d'accueil",
    "Body": null,
    "createdAt": "2024-03-07T13:28:26.349Z",
    "updatedAt": "2024-03-07T13:28:26.349Z",
    "publishedAt": "2024-03-07T13:28:26.353Z",
    "locale": "fr"
  },
  "meta": {}
}


DELETE Delete a locale version of a document
To delete a locale version of a document, send a DELETE request with the appropriate locale parameter.

DELETE requests only send a 204 HTTP status code on success and do not return any data in the response body.

In a collection type
To delete only a specific locale version of a document in a collection type, add the locale parameter to the query after the documentId:

Example request
DELETE /api/restaurants/abcdefghijklmno456?locale=fr

In a single type
To delete only a specific locale version of a single type document, add the locale parameter to the query after the single type name:

Example request
DELETE /api/homepage?locale=fr


REST API: status
The REST API offers the ability to filter results based on their status, draft or published.

Prerequisites
The Draft & Publish feature should be enabled.

Queries can accept a status parameter to fetch documents based on their status:

published: returns only the published version of documents (default)
draft: returns only the draft version of documents
 Tip
In the response data, the publishedAt field is null for drafts.

 Note
Since published versions are returned by default, passing no status parameter is equivalent to passing status=published.



Get draft versions of restaurants
GET /api/articles?status=draft

JavaScript query (built with the qs library):
Example response
{
  "data": [
    // …
    {
      "id": 5,
      "documentId": "znrlzntu9ei5onjvwfaalu2v",
      "Name": "Biscotte Restaurant",
      "Description": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "text": "This is the draft version."
            }
          ]
        }
      ],
      "createdAt": "2024-03-06T13:43:30.172Z",
      "updatedAt": "2024-03-06T21:38:46.353Z",
      "publishedAt": null,
      "locale": "en"
    },
    // …
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 4
    }
  }
}


REST API: Population & Field Selection
The REST API by default does not populate any relations, media fields, components, or dynamic zones. Use the populate parameter to populate specific fields and the select parameter to return only specific fields with the query results.

 Tip
Strapi takes advantage of the ability of the `qs` library to parse nested objects to create more complex queries.

Use qs directly to generate complex queries instead of creating them manually. Examples in this documentation showcase how you can use qs.

You can also use the interactive query builder if you prefer playing with our online tool instead of generating queries with qs on your machine.

🏗 Work-in-progress
Strapi v4 docs very recently included a more extensive description of how to use the populate parameter, including an extensive API reference and additional guides. These v4 pages are currently being ported and adapted to Strapi 5 docs so that examples reflect the new data response format.

In the meantime, you can trust the content of the present page as accurate as it already reflects the new Strapi 5, flattened response format (see breaking change entry and REST API introduction for details); the present page is just not as complete as its v4 equivalent yet.

Field selection
Queries can accept a fields parameter to select only some fields. By default, only the following types of fields are returned:

string types: string, text, richtext, enumeration, email, password, and uid,
date types: date, time, datetime, and timestamp,
number types: integer, biginteger, float, and decimal,
generic types: boolean, array, and JSON.
Use case	Example parameter syntax
Select a single field	fields=name
Select multiple fields	fields[0]=name&fields[1]=description
 Note
Field selection does not work on relational, media, component, or dynamic zone fields. To populate these fields, use the populate parameter.

Example request: Return only name and description fields
GET /api/restaurants?fields[0]=name&fields[1]=description

JavaScript query (built with the qs library):

Example response
{
  "data": [
    {
      "id": 4,
      "Name": "Pizzeria Arrivederci",
      "Description": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "text": "Specialized in pizza, we invite you to rediscover our classics, such as 4 Formaggi or Calzone, and our original creations such as Do Luigi or Nduja."
            }
          ]
        }
      ],
      "documentId": "lr5wju2og49bf820kj9kz8c3"
    },
    // …
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 4
    }
  }
}


Population
The REST API by default does not populate any type of fields, so it will not populate relations, media fields, components, or dynamic zones unless you pass a populate parameter to populate various field types.

The populate parameter can be used alone or in combination with with multiple operators to have much more control over the population.

 Caution
The find permission must be enabled for the content-types that are being populated. If a role doesn't have access to a content-type it will not be populated (see User Guide for additional information on how to enable find permissions for content-types).

 Note
It's currently not possible to return just an array of ids with a request.

 Populating guides
The REST API guides section includes more detailed information about various possible use cases for the populate parameter:

The Understanding populate guide explains in details how populate works, with diagrams, comparisons, and real-world examples.
The How to populate creator fields guide provides step-by-step instructions on how to add createdBy and updatedBy fields to your queries responses.
The following table sums up possible populate use cases and their associated parameter syntaxes, and links to sections of the Understanding populate guide which includes more detailed explanations:

Use case	Example parameter syntax	Detailed explanations to read
Populate everything, 1 level deep, including media fields, relations, components, and dynamic zones	populate=*	Populate all relations and fields, 1 level deep
Populate one relation,
1 level deep	populate=a-relation-name	Populate 1 level deep for specific relations
Populate several relations,
1 level deep	populate[0]=relation-name&populate[1]=another-relation-name&populate[2]=yet-another-relation-name	Populate 1 level deep for specific relations
Populate some relations, several levels deep	populate[root-relation-name][populate][0]=nested-relation-name	Populate several levels deep for specific relations
Populate a component	populate[0]=component-name	Populate components
Populate a component and one of its nested components	populate[0]=component-name&populate[1]=component-name.nested-component-name	Populate components
Populate a dynamic zone (only its first-level elements)	populate[0]=dynamic-zone-name	Populate dynamic zones
Populate a dynamic zone and its nested elements and relations, using a precisely defined, detailed population strategy	populate[dynamic-zone-name][on][component-category.component-name][populate][relation-name][populate][0]=field-name	Populate dynamic zones
 Tip
The easiest way to build complex queries with multiple-level population is to use our interactive query builder tool.

Combining Population with other operators
By utilizing the populate operator it is possible to combine other operators such as field selection, filters, and sort in the population queries.

 Caution
The population and pagination operators cannot be combined.

Populate with field selection
fields and populate can be combined.

Example request
GET /api/articles?fields[0]=title&fields[1]=slug&populate[headerImage][fields][0]=name&populate[headerImage][fields][1]=url

JavaScript query (built with the qs library):

Example response
{
  "data": [
    {
      "id": 1,
      "documentId": "h90lgohlzfpjf3bvan72mzll",
      "title": "Test Article",
      "slug": "test-article",
      "headerImage": {
        "id": 1,
        "documentId": "cf07g1dbusqr8mzmlbqvlegx",
        "name": "17520.jpg",
        "url": "/uploads/17520_73c601c014.jpg"
      }
    }
  ],
  "meta": {
    // ...
  }
}

Populate with filtering
filters and populate can be combined.

Example request
GET /api/articles?populate[categories][sort][0]=name%3Aasc&populate[categories][filters][name][$eq]=Cars

JavaScript query (built with the qs library):

Example response
{
  "data": [
    {
      "id": 1,
      "documentId": "a1b2c3d4e5d6f7g8h9i0jkl",
      "title": "Test Article",
      // ...
      "categories": {
        "data": [
          {
            "id": 2,
            "documentId": "jKd8djla9ndalk98hflj3",
            "name": "Cars"
            // ...
          }
        ]
        }
      }
    }
  ],
  "meta": {
    // ...
  }
}


Managing relations with API requests
Defining relations between content-types (that are designated as entities in the database layers) is connecting entities with each other.

Relations between content-types can be managed through the admin panel or through REST API or Document Service API requests.

Relations can be connected, disconnected or set through the Content API by passing parameters in the body of the request:

Parameter name	Description	Type of update
connect	Connects new entities.

Can be used in combination with disconnect.

Can be used with positional arguments to define an order for relations.	Partial
disconnect	Disconnects entities.

Can be used in combination with connect.	Partial
set	Set entities to a specific set. Using set will overwrite all existing connections to other entities.

Cannot be used in combination with connect or disconnect.	Full
 Note
When Internationalization (i18n) is enabled on the content-type, you can also pass a locale to set relations for a specific locale, as in this Document Service API example:

await strapi.documents('api::restaurant.restaurant').update({ 
  documentId: 'a1b2c3d4e5f6g7h8i9j0klm',
  locale: 'fr',
  data: { 
    category: {
      connect: ['z0y2x4w6v8u1t3s5r7q9onm', 'j9k8l7m6n5o4p3q2r1s0tuv']
    }
  }
})

If no locale is passed, the default locale will be assumed.

connect
Using connect in the body of a request performs a partial update, connecting the specified relations.

connect accepts either a shorthand or a longhand syntax:

Syntax type	Syntax example
shorthand	connect: ['z0y2x4w6v8u1t3s5r7q9onm', 'j9k8l7m6n5o4p3q2r1s0tuv']
longhand	connect: [{ documentId: 'z0y2x4w6v8u1t3s5r7q9onm' }, { documentId: 'j9k8l7m6n5o4p3q2r1s0tuv' }]
You can also use the longhand syntax to reorder relations.

connect can be used in combination with disconnect.

 Caution
connect can not be used for media attributes

Shorthand syntax example
Longhand syntax example
Sending the following request updates a restaurant, identified by its documnentId a1b2c3d4e5f6g7h8i9j0klm. The request uses the categories attribute to connect the restaurant with 2 categories identified by their documentId:

Example REST request
PUT http://localhost:1337/api/restaurants/a1b2c3d4e5f6g7h8i9j0klm

{
  data: {
    categories: {
      connect: ['z0y2x4w6v8u1t3s5r7q9onm', 'j9k8l7m6n5o4p3q2r1s0tuv']
    }
  }
}

Example Node request
const fetch = require('node-fetch');

const response = await fetch(
  'http://localhost:1337/api/restaurants/a1b2c3d4e5f6g7h8i9j0klm',
  {
    method: 'put',
    body: {
      data: {
        categories: {
          connect: ['z0y2x4w6v8u1t3s5r7q9onm', 'j9k8l7m6n5o4p3q2r1s0tuv']
        }
      }
    }
  }
);


Relations reordering 4.6.0
Positional arguments can be passed to the longhand syntax of connect to define the order of relations.

The longhand syntax accepts an array of objects, each object containing the documentId of the entry to be connected and an optional position object to define where to connect the relation.

 Different syntaxes for different relations
The syntaxes described in this documentation are useful for one-to-many, many-to-many and many-ways relations.
For one-to-one, many-to-one and one-way relations, the syntaxes are also supported but only the last relation will be used, so it's preferable to use a shorter format (e.g.: { data: { category: 'a1b2c3d4e5f6g7h8i9j0klm' } }, see REST API documentation).

To define the position for a relation, pass one of the following 4 different positional attributes:

Parameter name and syntax	Description	Type
before: documentId	Positions the relation before the given documentId.	documentId (string)
after: documentId	Positions the relation after the given documentId.	documentId (string)
start: true	Positions the relation at the start of the existing list of relations.	Boolean
end: true	Positions the relation at the end of the existing list of relations.	Boolean
The position argument is optional and defaults to position: { end: true }.

 Sequential order
Since connect is an array, the order of operations is important as they will be treated sequentially (see combined example below).

 Caution
The same relation should not be connected more than once, otherwise it would return a Validation error by the API.

Basic example
Combined example
Consider the following record in the database:

categories: [
  { documentId: 'j9k8l7m6n5o4p3q2r1s0tuv' }
  { documentId: 'z0y2x4w6v8u1t3s5r7q9onm' }
]

Sending the following request updates a restaurant, identified by its documentId a1b2c3d4e5f6g7h8i9j0klm, connecting a relation of entity with a documentId of ma12bc34de56fg78hi90jkl for the categories attribute and positioning it before the entity with documentId z0y2x4w6v8u1t3s5r7q9onm:

Example request to update the position of one relation
PUT http://localhost:1337/api/restaurants/a1b2c3d4e5f6g7h8i9j0klm

{
  data: {
    categories: {
      connect: [
        { documentId: 'ma12bc34de56fg78hi90jkl', position: { before: 'z0y2x4w6v8u1t3s5r7q9onm' } },
      ]
    }
  }
}


Edge cases: Draft & Publish or i18n disabled
When some built-in features of Strapi 5 are disabled for a content-type, such as Draft & Publish and Internationalization (i18), the connect parameter might be used differently:

Relation from a Category with i18n off to an Article with i18n on:

In this situation you can select which locale you are connecting to:

data: {
    categories: {
      connect: [
        { documentId: 'z0y2x4w6v8u1t3s5r7q9onm', locale: 'en' },
        // Connect to the same document id but with a different locale 👇
        { documentId: 'z0y2x4w6v8u1t3s5r7q9onm', locale: 'fr' },
      ]
   }
}

Relation from a Category with Draft & Publish off to an Article with Draft & Publish on:

data: {
  categories: {
    connect: [
      { documentId: 'z0y2x4w6v8u1t3s5r7q9onm', status: 'draft' },
      // Connect to the same document id but with different publication states 👇
      { documentId: 'z0y2x4w6v8u1t3s5r7q9onm', status: 'published' },
    ]
  }
}


disconnect
Using disconnect in the body of a request performs a partial update, disconnecting the specified relations.

disconnect accepts either a shorthand or a longhand syntax:

Syntax type	Syntax example
shorthand	disconnect: ['z0y2x4w6v8u1t3s5r7q9onm', 'j9k8l7m6n5o4p3q2r1s0tuv']
longhand	disconnect: [{ documentId: 'z0y2x4w6v8u1t3s5r7q9onm' }, { documentId: 'j9k8l7m6n5o4p3q2r1s0tuv' }]
disconnect can be used in combination with connect.


Shorthand syntax example
Longhand syntax example
Sending the following request updates a restaurant, identified by its documentId a1b2c3d4e5f6g7h8i9j0klm, disconnecting the relations with 2 entries identified by their documentId:

Example request using the shorthand syntax
PUT http://localhost:1337/api/restaurants/a1b2c3d4e5f6g7h8i9j0klm

{
  data: {
    categories: {
      disconnect: ['z0y2x4w6v8u1t3s5r7q9onm', 'j9k8l7m6n5o4p3q2r1s0tuv'],
    }
  }
}


set
Using set performs a full update, replacing all existing relations with the ones specified, in the order specified.

set accepts a shorthand or a longhand syntax:

Syntax type	Syntax example
shorthand	set: ['z0y2x4w6v8u1t3s5r7q9onm', 'j9k8l7m6n5o4p3q2r1s0tuv']
longhand	set: [{ documentId: 'z0y2x4w6v8u1t3s5r7q9onm' }, { documentId: 'j9k8l7m6n5o4p3q2r1s0tuv' }]
As set replaces all existing relations, it should not be used in combination with other parameters. To perform a partial update, use connect and disconnect.

 Omitting set
Omitting any parameter is equivalent to using set.
For instance, the following 3 syntaxes are all equivalent:

data: { categories: set: [{ documentId: 'z0y2x4w6v8u1t3s5r7q9onm' }, { documentId: 'j9k8l7m6n5o4p3q2r1s0tuv' }] }}
data: { categories: set: ['z0y2x4w6v8u1t3s5r7q9onm2', 'j9k8l7m6n5o4p3q2r1s0tuv'] }}
data: { categories: ['z0y2x4w6v8u1t3s5r7q9onm2', 'j9k8l7m6n5o4p3q2r1s0tuv'] }
Shorthand syntax example
Longhand syntax example
Sending the following request updates a restaurant, identified by its documentId a1b2c3d4e5f6g7h8i9j0klm, replacing all previously existing relations and using the categories attribute to connect 2 categories identified by their documentId:

Example request using the shorthand syntax with set
PUT http://localhost:1337/api/restaurants/a1b2c3d4e5f6g7h8i9j0klm

{
  data: {
    categories: {
      set: ['z0y2x4w6v8u1t3s5r7q9onm', 'j9k8l7m6n5o4p3q2r1s0tuv4'],
    }
  }
}


REST API: Sort & Pagination
Entries that are returned by queries to the REST API can be sorted and paginated.

 Tip
Strapi takes advantage of the ability of the `qs` library to parse nested objects to create more complex queries.

Use qs directly to generate complex queries instead of creating them manually. Examples in this documentation showcase how you can use qs.

You can also use the interactive query builder if you prefer playing with our online tool instead of generating queries with qs on your machine.

Sorting
Queries can accept a sort parameter that allows sorting on one or multiple fields with the following syntaxes:

GET /api/:pluralApiId?sort=value to sort on 1 field
GET /api/:pluralApiId?sort[0]=value1&sort[1]=value2 to sort on multiple fields (e.g. on 2 fields)
The sorting order can be defined with:

:asc for ascending order (default order, can be omitted)
or :desc for descending order.
Example: Sort using 2 fields
You can sort by multiple fields by passing fields in a sort array.


Example request: Sort using 2 fields
GET /api/restaurants?sort[0]=Description&sort[1]=Name

JavaScript query (built with the qs library):
Example response
{
  "data": [
    {
      "id": 9,
      "documentId": "hgv1vny5cebq2l3czil1rpb3",
      "Name": "BMK Paris Bamako",
      "Description": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "text": "A very short description goes here."
            }
          ]
        }
      ],
      // …
    },
    {
      "id": 8,
      "documentId": "flzc8qrarj19ee0luix8knxn",
      "Name": "Restaurant D",
      "Description": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "text": "A very short description goes here."
            }
          ]
        }
      ],
      // …
    },
   // … 
  ],
  "meta": {
    // …
  }
}

Example: Sort using 2 fields and set the order
Using the sort parameter and defining :asc or :desc on sorted fields, you can get results sorted in a particular order.


Example request: Sort using 2 fields and set the order
GET /api/restaurants?sort[0]=Description:asc&sort[1]=Name:desc

JavaScript query (built with the qs library):
Example response
{
  "data": [
    {
      "id": 8,
      "documentId": "flzc8qrarj19ee0luix8knxn",
      "Name": "Restaurant D",
      "Description": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "text": "A very short description goes here."
            }
          ]
        }
      ],
      // …
    },
    {
      "id": 9,
      "documentId": "hgv1vny5cebq2l3czil1rpb3",
      "Name": "BMK Paris Bamako",
      "Description": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "text": "A very short description goes here."
            }
          ]
        }
      ],
      // …
    },
    // …
  ],
  "meta": {
    // …
  }
}

Pagination
Queries can accept pagination parameters. Results can be paginated:

either by page (i.e., specifying a page number and the number of entries per page)
or by offset (i.e., specifying how many entries to skip and to return)
 Note
Pagination methods can not be mixed. Always use either page with pageSize or start with limit.

Pagination by page
To paginate results by page, use the following parameters:

Parameter	Type	Description	Default
pagination[page]	Integer	Page number	1
pagination[pageSize]	Integer	Page size	25
pagination[withCount]	Boolean	Adds the total numbers of entries and the number of pages to the response	True
Example request: Return only 10 entries on page 1
GET /api/articles?pagination[page]=1&pagination[pageSize]=10

JavaScript query (built with the qs library):
Example response
{
  "data": [
    // ...
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "pageCount": 5,
      "total": 48
    }
  }
}

Pagination by offset
To paginate results by offset, use the following parameters:

Parameter	Type	Description	Default
pagination[start]	Integer	Start value (i.e. first entry to return)	0
pagination[limit]	Integer	Number of entries to return	25
pagination[withCount]	Boolean	Toggles displaying the total number of entries to the response	true
 Tip
The default and maximum values for pagination[limit] can be configured in the ./config/api.js file with the api.rest.defaultLimit and api.rest.maxLimit keys.

Example request: Return only the first 10 entries using offset
GET /api/articles?pagination[start]=0&pagination[limit]=10

JavaScript query (built with the qs library):
Example response
{
  "data": [
    // ...
  ],
  "meta": {
    "pagination": {
      "start": 0,
      "limit": 10,
      "total": 42
    }
  }
}

REST API: Upload files
The Media Library feature is powered in the back-end server of Strapi by the upload package. To upload files to Strapi, you can either use the Media Library directly from the admin panel, or use the REST API, with the following available endpoints :

Method	Path	Description
GET	/api/upload/files	Get a list of files
GET	/api/upload/files/:id	Get a specific file
POST	/api/upload	Upload files
POST	/api/upload?id=x	Update fileInfo
DELETE	/api/upload/files/:id	Delete a file
 Notes
Folders are an admin panel-only feature and are not part of the Content API (REST or GraphQL). Files uploaded through REST are located in the automatically created "API Uploads" folder.
The GraphQL API does not support uploading media files. To upload files, use the REST API or directly add files from the Media Library in the admin panel. Some GraphQL mutations to update or delete uploaded media files are still possible (see GraphQL API documentation for details).
Upload files
Upload one or more files to your application.

files is the only accepted parameter, and describes the file(s) to upload. The value(s) can be a Buffer or Stream:

Browser
Node.js
<form>
  <!-- Can be multiple files -->
  <input type="file" name="files" />
  <input type="submit" value="Submit" />
</form>

<script type="text/javascript">
  const form = document.querySelector('form');

  form.addEventListener('submit', async (e) => {
    e.preventDefault();

    await fetch('/api/upload', {
      method: 'post',
      body: new FormData(e.target)
    });
  });
</script>

 Caution
You have to send FormData in your request body.

Upload entry files
Upload one or more files that will be linked to a specific entry.

The following parameters are accepted:

Parameter	Description
files	The file(s) to upload. The value(s) can be a Buffer or Stream.
path (optional)	The folder where the file(s) will be uploaded to (only supported on strapi-provider-upload-aws-s3).
refId	The ID of the entry which the file(s) will be linked to.
ref	The unique ID (uid) of the model which the file(s) will be linked to (see more below).
source (optional)	The name of the plugin where the model is located.
field	The field of the entry which the file(s) will be precisely linked to.
For example, given the Restaurant model attributes:

/src/api/restaurant/content-types/restaurant/schema.json
{
  // ...
  "attributes": {
    "name": {
      "type": "string"
    },
    "cover": {
      "type": "media",
      "multiple": false,
    }
  }
// ...
}

The following is an example of a corresponding front-end code:

<form>
  <!-- Can be multiple files if you setup "collection" instead of "model" -->
  <input type="file" name="files" />
  <input type="text" name="ref" value="api::restaurant.restaurant" />
  <input type="text" name="refId" value="5c126648c7415f0c0ef1bccd" />
  <input type="text" name="field" value="cover" />
  <input type="submit" value="Submit" />
</form>

<script type="text/javascript">
  const form = document.querySelector('form');

  form.addEventListener('submit', async (e) => {
    e.preventDefault();

    await fetch('/api/upload', {
      method: 'post',
      body: new FormData(e.target)
    });
  });
</script>


 Caution
You have to send FormData in your request body.

Update fileInfo
Update a file in your application.

fileInfo is the only accepted parameter, and describes the fileInfo to update:

import { FormData } from 'formdata-node';
import fetch from 'node-fetch';

const fileId = 50;
const newFileData = {
  alternativeText: 'My new alternative text for this image!',
};

const form = new FormData();

form.append('fileInfo', JSON.stringify(newFileData));

const response = await fetch(`http://localhost:1337/api/upload?id=${fileId}`, {
  method: 'post',
  body: form,
});



Models definition
Adding a file attribute to a model (or the model of another plugin) is like adding a new association.

The following example lets you upload and attach one file to the avatar attribute:

/src/api/restaurant/content-types/restaurant/schema.json

{
  // ...
  {
    "attributes": {
      "pseudo": {
        "type": "string",
        "required": true
      },
      "email": {
        "type": "email",
        "required": true,
        "unique": true
      },
      "avatar": {
        "type": "media",
        "multiple": false,
      }
    }
  }
  // ...
}


The following example lets you upload and attach multiple pictures to the restaurant content-type:

/src/api/restaurant/content-types/restaurant/schema.json
{
  // ...
  {
    "attributes": {
      "name": {
        "type": "string",
        "required": true
      },
      "covers": {
        "type": "media",
        "multiple": true,
      }
    }
  }
  // ...
}


Build your query URL with Strapi's interactive tool
A wide range of parameters can be used and combined to query your content with the REST API, which can result in long and complex query URLs.

Strapi's codebase uses the `qs` library to parse and stringify nested JavaScript objects. It's recommended to use qs directly to generate complex query URLs instead of creating them manually.

You can use the following interactive query builder tool to generate query URLs automatically:

Replace the values in the Endpoint and Endpoint Query Parameters fields with content that fits your needs.
Click the Copy to clipboard button to copy the automatically generated Query String URL which is updated as you type.
 Parameters usage
Please refer to the REST API parameters table and read the corresponding parameters documentation pages to better understand parameters usage.



Endpoint:
/api/books
Please keep the /api/ part unless you explicitly configured it differently in your project.

Endpoint Query Parameters:
{
sort: ['title:asc'],
filters: {
  title: {
    $eq: 'hello',
  },
},
populate: {
  author: {
    fields: ['firstName', 'lastName']
  }
},
fields: ['title'],
pagination: {
  pageSize: 10,
  page: 1,
},
status: 'published',
locale: ['en'],
}
Feel free to modify the code above.

Query String URL:
/api/books?sort[0]=title:asc&filters[title][$eq]=hello&populate[author][fields][0]=firstName&populate[author][fields][1]=lastName&fields[0]=title&pagination[pageSize]=10&pagination[page]=1&status=published&locale[0]=en


 Note
The default endpoint path is prefixed with /api/ and should be kept as-is unless you configured a different API prefix using the rest.prefix API configuration option.
For instance, to query the books collection type using the default API prefix, type /api/books in the Endpoint field.

 Disclaimer
The qs library and the interactive query builder provided on this page:

might not detect all syntax errors,
are not aware of the parameters and values available in a Strapi project,
and do not provide autocomplete features.
Currently, these tools are only provided to transform the JavaScript object in an inline query string URL. Using the generated query URL does not guarantee that proper results will get returned with your API.


REST API Guides
The REST API reference documentation is meant to provide a quick reference for all the endpoints and parameters available.

Guides
The following guides, officially maintained by the Strapi Documentation team, cover dedicated topics and provide detailed explanations (guides indicated with 🧠) or step-by-step instructions (guides indicated with 🛠️) for some use cases:

Understanding populate
Learn what populating means and how you can use the populate parameter in your REST API queries to add additional fields to your responses.

How to populate creator fields
Read step-by-step instructions on how to build a custom controller that leverages the populate parameter to add 'createdBy' and 'updatedBy' data to queries responses

Additional resources
 Want to help other users?
Some of the additional resources listed in this section have been created for Strapi v4 and might not fully work with Strapi 5. If you want to update one of the following articles for Strapi 5, feel free to propose an article for the Write for the Community program.

Additional tutorials and guides can be found in the following blog posts:

Authenticating requests with the REST API
Learn how to authenticate your REST API queries with JSON Web Tokens and API tokens.

Using Fetch with Strapi's Content API
Explore how to use the fetch() method of the Fetch API to interact with Strapi's Content API.

Requesting Strapi's REST API behind a Content Delivery Network (CDN)
Learn how to overcome network latency issues when requesting large numbers of media assets by leveraging the usage of a CDN with Strapi's REST API.

Tags:APIContent APIguidesREST APIREST API guides