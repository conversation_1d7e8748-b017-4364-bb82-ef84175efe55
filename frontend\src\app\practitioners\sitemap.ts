import { MetadataRoute } from 'next';
import { getStrapiContent } from '@/lib/strapi';

// Define the site URL from environment variable
// We need an absolute URL for sitemaps to work properly
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_API_URL?.replace('.strapiapp.com', '') || 'https://naturalhealingnow.vercel.app';

// Ensure SITE_URL doesn't have a trailing slash
const normalizedSiteUrl = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;

// Define interfaces for Strapi data types
interface StrapiItem {
  id?: string | number;
  documentId?: string;
  attributes?: {
    slug?: string;
    updatedAt?: string;
    publishedAt?: string;
    [key: string]: any;
  };
  slug?: string;
  updatedAt?: string;
  publishedAt?: string;
  [key: string]: any;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    console.log('Generating practitioners sitemap...');
    
    // Fetch practitioners
    const practitionersResponse = await getStrapiContent.practitioners.getAll({
      pagination: { pageSize: 1000 }
    });
    
    const practitioners = practitionersResponse?.data || [];
    
    // Generate sitemap entries for practitioners
    const practitionerEntries = practitioners.map((practitioner: StrapiItem) => {
      const attributes = practitioner.attributes || practitioner;
      const slug = attributes.slug || practitioner.slug;
      const updatedAt = attributes.updatedAt || practitioner.updatedAt || new Date().toISOString();
      
      if (!slug) return null;
      
      return {
        url: `${normalizedSiteUrl}/practitioners/${slug}`,
        lastModified: new Date(updatedAt),
        changeFrequency: 'weekly',
        priority: 0.8,
      };
    }).filter(Boolean) as MetadataRoute.Sitemap;
    
    // Combine all practitioner-related entries
    return [
      {
        url: `${normalizedSiteUrl}/practitioners`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      ...practitionerEntries,
    ];
  } catch (error) {
    console.error('Error generating practitioners sitemap:', error);
    
    // Return only the main practitioners URL if there's an error
    return [
      {
        url: `${normalizedSiteUrl}/practitioners`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
    ];
  }
}
