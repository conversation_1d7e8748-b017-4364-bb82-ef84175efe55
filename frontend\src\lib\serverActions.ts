/**
 * Server actions for data fetching and mutations
 * These functions are meant to be used in Server Components and Route Handlers
 */
import 'server-only';
import { cache } from 'react';
import { revalidateTag, revalidatePath } from 'next/cache';
import { fetchContentType } from './serverFetch';
import { buildClinicListParams, buildPractitionerListParams } from './strapiQueryBuilder';

/**
 * Generate cache tags for a specific content type
 */
function generateCacheTags(contentType: string, id?: string | number): string[] {
  const tags = [`strapi-${contentType}`];
  if (id) {
    tags.push(`strapi-${contentType}-${id}`);
  }
  return tags;
}

/**
 * Cached function to fetch global settings
 */
export const getGlobalSettings = cache(async () => {
  return fetchContentType('global-setting', {
    params: {
      populate: '*',
      publicationState: 'live',
    },
    revalidate: 3600, // 1 hour
  });
});

/**
 * Cached function to fetch categories
 */
export const getCategories = cache(async (pageSize = 10) => {
  return fetchContentType('categories', {
    params: {
      pagination: { pageSize },
      populate: '*',
    },
    revalidate: 3600, // 1 hour
  });
});

/**
 * Cached function to fetch specialties
 */
export const getSpecialties = cache(async (pageSize = 10) => {
  return fetchContentType('specialties', {
    params: {
      pagination: { pageSize },
      populate: '*',
    },
    revalidate: 3600, // 1 hour
  });
});

/**
 * Cached function to fetch clinics with optimized parameters
 */
export const getClinics = cache(async (options: {
  page?: number;
  pageSize?: number;
  featured?: boolean;
  location?: string;
  query?: string;
  specialtySlug?: string;
} = {}) => {
  const params = buildClinicListParams(options);
  
  return fetchContentType('clinics', {
    params,
    revalidate: 1800, // 30 minutes
  });
});

/**
 * Cached function to fetch practitioners with optimized parameters
 */
export const getPractitioners = cache(async (options: {
  page?: number;
  pageSize?: number;
  featured?: boolean;
  location?: string;
  query?: string;
  specialtySlug?: string;
} = {}) => {
  const params = buildPractitionerListParams(options);
  
  return fetchContentType('practitioners', {
    params,
    revalidate: 1800, // 30 minutes
  });
});

/**
 * Cached function to fetch a single clinic by slug
 */
export const getClinicBySlug = cache(async (slug: string) => {
  return fetchContentType('clinics', {
    params: {
      filters: { slug: { $eq: slug } },
      populate: '*',
    },
    revalidate: 1800, // 30 minutes
  });
});

/**
 * Cached function to fetch a single practitioner by slug
 */
export const getPractitionerBySlug = cache(async (slug: string) => {
  return fetchContentType('practitioners', {
    params: {
      filters: { slug: { $eq: slug } },
      populate: '*',
    },
    revalidate: 1800, // 30 minutes
  });
});

/**
 * Cached function to fetch blog posts
 */
export const getBlogPosts = cache(async (options: {
  page?: number;
  pageSize?: number;
  featured?: boolean;
  categorySlug?: string;
  query?: string;
} = {}) => {
  const { page = 1, pageSize = 10, featured, categorySlug, query } = options;
  
  const filters: Record<string, any> = {};
  
  if (featured) {
    filters.isFeatured = { $eq: true };
  }
  
  if (categorySlug) {
    filters.categories = {
      slug: { $eq: categorySlug },
    };
  }
  
  if (query) {
    filters.$or = [
      { title: { $containsi: query } },
      { excerpt: { $containsi: query } },
      { content: { $containsi: query } },
    ];
  }
  
  return fetchContentType('blog-posts', {
    params: {
      sort: 'publishDate:desc',
      pagination: { page, pageSize },
      filters: Object.keys(filters).length > 0 ? filters : undefined,
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true,
          },
        },
        categories: true,
      },
    },
    revalidate: 900, // 15 minutes
  });
});

/**
 * Cached function to fetch a single blog post by slug
 */
export const getBlogPostBySlug = cache(async (slug: string) => {
  return fetchContentType('blog-posts', {
    params: {
      filters: { slug: { $eq: slug } },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true,
          },
        },
        categories: true,
      },
    },
    revalidate: 900, // 15 minutes
  });
});

/**
 * Revalidate content by type
 */
export function revalidateContent(contentType: string, id?: string | number): void {
  const tags = generateCacheTags(contentType, id);
  tags.forEach(tag => revalidateTag(tag));
}

/**
 * Revalidate all content
 */
export function revalidateAllContent(): void {
  revalidateTag('strapi-global-setting');
  revalidateTag('strapi-categories');
  revalidateTag('strapi-specialties');
  revalidateTag('strapi-clinics');
  revalidateTag('strapi-practitioners');
  revalidateTag('strapi-blog-posts');
  
  // Also revalidate key pages
  revalidatePath('/');
  revalidatePath('/clinics');
  revalidatePath('/practitioners');
  revalidatePath('/blog');
  revalidatePath('/categories');
  revalidatePath('/specialities');
}
