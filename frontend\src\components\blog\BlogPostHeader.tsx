"use client";

import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ser, <PERSON><PERSON>lock, FiFolder } from 'react-icons/fi';
import { formatDate } from '@/lib/blogUtils';
import { BlogPost } from '@/lib/blogUtils';

interface BlogPostHeaderProps {
  post: BlogPost;
}

/**
 * Blog post header component
 * Displays the title, metadata, and featured image
 */
const BlogPostHeader: React.FC<BlogPostHeaderProps> = ({ post }) => {
  return (
    <div className="mb-8">
      {/* Title */}
      <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">{post.title}</h1>

      {/* Date, Author Info, and Reading Time */}
      <div className="flex flex-wrap items-center text-gray-600 mb-6 gap-4">
        <div className="flex items-center">
          <FiCalendar className="mr-2 text-emerald-500" />
          <span>{formatDate(post.published_at)}</span>
        </div>

        <div className="flex items-center">
          <FiUser className="mr-2 text-emerald-500" />
          <Link
            href={`/blog/authors/${post.author.slug}`}
            className="text-emerald-600 hover:text-emerald-700"
          >
            {post.author.name}
          </Link>
        </div>

        {/* Reading Time */}
        <div className="flex items-center">
          <FiClock className="mr-2 text-emerald-500" />
          <span>{post.reading_time || 2} min read</span>
        </div>

        {/* Categories */}
        {post.categories && post.categories.length > 0 && (
          <div className="flex items-center flex-wrap gap-2">
            <div className="flex items-center">
              <FiFolder className="mr-2 text-emerald-500" />
            </div>
            {post.categories.map(category => (
              <Link
                key={category.id}
                href={`/blog/categories/${category.slug}`}
                className="bg-emerald-50 text-emerald-700 px-2 py-1 rounded-full text-xs hover:bg-emerald-100 transition-colors"
              >
                {category.name}
              </Link>
            ))}
          </div>
        )}
      </div>

      {/* Featured Image - only show if exists */}
      {post.featured_image && (
        <div className="relative h-96 w-full mb-8">
          <img
            src={post.featured_image}
            alt={post.title}
            className="object-cover w-full h-full rounded-lg"
          />
        </div>
      )}
    </div>
  );
};

export default BlogPostHeader;
