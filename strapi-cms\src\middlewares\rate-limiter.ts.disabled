/**
 * Rate limiter middleware for Strapi
 * Limits the number of requests from a single IP address
 */

import { RateLimiterMemory } from 'rate-limiter-flexible';

// Create a rate limiter instance with higher limits for Strapi Cloud
const apiLimiter = new RateLimiterMemory({
  points: 300, // Increased from 100 to 300 requests per minute
  duration: 60, // Per 60 seconds
});

// Create a more permissive rate limiter for authentication endpoints
const authLimiter = new RateLimiterMemory({
  points: 50, // Increased from 10 to 50 requests per minute
  duration: 60, // Per 60 seconds
});

export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Skip rate limiting in development mode
    if (process.env.NODE_ENV === 'development') {
      return await next();
    }

    // Skip rate limiting for health check endpoints and admin panel
    if (
      ctx.path === '/_health' ||
      ctx.path === '/healthz' ||
      ctx.path.startsWith('/admin') ||
      ctx.path.includes('/health') ||
      ctx.path.includes('/_health')
    ) {
      return await next();
    }

    // Get IP address from request
    const ip = ctx.request.ip;

    // Skip rate limiting for whitelisted IPs (if configured)
    const whitelistedIPs = process.env.RATE_LIMIT_WHITELIST
      ? process.env.RATE_LIMIT_WHITELIST.split(',')
      : [];

    if (whitelistedIPs.includes(ip)) {
      return await next();
    }

    try {
      // Use stricter rate limiting for authentication endpoints
      if (ctx.path.includes('/auth/')) {
        await authLimiter.consume(ip);
      } else {
        await apiLimiter.consume(ip);
      }

      // If rate limit not exceeded, proceed to next middleware
      return await next();
    } catch (err) {
      // Rate limit exceeded, return 429 Too Many Requests
      ctx.status = 429;
      ctx.body = {
        error: 'Too Many Requests',
        message: 'Rate limit exceeded, please try again later',
      };
      return;
    }
  };
};
