'use client';

import { QueryClient } from '@tanstack/react-query';
import { dehydrate, hydrate } from '@tanstack/react-query';

/**
 * Default stale time for queries (24 hours)
 * This determines how long data remains "fresh" before revalidation
 * Using a long stale time to rely on on-demand revalidation
 */
export const DEFAULT_STALE_TIME = 1 * 60 * 60 * 1000; // Changed to 1 hour

/**
 * Default cache time for queries (7 days)
 * This determines how long inactive data remains in the cache
 */
export const DEFAULT_CACHE_TIME = 7 * 24 * 60 * 60 * 1000;

/**
 * Content-specific cache times (in milliseconds)
 * Using longer cache times with on-demand revalidation
 */
export const CACHE_TIMES = {
  // Static content rarely changes
  static: {
    staleTime: Infinity,            // Never stale (rely on on-demand revalidation)
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
  // Semi-static content changes occasionally
  semiStatic: {
    staleTime: Infinity,            // Never stale (rely on on-demand revalidation)
    gcTime: 3 * 24 * 60 * 60 * 1000, // 3 days
  },
  // Dynamic content changes frequently
  dynamic: {
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 48 * 60 * 60 * 1000,    // 48 hours
  },
  // Content types
  global: {
    staleTime: Infinity,            // Never stale (rely on on-demand revalidation)
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
  categories: {
    staleTime: Infinity,            // Never stale (rely on on-demand revalidation)
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
  specialties: {
    staleTime: Infinity,            // Never stale (rely on on-demand revalidation)
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
  clinics: {
    staleTime: Infinity,            // Never stale (rely on on-demand revalidation)
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
  practitioners: {
    staleTime: Infinity,            // Never stale (rely on on-demand revalidation)
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
  blogPosts: {
    staleTime: Infinity,            // Never stale (rely on on-demand revalidation)
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
};

/**
 * Creates a new QueryClient with optimal configuration
 * @returns A configured QueryClient instance
 */
export function createQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Data remains fresh for 5 minutes by default
        staleTime: DEFAULT_STALE_TIME,

        // Keep data in cache for 10 minutes
        gcTime: DEFAULT_CACHE_TIME,

        // Retry failed queries 3 times
        retry: 3,

        // Use exponential backoff for retries
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),

        // Refetch on window focus by default
        refetchOnWindowFocus: true,

        // Don't refetch on reconnect by default (can be enabled per query)
        refetchOnReconnect: false,
      },
      mutations: {
        // Retry failed mutations 2 times
        retry: 2,

        // Use exponential backoff for retries
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
    },
  });
}

/**
 * Serialize the query client state for SSR
 */
export function serializeQueryClientState(queryClient: QueryClient): string {
  return JSON.stringify(dehydrate(queryClient));
}

/**
 * Hydrate the query client with serialized state
 */
export function hydrateQueryClient(queryClient: QueryClient, state: string): void {
  hydrate(queryClient, JSON.parse(state));
}
