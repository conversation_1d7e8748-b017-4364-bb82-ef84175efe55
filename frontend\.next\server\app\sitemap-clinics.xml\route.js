(()=>{var e={};e.id=4731,e.ids=[4731],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},93303:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>m,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var n={};i.r(n),i.d(n,{GET:()=>p});var r=i(96559),s=i(48088),o=i(37719),a=i(58446);let l=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";process.env.NEXT_PUBLIC_SITE_URL;let c=l.endsWith("/")?l.slice(0,-1):l;async function p(e){let t=[];try{console.log("Generating clinics sitemap...");let e=await a.$.clinics.getAll({pagination:{pageSize:1,page:1},fields:["id"],sort:["id:asc"],filters:{isActive:{$eq:!0}}}),i=e?.meta?.pagination?.total||0,n=Math.ceil(i/1e3);console.log(`Found ${i} total clinics, will fetch in ${n} pages with page size 1000`),console.log("API URL being used: https://nice-badge-2130241d6c.strapiapp.com"),console.log(`Site URL being used: ${l}`),0===i&&console.warn("No clinics found in the database. Check Strapi connection and data.");for(let e=1;e<=n;e++){console.log(`Fetching clinics page ${e} of ${n}...`);try{let i=await a.$.clinics.getAll({pagination:{pageSize:1e3,page:e},fields:["slug","updatedAt","createdAt","isActive"],sort:["id:asc"],filters:{isActive:{$eq:!0}}}),r=i?.data||[];for(let i of(console.log(`Retrieved ${r.length} clinics on page ${e}`),r.length<1e3&&e<n&&console.warn(`Warning: Got only ${r.length} clinics on page ${e}, expected 1000. This may indicate a pagination issue.`),r)){let e,n=null;if(i.slug?n=i.slug:i.attributes&&i.attributes.slug&&(n=i.attributes.slug),!n){console.warn(`Clinic object for which slug was not found (ID: ${i.id||"unknown"}, isActive: ${i.isActive??"N/A"}):`,JSON.stringify(i,null,2));continue}e=i.updatedAt?new Date(i.updatedAt):i.attributes&&i.attributes.updatedAt?new Date(i.attributes.updatedAt):i.createdAt?new Date(i.createdAt):i.attributes&&i.attributes.createdAt?new Date(i.attributes.createdAt):new Date,t.push({url:`${c}/clinics/${n}`,lastModified:e,changeFrequency:"weekly",priority:.8})}}catch(t){console.error(`Error fetching clinics page ${e}:`,t)}}console.log(`Total clinic entries in sitemap: ${t.length}`),t.unshift({url:`${c}/clinics`,lastModified:new Date,changeFrequency:"daily",priority:.9}),console.log(`Final sitemap entries count (including index page): ${t.length}`),console.log(`Sitemap generation summary:
    - Total clinics found: ${i}
    - Total pages fetched: ${n}
    - Page size used: 1000
    - Final sitemap entries: ${t.length}
    - Sitemap URL: ${c}/sitemap-clinics.xml
    `);let r=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${t.map(e=>`  <url>
    <loc>${e.url}</loc>
    <lastmod>${e.lastModified.toISOString()}</lastmod>
    <changefreq>${e.changeFrequency}</changefreq>
    <priority>${e.priority}</priority>
  </url>`).join("\n")}
</urlset>`;try{if(!r.startsWith("<?xml")||!r.includes("<urlset")||!r.includes("</urlset>"))throw console.error("Generated XML appears to be malformed"),Error("Malformed XML");return new Response(r,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600","X-Content-Type-Options":"nosniff"}})}catch(t){console.error("Error validating XML:",t);let e=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${c}/clinics</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;return new Response(e,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600","X-Content-Type-Options":"nosniff"}})}}catch(e){if(console.error("Error generating clinics sitemap:",e),t.length>0)return console.log(`Returning partial sitemap with ${t.length} entries despite error`),t.some(e=>e.url===`${c}/clinics`)||t.unshift({url:`${c}/clinics`,lastModified:new Date,changeFrequency:"daily",priority:.9}),new Response(`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${t.map(e=>`  <url>
    <loc>${e.url}</loc>
    <lastmod>${e.lastModified.toISOString()}</lastmod>
    <changefreq>${e.changeFrequency}</changefreq>
    <priority>${e.priority}</priority>
  </url>`).join("\n")}
</urlset>`,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600","X-Content-Type-Options":"nosniff"}});return console.log("Returning fallback sitemap with only the clinics index page"),new Response(`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${c}/clinics</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"no-cache","X-Content-Type-Options":"nosniff"}})}}let u=new r.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/sitemap-clinics.xml/route",pathname:"/sitemap-clinics.xml",filename:"route",bundlePath:"app/sitemap-clinics.xml/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\sitemap-clinics.xml\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:h}=u;function m(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},96487:()=>{},96559:(e,t,i)=>{"use strict";e.exports=i(44870)}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),n=t.X(0,[7719,1330,3376,8446],()=>i(93303));module.exports=n})();