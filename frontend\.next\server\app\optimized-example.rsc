1:"$Sreact.fragment"
3:I[7555,[],""]
4:I[1295,[],""]
6:I[9665,[],"MetadataBoundary"]
8:I[9665,[],"OutletBoundary"]
b:I[4911,[],"AsyncMetadataOutlet"]
d:I[9665,[],"ViewportBoundary"]
f:I[8385,["4219","static/chunks/app/global-error-33e94e2a08941e18.js"],"default"]
:HL["/_next/static/css/b3cbcd051438d1d5.css","style"]
:HL["/_next/static/css/876f41e4233f67d1.css","style"]
0:{"P":null,"b":"1j7101_TKUIPIhyNJnUYD","p":"","c":["","optimized-example"],"i":false,"f":[[["",{"children":["optimized-example",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b3cbcd051438d1d5.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/876f41e4233f67d1.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],"$L2"]}],{"children":["optimized-example",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":["$L5",["$","$L6",null,{"children":"$L7"}],null,["$","$L8",null,{"children":["$L9","$La",["$","$Lb",null,{"promise":"$@c"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","isqkB6Taxtx2Yto4lr4Id",{"children":[["$","$Ld",null,{"children":"$Le"}],null]}],null]}],false]],"m":"$undefined","G":["$f",[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/b3cbcd051438d1d5.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/876f41e4233f67d1.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]]],"s":false,"S":true}
10:I[8102,["6874","static/chunks/6874-a6d03f2fcdecd00f.js","6923","static/chunks/app/optimized-example/page-15faf6a2af332480.js"],"default"]
11:"$Sreact.suspense"
17:I[4911,[],"AsyncMetadata"]
5:["$","div",null,{"className":"container mx-auto px-4 py-8","children":[["$","h1",null,{"className":"text-3xl font-bold mb-4","children":"Optimized Data Fetching Example"}],["$","$L10",null,{}],["$","div",null,{"className":"mb-8","children":["$","p",null,{"className":"text-lg","children":"This page demonstrates server-side optimized data fetching from Strapi. The data is cached using React's cache function and Next.js built-in caching."}]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-8","children":[["$","$11",null,{"fallback":["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md animate-pulse","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Global Settings"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/2"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-5/6"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-2/3"}]]}]]}],"children":"$L12"}],["$","$11",null,{"fallback":["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md animate-pulse","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Categories"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/2"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-5/6"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-2/3"}]]}]]}],"children":"$L13"}],["$","$11",null,{"fallback":["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md animate-pulse","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Featured Clinics"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/2"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-5/6"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-2/3"}]]}]]}],"children":"$L14"}],["$","$11",null,{"fallback":["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md animate-pulse","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Featured Practitioners"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/2"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-5/6"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-2/3"}]]}]]}],"children":"$L15"}],["$","$11",null,{"fallback":["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md animate-pulse","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Latest Blog Posts"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-4 bg-gray-200 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-1/2"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-5/6"}],["$","div",null,{"className":"h-4 bg-gray-200 rounded w-2/3"}]]}]]}],"children":"$L16"}]]}],["$","div",null,{"className":"mt-8 p-6 bg-blue-50 rounded-lg","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"How This Reduces API Calls"}],["$","p",null,{"className":"mb-2","children":"This implementation reduces API calls to Strapi in several ways:"}],["$","ol",null,{"className":"list-decimal pl-5 space-y-2","children":[["$","li",null,{"children":[["$","strong",null,{"children":"React's cache function:"}]," Deduplicates requests within the same render pass."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Content-type specific caching:"}]," Different cache durations based on how frequently content changes (e.g., global settings are cached longer than blog posts)."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Suspense and streaming:"}]," Allows for parallel data fetching and progressive rendering."]}],["$","li",null,{"children":[["$","strong",null,{"children":"Server-side rendering:"}]," Data is fetched once on the server and sent to the client."]}]]}],["$","p",null,{"className":"mt-4","children":["For more details, see the documentation in ",["$","code",null,{"children":"frontend/src/docs/OPTIMIZED_API_CALLS.md"}],"."]}]]}]]}]
7:["$","$11",null,{"fallback":null,"children":["$","$L17",null,{"promise":"$@18"}]}]
a:null
e:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
9:null
13:["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Categories"}],["$","ul",null,{"className":"list-disc pl-5","children":[]}]]}]
12:["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Global Settings"}],["$","pre",null,{"className":"bg-gray-100 p-4 rounded overflow-auto max-h-60","children":"{\n  \"data\": []\n}"}]]}]
14:["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Featured Clinics"}],["$","ul",null,{"className":"list-disc pl-5","children":[]}]]}]
16:["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Latest Blog Posts"}],["$","ul",null,{"className":"list-disc pl-5","children":[]}]]}]
15:["$","div",null,{"className":"bg-white p-6 rounded-lg shadow-md","children":[["$","h2",null,{"className":"text-2xl font-semibold mb-4","children":"Featured Practitioners"}],["$","ul",null,{"className":"list-disc pl-5","children":[]}]]}]
19:I[8191,["844","static/chunks/ee560e2c-d487a5d95364bb7f.js","6874","static/chunks/6874-a6d03f2fcdecd00f.js","3063","static/chunks/3063-6f649b04282637e5.js","3464","static/chunks/3464-2a961ac37dd0bd57.js","3120","static/chunks/3120-81531c711eadd5f3.js","19","static/chunks/19-6505d4676b5a20ca.js","2112","static/chunks/2112-bea88abad6b22c99.js","3254","static/chunks/3254-87ac87476d72bc3d.js","7177","static/chunks/app/layout-7c7313da2febba34.js"],"ErrorProvider"]
1a:I[3310,["844","static/chunks/ee560e2c-d487a5d95364bb7f.js","6874","static/chunks/6874-a6d03f2fcdecd00f.js","3063","static/chunks/3063-6f649b04282637e5.js","3464","static/chunks/3464-2a961ac37dd0bd57.js","3120","static/chunks/3120-81531c711eadd5f3.js","19","static/chunks/19-6505d4676b5a20ca.js","2112","static/chunks/2112-bea88abad6b22c99.js","3254","static/chunks/3254-87ac87476d72bc3d.js","7177","static/chunks/app/layout-7c7313da2febba34.js"],"AuthProvider"]
1b:I[139,["844","static/chunks/ee560e2c-d487a5d95364bb7f.js","6874","static/chunks/6874-a6d03f2fcdecd00f.js","3063","static/chunks/3063-6f649b04282637e5.js","3464","static/chunks/3464-2a961ac37dd0bd57.js","3120","static/chunks/3120-81531c711eadd5f3.js","19","static/chunks/19-6505d4676b5a20ca.js","2112","static/chunks/2112-bea88abad6b22c99.js","3254","static/chunks/3254-87ac87476d72bc3d.js","7177","static/chunks/app/layout-7c7313da2febba34.js"],"default"]
1c:I[3254,["844","static/chunks/ee560e2c-d487a5d95364bb7f.js","6874","static/chunks/6874-a6d03f2fcdecd00f.js","3063","static/chunks/3063-6f649b04282637e5.js","3464","static/chunks/3464-2a961ac37dd0bd57.js","3120","static/chunks/3120-81531c711eadd5f3.js","19","static/chunks/19-6505d4676b5a20ca.js","2112","static/chunks/2112-bea88abad6b22c99.js","3254","static/chunks/3254-87ac87476d72bc3d.js","7177","static/chunks/app/layout-7c7313da2febba34.js"],"default"]
1d:I[1901,["844","static/chunks/ee560e2c-d487a5d95364bb7f.js","6874","static/chunks/6874-a6d03f2fcdecd00f.js","8039","static/chunks/app/error-1ac3c73860ef4f59.js"],"default"]
1e:I[6874,["6874","static/chunks/6874-a6d03f2fcdecd00f.js","4345","static/chunks/app/not-found-2ec3a196ca8210e7.js"],""]
1f:I[3864,["844","static/chunks/ee560e2c-d487a5d95364bb7f.js","6874","static/chunks/6874-a6d03f2fcdecd00f.js","3063","static/chunks/3063-6f649b04282637e5.js","3464","static/chunks/3464-2a961ac37dd0bd57.js","3120","static/chunks/3120-81531c711eadd5f3.js","19","static/chunks/19-6505d4676b5a20ca.js","2112","static/chunks/2112-bea88abad6b22c99.js","3254","static/chunks/3254-87ac87476d72bc3d.js","7177","static/chunks/app/layout-7c7313da2febba34.js"],"Analytics"]
2:["$","html",null,{"lang":"en","children":["$undefined",["$","body",null,{"className":"__className_d65c78","children":[["$","$L19",null,{"children":["$","$L1a",null,{"children":["$","$L1b",null,{"children":["$","$L1c",null,{"siteName":"Natural Healing Now","logoLight":null,"footerCategories":[],"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$1d","errorStyles":[],"errorScripts":[],"template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"container mx-auto px-4 py-16 flex flex-col items-center justify-center min-h-[60vh]","children":["$","div",null,{"className":"text-center max-w-md","children":[["$","div",null,{"className":"flex justify-center mb-6","children":["$","svg",null,{"stroke":"currentColor","fill":"none","strokeWidth":"2","viewBox":"0 0 24 24","strokeLinecap":"round","strokeLinejoin":"round","className":"w-16 h-16 text-emerald-600","children":["$undefined",[["$","circle","0",{"cx":"12","cy":"12","r":"10","children":[]}],["$","line","1",{"x1":"12","y1":"8","x2":"12","y2":"12","children":[]}],["$","line","2",{"x1":"12","y1":"16","x2":"12.01","y2":"16","children":[]}]]],"style":{"color":"$undefined"},"height":"1em","width":"1em","xmlns":"http://www.w3.org/2000/svg"}]}],["$","h1",null,{"className":"text-4xl font-bold mb-4","children":"404 - Page Not Found"}],["$","p",null,{"className":"text-gray-600 mb-8","children":"The page you are looking for doesn't exist or has been moved."}],["$","$L1e",null,{"href":"/","className":"inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 transition-colors","children":[["$","svg",null,{"stroke":"currentColor","fill":"none","strokeWidth":"2","viewBox":"0 0 24 24","strokeLinecap":"round","strokeLinejoin":"round","className":"mr-2","children":["$undefined",[["$","path","0",{"d":"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z","children":[]}],["$","polyline","1",{"points":"9 22 9 12 15 12 15 22","children":[]}]]],"style":{"color":"$undefined"},"height":"1em","width":"1em","xmlns":"http://www.w3.org/2000/svg"}],"Back to Homepage"]}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}],["$","$L1f",null,{}]]}]]}]
18:{"metadata":[["$","title","0",{"children":"Natural Healing Now - Holistic Health Directory"}],["$","meta","1",{"name":"description","content":"Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey."}],["$","link","2",{"rel":"shortcut icon","href":"/favicon.ico"}],["$","link","3",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","link","4",{"rel":"icon","href":"/favicon.ico"}],["$","link","5",{"rel":"apple-touch-icon","href":"/favicon.ico"}]],"error":null,"digest":"$undefined"}
c:{"metadata":"$18:metadata","error":null,"digest":"$undefined"}
