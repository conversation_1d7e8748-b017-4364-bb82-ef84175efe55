import Link from 'next/link';
import { FiMapPin, FiFilter } from 'react-icons/fi';
import { getStrapiContent } from '@/lib/strapi';
import ExploreFurther from '@/components/shared/ExploreFurther';
import SearchInput from '@/components/shared/SearchInput';
import { Metadata } from 'next';
import { Suspense } from 'react';
import ClinicsList from './ClinicsList'; // This will become more of a display component
import SpecialtiesList from './SpecialtiesList';
import ConditionsList from './ConditionsList';

// ISR and Dynamic Params configuration
export const revalidate = 3600; // 1 hour
export const experimental_ppr = true;
export const dynamicParams = true;

// --- Helper functions and type definitions (copied from previous correct version) ---
const STRAPI_URL_INTERNAL = process.env.NEXT_PUBLIC_STRAPI_API_URL;
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL;

const getStrapiMediaUrlInternal = (url: string | undefined | null): string | null => {
  if (!url) return null;
  if (url.startsWith('http://') || url.startsWith('https://')) return url;
  return `${STRAPI_URL_INTERNAL}${url}`;
};

interface RawStrapiClinic {
  id: string; name: string; slug: string; description?: string | null;
  logo?: { url?: string; } | null; featuredImage?: { url?: string; } | null;
  address: { city: string; stateProvince: string; streetAddress1?: string; streetAddress?: string; postalCode?: string; country?: string; };
  contactInfo?: { phoneNumber?: string; websiteUrl?: string; emailAddress?: string; } | null;
  isVerified?: boolean; location?: { googlePlaceId?: string | null; latitude?: number | null; longitude?: number | null; } | null;
  openingHours?: Array<{ id: string | number; day: string; openTime?: string | null; closeTime?: string | null; closed?: boolean | null; }> | null;
  services?: Array<{ id: string; name: string; slug: string }> | null; specialties?: Array<{ id: string; name: string; slug: string }> | null;
  conditions?: Array<{ id: string; name: string; slug: string }> | null;
  practitioners?: Array<{ id: string; name: string; slug: string; title?: string; profilePicture?: { url?: string } | null; }> | null;
  appointment_options?: Array<{ id: string; name: string; slug: string }> | null; payment_methods?: Array<{ id: string; name: string; slug: string }> | null;
  seo?: { metaTitle?: string | null; metaDescription?: string | null; metaImage?: { url?: string } | null; openGraph?: { ogTitle?: string | null; ogDescription?: string | null; ogImage?: { url?: string } | null; ogUrl?: string | null; ogType?: string | null; } | null; structuredData?: string | object | null; } | null;
}

interface TransformedClinic {
  id: string; name: string; slug: string; description?: string | null; logo: string | null; featuredImage: string | null;
  address: { city: string; stateProvince: string; streetAddress1?: string; streetAddress?: string; postalCode?: string; country?: string; };
  contactInfo?: { phoneNumber?: string; websiteUrl?: string; emailAddress?: string; } | null;
  isVerified?: boolean; location?: { googlePlaceId?: string | null; latitude?: number | null; longitude?: number | null; } | null;
  openingHours?: Array<{ id: string | number; day: string; openTime?: string | null; closeTime?: string | null; closed?: boolean | null; }> | null;
  services?: Array<{ id: string; name: string; slug: string }> | null; specialties?: Array<{ id: string; name: string; slug: string }> | null;
  conditions?: Array<{ id: string; name: string; slug: string }> | null;
  practitioners?: Array<{ id: string; name: string; slug: string; title?: string; profilePicture?: string | null; }> | null;
  appointment_options?: Array<{ id: string; name: string; slug: string }> | null; payment_methods?: Array<{ id: string; name: string; slug: string }> | null;
  seo?: { metaTitle?: string | null; metaDescription?: string | null; metaImage?: string | null; openGraph?: { ogTitle?: string | null; ogDescription?: string | null; ogImage?: string | null; ogUrl?: string | null; ogType?: string | null; } | null; structuredData?: string | object | null; } | null;
}

interface StrapiApiResponse<T> {
  data: T[];
  meta?: { pagination?: { page: number; pageSize: number; pageCount: number; total: number; }; };
}

function transformClinicDataInternal(rawClinic: RawStrapiClinic): TransformedClinic | null {
  if (!rawClinic || !rawClinic.id || !rawClinic.name) { return null; }
  const transformedPractitioners = rawClinic.practitioners?.map(p => ({ ...p, profilePicture: getStrapiMediaUrlInternal(p.profilePicture?.url) })) || null;
  const transformedSeo = rawClinic.seo ? { ...rawClinic.seo, metaImage: getStrapiMediaUrlInternal(rawClinic.seo.metaImage?.url), openGraph: rawClinic.seo.openGraph ? { ...rawClinic.seo.openGraph, ogImage: getStrapiMediaUrlInternal(rawClinic.seo.openGraph.ogImage?.url) } : null } : null;
  return {
    id: String(rawClinic.id), name: rawClinic.name || 'Unnamed Clinic', slug: rawClinic.slug || `clinic-${rawClinic.id}`,
    description: rawClinic.description, logo: getStrapiMediaUrlInternal(rawClinic.logo?.url), featuredImage: getStrapiMediaUrlInternal(rawClinic.featuredImage?.url),
    address: rawClinic.address || { city: 'Unknown', stateProvince: 'N/A' }, contactInfo: rawClinic.contactInfo, isVerified: rawClinic.isVerified || false,
    location: rawClinic.location, openingHours: rawClinic.openingHours, services: rawClinic.services, specialties: rawClinic.specialties,
    conditions: rawClinic.conditions, practitioners: transformedPractitioners, appointment_options: rawClinic.appointment_options,
    payment_methods: rawClinic.payment_methods, seo: transformedSeo
  };
}

interface StrapiCondition { id: string; name: string; slug: string; }
interface TransformedCondition { id: string; name: string; slug: string; }
function transformConditionData(strapiCondition: StrapiCondition): TransformedCondition | null {
  if (!strapiCondition || !strapiCondition.name || !strapiCondition.slug) { return null; }
  return { id: strapiCondition.id, name: strapiCondition.name, slug: strapiCondition.slug };
}

interface StrapiSpecialty { id: number | string; attributes?: { name?: string; slug?: string; }; name?: string; slug?: string; }
interface ProcessedSpecialty { name: string; slug: string; }
const transformSpecialtyData = (specialty: StrapiSpecialty): ProcessedSpecialty | null => {
  if (!specialty || !specialty.id) { return null; }
  const id = specialty.id?.toString() || ''; const attributes = specialty.attributes || {};
  const name = attributes.name || specialty.name || 'Unnamed Specialty';
  let slug = attributes.slug || specialty.slug || (name !== 'Unnamed Specialty' ? name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '') : `specialty-${id}`);
  return { name, slug };
};
// --- End of Helper functions ---

interface ClinicStaticParams { page?: string; location?: string; specialtySlug?: string; }

export async function generateStaticParams(): Promise<ClinicStaticParams[]> {
  // ... (generateStaticParams implementation remains the same)
  try {
    let popularLocations: string[] = [];
    try {
      const locationsResponse = await getStrapiContent.clinics.getAll({
        fields: ['address'],
        populate: { address: { fields: ['city', 'stateProvince'] } },
        pagination: { pageSize: 100 },
        next: { revalidate: 3600, tags: ['strapi-clinics-locations'] }
      });
      const cities = new Set<string>();
      locationsResponse?.data?.forEach((clinic: any) => {
        const city = clinic?.address?.city;
        const state = clinic?.address?.stateProvince;
        if (city && state) cities.add(`${city}, ${state}`);
        else if (city) cities.add(city);
      });
      popularLocations = Array.from(cities).slice(0, 5);
    } catch (error) {
      console.error('Error fetching popular locations for clinics page:', error);
      popularLocations = ['New York', 'Los Angeles', 'Chicago']; // Fallback
    }

    const params: ClinicStaticParams[] = [{}, { page: '1' }];
    popularLocations.forEach(location => {
      params.push({ location });
      params.push({ page: '1', location });
    });
    // Add more common params if needed
    return params;
  } catch (error) {
    console.error('Error generating static params for clinics list:', error);
    return [{}];
  }
}

export async function generateMetadata({ searchParams }: { searchParams?: { query?: string; location?: string; page?: string } } = {}): Promise<Metadata> {
  const query = searchParams?.query || '';
  const location = searchParams?.location || '';
  const page = searchParams?.page || '1';

  let title = "Find Holistic Health Clinics | Natural Healing Now";
  const baseDescription = "Discover clinics offering natural healing therapies and holistic health services. Search by location, specialty, or condition to find practitioners near you.";
  let description = baseDescription;
  let canonicalPath = "/clinics";
  const queryParts: string[] = [];

  if (query) {
    title = `Clinics for "${query}" | Natural Healing Now`;
    description = `Find clinics related to "${query}". ${baseDescription}`;
    queryParts.push(`query=${encodeURIComponent(query)}`);
  }
  if (location) {
    title = query ? `${title} in ${location}` : `Clinics in ${location} | Natural Healing Now`;
    description = `Find clinics in ${location}. ${baseDescription}`;
    queryParts.push(`location=${encodeURIComponent(location)}`);
  }
  if (page !== '1') {
    title += ` - Page ${page}`;
    queryParts.push(`page=${page}`);
  }
  if (queryParts.length > 0) {
    canonicalPath += `?${queryParts.join('&')}`;
  }

  const canonicalUrl = SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath;

  return {
    title, description, alternates: { canonical: canonicalUrl },
    openGraph: { title, description, url: canonicalUrl, type: 'website' },
    twitter: { card: 'summary_large_image', title, description },
  };
}

interface ClinicsPageProps {
  searchParams?: {
    query?: string;
    location?: string;
    page?: string;
  };
}

export default async function ClinicsPage({ searchParams }: ClinicsPageProps) {
  const query = searchParams?.query || '';
  const location = searchParams?.location || '';
  const currentPage = Number(searchParams?.page) || 1;

  // Fetch filtered/paginated clinics server-side
  let fetchedClinicsData: { clinics: TransformedClinic[], totalPages: number, totalCount: number } = {
    clinics: [], totalPages: 1, totalCount: 0,
  };
  try {
    // Construct tags for Next.js Data Cache based on search params
    const clinicCacheTags = ['strapi-clinics-list'];
    if (query) clinicCacheTags.push(`strapi-clinics-query-${query}`);
    if (location) clinicCacheTags.push(`strapi-clinics-location-${location}`);
    clinicCacheTags.push(`strapi-clinics-page-${currentPage}`);

    const response: StrapiApiResponse<RawStrapiClinic> = await getStrapiContent.clinics.getAll({
      query,
      location,
      page: currentPage,
      populate: { /* ... all necessary populates for ClinicCard ... */
        logo: true, address: true, contactInfo: true, location: true,
        openingHours: true, services: true, specialties: true, conditions: true,
        practitioners: { populate: { profilePicture: true } },
        appointment_options: true, payment_methods: true, seo: true
      },
      next: { revalidate: 3600, tags: clinicCacheTags } // Use dynamic tags
    });
    const rawClinics = response?.data || [];
    fetchedClinicsData = {
      clinics: rawClinics.map(transformClinicDataInternal).filter((c): c is TransformedClinic => c !== null),
      totalPages: response?.meta?.pagination?.pageCount || 1,
      totalCount: response?.meta?.pagination?.total || 0,
    };
  } catch (error) {
    console.error("Error fetching clinics for ClinicsPage:", error);
    // Keep default empty data on error
  }

  // Fetch initial specialties with longer cache time (1 week) since specialties rarely change
  let initialSpecialties: ProcessedSpecialty[] = [];
  try {
    const specialtiesResponse = await getStrapiContent.specialties.getAll({
      pagination: { pageSize: 20 },
      fields: ['name', 'slug'],
      cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15
      next: {
        revalidate: 604800, // 1 week (7 days)
        tags: ['strapi-specialties', 'strapi-specialties-all', 'initial-specialties-for-clinics-page']
      }
    });
    if (specialtiesResponse?.data && Array.isArray(specialtiesResponse.data)) { // Ensure it's an array
      initialSpecialties = specialtiesResponse.data
        .map((s: StrapiSpecialty) => transformSpecialtyData(s))
        .filter((s: ProcessedSpecialty | null): s is ProcessedSpecialty => s !== null);
    }
  } catch (error) { console.error("Error fetching initial specialties for /clinics page:", error); }

  // Fetch initial conditions with longer cache time (1 week) since conditions rarely change
  let initialConditions: TransformedCondition[] = [];
  try {
    const conditionsResponse = await getStrapiContent.conditions.getAll({
      pagination: { pageSize: 20 },
      fields: ['name', 'slug'],
      cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15
      next: {
        revalidate: 604800, // 1 week (7 days)
        tags: ['strapi-conditions', 'strapi-conditions-all', 'initial-conditions-for-clinics-page']
      }
    });
    if (conditionsResponse?.data && Array.isArray(conditionsResponse.data)) { // Ensure it's an array
      initialConditions = conditionsResponse.data
        .map((c: StrapiCondition) => transformConditionData(c))
        .filter((c: TransformedCondition | null): c is TransformedCondition => c !== null);
    }
  } catch (error) { console.error("Error fetching initial conditions for /clinics page:", error); }

  return (
    <>
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Find a Holistic Health Clinic</h1>
          <p className="text-lg max-w-3xl">
            Discover clinics offering natural healing therapies and holistic health services near you.
          </p>
        </div>
      </div>

      <div className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Suspense fallback={<div className="h-10 w-full bg-gray-200 rounded-lg animate-pulse"></div>}>
                <SearchInput placeholder="Search by name, speciality, conditions..." paramName="query" />
              </Suspense>
            </div>
            <div className="flex-1">
              <Suspense fallback={<div className="h-10 w-full bg-gray-200 rounded-lg animate-pulse"></div>}>
                <SearchInput placeholder="City, state, or zip code" paramName="location" icon={<FiMapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />} />
              </Suspense>
            </div>
            <div>
              <button className="w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200">
                <FiFilter />
                <span>Filters</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="py-12">
        <div className="container mx-auto px-4">
          <ClinicsList
            clinicsToDisplay={fetchedClinicsData.clinics}
            totalPages={fetchedClinicsData.totalPages}
            totalCount={fetchedClinicsData.totalCount}
            currentPage={currentPage} // Pass current page for pagination component
          />
        </div>
      </div>

      <div className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">Browse by Specialty</h2>
          <Suspense fallback={<div className="grid grid-cols-2 md:grid-cols-4 gap-4">{[...Array(4)].map((_, i) => (<div key={i} className="bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"></div>))}</div>}>
            <SpecialtiesList initialSpecialties={initialSpecialties} />
          </Suspense>
        </div>
      </div>

      <div className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">Specialists by Health Conditions</h2>
          <Suspense fallback={<div className="grid grid-cols-2 md:grid-cols-4 gap-4">{[...Array(4)].map((_, i) => (<div key={i} className="bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"></div>))}</div>}>
            <ConditionsList initialConditions={initialConditions} />
          </Suspense>
        </div>
      </div>

      <ExploreFurther currentPath="/clinics" />
    </>
  );
}
