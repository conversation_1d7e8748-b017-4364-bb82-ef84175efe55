import { NextRequest, NextResponse } from 'next/server';

/**
 * This is a CORS proxy for Strapi API requests
 * It allows the frontend to make requests to Strapi without CORS issues
 *
 * Example usage:
 * Instead of: fetch('https://your-strapi.com/api/posts')
 * Use: fetch('/api/strapi-proxy?endpoint=/posts')
 */
export async function GET(request: NextRequest) {
  try {
    // Get the Strapi API URL from environment variables
    const STRAPI_URL = process.env.NEXT_PUBLIC_API_URL;

    if (!STRAPI_URL) {
      return NextResponse.json(
        { error: 'NEXT_PUBLIC_API_URL is not defined in environment variables' },
        { status: 500 }
      );
    }

    // Get the endpoint and query parameters from the request
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint');

    if (!endpoint) {
      return NextResponse.json(
        { error: 'Missing endpoint parameter' },
        { status: 400 }
      );
    }

    // Build the full URL to the Strapi API
    const url = new URL(`/api${endpoint}`, STRAPI_URL);

    // Log the request for debugging
    console.log(`Proxying request to: ${url.toString()}`);

    // Get the API token from environment variables
    const API_TOKEN = process.env.STRAPI_API_TOKEN;

    // Create headers with Authorization token if available
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (API_TOKEN) {
      headers['Authorization'] = `Bearer ${API_TOKEN}`;
    }

    // Forward all query parameters except 'endpoint'
    searchParams.forEach((value, key) => {
      if (key !== 'endpoint') {
        url.searchParams.append(key, value);
      }
    });

    // Make the request to Strapi using native fetch
    const response = await fetch(url.toString(), {
      headers,
      method: 'GET',
      // No caching for proxy requests - these are client-initiated
      cache: 'no-store',
    });

    if (!response.ok) {
      throw new Error(`Strapi API responded with status: ${response.status}`);
    }

    // Parse and return the response from Strapi
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in Strapi proxy:', error);

    // Return a more detailed error response
    return NextResponse.json(
      {
        error: error.message || 'An error occurred while proxying the request to Strapi',
        status: error.status || 500,
      },
      { status: error.status || 500 }
    );
  }
}

/**
 * Handle POST requests to the proxy
 */
export async function POST(request: NextRequest) {
  try {
    // Get the Strapi API URL from environment variables
    const STRAPI_URL = process.env.NEXT_PUBLIC_API_URL;

    if (!STRAPI_URL) {
      return NextResponse.json(
        { error: 'NEXT_PUBLIC_API_URL is not defined in environment variables' },
        { status: 500 }
      );
    }

    // Get the endpoint from the query parameters
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint');

    if (!endpoint) {
      return NextResponse.json(
        { error: 'Missing endpoint parameter' },
        { status: 400 }
      );
    }

    // Build the full URL to the Strapi API
    const url = new URL(`/api${endpoint}`, STRAPI_URL);

    // Log the request for debugging
    console.log(`Proxying POST request to: ${url.toString()}`);

    // Get the API token from environment variables
    const API_TOKEN = process.env.STRAPI_API_TOKEN;

    // Create headers with Authorization token if available
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (API_TOKEN) {
      headers['Authorization'] = `Bearer ${API_TOKEN}`;
    }

    // Get the request body
    const body = await request.json();

    // Make the request to Strapi using native fetch
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      // No caching for proxy requests - these are client-initiated
      cache: 'no-store',
    });

    if (!response.ok) {
      throw new Error(`Strapi API responded with status: ${response.status}`);
    }

    // Parse and return the response from Strapi
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in Strapi proxy:', error);

    // Return a more detailed error response
    return NextResponse.json(
      {
        error: error.message || 'An error occurred while proxying the request to Strapi',
        status: error.status || 500,
      },
      { status: error.status || 500 }
    );
  }
}
