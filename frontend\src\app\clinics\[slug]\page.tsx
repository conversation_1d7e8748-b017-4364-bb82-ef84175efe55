import Layout from '@/components/layout/Layout';
import Link from 'next/link';
import { FiMapPin, FiPhone, FiGlobe, FiArrowLeft } from 'react-icons/fi';
import { MdVerifiedUser } from "react-icons/md"; // Import the verified icon
import { getStrapiContent } from '@/lib/strapi'; // Import the API functions
import { notFound } from 'next/navigation'; // Import notFound for handling missing data
import { Metadata } from 'next'; // Import Metadata type
import ClinicLogo from '@/components/clinics/ClinicLogo'; // Import the new logo component
import OpeningHours from '@/components/clinics/OpeningHours'; // Import the new OpeningHours component
import MarkdownContent from '@/components/blog/MarkdownContent'; // Import MarkdownContent
import LazyImage from '@/components/shared/LazyImage'; // Import LazyImage for better image handling
import { sanitizeUrl, getOgImageUrl } from '@/lib/mediaUtils'; // Import sanitizeUrl and getOgImageUrl functions
// Removed ExploreFurther import

// Generate static paths for all clinics at build time
export async function generateStaticParams() {
  try {
    // In Next.js 15, we need to explicitly opt-in to caching
    const response = await getStrapiContent.clinics.getAllSlugs({
      cache: 'force-cache', // Explicitly cache for generateStaticParams
      next: {
        revalidate: 43200, // Revalidate slugs every 12 hours
        tags: ['strapi-clinics-slugs'] // Use the tag defined in getAllSlugs
      }
    });

    // Ensure response and response.data are not null and response.data is an array
    if (response && response.data && Array.isArray(response.data)) {
      console.log(`Pre-rendering ${response.data.length} clinic detail pages`);

      // Filter out any items that might be null or don't have a slug property
      return response.data
        .filter(item => item && typeof item.slug === 'string')
        .map(item => ({
          slug: item.slug,
        }));
    }

    // Log an error or return an empty array if data is not as expected
    console.error('Failed to fetch clinic slugs or data is not in expected format:', response);
    return [];
  } catch (error) {
    console.error('Error fetching clinic slugs for generateStaticParams:', error);
    return []; // Return empty array on error to prevent build failure
  }
}

// Force static rendering for this route segment
export const dynamic = 'force-static';

// Enable ISR with both time-based and on-demand revalidation
// Use a 12-hour revalidation period to match the list page
export const revalidate = 43200; // 12 hours in seconds
// Allow dynamic params to be generated on-demand
export const dynamicParams = true;

// Define the site URL from environment variable with proper fallback to your actual domain
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log the URL being used for debugging
if (process.env.NODE_ENV === 'production') {
  console.log(`Using site URL for canonical URLs: ${SITE_URL}`);
}

// Helper function to process image URLs
const processImageUrl = (imageUrl: string | undefined | null, imageType: string, itemName: string = 'Unknown'): string | null => {
  if (!imageUrl) return null;

  const strapiBaseUrl = process.env.NEXT_PUBLIC_API_URL || '';

  // Check if this is a Strapi Cloud URL (contains media.strapiapp.com)
  if (imageUrl.includes('media.strapiapp.com')) {
    // Ensure it has https:// prefix
    const processedUrl = imageUrl.startsWith('http') ? imageUrl : `https://${imageUrl}`;

    // Log in production for debugging
    if (process.env.NODE_ENV === 'production') {
      console.log(`Processed ${imageType} URL:`, {
        itemName,
        original: imageUrl,
        processed: processedUrl
      });
    }

    return processedUrl;
  } else if (imageUrl.startsWith('http')) {
    // Already an absolute URL
    return imageUrl;
  } else {
    // Relative URL, prepend the Strapi API URL
    const processedUrl = `${strapiBaseUrl}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`;

    // Log in production for debugging
    if (process.env.NODE_ENV === 'production') {
      console.log(`Processed ${imageType} URL:`, {
        itemName,
        original: imageUrl,
        processed: processedUrl
      });
    }

    return processedUrl;
  }
};

// Define the expected structure for a single clinic from the API
// Adjust based on the actual populated fields from getBySlug
type PractitionerStub = { // Simplified type for linked practitioners - Direct structure from API
  id: string;
  name: string;
  slug: string;
  title?: string; // Assuming title might be populated
  profilePicture?: { url?: string } | null; // Add profilePicture type
};

// Type for the direct data structure expected from getBySlug
// (Assuming no top-level 'attributes' wrapper for the main clinic data)
type ClinicData = {
    id: string; // Keep ID
    name: string;
    slug: string;
    isVerified?: boolean; // Add isVerified field
    description?: string | null;
    logo?: { url?: string } | null; // Updated type for Strapi v5 flat structure
    featured_image?: { url?: string } | null; // Assuming similar flat structure for featured_image
    videoEmbed?: { // Keep nested structure
      url?: string;
      oembed?: { html?: string };
    } | null;
    address: { // Keep nested structure
      streetAddress1?: string;
      streetAddress?: string;
      city: string;
      stateProvince: string;
      postalCode: string;
      country: string;
    };
    location?: { // Add this location object
      googlePlaceId?: string | null;
      latitude?: number | null; // Optional: include if needed later
      longitude?: number | null; // Optional: include if needed later
    } | null;
    contactInfo?: { // Keep nested structure
      phoneNumber?: string;
      emailAddress?: string;
      websiteUrl?: string;
    } | null;
    appointment_options?: Array<{ id: string; name: string; slug: string }> | null; // Updated to relation
    payment_methods?: Array<{ id: string; name: string; slug: string }> | null; // Updated to relation
  // Update hours type to match the new component structure
  openingHours?: Array<{
    id: string | number;
    day: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';
    openTime?: string | null;
    closeTime?: string | null;
    closed?: boolean | null;
  }> | null;
  services?: Array<{ id: string; name: string; slug: string }> | null; // Added slug
  specialties?: Array<{ id: string; name: string; slug: string }> | null; // Added slug
  conditions?: Array<{ id: string; name: string; slug: string }> | null; // Added slug
    practitioners?: Array<PractitionerStub> | null; // Direct array based on API response
    seo?: {
      metaTitle?: string | null;
      metaDescription?: string | null;
      metaImage?: { url?: string } | null;
      openGraph?: {
        ogTitle?: string | null;
        ogDescription?: string | null;
        ogImage?: { url?: string } | null;
        ogUrl?: string | null;
        ogType?: string | null;
      } | null;
      structuredData?: string | object | null; // Allow object too
    } | null;
};

// This function fetches the actual data
async function getClinicData(slug: string): Promise<ClinicData | null> {
  try {
    // Use a cache-first approach with the same revalidation period as the page
    // In Next.js 15, we need to explicitly opt-in to caching
    const response = await getStrapiContent.clinics.getBySlug(slug, {
      next: {
        tags: ['strapi-clinics-list', `strapi-clinic-${slug}`],
        revalidate: 43200 // 12 hours to match the page-level setting
      },
      cache: 'force-cache' // Explicitly opt-in to caching for Next.js 15
    });

    // Assuming getBySlug returns { data: [ { id: ..., name: ..., slug: ... } ] }
    if (response?.data && response.data.length > 0) {
      return response.data[0] as ClinicData; // Cast to direct data type
    }
    return null;
  } catch (error) {
    console.error(`Error fetching clinic with slug ${slug}:`, error);
    return null;
  }
}

// Generate Metadata
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  // In Next.js 15, we don't need to await params anymore
  const { slug } = params;
  const clinic = await getClinicData(slug);

  if (!clinic) {
    return {
      title: 'Clinic Not Found | Natural Healing Now',
      description: 'The requested clinic could not be found.',
      // No canonical for not found pages
    };
  }

  const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL?.replace('/api', '') || ''; // Get base URL for images

  // --- SEO Data Extraction ---
  const seoData = clinic.seo;
  const fallbackTitle = `${clinic.name} | Natural Healing Now`;
  const fallbackDescription = clinic.description || `Learn more about ${clinic.name}, a clinic offering holistic health services.`;

  const metaTitle = seoData?.metaTitle || fallbackTitle;
  const metaDescription = seoData?.metaDescription || fallbackDescription;

  const canonicalPath = `/clinics/${clinic.slug}`;
  // Assuming canonicalURL might be a direct property of seoData, if not, fallback.
  // If canonicalURL is consistently missing, this part might need adjustment based on Strapi's actual output.
  const canonicalUrl = (seoData as any)?.canonicalURL || (SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath);

  // Determine OG/Twitter Image URL (prioritize OG from SEO plugin, then meta, then clinic logo)
  let imageUrl: string | undefined = undefined;

  // First try to get image from SEO plugin's openGraph data
  const ogImageFromSeo = seoData?.openGraph?.ogImage?.url; // Corrected: ogImage
  const metaImageUrl = seoData?.metaImage?.url;
  const clinicLogoUrl = clinic.logo?.url; // Use clinic logo as fallback

  // Use the new getOgImageUrl function to get proper Strapi media URLs
  if (ogImageFromSeo) { // Corrected: ogImageFromSeo
    imageUrl = getOgImageUrl(ogImageFromSeo);
  } else if (metaImageUrl) {
    imageUrl = getOgImageUrl(metaImageUrl);
  } else if (clinicLogoUrl) {
    imageUrl = getOgImageUrl(clinicLogoUrl);
  }

  // Log the image URL in production for debugging
  if (process.env.NODE_ENV === 'production') {
    console.log('Clinic og:image:', {
      ogImageFromSeo, // Corrected: ogImageFromSeo
      metaImageUrl,
      clinicLogoUrl,
      imageUrl
    });
  }

  const ogImages = imageUrl ? [{ url: imageUrl }] : [];

  // Define allowed Open Graph types
  type OgType = "article" | "website" | "book" | "profile" | "music.song" | "music.album" | "music.playlist" | "music.radio_station" | "video.movie" | "video.episode" | "video.tv_show" | "video.other";
  const allowedOgTypes: OgType[] = ["article", "website", "book", "profile", "music.song", "music.album", "music.playlist", "music.radio_station", "video.movie", "video.episode", "video.tv_show", "video.other"];

  // Validate or fallback ogType
  let ogType: OgType = 'website'; // Default to 'website'
  const strapiOgType = seoData?.openGraph?.ogType; // Corrected: ogType
  if (strapiOgType && (allowedOgTypes as string[]).includes(strapiOgType)) {
    ogType = strapiOgType as OgType;
  }

  // --- Metadata Object ---
  return {
    title: metaTitle,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    // Use openGraph data from SEO plugin if available
    openGraph: seoData?.openGraph ? {
      title: seoData.openGraph.ogTitle || metaTitle, // Corrected: ogTitle
      description: seoData.openGraph.ogDescription || metaDescription, // Corrected: ogDescription
      url: seoData.openGraph.ogUrl || canonicalUrl, // Corrected: ogUrl
      type: ogType, // Use the validated type
      // siteName: 'Natural Healing Now', // siteName is not standard in OpenGraph type and not in your ClinicData type
      images: ogImages,
    } : {
      // Fallback if no openGraph data in SEO plugin
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      type: ogType,
      images: ogImages,
      // siteName: 'Natural Healing Now',
    },
    twitter: { // Add Twitter card data
      card: 'summary_large_image',
      title: seoData?.openGraph?.ogTitle || metaTitle, // Corrected: ogTitle
      description: seoData?.openGraph?.ogDescription || metaDescription, // Corrected: ogDescription
      images: ogImages, // Use the same image array
      // site: '@YourTwitterHandle', // Optional: Add Twitter handle
      // creator: '@CreatorHandle', // Optional: Add creator handle if applicable
    },
  };
}

export default async function ClinicDetailPage({ params }: { params: { slug: string } }) {
  // In Next.js 15, we don't need to await params anymore
  const { slug } = params;

  // We don't need to check for prefetched data with ISR
  // The data will be cached and revalidated according to our ISR settings
  const clinic = await getClinicData(slug);

  // If clinic not found, use Next.js notFound function
  if (!clinic) {
    notFound();
  }

  // Access properties directly from the fetched clinic object
  const {
    name = 'Unnamed Clinic',
    description = 'No description available.',
    logo = null, // Keep nested structure access below
    featured_image = null, // Keep nested structure access below
    videoEmbed = null, // Keep nested structure access below
    address = { city: 'N/A', stateProvince: 'N/A', postalCode: '', country: '' }, // Keep nested structure access below
    location = null, // Destructure location
    contactInfo = null, // Keep nested structure access below
    appointment_options = null, // Updated to relation field
    payment_methods = null, // Updated to relation field
    openingHours = null, // Destructure openingHours instead of hours
    services = null, // Direct access below
    specialties = null, // Direct access below
    conditions = null, // Direct access below
    practitioners = null, // Direct access below
    isVerified = false, // Destructure isVerified, default to false
    seo = null // Destructure seo
  } = clinic; // Destructure directly from clinic, not clinic.attributes

  // Construct full address string safely
  const streetPart = address ? (address.streetAddress1 || address.streetAddress || '').trim() : '';
  const cityStateZipPart = address ? `${address.city}, ${address.stateProvince} ${address.postalCode}`.trim() : '';
  const fullAddress = streetPart && cityStateZipPart ? `${streetPart}, ${cityStateZipPart}` : (streetPart || cityStateZipPart); // Combine with comma only if both parts exist
  // Process the logo URL using our utility function
  const logoUrl = logo?.url ? processImageUrl(logo.url, 'clinic-logo', name) : null;
  // Prioritize constructing iframe from URL for consistent responsive styling
  const videoHtml = videoEmbed?.url ? `<iframe src="${videoEmbed.url.replace('watch?v=', 'embed/')}" title="${name} Video" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: 0;"></iframe>` : videoEmbed?.oembed?.html || null;

  // Ensure structuredData is a string before rendering
  let structuredDataString: string | null = null;
  if (seo?.structuredData) {
    if (typeof seo.structuredData === 'string') {
      structuredDataString = seo.structuredData;
    } else if (typeof seo.structuredData === 'object') {
      try {
        structuredDataString = JSON.stringify(seo.structuredData);
      } catch (e) {
        console.error("Failed to stringify structuredData object:", e);
      }
    }
  }

  return (
    <>
      {/* Breadcrumb Section */}
      <div className="bg-gray-100 py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-emerald-600">Home</Link>
            <span className="mx-2">/</span>
            <Link href="/clinics" className="hover:text-emerald-600">Clinics</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-800">{name}</span>
          </div>
        </div>
      </div>

      {/* Clinic Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row md:items-center">
            {/* Logo using ClinicLogo component */}
            <div className="mb-4 md:mb-0 md:mr-6"> {/* Wrapper div, removed specific styling now handled by component */}
              <ClinicLogo
                src={logoUrl || ''} // Pass logoUrl, ensure it's a string
                alt={`${name} logo`}
                fallbackChar={name.charAt(0)}
                // containerClassName and imageClassName use defaults from the component
              />
            </div>

            <div>
              <div className="flex items-center gap-x-2 mb-2"> {/* Wrapper for name and verified status */}
                <h1 className="text-3xl font-bold text-gray-800">{name}</h1>
                {isVerified && (
                  <div className="flex items-center gap-x-1 bg-emerald-100 text-emerald-700 px-2 py-0.5 rounded-full text-sm font-medium">
                    <MdVerifiedUser color="#009967" size={16} />
                    <span>VERIFIED</span>
                  </div>
                )}
              </div>
              {fullAddress && (
                <div className="flex items-center text-gray-600 mb-1">
                  <FiMapPin className="mr-2 text-emerald-500 flex-shrink-0" />
                  <span>{fullAddress}</span>
                </div>
              )}

              {contactInfo?.phoneNumber && (
                <div className="flex items-center text-gray-600 mb-1">
                  <FiPhone className="mr-2 text-emerald-500 flex-shrink-0" />
                  <a href={`tel:${contactInfo.phoneNumber.replace(/[^\d+]/g, '')}`} className="hover:text-emerald-600">
                    {contactInfo.phoneNumber}
                  </a>
                </div>
              )}

              {contactInfo?.websiteUrl && (
                <div className="flex items-center text-gray-600">
                  <FiGlobe className="mr-2 text-emerald-500 flex-shrink-0" />
                  <a
                    href={contactInfo.websiteUrl}
                    target="_blank"
                    rel="nofollow noopener noreferrer" // Added nofollow
                    className="text-emerald-600 hover:text-emerald-700 break-all" // Added break-all for long URLs
                  >
                    Visit Website
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Column */}
          <div className="lg:col-span-2">
            {/* About Section */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">About {name}</h2>
              <div className="bg-white rounded-lg shadow-sm p-6">
                {/* Use MarkdownContent, pass applyNoFollow={false} for clinic descriptions */}
                <MarkdownContent content={description || ''} applyNoFollow={false} />

                {/* Video embedded */}
                {videoHtml && (
                  <div className="mt-6 rounded-lg overflow-hidden" style={{ maxWidth: '100%' }}>
                    <div
                      style={{ position: 'relative', paddingBottom: '56.25%', height: 0 }}
                      dangerouslySetInnerHTML={{ __html: videoHtml }} // Use dangerouslySetInnerHTML for oembed html
                    />
                  </div>
                )}
              </div>
            </section>

            {/* Services Section */}
            {services && services.length > 0 && ( // Check direct array length
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Services</h2>
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {services.map((service) => (
                      <li key={service.id} className="flex items-center">
                        <span className="h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"></span>
                        {/* Service name rendered as text, not a link */}
                        <span className="text-gray-700">{service.name}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            )}

            {/* Specialities Section */}
            {specialties && specialties.length > 0 && ( // Check direct array length
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Specialties</h2>
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {specialties.map((specialty) => (
                      <li key={specialty.id} className="flex items-center">
                        <span className="h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"></span>
                        <Link href={`/specialities/${specialty.slug}`} className="no-underline hover:text-emerald-600">
                          {specialty.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            )}

            {/* Conditions Treated Section */}
            {conditions && conditions.length > 0 && ( // Check direct array length
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Conditions Treated</h2>
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {conditions.map((condition) => (
                      <li key={condition.id} className="flex items-center">
                        <span className="h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"></span>
                        <Link href={`/conditions/${condition.slug}`} className="no-underline hover:text-emerald-600">
                          {condition.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            )}

            {/* Practitioners Section */}
            {practitioners && practitioners.length > 0 && (
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Practitioners</h2>
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <ul className="divide-y divide-gray-200">
                    {practitioners.map((practitioner) => (
                      <li key={practitioner.id} className="py-4 first:pt-0 last:pb-0">
                        <Link
                          href={`/practitioners/${practitioner.slug}`}
                          className="flex items-center hover:bg-gray-50 p-2 rounded-lg transition-colors"
                        >
                          <div className={`${practitioner.profilePicture?.url ? '' : 'bg-emerald-100'} h-12 w-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0 overflow-hidden relative`}>
                            {practitioner.profilePicture?.url ? (
                              <LazyImage
                                src={processImageUrl(practitioner.profilePicture.url, 'practitioner-profile', practitioner.name) || ''}
                                alt={`${practitioner.name} profile picture`}
                                width={48} // Aspect ratio hint
                                height={48} // Aspect ratio hint
                                fillContainer={true}
                                className="object-cover rounded-full" // fillContainer will make it fill the parent div
                                showPlaceholder={true}
                              />
                            ) : (
                              <span className="text-emerald-700 font-semibold">
                                {practitioner.name.charAt(0)}
                              </span>
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-800">{practitioner.name}</h3>
                            {practitioner.title && (
                              <p className="text-sm text-gray-600">{practitioner.title}</p>
                            )}
                          </div>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            )}
          </div>

          {/* Sidebar */}
          <div>
            {/* Appointment Options Section */}
            {appointment_options && appointment_options.length > 0 && (
              <section className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 className="font-bold text-gray-800 mb-4">Appointment Options</h3>
                <ul className="space-y-2">
                  {appointment_options.map((option) => (
                    <li key={option.id} className="flex items-center">
                      <span className="h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"></span>
                      <span>{option.name}</span>
                    </li>
                  ))}
                </ul>
              </section>
            )}

            {/* Payment Methods Section */}
            {payment_methods && payment_methods.length > 0 && (
              <section className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 className="font-bold text-gray-800 mb-4">Accepted Payment Methods</h3>
                <ul className="space-y-2">
                  {payment_methods.map((method) => (
                    <li key={method.id} className="flex items-center">
                      <span className="h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"></span>
                      <span>{method.name}</span>
                    </li>
                  ))}
                </ul>
              </section>
            )}

            {/* Contact Section */}
            {contactInfo && (contactInfo.phoneNumber || contactInfo.emailAddress || fullAddress) && (
              <section className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 className="font-bold text-gray-800 mb-4">Contact</h3>
                <div className="space-y-3">
                  {contactInfo.phoneNumber && (
                    <div>
                      <p className="text-gray-600 mb-1">Phone</p>
                      <a
                        href={`tel:${contactInfo.phoneNumber.replace(/[^\d+]/g, '')}`}
                        className="font-medium text-emerald-600 hover:text-emerald-700"
                      >
                        {contactInfo.phoneNumber}
                      </a>
                    </div>
                  )}
                  {contactInfo.emailAddress && (
                    <div>
                      <p className="text-gray-600 mb-1">Email</p>
                      <a
                        href={`mailto:${contactInfo.emailAddress}`}
                        className="font-medium text-emerald-600 hover:text-emerald-700 break-all"
                      >
                        {contactInfo.emailAddress}
                      </a>
                    </div>
                  )}
                  {address && ( // Check if address object exists
                    <div>
                      <p className="text-gray-600 mb-1">Address</p>
                      <p className="font-medium">
                        {address.streetAddress1 || address.streetAddress || ''} {/* Street part */}
                        <br /> {/* Line break */}
                        {`${address.city}, ${address.stateProvince} ${address.postalCode}`} {/* City, State Zip part */}
                      </p> {/* Added closing p tag */}
                    </div>
                  )}
                </div>
              </section>
            )}

            {/* Opening Hours Section (New Component) */}
            {openingHours && openingHours.length > 0 && (
              <OpeningHours hours={openingHours} />
            )}

            {/* Google Map Embed - Only render if place ID and API key exist */}
            {location?.googlePlaceId && process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY && (
              <section className="mb-6">
                <h3 className="font-bold text-gray-800 mb-4">Location Map</h3>
                <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                  <iframe
                    width="100%"
                    height="450" // Adjust height as needed
                    style={{ border: 0 }}
                    loading="lazy"
                    allowFullScreen
                    referrerPolicy="no-referrer-when-downgrade"
                    src={`https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&q=place_id:${location.googlePlaceId}`}>
                  </iframe>
                </div>
              </section>
            )}
            {/* End Google Map Embed */}
          </div>
        </div>
      </div>

      {/* Structured Data (JSON-LD) */}
      {structuredDataString && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: structuredDataString }}
        />
      )}
    </>
  );
}
