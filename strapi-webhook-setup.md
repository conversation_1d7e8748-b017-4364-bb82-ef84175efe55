# Strapi 5 Webhook Configuration for Next.js ISR

This guide explains how to set up Strapi 5 webhooks to trigger on-demand revalidation in your Next.js application.

## 1. Create Webhooks in Strapi Admin

1. Log in to your Strapi admin panel
2. Navigate to Settings > Webhooks
3. Click "Create new webhook"

## 2. Configure Webhooks for Clinics

Create a webhook with the following settings:

- **Name**: Revalidate Clinics
- **URL**: `https://your-nextjs-site.com/api/revalidate/clinics`
- **Headers**:
  - Key: `X-Revalidate-Secret`
  - Value: `your-secret-from-env-variables`
- **Events to send**:
  - Entry: Create, Update, Delete, Publish, Unpublish
  - Media: Create, Update, Delete
- **Collection Types**:
  - Select only `clinic` from the list

## 3. Configure Webhooks for Practitioners

Create another webhook with the following settings:

- **Name**: Revalidate Practitioners
- **URL**: `https://your-nextjs-site.com/api/revalidate/practitioners`
- **Headers**:
  - Key: `X-Revalidate-Secret`
  - Value: `your-secret-from-env-variables`
- **Events to send**:
  - Entry: Create, Update, Delete, Publish, Unpublish
  - Media: Create, Update, Delete
- **Collection Types**:
  - Select only `practitioner` from the list

## 4. Test the Webhooks

After setting up the webhooks, you can test them:

1. Make a change to a clinic or practitioner in Strapi
2. Check your Next.js server logs for revalidation messages
3. Verify that the changes appear on your site without a full rebuild

## 5. Troubleshooting

If revalidation isn't working:

1. Check that the `STRAPI_REVALIDATE_SECRET` environment variable is set correctly in your Next.js app
2. Verify that the webhook URL is correct and accessible
3. Check the Strapi webhook logs for any delivery failures
4. Look for error messages in your Next.js server logs
5. Try running the `test-revalidation.js` script to test the endpoints directly

## 6. Webhook Payload Structure

Strapi 5 webhook payloads have the following structure:

```json
{
  "event": "entry.update",
  "model": "api::clinic.clinic",
  "entry": {
    "id": 1,
    "slug": "example-clinic",
    // other fields...
  }
}
```

Make sure your revalidation endpoints can handle this payload format.

## 7. Environment Variables

Ensure these environment variables are set in your Next.js application:

```
STRAPI_REVALIDATE_SECRET=your-secret-here
NEXT_PUBLIC_API_URL=https://your-strapi-api.com
```

The same secret must be used in both your Next.js app and the Strapi webhook configuration.
