# Pre-Production Checklist for Natural Healing Now

## Automated Testing
- [x] All accessibility tests pass across browsers (Chrome, Firefox, Safari)
- [x] All accessibility tests pass across devices (Desktop, Mobile)
- [x] All functional tests pass across browsers
- [x] All functional tests pass across devices

## Manual Testing

### Core Functionality
- [ ] Homepage loads correctly with all sections visible
- [ ] Navigation to all main pages works
- [ ] Search functionality works with various queries
- [ ] Filtering on clinics/practitioners pages works
- [ ] Blog posts display correctly with images and formatting
- [ ] Clinic detail pages show all information correctly
- [ ] Practitioner detail pages show all information correctly

### Content
- [ ] All images have proper alt text
- [ ] No placeholder or test content remains
- [ ] No broken links or 404 errors
- [ ] Content is properly formatted on all screen sizes

### Performance
- [ ] Homepage loads in under 3 seconds on desktop
- [ ] Homepage loads in under 5 seconds on mobile
- [ ] Images are properly optimized
- [ ] Lazy loading works for below-fold content

### SEO
- [ ] All pages have proper meta titles and descriptions
- [ ] Structured data is implemented correctly
- [ ] Canonical URLs are set correctly
- [ ] Sitemap.xml is accessible and contains all pages

### Security
- [ ] API tokens are properly secured
- [ ] CORS settings are properly configured
- [ ] Rate limiting is in place for API endpoints
- [ ] No sensitive information is exposed in client-side code

### Browser Compatibility
- [ ] Site works in latest Chrome
- [ ] Site works in latest Firefox
- [ ] Site works in latest Safari
- [ ] Site works in latest Edge
- [ ] Site works on iOS Safari
- [ ] Site works on Android Chrome

### Deployment Preparation
- [ ] Environment variables are configured for production
- [ ] Build process completes successfully
- [ ] Strapi CMS is properly configured for production
- [ ] Database backups are in place
- [ ] Monitoring is set up

## Post-Deployment Verification

### Immediate Checks
- [ ] Site is accessible at the production URL
- [ ] SSL certificate is valid
- [ ] All pages load without errors
- [ ] Forms submit correctly
- [ ] Search works in production
- [ ] Analytics tracking is working

### Performance Monitoring
- [ ] Set up monitoring for page load times
- [ ] Set up monitoring for API response times
- [ ] Set up alerts for performance degradation

### Content Migration Verification
- [ ] All clinics are correctly migrated to production
- [ ] All practitioners are correctly migrated to production
- [ ] All blog posts are correctly migrated to production
- [ ] All images and media are correctly migrated to production

## Rollback Plan
1. Identify issue requiring rollback
2. Restore previous version of frontend deployment on Vercel
3. Restore database backup for Strapi if needed
4. Verify rollback was successful
5. Communicate status to stakeholders

## Notes
- Remember that content created during development is not automatically deployed to production and needs to be migrated separately
- The problematic clinic with ID 3 in production (ID 8 in development) should be checked specifically
