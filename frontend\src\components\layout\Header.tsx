"use client";

import Link from 'next/link';
import { useState } from 'react';
import { FiMenu, FiX, FiUser } from 'react-icons/fi';
import { useAuth } from '@/contexts/AuthContext';
import UserAccountDropdown from '@/components/auth/UserAccountDropdown';
import LazyImage from '@/components/shared/LazyImage'; // Import LazyImage component
import LinkWithPreload from '@/components/shared/LinkWithPreload'; // Import LinkWithPreload component
import { sanitizeUrl, getStrapiMediaPath } from '@/lib/mediaUtils';

// Define the type for the actual logoLight prop received from Strapi's API response
// (It's the media object itself, not nested under data/attributes when populated)
interface LogoLightMedia {
  id: number;
  name: string;
  alternativeText?: string | null;
  caption?: string | null;
  width?: number;
  height?: number;
  formats?: any; // Can be more specific if needed
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string | null;
  provider: string;
  provider_metadata?: any;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string; // publishedAt might not always be present
}

// Define props for the Header component
interface HeaderProps {
  siteName: string;
  logoLight: LogoLightMedia | null; // Use the corrected type
}

// Accept props in the component function signature
const Header = ({ siteName, logoLight }: HeaderProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, isLoading } = useAuth();

  // Prepare logo variables directly from the logoLight prop
  const logoUrl = logoLight?.url;
  const logoAlt = logoLight?.alternativeText || siteName; // Use siteName as fallback alt text
  const logoWidth = logoLight?.width;
  const logoHeight = logoLight?.height;

  // Using the imported sanitizeUrl function from mediaUtils

  // Get the Strapi API URL from environment variables
  const strapiApiUrl = process.env.NEXT_PUBLIC_API_URL || '';

  // Get the Strapi Media URL from environment variables (or derive it from API URL)
  const strapiMediaUrl = process.env.IMAGE_HOSTNAME ||
    (strapiApiUrl ? strapiApiUrl.replace('strapiapp.com', 'media.strapiapp.com') : '');

  // For Strapi Cloud, construct the media URL correctly
  let fullLogoUrl = '';

  if (logoUrl) {
    // Use the new getStrapiMediaPath function to properly handle the URL
    // This function will handle all the edge cases and prevent malformed URLs
    fullLogoUrl = getStrapiMediaPath(logoUrl);

    // Log the URL in development for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('Header Logo URL:', {
        original: logoUrl,
        processed: fullLogoUrl
      });
    }
  }

  // Log the URL in production for debugging
  if (process.env.NODE_ENV === 'production') {
    console.log('Logo URL:', {
      original: logoUrl,
      processed: fullLogoUrl
    });
  }

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Logo - Conditional Rendering */}
          <div className="flex-shrink-0">
            <Link href="/" className="inline-block align-middle"> {/* Remove text styles, apply conditionally or to image/span */}
              {logoUrl && logoWidth && logoHeight ? (
                <LazyImage // Use LazyImage for optimization
                  src={fullLogoUrl}
                  alt={logoAlt}
                  width={logoWidth} // Use actual width from Strapi
                  height={logoHeight} // Use actual height from Strapi
                  className="h-12 w-auto" // Constrain height, width adjusts automatically
                  aboveTheFold={true} // Logo is above the fold
                  showPlaceholder={false} // No placeholder for logo
                  priority // Prioritize loading the logo
                  unoptimized={process.env.NODE_ENV === 'production'} // Use unoptimized in production to avoid issues
                />
              ) : logoUrl ? ( // Fallback if width/height missing, use standard img
                 <img
                  src={fullLogoUrl}
                  alt={logoAlt}
                  className="h-12 w-auto" // Basic styling
                />
              ) : siteName ? (
                <span className="text-2xl font-bold text-emerald-600">{siteName}</span> // Apply text style here
              ) : (
                <span className="text-2xl font-bold text-emerald-600">My Directory Site</span> // Fallback with style
              )}
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center">
            <nav className="flex space-x-8 mr-8">
              <LinkWithPreload
                href="/"
                className="text-gray-700 hover:text-emerald-600"
                prefetchOnHover={true}
              >
                Home
              </LinkWithPreload>
              <LinkWithPreload
                href="/clinics"
                className="text-gray-700 hover:text-emerald-600"
                prefetchApiEndpoint="/clinics"
                prefetchApiParams={{
                  sort: 'name:asc',
                  pagination: { page: 1, pageSize: 12 },
                  populate: '*'
                }}
                prefetchOnHover={true}
              >
                Find a Clinic
              </LinkWithPreload>
              <LinkWithPreload
                href="/practitioners"
                className="text-gray-700 hover:text-emerald-600"
                prefetchApiEndpoint="/practitioners"
                prefetchApiParams={{
                  sort: 'name:asc',
                  pagination: { page: 1, pageSize: 12 },
                  populate: '*'
                }}
                prefetchOnHover={true}
              >
                Find a Practitioner
              </LinkWithPreload>
              <LinkWithPreload
                href="/categories"
                className="text-gray-700 hover:text-emerald-600"
                prefetchApiEndpoint="/categories"
                prefetchApiParams={{
                  sort: 'name:asc',
                  populate: '*'
                }}
                prefetchOnHover={true}
              >
                Categories
              </LinkWithPreload>
              <LinkWithPreload
                href="/blog"
                className="text-gray-700 hover:text-emerald-600"
                prefetchApiEndpoint="/blog-posts"
                prefetchApiParams={{
                  sort: 'publishDate:desc',
                  pagination: { page: 1, pageSize: 10 },
                  populate: {
                    featuredImage: true,
                    author_blogs: {
                      populate: {
                        profilePicture: true
                      }
                    }
                  }
                }}
                prefetchOnHover={true}
              >
                Blog
              </LinkWithPreload>
            </nav>

            {/* Authentication UI */}
            {!isLoading && (
              user ? (
                <UserAccountDropdown />
              ) : (
                <div className="flex items-center space-x-4">
                  <Link
                    href="/signin"
                    className="text-gray-700 hover:text-emerald-600 font-medium"
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/signup"
                    className="bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700"
                  >
                    Sign Up
                  </Link>
                </div>
              )
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="text-gray-700 hover:text-emerald-600 focus:outline-none"
              aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
            >
              {isMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 space-y-4 pb-4">
            <Link
              href="/"
              className="block text-gray-700 hover:text-emerald-600"
              onClick={() => setIsMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              href="/clinics"
              className="block text-gray-700 hover:text-emerald-600"
              onClick={() => setIsMenuOpen(false)}
            >
              Find a Clinic
            </Link>
            <Link
              href="/practitioners"
              className="block text-gray-700 hover:text-emerald-600"
              onClick={() => setIsMenuOpen(false)}
            >
              Find a Practitioner
            </Link>
            <Link
              href="/categories"
              className="block text-gray-700 hover:text-emerald-600"
              onClick={() => setIsMenuOpen(false)}
            >
              Categories
            </Link>
            <Link
              href="/blog"
              className="block text-gray-700 hover:text-emerald-600"
              onClick={() => setIsMenuOpen(false)}
            >
              Blog
            </Link>

            {/* Mobile Authentication UI */}
            {!isLoading && (
              user ? (
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <Link
                    href="/account"
                    className="flex items-center text-gray-700 hover:text-emerald-600"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <FiUser className="mr-2" />
                    My Account
                  </Link>
                  <button
                    onClick={() => {
                      setIsMenuOpen(false);
                      // Sign out is handled within the dropdown, but here we close the menu
                    }}
                    className="mt-2 flex items-center text-gray-700 hover:text-emerald-600"
                  >
                    <span className="text-red-600">Sign Out</span>
                  </button>
                </div>
              ) : (
                <div className="border-t border-gray-200 pt-4 mt-4 flex flex-col space-y-2">
                  <Link
                    href="/signin"
                    className="block text-gray-700 hover:text-emerald-600 font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/signup"
                    className="block text-emerald-600 hover:text-emerald-700 font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign Up
                  </Link>
                </div>
              )
            )}
          </nav>
        )}
      </div>
    </header>
  );
};

export default Header;
