import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';

// Interface for Strapi user
export interface StrapiUser {
  id: number;
  username: string;
  email: string;
  provider: string;
  confirmed: boolean;
  blocked: boolean;
  createdAt: string;
  updatedAt: string;
}

// Function to get the JWT token from cookies
export const getToken = () => {
  const cookieStore = cookies();
  return cookieStore.get('jwt')?.value;
};

// Function to verify the JWT token with Strapi
export const verifyToken = async (token: string) => {
  try {
    const response = await axios.get(`${API_URL}/api/users/me`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    
    return { user: response.data, error: null };
  } catch (error) {
    return { user: null, error };
  }
};

// Function to get the current user from the JWT token
export const getCurrentUser = async () => {
  const token = getToken();
  
  if (!token) {
    return { user: null };
  }
  
  const { user, error } = await verifyToken(token);
  
  if (error) {
    return { user: null };
  }
  
  return { user };
};

// Function to check if the user is authenticated
export const isAuthenticated = async () => {
  const { user } = await getCurrentUser();
  return !!user;
};

// Function to require authentication for a page
export const requireAuth = async (redirectUrl = '/signin') => {
  const isAuthed = await isAuthenticated();
  
  if (!isAuthed) {
    redirect(redirectUrl);
  }
};

// Function to create an authenticated API instance
export const createAuthenticatedServerAPI = () => {
  const token = getToken();
  
  return axios.create({
    baseURL: API_URL,
    headers: {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  });
};
