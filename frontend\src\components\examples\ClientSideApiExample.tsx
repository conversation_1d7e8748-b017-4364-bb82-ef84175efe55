'use client';

/**
 * Example client component that uses the client API utilities
 * This demonstrates how to fetch data from Strapi in a client component
 */
import { fetchFromApi } from '@/lib/apiUtils';
import { useState, useEffect } from 'react';

// Define the response type for blog posts
interface BlogPostsResponse {
  data: Array<{
    id: number;
    attributes: {
      title: string;
      slug: string;
      excerpt: string;
      publishDate: string;
      // Add other fields as needed
    };
  }>;
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export default function ClientSideApiExample() {
  const [posts, setPosts] = useState<BlogPostsResponse['data']>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setIsLoading(true);
        
        // Fetch blog posts from Strapi using the client API utility
        const response = await fetchFromApi<BlogPostsResponse>(
          '/blog-posts',
          {
            params: {
              sort: 'publishDate:desc',
              pagination: {
                page: 1,
                pageSize: 3,
              },
              populate: {
                featuredImage: true,
              },
              publicationState: 'live',
            },
          },
          false // Explicitly set to client-side
        );
        
        setPosts(response.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching blog posts:', err);
        setError('Failed to load blog posts. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPosts();
  }, []);

  if (isLoading) {
    return (
      <div className="animate-pulse p-4">
        <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 p-4 rounded-md border border-red-300">
        <p className="text-red-700">{error}</p>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
      <h2 className="text-xl font-semibold mb-4">Latest Blog Posts</h2>
      <p className="mb-4">
        This data was fetched on the client using the <code>fetchFromApi</code> utility.
      </p>
      
      <div className="space-y-4">
        {posts.map((post) => (
          <div key={post.id} className="bg-white p-3 rounded shadow-sm">
            <h3 className="font-medium">{post.attributes.title}</h3>
            <p className="text-sm text-gray-600">{post.attributes.excerpt}</p>
            <p className="text-xs text-gray-500 mt-2">
              Published: {new Date(post.attributes.publishDate).toLocaleDateString()}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
