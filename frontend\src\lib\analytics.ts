/**
 * Utility functions for analytics tracking
 */
import logger from './logger';

/**
 * Track a post view
 * @param postId The ID of the post
 * @param postSlug The slug of the post
 */
export async function trackPostView(postId: string, postSlug: string): Promise<void> {
  try {
    // Don't track views in development mode
    if (process.env.NODE_ENV === 'development') {
      logger.debug(`Post view tracked: ${postId} (${postSlug})`);
      return;
    }

    // Send the view to the API endpoint
    const response = await fetch('/api/analytics/post-view', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ postId, postSlug }),
    });

    if (!response.ok) {
      throw new Error(`Failed to track post view: ${response.statusText}`);
    }

  } catch (error) {
    // Don't let tracking errors affect the user experience
    logger.error('Error tracking post view:', error);
  }
}

/**
 * Get popular posts based on view count from Strapi
 * @param limit Number of popular posts to return
 * @param timeframe Timeframe to consider for popularity ('day', 'week', 'month', 'all')
 */
export async function getPopularPosts(limit: number = 4, timeframe: 'day' | 'week' | 'month' | 'all' = 'week'): Promise<any[]> {
  try {
    // Strapi API token for authentication - MUST be set in environment variables
    const strapiToken = process.env.STRAPI_API_TOKEN;

    if (!strapiToken) {
      logger.error('STRAPI_API_TOKEN environment variable is not set');
      return [];
    }

    // Strapi API URL
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';

    // Skip the popular posts endpoint for now since it's not working
    // Instead, directly fetch recent posts as a fallback
    logger.info('Fetching recent posts as fallback for popular posts');

    const recentPostsResponse = await fetch(
      `${strapiUrl}/api/blog-posts?sort=publishDate:desc&pagination[limit]=${limit}`,
      {
        headers: {
          'Authorization': `Bearer ${strapiToken}`
        },
        next: { revalidate: 3600 } // Cache for 1 hour
      }
    );

    if (!recentPostsResponse.ok) {
      throw new Error(`Failed to fetch recent posts: ${recentPostsResponse.statusText}`);
    }

    const recentData = await recentPostsResponse.json();

    if (recentData && recentData.data && Array.isArray(recentData.data) && recentData.data.length > 0) {
      // Map recent posts to the same format as popular posts
      return recentData.data.map((post: any) => {
        // Ensure post and post.attributes exist before accessing properties
        if (!post || !post.attributes) {
          logger.warn('Invalid post data structure:', post);
          return null;
        }

        const attributes = post.attributes;

        return {
          id: post.id,
          title: attributes.title || 'Untitled Post',
          slug: attributes.slug || `post-${post.id}`,
          excerpt: attributes.excerpt || '',
          featured_image: attributes.featuredImage?.data?.attributes?.url
            ? `${strapiUrl}${attributes.featuredImage.data.attributes.url}`
            : null,
          publish_date: attributes.publishDate || attributes.createdAt || new Date().toISOString(),
          view_count: 0,
          isFeatured: attributes.isFeatured || false
        };
      }).filter(Boolean); // Remove any null entries
    }

    // If we still don't have any posts, return an empty array
    logger.info('No posts found, returning empty array');
    return [];

  } catch (error) {
    logger.error('Error fetching posts:', error);
    // If all else fails, return an empty array
    return [];
  }
}
