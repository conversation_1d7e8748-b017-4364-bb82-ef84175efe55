/**
 * Import clinics from CSV file into Strapi
 * 
 * This script reads a CSV file and imports the data into the Strapi clinics collection.
 * If a clinic with the same slug already exists, it will only update fields that are empty.
 * If a clinic doesn't exist, it will create a new entry.
 * 
 * Usage: node scripts/import-clinics.js
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

// Path to the CSV file
const CSV_FILE_PATH = path.resolve(__dirname, '../Import-Strapi-directory-Sheet2.csv');

// Helper function to convert CSV string arrays to actual arrays
function parseArrayField(field) {
  if (!field) return [];
  return field.split(',').map(item => item.trim());
}

// Helper function to convert string boolean to actual boolean
function parseBoolean(value) {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') {
    return value.toLowerCase() === 'true';
  }
  return false;
}

// Helper function to check if a value is empty
function isEmpty(value) {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
}

// Main function to import clinics
async function importClinics(strapi) {
  const results = [];
  
  // Read CSV file
  return new Promise((resolve, reject) => {
    fs.createReadStream(CSV_FILE_PATH)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', async () => {
        console.log(`Read ${results.length} rows from CSV file`);
        
        let created = 0;
        let updated = 0;
        let skipped = 0;
        let errors = 0;
        
        // Process each row
        for (const row of results) {
          try {
            // Check if clinic with this slug already exists
            const slug = row.slug;
            if (!slug) {
              console.log('Skipping row without slug');
              skipped++;
              continue;
            }
            
            // Search for existing clinic with this slug
            const existingClinics = await strapi.entityService.findMany('api::clinic.clinic', {
              filters: { slug: slug },
              populate: ['contactInfo', 'address', 'location']
            });
            
            if (existingClinics && existingClinics.length > 0) {
              // Clinic exists, update only empty fields
              const existingClinic = existingClinics[0];
              const updateData = await prepareUpdateData(row, existingClinic);
              
              if (Object.keys(updateData).length > 0) {
                await strapi.entityService.update('api::clinic.clinic', existingClinic.id, {
                  data: updateData
                });
                console.log(`Updated clinic: ${slug}`);
                updated++;
              } else {
                console.log(`No updates needed for clinic: ${slug}`);
                skipped++;
              }
            } else {
              // Clinic doesn't exist, create new entry
              const createData = prepareCreateData(row);
              await strapi.entityService.create('api::clinic.clinic', {
                data: createData
              });
              console.log(`Created new clinic: ${slug}`);
              created++;
            }
          } catch (error) {
            console.error(`Error processing row with slug ${row.slug}:`, error.message);
            errors++;
          }
        }
        
        console.log('\nImport Summary:');
        console.log(`Created: ${created}`);
        console.log(`Updated: ${updated}`);
        console.log(`Skipped: ${skipped}`);
        console.log(`Errors: ${errors}`);
        console.log(`Total processed: ${created + updated + skipped + errors}`);
        
        resolve({ created, updated, skipped, errors });
      })
      .on('error', (error) => {
        console.error('Error reading CSV file:', error);
        reject(error);
      });
  });
}

// Prepare data for creating a new clinic
function prepareCreateData(row) {
  return {
    name: row.name,
    slug: row.slug,
    description: row.description,
    isActive: parseBoolean(row.isActive),
    isFeatured: parseBoolean(row.isFeatured),
    isVerified: parseBoolean(row.isVerified),
    paid: parseBoolean(row.paid),
    publishedAt: new Date(),
    appointmentOptions: parseArrayField(row.appointmentOptions),
    paymentMethod: parseArrayField(row.paymentMethod),
    videoEmbed: row.videoEmbedUrl ? { url: row.videoEmbedUrl } : null,
    
    // Components
    contactInfo: {
      phoneNumber: row.phoneNumber,
      emailAddress: row.emailAddress,
      websiteUrl: row.websiteUrl
    },
    address: {
      streetAddress1: row.streetAddress1,
      streetAddress2: row.streetAddress2,
      city: row.city,
      stateProvince: row.stateProvince,
      country: row.country || 'USA',
      postalCode: row.postalCode
    },
    location: {
      googlePlaceId: row.googlePlaceId,
      latitude: row.latitude ? parseFloat(row.latitude) : null,
      longitude: row.longitude ? parseFloat(row.longitude) : null
    }
  };
}

// Prepare data for updating an existing clinic
async function prepareUpdateData(row, existingClinic) {
  const updateData = {};
  
  // Only update fields that are empty in the existing data
  // Top-level fields
  if (!isEmpty(row.name) && isEmpty(existingClinic.name)) updateData.name = row.name;
  if (!isEmpty(row.description) && isEmpty(existingClinic.description)) updateData.description = row.description;
  if (row.isActive !== undefined && existingClinic.isActive === null) updateData.isActive = parseBoolean(row.isActive);
  if (row.isFeatured !== undefined && existingClinic.isFeatured === null) updateData.isFeatured = parseBoolean(row.isFeatured);
  if (row.isVerified !== undefined && existingClinic.isVerified === null) updateData.isVerified = parseBoolean(row.isVerified);
  if (row.paid !== undefined && existingClinic.paid === null) updateData.paid = parseBoolean(row.paid);
  
  // Multi-select fields
  if (!isEmpty(row.appointmentOptions) && isEmpty(existingClinic.appointmentOptions)) {
    updateData.appointmentOptions = parseArrayField(row.appointmentOptions);
  }
  if (!isEmpty(row.paymentMethod) && isEmpty(existingClinic.paymentMethod)) {
    updateData.paymentMethod = parseArrayField(row.paymentMethod);
  }
  
  // Video embed
  if (!isEmpty(row.videoEmbedUrl) && isEmpty(existingClinic.videoEmbed)) {
    updateData.videoEmbed = { url: row.videoEmbedUrl };
  }
  
  // Contact Info component
  const contactInfo = {};
  const existingContactInfo = existingClinic.contactInfo || {};
  
  if (!isEmpty(row.phoneNumber) && isEmpty(existingContactInfo.phoneNumber)) {
    contactInfo.phoneNumber = row.phoneNumber;
  }
  if (!isEmpty(row.emailAddress) && isEmpty(existingContactInfo.emailAddress)) {
    contactInfo.emailAddress = row.emailAddress;
  }
  if (!isEmpty(row.websiteUrl) && isEmpty(existingContactInfo.websiteUrl)) {
    contactInfo.websiteUrl = row.websiteUrl;
  }
  
  if (Object.keys(contactInfo).length > 0) {
    updateData.contactInfo = contactInfo;
  }
  
  // Address component
  const address = {};
  const existingAddress = existingClinic.address || {};
  
  if (!isEmpty(row.streetAddress1) && isEmpty(existingAddress.streetAddress1)) {
    address.streetAddress1 = row.streetAddress1;
  }
  if (!isEmpty(row.streetAddress2) && isEmpty(existingAddress.streetAddress2)) {
    address.streetAddress2 = row.streetAddress2;
  }
  if (!isEmpty(row.city) && isEmpty(existingAddress.city)) {
    address.city = row.city;
  }
  if (!isEmpty(row.stateProvince) && isEmpty(existingAddress.stateProvince)) {
    address.stateProvince = row.stateProvince;
  }
  if (!isEmpty(row.country) && isEmpty(existingAddress.country)) {
    address.country = row.country || 'USA';
  }
  if (!isEmpty(row.postalCode) && isEmpty(existingAddress.postalCode)) {
    address.postalCode = row.postalCode;
  }
  
  if (Object.keys(address).length > 0) {
    updateData.address = address;
  }
  
  // Location component
  const location = {};
  const existingLocation = existingClinic.location || {};
  
  if (!isEmpty(row.googlePlaceId) && isEmpty(existingLocation.googlePlaceId)) {
    location.googlePlaceId = row.googlePlaceId;
  }
  if (!isEmpty(row.latitude) && isEmpty(existingLocation.latitude)) {
    location.latitude = parseFloat(row.latitude);
  }
  if (!isEmpty(row.longitude) && isEmpty(existingLocation.longitude)) {
    location.longitude = parseFloat(row.longitude);
  }
  
  if (Object.keys(location).length > 0) {
    updateData.location = location;
  }
  
  return updateData;
}

module.exports = importClinics;
