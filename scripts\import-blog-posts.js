/**
 * Import blog posts from CSV file into Strapi
 * 
 * This script reads a CSV file and imports the data into the Strapi blog posts collection.
 * It also downloads featured images from URLs and uploads them to Strapi.
 * If a blog post with the same slug already exists, it will only update fields that are empty.
 * If a blog post doesn't exist, it will create a new entry.
 * 
 * Usage: 
 * 1. Start Strapi console: npm run strapi console
 * 2. Run: await require('../scripts/import-blog-posts.js')(strapi)
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const axios = require('axios');
const { createWriteStream } = require('fs');
const { promisify } = require('util');
const stream = require('stream');
const pipeline = promisify(stream.pipeline);

// Path to the CSV file
const CSV_FILE_PATH = path.resolve(__dirname, '../strapi-cms/NaturalHealingNow-Strapi-BlogPosts.csv');
const TEMP_IMAGE_DIR = path.resolve(__dirname, '../temp-images');

// Helper function to convert CSV string arrays to actual arrays
function parseArrayField(field) {
  if (!field) return [];
  return field.split('|||||').map(item => item.trim()).filter(Boolean);
}

// Helper function to download an image from a URL
async function downloadImage(url, outputPath) {
  try {
    const response = await axios({
      method: 'GET',
      url: url,
      responseType: 'stream',
    });

    // Ensure the directory exists
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    await pipeline(response.data, createWriteStream(outputPath));
    console.log(`Image downloaded to ${outputPath}`);
    return outputPath;
  } catch (error) {
    console.error(`Error downloading image from ${url}:`, error.message);
    return null;
  }
}

// Helper function to upload an image to Strapi
async function uploadImageToStrapi(strapi, imagePath, altText) {
  try {
    // Read the file
    const fileData = fs.readFileSync(imagePath);
    const stats = fs.statSync(imagePath);
    const fileName = path.basename(imagePath);
    const mimeType = fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') 
      ? 'image/jpeg' 
      : fileName.endsWith('.png') 
        ? 'image/png' 
        : 'application/octet-stream';

    // Create a file entity
    const fileEntity = {
      name: fileName,
      hash: path.parse(fileName).name,
      ext: path.extname(fileName),
      mime: mimeType,
      size: stats.size / 1000, // Convert to KB
      provider: 'local',
      buffer: fileData,
      alternativeText: altText || fileName,
      caption: altText || fileName,
    };

    // Upload the file using Strapi's upload service
    const uploadedFiles = await strapi.plugins.upload.services.upload.upload({
      data: {
        fileInfo: {
          alternativeText: altText || fileName,
          caption: altText || fileName,
        }
      },
      files: {
        path: imagePath,
        name: fileName,
        type: mimeType,
        size: stats.size,
      }
    });

    return uploadedFiles[0].id;
  } catch (error) {
    console.error(`Error uploading image to Strapi:`, error);
    return null;
  }
}

// Main function to import blog posts
module.exports = async (strapi) => {
  console.log('Starting blog posts import from CSV...');
  
  try {
    // Install dependencies if not already installed
    try {
      require('csv-parser');
      require('axios');
    } catch (e) {
      console.log('Installing dependencies...');
      const { execSync } = require('child_process');
      execSync('npm install csv-parser axios', { stdio: 'inherit' });
      console.log('Dependencies installed successfully.');
    }
    
    const results = [];
    
    // Read CSV file
    return new Promise((resolve, reject) => {
      fs.createReadStream(CSV_FILE_PATH)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', async () => {
          console.log(`Read ${results.length} rows from CSV file`);
          
          let created = 0;
          let updated = 0;
          let skipped = 0;
          let errors = 0;
          
          // Process each row
          for (const row of results) {
            try {
              // Check if blog post with this slug already exists
              const slug = row.slug;
              if (!slug) {
                console.log('Skipping row without slug');
                skipped++;
                continue;
              }
              
              // Search for existing blog post with this slug
              const existingPosts = await strapi.entityService.findMany('api::blog-post.blog-post', {
                filters: { slug: slug },
                populate: ['featuredImage', 'blog_categories', 'author_blogs', 'blog_tags']
              });
              
              // Find or create blog categories
              let categoryIds = [];
              if (row.blog_categories) {
                const categoryName = row.blog_categories.trim();
                if (categoryName) {
                  const existingCategories = await strapi.entityService.findMany('api::blog-category.blog-category', {
                    filters: { name: categoryName }
                  });
                  
                  if (existingCategories && existingCategories.length > 0) {
                    categoryIds.push(existingCategories[0].id);
                  } else {
                    // Create new category
                    const newCategory = await strapi.entityService.create('api::blog-category.blog-category', {
                      data: {
                        name: categoryName,
                        slug: categoryName.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
                        publishedAt: new Date()
                      }
                    });
                    categoryIds.push(newCategory.id);
                    console.log(`Created new category: ${categoryName}`);
                  }
                }
              }
              
              // Find author
              let authorIds = [];
              if (row.author_blogs) {
                const authorName = row.author_blogs.trim();
                if (authorName) {
                  const existingAuthors = await strapi.entityService.findMany('api::author.author', {
                    filters: { name: authorName }
                  });
                  
                  if (existingAuthors && existingAuthors.length > 0) {
                    authorIds.push(existingAuthors[0].id);
                  } else {
                    console.log(`Author not found: ${authorName}, using existing authors if available`);
                    // Get any author if available
                    const anyAuthors = await strapi.entityService.findMany('api::author.author', {
                      sort: { id: 'asc' },
                      pagination: { limit: 1 }
                    });
                    
                    if (anyAuthors && anyAuthors.length > 0) {
                      authorIds.push(anyAuthors[0].id);
                    }
                  }
                }
              }
              
              // Process tags from Alternative text field
              let tagIds = [];
              if (row['Alternative text']) {
                const tagNames = parseArrayField(row['Alternative text']);
                for (const tagName of tagNames) {
                  if (tagName) {
                    const existingTags = await strapi.entityService.findMany('api::blog-tag.blog-tag', {
                      filters: { name: tagName }
                    });
                    
                    if (existingTags && existingTags.length > 0) {
                      tagIds.push(existingTags[0].id);
                    } else {
                      // Create new tag
                      const newTag = await strapi.entityService.create('api::blog-tag.blog-tag', {
                        data: {
                          name: tagName,
                          slug: tagName.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
                          publishedAt: new Date()
                        }
                      });
                      tagIds.push(newTag.id);
                      console.log(`Created new tag: ${tagName}`);
                    }
                  }
                }
              }
              
              // Download and upload featured image if URL is provided
              let featuredImageId = null;
              if (row.featuredImage) {
                const imageUrl = row.featuredImage.trim();
                if (imageUrl) {
                  const imageName = path.basename(imageUrl);
                  const tempImagePath = path.join(TEMP_IMAGE_DIR, imageName);
                  
                  // Download the image
                  const downloadedImagePath = await downloadImage(imageUrl, tempImagePath);
                  if (downloadedImagePath) {
                    // Upload to Strapi
                    featuredImageId = await uploadImageToStrapi(strapi, downloadedImagePath, row['Alternative text'] || row.title);
                    
                    // Clean up temp file
                    try {
                      fs.unlinkSync(downloadedImagePath);
                    } catch (e) {
                      console.warn(`Could not delete temp file ${downloadedImagePath}:`, e.message);
                    }
                  }
                }
              }
              
              if (existingPosts && existingPosts.length > 0) {
                // Blog post exists, update only empty fields
                const existingPost = existingPosts[0];
                const updateData = {};
                
                // Only update title if empty
                if (!existingPost.title && row.title) {
                  updateData.title = row.title;
                }
                
                // Only update content if empty
                if (!existingPost.content && row.content) {
                  updateData.content = row.content;
                }
                
                // Only update featured image if empty
                if ((!existingPost.featuredImage || !existingPost.featuredImage.id) && featuredImageId) {
                  updateData.featuredImage = featuredImageId;
                }
                
                // Only update categories if empty
                if ((!existingPost.blog_categories || existingPost.blog_categories.length === 0) && categoryIds.length > 0) {
                  updateData.blog_categories = categoryIds;
                }
                
                // Only update authors if empty
                if ((!existingPost.author_blogs || existingPost.author_blogs.length === 0) && authorIds.length > 0) {
                  updateData.author_blogs = authorIds;
                }
                
                // Only update tags if empty
                if ((!existingPost.blog_tags || existingPost.blog_tags.length === 0) && tagIds.length > 0) {
                  updateData.blog_tags = tagIds;
                }
                
                // Only update if there are fields to update
                if (Object.keys(updateData).length > 0) {
                  await strapi.entityService.update('api::blog-post.blog-post', existingPost.id, {
                    data: updateData
                  });
                  console.log(`Updated blog post: ${slug}`);
                  updated++;
                } else {
                  console.log(`No updates needed for blog post: ${slug}`);
                  skipped++;
                }
              } else {
                // Blog post doesn't exist, create new entry
                const createData = {
                  title: row.title,
                  slug: slug,
                  content: row.content,
                  publishDate: new Date(),
                  publishedAt: new Date()
                };
                
                // Add featured image if available
                if (featuredImageId) {
                  createData.featuredImage = featuredImageId;
                }
                
                // Add categories if available
                if (categoryIds.length > 0) {
                  createData.blog_categories = categoryIds;
                }
                
                // Add authors if available
                if (authorIds.length > 0) {
                  createData.author_blogs = authorIds;
                }
                
                // Add tags if available
                if (tagIds.length > 0) {
                  createData.blog_tags = tagIds;
                }
                
                await strapi.entityService.create('api::blog-post.blog-post', {
                  data: createData
                });
                console.log(`Created new blog post: ${slug}`);
                created++;
              }
            } catch (error) {
              console.error(`Error processing row with slug ${row.slug}:`, error);
              errors++;
            }
          }
          
          // Clean up temp directory
          try {
            if (fs.existsSync(TEMP_IMAGE_DIR)) {
              fs.rmdirSync(TEMP_IMAGE_DIR, { recursive: true });
            }
          } catch (e) {
            console.warn(`Could not clean up temp directory:`, e.message);
          }
          
          console.log(`Import completed: ${created} created, ${updated} updated, ${skipped} skipped, ${errors} errors`);
          resolve({ created, updated, skipped, errors });
        })
        .on('error', (error) => {
          console.error('Error reading CSV file:', error);
          reject(error);
        });
    });
  } catch (error) {
    console.error('Import failed:', error);
    throw error;
  }
};
