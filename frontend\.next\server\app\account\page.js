(()=>{var e={};e.id=1298,e.ids=[1298],e.modules={100:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(65239),n=t(48088),o=t(31369),i=t(30893),a={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>i[e]);t.d(r,a);let l={children:["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,11504)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\account\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\account\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11504:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\account\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\account\\page.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},17065:(e,r,t)=>{Promise.resolve().then(t.bind(t,72817))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26793:(e,r,t)=>{Promise.resolve().then(t.bind(t,11504))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72817:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(60687),n=t(16189),o=t(6363),i=t(28351);function a({children:e,redirectTo:r="/signin"}){let{isAuthenticated:t,isLoading:i}=(0,o.A)();return((0,n.useRouter)(),i)?(0,s.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,s.jsx)("div",{className:"animate-pulse text-lg",children:"Loading..."})}):t?(0,s.jsx)(s.Fragment,{children:e}):null}function l(){let{user:e}=(0,o.A)(),r=(0,n.useRouter)();return(0,s.jsx)(a,{children:(0,s.jsx)(i.default,{children:(0,s.jsx)("div",{className:"container mx-auto py-12 px-4",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6 text-gray-800",children:"My Account"}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Account Information"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Email"}),(0,s.jsx)("p",{className:"font-medium",children:e?.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Username"}),(0,s.jsx)("p",{className:"font-medium",children:e?.username})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Account ID"}),(0,s.jsx)("p",{className:"font-medium",children:e?.id})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Account Settings"}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsx)("button",{className:"px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2",onClick:()=>r.push("/forgot-password"),children:"Change Password"})})]})]})})})})}t(43210)},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,1330,3376,6391,2975,8446,270],()=>t(100));module.exports=s})();