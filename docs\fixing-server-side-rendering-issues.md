# Fixing Server-Side Rendering Issues in Natural Healing Now

This document outlines the steps to fix the server-side rendering issues in the Natural Healing Now website.

## Current Issues

Based on the API logs, we've identified the following issues:

1. **Pages are being server-side rendered when they should be served from cache**:
   ```
   May 12 14:42:04.58 GET 200 www.naturalhealingnow.com /blog
   May 12 14:38:04.21 GET 200 www.naturalhealingnow.com /practitioners/dr-veena-dzik
   ```

2. **Duplicate API requests are being made for the same endpoints**:
   ```
   [2025-05-12 14:38:04] [2025-05-12 14:38:04.826] http: GET /api/global-setting?populate=*&publicationState=live (99 ms) 200
   [2025-05-12 14:38:04] [2025-05-12 14:38:04.921] http: GET /api/global-setting?populate=*&publicationState=live (155 ms) 200
   ```

3. **Webhook configuration issues**:
   - Outdated webhook URL: `https://naturalhealingnow-directory-2hysxhy6h.vercel.app/api/revalidate`
   - Token sent as header instead of in request body
   - Missing content type information

## Root Causes

1. **Inconsistent ISR Configuration**: Some pages use `revalidate = false` (on-demand revalidation only), while others use `revalidate = 3600` (time-based revalidation every hour).

2. **Webhook Configuration Issues**: The Strapi webhook is not properly configured to trigger revalidation when content changes.

3. **Inefficient Data Fetching**: Components are making redundant API calls or missing cache configuration.

## Solution

### 1. Fix the Revalidation API

We've enhanced the revalidation API to:
- Accept tokens in both the request body and headers
- Provide more detailed logging for debugging
- Handle errors more gracefully

### 2. Update Strapi Webhook Configuration

Use the provided script to configure Strapi webhooks correctly:

```bash
node scripts/configure-strapi-webhooks.js
```

This script will:
- Update the webhook URL to `https://www.naturalhealingnow.com/api/revalidate`
- Configure the webhook to send the token in the request body
- Ensure the webhook includes the necessary content type information

### 3. Test the Revalidation API

Use the provided script to test the revalidation API:

```bash
node scripts/test-revalidation.js
```

This script will:
- Test the revalidation API with different payload formats
- Verify that the revalidation API is working correctly
- Provide detailed logs for debugging

### 4. Implement the Recommended ISR Strategy

Follow the recommendations in the `docs/isr-strategy-recommendation.md` document to:
- Set `revalidate = false` for all dynamic content pages
- Set `revalidate = 3600` for all list pages
- Optimize data fetching to reduce duplicate API requests

## Step-by-Step Implementation Guide

### 1. Fix the Revalidation API (Already Done)

The revalidation API has been enhanced to:
- Accept tokens in both the request body and headers
- Provide more detailed logging for debugging
- Handle errors more gracefully

### 2. Update Strapi Webhook Configuration

1. Run the webhook configuration script:
   ```bash
   node scripts/configure-strapi-webhooks.js
   ```

2. Verify that the webhooks are configured correctly in the Strapi admin panel:
   - Go to Settings > Webhooks
   - Check that the webhook URL is `https://www.naturalhealingnow.com/api/revalidate`
   - Check that the webhook is configured to send the token in the request body
   - Check that the webhook includes the necessary content type information

### 3. Test the Revalidation API

1. Run the revalidation test script:
   ```bash
   node scripts/test-revalidation.js
   ```

2. Verify that the revalidation API is working correctly:
   - Check that the revalidation API returns a 200 status code
   - Check that the revalidation API returns a JSON response with `revalidated: true`
   - Check the Vercel logs to ensure the revalidation API is being called

### 4. Update ISR Configuration

1. For dynamic content pages, set `revalidate = false`:
   - Blog posts (`/blog/[slug]`)
   - Blog category pages (`/blog/categories/[slug]`)
   - Clinic pages (`/clinics/[slug]`)
   - Practitioner pages (`/practitioners/[slug]`)
   - Specialty pages (`/specialities/[slug]`)
   - Condition pages (`/conditions/[slug]`)
   - Homepage

2. For list pages, set `revalidate = 3600`:
   - Blog list page (`/blog`)
   - Clinics list page (`/clinics`)
   - Practitioners list page (`/practitioners`)
   - Categories list page (`/categories`)
   - Specialties list page (`/specialities`)

### 5. Optimize Data Fetching

1. Review and update React Query configuration:
   - Set `staleTime: Infinity` for all queries
   - Set `gcTime: 7 * 24 * 60 * 60 * 1000` (7 days) for all queries

2. Implement server-side caching for all data fetching functions:
   ```typescript
   export const getClinicBySlug = cache(async (slug: string) => {
     return fetchContentType('clinics', {
       params: {
         filters: { slug: { $eq: slug } },
         populate: '*',
       },
     });
   });
   ```

3. Use Next.js cache tags for all fetch requests:
   ```typescript
   const data = await fetch(`https://api.example.com/data`, {
     next: { tags: ['data'] },
   });
   ```

## Verification

After implementing these changes, verify that:

1. Pages are being served from cache instead of server-side rendered:
   - Check the Vercel logs to ensure pages are not being server-side rendered
   - Check the network tab in the browser to ensure pages are being served from cache

2. Duplicate API requests are eliminated:
   - Check the Strapi logs to ensure there are no duplicate API requests
   - Check the network tab in the browser to ensure there are no duplicate API requests

3. Webhooks are triggering revalidation:
   - Make a change to a content item in Strapi
   - Check the Strapi webhook logs to ensure the webhook was triggered
   - Check the Vercel logs to ensure the revalidation API was called
   - Check that the page was revalidated

## Conclusion

By implementing these changes, you will:

1. **Improve Performance**: Pages will be served from the CDN most of the time, reducing server load and improving response times.

2. **Reduce Costs**: Fewer server-side renders means lower Vercel compute costs.

3. **Enhance Reliability**: Even if webhooks fail, pages will still be updated eventually thanks to the fallback revalidation period.

4. **Optimize Data Fetching**: Proper caching will reduce the number of API requests, further improving performance and reducing costs.
