import { test, expect } from '@playwright/test';

test.describe('Clinics Page', () => {
  test('should load the clinics page successfully', async ({ page }) => {
    await page.goto('/clinics');
    
    // Check that the page title is correct
    await expect(page).toHaveTitle(/Clinics/);
    
    // Check that main sections are visible
    await expect(page.getByRole('heading', { name: /Clinics/i })).toBeVisible();
  });

  test('should filter clinics by location', async ({ page }) => {
    await page.goto('/clinics');
    
    // Check if there's a location filter
    const locationFilter = page.getByText(/Filter by Location/i);
    if (await locationFilter.count() > 0) {
      // Click on the location filter dropdown
      await locationFilter.click();
      
      // Select the first location option
      const locationOptions = page.locator('li, option').filter({ hasText: /./ });
      if (await locationOptions.count() > 0) {
        await locationOptions.first().click();
        
        // Check that the filter has been applied
        await expect(page.getByText(/Filtered by/i)).toBeVisible();
      } else {
        test.skip('No location options found to test');
      }
    } else {
      test.skip('No location filter found to test');
    }
  });

  test('should navigate to a clinic detail page', async ({ page }) => {
    await page.goto('/clinics');
    
    // Click on the first clinic card
    const clinicCards = page.locator('.clinic-card, [data-testid="clinic-card"]').first();
    if (await clinicCards.count() > 0) {
      await clinicCards.click();
      
      // Check that we've navigated to the clinic detail page
      await expect(page).toHaveURL(/\/clinics\/.+/);
      
      // Check that the clinic details are displayed
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
    } else {
      test.skip('No clinic cards found to test');
    }
  });

  test('should search for clinics', async ({ page }) => {
    await page.goto('/clinics');
    
    // Fill the search input
    await page.getByPlaceholder(/Search/i).fill('wellness');
    
    // Press Enter to submit the search
    await page.keyboard.press('Enter');
    
    // Check that search results are displayed
    await expect(page.getByText(/Search Results/i)).toBeVisible();
  });
});
