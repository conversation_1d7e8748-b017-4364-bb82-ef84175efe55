/**
 * Utilities for batching multiple API requests to Strapi
 * This reduces the number of separate HTTP requests
 */
import axios, { AxiosRequestConfig } from 'axios';
import { fetchFromClient } from './clientFetch';
import { QueryClient } from '@tanstack/react-query'; // Import QueryClient
import { CACHE_TIMES } from './reactQuery'; // Import CACHE_TIMES

// Get Strapi URL from environment variable
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1337';
const API_PATH = '/api';

/**
 * Interface for a batch request item
 */
interface BatchRequestItem {
  endpoint: string;
  params?: Record<string, any>;
}

/**
 * Interface for a batch response item
 */
interface BatchResponseItem<T> {
  data: T;
  status: number;
}

/**
 * Batch multiple API requests into a single request using Promise.all
 * This is a simple approach that still makes multiple HTTP requests but in parallel
 *
 * @param requests Array of request objects with endpoint and params
 * @returns Promise with an array of responses
 */
export async function batchRequestsParallel<T>(
  requests: BatchRequestItem[]
): Promise<T[]> {
  return Promise.all(
    requests.map(({ endpoint, params }) => {
      const options: AxiosRequestConfig = {};
      if (params) {
        options.params = params;
      }

      return fetchFromClient<T>(endpoint, options);
    })
  );
}

/**
 * Create a custom batch endpoint request
 * This requires a custom endpoint on your Strapi backend that can handle batch requests
 *
 * @param requests Array of request objects with endpoint and params
 * @returns Promise with an array of responses
 */
export async function batchRequestsCustomEndpoint<T>(
  requests: BatchRequestItem[]
): Promise<T[]> {
  try {
    // Make a single request to a custom batch endpoint
    const response = await axios.post<BatchResponseItem<T>[]>(
      `${API_URL}${API_PATH}/batch`,
      { requests },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    // Extract the data from each response item
    return response.data.map(item => item.data);
  } catch (error: any) {
    console.error('Error making batch request:', error);

    if (error.response) {
      console.error('Response status:', error.response.status);
    }

    throw error;
  }
}

/**
 * Group requests by content type to reduce the number of API calls
 * This combines multiple requests for the same content type into a single request with filters
 *
 * @param requests Array of request objects for the same content type
 * @returns Promise with the combined response
 */
export async function batchRequestsByContentType<T>(
  contentType: string,
  ids: (string | number)[],
  fields: string[] = [],
  populate: Record<string, any> = {}
): Promise<T[]> {
  try {
    // Build a single request with an $in filter
    const params: Record<string, any> = {
      filters: {
        id: {
          $in: ids,
        },
      },
    };

    // Add fields if specified
    if (fields.length > 0) {
      params.fields = fields;
    }

    // Add populate if specified
    if (Object.keys(populate).length > 0) {
      params.populate = populate;
    }

    // Make a single request
    const response = await fetchFromClient<{ data: T[] }>(`/${contentType}`, { params });

    return response.data;
  } catch (error: any) {
    console.error(`Error making batch request for ${contentType}:`, error);

    if (error.response) {
      console.error('Response status:', error.response.status);
    }

    throw error;
  }
}

/**
 * Prefetch common data that's needed across multiple pages
 * This reduces the number of API calls when navigating between pages
 *
 * @param queryClient React Query client instance
 */
export async function prefetchCommonData(queryClient: QueryClient): Promise<void> {
  const commonDataToPrefetch = [
    {
      queryKey: ['global-setting'],
      endpoint: '/global-setting',
      params: { populate: '*' },
      staleTime: CACHE_TIMES.global.staleTime,
    },
    {
      queryKey: ['categories', { pagination: { pageSize: 10 }, fields: ['name', 'slug'] }],
      endpoint: '/categories',
      params: { pagination: { pageSize: 10 }, fields: ['name', 'slug'] },
      staleTime: CACHE_TIMES.categories.staleTime,
    },
    {
      queryKey: ['specialties', { pagination: { pageSize: 10 }, fields: ['name', 'slug'] }],
      endpoint: '/specialties',
      params: { pagination: { pageSize: 10 }, fields: ['name', 'slug'] },
      staleTime: CACHE_TIMES.specialties.staleTime,
    },
  ];

  const prefetchPromises = commonDataToPrefetch.map(async (item) => {
    const existingData = queryClient.getQueryData(item.queryKey);
    if (existingData) {
      // Data already exists, no need to prefetch again if staleTime is Infinity
      // or if not stale based on a more complex logic if staleTime wasn't Infinity.
      // For Infinity staleTime, existence is enough.
      if (item.staleTime === Infinity) {
        console.log(`Data for ${item.endpoint} already in cache, skipping prefetch.`);
        return;
      }
      // Add more sophisticated staleness check here if needed for non-Infinity staleTimes
    }

    try {
      await queryClient.prefetchQuery({
        queryKey: item.queryKey,
        queryFn: () => fetchFromClient<unknown>(item.endpoint, { params: item.params }),
        staleTime: item.staleTime,
      });
      console.log(`Prefetched data for ${item.endpoint}`);
    } catch (error) {
      console.error(`Error prefetching data for ${item.endpoint}:`, error);
      // Silently fail for individual prefetches
    }
  });

  try {
    await Promise.all(prefetchPromises);
  } catch (error) {
    // This catch is mostly for Promise.all itself, individual errors are handled above
    console.error('Error during batch prefetching of common data:', error);
    // Silently fail - this is just prefetching
  }
}
