import Link from 'next/link';
import { FiArrowLeft } from 'react-icons/fi';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-sm p-8 text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">Category Not Found</h1>
          <p className="text-gray-600 mb-8">
            Sorry, we couldn't find the blog category you're looking for. It may have been removed or renamed.
          </p>
          
          <div className="flex flex-col items-center gap-4">
            <Link
              href="/blog"
              className="text-emerald-600 hover:text-emerald-700 flex items-center"
            >
              <FiArrowLeft className="mr-2" /> Back to Blog
            </Link>
            
            <Link
              href="/"
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-md transition-colors"
            >
              Go to Homepage
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
