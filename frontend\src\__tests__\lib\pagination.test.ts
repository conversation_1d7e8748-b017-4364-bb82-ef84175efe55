import { 
  calculatePagination, 
  generatePageNumbers 
} from '@/lib/pagination';

describe('Pagination Utilities', () => {
  describe('calculatePagination', () => {
    it('should calculate correct pagination values', () => {
      const result = calculatePagination({
        totalItems: 100,
        currentPage: 2,
        pageSize: 10,
      });
      
      expect(result).toEqual({
        totalItems: 100,
        currentPage: 2,
        pageSize: 10,
        totalPages: 10,
        startIndex: 10,
        endIndex: 19,
        startPage: 1,
        endPage: 10,
        pages: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      });
    });

    it('should handle first page correctly', () => {
      const result = calculatePagination({
        totalItems: 100,
        currentPage: 1,
        pageSize: 10,
      });
      
      expect(result).toEqual({
        totalItems: 100,
        currentPage: 1,
        pageSize: 10,
        totalPages: 10,
        startIndex: 0,
        endIndex: 9,
        startPage: 1,
        endPage: 10,
        pages: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      });
    });

    it('should handle last page correctly', () => {
      const result = calculatePagination({
        totalItems: 100,
        currentPage: 10,
        pageSize: 10,
      });
      
      expect(result).toEqual({
        totalItems: 100,
        currentPage: 10,
        pageSize: 10,
        totalPages: 10,
        startIndex: 90,
        endIndex: 99,
        startPage: 1,
        endPage: 10,
        pages: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      });
    });

    it('should handle empty data correctly', () => {
      const result = calculatePagination({
        totalItems: 0,
        currentPage: 1,
        pageSize: 10,
      });
      
      expect(result).toEqual({
        totalItems: 0,
        currentPage: 1,
        pageSize: 10,
        totalPages: 0,
        startIndex: 0,
        endIndex: 0,
        startPage: 1,
        endPage: 1,
        pages: [1],
      });
    });
  });

  describe('generatePageNumbers', () => {
    it('should generate page numbers with ellipsis for many pages', () => {
      const result = generatePageNumbers(10, 5);
      
      expect(result).toEqual([1, 2, 3, 4, 5, 6, 7, '...', 10]);
    });

    it('should generate page numbers with ellipsis at the beginning', () => {
      const result = generatePageNumbers(10, 8);
      
      expect(result).toEqual([1, '...', 6, 7, 8, 9, 10]);
    });

    it('should generate page numbers with ellipsis at both ends', () => {
      const result = generatePageNumbers(20, 10);
      
      expect(result).toEqual([1, '...', 8, 9, 10, 11, 12, '...', 20]);
    });

    it('should generate all page numbers for few pages', () => {
      const result = generatePageNumbers(5, 3);
      
      expect(result).toEqual([1, 2, 3, 4, 5]);
    });

    it('should handle edge case with current page at the beginning', () => {
      const result = generatePageNumbers(10, 1);
      
      expect(result).toEqual([1, 2, 3, '...', 10]);
    });

    it('should handle edge case with current page at the end', () => {
      const result = generatePageNumbers(10, 10);
      
      expect(result).toEqual([1, '...', 8, 9, 10]);
    });
  });
});
