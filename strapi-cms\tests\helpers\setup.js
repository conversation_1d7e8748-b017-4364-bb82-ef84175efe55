// This file runs before each test file
jest.setTimeout(30000); // Increase timeout for API operations

// Import the mock Strapi instance
const mockStrapi = require('./strapi-instance');

// Make the mock Strapi instance globally available
global.strapi = mockStrapi;

// We're using real Strapi data, so we don't need to mock these anymore
// Uncomment if you want to use mocks for specific tests
/*
jest.mock('supertest', () => {
  const mockResponse = {
    status: 200,
    body: {
      jwt: 'mock-jwt-token',
      user: {
        id: 1,
        username: 'tester',
        email: '<EMAIL>',
      },
      data: {
        id: 1,
        attributes: {
          name: 'Test Clinic',
          description: 'Updated description',
        },
      },
    },
  };

  return jest.fn().mockImplementation(() => ({
    post: jest.fn().mockReturnThis(),
    get: jest.fn().mockReturnThis(),
    put: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    send: jest.fn().mockResolvedValue(mockResponse),
  }));
});

jest.mock('jsonwebtoken', () => ({
  sign: jest.fn().mockReturnValue('mock-jwt-token'),
}));
*/

// Keep console logs for debugging
// Uncomment to silence logs during tests
/*
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
*/
