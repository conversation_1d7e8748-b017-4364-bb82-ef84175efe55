"use client";

import Link from 'next/link';
import { FiTag } from 'react-icons/fi';

interface TagCloudProps {
  tags: string[];
}

const TagCloud = ({ tags }: TagCloudProps) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        <FiTag className="mr-2 text-emerald-600" />
        Popular Topics
      </h3>
      <div className="flex flex-wrap gap-2">
        {tags.map(tag => (
          <Link
            key={tag}
            href={`/blog/tags/${tag.toLowerCase().replace(/\s+/g, '-')}`}
            className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 transition-colors"
          >
            {tag}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default TagCloud;
