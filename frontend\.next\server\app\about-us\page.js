(()=>{var e={};e.id=6851,e.ids=[6851],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12045:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\MarkdownContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\MarkdownContent.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40921:(e,t,r)=>{Promise.resolve().then(r.bind(r,12045))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60874:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>u,routeModule:()=>c,tree:()=>l});var o=r(65239),s=r(48088),n=r(31369),a=r(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);r.d(t,i);let l={children:["",{children:["about-us",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74567)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\about-us\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\about-us\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/about-us/page",pathname:"/about-us",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},74567:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,generateMetadata:()=>i});var o=r(37413),s=r(58446),n=r(12045),a=r(39916);async function i(){try{let e=await s.$.aboutUs.get(),t=e?.data;if(!t)return{title:"About Us",description:"Learn more about our mission and team."};let r=t?.seo,o=r?.metaTitle||t?.title||"About Us",n=r?.metaDescription||"Learn more about our mission and team.";return{title:o,description:n,...r?.canonicalURL&&{alternates:{canonical:r.canonicalURL}},...r?.metaRobots&&{robots:r.metaRobots}}}catch(e){return console.error("Error fetching About Us metadata:",e),{title:"About Us",description:"Error loading page information."}}}async function l(){let e=null;try{let t=await s.$.aboutUs.get();e=t?.data}catch(e){console.error("Failed to fetch About Us page data:",e)}e||(console.error("About Us data object is null or undefined after fetch."),(0,a.notFound)()),e.content||console.error("About Us data fetched, but content field is missing or empty."),e.seo?.metaTitle||e.title;let t=e.content;return(0,o.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,o.jsx)("div",{className:"prose lg:prose-xl max-w-none mx-auto",children:t?(0,o.jsx)(n.default,{content:t}):(0,o.jsx)("p",{children:"About us content is not available at the moment."})})})}},76760:e=>{"use strict";e.exports=require("node:path")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90612:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var o=r(60687),s=r(84189),n=r(3832),a=r(43210),i=r(66501);async function l(e,t){try{let r=await fetch("/api/analytics/post-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:e,postSlug:t})});if(!r.ok)throw Error(`Failed to track post view: ${r.statusText}`)}catch(e){i.Ay.error("Error tracking post view:",e)}}let u=({content:e,postId:t,postSlug:r,applyNoFollow:i=!0})=>((0,a.useEffect)(()=>{if(t&&r){let e=setTimeout(()=>{l(t,r)},2e3);return()=>clearTimeout(e)}},[t,r]),(0,o.jsxs)("div",{className:"mb-8",children:[" ",(0,o.jsx)(s.oz,{components:{h1:({node:e,...t})=>(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mt-8 mb-4",...t}),h2:({node:e,...t})=>(0,o.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mt-6 mb-3",...t}),h3:({node:e,...t})=>(0,o.jsx)("h3",{className:"text-xl font-bold text-gray-800 mt-5 mb-2",...t}),h4:({node:e,...t})=>(0,o.jsx)("h4",{className:"text-lg font-bold text-gray-800 mt-4 mb-2",...t}),p:({node:e,...t})=>(0,o.jsx)("p",{className:"text-gray-700 mb-4",...t}),a:({node:e,href:t,...r})=>{let s=t&&(t.startsWith("http://")||t.startsWith("https://")),n="";return s&&(i&&(n+="nofollow "),n+="noopener noreferrer"),(0,o.jsx)("a",{className:"text-emerald-600 hover:text-emerald-700 underline",href:t,rel:n.trim()||void 0,target:s?"_blank":void 0,...r})},ul:({node:e,...t})=>(0,o.jsx)("ul",{className:"list-disc pl-6 mb-4",...t}),ol:({node:e,...t})=>(0,o.jsx)("ol",{className:"list-decimal pl-6 mb-4",...t}),li:({node:e,...t})=>(0,o.jsx)("li",{className:"mb-1",...t}),blockquote:({node:e,...t})=>(0,o.jsx)("blockquote",{className:"border-l-4 border-emerald-500 pl-4 italic my-4",...t})},rehypePlugins:[n.A],children:e})]}))},93721:(e,t,r)=>{Promise.resolve().then(r.bind(r,90612))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[7719,1330,3376,6391,2975,5373,8446,270],()=>r(60874));module.exports=o})();