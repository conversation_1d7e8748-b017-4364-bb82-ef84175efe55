'use client';

import { useState, useEffect } from 'react';
import { fetchAPI } from '@/lib/strapi';
import { fetchFromProxy } from '@/lib/proxyUtils';
import { AxiosRequestConfig } from 'axios';

/**
 * Custom hook that attempts to fetch data directly from Strapi first,
 * and falls back to using the proxy if the direct request fails with a CORS error.
 * 
 * @param endpoint The Strapi API endpoint (e.g., '/posts', '/categories')
 * @param options Axios request options
 * @returns An object containing the data, loading state, and error
 */
export function useStrapiWithFallback<T>(
  endpoint: string,
  options: AxiosRequestConfig = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [usingProxy, setUsingProxy] = useState(false);

  useEffect(() => {
    let isMounted = true;
    
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // First, try to fetch directly from Strapi
        const result = await fetchAPI(endpoint, options);
        
        if (isMounted) {
          setData(result as T);
          setIsLoading(false);
        }
      } catch (directError: any) {
        console.log('Direct Strapi request failed, trying proxy...', directError);
        
        // Check if this is a CORS error or network error
        const isCorsError = 
          directError.message?.includes('CORS') || 
          directError.message?.includes('Network Error') ||
          directError.message?.includes('Failed to fetch');
        
        if (isCorsError) {
          try {
            // If it's a CORS error, try using the proxy
            setUsingProxy(true);
            const proxyResult = await fetchFromProxy<T>(endpoint, options);
            
            if (isMounted) {
              setData(proxyResult);
              setIsLoading(false);
            }
          } catch (proxyError: any) {
            console.error('Proxy request also failed:', proxyError);
            
            if (isMounted) {
              setError(proxyError instanceof Error ? proxyError : new Error(String(proxyError)));
              setIsLoading(false);
            }
          }
        } else {
          // If it's not a CORS error, just set the error
          if (isMounted) {
            setError(directError instanceof Error ? directError : new Error(String(directError)));
            setIsLoading(false);
          }
        }
      }
    };
    
    fetchData();
    
    return () => {
      isMounted = false;
    };
  }, [endpoint, JSON.stringify(options)]);
  
  return { data, isLoading, error, usingProxy };
}
