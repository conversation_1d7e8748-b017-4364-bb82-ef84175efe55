# CORS and Image URL Fix

This document outlines the changes made to fix CORS issues and image URL problems in the Natural Healing Now application.

## Issues Fixed

1. **CORS Configuration**: Updated the CORS configuration in Strapi to allow requests from the Vercel domain.
2. **Image URL Sanitization**: Fixed issues with image URLs where "undefined" was being prepended to the actual URL.
3. **Double URL Issue**: Fixed issues where URLs were being concatenated incorrectly (e.g., `https://domain.comhttps://domain.com`).
4. **Next.js Image Configuration**: Updated the Next.js configuration to properly handle Strapi media domains.
5. **Middleware CSP**: Updated the Content Security Policy in the Next.js middleware to allow images from Strapi media domains.

## Changes Made

### 1. Strapi CORS Configuration (`strapi-cms/config/middlewares.ts`)

Updated the CORS configuration to include the specific Vercel domain and temporarily allow all origins for debugging:

```javascript
origin: [
  env('FRONTEND_URL', 'http://localhost:3000'),
  'http://localhost:3001', // Add localhost:3001 for development
  'https://naturalhealingnow-directory-2hysxhy6h.vercel.app', // Add your Vercel domain
  'https://naturalhealingnow-directory-lmdcdb3lh.vercel.app', // Current Vercel deployment
  'https://*.vercel.app', // Allow all Vercel preview deployments
  '*', // Temporarily allow all origins for debugging
  // Add any other allowed origins here
  // 'https://your-production-domain.com',
],
```

### 2. LazyImage Component (`frontend/src/components/shared/LazyImage.tsx`)

Enhanced URL sanitization to fix double URL issues:

```javascript
// Sanitize the src URL to prevent "undefined" being prepended and fix double URLs
const sanitizedSrc = typeof src === 'string'
  ? src.replace(/^undefined/, '') // Remove 'undefined' prefix if present
       .replace(/^(https?:\/\/[^\/]+)(https?:\/\/)/, '$1/') // Fix double URLs (e.g., https://domain.comhttps://domain.com)
       .replace(/(https?:\/\/[^\/]+)\/+(https?:\/\/)/, '$1/') // Fix URLs with slashes between domains
       .trim()
  : src;
```

### 3. Media Utilities (`frontend/src/lib/mediaUtils.ts`)

Enhanced the `sanitizeUrl` function to better handle URL issues:

```javascript
// Fix double URLs (e.g., https://domain.comhttps://domain.com)
sanitized = sanitized.replace(/^(https?:\/\/[^\/]+)(https?:\/\/)/, '$1/');

// Fix URLs with slashes between domains
sanitized = sanitized.replace(/(https?:\/\/[^\/]+)\/+(https?:\/\/)/, '$1/');
```

### 4. Next.js Configuration (`frontend/next.config.js`)

Added specific Strapi domain to the remotePatterns configuration:

```javascript
// Specific Strapi domain
{
  protocol: 'https',
  hostname: 'nice-badge-2130241d6c.strapiapp.com',
  port: '',
  pathname: '/**',
},
```

### 5. Next.js Middleware (`frontend/src/middleware.ts`)

Updated the middleware to include Strapi media URL and add CORS headers:

```javascript
// Get Strapi Media URL for images
const strapiMediaUrl = 'https://nice-badge-2130241d6c.media.strapiapp.com';

// Updated CSP to include media URLs
img-src 'self' data: blob: ${strapiUrl} ${strapiMediaUrl} https://*.strapiapp.com https://*.media.strapiapp.com https://*.vercel.app;
connect-src 'self' ${strapiUrl} ${strapiMediaUrl} https://*.strapiapp.com https://*.media.strapiapp.com https://*.vercel.app;

// Add CORS headers to allow requests from Strapi
response.headers.set('Access-Control-Allow-Origin', '*');
response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
```

## Deployment Steps

1. **Deploy Strapi Changes**:
   - Update the CORS configuration in Strapi Cloud
   - Restart the Strapi application

2. **Deploy Next.js Changes**:
   - Push the changes to your repository
   - Deploy the Next.js application on Vercel

3. **Verify the Fix**:
   - Check that images are loading correctly
   - Verify that API requests are working without CORS errors
   - Check the browser console for any remaining errors

## Security Considerations

The current configuration includes a temporary wildcard (`*`) for CORS origins. After verifying that everything works correctly, you should remove this wildcard and only allow specific domains:

```javascript
origin: [
  env('FRONTEND_URL', 'http://localhost:3000'),
  'http://localhost:3001',
  'https://naturalhealingnow-directory-2hysxhy6h.vercel.app',
  'https://naturalhealingnow-directory-lmdcdb3lh.vercel.app',
  'https://*.vercel.app',
  // Remove the wildcard '*' after testing
],
```

Similarly, in the Next.js middleware, you should consider removing the wildcard CORS headers after testing:

```javascript
// Replace this:
response.headers.set('Access-Control-Allow-Origin', '*');

// With this:
response.headers.set('Access-Control-Allow-Origin', 'https://nice-badge-2130241d6c.strapiapp.com');
```

## Troubleshooting

If issues persist after deployment:

1. Check the browser console for specific error messages
2. Verify that the Strapi API URL is correctly set in your environment variables
3. Check that the image URLs in the HTML source are correctly formatted
4. Verify that the CORS headers are being properly set in the response
