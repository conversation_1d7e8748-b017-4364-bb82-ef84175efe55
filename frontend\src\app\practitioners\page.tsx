// Removed unused Layout import
import PractitionerCard from '@/components/practitioners/PractitionerCard';
import Link from 'next/link';
import { FiFilter } from 'react-icons/fi';
import { getStrapiContent } from '@/lib/strapi';
import ExploreFurther from '@/components/shared/ExploreFurther';
import SearchInput from '@/components/shared/SearchInput';
import Pagination from '@/components/shared/Pagination';
import { Metadata } from 'next';
import { transformPractitionerData, transformConditionData } from '@/lib/transformers';
import { TransformedPractitioner, StrapiCondition, TransformedCondition, RawStrapiPractitioner, StrapiApiResponse } from '@/types';

// Enable ISR with a 12-hour revalidation period
export const revalidate = 43200; // 12 hours in seconds
// Allow dynamic params to be generated on-demand
export const dynamicParams = true;

// Generate static params for the practitioners list page
// This pre-renders common parameter combinations at build time
export async function generateStaticParams() {
  try {
    // Define the parameter combinations we want to pre-render
    return [
      // Default page (no parameters)
      {},
      // First few pages
      { page: '1' },
      { page: '2' },
      { page: '3' },
      // Common specialty combinations
      { specialtySlug: 'acupuncture' },
      { specialtySlug: 'naturopathy' },
      { specialtySlug: 'massage-therapy' },
      { specialtySlug: 'chiropractic' },
      // Common page + specialty combinations
      { page: '1', specialtySlug: 'acupuncture' },
      { page: '1', specialtySlug: 'naturopathy' },
    ];
  } catch (error) {
    console.error('Error generating static params for practitioners list:', error);
    return [{}]; // At least pre-render the default page
  }
}

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL;
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL; // Get base site URL

// Define types for raw practitioner data based on flat API response
interface RawStrapiPractitioner {
  id: string; // Changed to string
  name: string;
  slug: string;
  bio?: string | null;
  profilePicture?: { // Corrected: profile_picture -> profilePicture (camelCase)
    url?: string;
  } | null;
  qualifications?: string | null;
  contactInfo?: { // Corrected: contact_info -> contactInfo (camelCase)
    phoneNumber?: string;
    emailAddress?: string;
  } | null;
  isVerified?: boolean; // Add isVerified to raw type
  // Add other potential fields if needed
}

// Define the transformed practitioner data structure that matches PractitionerCard props
interface TransformedPractitioner {
  id: string;
  name: string;
  slug: string;
  bio?: string | null;
  profilePicture: string | null; // Corrected: profile_picture -> profilePicture (camelCase), will be absolute URL
  qualifications?: string | null;
  contactInfo?: { // Corrected: contact_info -> contactInfo (camelCase)
    phoneNumber?: string;
    emailAddress?: string;
  } | null;
  isVerified?: boolean; // Add isVerified to transformed type
}

// Define types for condition data (copied from categories/page.tsx)
interface StrapiCondition {
  id: string;
  name: string;
  slug: string;
  // Add other fields if needed from Strapi
}

interface TransformedCondition {
  id: string;
  name: string;
  slug: string;
}

// Transform Strapi condition data (copied from categories/page.tsx)
function transformConditionData(strapiCondition: StrapiCondition): TransformedCondition | null {
  if (!strapiCondition || !strapiCondition.name || !strapiCondition.slug) {
    console.warn(`Skipping invalid condition: ID ${strapiCondition?.id}`);
    return null;
  }
  return {
    id: strapiCondition.id,
    name: strapiCondition.name,
    slug: strapiCondition.slug,
  };
}

// Helper to create absolute URL for Strapi assets (copied from clinics/page.tsx)
const getStrapiMediaUrl = (url: string | undefined | null): string | null => {
  if (!url) return null;
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return `${STRAPI_URL}${url}`;
};

// Transform Strapi data (flat) to match PractitionerCard component props
function transformPractitionerData(rawPractitioner: RawStrapiPractitioner): TransformedPractitioner | null {
  // Basic check
  if (!rawPractitioner) {
    console.warn(`Skipping practitioner due to missing data.`);
    return null;
  }
  // Check essential fields
  if (!rawPractitioner.id || !rawPractitioner.name) {
     console.warn(`Skipping practitioner with missing ID or Name: ID ${rawPractitioner?.id}`);
     return null;
  }

  return {
    id: String(rawPractitioner.id), // Ensure ID is string
    name: rawPractitioner.name || 'Unnamed Practitioner', // Fallback name
    slug: rawPractitioner.slug || `practitioner-${rawPractitioner.id}`, // Fallback slug
    bio: rawPractitioner.bio,
    profilePicture: getStrapiMediaUrl(rawPractitioner.profilePicture?.url), // Corrected field name and use helper
    qualifications: rawPractitioner.qualifications,
    contactInfo: rawPractitioner.contactInfo, // Corrected field name
    isVerified: rawPractitioner.isVerified || false // Pass through isVerified, default false
  };
}

// Define the props for the page component to accept searchParams
interface PractitionersPageProps {
  searchParams?: {
    query?: string;
    page?: string;
    // Add other potential params like location later
  };
}

// Define a type for the Strapi API response (flat data items)
interface StrapiApiResponse<T> {
  data: T[]; // Array of flat type T
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// Generate metadata for the practitioners listing page
// This function is needed to tell Next.js that this page should be statically generated
// for the parameter combinations specified in generateStaticParams
export async function generateMetadata({ searchParams }: { searchParams?: any } = {}): Promise<Metadata> {
  // Default metadata
  let title = "Find Holistic Health Practitioners | Natural Healing Now";
  const description = "Connect with experienced practitioners specializing in natural healing and holistic health approaches. Search by name or specialty.";
  let canonicalPath = "/practitioners";

  // Customize title based on search parameters if present
  if (searchParams) {
    if (searchParams.specialtySlug) {
      // Convert slug to readable format
      const specialty = searchParams.specialtySlug
        .split('-')
        .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      title = `${specialty} Practitioners | Natural Healing Now`;
      canonicalPath += `?specialtySlug=${encodeURIComponent(searchParams.specialtySlug)}`;
    }

    // Only add page to canonical if it's not page 1 (which is the default)
    if (searchParams.page && searchParams.page !== '1') {
      if (canonicalPath.includes('?')) {
        canonicalPath += `&page=${searchParams.page}`;
      } else {
        canonicalPath += `?page=${searchParams.page}`;
      }

      // Add page number to title if not page 1
      title += ` - Page ${searchParams.page}`;
    }
  }

  const canonicalUrl = SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath;

  return {
    title: title,
    description: description,
    alternates: {
      canonical: canonicalUrl,
    },
    // Add Open Graph and Twitter card data
    openGraph: {
      title: title,
      description: description,
      url: canonicalUrl,
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
    },
  };
}

export default async function PractitionersPage({ searchParams }: PractitionersPageProps) { // Destructure searchParams
  // No need to await searchParams, it's already available
  const query = searchParams?.query || ''; // Get query from searchParams
  const currentPage = Number(searchParams?.page) || 1; // Get page from searchParams

  // Fetch practitioners from Strapi, passing query and page
  // Add cache tags for proper revalidation
  const cacheTags = ['strapi-practitioners-list'];

  // Add query-specific cache tag if filtering by query
  if (query) {
    cacheTags.push(`strapi-practitioners-query-${query}`);
  }

  // Add page-specific cache tag
  cacheTags.push(`strapi-practitioners-page-${currentPage}`);

  const response: StrapiApiResponse<RawStrapiPractitioner> = await getStrapiContent.practitioners.getAll({
    query,
    page: currentPage,
    next: {
      tags: cacheTags,
      revalidate: 43200 // Explicitly pass revalidate time for this fetch
    }
  });
  const rawPractitioners: RawStrapiPractitioner[] = response?.data || [];
  const totalPages = response?.meta?.pagination?.pageCount || 1; // Get total pages from meta
  const totalCount = response?.meta?.pagination?.total || 0; // Get total count from meta

  // Transform the data, filtering out any null results
  const transformedPractitioners = rawPractitioners.map(transformPractitionerData); // Pass rawPractitioners
  const practitioners = transformedPractitioners.filter((p): p is TransformedPractitioner => p !== null);

  // We'll use this to store practitioners with detailed data
  let practitionersWithDetails = practitioners;

  // If we're not filtering by query, we can prefetch detailed data for each practitioner
  // This improves performance by avoiding additional API calls when a user clicks on a practitioner
  if (!query) {
    // Use a Map to deduplicate requests and avoid fetching the same practitioner multiple times
    const slugsToFetch = new Map();
    practitioners.forEach(practitioner => {
      if (!slugsToFetch.has(practitioner.slug)) {
        slugsToFetch.set(practitioner.slug, practitioner);
      }
    });
    
    // Create a map of slug to detailed data
    const detailedDataMap = new Map();
    
    // Fetch detailed data for each unique practitioner
    await Promise.all(
      Array.from(slugsToFetch.values()).map(async (practitioner) => {
        try {
          // Try to get detailed data for each practitioner
          const detailResponse = await getStrapiContent.practitioners.getBySlug(practitioner.slug, {
            next: {
              tags: [`strapi-practitioner-${practitioner.id}`],
              revalidate: 43200 // 12 hours
            }
          });
          
          // If we have detailed data, store it in the map
          if (detailResponse?.data && detailResponse.data.length > 0) {
            const detailedData = detailResponse.data[0];
            detailedDataMap.set(practitioner.slug, {
              ...practitioner,
              ...detailedData,
              // Flag this practitioner as having complete data
              _hasDetailedData: true
            });
          }
        } catch (error) {
          console.error(`Error prefetching details for practitioner ${practitioner.slug}:`, error);
        }
      })
    );
    
    // Map the original practitioners array to include detailed data where available
    practitionersWithDetails = practitioners.map(practitioner => {
      return detailedDataMap.get(practitioner.slug) || practitioner;
    });
  }

  // Define type for specialty data from Strapi (Handles potential nesting)
  interface StrapiSpecialty {
    id: number | string;
    attributes?: { // Optional attributes object
      name?: string;
      slug?: string;
      [key: string]: any;
    };
    // Allow top-level fields for flat structure too
    name?: string;
    slug?: string;
    [key: string]: any;
  }

  // Define the structure for the processed specialty data
  interface ProcessedSpecialty {
    name: string;
    slug: string;
  }

  // Fetch specialties from Strapi with proper error handling and caching
  let specialties: ProcessedSpecialty[] = [ // Default list with placeholder slugs
    { name: 'Acupuncture', slug: 'acupuncture' },
    { name: 'Naturopathy', slug: 'naturopathy' },
    { name: 'Massage Therapy', slug: 'massage-therapy' },
    { name: 'Chiropractic', slug: 'chiropractic' },
    { name: 'Ayurveda', slug: 'ayurveda' },
    { name: 'Herbalism', slug: 'herbalism' },
    { name: 'Nutrition', slug: 'nutrition' },
    { name: 'Energy Healing', slug: 'energy-healing' }
  ];
  try {
    const specialtiesResponse = await getStrapiContent.specialties.getAll({
      next: {
        tags: ['strapi-specialties-list'],
        revalidate: 43200 // 12 hours
      }
    });
    if (specialtiesResponse?.data && Array.isArray(specialtiesResponse.data)) {
      const strapiSpecialties = specialtiesResponse.data
        .filter((specialty: any) => specialty && specialty.id) // Ensure ID exists
        .map((specialty: StrapiSpecialty) => {
          // Check for nested attributes first, then fallback to direct properties
          const id = specialty.id?.toString() || '';
          const attributes = specialty.attributes || {};
          const name = attributes.name || specialty.name || 'Unnamed Specialty';

          // Generate a slug if it doesn't exist
          let slug = '';
          if (attributes.slug) {
            slug = attributes.slug;
          } else if (specialty.slug) {
            slug = specialty.slug;
          } else if (name && name !== 'Unnamed Specialty') {
            // Generate slug from name - convert to lowercase, replace spaces with hyphens
            slug = name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
          } else {
            // Fallback to ID-based slug
            slug = `specialty-${id}`;
          }

          return {
            name: name,
            slug: slug
          };
        })
        .filter((s: ProcessedSpecialty | null): s is ProcessedSpecialty => s !== null); // Filter out nulls

      if (strapiSpecialties.length > 0) {
        specialties = strapiSpecialties; // Replace default list with fetched data
      } else {
         // Add logging here to see why strapiSpecialties is empty
         console.warn("Fetched specialties from Strapi but the processed list was empty. Using default list.");
      }
    } else {
        // Add logging here to see why the response structure was invalid
        console.warn("Invalid or empty data received from getStrapiContent.specialties.getAll(). Using default list.");
    }
  } catch (error) {
    console.error("Error fetching specialties, using default list:", error);
    // Keep the default list if fetching fails
  }

  // Fetch conditions from Strapi with proper caching
  const conditionResponse = await getStrapiContent.conditions.getAll({
    next: {
      tags: ['strapi-conditions-list'],
      revalidate: 43200 // 12 hours
    }
  });
  const strapiConditions = conditionResponse?.data || [];
  const transformedConditions = strapiConditions.map((condition: any) => transformConditionData(condition as StrapiCondition));
  const conditions = transformedConditions.filter((cond: any): cond is TransformedCondition => cond !== null);


  return (
    <>
      {/* Page Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Find a Holistic Health Practitioner</h1>
          <p className="text-lg max-w-3xl">
            Connect with experienced practitioners specializing in natural healing and holistic health approaches.
          </p>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search by Name - Replaced with SearchInput component */}
            <div className="flex-1">
               <SearchInput placeholder="Search by practitioner name or specialty" paramName="query" />
            </div>

            {/* Filter Button */}
            <div>
              <button className="w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200">
                <FiFilter />
                <span>Filters</span>
              </button>
            </div>
          </div>

          {/* Filter Tags (placeholder) */}
          <div className="flex flex-wrap gap-2 mt-4">
            {/* Removed Acupuncture and Naturopathy tags */}
          </div>
        </div>
      </div>

      {/* Results Section */}
      <div className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">
              {totalCount} Practitioners Found
            </h2>
            <div className="flex items-center gap-2">
              <span className="text-gray-600">Sort by:</span>
              <select className="border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <option>Relevance</option>
                <option>Name (A-Z)</option>
                <option>Experience</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {practitionersWithDetails.length > 0 ? (
              practitionersWithDetails.map((practitioner) => (
                <PractitionerCard
                  key={practitioner.id}
                  practitioner={practitioner}
                  prefetchedData={!!practitioner._hasDetailedData}
                />
              ))
            ) : (
              <div className="col-span-3 text-center py-8">
                <p className="text-gray-500">No practitioners found. Please try adjusting your search criteria.</p>
              </div>
            )}
          </div>

          {/* Pagination */}
          <div className="mt-12 flex justify-center">
             <Pagination totalPages={totalPages} />
          </div>
        </div>
      </div>

      {/* Featured Specialties */}
      <div className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            Browse by Specialty
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {specialties.map((specialty) => ( // Iterate over ProcessedSpecialty objects
              <Link
                key={specialty.slug} // Use slug as key
                href={`/specialities/${specialty.slug}`} // Link to /specialities/[slug]
                prefetch={false}
                className="bg-gray-50 hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors"
              >
                <span className="text-gray-800 font-medium">{specialty.name}</span> {/* Display name */}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Conditions Section (copied from categories/page.tsx) */}
      <div className="py-12 bg-white"> {/* Changed background to white */}
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            Specialists by Health Conditions
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {conditions.length > 0 ? (
              conditions.map((condition: TransformedCondition) => ( // Add type TransformedCondition
                <Link
                  key={condition.id}
                  href={`/conditions/${condition.slug}`} // Use slug directly
                  className="bg-white hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors" // Changed background to white
                >
                  <span className="text-gray-800 font-medium">{condition.name}</span>
                </Link>
              ))
            ) : (
              <div className="col-span-full text-center py-8"> {/* Use col-span-full */}
                <p className="text-gray-500">No health conditions found. Please check back later.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Add the ExploreFurther component */}
      <ExploreFurther currentPath="/practitioners" />
    </>
  );
}
