(()=>{var e={};e.id=7800,e.ids=[7800],e.modules={2167:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\clinics\\\\SpecialtiesList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\clinics\\SpecialtiesList.tsx","default")},2898:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var n=r(60687);r(43210);var s=r(26800),i=r(91936);function a({clinicsToDisplay:e,totalPages:t,totalCount:r,currentPage:a}){return e?(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsxs)("h2",{className:"text-2xl font-bold text-gray-800",children:[r," Clinics Found"]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-gray-600",children:"Sort by:"}),(0,n.jsxs)("select",{className:"border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",children:[(0,n.jsx)("option",{children:"Relevance"}),(0,n.jsx)("option",{children:"Distance"}),(0,n.jsx)("option",{children:"Name (A-Z)"})]})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.length>0?e.map(e=>(0,n.jsx)(s.default,{clinic:e,showContactInfo:!1,prefetchedData:!0},e.id)):(0,n.jsx)("div",{className:"col-span-3 text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:"No clinics found. Please try adjusting your search criteria."})})}),t>1&&(0,n.jsx)("div",{className:"mt-12 flex justify-center",children:(0,n.jsx)(i.default,{totalPages:t,currentPage:a})})]}):(0,n.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Loading clinics or no data available..."})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7610:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},9770:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,41256)),Promise.resolve().then(r.bind(r,76557)),Promise.resolve().then(r.bind(r,2167)),Promise.resolve().then(r.bind(r,10592))},10592:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\SearchInput.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\SearchInput.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14007:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(37413),s=r(4536),i=r.n(s);let a=({currentPath:e})=>(0,n.jsx)("div",{className:"py-16 bg-emerald-50",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold mb-6 text-gray-800",children:"Explore Further"}),(0,n.jsxs)("p",{className:"text-lg mb-8 max-w-3xl mx-auto text-gray-600",children:["Didn't find what you were looking for?",(0,n.jsx)("br",{}),"Explore our complete listings of clinics, practitioners, and categories."]}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:["/clinics"!==e&&(0,n.jsx)(i(),{href:"/clinics",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold",children:"Find All Clinics"}),"/practitioners"!==e&&(0,n.jsx)(i(),{href:"/practitioners",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-semibold",children:"Find All Practitioners"}),(0,n.jsx)(i(),{href:"/categories",className:"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-semibold",children:"View All Categories"})]})]})})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26800:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(60687),s=r(85814),i=r.n(s),a=r(17019),l=r(20255);let o=({clinic:e,showContactInfo:t=!0,prefetchedData:r=!1})=>{let s=r?{pathname:`/clinics/${e.slug}`,query:{prefetched:"true"}}:`/clinics/${e.slug}`;return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 flex-grow",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,n.jsx)(i(),{href:s,className:"hover:text-emerald-600",children:e.name})}),e.isVerified&&(0,n.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,n.jsx)(l.AI8,{color:"#009967",size:14}),(0,n.jsx)("span",{children:"VERIFIED"})]}),e.description&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.description}),(0,n.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[e.address&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(a.HzC,{className:"mr-2 text-emerald-500"}),(0,n.jsxs)("span",{children:[e.address.city,", ",e.address.stateProvince]})]}),t&&e.contactInfo?.phoneNumber&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(a.QFc,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("span",{children:e.contactInfo.phoneNumber})]}),t&&e.contactInfo?.websiteUrl&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(a.VeH,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("a",{href:e.contactInfo.websiteUrl,target:"_blank",rel:"nofollow noopener noreferrer",className:"hover:text-emerald-600",children:"Visit Website"})]})]})]}),(0,n.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,n.jsx)(i(),{href:s,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Details →"})})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31332:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var n=r(65239),s=r(48088),i=r(31369),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let o={children:["",{children:["clinics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,35194)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\clinics\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\clinics\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/clinics/page",pathname:"/clinics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},33873:e=>{"use strict";e.exports=require("path")},35194:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y,dynamicParams:()=>h,experimental_ppr:()=>m,generateMetadata:()=>j,generateStaticParams:()=>b,revalidate:()=>p});var n=r(37413),s=r(73993),i=r(58446),a=r(14007),l=r(10592),o=r(61120),c=r(41256),d=r(2167),u=r(76557);let p=3600,m=!0,h=!0,g=process.env.NEXT_PUBLIC_SITE_URL,x=e=>e?e.startsWith("http://")||e.startsWith("https://")?e:`https://nice-badge-2130241d6c.strapiapp.com${e}`:null;function f(e){if(!e||!e.id||!e.name)return null;let t=e.practitioners?.map(e=>({...e,profilePicture:x(e.profilePicture?.url)}))||null,r=e.seo?{...e.seo,metaImage:x(e.seo.metaImage?.url),openGraph:e.seo.openGraph?{...e.seo.openGraph,ogImage:x(e.seo.openGraph.ogImage?.url)}:null}:null;return{id:String(e.id),name:e.name||"Unnamed Clinic",slug:e.slug||`clinic-${e.id}`,description:e.description,logo:x(e.logo?.url),featuredImage:x(e.featuredImage?.url),address:e.address||{city:"Unknown",stateProvince:"N/A"},contactInfo:e.contactInfo,isVerified:e.isVerified||!1,location:e.location,openingHours:e.openingHours,services:e.services,specialties:e.specialties,conditions:e.conditions,practitioners:t,appointment_options:e.appointment_options,payment_methods:e.payment_methods,seo:r}}let v=e=>{if(!e||!e.id)return null;let t=e.id?.toString()||"",r=e.attributes||{},n=r.name||e.name||"Unnamed Specialty",s=r.slug||e.slug||("Unnamed Specialty"!==n?n.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""):`specialty-${t}`);return{name:n,slug:s}};async function b(){try{let e=[];try{let t=await i.$.clinics.getAll({fields:["address"],populate:{address:{fields:["city","stateProvince"]}},pagination:{pageSize:100},next:{revalidate:3600,tags:["strapi-clinics-locations"]}}),r=new Set;t?.data?.forEach(e=>{let t=e?.address?.city,n=e?.address?.stateProvince;t&&n?r.add(`${t}, ${n}`):t&&r.add(t)}),e=Array.from(r).slice(0,5)}catch(t){console.error("Error fetching popular locations for clinics page:",t),e=["New York","Los Angeles","Chicago"]}let t=[{},{page:"1"}];return e.forEach(e=>{t.push({location:e}),t.push({page:"1",location:e})}),t}catch(e){return console.error("Error generating static params for clinics list:",e),[{}]}}async function j({searchParams:e}={}){let t=e?.query||"",r=e?.location||"",n=e?.page||"1",s="Find Holistic Health Clinics | Natural Healing Now",i="Discover clinics offering natural healing therapies and holistic health services. Search by location, specialty, or condition to find practitioners near you.",a=i,l="/clinics",o=[];t&&(s=`Clinics for "${t}" | Natural Healing Now`,a=`Find clinics related to "${t}". ${i}`,o.push(`query=${encodeURIComponent(t)}`)),r&&(s=t?`${s} in ${r}`:`Clinics in ${r} | Natural Healing Now`,a=`Find clinics in ${r}. ${i}`,o.push(`location=${encodeURIComponent(r)}`)),"1"!==n&&(s+=` - Page ${n}`,o.push(`page=${n}`)),o.length>0&&(l+=`?${o.join("&")}`);let c=g?`${g}${l}`:l;return{title:s,description:a,alternates:{canonical:c},openGraph:{title:s,description:a,url:c,type:"website"},twitter:{card:"summary_large_image",title:s,description:a}}}async function y({searchParams:e}){let t=e?.query||"",r=e?.location||"",p=Number(e?.page)||1,m={clinics:[],totalPages:1,totalCount:0};try{let e=["strapi-clinics-list"];t&&e.push(`strapi-clinics-query-${t}`),r&&e.push(`strapi-clinics-location-${r}`),e.push(`strapi-clinics-page-${p}`);let n=await i.$.clinics.getAll({query:t,location:r,page:p,populate:{logo:!0,address:!0,contactInfo:!0,location:!0,openingHours:!0,services:!0,specialties:!0,conditions:!0,practitioners:{populate:{profilePicture:!0}},appointment_options:!0,payment_methods:!0,seo:!0},next:{revalidate:3600,tags:e}});m={clinics:(n?.data||[]).map(f).filter(e=>null!==e),totalPages:n?.meta?.pagination?.pageCount||1,totalCount:n?.meta?.pagination?.total||0}}catch(e){console.error("Error fetching clinics for ClinicsPage:",e)}let h=[];try{let e=await i.$.specialties.getAll({pagination:{pageSize:20},fields:["name","slug"],cache:"force-cache",next:{revalidate:604800,tags:["strapi-specialties","strapi-specialties-all","initial-specialties-for-clinics-page"]}});e?.data&&Array.isArray(e.data)&&(h=e.data.map(e=>v(e)).filter(e=>null!==e))}catch(e){console.error("Error fetching initial specialties for /clinics page:",e)}let g=[];try{let e=await i.$.conditions.getAll({pagination:{pageSize:20},fields:["name","slug"],cache:"force-cache",next:{revalidate:604800,tags:["strapi-conditions","strapi-conditions-all","initial-conditions-for-clinics-page"]}});e?.data&&Array.isArray(e.data)&&(g=e.data.map(e=>e&&e.name&&e.slug?{id:e.id,name:e.name,slug:e.slug}:null).filter(e=>null!==e))}catch(e){console.error("Error fetching initial conditions for /clinics page:",e)}return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Find a Holistic Health Clinic"}),(0,n.jsx)("p",{className:"text-lg max-w-3xl",children:"Discover clinics offering natural healing therapies and holistic health services near you."})]})}),(0,n.jsx)("div",{className:"bg-white shadow-md",children:(0,n.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,n.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)(o.Suspense,{fallback:(0,n.jsx)("div",{className:"h-10 w-full bg-gray-200 rounded-lg animate-pulse"}),children:(0,n.jsx)(l.default,{placeholder:"Search by name, speciality, conditions...",paramName:"query"})})}),(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)(o.Suspense,{fallback:(0,n.jsx)("div",{className:"h-10 w-full bg-gray-200 rounded-lg animate-pulse"}),children:(0,n.jsx)(l.default,{placeholder:"City, state, or zip code",paramName:"location",icon:(0,n.jsx)(s.HzC,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})})})}),(0,n.jsx)("div",{children:(0,n.jsxs)("button",{className:"w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200",children:[(0,n.jsx)(s.K7R,{}),(0,n.jsx)("span",{children:"Filters"})]})})]})})}),(0,n.jsx)("div",{className:"py-12",children:(0,n.jsx)("div",{className:"container mx-auto px-4",children:(0,n.jsx)(c.default,{clinicsToDisplay:m.clinics,totalPages:m.totalPages,totalCount:m.totalCount,currentPage:p})})}),(0,n.jsx)("div",{className:"py-12 bg-white",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Browse by Specialty"}),(0,n.jsx)(o.Suspense,{fallback:(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,n.jsx)("div",{className:"bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"},t))}),children:(0,n.jsx)(d.default,{initialSpecialties:h})})]})}),(0,n.jsx)("div",{className:"py-12 bg-white",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Specialists by Health Conditions"}),(0,n.jsx)(o.Suspense,{fallback:(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,n.jsx)("div",{className:"bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"},t))}),children:(0,n.jsx)(u.default,{initialConditions:g})})]})}),(0,n.jsx)(a.A,{currentPath:"/clinics"})]})}},41256:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\clinics\\\\ClinicsList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\clinics\\ClinicsList.tsx","default")},42859:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(60687),s=r(43210),i=r(85814),a=r.n(i);function l({initialConditions:e}){let[t,r]=(0,s.useState)(e||[]),[i,l]=(0,s.useState)(!0),[o,c]=(0,s.useState)(null);return i?(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,n.jsx)("div",{className:"bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"},t))}):o?(0,n.jsx)("div",{className:"text-center py-4 text-red-500",children:o}):0===t.length?(0,n.jsx)("div",{className:"text-center py-4 text-gray-500",children:"No health conditions available at the moment."}):(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:t.map(e=>(0,n.jsx)(a(),{href:`/conditions/${e.slug}`,className:"bg-white hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors",children:(0,n.jsx)("span",{className:"text-gray-800 font-medium",children:e.name})},e.id))})}r(73076)},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},49384:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=function(){for(var e,t,r=0,n="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,n,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(s&&(s+=" "),s+=n)}else for(n in t)t[n]&&(s&&(s+=" "),s+=n);return s}(e))&&(n&&(n+=" "),n+=t);return n}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68016:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(60687),s=r(43210),i=r(16189),a=r(17019);function l({placeholder:e,paramName:t="query",icon:r}){let l=(0,i.useSearchParams)(),o=(0,i.usePathname)(),{replace:c}=(0,i.useRouter)(),d=function(e,t,r){var n=this,i=(0,s.useRef)(null),a=(0,s.useRef)(0),l=(0,s.useRef)(null),o=(0,s.useRef)([]),c=(0,s.useRef)(),d=(0,s.useRef)(),u=(0,s.useRef)(e),p=(0,s.useRef)(!0);u.current=e;var m="undefined"!=typeof window,h=!t&&0!==t&&m;if("function"!=typeof e)throw TypeError("Expected a function");t=+t||0;var g=!!(r=r||{}).leading,x=!("trailing"in r)||!!r.trailing,f="maxWait"in r,v="debounceOnServer"in r&&!!r.debounceOnServer,b=f?Math.max(+r.maxWait||0,t):null;return(0,s.useEffect)(function(){return p.current=!0,function(){p.current=!1}},[]),(0,s.useMemo)(function(){var e=function(e){var t=o.current,r=c.current;return o.current=c.current=null,a.current=e,d.current=u.current.apply(r,t)},r=function(e,t){h&&cancelAnimationFrame(l.current),l.current=h?requestAnimationFrame(e):setTimeout(e,t)},s=function(e){if(!p.current)return!1;var r=e-i.current;return!i.current||r>=t||r<0||f&&e-a.current>=b},j=function(t){return l.current=null,x&&o.current?e(t):(o.current=c.current=null,d.current)},y=function e(){var n=Date.now();if(s(n))return j(n);if(p.current){var l=t-(n-i.current);r(e,f?Math.min(l,b-(n-a.current)):l)}},N=function(){if(m||v){var u=Date.now(),h=s(u);if(o.current=[].slice.call(arguments),c.current=n,i.current=u,h){if(!l.current&&p.current)return a.current=i.current,r(y,t),g?e(i.current):d.current;if(f)return r(y,t),e(i.current)}return l.current||r(y,t),d.current}};return N.cancel=function(){l.current&&(h?cancelAnimationFrame(l.current):clearTimeout(l.current)),a.current=0,o.current=i.current=c.current=l.current=null},N.isPending=function(){return!!l.current},N.flush=function(){return l.current?j(Date.now()):d.current},N},[g,f,t,b,x,h,m,v])}(e=>{console.log(`Searching... ${e}`);let r=new URLSearchParams(l);r.set("page","1"),e?r.set(t,e):r.delete(t),c(`${o}?${r.toString()}`)},500);return(0,n.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,n.jsx)("label",{htmlFor:t,className:"sr-only",children:"Search"}),(0,n.jsx)("input",{id:t,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:e,onChange:e=>{d(e.target.value)},defaultValue:l.get(t)?.toString()}),r||(0,n.jsx)(a.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},72818:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,2898)),Promise.resolve().then(r.bind(r,42859)),Promise.resolve().then(r.bind(r,80989)),Promise.resolve().then(r.bind(r,68016))},74075:e=>{"use strict";e.exports=require("zlib")},76557:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\clinics\\\\ConditionsList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\clinics\\ConditionsList.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},80989:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(60687),s=r(43210),i=r(85814),a=r.n(i);function l({initialSpecialties:e}){let[t,r]=(0,s.useState)(e||[]),[i,l]=(0,s.useState)(!0),[o,c]=(0,s.useState)(null);return i?(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,n.jsx)("div",{className:"bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"},t))}):o?(0,n.jsx)("div",{className:"text-center py-4 text-red-500",children:o}):0===t.length?(0,n.jsx)("div",{className:"text-center py-4 text-gray-500",children:"No specialties available at the moment."}):(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:t.map(e=>(0,n.jsx)(a(),{href:`/specialities/${e.slug}`,prefetch:!1,className:"bg-gray-50 hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors",children:(0,n.jsx)("span",{className:"text-gray-800 font-medium",children:e.name})},e.slug))})}r(73076)},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91936:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var n=r(60687),s=r(44725),i=r(7610),a=r(49384),l=r(85814),o=r.n(l),c=r(16189);let d=(e,t)=>t<=7?Array.from({length:t},(e,t)=>t+1):e<=3?[1,2,3,"...",t-1,t]:e>=t-2?[1,2,"...",t-2,t-1,t]:[1,"...",e-1,e,e+1,"...",t];function u({totalPages:e,currentPage:t}){let r=(0,c.usePathname)(),s=(0,c.useSearchParams)(),i=void 0!==t?t:Number(s.get("page"))||1,a=e=>{let t=new URLSearchParams(s);return t.set("page",e.toString()),`${r}?${t.toString()}`},l=d(i,e);return e<=1?null:(0,n.jsxs)("div",{className:"inline-flex",children:[(0,n.jsx)(m,{direction:"left",href:a(i-1),isDisabled:i<=1}),(0,n.jsx)("div",{className:"flex -space-x-px",children:l.map((e,t)=>{let r;return 0===t&&(r="first"),t===l.length-1&&(r="last"),1===l.length&&(r="single"),"..."===e&&(r="middle"),(0,n.jsx)(p,{href:a(e),page:e,position:r,isActive:i===e},`${e}-${t}`)})}),(0,n.jsx)(m,{direction:"right",href:a(i+1),isDisabled:i>=e})]})}function p({page:e,href:t,isActive:r,position:s}){let i=(0,a.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===s||"single"===s,"rounded-r-md":"last"===s||"single"===s,"z-10 bg-emerald-600 border-emerald-600 text-white":r,"hover:bg-gray-100":!r&&"middle"!==s,"text-gray-300 pointer-events-none":"middle"===s});return r||"middle"===s?(0,n.jsx)("div",{className:i,children:e}):(0,n.jsx)(o(),{href:t,className:i,children:e})}function m({href:e,direction:t,isDisabled:r}){let l=(0,a.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":r,"hover:bg-gray-100":!r,"mr-2 md:mr-4":"left"===t,"ml-2 md:ml-4":"right"===t}),c="left"===t?(0,n.jsx)(s.A,{className:"w-4"}):(0,n.jsx)(i.A,{className:"w-4"});return r?(0,n.jsx)("div",{className:l,children:c}):(0,n.jsx)(o(),{className:l,href:e,children:c})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,1330,3376,6391,2975,255,9298,8446,270,3076],()=>r(31332));module.exports=n})();