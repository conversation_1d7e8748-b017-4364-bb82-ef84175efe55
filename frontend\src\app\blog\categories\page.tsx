import Layout from '@/components/layout/Layout';
import Link from 'next/link';
import { FiSearch } from 'react-icons/fi';
import { getStrapiContent } from '@/lib/strapi';
import SearchInput from '@/components/shared/SearchInput';
import Pagination from '@/components/shared/Pagination';
import SEOHead from '@/components/SEOHead';

// Define interface for blog category
interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  count: number;
}

// Define props for the page component to accept searchParams
interface BlogCategoriesPageProps {
  searchParams?: {
    query?: string;
    page?: string;
  };
}

// Function to map Strapi API response for blog categories
function mapStrapiBlogCategoryToProps(category: any): BlogCategory {
  if (!category || !category.id) {
    console.warn("Received invalid category data:", category);
    return {
      id: category?.id || '',
      name: 'Invalid Category Data',
      slug: `invalid-category-${category?.id || 'unknown'}`,
      description: null,
      count: 0
    };
  }

  // Access fields directly
  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    description: category.description || null,
    count: category.blog_posts?.length || 0
  };
}

export default async function BlogCategoriesPage({ searchParams }: BlogCategoriesPageProps) {
  // Get search params
  const awaitedSearchParams = await searchParams;
  const query = awaitedSearchParams?.query || '';
  const currentPage = Number(awaitedSearchParams?.page) || 1;
  const pageSize = 9; // Number of categories per page

  // Fetch categories from Strapi
  let categories: BlogCategory[] = [];
  let totalPages = 1;

  try {
    // Prepare filters for search query if provided
    const filters = query ? { name: { $containsi: query } } : undefined;

    // Fetch categories with pagination
    const categoriesResponse = await getStrapiContent.blog.getCategories({
      filters,
      pagination: {
        page: currentPage,
        pageSize
      },
      populate: ['blog_posts']
    });

    // Map response to our interface
    if (categoriesResponse && Array.isArray(categoriesResponse.data)) {
      categories = categoriesResponse.data.map(mapStrapiBlogCategoryToProps);

      // Get total pages from pagination metadata
      totalPages = categoriesResponse.meta?.pagination?.pageCount || 1;
    } else {
      console.error("Invalid categoriesResponse structure:", categoriesResponse);
    }
  } catch (error) {
    console.error('Error fetching blog categories:', error);
  }
  // Prepare SEO props
  const seoProps = {
    defaultTitle: 'Blog Categories | Natural Healing Now',
    defaultDescription: 'Explore our blog categories to find articles on specific holistic health topics and natural healing approaches.',
  };

  return (
    <>
      {/* SEO Head */}
      <SEOHead {...seoProps} />

      {/* Page Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Blog Categories</h1>
          <p className="text-lg max-w-3xl">
            Explore our articles by category to find information on specific holistic health topics.
          </p>
        </div>
      </div>

      {/* Search Section */}
      <div className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-md mx-auto">
            <SearchInput placeholder="Search categories" />
          </div>
        </div>
      </div>

      {/* Categories Grid */}
      <div className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            All Categories
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.length > 0 ? (
              categories.map(category => (
                <div
                  key={category.id}
                  className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                >
                  {/* Category Image/Icon Placeholder */}
                  <div className="h-40 bg-emerald-100 flex items-center justify-center">
                    <span className="text-emerald-700 text-2xl font-semibold">
                      {category.name.charAt(0)}
                    </span>
                  </div>

                  <div className="p-5">
                    <h3 className="text-xl font-semibold mb-2 text-gray-800">
                      <Link
                        href={`/blog/categories/${category.slug}`}
                        className="hover:text-emerald-600"
                      >
                        {category.name}
                      </Link>
                    </h3>

                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {category.description || `Articles about ${category.name.toLowerCase()}`}
                    </p>

                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">
                        {category.count} {category.count === 1 ? 'article' : 'articles'}
                      </span>

                      <Link
                        href={`/blog/categories/${category.slug}`}
                        className="text-emerald-600 hover:text-emerald-700 font-medium text-sm"
                      >
                        View Articles →
                      </Link>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-3 text-center py-8">
                <p className="text-gray-500">
                  {query
                    ? `No categories found matching "${query}".`
                    : 'No categories found. Check back soon!'}
                </p>
              </div>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-12 flex justify-center">
              <Pagination totalPages={totalPages} />
            </div>
          )}
        </div>
      </div>

      {/* Back to Blog Link */}
      <div className="py-8 bg-white">
        <div className="container mx-auto px-4 text-center">
          <Link
            href="/blog"
            className="text-emerald-600 hover:text-emerald-700 font-medium"
          >
            ← Back to Blog
          </Link>
        </div>
      </div>
    </>
  );
}
