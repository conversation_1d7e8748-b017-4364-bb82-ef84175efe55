import Layout from '@/components/layout/Layout';
import Link from 'next/link';
import { FiTag, FiTrendingUp } from 'react-icons/fi';
import { getStrapiContent } from '@/lib/strapi';
import SEOHead from '@/components/SEOHead';
import SearchInput from '@/components/shared/SearchInput';
import Pagination from '@/components/shared/Pagination';
import { Suspense } from 'react';
import CategoryFilter from '@/components/blog/CategoryFilter';
import PopularPosts from '@/components/blog/PopularPosts';
import NewsletterSignup from '@/components/blog/NewsletterSignup';
import TagCloud from '@/components/blog/TagCloud';
import { getPopularPosts } from '@/lib/analytics';
import { getStrapiMediaUrl } from '@/lib/mediaUtils'; // Import the centralized utility
import logger from '@/lib/logger'; // Import logger
import {
  getBlogPosts,
  getFeaturedBlogPosts,
  getBlogCategories,
  getBlogTags,
  getBlogHomepage
} from '@/lib/serverCache'; // Import cached functions
import BlogContent from '@/components/blog/BlogContent'; // Import the client component

// Enable ISR with on-demand revalidation
// No revalidation period - will only revalidate when triggered by webhook
export const revalidate = false;

// Allow dynamic params to be generated on-demand
export const dynamicParams = true;

// Generate static paths for common blog page variants at build time
export async function generateStaticParams() {
  try {
    // Pre-render the first 3 pages of the blog
    const pages = [1, 2, 3];
    const staticParams = pages.map(page => ({
      searchParams: { page: page.toString() }
    }));

    // Also pre-render the main blog page without any parameters
    staticParams.push({ searchParams: { page: '' } });

    // Get categories to pre-render category pages
    const categoriesResponse = await getBlogCategories();
    if (categoriesResponse?.data) {
      // Get the top 5 categories with the most posts
      const topCategories = categoriesResponse.data
        .map((c: FlatStrapiBlogCategory) => mapStrapiBlogCategoryToProps(c))
        .filter((c): c is BlogCategory => c !== null && c.id !== '')
        .sort((a: BlogCategory, b: BlogCategory) => b.count - a.count)
        .slice(0, 5);

      // Add category pages to static params
      topCategories.forEach((category: BlogCategory) => {
        staticParams.push({
          searchParams: { page: '', category: category.slug }
        });
      });
    }

    logger.debug(`Pre-rendering ${staticParams.length} blog page variants`);
    return staticParams;
  } catch (error) {
    logger.error('Error generating static params for blog page:', error);
    // Return basic params to prevent build failure
    return [{ searchParams: {} }];
  }
}

// Define TypeScript interfaces reflecting the FLAT structure from logs
// NOTE: These might need further refinement based on full API response details
interface FlatStrapiBlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  featuredImage?: {
    url?: string;
  } | string | null; // Allow string for direct URL or object
  publishDate?: string;
  createdAt: string;
  content?: string; // Added content
  isFeatured?: boolean; // Added isFeatured
  view_count?: number; // Added view_count
  author_blogs?: Array<{
    name: string;
    slug: string;
    profilePicture?: {
      url?: string;
    } | string | null; // Allow string for direct URL or object
  }>;
}

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  featured_image: string | null;
  publish_date: string;
  isFeatured?: boolean;
  view_count?: number;
  reading_time?: number; // Added reading time field
  content?: string; // Added content field for reading time calculation
  author: {
    name: string;
    slug: string;
    profile_picture: string | null;
  } | null;
}

interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  count: number;
}

// Interface for the flat category structure
interface FlatStrapiBlogCategory {
  id: string;
  name: string;
  slug: string;
  blog_posts?: Array<any>; // Based on logs
  // Add other direct fields if needed
}

// Interface for the flat tag structure
interface FlatStrapiBlogTag {
  id: string;
  name: string;
  slug: string;
  // Add other direct fields if needed
}


// Function to map Strapi API response to component props (using flat structure)
function mapStrapiBlogPostToProps(post: FlatStrapiBlogPost): BlogPost {
  if (!post || !post.id) { // Check for post and id
    logger.warn("Received invalid post data in mapStrapiBlogPostToProps:", { postData: post });
    return {
      id: post?.id || `invalid-${Date.now()}`,
      title: 'Invalid Post Data',
      slug: `invalid-post-${post?.id || Date.now()}`,
      excerpt: null,
      featured_image: null,
      publish_date: new Date().toISOString(),
      author: null,
      reading_time: 0,
      content: '',
    };
  }

  const authorData = post.author_blogs?.[0];

  if (process.env.NODE_ENV === 'development') {
    logger.debug(`Author data for post "${post.title}":`, { authorData });
    if (authorData?.profilePicture) {
      logger.debug(`Profile picture data for ${authorData.name}:`, { profilePicData: authorData.profilePicture });
    } else {
      logger.debug(`No profile picture found for author: ${authorData?.name}`);
    }
  }

  // Calculate reading time based on excerpt and/or title if content is not available
  const calculateReadingTime = (text: string): number => {
    const wordsPerMinute = 200;
    const wordCount = text?.split(/\s+/)?.length || 0;
    return Math.max(1, Math.ceil(wordCount / wordsPerMinute));
  };

  const contentForReading = post.content || '';
  const textForReadingTimeEstimate = contentForReading || ((post.excerpt || '') + ' ' + (post.title || '')).repeat(5);
  const readingTime = calculateReadingTime(textForReadingTimeEstimate);

  const blogPost: BlogPost = {
    id: post.id,
    title: post.title || 'Untitled Post',
    slug: post.slug || `post-${post.id}`,
    excerpt: post.excerpt || null,
    featured_image: getStrapiMediaUrl(post.featuredImage), // Use centralized utility
    publish_date: post.publishDate || post.createdAt || new Date().toISOString(),
    isFeatured: post.isFeatured || false,
    reading_time: readingTime,
    content: contentForReading,
    author: authorData ? {
      name: authorData.name || 'Unknown Author',
      slug: authorData.slug || 'unknown-author',
      profile_picture: getStrapiMediaUrl(authorData.profilePicture) // Use centralized utility
    } : null
  };

  Object.defineProperty(blogPost, 'view_count', {
    value: post.view_count || 0,
    enumerable: false
  });

  return blogPost;
}

// Function to map Strapi API response for blog categories (using flat structure)
function mapStrapiBlogCategoryToProps(category: FlatStrapiBlogCategory): BlogCategory {
  if (!category || !category.id) {
    logger.warn("Received invalid category data in mapStrapiBlogCategoryToProps:", { categoryData: category });
    return {
      id: category?.id || `invalid-${Date.now()}`,
      name: 'Invalid Category Data',
      slug: `invalid-category-${category?.id || Date.now()}`,
      count: 0
    };
  }

  // Access fields directly
  return {
    id: category.id,
    name: category.name, // Direct access
    slug: category.slug, // Direct access
    count: category.blog_posts?.length || 0 // Direct access based on logs
  };
}

// Function to map Strapi API response for blog tags (using flat structure)
// Uncommenting this function to fix syntax issues, even if not directly called later.
function mapStrapiBlogTagToProps(tag: FlatStrapiBlogTag) {
  if (!tag || !tag.id) return null;

  return {
    id: tag.id,
    name: tag.name, // Direct access
    slug: tag.slug  // Direct access
  };
}

// Define props for the page component to accept searchParams
interface BlogPageProps {
  searchParams?: {
    query?: string;
    page?: string;
    category?: string;
  };
}

export default async function BlogPage({ searchParams }: BlogPageProps) {
  // Define page size for static data fetching
  const pageSize = 6;

  // Fetch data from Strapi
  let posts: BlogPost[] = []; // Ensure type safety
  let featuredPost: BlogPost | null = null; // Featured post
  let popularPosts: BlogPost[] = []; // Popular posts for sidebar
  let categories: BlogCategory[] = []; // Ensure type safety
  let tags: string[] = []; // Ensure type safety for tags as well
  let blogHomepage = null;
  let totalPages = 1; // Declare totalPages outside try block with default

  try {
    // Default fetch for static page - can be overridden by client component
    const postsResponse = await getBlogPosts({
      page: 1,
      pageSize,
      next: {
        tags: ['strapi-blog-posts', 'page-1']
      }
    });

    const featuredPostsResponse = await getFeaturedBlogPosts({
      next: {
        tags: ['strapi-blog-posts-featured']
      }
    });

    // Fallback for popular posts
    const popularPostsFallbackResponse = await getBlogPosts({
      page: 1,
      pageSize: 4,
      next: {
        tags: ['strapi-blog-posts', 'strapi-blog-posts-recent']
      }
    });

    logger.debug("Raw postsResponse:", { data: postsResponse });

    if (postsResponse?.data) {
      if (postsResponse.data.length > 0) {
        logger.debug("First post data structure (from postsResponse):", { post: postsResponse.data[0] });
      }
      posts = postsResponse.data.map((p: FlatStrapiBlogPost) => mapStrapiBlogPostToProps(p)).filter((p): p is BlogPost => p !== null && p.id !== '');
    } else {
      logger.error("Invalid postsResponse structure or no data", { postsResponse });
      // posts remains []
    }

    let tempFeaturedPosts: BlogPost[] = [];
    if (featuredPostsResponse?.data) {
      tempFeaturedPosts = featuredPostsResponse.data
        .map((post: FlatStrapiBlogPost) => mapStrapiBlogPostToProps(post))
        .filter((post): post is BlogPost => post !== null && post.id !== '');
      logger.debug(`Found ${tempFeaturedPosts.length} manually featured posts`);
      if (tempFeaturedPosts.length > 1) {
        const today = new Date();
        const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / 86400000);
        const postIndex = dayOfYear % tempFeaturedPosts.length;
        featuredPost = tempFeaturedPosts[postIndex];
        logger.debug(`Rotating featured posts: Day ${dayOfYear}, showing post index ${postIndex}`);
      } else if (tempFeaturedPosts.length === 1) {
        featuredPost = tempFeaturedPosts[0];
      }
    }

    if (!featuredPost) {
      try {
        logger.debug('No manually featured posts, trying to get most popular post from analytics');
        const analyticsPopularPosts = await getPopularPosts(1, 'month');
        if (analyticsPopularPosts?.length > 0) {
          logger.debug('Using most popular post from analytics as featured');
          featuredPost = analyticsPopularPosts[0];
        }
      } catch (err) {
        logger.error('Error fetching popular post for featuring from analytics:', { error: err });
      }
    }

    if (!featuredPost && posts.length > 0) {
      logger.debug('No featured or popular (analytics) posts found, using most recent post from current list');
      featuredPost = posts[0];
    }

    if (featuredPost) { // Ensure featuredPost is not null before filtering
      posts = posts.filter(post => post.id !== featuredPost!.id);
    }

    try {
      logger.debug('Attempting to fetch popular posts from analytics');
      const analyticsPopular = await getPopularPosts(4, 'all'); // Fetch 4 popular posts
      if (analyticsPopular?.length > 0) {
        popularPosts = analyticsPopular.filter(p => p.id !== featuredPost?.id).slice(0, 4);
        logger.debug('Using popular posts from analytics', { count: popularPosts.length });
      } else {
        throw new Error("No popular posts from analytics"); // Force fallback
      }
    } catch (err) {
      logger.warn('Failed to get popular posts from analytics, falling back to recent posts', { error: err });
      if (popularPostsFallbackResponse?.data) {
        popularPosts = popularPostsFallbackResponse.data
          .map((post: FlatStrapiBlogPost) => mapStrapiBlogPostToProps(post))
          .filter((post): post is BlogPost => post !== null && post.id !== '' && post.id !== featuredPost?.id)
          .slice(0, 4);
        logger.debug('Using recent posts as popular posts fallback', { count: popularPosts.length });
      }
    }

    const totalPosts = postsResponse?.meta?.pagination?.total || 0;
    totalPages = Math.ceil(totalPosts / pageSize);

    // Use cached functions for categories and tags with proper Next.js 15 cache options
    const categoriesResponse = await getBlogCategories({
      next: {
        tags: ['strapi-blog-categories', 'strapi-categories']
      }
    });
    logger.debug("Raw categoriesResponse:", { data: categoriesResponse });
    if (categoriesResponse?.data) {
      categories = categoriesResponse.data.map((c: FlatStrapiBlogCategory) => mapStrapiBlogCategoryToProps(c)).filter((c): c is BlogCategory => c !== null && c.id !== '');
    } else {
      logger.error("Invalid categoriesResponse structure or no data", { categoriesResponse });
      // categories remains []
    }

    const tagsResponse = await getBlogTags({
      next: {
        tags: ['strapi-blog-tags', 'strapi-tags']
      }
    });
    logger.debug("Raw tagsResponse:", { data: tagsResponse });
    if (tagsResponse?.data) {
      tags = tagsResponse.data
        .map((tag: FlatStrapiBlogTag): string | null => {
          if (!tag?.name) {
            logger.warn(`Blog tag with ID ${tag?.id} is missing name. Skipping.`);
            return null;
          }
          return tag.name;
        })
        .filter((name): name is string => name !== null);
    } else {
      logger.error("Invalid tagsResponse structure or no data", { tagsResponse });
      // tags remains []
    }

    // Use cached function for blog homepage with proper Next.js 15 cache options
    const blogHomepageResponse = await getBlogHomepage({
      next: {
        tags: ['strapi-global-setting', 'strapi-blog-homepage']
      }
    });
    if (blogHomepageResponse?.data) {
        blogHomepage = blogHomepageResponse.data;
    } else {
        logger.warn("Blog homepage data not found or invalid structure", { blogHomepageResponse });
        // blogHomepage remains null
    }

  } catch (error) {
    logger.error('Error fetching blog data in BlogPage:', { error });

    // Log more detailed error information
    if (error.response) {
      logger.error('Response status:', error.response.status);
      logger.error('Response data:', error.response.data);
    } else if (error.request) {
      logger.error('No response received. Request details:', error.request);
    } else {
      logger.error('Error details:', error.message);
    }

    // Set empty arrays and handle gracefully, totalPages defaults to 1
    featuredPost = null;
    popularPosts = [];
    categories = [];
    tags = [];
    blogHomepage = null;
    totalPages = 1;

    // Show error message to user
    console.error('Failed to load blog data. Please try again later.');

    // Add specific error message for debugging in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error details:', error.message || 'Unknown error');
    }
  }

  // Use SEO data from blog homepage if available (moved outside catch)
  const seoMetadata = blogHomepage ? getStrapiContent.seo.getMetadata(blogHomepage) : null;

  // Prepare SEO props
  const seoProps = seoMetadata ? {
    defaultTitle: seoMetadata.title || 'Natural Healing Blog',
    defaultDescription: seoMetadata.description || 'Insights, tips, and information about holistic health approaches and natural healing.',
    defaultOgImage: seoMetadata.openGraph?.image || '',
    seo: {
      metaTitle: seoMetadata.title,
      metaDescription: seoMetadata.description,
      metaRobots: seoMetadata.metaRobots,
      structuredData: seoMetadata.structuredData,
      canonicalURL: seoMetadata.canonicalURL,
      metaSocial: [
        seoMetadata.openGraph && {
          socialNetwork: 'Facebook' as const,
          title: seoMetadata.openGraph.title,
          description: seoMetadata.openGraph.description,
        },
        seoMetadata.twitter && {
          socialNetwork: 'Twitter' as const,
          title: seoMetadata.twitter.title,
          description: seoMetadata.twitter.description,
        }
      ].filter(Boolean)
    }
  } : {
    defaultTitle: 'Natural Healing Blog',
    defaultDescription: 'Insights, tips, and information about holistic health approaches and natural healing.',
  };

  return (
    <>
      {/* SEO Head */}
      <SEOHead {...seoProps} />

      {/* Enhanced Hero Section */}
      <div className="bg-gradient-to-r from-emerald-700 to-teal-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Natural Healing Blog</h1>
            <p className="text-xl opacity-90 mb-8">
              Discover insights, tips, and expert advice on holistic health approaches and natural healing methods.
            </p>
            <div className="max-w-lg mx-auto">
              <Suspense fallback={
                <div className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg bg-gray-100 animate-pulse">
                  <span className="sr-only">Loading search...</span>
                </div>
              }>
                <SearchInput placeholder="Search for topics, remedies, or health concerns..." />
              </Suspense>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Suspense fallback={
            <div className="flex flex-wrap items-center gap-3">
              <span className="font-medium text-gray-700">Browse by:</span>
              <div className="px-3 py-1 rounded-full text-sm bg-gray-100 animate-pulse w-16"></div>
              <div className="px-3 py-1 rounded-full text-sm bg-gray-100 animate-pulse w-20"></div>
              <div className="px-3 py-1 rounded-full text-sm bg-gray-100 animate-pulse w-24"></div>
            </div>
          }>
            <CategoryFilter categories={categories} />
          </Suspense>
        </div>
      </div>

      {/* Wrap the dynamic content section in Suspense */}
      <Suspense fallback={
        <div className="py-12">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Main Content Loading State */}
              <div className="lg:w-2/3">
                <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden h-96 animate-pulse">
                      <div className="h-48 bg-gray-200"></div>
                      <div className="p-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                        <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
                        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              {/* Sidebar Loading State */}
              <div className="lg:w-1/3 space-y-8">
                <div className="bg-white shadow-md rounded-lg p-6 animate-pulse">
                  <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="space-y-3">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="flex gap-3 items-center">
                        <div className="h-12 w-12 bg-gray-200 rounded"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      }>
        {/* Client component that handles search parameters */}
        <BlogContent
          initialPosts={posts}
          initialFeaturedPost={featuredPost}
          initialPopularPosts={popularPosts}
          categories={categories}
          tags={tags}
          totalPages={totalPages}
        />
      </Suspense>
    </>
  );
}
