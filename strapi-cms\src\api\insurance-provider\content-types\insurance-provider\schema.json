{"kind": "collectionType", "collectionName": "insurance_providers", "info": {"singularName": "insurance-provider", "pluralName": "insurance-providers", "displayName": "Insurance Provider"}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "website": {"type": "string"}, "clinics": {"type": "relation", "relation": "manyToMany", "target": "api::clinic.clinic", "mappedBy": "insurance_providers"}}}