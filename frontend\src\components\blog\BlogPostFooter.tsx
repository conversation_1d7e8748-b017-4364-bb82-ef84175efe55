"use client";

import Link from 'next/link';
import { Fi<PERSON>rrowLeft, FiTag, FiClock } from 'react-icons/fi';
import { BlogPost, truncateByWords } from '@/lib/blogUtils';

interface BlogPostFooterProps {
  post: BlogPost;
}

/**
 * Blog post footer component
 * Displays categories, tags, author bio, related posts, and back link
 */
const BlogPostFooter: React.FC<BlogPostFooterProps> = ({ post }) => {
  return (
    <footer className="border-t border-gray-200 pt-6">
      {/* Categories and Tags */}
      <div className="mb-8">
        {post.categories && post.categories.length > 0 && (
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Categories:</h3>
            <div className="flex flex-wrap gap-2">
              {post.categories.map(category => (
                <Link
                  key={category.id}
                  href={`/blog/categories/${category.slug}`}
                  className="bg-emerald-50 text-emerald-700 px-3 py-1 rounded-full text-sm hover:bg-emerald-100"
                >
                  {category.name}
                </Link>
              ))}
            </div>
          </div>
        )}

        {post.tags && post.tags.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Tags:</h3>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag, index) => (
                <Link
                  key={index}
                  href={`/blog/tags/${tag.toLowerCase().replace(/ /g, '-')}`}
                  className="flex items-center bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200"
                >
                  <FiTag className="mr-1 h-3 w-3" />
                  {tag}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Author Bio */}
      <div className="bg-gray-50 rounded-lg p-6 mb-8">
        <div className="flex items-center mb-4">
          {post.author.profile_picture ? (
            <div className="h-12 w-12 rounded-full overflow-hidden mr-4">
              <img
                src={post.author.profile_picture}
                alt={post.author.name}
                className="h-full w-full object-cover"
              />
            </div>
          ) : (
            <div className="bg-emerald-100 h-12 w-12 rounded-full flex items-center justify-center mr-4">
              <span className="text-emerald-700 font-semibold">
                {post.author.name.charAt(0)}
              </span>
            </div>
          )}
          <div>
            <h3 className="font-semibold text-gray-800">
              <Link
                href={`/blog/authors/${post.author.slug}`}
                className="hover:text-emerald-600"
              >
                {post.author.name}
              </Link>
            </h3>
            <p className="text-sm text-gray-600">Contributor</p>
          </div>
        </div>
        {/* Display truncated author bio if available, otherwise show a default message */}
        {post.author.bio ? (
          <p className="text-gray-700">{truncateByWords(post.author.bio, 30)}</p>
        ) : (
          <p className="text-gray-700 italic">No bio available for this author.</p>
        )}
      </div>

      {/* Related Posts */}
      {post.related_posts && post.related_posts.length > 0 && (
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center">
            <span className="bg-emerald-100 text-emerald-700 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
              </svg>
            </span>
            You May Also Like
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {post.related_posts.map(relatedPost => (
              <div key={relatedPost.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-all hover:-translate-y-1">
                <div className="p-5">
                  <h4 className="text-lg font-semibold mb-2 line-clamp-2">
                    <Link
                      href={`/blog/${relatedPost.slug}`}
                      className="text-gray-800 hover:text-emerald-600"
                    >
                      {relatedPost.title}
                    </Link>
                  </h4>

                  {relatedPost.excerpt && (
                    <p className="text-gray-600 mb-4 line-clamp-2">{relatedPost.excerpt}</p>
                  )}

                  <div className="flex justify-between items-center">
                    <Link
                      href={`/blog/${relatedPost.slug}`}
                      className="text-emerald-600 hover:text-emerald-700 font-medium flex items-center"
                    >
                      Read Article <span className="ml-1">→</span>
                    </Link>

                    <div className="flex items-center text-gray-500 text-sm">
                      <FiClock className="mr-1" />
                      <span>{relatedPost.reading_time || 2} min read</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 text-center">
            <Link
              href="/blog"
              className="inline-flex items-center justify-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
            >
              Explore More Articles
            </Link>
          </div>
        </div>
      )}

      {/* Back to blog link */}
      <div className="mt-8">
        <Link href="/blog" className="text-emerald-600 hover:text-emerald-700 flex items-center">
          <FiArrowLeft className="mr-2" /> Back to Blog
        </Link>
      </div>
    </footer>
  );
};

export default BlogPostFooter;
