"use strict";(()=>{var e={};e.id=9505,e.ids=[9505],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},86671:(e,r,t)=>{t.r(r),t.d(r,{default:()=>i});var s=t(37413),a=t(4536),n=t.n(a),o=t(73993);let l=[{name:"stress relief",slug:"stress-relief",post_count:1},{name:"acupuncture",slug:"acupuncture",post_count:1},{name:"holistic health",slug:"holistic-health",post_count:3},{name:"herbal remedies",slug:"herbal-remedies",post_count:1},{name:"digestive health",slug:"digestive-health",post_count:1},{name:"mental health",slug:"mental-health",post_count:1},{name:"nutrition",slug:"nutrition",post_count:1},{name:"natural medicine",slug:"natural-medicine",post_count:2},{name:"gut health",slug:"gut-health",post_count:2},{name:"relaxation",slug:"relaxation",post_count:1},{name:"brain health",slug:"brain-health",post_count:1},{name:"diet",slug:"diet",post_count:1}];function i(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Blog Tags"}),(0,s.jsx)("p",{className:"text-lg max-w-3xl",children:"Browse articles by topic to find the information you're looking for."})]})}),(0,s.jsx)("div",{className:"bg-white shadow-md",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:"Search tags",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"}),(0,s.jsx)(o.CKj,{className:"absolute left-3 top-3 text-gray-400"})]})})})}),(0,s.jsx)("div",{className:"py-12 bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-8",children:"All Tags"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-4",children:l.map((e,r)=>{let t=e.name.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");return(0,s.jsxs)(n(),{href:`/blog/tags/${e.slug}`,className:"bg-white border border-gray-200 hover:border-emerald-300 hover:bg-emerald-50 px-4 py-3 rounded-lg text-gray-700 flex items-center group",children:[(0,s.jsx)(o.cnX,{className:"mr-2 text-emerald-500 group-hover:text-emerald-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium block",children:t}),(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:[e.post_count," ",1===e.post_count?"article":"articles"]})]})]},r)})})]})}),(0,s.jsx)("div",{className:"py-12 bg-white",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Popular Tags"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-3",children:l.sort((e,r)=>r.post_count-e.post_count).slice(0,6).map((e,r)=>{let t=e.name.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");return(0,s.jsxs)(n(),{href:`/blog/tags/${e.slug}`,className:"bg-emerald-50 text-emerald-700 px-4 py-2 rounded-full hover:bg-emerald-100 flex items-center",children:[(0,s.jsx)(o.cnX,{className:"mr-2"}),t," (",e.post_count,")"]},r)})})]})}),(0,s.jsx)("div",{className:"py-8 bg-gray-50",children:(0,s.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,s.jsx)(n(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"← Back to Blog"})})})]})}},94735:e=>{e.exports=require("events")},99586:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>i});var s=t(65239),a=t(48088),n=t(31369),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let i={children:["",{children:["blog",{children:["tags",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,86671)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\tags\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\tags\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/blog/tags/page",pathname:"/blog/tags",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,1330,3376,6391,2975,8446,270],()=>t(99586));module.exports=s})();