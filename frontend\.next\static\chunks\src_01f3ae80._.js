(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/strapiAuth.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAuthenticatedAPI": (()=>createAuthenticatedAPI),
    "strapiAuth": (()=>strapiAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
// Define the base URL for Strapi API
const API_URL = ("TURBOPACK compile-time value", "http://localhost:1337") || 'http://localhost:1337';
// Create an axios instance for authentication requests
const authAPI = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
const strapiAuth = {
    // Register a new user
    register: async (username, email, password)=>{
        try {
            const response = await authAPI.post('/api/auth/local/register', {
                username,
                email,
                password
            });
            // Store the JWT token in localStorage
            if (response.data.jwt) {
                localStorage.setItem('jwt', response.data.jwt);
                localStorage.setItem('user', JSON.stringify(response.data.user));
            }
            return {
                data: response.data,
                error: null
            };
        } catch (error) {
            return {
                data: null,
                error: error.response?.data?.error || {
                    message: 'An error occurred during registration'
                }
            };
        }
    },
    // Login an existing user
    login: async (identifier, password)=>{
        try {
            const response = await authAPI.post('/api/auth/local', {
                identifier,
                password
            });
            // Store the JWT token in localStorage
            if (response.data.jwt) {
                localStorage.setItem('jwt', response.data.jwt);
                localStorage.setItem('user', JSON.stringify(response.data.user));
            }
            return {
                data: response.data,
                error: null
            };
        } catch (error) {
            return {
                data: null,
                error: error.response?.data?.error || {
                    message: 'Invalid credentials'
                }
            };
        }
    },
    // Logout the current user
    logout: ()=>{
        localStorage.removeItem('jwt');
        localStorage.removeItem('user');
        return {
            error: null
        };
    },
    // Get the current user from localStorage
    getCurrentUser: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        const jwt = localStorage.getItem('jwt');
        const user = localStorage.getItem('user');
        if (!jwt || !user) {
            return {
                user: null
            };
        }
        try {
            return {
                user: JSON.parse(user)
            };
        } catch  {
            return {
                user: null
            };
        }
    },
    // Check if the user is authenticated
    isAuthenticated: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        const jwt = localStorage.getItem('jwt');
        return !!jwt;
    },
    // Get the JWT token
    getToken: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return localStorage.getItem('jwt');
    },
    // Forgot password
    forgotPassword: async (email)=>{
        try {
            const response = await authAPI.post('/api/auth/forgot-password', {
                email
            });
            return {
                data: response.data,
                error: null
            };
        } catch (error) {
            return {
                data: null,
                error: error.response?.data?.error || {
                    message: 'An error occurred during password reset request'
                }
            };
        }
    },
    // Reset password
    resetPassword: async (code, password, passwordConfirmation)=>{
        try {
            const response = await authAPI.post('/api/auth/reset-password', {
                code,
                password,
                passwordConfirmation
            });
            // Store the JWT token in localStorage if the response includes it
            if (response.data.jwt) {
                localStorage.setItem('jwt', response.data.jwt);
                localStorage.setItem('user', JSON.stringify(response.data.user));
            }
            return {
                data: response.data,
                error: null
            };
        } catch (error) {
            return {
                data: null,
                error: error.response?.data?.error || {
                    message: 'An error occurred during password reset'
                }
            };
        }
    }
};
const createAuthenticatedAPI = ()=>{
    const token = strapiAuth.getToken();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
        baseURL: API_URL,
        headers: {
            'Content-Type': 'application/json',
            ...token ? {
                Authorization: `Bearer ${token}`
            } : {}
        }
    });
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapiAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/strapiAuth.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isAuthenticated, setIsAuthenticated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            // Check for active session on initial load
            const initializeAuth = {
                "AuthProvider.useEffect.initializeAuth": async ()=>{
                    setIsLoading(true);
                    try {
                        // Get user from localStorage (set during login)
                        const { user } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapiAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["strapiAuth"].getCurrentUser();
                        setUser(user);
                        setIsAuthenticated(!!user);
                    } catch (error) {
                        console.error('Error loading user:', error);
                        setUser(null);
                        setIsAuthenticated(false);
                    } finally{
                        setIsLoading(false);
                    }
                }
            }["AuthProvider.useEffect.initializeAuth"];
            initializeAuth();
            // Add event listener for storage changes (for multi-tab support)
            const handleStorageChange = {
                "AuthProvider.useEffect.handleStorageChange": ()=>{
                    const { user } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapiAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["strapiAuth"].getCurrentUser();
                    setUser(user);
                    setIsAuthenticated(!!user);
                    router.refresh();
                }
            }["AuthProvider.useEffect.handleStorageChange"];
            window.addEventListener('storage', handleStorageChange);
            // Clean up event listener on unmount
            return ({
                "AuthProvider.useEffect": ()=>{
                    window.removeEventListener('storage', handleStorageChange);
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], [
        router
    ]);
    const signIn = async (identifier, password)=>{
        setIsLoading(true);
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapiAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["strapiAuth"].login(identifier, password);
        if (data) {
            setUser(data.user);
            setIsAuthenticated(true);
            router.refresh();
        }
        setIsLoading(false);
        return {
            error
        };
    };
    const signUp = async (username, email, password)=>{
        setIsLoading(true);
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapiAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["strapiAuth"].register(username, email, password);
        if (data) {
            setUser(data.user);
            setIsAuthenticated(true);
            router.refresh();
        }
        setIsLoading(false);
        return {
            error
        };
    };
    const signOut = ()=>{
        setIsLoading(true);
        const { error } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapiAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["strapiAuth"].logout();
        setUser(null);
        setIsAuthenticated(false);
        router.refresh();
        setIsLoading(false);
        return {
            error
        };
    };
    const forgotPassword = async (email)=>{
        setIsLoading(true);
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapiAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["strapiAuth"].forgotPassword(email);
        setIsLoading(false);
        return {
            error
        };
    };
    const resetPassword = async (code, password, passwordConfirmation)=>{
        setIsLoading(true);
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapiAuth$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["strapiAuth"].resetPassword(code, password, passwordConfirmation);
        if (data) {
            setUser(data.user);
            setIsAuthenticated(true);
            router.refresh();
        }
        setIsLoading(false);
        return {
            error
        };
    };
    const value = {
        user,
        isLoading,
        signIn,
        signUp,
        signOut,
        forgotPassword,
        resetPassword,
        isAuthenticated
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 135,
        columnNumber: 10
    }, this);
}
_s(AuthProvider, "CKszQgnBAx9Vbx+r2b8vusIZik0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/ErrorContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ErrorProvider": (()=>ErrorProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useError": (()=>useError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const ErrorContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const ErrorProvider = ({ children })=>{
    _s();
    const [globalError, setGlobalError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [errorLogs, setErrorLogs] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const clearGlobalError = ()=>{
        setGlobalError(null);
    };
    const addErrorLog = (error, source)=>{
        const newErrorLog = {
            id: Date.now().toString(),
            error,
            timestamp: new Date(),
            source
        };
        // Keep only the last 10 errors to avoid memory issues
        setErrorLogs((prevLogs)=>[
                newErrorLog,
                ...prevLogs
            ].slice(0, 10));
        // Log to console in development
        if ("TURBOPACK compile-time truthy", 1) {
            console.error(`Error from ${source || 'unknown source'}:`, error);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ErrorContext.Provider, {
        value: {
            globalError,
            setGlobalError,
            clearGlobalError,
            addErrorLog,
            errorLogs
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ErrorContext.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
};
_s(ErrorProvider, "V5XjAwONPbE0S7FSu2ON5sbPF64=");
_c = ErrorProvider;
const useError = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ErrorContext);
    if (context === undefined) {
        throw new Error('useError must be used within an ErrorProvider');
    }
    return context;
};
_s1(useError, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const __TURBOPACK__default__export__ = ErrorContext;
var _c;
__turbopack_context__.k.register(_c, "ErrorProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/auth/UserAccountDropdown.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>UserAccountDropdown)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fi/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
function UserAccountDropdown() {
    _s();
    const { user, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Close dropdown when clicking outside
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "UserAccountDropdown.useEffect": ()=>{
            function handleClickOutside(event) {
                if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                    setIsOpen(false);
                }
            }
            document.addEventListener('mousedown', handleClickOutside);
            return ({
                "UserAccountDropdown.useEffect": ()=>{
                    document.removeEventListener('mousedown', handleClickOutside);
                }
            })["UserAccountDropdown.useEffect"];
        }
    }["UserAccountDropdown.useEffect"], []);
    const handleSignOut = async ()=>{
        await signOut();
        setIsOpen(false);
    };
    // Truncate email for display if needed
    const displayEmail = user?.email && user.email.length > 20 ? `${user.email.substring(0, 17)}...` : user?.email;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        ref: dropdownRef,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>setIsOpen(!isOpen),
                className: "flex items-center text-gray-700 hover:text-emerald-600 focus:outline-none",
                "aria-expanded": isOpen,
                "aria-haspopup": "true",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center mr-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiUser"], {
                            className: "text-emerald-600"
                        }, void 0, false, {
                            fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                            lineNumber: 46,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                        lineNumber: 45,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "hidden md:block max-w-[150px] truncate",
                        children: displayEmail
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                        lineNumber: 48,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiChevronDown"], {
                        className: `ml-1 transition-transform ${isOpen ? 'rotate-180' : ''}`
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                        lineNumber: 49,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                lineNumber: 39,
                columnNumber: 7
            }, this),
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        href: "/account",
                        className: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center",
                        onClick: ()=>setIsOpen(false),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiUser"], {
                                className: "mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                                lineNumber: 59,
                                columnNumber: 13
                            }, this),
                            "My Account"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                        lineNumber: 54,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleSignOut,
                        className: "w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiLogOut"], {
                                className: "mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                                lineNumber: 66,
                                columnNumber: 13
                            }, this),
                            "Sign Out"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                        lineNumber: 62,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
                lineNumber: 53,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/auth/UserAccountDropdown.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
_s(UserAccountDropdown, "anTC7vTGNCLAaDQv8sSL8QS7sIs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = UserAccountDropdown;
var _c;
__turbopack_context__.k.register(_c, "UserAccountDropdown");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/imageLoader.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/lib/imageLoader.js
/**
 * High-performance image loader for Next.js 15 and Strapi 5
 *
 * Advanced features:
 * - Adaptive format selection (AVIF/WebP) based on browser support
 * - Progressive image loading with optimal quality settings
 * - Automatic responsive sizing with device-pixel-ratio awareness
 * - Smart caching with deterministic URL parameters
 * - Bandwidth-aware quality optimization
 * - Special handling for SVGs, GIFs, and animated content
 * - Performance monitoring with detailed metrics
 * - Blur-up placeholder support for improved perceived performance
 *
 * @param {object} params - The parameters for the loader.
 * @param {string} params.src - The source URL of the image.
 * @param {number} params.width - The requested width of the image.
 * @param {number} [params.quality] - The requested quality of the image.
 * @returns {string} The optimized image URL.
 */ // Performance metric collection
__turbopack_context__.s({
    "default": (()=>optimizedImageLoader),
    "imageUtils": (()=>imageUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const metrics = {
    count: 0,
    errors: 0,
    totalTime: 0,
    slowestTime: 0,
    slowestImage: ''
};
// Cache for dimension detection to avoid layout shifts
const imageDimensionsCache = new Map();
// Environment configuration with defaults
const CONFIG = {
    enableMetrics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_CACHE_METRICS === 'true',
    useHighQuality: ("TURBOPACK compile-time value", "true") === 'true',
    disableOptimization: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION === 'true',
    defaultQuality: ("TURBOPACK compile-time truthy", 1) ? 85 : ("TURBOPACK unreachable", undefined),
    avifQuality: ("TURBOPACK compile-time truthy", 1) ? 80 : ("TURBOPACK unreachable", undefined),
    webpQuality: ("TURBOPACK compile-time truthy", 1) ? 85 : ("TURBOPACK unreachable", undefined),
    jpegQuality: ("TURBOPACK compile-time truthy", 1) ? 90 : ("TURBOPACK unreachable", undefined),
    pngQuality: ("TURBOPACK compile-time truthy", 1) ? 90 : ("TURBOPACK unreachable", undefined),
    maxDevicePixelRatio: 3,
    minWidth: 20,
    blurUpRadius: 10
};
/**
 * Determines if an image should be optimized or passed through as-is
 */ function shouldOptimizeImage(src) {
    if (!src || CONFIG.disableOptimization) return false;
    // Skip optimization for these formats
    const skipFormats = [
        '.svg',
        '.gif',
        '.webp',
        '.avif'
    ];
    if (typeof src === 'string' && skipFormats.some((format)=>src.toLowerCase().endsWith(format))) {
        return false;
    }
    // Skip optimization for external domains we don't control
    if (src.startsWith('http') && !src.includes('strapiapp.com') && !src.includes('localhost:1337')) {
        return false;
    }
    return true;
}
/**
 * Determines the optimal quality setting based on the image format
 */ function getOptimalQuality(src, requestedQuality) {
    if (requestedQuality) return requestedQuality;
    // Format-specific quality settings
    if (src.toLowerCase().match(/\.avif$/i)) return CONFIG.avifQuality;
    if (src.toLowerCase().match(/\.webp$/i)) return CONFIG.webpQuality;
    if (src.toLowerCase().match(/\.jpe?g$/i)) return CONFIG.jpegQuality;
    if (src.toLowerCase().match(/\.png$/i)) return CONFIG.pngQuality;
    // Default quality based on whether the image appears to be a photo
    const isPhoto = src.toLowerCase().match(/\.(jpe?g|png)$/i);
    return isPhoto ? CONFIG.jpegQuality : CONFIG.webpQuality;
}
/**
 * Constructs an optimized image URL
 */ function buildOptimizedUrl(src, width, quality) {
    // Determine base URL for media with fallbacks
    const strapiMediaUrl = ("TURBOPACK compile-time value", "https://nice-badge-2130241d6c.media.strapiapp.com") || ("TURBOPACK compile-time value", "https://nice-badge-2130241d6c.strapiapp.com") || 'https://nice-badge-2130241d6c.media.strapiapp.com';
    // Get optimal quality setting
    const finalQuality = getOptimalQuality(src, quality);
    // Calculate optimal width based on device pixel ratio and requested width
    // This creates sharper images on high-DPI screens
    const devicePixelRatio = ("TURBOPACK compile-time truthy", 1) ? Math.min(window.devicePixelRatio || 1, CONFIG.maxDevicePixelRatio) : ("TURBOPACK unreachable", undefined);
    // Never optimize below minimum width
    if (width < CONFIG.minWidth) {
        return src;
    }
    // Create a proper URL object for manipulation
    try {
        const optimalWidth = Math.round(width * devicePixelRatio);
        const url = new URL(src.startsWith('http') ? src : `${strapiMediaUrl}${src.startsWith('/') ? src : `/${src}`}`);
        // Only add optimization parameters for Strapi domains we control
        if (url.hostname.includes('strapiapp.com') || url.hostname.includes('localhost')) {
            // Use content-aware sizing (never upscale small images)
            url.searchParams.set('w', optimalWidth.toString());
            url.searchParams.set('q', finalQuality.toString());
            // Check if original is a photo (high entropy) or illustration (low entropy)
            const isPhoto = src.toLowerCase().match(/\.(jpe?g|png)$/i);
            if (!url.searchParams.has('format')) {
                // Prefer AVIF for photos for better compression, WebP for everything else
                const preferredFormat = isPhoto ? 'avif' : 'webp';
                url.searchParams.set('format', preferredFormat);
            }
            // Add cache-busting parameter only in development to ensure fresh images
            if ("TURBOPACK compile-time truthy", 1) {
                url.searchParams.set('t', Date.now().toString());
            } else {
                "TURBOPACK unreachable";
            }
            // Apply filters for image optimization if needed
            if (isPhoto) {
                // Apply minimal sharpening for photos
                url.searchParams.set('sharp', '10');
            }
            // Ensure HTTPS for production
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            return url.toString();
        }
        return url.toString();
    } catch (e) {
        // If URL parsing fails, construct URL manually as fallback
        if (src.startsWith('/')) {
            return `${strapiMediaUrl}${src}?w=${width}&q=${finalQuality}`;
        }
        // Last resort: return original source
        return src;
    }
}
function optimizedImageLoader({ src, width, quality }) {
    // Start performance monitoring
    const startTime = CONFIG.enableMetrics ? performance.now() : 0;
    // Handle empty source gracefully
    if (!src) return '';
    try {
        // Determine if we should optimize this image
        if (!shouldOptimizeImage(src)) {
            return src; // Pass through without optimization
        }
        // Build optimized URL
        const finalUrl = buildOptimizedUrl(src, width, quality);
        // Performance monitoring in development or when metrics are enabled
        if (CONFIG.enableMetrics && startTime) {
            const duration = performance.now() - startTime;
            metrics.count++;
            metrics.totalTime += duration;
            if (duration > metrics.slowestTime) {
                metrics.slowestTime = duration;
                metrics.slowestImage = src;
            }
            // Log periodically in development
            if (("TURBOPACK compile-time value", "development") === 'development' && metrics.count % 10 === 0) {
                console.log(`🖼️ Image optimization metrics:
- Processed: ${metrics.count} images
- Avg time: ${(metrics.totalTime / metrics.count).toFixed(2)}ms
- Errors: ${metrics.errors}
- Slowest: ${metrics.slowestTime.toFixed(2)}ms (${metrics.slowestImage.substring(0, 50)}...)
`);
            }
        }
        return finalUrl;
    } catch (error) {
        // Increment error counter
        if (CONFIG.enableMetrics) {
            metrics.errors++;
        }
        // Log error in development
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn('Image loader error:', error, 'for src:', src);
        }
        // Safety fallback
        return src;
    }
}
const imageUtils = {
    getBlurDataUrl: (src, size = 10)=>{
        if (!src) return '';
        // Generate tiny placeholder for blur-up effect
        return `${buildOptimizedUrl(src, size, 10)}&blur=80`;
    },
    preloadImage: (src, width)=>{
        if ("object" === 'undefined' || !src) return;
        const img = new Image();
        img.src = optimizedImageLoader({
            src,
            width,
            quality: CONFIG.defaultQuality
        });
        return img;
    },
    resetMetrics: ()=>{
        metrics.count = 0;
        metrics.errors = 0;
        metrics.totalTime = 0;
        metrics.slowestTime = 0;
        metrics.slowestImage = '';
    },
    getMetrics: ()=>({
            ...metrics
        })
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/shared/LazyImage.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$imageLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/imageLoader.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
/**
 * High-Performance LazyImage component with advanced optimizations
 * - Uses next/image with additional performance enhancements
 * - Progressive loading with intelligent blur-up technique
 * - Smart preloading for critical images
 * - Avoids layout shifts with aspect ratio preservation
 * - Efficient resource loading with modern browser hints
 * - Advanced error handling with graceful degradation
 * - Zero overhead when fully loaded
 */ const LazyImage = ({ src, alt, width, height, className = '', fallbackClassName = '', showPlaceholder = true, advancedBlur = false, preload = false, fadeIn = true, wrapperAs = 'div', fillContainer = false, sizes = '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw', style, priority = false, qualityOverride, ...rest })=>{
    _s();
    // Track component mounted state to prevent state updates after unmount
    const isMounted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    // Single state to track loading status: "loading" | "loaded" | "error"
    const [loadState, setLoadState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(priority ? 'loaded' : 'loading' // Assume priority images are already loaded for better UX
    );
    // Keep track of actual dimensions for aspect ratio calculations
    // If fillContainer is true, width/height props are for aspect ratio, not fixed size.
    const [dimensions, setDimensions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        width: fillContainer ? undefined : width,
        height: fillContainer ? undefined : height
    });
    // Process source with enhanced handling for various formats
    const imageSrc = typeof src === 'string' ? src : src?.src || src?.url || src?.default?.src || null;
    // Advanced blur data URL generation using our optimized loader
    const blurDataURL = advancedBlur && showPlaceholder && imageSrc ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$imageLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["imageUtils"].getBlurDataUrl(imageSrc, 20) // Generate an optimized tiny preview image
     : showPlaceholder ? 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+' : undefined;
    // Handle preloading for important images that are just outside the viewport
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LazyImage.useEffect": ()=>{
            if (preload && imageSrc && !priority && "object" !== 'undefined') {
                const img = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$imageLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["imageUtils"].preloadImage(imageSrc, typeof width === 'number' ? width : 500);
                // When preloaded, update state if still mounted
                if (img) {
                    img.onload = ({
                        "LazyImage.useEffect": ()=>{
                            if (isMounted.current) {
                                setLoadState('loaded');
                            }
                        }
                    })["LazyImage.useEffect"];
                    img.onerror = ({
                        "LazyImage.useEffect": ()=>{
                            if (isMounted.current) {
                                setLoadState('error');
                            }
                        }
                    })["LazyImage.useEffect"];
                }
            }
            // Clean up on unmount
            return ({
                "LazyImage.useEffect": ()=>{
                    isMounted.current = false;
                }
            })["LazyImage.useEffect"];
        }
    }["LazyImage.useEffect"], [
        imageSrc,
        preload,
        priority,
        width
    ]);
    // Calculate aspect ratio for responsive images
    const aspectRatio = typeof width === 'number' && typeof height === 'number' && width > 0 && height > 0 ? width / height : undefined;
    // Memoized callbacks to prevent recreating functions on each render
    const handleLoad = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LazyImage.useCallback[handleLoad]": (event)=>{
            // Update with actual loaded dimensions to prevent layout shift, only if not in fillContainer mode
            if (!fillContainer && event?.target) {
                const { naturalWidth, naturalHeight } = event.target;
                if (naturalWidth && naturalHeight) {
                    if (isMounted.current) {
                        setDimensions({
                            width: naturalWidth,
                            height: naturalHeight
                        });
                    }
                }
            }
            if (isMounted.current) {
                setLoadState('loaded');
            }
        }
    }["LazyImage.useCallback[handleLoad]"], [
        fillContainer
    ]);
    const handleError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LazyImage.useCallback[handleError]": ()=>{
            setLoadState('error');
            // Only log in development to avoid excessive logs in production
            if ("TURBOPACK compile-time truthy", 1) {
                console.error('Image failed to load:', {
                    src: imageSrc,
                    alt
                });
            }
        }
    }["LazyImage.useCallback[handleError]"], [
        imageSrc,
        alt
    ]);
    // Calculate loading strategy based on priority
    const loadingStrategy = priority ? 'eager' : 'lazy';
    // Calculate placeholder strategy
    const placeholderStrategy = showPlaceholder ? 'blur' : 'empty';
    // Render fallback for errors or missing source
    if (loadState === 'error' || !imageSrc) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `flex items-center justify-center bg-gray-100 ${fallbackClassName || (fillContainer ? '' : className)}`,
            style: {
                width: fillContainer ? '100%' : width,
                height: fillContainer ? '100%' : height,
                aspectRatio: aspectRatio ? `${aspectRatio}` : undefined,
                ...style
            },
            role: "img",
            "aria-label": alt || 'Image failed to load',
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiImage"], {
                className: "text-gray-400 w-1/5 h-1/5"
            }, void 0, false, {
                fileName: "[project]/src/components/shared/LazyImage.tsx",
                lineNumber: 187,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/shared/LazyImage.tsx",
            lineNumber: 176,
            columnNumber: 7
        }, this);
    }
    // Combine transition classes for smoother loading experience
    const imageClasses = [
        className,
        loadState === 'loaded' ? 'opacity-100' : 'opacity-0',
        fadeIn ? 'transition-opacity duration-300' : ''
    ].filter(Boolean).join(' ');
    // Create responsive style object with aspect ratio preservation
    const responsiveStyle = {
        objectFit: style?.objectFit || 'cover',
        aspectRatio: fillContainer ? undefined : aspectRatio ? `${aspectRatio}` : undefined,
        ...style,
        width: fillContainer ? undefined : style?.width,
        height: fillContainer ? undefined : style?.height
    };
    // Image render with optimized props
    const imageElement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        src: imageSrc,
        alt: alt || '',
        width: fillContainer ? undefined : dimensions.width,
        height: fillContainer ? undefined : dimensions.height,
        fill: fillContainer,
        className: imageClasses,
        loading: loadingStrategy,
        fetchPriority: priority ? "high" : preload ? "low" : "auto",
        priority: priority,
        sizes: sizes,
        style: responsiveStyle,
        placeholder: placeholderStrategy,
        blurDataURL: blurDataURL,
        onLoad: handleLoad,
        onError: handleError,
        quality: qualityOverride,
        ...rest
    }, void 0, false, {
        fileName: "[project]/src/components/shared/LazyImage.tsx",
        lineNumber: 210,
        columnNumber: 5
    }, this);
    // Wrapper element (div or picture) with appropriate ARIA attributes
    const WrapperElement = wrapperAs;
    const wrapperStyle = fillContainer ? {
        width: '100%',
        height: '100%',
        position: 'relative',
        ...style
    } // Ensure parent has dimensions
     : {
        width: dimensions.width,
        height: dimensions.height,
        aspectRatio: aspectRatio ? `${aspectRatio}` : undefined,
        position: 'relative',
        ...style
    };
    // If fillContainer, className from props should apply to the wrapper for sizing, not the image.
    // However, className often contains object-fit, which should apply to the image.
    // This is tricky. For now, className from props is passed to Image, assuming it contains object-fit.
    // If fillContainer, the wrapper takes full size of its parent.
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(WrapperElement, {
        className: `relative ${fillContainer ? 'w-full h-full' : ''}`,
        style: wrapperStyle,
        children: [
            imageElement,
            loadState === 'loading' && showPlaceholder && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `absolute inset-0 bg-gray-100 animate-pulse ${fallbackClassName || ''}`,
                style: {
                    width: '100%',
                    height: '100%'
                },
                "aria-hidden": "true"
            }, void 0, false, {
                fileName: "[project]/src/components/shared/LazyImage.tsx",
                lineNumber: 252,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/shared/LazyImage.tsx",
        lineNumber: 244,
        columnNumber: 5
    }, this);
};
_s(LazyImage, "IK3L9LU4cd3i3sXdsS/6FhZXd2c=");
_c = LazyImage;
// Use displayName for better debugging
LazyImage.displayName = 'LazyImage';
const __TURBOPACK__default__export__ = /*#__PURE__*/ _c1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(LazyImage);
var _c, _c1;
__turbopack_context__.k.register(_c, "LazyImage");
__turbopack_context__.k.register(_c1, "%default%");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/apiUtils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * API utilities for making authenticated requests to Strapi
 * Handles both client-side and server-side requests
 */ __turbopack_context__.s({
    "createClientSideApiClient": (()=>createClientSideApiClient),
    "createServerSideApiClient": (()=>createServerSideApiClient),
    "deleteFromApi": (()=>deleteFromApi),
    "fetchFromApi": (()=>fetchFromApi),
    "postToApi": (()=>postToApi),
    "putToApi": (()=>putToApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
// Get Strapi URL from environment variable
const API_URL = ("TURBOPACK compile-time value", "https://nice-badge-2130241d6c.strapiapp.com") || 'http://localhost:1337';
const API_PATH = '/api';
const createServerSideApiClient = ()=>{
    const API_TOKEN = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.STRAPI_API_TOKEN;
    if (!API_TOKEN) {
        console.warn('No STRAPI_API_TOKEN found in environment variables for server-side API client');
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
        baseURL: `${API_URL}${API_PATH}`,
        headers: {
            'Content-Type': 'application/json',
            ...API_TOKEN ? {
                Authorization: `Bearer ${API_TOKEN}`
            } : {}
        }
    });
};
const createClientSideApiClient = ()=>{
    // Only access localStorage in the browser
    const getToken = ()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            return localStorage.getItem('jwt');
        }
        "TURBOPACK unreachable";
    };
    const token = getToken();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
        baseURL: `${API_URL}${API_PATH}`,
        headers: {
            'Content-Type': 'application/json',
            ...token ? {
                Authorization: `Bearer ${token}`
            } : {}
        }
    });
};
const fetchFromApi = async (endpoint, options = {}, isServerSide = "object" === 'undefined')=>{
    try {
        const apiClient = isServerSide ? createServerSideApiClient() : createClientSideApiClient();
        const response = await apiClient.get(endpoint, options);
        return response.data;
    } catch (error) {
        console.error(`Error fetching from API (${endpoint}):`, error);
        if (error.response) {
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
        }
        throw error;
    }
};
const postToApi = async (endpoint, data, options = {}, isServerSide = "object" === 'undefined')=>{
    try {
        const apiClient = isServerSide ? createServerSideApiClient() : createClientSideApiClient();
        const response = await apiClient.post(endpoint, data, options);
        return response.data;
    } catch (error) {
        console.error(`Error posting to API (${endpoint}):`, error);
        if (error.response) {
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
        }
        throw error;
    }
};
const putToApi = async (endpoint, data, options = {}, isServerSide = "object" === 'undefined')=>{
    try {
        const apiClient = isServerSide ? createServerSideApiClient() : createClientSideApiClient();
        const response = await apiClient.put(endpoint, data, options);
        return response.data;
    } catch (error) {
        console.error(`Error putting to API (${endpoint}):`, error);
        if (error.response) {
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
        }
        throw error;
    }
};
const deleteFromApi = async (endpoint, options = {}, isServerSide = "object" === 'undefined')=>{
    try {
        const apiClient = isServerSide ? createServerSideApiClient() : createClientSideApiClient();
        const response = await apiClient.delete(endpoint, options);
        return response.data;
    } catch (error) {
        console.error(`Error deleting from API (${endpoint}):`, error);
        if (error.response) {
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
        }
        throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/enhancedApiUtils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "enhancedDeleteFromApi": (()=>enhancedDeleteFromApi),
    "enhancedFetchFromApi": (()=>enhancedFetchFromApi),
    "enhancedPostToApi": (()=>enhancedPostToApi),
    "enhancedPutToApi": (()=>enhancedPutToApi),
    "formatApiError": (()=>formatApiError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiUtils.ts [app-client] (ecmascript)");
;
;
// Maximum number of retries for transient errors
const MAX_RETRIES = 2;
// Delay between retries in milliseconds (exponential backoff)
const getRetryDelay = (retryCount)=>Math.pow(2, retryCount) * 1000;
// Error types that might be transient and worth retrying
const TRANSIENT_ERROR_CODES = [
    408,
    429,
    500,
    502,
    503,
    504
];
/**
 * Determines if an error is likely transient and worth retrying
 */ const isTransientError = (error)=>{
    if (!error.response) {
        // Network errors are often transient
        return true;
    }
    return TRANSIENT_ERROR_CODES.includes(error.response.status);
};
const enhancedFetchFromApi = async (endpoint, options = {}, isServerSide = "object" === 'undefined', retryCount = 0)=>{
    try {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchFromApi"])(endpoint, options, isServerSide);
    } catch (error) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && isTransientError(error) && retryCount < MAX_RETRIES) {
            // Wait before retrying (exponential backoff)
            const delay = getRetryDelay(retryCount);
            console.warn(`Retrying API request to ${endpoint} after ${delay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`);
            await new Promise((resolve)=>setTimeout(resolve, delay));
            // Retry the request
            return enhancedFetchFromApi(endpoint, options, isServerSide, retryCount + 1);
        }
        // Format error for better debugging
        const formattedError = formatApiError(error, endpoint);
        throw formattedError;
    }
};
const enhancedPostToApi = async (endpoint, data, options = {}, isServerSide = "object" === 'undefined', retryCount = 0)=>{
    try {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["postToApi"])(endpoint, data, options, isServerSide);
    } catch (error) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && isTransientError(error) && retryCount < MAX_RETRIES) {
            const delay = getRetryDelay(retryCount);
            console.warn(`Retrying API POST to ${endpoint} after ${delay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`);
            await new Promise((resolve)=>setTimeout(resolve, delay));
            return enhancedPostToApi(endpoint, data, options, isServerSide, retryCount + 1);
        }
        const formattedError = formatApiError(error, endpoint);
        throw formattedError;
    }
};
const formatApiError = (error, endpoint)=>{
    let errorMessage = `API Error (${endpoint}): `;
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
        if (error.response) {
            // Server responded with an error status
            const status = error.response.status;
            const data = error.response.data;
            errorMessage += `${status} - ${data?.error?.message || 'Unknown server error'}`;
            // Create a custom error with additional properties
            const customError = new Error(errorMessage);
            customError.status = status;
            customError.endpoint = endpoint;
            customError.responseData = data;
            return customError;
        } else if (error.request) {
            // Request was made but no response received
            errorMessage += 'No response received from server';
            const customError = new Error(errorMessage);
            customError.endpoint = endpoint;
            customError.isNetworkError = true;
            return customError;
        }
    }
    // For non-Axios errors
    errorMessage += error.message || 'Unknown error';
    return new Error(errorMessage);
};
const enhancedPutToApi = async (endpoint, data, options = {}, isServerSide = "object" === 'undefined')=>{
    try {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["putToApi"])(endpoint, data, options, isServerSide);
    } catch (error) {
        const formattedError = formatApiError(error, endpoint);
        throw formattedError;
    }
};
const enhancedDeleteFromApi = async (endpoint, options = {}, isServerSide = "object" === 'undefined')=>{
    try {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteFromApi"])(endpoint, options, isServerSide);
    } catch (error) {
        const formattedError = formatApiError(error, endpoint);
        throw formattedError;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/apiOptimization.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "batchRequests": (()=>batchRequests),
    "clearCache": (()=>clearCache),
    "clearCacheEntry": (()=>clearCacheEntry),
    "createFilterParams": (()=>createFilterParams),
    "createPaginationParams": (()=>createPaginationParams),
    "fetchWithCache": (()=>fetchWithCache),
    "generateCacheKey": (()=>generateCacheKey),
    "isCacheValid": (()=>isCacheValid),
    "prefetchData": (()=>prefetchData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$enhancedApiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/enhancedApiUtils.ts [app-client] (ecmascript)");
;
const apiCache = new Map();
function generateCacheKey(endpoint, params) {
    return `${endpoint}:${JSON.stringify(params || {})}`;
}
function isCacheValid(cacheEntry) {
    if (!cacheEntry) return false;
    const now = Date.now();
    const expiryTime = cacheEntry.timestamp + cacheEntry.ttl;
    return now < expiryTime;
}
async function fetchWithCache(endpoint, options = {}, ttl = 5 * 60 * 1000, bypassCache = false) {
    const cacheKey = generateCacheKey(endpoint, options.params);
    const cachedData = apiCache.get(cacheKey);
    // Return cached data if valid and not bypassing cache
    if (!bypassCache && isCacheValid(cachedData)) {
        return cachedData.data;
    }
    // Fetch fresh data
    const data = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$enhancedApiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["enhancedFetchFromApi"])(endpoint, options, "object" === 'undefined');
    // Cache the response
    apiCache.set(cacheKey, {
        data,
        timestamp: Date.now(),
        ttl
    });
    return data;
}
function clearCacheEntry(endpoint, params) {
    const cacheKey = generateCacheKey(endpoint, params);
    apiCache.delete(cacheKey);
}
function clearCache() {
    apiCache.clear();
}
async function prefetchData(endpoint, options = {}, ttl = 5 * 60 * 1000) {
    try {
        await fetchWithCache(endpoint, options, ttl, true);
    } catch (error) {
        console.error(`Error prefetching data from ${endpoint}:`, error);
    // Silently fail on prefetch errors
    }
}
async function batchRequests(requests) {
    return Promise.all(requests.map(({ endpoint, options = {} })=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$enhancedApiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["enhancedFetchFromApi"])(endpoint, options, "object" === 'undefined')));
}
function createPaginationParams(page = 1, pageSize = 10, fields = []) {
    const params = {
        pagination: {
            page,
            pageSize
        }
    };
    // Add fields to select if provided
    if (fields.length > 0) {
        params.fields = fields;
    }
    return params;
}
function createFilterParams(filters = {}, sort = [], fields = []) {
    const params = {};
    // Add filters if provided
    if (Object.keys(filters).length > 0) {
        params.filters = filters;
    }
    // Add sort if provided
    if (sort.length > 0) {
        params.sort = Array.isArray(sort) ? sort : [
            sort
        ];
    }
    // Add fields to select if provided
    if (fields.length > 0) {
        params.fields = fields;
    }
    return params;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/preload.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "preloadApiData": (()=>preloadApiData),
    "preloadApiDataWithCache": (()=>preloadApiDataWithCache),
    "preloadFont": (()=>preloadFont),
    "preloadImage": (()=>preloadImage),
    "preloadPage": (()=>preloadPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$enhancedApiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/enhancedApiUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiOptimization$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiOptimization.ts [app-client] (ecmascript)");
'use client';
;
;
function preloadApiData(queryClient, endpoint, params, queryKey) {
    // Use provided queryKey or default to [endpoint, params]
    const key = queryKey || [
        endpoint,
        params
    ];
    // Prefetch the data and store in React Query cache
    return queryClient.prefetchQuery({
        queryKey: key,
        queryFn: async ()=>{
            const options = {};
            if (params) {
                options.params = params;
            }
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$enhancedApiUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["enhancedFetchFromApi"])(endpoint, options, false);
        },
        // Cache for 5 minutes
        staleTime: 5 * 60 * 1000
    });
}
function preloadApiDataWithCache(endpoint, params, ttl = 5 * 60 * 1000) {
    const options = {};
    if (params) {
        options.params = params;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiOptimization$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prefetchData"])(endpoint, options, ttl);
}
function preloadImage(src) {
    return new Promise((resolve, reject)=>{
        const img = new Image();
        img.onload = ()=>resolve();
        img.onerror = reject;
        img.src = src;
    });
}
function preloadPage(path) {
    // Create a link element for prefetching
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = path;
    link.as = 'document';
    // Add to document head
    document.head.appendChild(link);
}
function preloadFont(fontUrl, fontFormat = 'woff2') {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = fontUrl;
    link.as = 'font';
    link.type = `font/${fontFormat}`;
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/shared/LinkWithPreload.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LinkWithPreload)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$preload$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/preload.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function LinkWithPreload({ href, children, className, prefetchApiEndpoint, prefetchApiParams, prefetchQueryKey, prefetchOnHover = true, prefetchOnMount = false, onClick, ariaLabel, title }) {
    _s();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [hasPrefetched, setHasPrefetched] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Function to handle preloading
    const handlePreload = async ()=>{
        if (hasPrefetched) return;
        try {
            // Preload the page
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$preload$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["preloadPage"])(href);
            // Preload API data if endpoint is provided
            if (prefetchApiEndpoint) {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$preload$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["preloadApiData"])(queryClient, prefetchApiEndpoint, prefetchApiParams, prefetchQueryKey);
            }
            setHasPrefetched(true);
        } catch (error) {
            console.error('Error preloading data:', error);
        // Don't throw - preloading errors shouldn't affect the user experience
        }
    };
    // Preload on mount if enabled
    if (prefetchOnMount && !hasPrefetched) {
        handlePreload();
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        href: href,
        className: className,
        "aria-label": ariaLabel,
        title: title,
        onClick: (e)=>{
            if (onClick) onClick();
        },
        onMouseEnter: ()=>{
            if (prefetchOnHover) {
                handlePreload();
            }
        },
        onTouchStart: ()=>{
            if (prefetchOnHover) {
                handlePreload();
            }
        },
        prefetch: false,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/shared/LinkWithPreload.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this);
}
_s(LinkWithPreload, "iT4xRuIfi4GVuP+ji1N2et+/Nvo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = LinkWithPreload;
var _c;
__turbopack_context__.k.register(_c, "LinkWithPreload");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/logger.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Utility for consistent logging throughout the application
 * Only logs in development mode by default
 */ /**
 * Log levels
 */ __turbopack_context__.s({
    "LogLevel": (()=>LogLevel),
    "configureLogger": (()=>configureLogger),
    "debug": (()=>debug),
    "default": (()=>__TURBOPACK__default__export__),
    "error": (()=>error),
    "info": (()=>info),
    "warn": (()=>warn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var LogLevel = /*#__PURE__*/ function(LogLevel) {
    LogLevel["DEBUG"] = "debug";
    LogLevel["INFO"] = "info";
    LogLevel["WARN"] = "warn";
    LogLevel["ERROR"] = "error";
    return LogLevel;
}({});
/**
 * Default configuration
 * Only enabled in development mode
 * Uses INFO level by default to reduce noise
 */ const defaultConfig = {
    enabled: ("TURBOPACK compile-time value", "development") === 'development',
    level: "info",
    prefix: '[NHN]'
};
/**
 * Current configuration
 */ let config = {
    ...defaultConfig
};
function configureLogger(newConfig) {
    config = {
        ...config,
        ...newConfig
    };
}
/**
 * Log a message at the specified level
 * @param level - Log level
 * @param message - Message to log
 * @param args - Additional arguments to log
 */ function log(level, message, ...args) {
    if (!config.enabled) return;
    const logLevels = Object.values(LogLevel);
    const configLevelIndex = logLevels.indexOf(config.level);
    const messageLevelIndex = logLevels.indexOf(level);
    // Only log if the message level is >= the configured level
    if (messageLevelIndex >= configLevelIndex) {
        const prefix = config.prefix ? `${config.prefix} ` : '';
        const formattedMessage = `${prefix}${message}`;
        switch(level){
            case "debug":
                console.debug(formattedMessage, ...args);
                break;
            case "info":
                console.info(formattedMessage, ...args);
                break;
            case "warn":
                console.warn(formattedMessage, ...args);
                break;
            case "error":
                console.error(formattedMessage, ...args);
                break;
        }
    }
}
function debug(message, ...args) {
    log("debug", message, ...args);
}
function info(message, ...args) {
    log("info", message, ...args);
}
function warn(message, ...args) {
    log("warn", message, ...args);
}
function error(message, ...args) {
    log("error", message, ...args);
}
const __TURBOPACK__default__export__ = {
    debug,
    info,
    warn,
    error,
    configure: configureLogger
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/mediaUtils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Optimized utility functions for handling media URLs and assets
 * Simplified for better performance and reliability
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "getFeaturedImageUrl": (()=>getFeaturedImageUrl),
    "getOgImageUrl": (()=>getOgImageUrl),
    "getProfilePictureUrl": (()=>getProfilePictureUrl),
    "getStrapiMediaPath": (()=>getStrapiMediaPath),
    "getStrapiMediaUrl": (()=>getStrapiMediaUrl),
    "sanitizeUrl": (()=>sanitizeUrl),
    "transformToSiteDomainUrl": (()=>transformToSiteDomainUrl)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/logger.ts [app-client] (ecmascript)");
;
// Environment variables for media handling
const ENV = {
    NEXT_PUBLIC_API_URL: ("TURBOPACK compile-time value", "https://nice-badge-2130241d6c.strapiapp.com"),
    NEXT_PUBLIC_STRAPI_API_URL: ("TURBOPACK compile-time value", "http://localhost:1337"),
    NEXT_PUBLIC_STRAPI_MEDIA_URL: ("TURBOPACK compile-time value", "https://nice-badge-2130241d6c.media.strapiapp.com"),
    NEXT_PUBLIC_SITE_URL: ("TURBOPACK compile-time value", "http://localhost:3000"),
    IMAGE_HOSTNAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.IMAGE_HOSTNAME,
    NODE_ENV: ("TURBOPACK compile-time value", "development")
};
// Resolve Strapi API URL with fallback
const EFFECTIVE_STRAPI_URL = ENV.NEXT_PUBLIC_STRAPI_API_URL || ENV.NEXT_PUBLIC_API_URL || (ENV.NODE_ENV === 'development' ? 'http://localhost:1337' : 'https://nice-badge-2130241d6c.strapiapp.com');
// Determine media URL with smarter defaults
const STRAPI_MEDIA_URL = (()=>{
    // Use explicit media URL if provided
    if (ENV.NEXT_PUBLIC_STRAPI_MEDIA_URL) {
        return ensureHttps(removeTrailingSlash(ENV.NEXT_PUBLIC_STRAPI_MEDIA_URL));
    }
    // Use IMAGE_HOSTNAME if provided
    if (ENV.IMAGE_HOSTNAME) {
        return ensureHttps(removeTrailingSlash(ENV.IMAGE_HOSTNAME));
    }
    // Derive from API URL
    try {
        const apiUrl = new URL(EFFECTIVE_STRAPI_URL);
        // For Strapi Cloud, generate media URL
        if (apiUrl.hostname.endsWith('strapiapp.com')) {
            return `${ensureHttps(apiUrl.protocol)}//${apiUrl.hostname.replace('strapiapp.com', 'media.strapiapp.com')}`;
        }
        // For other URLs, use as-is
        return ensureHttps(removeTrailingSlash(EFFECTIVE_STRAPI_URL));
    } catch (e) {
        // Fallback for invalid URLs
        return ENV.NODE_ENV === 'development' ? 'http://localhost:1337' : 'https://nice-badge-2130241d6c.media.strapiapp.com';
    }
})();
// Site URL for SEO and Open Graph
const SITE_URL = ENV.NEXT_PUBLIC_SITE_URL || (ENV.NEXT_PUBLIC_API_URL && ENV.NEXT_PUBLIC_API_URL.includes('strapiapp.com') ? ENV.NEXT_PUBLIC_API_URL.replace('.strapiapp.com', '.vercel.app') : 'https://naturalhealingnow.vercel.app');
// Helper functions
function ensureHttps(url) {
    if (!url) return url;
    return url.replace(/^http:/, 'https:');
}
function removeTrailingSlash(url) {
    if (!url) return url;
    return url.endsWith('/') ? url.slice(0, -1) : url;
}
// Log configuration in development
if (ENV.NODE_ENV === 'development') {
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].debug('Media Utils Initialized:', {
        EFFECTIVE_STRAPI_URL,
        STRAPI_MEDIA_URL,
        SITE_URL
    });
}
function getStrapiMediaPath(imagePath) {
    if (!imagePath) return '';
    // If it's already a full URL, try to sanitize it, especially for concatenated domains
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return sanitizeUrl(imagePath); // sanitizeUrl will handle malformed full URLs
    }
    // If it's a relative path, prepend the STRAPI_MEDIA_URL
    // This ensures we're using the correct media domain
    if (STRAPI_MEDIA_URL) {
        return `${STRAPI_MEDIA_URL}/${imagePath.startsWith('/') ? imagePath.substring(1) : imagePath}`;
    }
    // Fallback if STRAPI_MEDIA_URL is somehow not set (should be rare with new logic)
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].warn('STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaPath', {
        imagePath
    });
    return `${EFFECTIVE_STRAPI_URL}/${imagePath.startsWith('/') ? imagePath.substring(1) : imagePath}`;
}
function getStrapiMediaUrl(mediaInput, options = {
    debug: false
}) {
    if (options.debug) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].debug("getStrapiMediaUrl input:", {
            type: typeof mediaInput,
            isNull: mediaInput === null,
            isUndefined: mediaInput === undefined,
            value: mediaInput
        });
    }
    if (!mediaInput) return null;
    let urlToProcess = null;
    if (typeof mediaInput === 'string') {
        urlToProcess = mediaInput;
    } else if (typeof mediaInput === 'object') {
        urlToProcess = mediaInput.url || mediaInput.data?.attributes?.url || mediaInput.data?.url || null;
    }
    if (!urlToProcess) {
        if (options.debug || ("TURBOPACK compile-time value", "development") === 'production') {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].warn("Could not extract initial URL from mediaInput in getStrapiMediaUrl", {
                mediaInput
            });
        }
        return null;
    }
    const sanitizedUrl = sanitizeUrl(urlToProcess);
    if (!sanitizedUrl) {
        if (options.debug || ("TURBOPACK compile-time value", "development") === 'production') {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].warn("URL became empty after sanitization in getStrapiMediaUrl", {
                originalUrl: urlToProcess
            });
        }
        return null;
    }
    // If sanitizeUrl returned an absolute URL, use it
    if (sanitizedUrl.startsWith('http://') || sanitizedUrl.startsWith('https://')) {
        return sanitizedUrl;
    }
    // If it's a relative path after sanitization, prepend the STRAPI_MEDIA_URL
    if (STRAPI_MEDIA_URL) {
        return `${STRAPI_MEDIA_URL}${sanitizedUrl.startsWith('/') ? '' : '/'}${sanitizedUrl}`;
    }
    // Fallback: if STRAPI_MEDIA_URL is not available, use EFFECTIVE_STRAPI_URL (less ideal for media)
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].warn('STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl', {
        sanitizedUrl
    });
    return `${EFFECTIVE_STRAPI_URL}${sanitizedUrl.startsWith('/') ? '' : '/'}${sanitizedUrl}`;
}
function getProfilePictureUrl(authorData) {
    if (!authorData || !authorData.profilePicture) return null;
    const profilePic = authorData.profilePicture;
    // Try to get URL from various possible structures
    const url = profilePic.url || profilePic.data?.attributes?.url || profilePic.data?.url || profilePic.formats?.thumbnail?.url;
    return url ? getStrapiMediaUrl(url) : getStrapiMediaUrl(profilePic); // Fallback to passing the whole object
}
function getFeaturedImageUrl(postData) {
    if (!postData || !postData.featuredImage) return null;
    const featuredImg = postData.featuredImage;
    const url = featuredImg.url || featuredImg.data?.attributes?.url || featuredImg.data?.url;
    return url ? getStrapiMediaUrl(url) : getStrapiMediaUrl(featuredImg); // Fallback
}
function sanitizeUrl(urlInput) {
    // Fast path for common case: already valid URL or relative path starting with /
    if (typeof urlInput === 'string' && (urlInput.startsWith('https://') || urlInput.startsWith('http://') || urlInput.startsWith('/'))) {
        // Ensure https for absolute URLs
        if (urlInput.startsWith('http://')) {
            return urlInput.replace(/^http:/, 'https:');
        }
        return urlInput;
    }
    if (!urlInput) return '';
    let currentUrl;
    if (typeof urlInput === 'object' && urlInput.url && typeof urlInput.url === 'string') {
        currentUrl = urlInput.url;
    } else if (typeof urlInput === 'string') {
        currentUrl = urlInput;
    } else {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].warn('Invalid input type for sanitizeUrl. Expected string or object with url property.', {
            inputType: typeof urlInput
        });
        return '';
    }
    // Trim whitespace
    currentUrl = currentUrl.trim();
    // Remove 'undefined' prefix if present
    if (currentUrl.toLowerCase().startsWith('undefined')) {
        currentUrl = currentUrl.substring('undefined'.length);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].info('Removed "undefined" prefix from URL', {
            original: urlInput,
            new: currentUrl
        });
    }
    // Attempt to fix the specific concatenation: base_strapi_url + media_strapi_url
    // Example: https://nice-badge-2130241d6c.strapiapp.comhttps://nice-badge-2130241d6c.media.strapiapp.com/image.jpg
    // Should become: https://nice-badge-2130241d6c.media.strapiapp.com/image.jpg
    const strapiApiDomain = EFFECTIVE_STRAPI_URL.replace(/^https?:\/\//, '').split('/')[0]; // e.g., nice-badge-2130241d6c.strapiapp.com
    const strapiMediaDomain = STRAPI_MEDIA_URL.replace(/^https?:\/\//, '').split('/')[0]; // e.g., nice-badge-2130241d6c.media.strapiapp.com
    if (strapiApiDomain && strapiMediaDomain && currentUrl.includes(strapiApiDomain) && currentUrl.includes(strapiMediaDomain)) {
        const regex = new RegExp(`(https?:\/\/)?(${strapiApiDomain})(\/*)(https?:\/\/)?(${strapiMediaDomain})`, 'gi');
        const replacement = `https://${strapiMediaDomain}`;
        if (regex.test(currentUrl)) {
            const originalForLog = currentUrl;
            currentUrl = currentUrl.replace(regex, replacement);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].info('Fixed concatenated Strapi domains', {
                original: originalForLog,
                fixed: currentUrl,
                apiDomain: strapiApiDomain,
                mediaDomain: strapiMediaDomain
            });
        }
    }
    // Fix missing colon in https// -> https://
    if (currentUrl.includes('https//')) {
        const originalForLog = currentUrl;
        currentUrl = currentUrl.replace(/https\/\//g, 'https://');
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].info('Fixed missing colon in URL (https//)', {
            original: originalForLog,
            fixed: currentUrl
        });
    }
    // Ensure HTTPS for known media domains or if it's a full URL without protocol
    if (currentUrl.startsWith('//')) {
        currentUrl = `https:${currentUrl}`;
    } else if ((currentUrl.includes('media.strapiapp.com') || currentUrl.includes(strapiMediaDomain)) && !currentUrl.startsWith('http')) {
        currentUrl = `https://${currentUrl}`;
    } else if (currentUrl.startsWith('localhost') || currentUrl.startsWith(strapiApiDomain.split('.')[0])) {
        currentUrl = `https://${currentUrl}`; // Assume https if protocol missing for these
    }
    // If it's a relative path (starts with /), it's fine as is for now.
    // getStrapiMediaUrl will prepend the correct base.
    if (currentUrl.startsWith('/')) {
        return currentUrl;
    }
    // If it's now a valid absolute URL, return it
    if (currentUrl.startsWith('http://') || currentUrl.startsWith('https://')) {
        try {
            // Validate if it's a proper URL
            new URL(currentUrl);
            return currentUrl;
        } catch (e) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].error('URL parsing failed after sanitization attempts', {
                url: currentUrl,
                error: e
            });
            // If parsing fails, it might be a relative path that doesn't start with /
            // or completely malformed.
            // If it looks like a path (no protocol, no domain), return it for further processing.
            if (!currentUrl.includes('://') && !currentUrl.includes('.')) {
                return currentUrl;
            }
            return ''; // Unfixable
        }
    }
    // If it's not an absolute URL and not a relative path starting with /,
    // it might be a path without a leading slash.
    // This case should be handled by the caller (getStrapiMediaUrl) by prepending the base URL.
    // However, if STRAPI_MEDIA_URL is available, we can assume it's a media path.
    if (STRAPI_MEDIA_URL && currentUrl && !currentUrl.includes('://')) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].debug('Assuming relative media path, prepending STRAPI_MEDIA_URL', {
            path: currentUrl
        });
        return `/${currentUrl}`; // Return as relative path for getStrapiMediaUrl to handle
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].warn('sanitizeUrl could not produce a valid absolute or relative URL', {
        originalInput: urlInput,
        finalSanitized: currentUrl
    });
    return currentUrl; // Return what we have, or empty if it was invalid from start
}
function transformToSiteDomainUrl(url) {
    if (!url) return null;
    const sanitizedUrl = sanitizeUrl(url);
    if (!sanitizedUrl) return null;
    // For media URLs from media.strapiapp.com, return them directly (ensuring https)
    if (sanitizedUrl.includes('media.strapiapp.com')) {
        const directMediaUrl = sanitizedUrl.startsWith('http') ? sanitizedUrl : `https://${sanitizedUrl}`;
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].debug('Using direct Strapi media URL (transformToSiteDomainUrl)', {
            url: directMediaUrl
        });
        return directMediaUrl.replace(/^http:/, 'https:');
    }
    // If it's not a Strapi URL (doesn't include strapiapp.com), return as is
    if (!sanitizedUrl.includes('strapiapp.com')) {
        return sanitizedUrl.replace(/^http:/, 'https:');
    }
    try {
        const urlObj = new URL(sanitizedUrl.startsWith('http') ? sanitizedUrl : `https://${sanitizedUrl}`);
        const path = urlObj.pathname;
        const siteDomain = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;
        const transformedUrl = `${siteDomain}${path}`;
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].debug('Transformed URL for SEO (transformToSiteDomainUrl)', {
            original: sanitizedUrl,
            transformed: transformedUrl
        });
        return transformedUrl.replace(/^http:/, 'https:');
    } catch (error) {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].error('Error transforming URL in transformToSiteDomainUrl', {
            url: sanitizedUrl,
            error
        });
        return sanitizedUrl.replace(/^http:/, 'https:'); // Fallback
    }
}
function getOgImageUrl(url) {
    if (!url) return undefined;
    let processedUrl = sanitizeUrl(url);
    if (!processedUrl) return undefined;
    // If it's already an absolute URL (likely from sanitizeUrl fixing it or it was already absolute)
    if (processedUrl.startsWith('http://') || processedUrl.startsWith('https://')) {
        return processedUrl.replace(/^http:/, 'https:');
    }
    // If it's a relative path (e.g., /uploads/image.jpg or image.jpg)
    if (STRAPI_MEDIA_URL) {
        const finalUrl = `${STRAPI_MEDIA_URL}${processedUrl.startsWith('/') ? '' : '/'}${processedUrl}`;
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].debug('Constructed OG image URL from relative path', {
            original: url,
            final: finalUrl
        });
        return finalUrl.replace(/^http:/, 'https:');
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$logger$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].warn('Could not determine OG image URL confidently', {
        originalUrl: url,
        processedUrl
    });
    // Fallback: if STRAPI_MEDIA_URL is not set, try with EFFECTIVE_STRAPI_URL (less ideal)
    if (EFFECTIVE_STRAPI_URL) {
        return `${EFFECTIVE_STRAPI_URL}${processedUrl.startsWith('/') ? '' : '/'}${processedUrl}`.replace(/^http:/, 'https:');
    }
    return undefined;
}
const __TURBOPACK__default__export__ = {
    getStrapiMediaUrl,
    getProfilePictureUrl,
    getFeaturedImageUrl,
    sanitizeUrl,
    transformToSiteDomainUrl,
    getOgImageUrl,
    SITE_URL
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/Header.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$UserAccountDropdown$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/auth/UserAccountDropdown.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$LazyImage$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/shared/LazyImage.tsx [app-client] (ecmascript)"); // Import LazyImage component
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$LinkWithPreload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/shared/LinkWithPreload.tsx [app-client] (ecmascript)"); // Import LinkWithPreload component
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mediaUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mediaUtils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
// Accept props in the component function signature
const Header = ({ siteName, logoLight })=>{
    _s();
    const [isMenuOpen, setIsMenuOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { user, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    // Prepare logo variables directly from the logoLight prop
    const logoUrl = logoLight?.url;
    const logoAlt = logoLight?.alternativeText || siteName; // Use siteName as fallback alt text
    const logoWidth = logoLight?.width;
    const logoHeight = logoLight?.height;
    // Using the imported sanitizeUrl function from mediaUtils
    // Get the Strapi API URL from environment variables
    const strapiApiUrl = ("TURBOPACK compile-time value", "https://nice-badge-2130241d6c.strapiapp.com") || '';
    // Get the Strapi Media URL from environment variables (or derive it from API URL)
    const strapiMediaUrl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.IMAGE_HOSTNAME || (("TURBOPACK compile-time truthy", 1) ? strapiApiUrl.replace('strapiapp.com', 'media.strapiapp.com') : ("TURBOPACK unreachable", undefined));
    // For Strapi Cloud, construct the media URL correctly
    let fullLogoUrl = '';
    if (logoUrl) {
        // Use the new getStrapiMediaPath function to properly handle the URL
        // This function will handle all the edge cases and prevent malformed URLs
        fullLogoUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mediaUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStrapiMediaPath"])(logoUrl);
        // Log the URL in development for debugging
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('Header Logo URL:', {
                original: logoUrl,
                processed: fullLogoUrl
            });
        }
    }
    // Log the URL in production for debugging
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const toggleMenu = ()=>{
        setIsMenuOpen(!isMenuOpen);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "bg-white shadow-sm",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container mx-auto px-4 py-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-between items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-shrink-0",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/",
                                className: "inline-block align-middle",
                                children: [
                                    " ",
                                    logoUrl && logoWidth && logoHeight ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$LazyImage$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ // Use LazyImage for optimization
                                    ["default"], {
                                        src: fullLogoUrl,
                                        alt: logoAlt,
                                        width: logoWidth,
                                        height: logoHeight,
                                        className: "h-12 w-auto" // Constrain height, width adjusts automatically
                                        ,
                                        aboveTheFold: true,
                                        showPlaceholder: false,
                                        priority: true,
                                        unoptimized: ("TURBOPACK compile-time value", "development") === 'production'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/Header.tsx",
                                        lineNumber: 98,
                                        columnNumber: 17
                                    }, this) : logoUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: fullLogoUrl,
                                        alt: logoAlt,
                                        className: "h-12 w-auto" // Basic styling
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/Header.tsx",
                                        lineNumber: 110,
                                        columnNumber: 18
                                    }, this) : siteName ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-2xl font-bold text-emerald-600",
                                        children: siteName
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/Header.tsx",
                                        lineNumber: 116,
                                        columnNumber: 17
                                    }, this) // Apply text style here
                                     : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-2xl font-bold text-emerald-600",
                                        children: "My Directory Site"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/Header.tsx",
                                        lineNumber: 118,
                                        columnNumber: 17
                                    }, this) // Fallback with style
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/layout/Header.tsx",
                                lineNumber: 96,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 95,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "hidden md:flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                    className: "flex space-x-8 mr-8",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$LinkWithPreload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/",
                                            className: "text-gray-700 hover:text-emerald-600",
                                            prefetchOnHover: true,
                                            children: "Home"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Header.tsx",
                                            lineNumber: 126,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$LinkWithPreload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/clinics",
                                            className: "text-gray-700 hover:text-emerald-600",
                                            prefetchApiEndpoint: "/clinics",
                                            prefetchApiParams: {
                                                sort: 'name:asc',
                                                pagination: {
                                                    page: 1,
                                                    pageSize: 12
                                                },
                                                populate: '*'
                                            },
                                            prefetchOnHover: true,
                                            children: "Find a Clinic"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Header.tsx",
                                            lineNumber: 133,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$LinkWithPreload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/practitioners",
                                            className: "text-gray-700 hover:text-emerald-600",
                                            prefetchApiEndpoint: "/practitioners",
                                            prefetchApiParams: {
                                                sort: 'name:asc',
                                                pagination: {
                                                    page: 1,
                                                    pageSize: 12
                                                },
                                                populate: '*'
                                            },
                                            prefetchOnHover: true,
                                            children: "Find a Practitioner"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Header.tsx",
                                            lineNumber: 146,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$LinkWithPreload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/categories",
                                            className: "text-gray-700 hover:text-emerald-600",
                                            prefetchApiEndpoint: "/categories",
                                            prefetchApiParams: {
                                                sort: 'name:asc',
                                                populate: '*'
                                            },
                                            prefetchOnHover: true,
                                            children: "Categories"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Header.tsx",
                                            lineNumber: 159,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$LinkWithPreload$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/blog",
                                            className: "text-gray-700 hover:text-emerald-600",
                                            prefetchApiEndpoint: "/blog-posts",
                                            prefetchApiParams: {
                                                sort: 'publishDate:desc',
                                                pagination: {
                                                    page: 1,
                                                    pageSize: 10
                                                },
                                                populate: {
                                                    featuredImage: true,
                                                    author_blogs: {
                                                        populate: {
                                                            profilePicture: true
                                                        }
                                                    }
                                                }
                                            },
                                            prefetchOnHover: true,
                                            children: "Blog"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Header.tsx",
                                            lineNumber: 171,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/Header.tsx",
                                    lineNumber: 125,
                                    columnNumber: 13
                                }, this),
                                !isLoading && (user ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$UserAccountDropdown$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/layout/Header.tsx",
                                    lineNumber: 196,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/signin",
                                            className: "text-gray-700 hover:text-emerald-600 font-medium",
                                            children: "Sign In"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Header.tsx",
                                            lineNumber: 199,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/signup",
                                            className: "bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700",
                                            children: "Sign Up"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Header.tsx",
                                            lineNumber: 205,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/Header.tsx",
                                    lineNumber: 198,
                                    columnNumber: 17
                                }, this))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 124,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "md:hidden",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: toggleMenu,
                                className: "text-gray-700 hover:text-emerald-600 focus:outline-none",
                                "aria-label": isMenuOpen ? 'Close menu' : 'Open menu',
                                children: isMenuOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiX"], {
                                    size: 24
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Header.tsx",
                                    lineNumber: 223,
                                    columnNumber: 29
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiMenu"], {
                                    size: 24
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Header.tsx",
                                    lineNumber: 223,
                                    columnNumber: 49
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/Header.tsx",
                                lineNumber: 218,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 217,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/Header.tsx",
                    lineNumber: 93,
                    columnNumber: 9
                }, this),
                isMenuOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                    className: "md:hidden mt-4 space-y-4 pb-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/",
                            className: "block text-gray-700 hover:text-emerald-600",
                            onClick: ()=>setIsMenuOpen(false),
                            children: "Home"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 231,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/clinics",
                            className: "block text-gray-700 hover:text-emerald-600",
                            onClick: ()=>setIsMenuOpen(false),
                            children: "Find a Clinic"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 238,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/practitioners",
                            className: "block text-gray-700 hover:text-emerald-600",
                            onClick: ()=>setIsMenuOpen(false),
                            children: "Find a Practitioner"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 245,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/categories",
                            className: "block text-gray-700 hover:text-emerald-600",
                            onClick: ()=>setIsMenuOpen(false),
                            children: "Categories"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 252,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/blog",
                            className: "block text-gray-700 hover:text-emerald-600",
                            onClick: ()=>setIsMenuOpen(false),
                            children: "Blog"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 259,
                            columnNumber: 13
                        }, this),
                        !isLoading && (user ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "border-t border-gray-200 pt-4 mt-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/account",
                                    className: "flex items-center text-gray-700 hover:text-emerald-600",
                                    onClick: ()=>setIsMenuOpen(false),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiUser"], {
                                            className: "mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Header.tsx",
                                            lineNumber: 276,
                                            columnNumber: 21
                                        }, this),
                                        "My Account"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/Header.tsx",
                                    lineNumber: 271,
                                    columnNumber: 19
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        setIsMenuOpen(false);
                                    // Sign out is handled within the dropdown, but here we close the menu
                                    },
                                    className: "mt-2 flex items-center text-gray-700 hover:text-emerald-600",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-red-600",
                                        children: "Sign Out"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/Header.tsx",
                                        lineNumber: 286,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Header.tsx",
                                    lineNumber: 279,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 270,
                            columnNumber: 17
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "border-t border-gray-200 pt-4 mt-4 flex flex-col space-y-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/signin",
                                    className: "block text-gray-700 hover:text-emerald-600 font-medium",
                                    onClick: ()=>setIsMenuOpen(false),
                                    children: "Sign In"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Header.tsx",
                                    lineNumber: 291,
                                    columnNumber: 19
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/signup",
                                    className: "block text-emerald-600 hover:text-emerald-700 font-medium",
                                    onClick: ()=>setIsMenuOpen(false),
                                    children: "Sign Up"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Header.tsx",
                                    lineNumber: 298,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/Header.tsx",
                            lineNumber: 290,
                            columnNumber: 17
                        }, this))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/Header.tsx",
                    lineNumber: 230,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/Header.tsx",
            lineNumber: 92,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/Header.tsx",
        lineNumber: 91,
        columnNumber: 5
    }, this);
};
_s(Header, "zCx1JBc+wJriJV60xB6oQvZJ+8M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = Header;
const __TURBOPACK__default__export__ = Header;
var _c;
__turbopack_context__.k.register(_c, "Header");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/Footer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// Removed useState, useEffect, getStrapiContent imports as data is now passed via props
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mediaUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mediaUtils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
// Accept footerCategories as a prop
const Footer = ({ siteName, logoLight, footerCategories })=>{
    const currentYear = new Date().getFullYear();
    // Using the imported sanitizeUrl function from mediaUtils
    // Get the Strapi API URL from environment variables
    const strapiApiUrl = ("TURBOPACK compile-time value", "https://nice-badge-2130241d6c.strapiapp.com") || '';
    // Get the Strapi Media URL from environment variables (or derive it from API URL)
    const strapiMediaUrl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.IMAGE_HOSTNAME || (("TURBOPACK compile-time truthy", 1) ? strapiApiUrl.replace('strapiapp.com', 'media.strapiapp.com') : ("TURBOPACK unreachable", undefined));
    // For Strapi Cloud, construct the media URL correctly
    let logoUrl = null;
    if (logoLight?.url) {
        // Use the new getStrapiMediaPath function to properly handle the URL
        // This function will handle all the edge cases and prevent malformed URLs
        logoUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mediaUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStrapiMediaPath"])(logoLight.url);
        // Log the URL in development for debugging
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('Footer Logo URL:', {
                original: logoLight.url,
                processed: logoUrl
            });
        }
    }
    // Log the URL in production for debugging
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Removed useEffect and related state variables
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
        className: "bg-gray-800 text-white",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container mx-auto px-4 py-12",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 md:grid-cols-4 gap-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                logoUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/",
                                    className: "inline-block mb-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        src: logoUrl,
                                        alt: logoLight?.alternativeText || siteName,
                                        width: logoLight?.width || 150,
                                        height: logoLight?.height || 40,
                                        className: "h-12 w-auto" // Adjust styling as needed
                                        ,
                                        unoptimized: ("TURBOPACK compile-time value", "development") === 'production',
                                        priority: true
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/layout/Footer.tsx",
                                        lineNumber: 94,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 93,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-xl font-semibold mb-4",
                                    children: siteName
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 105,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-300 mb-4",
                                    children: "Connecting you with holistic health practitioners and clinics to support your wellness journey."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 107,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex space-x-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            target: "_blank",
                                            rel: "nofollow noopener noreferrer",
                                            className: "text-gray-300 hover:text-white",
                                            "aria-label": "Facebook",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiFacebook"], {
                                                size: 20
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 113,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 112,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            target: "_blank",
                                            rel: "nofollow noopener noreferrer",
                                            className: "text-gray-300 hover:text-white",
                                            "aria-label": "Twitter",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiTwitter"], {
                                                size: 20
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 116,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 115,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            target: "_blank",
                                            rel: "nofollow noopener noreferrer",
                                            className: "text-gray-300 hover:text-white",
                                            "aria-label": "Instagram",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiInstagram"], {
                                                size: 20
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 119,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 118,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            target: "_blank",
                                            rel: "nofollow noopener noreferrer",
                                            className: "text-gray-300 hover:text-white",
                                            "aria-label": "LinkedIn",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiLinkedin"], {
                                                size: 20
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 122,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 121,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 110,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/Footer.tsx",
                            lineNumber: 91,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-xl font-semibold mb-4",
                                    children: "Quick Links"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 129,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/",
                                                className: "text-gray-300 hover:text-white",
                                                children: "Home"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 132,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 131,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/clinics",
                                                className: "text-gray-300 hover:text-white",
                                                children: "Find a Clinic"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 137,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 136,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/practitioners",
                                                className: "text-gray-300 hover:text-white",
                                                children: "Find a Practitioner"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 142,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 141,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/blog",
                                                className: "text-gray-300 hover:text-white",
                                                children: "Blog"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 147,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 146,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/about-us",
                                                className: "text-gray-300 hover:text-white",
                                                children: "About Us"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 152,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 151,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 130,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/Footer.tsx",
                            lineNumber: 128,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-xl font-semibold mb-4",
                                    children: "Categories"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 161,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: "space-y-2",
                                    children: [
                                        footerCategories && footerCategories.length > 0 ? footerCategories.map((category)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    // Use '#' as href if slug is missing due to permission issues
                                                    href: category.attributes.slug && category.attributes.slug !== '#' ? `/categories/${category.attributes.slug}` : '#',
                                                    className: "text-gray-300 hover:text-white",
                                                    children: category.attributes.name
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                                    lineNumber: 167,
                                                    columnNumber: 21
                                                }, this)
                                            }, category.id, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 166,
                                                columnNumber: 19
                                            }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: "No categories available."
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 178,
                                            columnNumber: 17
                                        }, this) // Display message if no categories fetched/passed
                                        ,
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/categories",
                                                className: "text-gray-300 hover:text-white",
                                                children: "View All Categories"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/layout/Footer.tsx",
                                                lineNumber: 182,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 181,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 162,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/Footer.tsx",
                            lineNumber: 160,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-xl font-semibold mb-4",
                                    children: "Contact Us"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 191,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-300 mb-2",
                                    children: "Have questions or feedback?"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 192,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/contact",
                                    className: "text-emerald-400 hover:text-emerald-300 mb-2 block",
                                    children: "Get in touch with us"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 193,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-2 space-y-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/privacy",
                                            className: "text-gray-300 hover:text-white text-sm block",
                                            children: "Privacy Policy"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 198,
                                            columnNumber: 16
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/terms",
                                            className: "text-gray-300 hover:text-white text-sm block",
                                            children: "Terms of Service"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 201,
                                            columnNumber: 16
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/affiliate-disclosure",
                                            className: "text-gray-300 hover:text-white text-sm block",
                                            children: "Affiliate Disclosure"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/layout/Footer.tsx",
                                            lineNumber: 204,
                                            columnNumber: 16
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/layout/Footer.tsx",
                                    lineNumber: 197,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/layout/Footer.tsx",
                            lineNumber: 190,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/Footer.tsx",
                    lineNumber: 89,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border-t border-gray-700 mt-8 pt-8 text-center text-gray-400",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "© ",
                            currentYear,
                            " ",
                            siteName,
                            ". All rights reserved."
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/Footer.tsx",
                        lineNumber: 212,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/Footer.tsx",
                    lineNumber: 211,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/Footer.tsx",
            lineNumber: 88,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/Footer.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, this);
};
_c = Footer;
const __TURBOPACK__default__export__ = Footer;
var _c;
__turbopack_context__.k.register(_c, "Footer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/shared/ErrorFallback.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fi/index.mjs [app-client] (ecmascript)");
'use client';
;
;
;
/**
 * A reusable error fallback component that displays a user-friendly error message
 * with options to refresh the page or navigate home.
 */ const ErrorFallback = ({ error, resetErrorBoundary, message = 'Something went wrong', showHomeLink = true, showRefreshButton = true })=>{
    // Determine if we should show the technical error details
    const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
    const errorMessage = error?.message || message;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-lg shadow-md p-6 my-4 max-w-2xl mx-auto",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center mb-4 text-red-600",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiAlertTriangle"], {
                        className: "w-6 h-6 mr-2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                        lineNumber: 33,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold",
                        children: "Error Encountered"
                    }, void 0, false, {
                        fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                        lineNumber: 34,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mb-4 text-gray-700",
                children: message
            }, void 0, false, {
                fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this),
            isDevelopment && error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4 p-3 bg-gray-100 rounded overflow-auto max-h-40 text-sm",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "font-mono text-red-600",
                        children: errorMessage
                    }, void 0, false, {
                        fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                        lineNumber: 42,
                        columnNumber: 11
                    }, this),
                    error.stack && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                        className: "mt-2 text-xs text-gray-700 whitespace-pre-wrap",
                        children: error.stack.split('\n').slice(1, 5).join('\n')
                    }, void 0, false, {
                        fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                        lineNumber: 44,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                lineNumber: 41,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-wrap gap-3 mt-4",
                children: [
                    showRefreshButton && resetErrorBoundary && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: resetErrorBoundary,
                        className: "flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiRefreshCw"], {
                                className: "mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                                lineNumber: 57,
                                columnNumber: 13
                            }, this),
                            "Try Again"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                        lineNumber: 53,
                        columnNumber: 11
                    }, this),
                    showRefreshButton && !resetErrorBoundary && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>window.location.reload(),
                        className: "flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiRefreshCw"], {
                                className: "mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                                lineNumber: 67,
                                columnNumber: 13
                            }, this),
                            "Refresh Page"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                        lineNumber: 63,
                        columnNumber: 11
                    }, this),
                    showHomeLink && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        href: "/",
                        className: "flex items-center px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiHome"], {
                                className: "mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                                lineNumber: 74,
                                columnNumber: 13
                            }, this),
                            "Go to Homepage"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                        lineNumber: 73,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/shared/ErrorFallback.tsx",
                lineNumber: 51,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/shared/ErrorFallback.tsx",
        lineNumber: 31,
        columnNumber: 5
    }, this);
};
_c = ErrorFallback;
const __TURBOPACK__default__export__ = ErrorFallback;
var _c;
__turbopack_context__.k.register(_c, "ErrorFallback");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/shared/ErrorBoundary.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$ErrorFallback$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/shared/ErrorFallback.tsx [app-client] (ecmascript)");
'use client';
;
;
;
/**
 * A client-side error boundary component that catches JavaScript errors
 * in its child component tree and displays a fallback UI.
 */ class ErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"] {
    constructor(props){
        super(props);
        this.state = {
            hasError: false,
            error: null
        };
    }
    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return {
            hasError: true,
            error
        };
    }
    componentDidCatch(error, errorInfo) {
        // Log the error to an error reporting service
        console.error('Error caught by ErrorBoundary:', error, errorInfo);
        // Call the onError callback if provided
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        }
    }
    render() {
        if (this.state.hasError) {
            // You can render any custom fallback UI
            if (this.props.fallback) {
                return this.props.fallback;
            }
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$ErrorFallback$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                error: this.state.error,
                resetErrorBoundary: ()=>this.setState({
                        hasError: false,
                        error: null
                    })
            }, void 0, false, {
                fileName: "[project]/src/components/shared/ErrorBoundary.tsx",
                lineNumber: 56,
                columnNumber: 9
            }, this);
        }
        return this.props.children;
    }
}
const __TURBOPACK__default__export__ = ErrorBoundary;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/shared/GlobalErrorBoundary.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$ErrorBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/shared/ErrorBoundary.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ErrorContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ErrorContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
/**
 * A global error boundary component that integrates with the ErrorContext
 * to provide consistent error handling across the application.
 */ const GlobalErrorBoundary = ({ children })=>{
    _s();
    const { addErrorLog } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ErrorContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useError"])();
    // Handle unhandled promise rejections
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GlobalErrorBoundary.useEffect": ()=>{
            const handleUnhandledRejection = {
                "GlobalErrorBoundary.useEffect.handleUnhandledRejection": (event)=>{
                    console.error('Unhandled promise rejection:', event.reason);
                    addErrorLog(event.reason instanceof Error ? event.reason : new Error(String(event.reason)), 'unhandled-promise-rejection');
                }
            }["GlobalErrorBoundary.useEffect.handleUnhandledRejection"];
            // Add event listener for unhandled promise rejections
            window.addEventListener('unhandledrejection', handleUnhandledRejection);
            // Clean up event listener
            return ({
                "GlobalErrorBoundary.useEffect": ()=>{
                    window.removeEventListener('unhandledrejection', handleUnhandledRejection);
                }
            })["GlobalErrorBoundary.useEffect"];
        }
    }["GlobalErrorBoundary.useEffect"], [
        addErrorLog
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$ErrorBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        onError: (error, errorInfo)=>{
            addErrorLog(error, 'react-error-boundary');
        // You could also send the error to an error reporting service here
        // Example: sendToErrorReportingService(error, errorInfo);
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/shared/GlobalErrorBoundary.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
};
_s(GlobalErrorBoundary, "30njaDG/xaRDEZcHQenxIswmD0g=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ErrorContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useError"]
    ];
});
_c = GlobalErrorBoundary;
const __TURBOPACK__default__export__ = GlobalErrorBoundary;
var _c;
__turbopack_context__.k.register(_c, "GlobalErrorBoundary");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/scriptManager.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ScriptStrategy": (()=>ScriptStrategy),
    "getRegisteredScript": (()=>getRegisteredScript),
    "getRegisteredScripts": (()=>getRegisteredScripts),
    "isScriptRegistered": (()=>isScriptRegistered),
    "registerScript": (()=>registerScript),
    "unregisterScript": (()=>unregisterScript)
});
'use client';
var ScriptStrategy = /*#__PURE__*/ function(ScriptStrategy) {
    /**
   * Load the script before any Next.js code and before any page hydration occurs
   */ ScriptStrategy["BEFORE_INTERACTIVE"] = "beforeInteractive";
    /**
   * Load the script early but after some hydration on the page occurs
   */ ScriptStrategy["AFTER_INTERACTIVE"] = "afterInteractive";
    /**
   * Load the script later during browser idle time
   */ ScriptStrategy["LAZY_ONLOAD"] = "lazyOnload";
    /**
   * Load the script in a web worker (experimental)
   */ ScriptStrategy["WORKER"] = "worker";
    return ScriptStrategy;
}({});
/**
 * Registry of scripts to be loaded
 */ const scriptRegistry = {};
function registerScript(config) {
    scriptRegistry[config.id] = config;
}
function getRegisteredScripts() {
    return Object.values(scriptRegistry);
}
function getRegisteredScript(id) {
    return scriptRegistry[id];
}
function unregisterScript(id) {
    delete scriptRegistry[id];
}
function isScriptRegistered(id) {
    return !!scriptRegistry[id];
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/shared/OptimizedScripts.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>OptimizedScripts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/script.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scriptManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/scriptManager.ts [app-client] (ecmascript)");
'use client';
;
;
;
function OptimizedScripts() {
    const scripts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scriptManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRegisteredScripts"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: scripts.map((script)=>{
            // Skip scripts without src or content
            if (!script.src && !script.content) {
                return null;
            }
            // Prepare additional attributes
            const attributes = script.attributes || {};
            if (script.content) {
                // Render inline script
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    id: script.id,
                    strategy: script.strategy || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scriptManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScriptStrategy"].AFTER_INTERACTIVE,
                    dangerouslySetInnerHTML: {
                        __html: script.content
                    },
                    onLoad: script.onLoad,
                    onError: script.onError ? (e)=>script.onError?.(new Error(`Failed to load script: ${script.id}`)) : undefined,
                    ...attributes
                }, script.id, false, {
                    fileName: "[project]/src/components/shared/OptimizedScripts.tsx",
                    lineNumber: 27,
                    columnNumber: 13
                }, this);
            } else {
                // Render external script
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    id: script.id,
                    src: script.src,
                    strategy: script.strategy || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scriptManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScriptStrategy"].AFTER_INTERACTIVE,
                    onLoad: script.onLoad,
                    onError: script.onError ? (e)=>script.onError?.(new Error(`Failed to load script: ${script.id}`)) : undefined,
                    ...attributes
                }, script.id, false, {
                    fileName: "[project]/src/components/shared/OptimizedScripts.tsx",
                    lineNumber: 43,
                    columnNumber: 13
                }, this);
            }
        })
    }, void 0, false);
}
_c = OptimizedScripts;
var _c;
__turbopack_context__.k.register(_c, "OptimizedScripts");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/analytics/AnalyticsScripts.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AnalyticsScripts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scriptManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/scriptManager.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function AnalyticsScripts({ googleAnalyticsId, enableInDevelopment = false }) {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnalyticsScripts.useEffect": ()=>{
            // Skip in development mode unless explicitly enabled
            if (("TURBOPACK compile-time value", "development") === 'development' && !enableInDevelopment) {
                return;
            }
            // Register Google Analytics if ID is provided
            if (googleAnalyticsId) {
                // Register the GA script
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scriptManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["registerScript"])({
                    id: 'google-analytics',
                    src: `https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`,
                    strategy: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scriptManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScriptStrategy"].AFTER_INTERACTIVE,
                    attributes: {
                        async: 'true'
                    }
                });
                // Register the GA initialization script
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scriptManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["registerScript"])({
                    id: 'google-analytics-init',
                    content: `
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${googleAnalyticsId}', {
            page_path: window.location.pathname,
          });
        `,
                    strategy: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scriptManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScriptStrategy"].AFTER_INTERACTIVE
                });
            }
        // Add more analytics scripts as needed
        }
    }["AnalyticsScripts.useEffect"], [
        googleAnalyticsId,
        enableInDevelopment
    ]);
    // This component doesn't render anything
    return null;
}
_s(AnalyticsScripts, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = AnalyticsScripts;
var _c;
__turbopack_context__.k.register(_c, "AnalyticsScripts");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/shared/ResourceHints.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ResourceHints)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// Using Script component from next/script for client-side resource hints
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/script.js [app-client] (ecmascript)");
'use client';
;
;
function ResourceHints({ dnsPrefetch = [], preconnect = [], preload = [], prefetch = [] }) {
    // In Next.js 15 with App Router, we need to inject these hints using a script
    // that runs on the client side to add the resource hints to the document head
    // Create a script that will inject the resource hints
    const injectResourceHints = `
    (function() {
      const head = document.head;

      // DNS Prefetch
      ${dnsPrefetch.map((domain)=>`
        const dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')} = document.createElement('link');
        dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.rel = 'dns-prefetch';
        dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.href = '${domain}';
        head.appendChild(dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')});
      `).join('')}

      // Preconnect
      ${preconnect.map((domain)=>`
        const preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')} = document.createElement('link');
        preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.rel = 'preconnect';
        preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.href = '${domain}';
        preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.crossOrigin = 'anonymous';
        head.appendChild(preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')});
      `).join('')}

      // Preload
      ${preload.map((resource, index)=>`
        const preload_${index} = document.createElement('link');
        preload_${index}.rel = 'preload';
        preload_${index}.href = '${resource.href}';
        preload_${index}.as = '${resource.as}';
        ${resource.type ? `preload_${index}.type = '${resource.type}';` : ''}
        ${resource.crossOrigin ? `preload_${index}.crossOrigin = '${resource.crossOrigin}';` : ''}
        head.appendChild(preload_${index});
      `).join('')}

      // Prefetch
      ${prefetch.map((resource, index)=>`
        const prefetch_${index} = document.createElement('link');
        prefetch_${index}.rel = 'prefetch';
        prefetch_${index}.href = '${resource.href}';
        ${resource.as ? `prefetch_${index}.as = '${resource.as}';` : ''}
        head.appendChild(prefetch_${index});
      `).join('')}
    })();
  `;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$script$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        id: "resource-hints",
        strategy: "beforeInteractive",
        dangerouslySetInnerHTML: {
            __html: injectResourceHints
        }
    }, void 0, false, {
        fileName: "[project]/src/components/shared/ResourceHints.tsx",
        lineNumber: 96,
        columnNumber: 5
    }, this);
}
_c = ResourceHints;
var _c;
__turbopack_context__.k.register(_c, "ResourceHints");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/shared/EnvCheck.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
/**
 * Component to check environment variables and display warnings in development
 * This component will only render in development mode
 */ const EnvCheck = ()=>{
    _s();
    const [showWarning, setShowWarning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [warnings, setWarnings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EnvCheck.useEffect": ()=>{
            // Only run in development mode
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            const newWarnings = [];
            // Check for API URL
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            // Check for API token
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.STRAPI_API_TOKEN) {
                newWarnings.push('STRAPI_API_TOKEN is not set. This is required for authenticated API requests.');
            }
            // Set warnings and show if there are any
            setWarnings(newWarnings);
            setShowWarning(newWarnings.length > 0);
        }
    }["EnvCheck.useEffect"], []);
    // Don't render anything in production or if there are no warnings
    if (("TURBOPACK compile-time value", "development") !== 'development' || !showWarning) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-0 left-0 right-0 bg-amber-100 border-t border-amber-300 p-4 z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "text-amber-800 font-bold mb-2",
                    children: "⚠️ Environment Variable Warnings"
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/EnvCheck.tsx",
                    lineNumber: 44,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                    className: "list-disc pl-5 text-amber-700",
                    children: warnings.map((warning, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: warning
                        }, index, false, {
                            fileName: "[project]/src/components/shared/EnvCheck.tsx",
                            lineNumber: 47,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/EnvCheck.tsx",
                    lineNumber: 45,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "mt-2 text-amber-600 text-sm",
                    children: "These warnings are only visible in development mode. Check your .env.local file."
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/EnvCheck.tsx",
                    lineNumber: 50,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>setShowWarning(false),
                    className: "mt-2 px-3 py-1 bg-amber-200 hover:bg-amber-300 text-amber-800 rounded text-sm",
                    children: "Dismiss"
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/EnvCheck.tsx",
                    lineNumber: 53,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/shared/EnvCheck.tsx",
            lineNumber: 43,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/shared/EnvCheck.tsx",
        lineNumber: 42,
        columnNumber: 5
    }, this);
};
_s(EnvCheck, "HuYVHHiH44ODoNlEnX3GVuratNQ=");
_c = EnvCheck;
const __TURBOPACK__default__export__ = EnvCheck;
var _c;
__turbopack_context__.k.register(_c, "EnvCheck");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/Layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/Header.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/Footer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$GlobalErrorBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/shared/GlobalErrorBoundary.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$OptimizedScripts$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/shared/OptimizedScripts.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$analytics$2f$AnalyticsScripts$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/analytics/AnalyticsScripts.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$ResourceHints$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/shared/ResourceHints.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$EnvCheck$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/shared/EnvCheck.tsx [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
// Receive the new props
const Layout = ({ children, siteName, logoLight, footerCategories })=>{
    // Define critical resources for preloading
    const strapiBaseUrl = ("TURBOPACK compile-time value", "https://nice-badge-2130241d6c.strapiapp.com") || '';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$GlobalErrorBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$ResourceHints$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                dnsPrefetch: [
                    strapiBaseUrl,
                    'https://fonts.googleapis.com',
                    'https://fonts.gstatic.com'
                ],
                preconnect: [
                    strapiBaseUrl,
                    'https://fonts.googleapis.com',
                    'https://fonts.gstatic.com'
                ],
                preload: [
                    // Preload critical fonts
                    {
                        href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
                        as: 'style'
                    }
                ]
            }, void 0, false, {
                fileName: "[project]/src/components/layout/Layout.tsx",
                lineNumber: 60,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col min-h-screen",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        siteName: siteName,
                        logoLight: logoLight
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/Layout.tsx",
                        lineNumber: 84,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                        className: "flex-grow",
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/Layout.tsx",
                        lineNumber: 85,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Footer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        siteName: siteName,
                        logoLight: logoLight,
                        footerCategories: footerCategories
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/Layout.tsx",
                        lineNumber: 87,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$analytics$2f$AnalyticsScripts$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        googleAnalyticsId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
                        enableInDevelopment: false
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/Layout.tsx",
                        lineNumber: 90,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$OptimizedScripts$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/Layout.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$EnvCheck$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/components/layout/Layout.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/Layout.tsx",
                lineNumber: 82,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/Layout.tsx",
        lineNumber: 58,
        columnNumber: 5
    }, this);
};
_c = Layout;
const __TURBOPACK__default__export__ = Layout;
var _c;
__turbopack_context__.k.register(_c, "Layout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/QueryProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>QueryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query-devtools/build/modern/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client'; // This directive is essential for providers using client-side hooks
;
;
;
function QueryProvider({ children }) {
    _s();
    // Initialize QueryClient. It's good practice to create it once.
    // useState ensures it's not recreated on every render.
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "QueryProvider.useState": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]({
                defaultOptions: {
                    queries: {
                        // Default staleTime: 0 means queries are considered stale immediately.
                        // You might want to set a global staleTime, e.g., 5 minutes.
                        staleTime: 1000 * 60 * 5,
                        // gcTime (garbage collection time): Data is kept in cache for this long after all observers are unmounted.
                        // Renamed from cacheTime in v4. Default is 5 minutes.
                        gcTime: 1000 * 60 * 30,
                        // Default refetchOnWindowFocus: true. Refetches query on window focus.
                        refetchOnWindowFocus: ("TURBOPACK compile-time value", "development") === 'production'
                    }
                }
            })
    }["QueryProvider.useState"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: [
            children,
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2d$devtools$2f$build$2f$modern$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReactQueryDevtools"], {
                initialIsOpen: false
            }, void 0, false, {
                fileName: "[project]/src/providers/QueryProvider.tsx",
                lineNumber: 38,
                columnNumber: 50
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/providers/QueryProvider.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
}
_s(QueryProvider, "ereqih+jSxIQ/XRTPAhHxfYnjWg=");
_c = QueryProvider;
var _c;
__turbopack_context__.k.register(_c, "QueryProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_01f3ae80._.js.map