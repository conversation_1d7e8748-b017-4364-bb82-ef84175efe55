{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/app/error.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useEffect } from 'react';\nimport ErrorFallback from '@/components/shared/ErrorFallback';\nimport { useError } from '@/contexts/ErrorContext';\n\ninterface ErrorPageProps {\n  error: Error & { digest?: string };\n  reset: () => void;\n}\n\n/**\n * Global error page for the Next.js application\n * This is used by Next.js when an error occurs in a route segment\n */\nexport default function ErrorPage({ error, reset }: ErrorPageProps) {\n  const { addErrorLog } = useError();\n  \n  useEffect(() => {\n    // Log the error to our error context\n    addErrorLog(error, 'next-error-page');\n  }, [error, addErrorLog]);\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <ErrorFallback\n        error={error}\n        resetErrorBoundary={reset}\n        message=\"Sorry, something went wrong while loading this page.\"\n        showHomeLink={true}\n        showRefreshButton={true}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAgBe,SAAS,UAAU,EAAE,KAAK,EAAE,KAAK,EAAkB;IAChE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,YAAY,OAAO;IACrB,GAAG;QAAC;QAAO;KAAY;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,6IAAA,CAAA,UAAa;YACZ,OAAO;YACP,oBAAoB;YACpB,SAAQ;YACR,cAAc;YACd,mBAAmB;;;;;;;;;;;AAI3B", "debugId": null}}]}