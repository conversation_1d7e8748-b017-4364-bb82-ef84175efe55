/**
 * Example of a protected API route that uses the server API utilities
 * This demonstrates how to create a secure API endpoint that requires authentication
 */
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { fetchFromApiServerWithUserAuth } from '@/lib/serverApi';

// Define the response type for user data
interface UserResponse {
  id: number;
  username: string;
  email: string;
  provider: string;
  confirmed: boolean;
  blocked: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * GET handler for the protected API route
 * Requires a valid JWT token in cookies
 */
export async function GET(request: NextRequest) {
  // Get the JWT token from cookies
  const cookieStore = cookies();
  const token = cookieStore.get('jwt')?.value;
  
  // If no token exists, return unauthorized
  if (!token) {
    return NextResponse.json(
      { error: 'Unauthorized - No token provided' },
      { status: 401 }
    );
  }
  
  try {
    // Fetch user data from Strapi using the user-authenticated server API utility
    const userData = await fetchFromApiServerWithUserAuth<UserResponse>('/users/me');
    
    // Return the user data
    return NextResponse.json({
      message: 'Authentication successful',
      user: {
        id: userData.id,
        username: userData.username,
        email: userData.email,
      }
    });
  } catch (error: any) {
    // If the token is invalid or expired, return unauthorized
    if (error.response?.status === 401) {
      return NextResponse.json(
        { error: 'Unauthorized - Invalid or expired token' },
        { status: 401 }
      );
    }
    
    // For other errors, return a server error
    console.error('Error in protected API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
