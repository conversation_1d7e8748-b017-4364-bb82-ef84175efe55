'use client';

import React from 'react';
import Link from 'next/link';
import { FiAlertTriangle, FiRefreshCw, FiHome } from 'react-icons/fi';

interface ErrorFallbackProps {
  error?: Error | null;
  resetErrorBoundary?: () => void;
  message?: string;
  showHomeLink?: boolean;
  showRefreshButton?: boolean;
}

/**
 * A reusable error fallback component that displays a user-friendly error message
 * with options to refresh the page or navigate home.
 */
const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  message = 'Something went wrong',
  showHomeLink = true,
  showRefreshButton = true,
}) => {
  // Determine if we should show the technical error details
  const isDevelopment = process.env.NODE_ENV === 'development';
  const errorMessage = error?.message || message;
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6 my-4 max-w-2xl mx-auto">
      <div className="flex items-center mb-4 text-red-600">
        <FiAlertTriangle className="w-6 h-6 mr-2" />
        <h2 className="text-xl font-semibold">Error Encountered</h2>
      </div>
      
      <p className="mb-4 text-gray-700">{message}</p>
      
      {/* Show technical error details only in development */}
      {isDevelopment && error && (
        <div className="mb-4 p-3 bg-gray-100 rounded overflow-auto max-h-40 text-sm">
          <p className="font-mono text-red-600">{errorMessage}</p>
          {error.stack && (
            <pre className="mt-2 text-xs text-gray-700 whitespace-pre-wrap">
              {error.stack.split('\n').slice(1, 5).join('\n')}
            </pre>
          )}
        </div>
      )}
      
      <div className="flex flex-wrap gap-3 mt-4">
        {showRefreshButton && resetErrorBoundary && (
          <button
            onClick={resetErrorBoundary}
            className="flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors"
          >
            <FiRefreshCw className="mr-2" />
            Try Again
          </button>
        )}
        
        {showRefreshButton && !resetErrorBoundary && (
          <button
            onClick={() => window.location.reload()}
            className="flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors"
          >
            <FiRefreshCw className="mr-2" />
            Refresh Page
          </button>
        )}
        
        {showHomeLink && (
          <Link href="/" className="flex items-center px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors">
            <FiHome className="mr-2" />
            Go to Homepage
          </Link>
        )}
      </div>
    </div>
  );
};

export default ErrorFallback;
