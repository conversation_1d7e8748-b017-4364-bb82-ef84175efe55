import Link from 'next/link';
import { FiArrowLeft } from 'react-icons/fi';
import BlogPostCard from '@/components/blog/BlogPostCard';
import { getStrapiContent, fetchAPI } from '@/lib/strapi';
import { notFound } from 'next/navigation';
import { Metadata, ResolvingMetadata } from 'next';
import type { OpenGraphType } from 'next/dist/lib/metadata/types/opengraph-types'; // Import OpenGraphType
import Pagination from '@/components/shared/Pagination';
import { sanitizeUrl, getOgImageUrl } from '@/lib/mediaUtils';

// Enable ISR with on-demand revalidation
// No revalidation period - will only revalidate when triggered by webhook
export const revalidate = false;

// Helper function to validate OpenGraph type
function getValidOpenGraphType(ogType: string | undefined): OpenGraphType {
  const validTypes: OpenGraphType[] = [
    'article', 'book', 'profile', 'website',
    'music.song', 'music.album', 'music.playlist', 'music.radio_station',
    'video.movie', 'video.episode', 'video.tv_show', 'video.other'
  ];
  if (ogType && (validTypes as string[]).includes(ogType)) {
    return ogType as OpenGraphType;
  }
  return 'website'; // Default to 'website'
}

export const dynamicParams = true;

// Interface for the blog post structure within the category
interface BlogPostInCategory {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  featured_image: string | null;
  publish_date: string; // Use publish_date for consistency with BlogPostCard
  author: {
    name: string;
    slug: string;
    profile_picture?: string | null; // Add optional profile picture
  };
}

// Interface for the blog category structure
interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  posts: BlogPostInCategory[];
  pagination?: {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
  };
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    metaRobots?: string;
    structuredData?: string;
    canonicalURL?: string;
    metaImage?: {
      url?: string;
    };
    openGraph?: {
      title?: string;
      description?: string;
      image?: {
        url?: string;
      };
      url?: string;
      type?: string; // Make type a simple optional string
      siteName?: string; // Add missing siteName property
    };
  };
}

// Map Strapi API response to our blog category structure
function mapStrapiCategoryToProps(strapiCategory: any): BlogCategory | null {
  console.log("Mapping category data structure:", JSON.stringify({
    hasId: !!strapiCategory?.id,
    hasAttributes: !!strapiCategory?.attributes,
    topLevelKeys: strapiCategory ? Object.keys(strapiCategory) : [],
    hasBlogPosts: !!(strapiCategory?.blog_posts || strapiCategory?.attributes?.blog_posts || strapiCategory?.blogPosts || strapiCategory?.attributes?.blogPosts)
  }));

  if (!strapiCategory) {
    console.warn("No category data to map");
    return null;
  }

  // Handle both potential response formats: with attributes (v4 style) or direct properties (v5 style)
  const hasAttributes = !!strapiCategory.attributes;
  const id = strapiCategory.id;
  const data = hasAttributes ? strapiCategory.attributes : strapiCategory;

  if (!id || !data) {
    console.warn("Invalid category data structure:", strapiCategory);
    return null;
  }

  // Get the API URL and image hostname from environment variables
  const apiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || '';
  const imageHostname = process.env.IMAGE_HOSTNAME || '';

  // Determine the best URL to use for images
  const effectiveImageUrl = imageHostname || apiUrl;

  console.log("Using API URL for category mapping:", apiUrl);
  console.log("Using Image URL for category mapping:", effectiveImageUrl);

  // Extract posts - handle both v4 and v5 structures
  let posts: BlogPostInCategory[] = [];
  try {
    // Try multiple possible paths to find the posts data
    let postsData = null;

    // Check all possible paths where posts might be found
    if (hasAttributes) {
      if (data.blogPosts?.data) {
        postsData = data.blogPosts.data;
      } else if (data.blog_posts?.data) {
        postsData = data.blog_posts.data;
      } else if (data.blogPosts) {
        postsData = data.blogPosts;
      } else if (data.blog_posts) {
        postsData = data.blog_posts;
      }
    } else {
      if (data.blog_posts) {
        postsData = data.blog_posts;
      } else if (data.blogPosts) {
        postsData = data.blogPosts;
      }
    }

    console.log("Posts data found:", !!postsData, "Is array:", Array.isArray(postsData), "Length:", Array.isArray(postsData) ? postsData.length : 0);

    if (Array.isArray(postsData)) {
      posts = postsData.map((post: any) => {
        const postData = post.attributes || post;
        // Correctly access author from author_blogs array (take the first one)
        const authorArray = postData.author_blogs || [];
        // Robust author data access: check for attributes first, then direct access
        const rawAuthorData = authorArray.length > 0 ? authorArray[0] : {};
        const authorData = rawAuthorData.attributes || rawAuthorData;

        // Robust featured image data access: check for attributes first, then direct access
        const rawFeaturedImageData = postData.featuredImage?.data || postData.featuredImage || {};

        // Log the raw featured image data for debugging
        console.log(`Raw featured image data for post "${postData.title}":`, JSON.stringify(rawFeaturedImageData));

        // Use sanitizeUrl to handle all the URL extraction and formatting
        let featuredImageUrl = sanitizeUrl(rawFeaturedImageData);

        // If sanitizeUrl returns an empty string, try more direct approaches
        if (!featuredImageUrl && rawFeaturedImageData) {
          if (typeof rawFeaturedImageData === 'string') {
            // If it's already a string URL
            featuredImageUrl = sanitizeUrl(rawFeaturedImageData);
          } else if (rawFeaturedImageData.url) {
            // If it has a direct URL property
            featuredImageUrl = sanitizeUrl(rawFeaturedImageData.url);
          } else if (rawFeaturedImageData.data?.attributes?.url) {
            // If it has a nested URL in data.attributes
            featuredImageUrl = sanitizeUrl(rawFeaturedImageData.data.attributes.url);
          }
        }

        // If we still don't have a URL, try using the IMAGE_HOSTNAME environment variable
        if (!featuredImageUrl && process.env.IMAGE_HOSTNAME && rawFeaturedImageData) {
          const imageHostname = process.env.IMAGE_HOSTNAME;
          console.log(`Using IMAGE_HOSTNAME: ${imageHostname}`);

          // Try to extract a filename from any available property
          let filename = '';

          if (typeof rawFeaturedImageData === 'string' && rawFeaturedImageData.includes('/')) {
            const parts = rawFeaturedImageData.split('/');
            filename = parts[parts.length - 1];
          } else if (rawFeaturedImageData.url && rawFeaturedImageData.url.includes('/')) {
            const parts = rawFeaturedImageData.url.split('/');
            filename = parts[parts.length - 1];
          } else if (rawFeaturedImageData.data?.attributes?.url && rawFeaturedImageData.data.attributes.url.includes('/')) {
            const parts = rawFeaturedImageData.data.attributes.url.split('/');
            filename = parts[parts.length - 1];
          } else if (rawFeaturedImageData.data?.attributes?.name) {
            // Try to use the name attribute directly
            filename = rawFeaturedImageData.data.attributes.name;
          } else if (rawFeaturedImageData.name) {
            // Try to use the name attribute directly
            filename = rawFeaturedImageData.name;
          } else if (rawFeaturedImageData.data?.attributes?.hash) {
            // Try to use the hash attribute with an extension
            filename = `${rawFeaturedImageData.data.attributes.hash}.${rawFeaturedImageData.data.attributes.ext || 'jpg'}`;
          }

          if (filename) {
            featuredImageUrl = `${imageHostname}/${filename}`;
            console.log(`Constructed image URL from filename: ${featuredImageUrl}`);
          }
        }

        // Log the featured image URL for debugging
        console.log(`Featured image URL for post "${postData.title}":`, featuredImageUrl);

        // Robust author profile picture data access: check for attributes first, then direct access
        const rawAuthorProfilePicData = authorData.profilePicture?.data || authorData.profilePicture || {};

        // Log the raw author profile picture data for debugging
        console.log(`Raw author profile picture data for "${authorData.name}":`, JSON.stringify(rawAuthorProfilePicData));

        // Use sanitizeUrl to handle all the URL extraction and formatting
        let authorProfilePicUrl = sanitizeUrl(rawAuthorProfilePicData);

        // If sanitizeUrl returns an empty string, try more direct approaches
        if (!authorProfilePicUrl && rawAuthorProfilePicData) {
          if (typeof rawAuthorProfilePicData === 'string') {
            // If it's already a string URL
            authorProfilePicUrl = sanitizeUrl(rawAuthorProfilePicData);
          } else if (rawAuthorProfilePicData.url) {
            // If it has a direct URL property
            authorProfilePicUrl = sanitizeUrl(rawAuthorProfilePicData.url);
          } else if (rawAuthorProfilePicData.data?.attributes?.url) {
            // If it has a nested URL in data.attributes
            authorProfilePicUrl = sanitizeUrl(rawAuthorProfilePicData.data.attributes.url);
          }
        }

        // If we still don't have a URL, try using the IMAGE_HOSTNAME environment variable
        if (!authorProfilePicUrl && process.env.IMAGE_HOSTNAME && rawAuthorProfilePicData) {
          const imageHostname = process.env.IMAGE_HOSTNAME;

          // Try to extract a filename from any available property
          let filename = '';

          if (typeof rawAuthorProfilePicData === 'string' && rawAuthorProfilePicData.includes('/')) {
            const parts = rawAuthorProfilePicData.split('/');
            filename = parts[parts.length - 1];
          } else if (rawAuthorProfilePicData.url && rawAuthorProfilePicData.url.includes('/')) {
            const parts = rawAuthorProfilePicData.url.split('/');
            filename = parts[parts.length - 1];
          } else if (rawAuthorProfilePicData.data?.attributes?.url && rawAuthorProfilePicData.data.attributes.url.includes('/')) {
            const parts = rawAuthorProfilePicData.data.attributes.url.split('/');
            filename = parts[parts.length - 1];
          } else if (rawAuthorProfilePicData.data?.attributes?.name) {
            // Try to use the name attribute directly
            filename = rawAuthorProfilePicData.data.attributes.name;
          } else if (rawAuthorProfilePicData.name) {
            // Try to use the name attribute directly
            filename = rawAuthorProfilePicData.name;
          }

          if (filename) {
            authorProfilePicUrl = `${imageHostname}/${filename}`;
            console.log(`Constructed author profile picture URL from filename: ${authorProfilePicUrl}`);
          }
        }

        // Log the author profile picture URL for debugging
        console.log(`Author profile picture URL for "${authorData.name}":`, authorProfilePicUrl);

        // The following code is no longer needed as we're using sanitizeUrl
        /*
        // Handle author profile picture URL construction
        if (authorProfilePicData && authorProfilePicData.url) {
          // If the URL already starts with http, use it directly
          if (authorProfilePicData.url.startsWith('http')) {
            authorProfilePicUrl = authorProfilePicData.url;
          } else {
            // Otherwise, prepend the API URL
            authorProfilePicUrl = `${effectiveImageUrl}${authorProfilePicData.url}`;
          }
        }
        */

        // Create the blog post object without view_count directly in the object
        const blogPost = {
          id: post.id || '',
          title: postData.title || 'Untitled Post',
          slug: postData.slug || 'untitled-post',
          excerpt: postData.excerpt || null,
          featured_image: featuredImageUrl,
          // Prioritize publishDate, then fall back
          publish_date: postData.publishDate || postData.published_at || postData.createdAt || new Date().toISOString(),
          author: {
            name: authorData.name || 'Unknown Author',
            slug: authorData.slug || 'unknown-author', // Assuming author has a slug field
            profile_picture: authorProfilePicUrl
          }
        };

        // Add view_count as a non-enumerable property if it exists
        if (postData.view_count !== undefined) {
          Object.defineProperty(blogPost, 'view_count', {
            value: postData.view_count,
            enumerable: false
          });
        }

        return blogPost;
      });
    }
  } catch (e) {
    console.warn("Error extracting posts for category:", e);
  }

  // Get category fields with fallbacks
  const name = data.name || 'Unnamed Category';
  const slug = data.slug || 'unnamed-category';

  // Handle richtext description field
  let description = null;
  if (data.description) {
    // If it's a string, use it directly
    if (typeof data.description === 'string') {
      description = data.description;
    }
    // If it's an array (richtext format), try to extract text
    else if (Array.isArray(data.description)) {
      try {
        // Extract text from richtext format
        description = data.description
          .map((block: any) => {
            if (block.children) {
              return block.children
                .map((child: any) => child.text || '')
                .join(' ');
            }
            return '';
          })
          .filter(Boolean)
          .join(' ');
      } catch (e) {
        console.warn("Error parsing richtext description:", e);
      }
    }
  }

  // Extract SEO data if available
  const seo = data.seo || null;

  // Extract pagination metadata if available
  let pagination = undefined;

  // Log available pagination data for debugging
  console.log("Checking for pagination metadata in category response");

  // In Strapi v5, the pagination for blog_posts should be in the blog_posts.meta.pagination
  if (data.blog_posts?.meta?.pagination) {
    console.log("Found pagination in data.blog_posts.meta.pagination:", JSON.stringify(data.blog_posts.meta.pagination));
    pagination = data.blog_posts.meta.pagination;
  }
  // Then check if it's directly in the response meta (less likely but possible)
  else if (strapiCategory.meta?.pagination) {
    console.log("Found pagination in strapiCategory.meta.pagination:", JSON.stringify(strapiCategory.meta.pagination));
    pagination = strapiCategory.meta.pagination;
  }
  // Check other possible locations
  else {
    // Check all possible locations for pagination data
    const possiblePaginationPaths = [
      { path: 'strapiCategory.blog_posts.meta.pagination', value: strapiCategory.blog_posts?.meta?.pagination },
      { path: 'data.blogPosts.meta.pagination', value: data.blogPosts?.meta?.pagination },
      { path: 'strapiCategory.data.meta.pagination', value: strapiCategory.data?.meta?.pagination }
    ];

    // Find the first valid pagination data
    for (const { path, value } of possiblePaginationPaths) {
      if (value) {
        console.log(`Found pagination in ${path}:`, JSON.stringify(value));
        pagination = value;
        break;
      }
    }
  }

  // If we still don't have pagination data, create a default one based on posts length
  if (!pagination && Array.isArray(posts)) {
    console.log("Creating default pagination based on posts length:", posts.length);
    pagination = {
      page: 1,
      pageSize: 12, // Always use 12 as the default page size
      pageCount: Math.max(1, Math.ceil((posts.length || 0) / 12)), // Ensure at least 1 page
      total: posts.length || 0
    };
  }

  // Ensure pageCount is at least 1 to prevent issues with pagination component
  if (pagination && pagination.pageCount < 1) {
    pagination.pageCount = 1;
  }

  // Log the final pagination data
  console.log("Final pagination data:", JSON.stringify(pagination));

  // Return the mapped category with all available data
  return {
    id,
    name,
    slug,
    description,
    posts,
    pagination,
    seo
  };
}

// Define pagination parameters interface
interface PaginationParams {
  page?: number;
  pageSize?: number;
}

// Define the props type for generateMetadata
type Props = {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

// Separate async function for data fetching
async function getCategoryData(slug: string, paginationParams: PaginationParams = {}) {
  // Default pagination values
  const page = paginationParams.page || 1;
  const pageSize = paginationParams.pageSize || 12;

  console.log(`Fetching category data for slug: "${slug}" (page ${page}, pageSize ${pageSize})`);

  try {
    // Step 1: First, get the category data without posts to get the category ID
    const categoryResponse = await fetchAPI('/blog-categories', {
      params: {
        filters: { slug: { $eq: slug } },
        populate: {
          seo: true // Populate SEO data
        }
      }
    });

    // Check if category exists
    if (!categoryResponse?.data || !Array.isArray(categoryResponse.data) || categoryResponse.data.length === 0) {
      console.error(`No category found with slug: ${slug}`);
      return { data: [], meta: { pagination: { page, pageSize, pageCount: 0, total: 0 } } };
    }

    // Get the category ID
    const categoryId = categoryResponse.data[0].id;
    console.log(`Found category with ID: ${categoryId}`);

    // Step 2: Now fetch the blog posts for this category with pagination
    const postsResponse = await fetchAPI(`/blog-posts`, {
      params: {
        filters: {
          blog_categories: {
            id: { $eq: categoryId }
          }
        },
        pagination: {
          page,
          pageSize
        },
        sort: ['publishDate:desc'], // Show newest posts first
        populate: {
          featuredImage: true,
          author_blogs: {
            populate: {
              profilePicture: true
            },
            // Ensure author name and slug are fetched for the card
            fields: ['name', 'slug']
          },
          // Explicitly request fields needed by BlogPostCard
          // Removed reading_time as it's not available in Strapi v5
          fields: ['title', 'slug', 'excerpt', 'publishDate', 'content']
        }
      }
    });

    console.log(`Found ${postsResponse?.data?.length || 0} posts for category ID ${categoryId}`);

    // Step 3: Combine the responses
    const combinedResponse = {
      data: [
        {
          ...categoryResponse.data[0],
          blog_posts: postsResponse.data || []
        }
      ],
      meta: postsResponse.meta || { pagination: { page, pageSize, pageCount: 0, total: 0 } }
    };

    // Log the combined response structure
    console.log(`Combined category response for slug ${slug}:`, JSON.stringify({
      hasData: !!combinedResponse?.data,
      isArray: Array.isArray(combinedResponse?.data),
      length: Array.isArray(combinedResponse?.data) ? combinedResponse.data.length : 'not an array',
      hasPosts: Array.isArray(combinedResponse?.data) && combinedResponse.data.length > 0 &&
               Array.isArray(combinedResponse.data[0].blog_posts) ?
               combinedResponse.data[0].blog_posts.length : 0,
      hasPagination: !!combinedResponse?.meta?.pagination,
      paginationTotal: combinedResponse?.meta?.pagination?.total || 0
    }));

    return combinedResponse;
  } catch (error) {
    console.error(`Error fetching category data for slug ${slug}:`, error);
    throw error;
  }
}

// Generate Metadata function
export async function generateMetadata(
  { params, searchParams }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  // Await params before accessing its properties
  const awaitedParams = await params;
  // Access slug from awaited params
  const slug = awaitedParams.slug;

  // Define the site URL from environment variable with proper fallback to your actual domain
  const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';
  const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || '';

  // Log the URL being used for debugging
  if (process.env.NODE_ENV === 'production') {
    console.log(`Using site URL for canonical URLs: ${SITE_URL}`);
  }

  try {
    // Get the current page from search params - ensure it's a number and at least 1
    const currentPage = Math.max(1, Number(searchParams.page) || 1);
    const pageSize = 12; // Show 12 posts per page

    const response = await getCategoryData(slug, {
      page: currentPage,
      pageSize: pageSize
    });

    if (!response || !response.data || response.data.length === 0) {
      return {
        title: 'Category Not Found | Natural Healing Now',
        description: 'The requested blog category could not be found.'
      };
    }

    const categoryData = response.data[0];
    const mappedCategory = mapStrapiCategoryToProps(categoryData);

    if (!mappedCategory) {
      return {
        title: 'Category Not Found | Natural Healing Now',
        description: 'The requested blog category could not be found.'
      };
    }

    // Get SEO data from Strapi SEO plugin
    const seo = mappedCategory.seo;

    // Fallback values
    const pageNumber = Number(searchParams.page) || 1;
    const pageText = pageNumber > 1 ? ` - Page ${pageNumber}` : '';
    const defaultTitle = `${mappedCategory.name}${pageText} - Blog Category | Natural Healing Now`;
    const defaultDescription = mappedCategory.description ||
      `Explore articles in the ${mappedCategory.name} category on Natural Healing Now.`;

    // Canonical URL
    const pageParam = pageNumber > 1 ? `?page=${pageNumber}` : '';
    const canonicalPath = `/blog/categories/${mappedCategory.slug}${pageParam}`;
    const canonicalUrl = seo?.canonicalURL || (SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath);

    // Image handling for OpenGraph
    let ogImageUrl: string | undefined = undefined;

    // First try to get image from SEO plugin's openGraph data
    if (seo?.openGraph?.image?.url) {
      ogImageUrl = getOgImageUrl(seo.openGraph.image.url);
    }
    // Then fall back to metaImage if openGraph image is not available
    else if (seo?.metaImage?.url) {
      ogImageUrl = getOgImageUrl(seo.metaImage.url);
    }

    // Log the image URL in production for debugging
    if (process.env.NODE_ENV === 'production') {
      console.log('Blog category og:image:', {
        openGraphImageUrl: seo?.openGraph?.image?.url,
        metaImageUrl: seo?.metaImage?.url,
        ogImageUrl
      });
    }

    // Construct metadata
    const metadata: Metadata = {
      title: seo?.metaTitle || defaultTitle,
      description: seo?.metaDescription || defaultDescription,
      robots: seo?.metaRobots || 'index, follow',
      alternates: {
        canonical: canonicalUrl,
      },
      // Use openGraph data from SEO plugin if available
      openGraph: seo?.openGraph ? {
        title: seo.openGraph.title || seo.metaTitle || defaultTitle,
        description: seo.openGraph.description || seo.metaDescription || defaultDescription,
        url: seo.openGraph.url || canonicalUrl,
        siteName: seo.openGraph.siteName || 'Natural Healing Now',
        // Ensure the type is one of the allowed OpenGraph types
        type: getValidOpenGraphType(seo.openGraph.type),
        ...(ogImageUrl && { images: [{ url: ogImageUrl }] }),
      } : {
        // Fallback if no openGraph data in SEO plugin
        title: seo?.metaTitle || defaultTitle,
        description: seo?.metaDescription || defaultDescription,
        url: canonicalUrl,
        siteName: 'Natural Healing Now',
        type: 'website', // Default to 'website'
        ...(ogImageUrl && { images: [{ url: ogImageUrl }] }),
      },
      twitter: {
        card: 'summary_large_image',
        title: seo?.openGraph?.title || seo?.metaTitle || defaultTitle,
        description: seo?.openGraph?.description || seo?.metaDescription || defaultDescription,
        ...(ogImageUrl && { images: [ogImageUrl] }),
      },
    };

    // Add structured data if available
    if (seo?.structuredData) {
      // Check if structuredData is already an object or a string
      if (typeof seo.structuredData === 'string') {
        try {
          // Parse to validate it's proper JSON if it's a string
          JSON.parse(seo.structuredData);
          // We'll handle the actual injection in the component return
        } catch (e) {
          console.error("Invalid structured data JSON:", e);
        }
      }
      // If it's an object, it's already valid (no need to parse)
    }

    return metadata;
  } catch (error) {
    console.error(`Error generating metadata for blog category ${slug}:`, error);
    return {
      title: 'Blog Category | Natural Healing Now',
      description: 'Explore our blog categories for natural healing information.'
    };
  }
}

export default async function BlogCategoryPage({
  params: paramsProp,
  searchParams
}: {
  params: { slug: string },
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  // Await the params object before destructuring
  const params = await paramsProp;
  const { slug } = params;

  // Get the current page from search params - ensure it's a number and at least 1
  const currentPage = Math.max(1, Number(searchParams.page) || 1);
  const pageSize = 12; // Show 12 posts per page

  let category: BlogCategory | null = null;
  let categoryResponse: any = null;

  try {
    console.log(`Fetching blog category with slug: "${slug}" (page ${currentPage})`);

    // Call the separate data fetching function
    categoryResponse = await getCategoryData(slug, {
      page: currentPage,
      pageSize: pageSize
    });

    // Log the response structure to help debug
    console.log("Blog category response structure:", JSON.stringify({
      hasData: !!categoryResponse?.data,
      dataKeys: categoryResponse?.data && categoryResponse.data.length > 0 ? Object.keys(categoryResponse.data[0]) : [],
      isArray: Array.isArray(categoryResponse?.data),
      length: Array.isArray(categoryResponse?.data) ? categoryResponse.data.length : 'not an array',
      hasPagination: !!categoryResponse?.meta?.pagination,
      paginationTotal: categoryResponse?.meta?.pagination?.total || 0
    }));

    if (categoryResponse && categoryResponse.data && categoryResponse.data.length > 0) {
      console.log("Found category data, mapping to component props");

      // Log pagination metadata if available
      if (categoryResponse.meta?.pagination) {
        console.log("Pagination metadata:", JSON.stringify(categoryResponse.meta.pagination));
      }

      category = mapStrapiCategoryToProps(categoryResponse.data[0]);

      // Log the mapped category pagination
      console.log("Mapped category pagination:", JSON.stringify(category?.pagination || "No pagination data"));

      // Log the current page and search params
      console.log("Current page from searchParams:", currentPage);
      console.log("Search params:", JSON.stringify(searchParams));

      // SEO data is now included in the mapped category object
    } else {
      console.log(`No blog category found with slug "${slug}"`);

      // Try to fetch all categories to see if the slug exists
      try {
        const allCategoriesResponse = await getStrapiContent.blog.getCategories();
        console.log("All categories response:", JSON.stringify({
          hasData: !!allCategoriesResponse?.data,
          count: allCategoriesResponse?.data?.length || 0,
          slugs: allCategoriesResponse?.data?.map((cat: any) => cat.slug || (cat.attributes && cat.attributes.slug))
        }));
      } catch (categoriesError) {
        console.error("Error fetching all categories:", categoriesError);
      }

      return notFound();
    }
  } catch (error: any) {
    console.error(`Error fetching blog category with slug ${slug}:`, error);

    // Log more details about the error
    if (error.response) {
      console.error("Error response status:", error.response.status);
      console.error("Error response data:", JSON.stringify(error.response.data));
    }

    // In production, show 404 if the API fails
    return notFound();
  }

  // If category not found after mapping or API error, show 404
  if (!category) {
    return notFound();
  }

  // We'll use the Next.js Metadata API instead of SEOHead component

  // Ensure structuredData is a string before rendering
  let structuredDataString: string | null = null;
  if (category?.seo?.structuredData) {
    if (typeof category.seo.structuredData === 'string') {
      structuredDataString = category.seo.structuredData;
    } else if (typeof category.seo.structuredData === 'object') {
      try {
        structuredDataString = JSON.stringify(category.seo.structuredData);
      } catch (e) {
        console.error("Failed to stringify structuredData object:", e);
      }
    }
  }

  // If no structured data from SEO plugin, create a default one
  if (!structuredDataString) {
    // Create default structured data for blog category
    const defaultStructuredData = {
      "@context": "https://schema.org",
      "@type": "CollectionPage",
      "name": category?.name,
      "description": category?.description || `Articles in the ${category?.name} category`,
      "url": `${process.env.NEXT_PUBLIC_SITE_URL || ''}/blog/categories/${category?.slug}`,
      "mainEntity": {
        "@type": "ItemList",
        "itemListElement": category?.posts.map((post, index) => ({
          "@type": "ListItem",
          "position": index + 1,
          "url": `${process.env.NEXT_PUBLIC_SITE_URL || ''}/blog/${post.slug}`,
          "name": post.title
        })) || []
      }
    };

    structuredDataString = JSON.stringify(defaultStructuredData);
  }

  // Note: We don't need to wrap in Layout here since it's already provided by the RootLayout
  return (
    <>
      {/* Structured Data (JSON-LD) */}
      {structuredDataString && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: structuredDataString }}
        />
      )}

      {/* Breadcrumb */}
      <div className="bg-gray-100 py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-emerald-600">Home</Link>
            <span className="mx-2">/</span>
            <Link href="/blog" className="hover:text-emerald-600">Blog</Link>
            <span className="mx-2">/</span>
            {/* Link to a potential categories overview page if it exists */}
            {/* <Link href="/blog/categories" className="hover:text-emerald-600">Categories</Link>
            <span className="mx-2">/</span> */}
            <span className="text-gray-800">{category.name}</span>
          </div>
        </div>
      </div>

      {/* Category Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">{category.name}</h1>
          {category.description && (
            <p className="text-lg max-w-3xl">
              {category.description}
            </p>
          )}
        </div>
      </div>

      {/* Category Posts */}
      <div className="bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            {categoryResponse.meta?.pagination?.total ?
              `${categoryResponse.meta.pagination.total} Articles in ${category.name}` :
              (category.posts?.length ?
                `${category.posts.length} Articles in ${category.name}` :
                `Articles in ${category.name}`)}
          </h2>

          {category.posts && category.posts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {category.posts.map(post => (
                <BlogPostCard
                  key={post.id}
                  post={post} // Pass the mapped post directly
                />
              ))}
            </div>
          ) : (
            <div className="bg-white p-8 rounded-lg text-center">
              <p className="text-gray-600">No articles found in this category.</p>
            </div>
          )}

          {/* Pagination - only show when there are multiple pages */}
          <div className="mt-12 flex justify-center">
            {categoryResponse.meta?.pagination && categoryResponse.meta.pagination.pageCount > 1 ? (
              <Pagination
                totalPages={categoryResponse.meta.pagination.pageCount}
              />
            ) : (
              category.posts && category.posts.length > 12 ? (
                <Pagination
                  totalPages={Math.max(1, Math.ceil(category.posts.length / 12))}
                />
              ) : null
            )}
          </div>

          {/* Back to Blog Link */}
          <div className="mt-12 text-center">
            <Link
              href="/blog"
              className="text-emerald-600 hover:text-emerald-700 flex items-center justify-center"
            >
              <FiArrowLeft className="mr-2" /> Back to All Articles
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
