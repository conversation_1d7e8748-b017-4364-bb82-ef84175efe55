'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

/**
 * Type for form action result
 * Success: { success: true, data: T }
 * Error: { success: false, error: string }
 */
export type FormActionResult<T = any> = 
  | { success: true; data: T }
  | { success: false; error: string };

/**
 * Generic form action handler that follows Next.js 15 best practices
 * Returns a result object instead of throwing errors
 */
export async function handleFormAction<T = any>(
  action: () => Promise<T>,
  options: {
    successMessage?: string;
    errorMessage?: string;
    revalidatePaths?: string[];
    redirectTo?: string;
  } = {}
): Promise<FormActionResult<T>> {
  const {
    successMessage = 'Operation completed successfully',
    errorMessage = 'An error occurred',
    revalidatePaths = [],
    redirectTo,
  } = options;

  try {
    // Execute the action
    const result = await action();

    // Revalidate any paths if specified
    if (revalidatePaths.length > 0) {
      revalidatePaths.forEach(path => revalidatePath(path));
    }

    // Return success result
    const successResult: FormActionResult<T> = {
      success: true,
      data: result,
    };

    // If redirectTo is specified, redirect after successful action
    if (redirectTo) {
      redirect(redirectTo);
    }

    return successResult;
  } catch (error) {
    // Log the error
    console.error('Form action error:', error);

    // Return error result
    return {
      success: false,
      error: error instanceof Error ? error.message : errorMessage,
    };
  }
}

/**
 * Example usage:
 * 
 * export async function createItem(prevState: any, formData: FormData) {
 *   return handleFormAction(
 *     async () => {
 *       // Validate form data
 *       const title = formData.get('title');
 *       if (!title || typeof title !== 'string') {
 *         throw new Error('Title is required');
 *       }
 *       
 *       // Process the data
 *       const result = await db.items.create({ title });
 *       return result;
 *     },
 *     {
 *       successMessage: 'Item created successfully',
 *       errorMessage: 'Failed to create item',
 *       revalidatePaths: ['/items'],
 *       redirectTo: '/items',
 *     }
 *   );
 * }
 */
