"use strict";exports.id=3762,exports.ids=[3762],exports.modules={36463:(e,t,r)=>{r.d(t,{Ay:()=>l});var a=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let s={enabled:!1,level:"info",prefix:"[NHN]"};function i(e,t,...r){if(!s.enabled)return;let l=Object.values(a),n=l.indexOf(s.level);if(l.indexOf(e)>=n){let a=s.prefix?`${s.prefix} `:"",i=`${a}${t}`;switch(e){case"debug":console.debug(i,...r);break;case"info":console.info(i,...r);break;case"warn":console.warn(i,...r);break;case"error":console.error(i,...r)}}}let l={debug:function(e,...t){i("debug",e,...t)},info:function(e,...t){i("info",e,...t)},warn:function(e,...t){i("warn",e,...t)},error:function(e,...t){i("error",e,...t)},configure:function(e){s={...s,...e}}}},47365:(e,t,r)=>{r.d(t,{A:()=>c});var a=r(60687),s=r(24587),i=r(85814),l=r.n(i),n=r(44867),d=r(28136),o=r(17019);let c=({posts:e})=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Popular Articles"}),(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-16 h-16 relative rounded overflow-hidden",children:e.featured_image?(0,a.jsx)(s.default,{src:(0,d.Jf)(e.featured_image),alt:e.title,width:64,height:64,fillContainer:!0,className:"object-cover",sizes:"64px",showPlaceholder:!0}):(0,a.jsx)("div",{className:"w-full h-full bg-emerald-100 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-emerald-700",children:e.title.charAt(0)})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 line-clamp-2",children:(0,a.jsx)(l(),{href:`/blog/${e.slug}`,className:"hover:text-emerald-600",children:e.title})}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500 mt-1",children:[e.author&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.JXP,{className:"text-emerald-600 mr-1.5"}),(0,a.jsx)(l(),{href:`/blog/authors/${e.author.slug}`,className:"hover:text-emerald-600 mr-2",children:e.author.name}),(0,a.jsx)("span",{className:"mx-1",children:"•"})]}),(0,n.GP)(new Date(e.publish_date),"MMM d, yyyy")]})]})]},e.id))})]})},55061:(e,t,r)=>{r.d(t,{default:()=>c});var a=r(60687),s=r(24587),i=r(85814),l=r.n(i),n=r(44867),d=r(17019),o=r(28136);let c=({post:e,showReadingTime:t=!1,showShareButton:r=!1,showBadge:i=!1})=>{var c;let h=(0,o.Jf)(e.featured_image),m=!!e.featured_image,p=(0,o.Jf)(e.author?.profile_picture);e.author?.profile_picture;let u=e.reading_time||(e.content?(c=e.content,Math.max(1,Math.ceil((c?.split(/\s+/)?.length||0)/200))):2),x=p&&(p.startsWith("http")||p.startsWith("/")||p.startsWith("data:"))?p:"",f=(0,n.GP)(new Date(e.publish_date),"MMMM d, yyyy"),g=Object.getOwnPropertyDescriptor(e,"view_count")?.value||0;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,a.jsxs)(l(),{href:`/blog/${e.slug}`,className:"block relative h-48 w-full overflow-hidden",children:[" ",m?(0,a.jsx)(s.default,{src:h,alt:e.title||"Blog post image",width:600,height:400,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1,showPlaceholder:!0,advancedBlur:!0,fadeIn:!0,preload:e.isFeatured||g>10}):(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-50 to-teal-100 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-emerald-700 font-semibold text-xl opacity-50",children:e.title.charAt(0)})}),i&&(!0===e.isFeatured||g>0)&&(0,a.jsx)("div",{className:`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ${!0===e.isFeatured?"bg-emerald-600 text-white":"bg-amber-500 text-white"}`,children:!0===e.isFeatured?"Featured Post":"Popular Post"})]}),(0,a.jsxs)("div",{className:"p-4 flex-grow flex flex-col",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:f}),t&&(0,a.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,a.jsx)(d.Ohp,{className:"mr-1"}),(0,a.jsxs)("span",{children:[u," min read"]})]})]}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800 flex-grow",children:(0,a.jsx)(l(),{href:`/blog/${e.slug}`,className:"hover:text-emerald-600 line-clamp-2",children:e.title})}),e.excerpt&&(0,a.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:e.excerpt}),e.author&&(0,a.jsxs)("div",{className:"flex items-center mt-auto pt-4 border-t border-gray-100",children:[" ",(0,a.jsxs)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-200 shadow-sm",children:[" ",x&&(e=>{try{return e&&(e.startsWith("http")||e.startsWith("/")||e.startsWith("data:"))}catch(e){return!1}})(x)?(0,a.jsx)(s.default,{src:x,alt:e.author.name||"Author image",width:40,height:40,fillContainer:!0,className:"object-cover rounded-full",sizes:"40px",showPlaceholder:!0,fadeIn:!0}):(0,a.jsx)("div",{className:"absolute inset-0 bg-emerald-100 flex items-center justify-center",children:(0,a.jsx)(d.JXP,{className:"text-emerald-700 text-lg"})})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"block text-xs text-gray-500 mb-0.5",children:"Written by"}),(0,a.jsx)(l(),{href:`/blog/authors/${e.author.slug}`,className:"font-medium text-gray-800 hover:text-emerald-600",children:e.author.name})]})]})]}),(0,a.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto flex justify-between items-center",children:[(0,a.jsx)(l(),{href:`/blog/${e.slug}`,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"Read More →"}),r&&(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors",onClick:t=>{t.preventDefault(),navigator.share?navigator.share({title:e.title,text:e.excerpt||"",url:`${window.location.origin}/blog/${e.slug}`}).catch(e=>{console.error("Error sharing:",e)}):navigator.clipboard.writeText(`${window.location.origin}/blog/${e.slug}`).then(()=>{alert("Link copied to clipboard!")}).catch(e=>{console.error("Could not copy text: ",e)})},"aria-label":"Share article",children:(0,a.jsx)(d.Pum,{size:18})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors","aria-label":"Save article",children:(0,a.jsx)(d.Y19,{size:18})})]})]})]})}},58101:(e,t,r)=>{r.d(t,{A:()=>c});var a=r(60687),s=r(24587),i=r(85814),l=r.n(i),n=r(44867),d=r(17019),o=r(28136);let c=({post:e,badgeType:t="featured"})=>{var r;let i=(0,o.Jf)(e.featured_image),c=!!e.featured_image,h=(0,o.Jf)(e.author?.profile_picture);e.author?.profile_picture;let m=e.reading_time||(e.content?(r=e.content,Math.max(1,Math.ceil((r?.split(/\s+/)?.length||0)/200))):2),p=h&&(h.startsWith("http")||h.startsWith("/")||h.startsWith("data:"))?h:"",u=(0,n.GP)(new Date(e.publish_date),"MMMM d, yyyy");return(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-md overflow-hidden transition-transform hover:shadow-lg",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row",children:[(0,a.jsxs)("div",{className:"md:w-1/2 relative h-64 md:h-auto",children:[c?(0,a.jsx)(s.default,{src:i,alt:e.title||"Featured post image",width:800,height:600,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,showPlaceholder:!0}):(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-100 to-teal-200 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-emerald-700 font-semibold text-2xl opacity-50",children:e.title.charAt(0)})}),(0,a.jsx)("div",{className:`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium ${"featured"===t?"bg-emerald-600 text-white":"popular"===t?"bg-amber-500 text-white":"bg-blue-500 text-white"}`,children:"featured"===t?"Featured Post":"popular"===t?"Popular Post":"Latest Post"})]}),(0,a.jsxs)("div",{className:"md:w-1/2 p-6 md:p-8 flex flex-col",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-3 text-sm text-gray-500 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.wIk,{className:"mr-1"}),(0,a.jsx)("span",{children:u})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.Ohp,{className:"mr-1"}),(0,a.jsxs)("span",{children:[m," min read"]})]})]}),(0,a.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-3",children:(0,a.jsx)(l(),{href:`/blog/${e.slug}`,className:"hover:text-emerald-600",children:e.title})}),e.excerpt&&(0,a.jsx)("p",{className:"text-gray-600 mb-6 line-clamp-3 md:line-clamp-4",children:e.excerpt}),(0,a.jsxs)("div",{className:"mt-auto flex items-center justify-between",children:[e.author&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-200 shadow-sm",children:p&&(e=>{try{return e&&(e.startsWith("http")||e.startsWith("/")||e.startsWith("data:"))}catch(e){return!1}})(p)?(0,a.jsx)(s.default,{src:p,alt:e.author.name||"Author image",width:40,height:40,fillContainer:!0,className:"object-cover rounded-full",sizes:"40px",priority:!1,showPlaceholder:!0}):(0,a.jsx)("div",{className:"absolute inset-0 bg-emerald-100 flex items-center justify-center",children:(0,a.jsx)(d.JXP,{className:"text-emerald-700 text-lg"})})}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"block text-xs text-gray-500 mb-0.5",children:"Written by"}),(0,a.jsx)(l(),{href:`/blog/authors/${e.author.slug}`,className:"font-medium text-gray-800 hover:text-emerald-600",children:e.author.name})]})]}),(0,a.jsxs)(l(),{href:`/blog/${e.slug}`,className:"inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm",children:["Read Article ",(0,a.jsx)("span",{className:"ml-1",children:"→"})]})]})]})]})})}},82158:(e,t,r)=>{r.d(t,{Jf:()=>m,Rb:()=>p,Z5:()=>c,tz:()=>h});var a=r(36463);let s={NEXT_PUBLIC_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_MEDIA_URL:"https://nice-badge-2130241d6c.media.strapiapp.com",NEXT_PUBLIC_SITE_URL:process.env.NEXT_PUBLIC_SITE_URL,IMAGE_HOSTNAME:process.env.IMAGE_HOSTNAME,NODE_ENV:"production"},i=s.NEXT_PUBLIC_STRAPI_API_URL||s.NEXT_PUBLIC_API_URL||("development"===s.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.strapiapp.com"),l=(()=>{if(s.NEXT_PUBLIC_STRAPI_MEDIA_URL)return d(o(s.NEXT_PUBLIC_STRAPI_MEDIA_URL));if(s.IMAGE_HOSTNAME)return d(o(s.IMAGE_HOSTNAME));try{let e=new URL(i);if(e.hostname.endsWith("strapiapp.com"))return`${d(e.protocol)}//${e.hostname.replace("strapiapp.com","media.strapiapp.com")}`;return d(o(i))}catch(e){return"development"===s.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.media.strapiapp.com"}})(),n=s.NEXT_PUBLIC_SITE_URL||(s.NEXT_PUBLIC_API_URL&&s.NEXT_PUBLIC_API_URL.includes("strapiapp.com")?s.NEXT_PUBLIC_API_URL.replace(".strapiapp.com",".vercel.app"):"https://naturalhealingnow.vercel.app");function d(e){return e?e.replace(/^http:/,"https:"):e}function o(e){return e&&e.endsWith("/")?e.slice(0,-1):e}function c(e,t={debug:!1}){if(t.debug&&a.Ay.debug("getStrapiMediaUrl input:",{type:typeof e,isNull:null===e,isUndefined:void 0===e,value:e}),!e)return null;let r=null;if("string"==typeof e?r=e:"object"==typeof e&&(r=e.url||e.data?.attributes?.url||e.data?.url||null),!r)return t.debug,a.Ay.warn("Could not extract initial URL from mediaInput in getStrapiMediaUrl",{mediaInput:e}),null;let s=m(r);return s?s.startsWith("http://")||s.startsWith("https://")?s:l?`${l}${s.startsWith("/")?"":"/"}${s}`:(a.Ay.warn("STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl",{sanitizedUrl:s}),`${i}${s.startsWith("/")?"":"/"}${s}`):(t.debug,a.Ay.warn("URL became empty after sanitization in getStrapiMediaUrl",{originalUrl:r}),null)}function h(e){if(!e||!e.profilePicture)return null;let t=e.profilePicture,r=t.url||t.data?.attributes?.url||t.data?.url||t.formats?.thumbnail?.url;return r?c(r):c(t)}function m(e){let t;if("string"==typeof e&&(e.startsWith("https://")||e.startsWith("http://")||e.startsWith("/")))return e.startsWith("http://")?e.replace(/^http:/,"https:"):e;if(!e)return"";if("object"==typeof e&&e.url&&"string"==typeof e.url)t=e.url;else{if("string"!=typeof e)return a.Ay.warn("Invalid input type for sanitizeUrl. Expected string or object with url property.",{inputType:typeof e}),"";t=e}(t=t.trim()).toLowerCase().startsWith("undefined")&&(t=t.substring(9),a.Ay.info('Removed "undefined" prefix from URL',{original:e,new:t}));let r=i.replace(/^https?:\/\//,"").split("/")[0],s=l.replace(/^https?:\/\//,"").split("/")[0];if(r&&s&&t.includes(r)&&t.includes(s)){let e=RegExp(`(https?://)?(${r})(/*)(https?://)?(${s})`,"gi"),i=`https://${s}`;if(e.test(t)){let l=t;t=t.replace(e,i),a.Ay.info("Fixed concatenated Strapi domains",{original:l,fixed:t,apiDomain:r,mediaDomain:s})}}if(t.includes("https//")){let e=t;t=t.replace(/https\/\//g,"https://"),a.Ay.info("Fixed missing colon in URL (https//)",{original:e,fixed:t})}if(t.startsWith("//")?t=`https:${t}`:(t.includes("media.strapiapp.com")||t.includes(s))&&!t.startsWith("http")?t=`https://${t}`:(t.startsWith("localhost")||t.startsWith(r.split(".")[0]))&&(t=`https://${t}`),t.startsWith("/"))return t;if(t.startsWith("http://")||t.startsWith("https://"))try{return new URL(t),t}catch(e){if(a.Ay.error("URL parsing failed after sanitization attempts",{url:t,error:e}),!t.includes("://")&&!t.includes("."))return t;return""}return l&&t&&!t.includes("://")?(a.Ay.debug("Assuming relative media path, prepending STRAPI_MEDIA_URL",{path:t}),`/${t}`):(a.Ay.warn("sanitizeUrl could not produce a valid absolute or relative URL",{originalInput:e,finalSanitized:t}),t)}function p(e){if(!e)return;let t=m(e);if(t){if(t.startsWith("http://")||t.startsWith("https://"))return t.replace(/^http:/,"https:");if(l){let r=`${l}${t.startsWith("/")?"":"/"}${t}`;return a.Ay.debug("Constructed OG image URL from relative path",{original:e,final:r}),r.replace(/^http:/,"https:")}if(a.Ay.warn("Could not determine OG image URL confidently",{originalUrl:e,processedUrl:t}),i)return`${i}${t.startsWith("/")?"":"/"}${t}`.replace(/^http:/,"https:")}}"development"===s.NODE_ENV&&a.Ay.debug("Media Utils Initialized:",{EFFECTIVE_STRAPI_URL:i,STRAPI_MEDIA_URL:l,SITE_URL:n})}};