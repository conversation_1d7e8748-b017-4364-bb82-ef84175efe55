'use strict';

/**
 * Bootstrap function for Strapi
 * This runs when Strap<PERSON> starts
 */
module.exports = async ({ strapi }) => {
  // Set permissions for the Post View content type
  if (process.env.NODE_ENV === 'development') {
    // In development, set permissions automatically
    try {
      // Find the Public role
      const publicRole = await strapi
        .query('plugin::users-permissions.role')
        .findOne({ where: { type: 'public' } });

      if (publicRole) {
        // Get all permissions
        const permissions = await strapi
          .query('plugin::users-permissions.permission')
          .findMany({
            where: {
              role: publicRole.id,
              action: ['find', 'findOne', 'create'],
              subject: 'api::post-view.post-view',
            },
          });

        // Create an array of permission IDs to update
        const permissionIds = permissions.map(p => p.id);

        // Update permissions to enable them
        if (permissionIds.length > 0) {
          await strapi
            .query('plugin::users-permissions.permission')
            .updateMany({
              where: { id: { $in: permissionIds } },
              data: { enabled: true },
            });

          console.log('Post View permissions set for Public role');
        }
      }
    } catch (error) {
      console.error('Error setting Post View permissions:', error);
    }
  }
};
