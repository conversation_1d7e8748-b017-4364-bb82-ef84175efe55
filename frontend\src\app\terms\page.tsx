import { getStrapiContent } from '@/lib/strapi';
import MarkdownContent from '@/components/blog/MarkdownContent';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

// Define the expected shape of the data (fields directly under data)
interface TermsOfServiceData {
  id: number;
  title?: string; // Title might be in seo.metaTitle, making this optional
  content: string;
  seo?: any; // Using 'any' for flexibility, refine if SEO structure is known
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

interface StrapiSingleResponse<T> {
  data: T | null;
  meta: object;
}

// Function to generate metadata
export async function generateMetadata(): Promise<Metadata> {
  const defaultTitle = 'Terms of Service';
  const defaultDescription = 'Read our Terms of Service.';

  try {
    const response: StrapiSingleResponse<TermsOfServiceData> = await getStrapiContent.termsOfService.get();
    const pageData = response?.data;

    if (!pageData) {
      return { title: defaultTitle, description: defaultDescription };
    }

    const seoData = pageData?.seo;
    const title = seoData?.metaTitle || pageData?.title || defaultTitle;
    const description = seoData?.metaDescription || defaultDescription;

    return {
      title: title,
      description: description,
      ...(seoData?.canonicalURL && { alternates: { canonical: seoData.canonicalURL } }),
      ...(seoData?.metaRobots && { robots: seoData.metaRobots }),
    };
  } catch (error) {
    console.error('Error fetching Terms of Service metadata:', error);
    return { title: defaultTitle, description: 'Error loading page information.' };
  }
}

// The page component
export default async function TermsOfServicePage() {
  let pageData: TermsOfServiceData | null = null;

  try {
    const rawResponse = await getStrapiContent.termsOfService.get();
    pageData = rawResponse?.data;
  } catch (error) {
    console.error('Failed to fetch Terms of Service page data:', error);
  }

  if (!pageData) {
     console.error('Terms of Service data object is null or undefined after fetch.');
     notFound();
  }

  if (!pageData.content) {
     console.error('Terms of Service data fetched, but content field is missing or empty.');
     // Allow rendering with a message if content is missing
  }

  const title = pageData.seo?.metaTitle || pageData.title || 'Terms of Service';
  const content = pageData.content;

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Title is handled by metadata, not shown in body */}
      <div className="prose lg:prose-xl max-w-none mx-auto">
        {content ? (
          <MarkdownContent content={content} />
        ) : (
          <p>Terms of Service content is not available at the moment.</p>
        )}
      </div>
    </div>
  );
}
