# Natural Healing Now Scripts

This directory contains utility scripts for the Natural Healing Now project.

## Import Clinics Script for Strapi 5

The `import-clinics-strapi5.js` script imports clinic data from a CSV file into the Strapi 5 CMS.

### Features

- Imports clinics from CSV file into Strapi 5
- Checks if a clinic with the same slug already exists
- Updates existing clinics or creates new entries as needed
- <PERSON>perly handles component structures (address, contactInfo, location)
- Processes multi-select fields (appointmentOptions, paymentMethod)
- Compatible with Strapi 5's REST API format

### Prerequisites

1. Node.js installed
2. Strapi running
3. Strapi API token with write permissions

### Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file based on `.env.example`:
   ```
   cp .env.example .env
   ```

3. Edit the `.env` file and add your Strapi API token:
   ```
   STRAPI_URL=http://localhost:1337
   STRAPI_API_TOKEN=your_api_token_here
   ```

### Usage

Run the script directly:

```
node import-clinics-strapi5.js
```

### CSV File Format

The script expects a CSV file with the following columns:

- `id` - Optional ID (not used for import)
- `name` - Clinic name
- `slug` - Unique slug for the clinic
- `description` - Description of the clinic
- `isActive` - Boolean indicating if the clinic is active
- `isFeatured` - Boolean indicating if the clinic is featured
- `isVerified` - Boolean indicating if the clinic is verified
- `paid` - Boolean indicating if the clinic is paid
- `appointmentOptions` - Comma-separated list of appointment options
- `paymentMethod` - Comma-separated list of payment methods
- `phoneNumber` - Phone number for contact info
- `emailAddress` - Email address for contact info
- `websiteUrl` - Website URL for contact info
- `streetAddress1` - Street address line 1
- `streetAddress2` - Street address line 2
- `city` - City
- `stateProvince` - State/Province code (e.g., NY, CA)
- `country` - Country (defaults to USA)
- `postalCode` - Postal code
- `googlePlaceId` - Google Place ID
- `latitude` - Latitude coordinate
- `longitude` - Longitude coordinate
- `videoEmbedUrl` - URL for video embed
