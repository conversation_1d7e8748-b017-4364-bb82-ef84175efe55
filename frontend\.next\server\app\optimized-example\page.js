(()=>{var e={};e.id=6923,e.ids=[6923],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13710:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(60687),a=s(85814),i=s.n(a),n=s(16189);function l(){let e=(0,n.usePathname)();return(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Optimization Examples"}),(0,r.jsx)("p",{className:"mb-4",children:"These examples demonstrate different approaches to reducing API calls to Strapi."}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[{href:"/optimized-example",label:"Server-Side Optimization"},{href:"/optimized-client-example",label:"Client-Side Optimization"}].map(t=>(0,r.jsx)(i(),{href:t.href,className:`px-4 py-2 rounded ${e===t.href?"bg-blue-500 text-white":"bg-white text-blue-500 hover:bg-blue-100"}`,children:t.label},t.href))})]})}},17756:(e,t,s)=>{Promise.resolve().then(s.bind(s,64780))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31417:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(37413),a=s(61120);let i=(0,a.cache)(async(e,t={},s=!0)=>{try{let s=e.startsWith("/")?e:`/${e}`,{params:r,revalidate:a=3600,tags:i=[]}=t,n=r?"?"+new URLSearchParams(Object.entries(r).reduce((e,[t,s])=>("object"==typeof s&&null!==s?e[t]=JSON.stringify(s):e[t]=String(s),e),{})).toString():"",l=await fetch(`https://nice-badge-2130241d6c.strapiapp.com/api${s}${n}`,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.STRAPI_API_TOKEN||""}`},next:{revalidate:a,tags:i}});if(!l.ok)throw Error(`Error fetching from API: ${l.status} ${l.statusText}`);return l.json()}catch(t){if(console.error(`Error fetching from API (${e}):`,t),e.includes("/")&&e.split("/").filter(Boolean).length>0)return{data:[]};return{}}}),n=(0,a.cache)(async(e,t={})=>{let{id:s,params:r,revalidate:a=!1,isDetailPage:n=!1}=t,l=s?`/${e}/${s}`:`/${e}`,o=function(e,t){let s=[`strapi-${e}`];return t&&s.push(`strapi-${e}-${t}`),s}(e,s);n||o.push("strapi-content");try{return i(l,{params:r,revalidate:!n&&(a||3600),tags:o})}catch(t){return console.error(`Error in fetchContentType for ${e}:`,t),{data:[]}}});(0,a.cache)(async(e,t={},s=6048e5)=>i(e,t,s,!1,!0));let l=(0,a.cache)(async()=>n("global-setting",{params:{populate:"*"}},!0)),o=(0,a.cache)(async(e=10)=>n("categories",{params:{pagination:{pageSize:e},populate:"*"}},!0));(0,a.cache)(async(e=10)=>n("specialties",{params:{pagination:{pageSize:e},populate:"*"}},!0));let d=(0,a.cache)(async(e={})=>{let{page:t=1,pageSize:s=10,featured:r,location:a,query:i,specialtySlug:l}=e,o={pagination:{page:t,pageSize:s},populate:"*"},d={};return r&&(d.isFeatured={$eq:!0}),a&&(d.$or=[{city:{$containsi:a}},{state:{$containsi:a}},{zipCode:{$containsi:a}}]),i&&(d.$or=[...d.$or||[],{name:{$containsi:i}},{description:{$containsi:i}}]),l&&(d.specialties={slug:{$eq:l}}),Object.keys(d).length>0&&(o.filters=d),n("clinics",{params:o},!0)}),c=(0,a.cache)(async(e={})=>{let{page:t=1,pageSize:s=10,featured:r,location:a,query:i,specialtySlug:l}=e,o={pagination:{page:t,pageSize:s},populate:"*"},d={};return r&&(d.isFeatured={$eq:!0}),a&&(d.clinic={$or:[{city:{$containsi:a}},{state:{$containsi:a}},{zipCode:{$containsi:a}}]}),i&&(d.$or=[{firstName:{$containsi:i}},{lastName:{$containsi:i}},{bio:{$containsi:i}}]),l&&(d.specialties={slug:{$eq:l}}),Object.keys(d).length>0&&(o.filters=d),n("practitioners",{params:o},!0)}),h=(0,a.cache)(async(e={})=>{let{page:t=1,pageSize:s=10,featured:r,categorySlug:a,query:i}=e;try{let e={sort:"publishDate:desc",pagination:{page:t,pageSize:s},populate:{featuredImage:!0,author:{populate:{profilePicture:!0}},categories:!0}},l={};r&&(l.isFeatured={$eq:!0}),a&&(l.categories={slug:{$eq:a}}),i&&(l.$or=[{title:{$containsi:i}},{excerpt:{$containsi:i}},{content:{$containsi:i}}]),Object.keys(l).length>0&&(e.filters=l);try{return await n("blog-posts",{params:e},!0)}catch(t){return console.log("Error fetching from blog-posts, trying blogs endpoint"),await n("blogs",{params:e},!0)}}catch(e){return console.error("Error in getBlogPosts:",e),{data:[]}}});var p=s(64780);async function u(){try{return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Optimized Data Fetching Example"}),(0,r.jsx)(p.default,{}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("p",{className:"text-lg",children:"This page demonstrates server-side optimized data fetching from Strapi. The data is cached using React's cache function and Next.js built-in caching."})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(j,{title:"Global Settings"}),children:(0,r.jsx)(m,{})}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(j,{title:"Categories"}),children:(0,r.jsx)(x,{})}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(j,{title:"Featured Clinics"}),children:(0,r.jsx)(g,{})}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(j,{title:"Featured Practitioners"}),children:(0,r.jsx)(b,{})}),(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(j,{title:"Latest Blog Posts"}),children:(0,r.jsx)(f,{})})]}),(0,r.jsxs)("div",{className:"mt-8 p-6 bg-blue-50 rounded-lg",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"How This Reduces API Calls"}),(0,r.jsx)("p",{className:"mb-2",children:"This implementation reduces API calls to Strapi in several ways:"}),(0,r.jsxs)("ol",{className:"list-decimal pl-5 space-y-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"React's cache function:"})," Deduplicates requests within the same render pass."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Content-type specific caching:"})," Different cache durations based on how frequently content changes (e.g., global settings are cached longer than blog posts)."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Suspense and streaming:"})," Allows for parallel data fetching and progressive rendering."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Server-side rendering:"})," Data is fetched once on the server and sent to the client."]})]}),(0,r.jsxs)("p",{className:"mt-4",children:["For more details, see the documentation in ",(0,r.jsx)("code",{children:"frontend/src/docs/OPTIMIZED_API_CALLS.md"}),"."]})]})]})}catch(e){return console.error("Error rendering OptimizedExamplePage:",e),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Optimized Data Fetching Example"}),(0,r.jsx)(p.default,{}),(0,r.jsxs)("div",{className:"bg-red-50 p-6 rounded-lg my-8 text-red-600",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Error Loading Page"}),(0,r.jsx)("p",{children:"There was an error loading the optimized example page. Please try again later."})]}),(0,r.jsxs)("div",{className:"mt-8 p-6 bg-blue-50 rounded-lg",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"How This Reduces API Calls"}),(0,r.jsx)("p",{className:"mb-2",children:"This implementation reduces API calls to Strapi in several ways:"}),(0,r.jsxs)("ol",{className:"list-decimal pl-5 space-y-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"React's cache function:"})," Deduplicates requests within the same render pass."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Content-type specific caching:"})," Different cache durations based on how frequently content changes (e.g., global settings are cached longer than blog posts)."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Suspense and streaming:"})," Allows for parallel data fetching and progressive rendering."]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Server-side rendering:"})," Data is fetched once on the server and sent to the client."]})]}),(0,r.jsxs)("p",{className:"mt-4",children:["For more details, see the documentation in ",(0,r.jsx)("code",{children:"frontend/src/docs/OPTIMIZED_API_CALLS.md"}),"."]})]})]})}}async function m(){try{let e=await l();return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Global Settings"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded overflow-auto max-h-60",children:JSON.stringify(e,null,2)})]})}catch(e){return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Global Settings"}),(0,r.jsx)("div",{className:"bg-red-50 p-4 rounded text-red-500",children:"Error loading global settings"})]})}}async function x(){try{let e=await o(5);return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Categories"}),(0,r.jsx)("ul",{className:"list-disc pl-5",children:e.data?.map(e=>(0,r.jsx)("li",{children:e.name||e.attributes?.name},e.id||e.documentId))})]})}catch(e){return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Categories"}),(0,r.jsx)("div",{className:"bg-red-50 p-4 rounded text-red-500",children:"Error loading categories"})]})}}async function g(){try{let e=await d({featured:!0,pageSize:5});return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Featured Clinics"}),(0,r.jsx)("ul",{className:"list-disc pl-5",children:e.data?.map(e=>(0,r.jsx)("li",{children:e.name||e.attributes?.name},e.id||e.documentId))})]})}catch(e){return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Featured Clinics"}),(0,r.jsx)("div",{className:"bg-red-50 p-4 rounded text-red-500",children:"Error loading clinics"})]})}}async function b(){try{let e=await c({featured:!0,pageSize:5});return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Featured Practitioners"}),(0,r.jsx)("ul",{className:"list-disc pl-5",children:e.data?.map(e=>{let t=e.firstName||e.attributes?.firstName,s=e.lastName||e.attributes?.lastName;return(0,r.jsxs)("li",{children:[t," ",s]},e.id||e.documentId)})})]})}catch(e){return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Featured Practitioners"}),(0,r.jsx)("div",{className:"bg-red-50 p-4 rounded text-red-500",children:"Error loading practitioners"})]})}}async function f(){try{let e=await h({pageSize:5});return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Latest Blog Posts"}),(0,r.jsx)("ul",{className:"list-disc pl-5",children:e.data?.map(e=>(0,r.jsx)("li",{children:e.title||e.attributes?.title},e.id||e.documentId))})]})}catch(e){return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Latest Blog Posts"}),(0,r.jsx)("div",{className:"bg-red-50 p-4 rounded text-red-500",children:"Error loading blog posts"})]})}}function j({title:e}){return(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md animate-pulse",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:e}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]})]})}},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64780:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\examples\\\\OptimizationNav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\examples\\OptimizationNav.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},75900:(e,t,s)=>{Promise.resolve().then(s.bind(s,13710))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},97338:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>h,tree:()=>o});var r=s(65239),a=s(48088),i=s(31369),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let o={children:["",{children:["optimized-example",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,31417)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\optimized-example\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\optimized-example\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/optimized-example/page",pathname:"/optimized-example",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,1330,3376,6391,2975,8446,270],()=>s(97338));module.exports=r})();