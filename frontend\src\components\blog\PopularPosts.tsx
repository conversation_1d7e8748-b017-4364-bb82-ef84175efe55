"use client";

import LazyImage from '@/components/shared/LazyImage';
import Link from 'next/link';
import { format } from 'date-fns';
import { sanitizeUrl } from '@/lib/mediaUtils';
import { FiUser } from 'react-icons/fi';

interface PopularPost {
  id: string;
  title: string;
  slug: string;
  featured_image?: string | null;
  publish_date: string;
  author?: {
    name: string;
    slug: string;
    profile_picture?: string | null;
  } | null;
}

interface PopularPostsProps {
  posts: PopularPost[];
}

const PopularPosts = ({ posts }: PopularPostsProps) => {
  // Use the imported sanitizeUrl function from mediaUtils

  // Debug posts data in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Popular Posts data:', posts.map(post => ({
      title: post.title,
      author: post.author,
      profile_picture: post.author?.profile_picture
    })));
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Popular Articles</h3>
      <div className="space-y-4">
        {posts.map(post => (
          <div key={post.id} className="flex gap-3">
            <div className="flex-shrink-0 w-16 h-16 relative rounded overflow-hidden">
              {post.featured_image ? (
                <LazyImage
                  src={sanitizeUrl(post.featured_image)}
                  alt={post.title}
                  width={64} // Aspect ratio hint
                  height={64} // Aspect ratio hint
                  fillContainer={true}
                  className="object-cover" // fillContainer will make it fill the parent div
                  sizes="64px" // Still useful for next/image optimization
                  showPlaceholder={true}
                />
              ) : (
                <div className="w-full h-full bg-emerald-100 flex items-center justify-center">
                  <span className="text-emerald-700">{post.title.charAt(0)}</span>
                </div>
              )}
            </div>
            <div>
              <h4 className="font-medium text-gray-800 line-clamp-2">
                <Link href={`/blog/${post.slug}`} className="hover:text-emerald-600">
                  {post.title}
                </Link>
              </h4>
              <div className="flex items-center text-xs text-gray-500 mt-1">
                {post.author && (
                  <>
                    <FiUser className="text-emerald-600 mr-1.5" />
                    <Link
                      href={`/blog/authors/${post.author.slug}`}
                      className="hover:text-emerald-600 mr-2"
                    >
                      {post.author.name}
                    </Link>
                    <span className="mx-1">•</span>
                  </>
                )}
                {format(new Date(post.publish_date), 'MMM d, yyyy')}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PopularPosts;
