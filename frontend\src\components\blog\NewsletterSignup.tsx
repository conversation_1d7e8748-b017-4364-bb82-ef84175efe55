"use client";

import { useState } from 'react';
import { <PERSON><PERSON>ail, FiCheck } from 'react-icons/fi';

const NewsletterSignup = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic email validation
    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    setError('');
    setIsSubmitting(true);
    
    // Simulate API call
    try {
      // Replace with actual newsletter signup API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setIsSubmitted(true);
      setEmail('');
    } catch (err) {
      setError('Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-6 border border-emerald-100">
      <h3 className="text-lg font-semibold text-gray-800 mb-2">Get Wellness Tips</h3>
      <p className="text-gray-600 text-sm mb-4">
        Subscribe to receive the latest health insights and natural healing tips directly in your inbox.
      </p>
      
      {isSubmitted ? (
        <div className="bg-emerald-100 text-emerald-700 p-4 rounded-lg flex items-center">
          <FiCheck className="mr-2 flex-shrink-0" />
          <p className="text-sm">Thank you for subscribing! Check your inbox for a confirmation email.</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-3">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiMail className="text-gray-400" />
            </div>
            <input
              type="email"
              placeholder="Your email address"
              className={`w-full pl-10 pr-4 py-2 border ${error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-emerald-500 focus:border-emerald-500'} rounded-lg`}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isSubmitting}
            />
          </div>
          
          {error && <p className="text-red-500 text-xs">{error}</p>}
          
          <button
            type="submit"
            className="w-full bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Subscribing...' : 'Subscribe'} <span className="ml-2">→</span>
          </button>
          
          <div className="text-xs text-gray-500 mt-3">
            We respect your privacy. Unsubscribe at any time.
          </div>
        </form>
      )}
    </div>
  );
};

export default NewsletterSignup;
