{"kind": "collectionType", "collectionName": "clinics", "info": {"singularName": "clinic", "pluralName": "clinics", "displayName": "Clinic", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "richtext"}, "contactInfo": {"type": "component", "repeatable": false, "component": "contact.contact-information"}, "address": {"type": "component", "repeatable": false, "component": "address.address"}, "location": {"type": "component", "repeatable": false, "component": "location.location-coordinates"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "featuredImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "gallery": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files"]}, "isActive": {"type": "boolean", "default": true}, "isFeatured": {"type": "boolean", "default": false}, "videoEmbed": {"type": "customField", "customField": "plugin::oembed.oembed"}, "categories": {"type": "relation", "relation": "manyToMany", "target": "api::category.category", "inversedBy": "clinics"}, "services": {"type": "relation", "relation": "manyToMany", "target": "api::service.service", "inversedBy": "clinics"}, "specialties": {"type": "relation", "relation": "manyToMany", "target": "api::specialty.specialty", "inversedBy": "clinics"}, "conditions": {"type": "relation", "relation": "manyToMany", "target": "api::condition.condition", "inversedBy": "clinics"}, "insurance_providers": {"type": "relation", "relation": "manyToMany", "target": "api::insurance-provider.insurance-provider", "inversedBy": "clinics"}, "practitioners": {"type": "relation", "relation": "manyToMany", "target": "api::practitioner.practitioner", "inversedBy": "clinics"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "isVerified": {"type": "boolean", "default": false}, "paid": {"type": "boolean", "default": false}, "openingHours": {"type": "component", "repeatable": true, "component": "location.opening-hours"}, "payment_methods": {"type": "relation", "relation": "manyToMany", "target": "api::payment-method.payment-method", "inversedBy": "clinics"}, "appointment_options": {"type": "relation", "relation": "manyToMany", "target": "api::appointment-option.appointment-option", "inversedBy": "clinics"}}}