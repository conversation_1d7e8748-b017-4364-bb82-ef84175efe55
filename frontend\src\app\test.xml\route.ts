// This is a simple test route handler to verify that XML routes are working correctly

export async function GET(request: Request): Promise<Response> {
  // Create a simple XML response
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<test>
  <message>This is a test XML response</message>
  <timestamp>${new Date().toISOString()}</timestamp>
</test>`;

  // Return the XML with the correct content type
  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}
