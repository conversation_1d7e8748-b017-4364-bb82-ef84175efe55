'use client';

import { useEffect, useState } from 'react';

/**
 * Component to check environment variables and display warnings in development
 * This component will only render in development mode
 */
const EnvCheck = () => {
  const [showWarning, setShowWarning] = useState(false);
  const [warnings, setWarnings] = useState<string[]>([]);

  useEffect(() => {
    // Only run in development mode
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    const newWarnings: string[] = [];

    // Check for API URL
    if (!process.env.NEXT_PUBLIC_API_URL) {
      newWarnings.push('NEXT_PUBLIC_API_URL is not set. This is required for API requests.');
    }

    // Check for API token
    if (!process.env.STRAPI_API_TOKEN) {
      newWarnings.push('STRAPI_API_TOKEN is not set. This is required for authenticated API requests.');
    }

    // Set warnings and show if there are any
    setWarnings(newWarnings);
    setShowWarning(newWarnings.length > 0);
  }, []);

  // Don't render anything in production or if there are no warnings
  if (process.env.NODE_ENV !== 'development' || !showWarning) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-amber-100 border-t border-amber-300 p-4 z-50">
      <div className="container mx-auto">
        <h3 className="text-amber-800 font-bold mb-2">⚠️ Environment Variable Warnings</h3>
        <ul className="list-disc pl-5 text-amber-700">
          {warnings.map((warning, index) => (
            <li key={index}>{warning}</li>
          ))}
        </ul>
        <p className="mt-2 text-amber-600 text-sm">
          These warnings are only visible in development mode. Check your .env.local file.
        </p>
        <button
          onClick={() => setShowWarning(false)}
          className="mt-2 px-3 py-1 bg-amber-200 hover:bg-amber-300 text-amber-800 rounded text-sm"
        >
          Dismiss
        </button>
      </div>
    </div>
  );
};

export default EnvCheck;
