/**
 * <PERSON><PERSON><PERSON> to fix the appointmentOptions field for clinics
 * 
 * This script addresses the issue with the clinic "NY Center For Integrative Health"
 * that has ID 3 in production and ID 8 in development, causing a Strapi admin error
 * with TypeError: Cannot use 'in' operator to search for '__component' in In-person
 * 
 * Usage:
 * - Run this script in the Strapi project directory
 * - NODE_ENV=production node scripts/fix-clinic-appointment-options.js (for production)
 * - NODE_ENV=development node scripts/fix-clinic-appointment-options.js (for development)
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Function to get Strapi instance
async function getStrapiInstance() {
  try {
    // This is a workaround to get the Strapi instance in a script
    const strapiDir = process.env.NODE_ENV === 'production' 
      ? path.resolve(__dirname, '../strapi-cms') 
      : path.resolve(__dirname, '../strapi-cms');
    
    process.chdir(strapiDir);
    const strapi = await require('@strapi/strapi').compile();
    await strapi.load();
    return strapi;
  } catch (error) {
    console.error('Error getting Strapi instance:', error);
    process.exit(1);
  }
}

// Function to fix the appointmentOptions field for a specific clinic
async function fixClinicAppointmentOptions(strapi, clinicId) {
  try {
    console.log(`Fixing appointmentOptions for clinic with ID ${clinicId}...`);
    
    // Get the clinic
    const clinic = await strapi.entityService.findOne('api::clinic.clinic', clinicId, {
      populate: ['appointmentOptions'],
    });
    
    if (!clinic) {
      console.error(`Clinic with ID ${clinicId} not found.`);
      return;
    }
    
    console.log(`Found clinic: ${clinic.name}`);
    
    // Check if appointmentOptions is causing the issue
    if (clinic.appointmentOptions && !Array.isArray(clinic.appointmentOptions)) {
      console.log('appointmentOptions field is not an array, fixing...');
      
      // Update the clinic with an empty array for appointmentOptions
      await strapi.entityService.update('api::clinic.clinic', clinicId, {
        data: {
          appointmentOptions: [],
        },
      });
      
      console.log('Successfully fixed appointmentOptions field.');
    } else if (!clinic.appointmentOptions) {
      console.log('appointmentOptions field is null or undefined, setting to empty array...');
      
      // Update the clinic with an empty array for appointmentOptions
      await strapi.entityService.update('api::clinic.clinic', clinicId, {
        data: {
          appointmentOptions: [],
        },
      });
      
      console.log('Successfully set appointmentOptions to empty array.');
    } else {
      console.log('appointmentOptions field is already an array, no fix needed.');
    }
  } catch (error) {
    console.error('Error fixing clinic:', error);
  }
}

// Function to fix all clinics
async function fixAllClinics(strapi) {
  try {
    console.log('Fetching all clinics...');
    
    // Get all clinics
    const { results: clinics } = await strapi.entityService.findMany('api::clinic.clinic', {
      fields: ['id', 'name', 'slug'],
    });
    
    console.log(`Found ${clinics.length} clinics.`);
    
    // Fix each clinic
    for (const clinic of clinics) {
      await fixClinicAppointmentOptions(strapi, clinic.id);
    }
    
    console.log('Finished fixing all clinics.');
  } catch (error) {
    console.error('Error fixing clinics:', error);
  }
}

// Main function
async function main() {
  let strapi;
  try {
    strapi = await getStrapiInstance();
    
    // Get the environment
    const env = process.env.NODE_ENV || 'development';
    console.log(`Running in ${env} environment.`);
    
    // Get the clinic ID based on the environment
    const clinicId = env === 'production' ? 3 : 8;
    
    // Fix the specific problematic clinic
    await fixClinicAppointmentOptions(strapi, clinicId);
    
    // Ask if user wants to fix all clinics
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    
    readline.question('Do you want to fix appointmentOptions for all clinics? (y/n) ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        await fixAllClinics(strapi);
      }
      
      // Cleanup and exit
      readline.close();
      await strapi.destroy();
      process.exit(0);
    });
  } catch (error) {
    console.error('Error in main function:', error);
    if (strapi) {
      await strapi.destroy();
    }
    process.exit(1);
  }
}

// Run the main function
main();
