// Test script to verify revalidation endpoints
const fetch = require('node-fetch');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Change to your local Next.js URL
const REVALIDATE_SECRET = process.env.STRAPI_REVALIDATE_SECRET || 'your-secret-here'; // Replace with your actual secret

// Test functions
async function testClinicsRevalidation() {
  console.log('\n--- Testing Clinics Revalidation ---');
  
  // Simulate a Strapi webhook payload for a clinic update
  const payload = {
    event: 'entry.update',
    model: 'api::clinic.clinic',
    entry: {
      id: 1,
      slug: 'test-clinic'
    }
  };

  try {
    const response = await fetch(`${BASE_URL}/api/revalidate/clinics`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Revalidate-Secret': REVALIDATE_SECRET
      },
      body: JSON.stringify(payload)
    });

    const data = await response.json();
    console.log(`Status: ${response.status}`);
    console.log('Response:', data);
  } catch (error) {
    console.error('Error testing clinics revalidation:', error);
  }
}

async function testPractitionersRevalidation() {
  console.log('\n--- Testing Practitioners Revalidation ---');
  
  // Simulate a Strapi webhook payload for a practitioner update
  const payload = {
    event: 'entry.update',
    model: 'api::practitioner.practitioner',
    entry: {
      id: 1,
      slug: 'test-practitioner'
    }
  };

  try {
    const response = await fetch(`${BASE_URL}/api/revalidate/practitioners`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Revalidate-Secret': REVALIDATE_SECRET
      },
      body: JSON.stringify(payload)
    });

    const data = await response.json();
    console.log(`Status: ${response.status}`);
    console.log('Response:', data);
  } catch (error) {
    console.error('Error testing practitioners revalidation:', error);
  }
}

async function testMainRevalidationEndpoint() {
  console.log('\n--- Testing Main Revalidation Endpoint ---');
  
  // Test revalidating clinics list
  const clinicsPayload = {
    contentType: 'clinic',
    paths: ['/clinics'],
    tags: ['strapi-clinics-list']
  };

  try {
    const response = await fetch(`${BASE_URL}/api/revalidate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Revalidate-Secret': REVALIDATE_SECRET
      },
      body: JSON.stringify(clinicsPayload)
    });

    const data = await response.json();
    console.log(`Status: ${response.status}`);
    console.log('Response:', data);
  } catch (error) {
    console.error('Error testing main revalidation endpoint:', error);
  }
}

// Run the tests
async function runTests() {
  console.log('Starting revalidation endpoint tests...');
  
  await testClinicsRevalidation();
  await testPractitionersRevalidation();
  await testMainRevalidationEndpoint();
  
  console.log('\nAll tests completed.');
}

runTests();
