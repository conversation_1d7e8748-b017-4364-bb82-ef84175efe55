"use strict";exports.id=1330,exports.ids=[1330],exports.modules={1328:t=>{t.exports=EvalError},1441:t=>{t.exports=ReferenceError},1700:(t,r,e)=>{var o=e(35580);t.exports=function(t){return o(t)||0===t?t:t<0?-1:1}},3489:t=>{t.exports=Math.abs},3495:t=>{t.exports=Math.max},3922:t=>{var r=Object.defineProperty||!1;if(r)try{r({},"a",{value:1})}catch(t){r=!1}t.exports=r},4957:t=>{t.exports=Math.round},9581:(t,r,e)=>{var o,n=e(70649),p=e(54198);try{o=[].__proto__===Array.prototype}catch(t){if(!t||"object"!=typeof t||!("code"in t)||"ERR_PROTO_ACCESS"!==t.code)throw t}var a=!!o&&p&&p(Object.prototype,"__proto__"),y=Object,i=y.getPrototypeOf;t.exports=a&&"function"==typeof a.get?n([a.get]):"function"==typeof i&&function(t){return i(null==t?t:y(t))}},23632:t=>{var r=Object.prototype.toString,e=Math.max,o=function(t,r){for(var e=[],o=0;o<t.length;o+=1)e[o]=t[o];for(var n=0;n<r.length;n+=1)e[n+t.length]=r[n];return e},n=function(t,r){for(var e=[],o=r||0,n=0;o<t.length;o+=1,n+=1)e[n]=t[o];return e},p=function(t,r){for(var e="",o=0;o<t.length;o+=1)e+=t[o],o+1<t.length&&(e+=r);return e};t.exports=function(t){var a,y=this;if("function"!=typeof y||"[object Function]"!==r.apply(y))throw TypeError("Function.prototype.bind called on incompatible "+y);for(var i=n(arguments,1),f=e(0,y.length-i.length),c=[],l=0;l<f;l++)c[l]="$"+l;if(a=Function("binder","return function ("+p(c,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof a){var r=y.apply(this,o(i,arguments));return Object(r)===r?r:this}return y.apply(t,o(i,arguments))}),y.prototype){var u=function(){};u.prototype=y.prototype,a.prototype=new u,u.prototype=null}return a}},24513:t=>{t.exports=RangeError},25586:t=>{t.exports=Object.getOwnPropertyDescriptor},25907:(t,r,e)=>{var o=e(42545),n=e(72595),p=e(9581);t.exports=o?function(t){return o(t)}:n?function(t){if(!t||"object"!=typeof t&&"function"!=typeof t)throw TypeError("getProto: not an object");return n(t)}:p?function(t){return p(t)}:null},35580:t=>{t.exports=Number.isNaN||function(t){return t!=t}},36456:(t,r,e)=>{var o="undefined"!=typeof Symbol&&Symbol,n=e(92590);t.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},39703:t=>{t.exports=Math.floor},42257:t=>{t.exports=SyntaxError},42545:t=>{t.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},44332:t=>{t.exports=Error},45149:t=>{t.exports=Function.prototype.call},46164:(t,r,e)=>{var o=e(23632);t.exports=Function.prototype.bind||o},47284:(t,r,e)=>{var o=Function.prototype.call,n=Object.prototype.hasOwnProperty;t.exports=e(46164).call(o,n)},50095:t=>{t.exports=Math.pow},54198:(t,r,e)=>{var o=e(25586);if(o)try{o([],"length")}catch(t){o=null}t.exports=o},58803:(t,r,e)=>{var o=e(46164),n=e(93377),p=e(45149);t.exports=e(93926)||o.call(p,n)},70649:(t,r,e)=>{var o=e(46164),n=e(88486),p=e(45149),a=e(58803);t.exports=function(t){if(t.length<1||"function"!=typeof t[0])throw new n("a function is required");return a(o,p,t)}},72595:(t,r,e)=>{t.exports=e(98363).getPrototypeOf||null},78022:t=>{t.exports=URIError},81330:(t,r,e)=>{var o,n=e(98363),p=e(44332),a=e(1328),y=e(24513),i=e(1441),f=e(42257),c=e(88486),l=e(78022),u=e(3489),s=e(39703),d=e(3495),A=e(96961),g=e(50095),P=e(4957),b=e(1700),h=Function,m=function(t){try{return h('"use strict"; return ('+t+").constructor;")()}catch(t){}},S=e(54198),v=e(3922),x=function(){throw new c},O=S?function(){try{return arguments.callee,x}catch(t){try{return S(arguments,"callee").get}catch(t){return x}}}():x,E=e(36456)(),w=e(25907),F=e(72595),I=e(42545),R=e(93377),j=e(45149),U={},M="undefined"!=typeof Uint8Array&&w?w(Uint8Array):o,_={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":E&&w?w([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":U,"%AsyncGenerator%":U,"%AsyncGeneratorFunction%":U,"%AsyncIteratorPrototype%":U,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":p,"%eval%":eval,"%EvalError%":a,"%Float16Array%":"undefined"==typeof Float16Array?o:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":h,"%GeneratorFunction%":U,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":E&&w?w(w([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&E&&w?w(new Map()[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":n,"%Object.getOwnPropertyDescriptor%":S,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":y,"%ReferenceError%":i,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&E&&w?w(new Set()[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":E&&w?w(""[Symbol.iterator]()):o,"%Symbol%":E?Symbol:o,"%SyntaxError%":f,"%ThrowTypeError%":O,"%TypedArray%":M,"%TypeError%":c,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":l,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet,"%Function.prototype.call%":j,"%Function.prototype.apply%":R,"%Object.defineProperty%":v,"%Object.getPrototypeOf%":F,"%Math.abs%":u,"%Math.floor%":s,"%Math.max%":d,"%Math.min%":A,"%Math.pow%":g,"%Math.round%":P,"%Math.sign%":b,"%Reflect.getPrototypeOf%":I};if(w)try{null.error}catch(t){var B=w(w(t));_["%Error.prototype%"]=B}var N=function t(r){var e;if("%AsyncFunction%"===r)e=m("async function () {}");else if("%GeneratorFunction%"===r)e=m("function* () {}");else if("%AsyncGeneratorFunction%"===r)e=m("async function* () {}");else if("%AsyncGenerator%"===r){var o=t("%AsyncGeneratorFunction%");o&&(e=o.prototype)}else if("%AsyncIteratorPrototype%"===r){var n=t("%AsyncGenerator%");n&&w&&(e=w(n.prototype))}return _[r]=e,e},k={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},G=e(46164),D=e(47284),W=G.call(j,Array.prototype.concat),T=G.call(R,Array.prototype.splice),C=G.call(j,String.prototype.replace),J=G.call(j,String.prototype.slice),V=G.call(j,RegExp.prototype.exec),$=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,q=/\\(\\)?/g,z=function(t){var r=J(t,0,1),e=J(t,-1);if("%"===r&&"%"!==e)throw new f("invalid intrinsic syntax, expected closing `%`");if("%"===e&&"%"!==r)throw new f("invalid intrinsic syntax, expected opening `%`");var o=[];return C(t,$,function(t,r,e,n){o[o.length]=e?C(n,q,"$1"):r||t}),o},H=function(t,r){var e,o=t;if(D(k,o)&&(o="%"+(e=k[o])[0]+"%"),D(_,o)){var n=_[o];if(n===U&&(n=N(o)),void 0===n&&!r)throw new c("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:e,name:o,value:n}}throw new f("intrinsic "+t+" does not exist!")};t.exports=function(t,r){if("string"!=typeof t||0===t.length)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof r)throw new c('"allowMissing" argument must be a boolean');if(null===V(/^%?[^%]*%?$/,t))throw new f("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var e=z(t),o=e.length>0?e[0]:"",n=H("%"+o+"%",r),p=n.name,a=n.value,y=!1,i=n.alias;i&&(o=i[0],T(e,W([0,1],i)));for(var l=1,u=!0;l<e.length;l+=1){var s=e[l],d=J(s,0,1),A=J(s,-1);if(('"'===d||"'"===d||"`"===d||'"'===A||"'"===A||"`"===A)&&d!==A)throw new f("property names with quotes must have matching quotes");if("constructor"!==s&&u||(y=!0),o+="."+s,D(_,p="%"+o+"%"))a=_[p];else if(null!=a){if(!(s in a)){if(!r)throw new c("base intrinsic for "+t+" exists, but the property is not available.");return}if(S&&l+1>=e.length){var g=S(a,s);a=(u=!!g)&&"get"in g&&!("originalValue"in g.get)?g.get:a[s]}else u=D(a,s),a=a[s];u&&!y&&(_[p]=a)}}return a}},88486:t=>{t.exports=TypeError},92590:t=>{t.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},r=Symbol("test"),e=Object(r);if("string"==typeof r||"[object Symbol]"!==Object.prototype.toString.call(r)||"[object Symbol]"!==Object.prototype.toString.call(e))return!1;for(var o in t[r]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var n=Object.getOwnPropertySymbols(t);if(1!==n.length||n[0]!==r||!Object.prototype.propertyIsEnumerable.call(t,r))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var p=Object.getOwnPropertyDescriptor(t,r);if(42!==p.value||!0!==p.enumerable)return!1}return!0}},93377:t=>{t.exports=Function.prototype.apply},93926:t=>{t.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},96961:t=>{t.exports=Math.min},98363:t=>{t.exports=Object}};