(()=>{var e={};e.id=6592,e.ids=[6592],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76537:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{POST:()=>u});var n=r(96559),a=r(48088),i=r(37719),o=r(32190),d=r(62351);let l=process.env.REVALIDATION_TOKEN;async function u(e){try{if(e.nextUrl.searchParams.get("secret")!==l)return o.NextResponse.json({message:"Invalid revalidation token"},{status:401});let t=await e.json();return console.log("Received conditions revalidation webhook:",JSON.stringify({event:t.event,model:t.model,entry:t.entry?{id:t.entry.id,slug:t.entry.slug}:null})),(0,d.revalidatePath)("/conditions"),(0,d.revalidateTag)("strapi-conditions"),(0,d.revalidateTag)("strapi-conditions-all"),t.entry&&t.entry.slug&&((0,d.revalidatePath)(`/conditions/${t.entry.slug}`),(0,d.revalidateTag)(`strapi-condition-${t.entry.slug}`)),o.NextResponse.json({revalidated:!0,message:"Conditions revalidated successfully",timestamp:new Date().toISOString()},{status:200})}catch(e){return console.error("Error revalidating conditions:",e),o.NextResponse.json({revalidated:!1,message:"Error revalidating conditions",error:e instanceof Error?e.message:String(e)},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/revalidate/conditions/route",pathname:"/api/revalidate/conditions",filename:"route",bundlePath:"app/api/revalidate/conditions/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\revalidate\\conditions\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:v,serverHooks:g}=c;function x(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:v})}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,6391,580],()=>r(76537));module.exports=s})();