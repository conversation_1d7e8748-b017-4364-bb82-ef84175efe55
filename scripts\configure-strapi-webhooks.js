/**
 * <PERSON><PERSON>t to configure Strapi webhooks for on-demand revalidation
 *
 * This script creates or updates webhooks in Strapi to trigger on-demand revalidation
 * in the Next.js application when content changes.
 *
 * Usage:
 * node scripts/configure-strapi-webhooks.js
 */

const axios = require('axios');
require('dotenv').config();

// Configuration
const STRAPI_URL = process.env.STRAPI_URL || process.env.NEXT_PUBLIC_API_URL || 'https://nice-badge-2130241d6c.strapiapp.com';
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';
const REVALIDATE_TOKEN = process.env.PREVIEW_SECRET || process.env.REVALIDATE_TOKEN || '3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=';

// Check required environment variables
if (!STRAPI_API_TOKEN) {
  console.error('Error: STRAPI_API_TOKEN is required.');
  process.exit(1);
}

if (!REVALIDATE_TOKEN) {
  console.error('Error: PREVIEW_SECRET or REVALIDATE_TOKEN is required.');
  process.exit(1);
}

// Axios instance with authorization header
const strapiAPI = axios.create({
  baseURL: STRAPI_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${STRAPI_API_TOKEN}`
  }
});

// Define webhook configurations for different content types
const webhookConfigs = [
  {
    name: 'Revalidate Blog Posts',
    url: `${SITE_URL}/api/revalidate`,
    headers: {
      'Content-Type': 'application/json'
    },
    events: [
      'entry.create',
      'entry.update',
      'entry.delete',
      'entry.publish',
      'entry.unpublish'
    ],
    contentType: 'blog-post',
    body: {
      token: REVALIDATE_TOKEN,
      contentType: 'blog-post',
      id: '{{entry.id}}',
      slug: '{{entry.slug}}'
    }
  },
  {
    name: 'Revalidate Practitioners',
    url: `${SITE_URL}/api/revalidate`,
    headers: {
      'Content-Type': 'application/json'
    },
    events: [
      'entry.create',
      'entry.update',
      'entry.delete',
      'entry.publish',
      'entry.unpublish'
    ],
    contentType: 'practitioner',
    body: {
      token: REVALIDATE_TOKEN,
      contentType: 'practitioner',
      id: '{{entry.id}}',
      slug: '{{entry.slug}}'
    }
  },
  {
    name: 'Revalidate Clinics',
    url: `${SITE_URL}/api/revalidate`,
    headers: {
      'Content-Type': 'application/json'
    },
    events: [
      'entry.create',
      'entry.update',
      'entry.delete',
      'entry.publish',
      'entry.unpublish'
    ],
    contentType: 'clinic',
    body: {
      token: REVALIDATE_TOKEN,
      contentType: 'clinic',
      id: '{{entry.id}}',
      slug: '{{entry.slug}}'
    }
  },
  {
    name: 'Revalidate Categories',
    url: `${SITE_URL}/api/revalidate/categories`,
    headers: {
      'Content-Type': 'application/json',
      'X-Revalidate-Secret': REVALIDATE_TOKEN
    },
    events: [
      'entry.create',
      'entry.update',
      'entry.delete',
      'entry.publish',
      'entry.unpublish'
    ],
    contentType: 'category',
    body: {
      // The token is now sent in the header, but we keep it in the body for backward compatibility
      token: REVALIDATE_TOKEN,
      model: 'category',
      entry: {
        id: '{{entry.id}}',
        slug: '{{entry.slug}}'
      }
    }
  },
  {
    name: 'Revalidate Specialties',
    url: `${SITE_URL}/api/revalidate`,
    headers: {
      'Content-Type': 'application/json'
    },
    events: [
      'entry.create',
      'entry.update',
      'entry.delete',
      'entry.publish',
      'entry.unpublish'
    ],
    contentType: 'specialty',
    body: {
      token: REVALIDATE_TOKEN,
      contentType: 'specialty',
      id: '{{entry.id}}',
      slug: '{{entry.slug}}'
    }
  }
];

// Function to get existing webhooks
async function getExistingWebhooks() {
  try {
    const response = await strapiAPI.get('/admin/webhooks');
    return response.data.data;
  } catch (error) {
    console.error('Error fetching existing webhooks:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    return [];
  }
}

// Function to create or update a webhook
async function createOrUpdateWebhook(config, existingWebhooks) {
  const existingWebhook = existingWebhooks.find(webhook => webhook.name === config.name);

  const webhookData = {
    name: config.name,
    url: config.url,
    headers: config.headers,
    events: config.events.map(event => `${config.contentType}.${event}`),
    body: JSON.stringify(config.body)
  };

  try {
    if (existingWebhook) {
      // Update existing webhook
      const response = await strapiAPI.put(`/admin/webhooks/${existingWebhook.id}`, webhookData);
      console.log(`✅ Updated webhook: ${config.name}`);
      return response.data;
    } else {
      // Create new webhook
      const response = await strapiAPI.post('/admin/webhooks', webhookData);
      console.log(`✅ Created webhook: ${config.name}`);
      return response.data;
    }
  } catch (error) {
    console.error(`❌ Error creating/updating webhook ${config.name}:`, error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    return null;
  }
}

// Main function to configure all webhooks
async function configureWebhooks() {
  console.log('🚀 Configuring Strapi webhooks for on-demand revalidation...');
  console.log(`Strapi URL: ${STRAPI_URL}`);
  console.log(`Site URL: ${SITE_URL}`);
  console.log(`Revalidation API URL: ${SITE_URL}/api/revalidate`);

  try {
    // Get existing webhooks
    const existingWebhooks = await getExistingWebhooks();
    console.log(`Found ${existingWebhooks.length} existing webhooks.`);

    // Create or update webhooks
    let successCount = 0;
    for (const config of webhookConfigs) {
      const result = await createOrUpdateWebhook(config, existingWebhooks);
      if (result) successCount++;
    }

    console.log(`\n📊 Webhook configuration complete: ${successCount}/${webhookConfigs.length} webhooks configured successfully.`);
  } catch (error) {
    console.error('Error configuring webhooks:', error.message);
  }
}

// Run the main function
configureWebhooks();
