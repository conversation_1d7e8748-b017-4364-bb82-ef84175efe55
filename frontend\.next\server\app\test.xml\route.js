(()=>{var e={};e.id=8816,e.ids=[8816],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>p,serverHooks:()=>c,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>i});var n=r(96559),o=r(48088),a=r(37719);async function i(e){return new Response(`<?xml version="1.0" encoding="UTF-8"?>
<test>
  <message>This is a test XML response</message>
  <timestamp>${new Date().toISOString()}</timestamp>
</test>`,{headers:{"Content-Type":"application/xml"}})}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/test.xml/route",pathname:"/test.xml",filename:"route",bundlePath:"app/test.xml/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\test.xml\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:c}=p;function l(){return(0,a.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719],()=>r(72488));module.exports=s})();