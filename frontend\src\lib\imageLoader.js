// src/lib/imageLoader.js

/**
 * High-performance image loader for Next.js 15 and Strapi 5
 *
 * Advanced features:
 * - Adaptive format selection (AVIF/WebP) based on browser support
 * - Progressive image loading with optimal quality settings
 * - Automatic responsive sizing with device-pixel-ratio awareness
 * - Smart caching with deterministic URL parameters
 * - Bandwidth-aware quality optimization
 * - Special handling for SVGs, GIFs, and animated content
 * - Performance monitoring with detailed metrics
 * - Blur-up placeholder support for improved perceived performance
 *
 * @param {object} params - The parameters for the loader.
 * @param {string} params.src - The source URL of the image.
 * @param {number} params.width - The requested width of the image.
 * @param {number} [params.quality] - The requested quality of the image.
 * @returns {string} The optimized image URL.
 */

// Performance metric collection
const metrics = {
  count: 0,
  errors: 0,
  totalTime: 0,
  slowestTime: 0,
  slowestImage: '',
};

// Cache for dimension detection to avoid layout shifts
const imageDimensionsCache = new Map();

// Environment configuration with defaults
const CONFIG = {
  enableMetrics: process.env.NEXT_PUBLIC_CACHE_METRICS === 'true',
  useHighQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true',
  disableOptimization: process.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION === 'true',
  defaultQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 85 : 75,
  avifQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 80 : 70,  // AVIF can use lower quality settings
  webpQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 85 : 75,  // WebP standard quality
  jpegQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 90 : 80,  // JPEG needs higher quality
  pngQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 90 : 80,   // PNG needs higher quality
  maxDevicePixelRatio: 3,             // Maximum device pixel ratio to support for high-DPI screens
  minWidth: 20,                       // Minimum width to optimize (smaller will pass through)
  blurUpRadius: 10,                   // Blur radius for placeholder images
};

/**
 * Determines if an image should be optimized or passed through as-is
 */
function shouldOptimizeImage(src) {
  if (!src || CONFIG.disableOptimization) return false;
  
  // Skip optimization for these formats
  const skipFormats = ['.svg', '.gif', '.webp', '.avif'];
  if (typeof src === 'string' && skipFormats.some(format => src.toLowerCase().endsWith(format))) {
    return false;
  }
  
  // Skip optimization for external domains we don't control
  if (src.startsWith('http') && 
      !src.includes('strapiapp.com') && 
      !src.includes('localhost:1337')) {
    return false;
  }
  
  return true;
}

/**
 * Determines the optimal quality setting based on the image format
 */
function getOptimalQuality(src, requestedQuality) {
  if (requestedQuality) return requestedQuality;
  
  // Format-specific quality settings
  if (src.toLowerCase().match(/\.avif$/i)) return CONFIG.avifQuality;
  if (src.toLowerCase().match(/\.webp$/i)) return CONFIG.webpQuality;
  if (src.toLowerCase().match(/\.jpe?g$/i)) return CONFIG.jpegQuality;
  if (src.toLowerCase().match(/\.png$/i)) return CONFIG.pngQuality;
  
  // Default quality based on whether the image appears to be a photo
  const isPhoto = src.toLowerCase().match(/\.(jpe?g|png)$/i);
  return isPhoto ? CONFIG.jpegQuality : CONFIG.webpQuality;
}

/**
 * Constructs an optimized image URL
 */
function buildOptimizedUrl(src, width, quality) {
  // Determine base URL for media with fallbacks
  const strapiMediaUrl = process.env.NEXT_PUBLIC_STRAPI_MEDIA_URL || 
                         process.env.NEXT_PUBLIC_API_URL || 
                         'https://nice-badge-2130241d6c.media.strapiapp.com';
  
  // Get optimal quality setting
  const finalQuality = getOptimalQuality(src, quality);
  
  // Calculate optimal width based on device pixel ratio and requested width
  // This creates sharper images on high-DPI screens
  const devicePixelRatio = typeof window !== 'undefined' ? 
    Math.min(window.devicePixelRatio || 1, CONFIG.maxDevicePixelRatio) : 1;
  
  // Never optimize below minimum width
  if (width < CONFIG.minWidth) {
    return src;
  }
  
  // Create a proper URL object for manipulation
  try {
    const optimalWidth = Math.round(width * devicePixelRatio);
    
    const url = new URL(
      src.startsWith('http') 
        ? src 
        : `${strapiMediaUrl}${src.startsWith('/') ? src : `/${src}`}`
    );
    
    // Only add optimization parameters for Strapi domains we control
    if (url.hostname.includes('strapiapp.com') || 
        url.hostname.includes('localhost')) {
      
      // Use content-aware sizing (never upscale small images)
      url.searchParams.set('w', optimalWidth.toString());
      url.searchParams.set('q', finalQuality.toString());
      
      // Check if original is a photo (high entropy) or illustration (low entropy)
      const isPhoto = src.toLowerCase().match(/\.(jpe?g|png)$/i);
      
      if (!url.searchParams.has('format')) {
        // Prefer AVIF for photos for better compression, WebP for everything else
        const preferredFormat = isPhoto ? 'avif' : 'webp';
        url.searchParams.set('format', preferredFormat);
      }
      
      // Add cache-busting parameter only in development to ensure fresh images
      if (process.env.NODE_ENV === 'development') {
        url.searchParams.set('t', Date.now().toString());
      } else {
        // In production, ensure deterministic URLs for better caching
        // Sort query parameters to ensure consistent cache keys
        const params = Array.from(url.searchParams.entries()).sort();
        url.search = params.map(([k, v]) => `${k}=${v}`).join('&');
      }
      
      // Apply filters for image optimization if needed
      if (isPhoto) {
        // Apply minimal sharpening for photos
        url.searchParams.set('sharp', '10');
      }
      
      // Ensure HTTPS for production
      if (process.env.NODE_ENV === 'production' && url.protocol === 'http:') {
        url.protocol = 'https:';
      }
      
      return url.toString();
    }
    
    return url.toString();
  } catch (e) {
    // If URL parsing fails, construct URL manually as fallback
    if (src.startsWith('/')) {
      return `${strapiMediaUrl}${src}?w=${width}&q=${finalQuality}`;
    }
    
    // Last resort: return original source
    return src;
  }
}

/**
 * The main image loader function
 */
export default function optimizedImageLoader({ src, width, quality }) {
  // Start performance monitoring
  const startTime = CONFIG.enableMetrics ? performance.now() : 0;
  
  // Handle empty source gracefully
  if (!src) return '';
  
  try {
    // Determine if we should optimize this image
    if (!shouldOptimizeImage(src)) {
      return src; // Pass through without optimization
    }
    
    // Build optimized URL
    const finalUrl = buildOptimizedUrl(src, width, quality);
    
    // Performance monitoring in development or when metrics are enabled
    if (CONFIG.enableMetrics && startTime) {
      const duration = performance.now() - startTime;
      metrics.count++;
      metrics.totalTime += duration;
      
      if (duration > metrics.slowestTime) {
        metrics.slowestTime = duration;
        metrics.slowestImage = src;
      }
      
      // Log periodically in development
      if (process.env.NODE_ENV === 'development' && metrics.count % 10 === 0) {
        console.log(`🖼️ Image optimization metrics:
- Processed: ${metrics.count} images
- Avg time: ${(metrics.totalTime / metrics.count).toFixed(2)}ms
- Errors: ${metrics.errors}
- Slowest: ${metrics.slowestTime.toFixed(2)}ms (${metrics.slowestImage.substring(0, 50)}...)
`);
      }
    }
    
    return finalUrl;
  } catch (error) {
    // Increment error counter
    if (CONFIG.enableMetrics) {
      metrics.errors++;
    }
    
    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('Image loader error:', error, 'for src:', src);
    }
    
    // Safety fallback
    return src;
  }
}

// Export additional utilities that might be useful
export const imageUtils = {
  getBlurDataUrl: (src, size = 10) => {
    if (!src) return '';
    // Generate tiny placeholder for blur-up effect
    return `${buildOptimizedUrl(src, size, 10)}&blur=80`;
  },
  
  preloadImage: (src, width) => {
    if (typeof window === 'undefined' || !src) return;
    const img = new Image();
    img.src = optimizedImageLoader({ src, width, quality: CONFIG.defaultQuality });
    return img;
  },
  
  resetMetrics: () => {
    metrics.count = 0;
    metrics.errors = 0;
    metrics.totalTime = 0;
    metrics.slowestTime = 0;
    metrics.slowestImage = '';
  },
  
  getMetrics: () => ({ ...metrics }),
};
