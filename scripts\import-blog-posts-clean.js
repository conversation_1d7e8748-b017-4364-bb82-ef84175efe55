/**
 * Import blog posts from CSV file into Strapi using REST API
 * 
 * This script reads a CSV file and imports the data into the Strapi blog posts collection
 * using the REST API with an API token for authentication.
 * 
 * Usage: node scripts/import-blog-posts-clean.js
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const axios = require('axios');
const FormData = require('form-data');
const { createWriteStream } = require('fs');
const { promisify } = require('util');
const stream = require('stream');
const pipeline = promisify(stream.pipeline);
require('dotenv').config();

// Configuration
const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1337';
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;
const CSV_FILE_PATH = path.resolve(__dirname, '../strapi-cms/NaturalHealingNow-Strapi-BlogPosts.csv');
const TEMP_IMAGE_DIR = path.resolve(__dirname, '../temp-images');

// Axios instance with authorization header
const strapiAPI = axios.create({
  baseURL: STRAPI_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${STRAPI_API_TOKEN}`
  }
});

// Helper function to convert CSV string arrays to actual arrays
function parseArrayField(field) {
  if (!field) return [];
  return field.split('|||||').map(item => item.trim()).filter(Boolean);
}

// Helper function to download an image from a URL
async function downloadImage(url, outputPath) {
  try {
    const response = await axios({
      method: 'GET',
      url: url,
      responseType: 'stream',
    });

    // Ensure the directory exists
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    await pipeline(response.data, createWriteStream(outputPath));
    console.log(`Image downloaded to ${outputPath}`);
    return outputPath;
  } catch (error) {
    console.error(`Error downloading image from ${url}:`, error.message);
    return null;
  }
}

// Helper function to upload an image to Strapi
async function uploadImageToStrapi(imagePath, altText) {
  try {
    const formData = new FormData();
    const fileStream = fs.createReadStream(imagePath);
    
    // Add file info
    formData.append('files', fileStream);
    
    // Add alternative text if available
    if (altText) {
      formData.append('fileInfo', JSON.stringify({
        alternativeText: altText,
        caption: altText
      }));
    }
    
    // Upload the file
    const uploadResponse = await axios.post(`${STRAPI_URL}/api/upload`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': `Bearer ${STRAPI_API_TOKEN}`
      }
    });
    
    if (uploadResponse.data && uploadResponse.data.length > 0) {
      return uploadResponse.data[0].id;
    }
    return null;
  } catch (error) {
    console.error(`Error uploading image to Strapi:`, error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    return null;
  }
}

// Main function to import blog posts
async function importBlogPosts() {
  console.log('Starting blog posts import from CSV...');
  
  try {
    // Test connection first
    console.log('Testing connection to Strapi...');
    console.log('API URL:', STRAPI_URL);
    console.log('API Token available:', !!STRAPI_API_TOKEN);

    if (!STRAPI_API_TOKEN) {
      console.error('No API token provided. Please set the STRAPI_API_TOKEN environment variable.');
      console.log('You can create an API token in the Strapi admin panel under Settings > API Tokens.');
      return;
    }

    try {
      const testResponse = await strapiAPI.get('/api/blog-posts?pagination[pageSize]=1');
      console.log('Connection successful!');
    } catch (error) {
      console.error('Connection test failed:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      }
      throw new Error('Failed to connect to Strapi API');
    }

    const results = [];
    
    // Read CSV file
    return new Promise((resolve, reject) => {
      fs.createReadStream(CSV_FILE_PATH)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', async () => {
          console.log(`Read ${results.length} rows from CSV file`);
          
          let created = 0;
          let updated = 0;
          let skipped = 0;
          let errors = 0;
          
          // Process each row
          for (const row of results) {
            try {
              // Check if blog post with this slug already exists
              const slug = row.slug;
              if (!slug) {
                console.log('Skipping row without slug');
                skipped++;
                continue;
              }
              
              console.log(`Processing blog post with slug: ${slug}`);
              
              // Search for existing blog post with this slug
              const existingResponse = await strapiAPI.get(`/api/blog-posts?filters[slug][$eq]=${encodeURIComponent(slug)}&populate=*`);
              const existingPosts = existingResponse.data.data;
              
              // Find or create blog categories
              let categoryIds = [];
              if (row.blog_categories) {
                const categoryName = row.blog_categories.trim();
                if (categoryName) {
                  const categoryResponse = await strapiAPI.get(`/api/blog-categories?filters[name][$eq]=${encodeURIComponent(categoryName)}`);
                  const existingCategories = categoryResponse.data.data;
                  
                  if (existingCategories && existingCategories.length > 0) {
                    categoryIds.push(existingCategories[0].id);
                  } else {
                    // Create new category
                    const newCategoryResponse = await strapiAPI.post('/api/blog-categories', {
                      data: {
                        name: categoryName,
                        slug: categoryName.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
                        publishedAt: new Date()
                      }
                    });
                    categoryIds.push(newCategoryResponse.data.data.id);
                    console.log(`Created new category: ${categoryName}`);
                  }
                }
              }
              
              // Find author
              let authorIds = [];
              if (row.author_blogs) {
                const authorName = row.author_blogs.trim();
                if (authorName) {
                  const authorResponse = await strapiAPI.get(`/api/authors?filters[name][$eq]=${encodeURIComponent(authorName)}`);
                  const existingAuthors = authorResponse.data.data;
                  
                  if (existingAuthors && existingAuthors.length > 0) {
                    authorIds.push(existingAuthors[0].id);
                  } else {
                    console.log(`Author not found: ${authorName}, using existing authors if available`);
                    // Get any author if available
                    const anyAuthorsResponse = await strapiAPI.get('/api/authors?sort[0]=id:asc&pagination[limit]=1');
                    const anyAuthors = anyAuthorsResponse.data.data;
                    
                    if (anyAuthors && anyAuthors.length > 0) {
                      authorIds.push(anyAuthors[0].id);
                    }
                  }
                }
              }
              
              // Process tags from Alternative text field
              let tagIds = [];
              if (row['Alternative text']) {
                const tagNames = parseArrayField(row['Alternative text']);
                for (const tagName of tagNames) {
                  if (tagName) {
                    const tagResponse = await strapiAPI.get(`/api/blog-tags?filters[name][$eq]=${encodeURIComponent(tagName)}`);
                    const existingTags = tagResponse.data.data;
                    
                    if (existingTags && existingTags.length > 0) {
                      tagIds.push(existingTags[0].id);
                    } else {
                      // Create new tag
                      const newTagResponse = await strapiAPI.post('/api/blog-tags', {
                        data: {
                          name: tagName,
                          slug: tagName.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
                          publishedAt: new Date()
                        }
                      });
                      tagIds.push(newTagResponse.data.data.id);
                      console.log(`Created new tag: ${tagName}`);
                    }
                  }
                }
              }
              
              // Download and upload featured image if URL is provided
              let featuredImageId = null;
              if (row.featuredImage) {
                const imageUrl = row.featuredImage.trim();
                if (imageUrl) {
                  const imageName = path.basename(imageUrl);
                  const tempImagePath = path.join(TEMP_IMAGE_DIR, imageName);
                  
                  // Download the image
                  const downloadedImagePath = await downloadImage(imageUrl, tempImagePath);
                  if (downloadedImagePath) {
                    // Upload to Strapi
                    featuredImageId = await uploadImageToStrapi(downloadedImagePath, row['Alternative text'] || row.title);
                    
                    // Clean up temp file
                    try {
                      fs.unlinkSync(downloadedImagePath);
                    } catch (e) {
                      console.warn(`Could not delete temp file ${downloadedImagePath}:`, e.message);
                    }
                  }
                }
              }
              
              if (existingPosts && existingPosts.length > 0) {
                // Blog post exists, update only empty fields
                const existingPost = existingPosts[0];
                const updateData = {};
                
                // Only update title if empty
                if (!existingPost.title && row.title) {
                  updateData.title = row.title;
                }
                
                // Only update content if empty
                if (!existingPost.content && row.content) {
                  updateData.content = row.content;
                }
                
                // Only update featured image if empty
                if ((!existingPost.featuredImage) && featuredImageId) {
                  updateData.featuredImage = featuredImageId;
                }
                
                // Only update categories if empty
                if ((!existingPost.blog_categories || existingPost.blog_categories.length === 0) && categoryIds.length > 0) {
                  updateData.blog_categories = categoryIds;
                }
                
                // Only update authors if empty
                if ((!existingPost.author_blogs || existingPost.author_blogs.length === 0) && authorIds.length > 0) {
                  updateData.author_blogs = authorIds;
                }
                
                // Only update tags if empty
                if ((!existingPost.blog_tags || existingPost.blog_tags.length === 0) && tagIds.length > 0) {
                  updateData.blog_tags = tagIds;
                }
                
                // Only update if there are fields to update
                if (Object.keys(updateData).length > 0) {
                  await strapiAPI.put(`/api/blog-posts/${existingPost.documentId}`, {
                    data: updateData
                  });
                  console.log(`Updated blog post: ${slug}`);
                  updated++;
                } else {
                  console.log(`No updates needed for blog post: ${slug}`);
                  skipped++;
                }
              } else {
                // Blog post doesn't exist, create new entry
                const createData = {
                  title: row.title,
                  slug: slug,
                  content: row.content,
                  publishDate: new Date(),
                  publishedAt: new Date()
                };
                
                // Add featured image if available
                if (featuredImageId) {
                  createData.featuredImage = featuredImageId;
                }
                
                // Add categories if available
                if (categoryIds.length > 0) {
                  createData.blog_categories = categoryIds;
                }
                
                // Add authors if available
                if (authorIds.length > 0) {
                  createData.author_blogs = authorIds;
                }
                
                // Add tags if available
                if (tagIds.length > 0) {
                  createData.blog_tags = tagIds;
                }
                
                await strapiAPI.post('/api/blog-posts', {
                  data: createData
                });
                console.log(`Created new blog post: ${slug}`);
                created++;
              }
            } catch (error) {
              console.error(`Error processing row with slug ${row.slug}:`, error.message);
              if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', JSON.stringify(error.response.data, null, 2));
              }
              errors++;
            }
          }
          
          // Clean up temp directory
          try {
            if (fs.existsSync(TEMP_IMAGE_DIR)) {
              fs.rmdirSync(TEMP_IMAGE_DIR, { recursive: true });
            }
          } catch (e) {
            console.warn(`Could not clean up temp directory:`, e.message);
          }
          
          console.log(`Import completed: ${created} created, ${updated} updated, ${skipped} skipped, ${errors} errors`);
          resolve({ created, updated, skipped, errors });
        })
        .on('error', (error) => {
          console.error('Error reading CSV file:', error);
          reject(error);
        });
    });
  } catch (error) {
    console.error('Import failed:', error);
    throw error;
  }
}

// Run the import if this file is executed directly
if (require.main === module) {
  // Check for required dependencies
  try {
    require('csv-parser');
    require('axios');
    require('form-data');
    require('dotenv');
  } catch (e) {
    console.log('Installing dependencies...');
    const { execSync } = require('child_process');
    execSync('npm install csv-parser axios form-data dotenv', { stdio: 'inherit' });
    console.log('Dependencies installed successfully.');
  }
  
  importBlogPosts().catch(error => {
    console.error('Import failed:', error);
    process.exit(1);
  });
}

module.exports = importBlogPosts;
