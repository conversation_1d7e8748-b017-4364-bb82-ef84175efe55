"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2112],{2112:(t,e,r)=>{r.d(e,{Jf:()=>u,Rb:()=>f,zl:()=>h});var n=r(3297),i=r(1890);let a={NEXT_PUBLIC_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_MEDIA_URL:"https://nice-badge-2130241d6c.media.strapiapp.com",NEXT_PUBLIC_SITE_URL:i.env.NEXT_PUBLIC_SITE_URL,IMAGE_HOSTNAME:i.env.IMAGE_HOSTNAME,NODE_ENV:"production"},s=a.NEXT_PUBLIC_STRAPI_API_URL||a.NEXT_PUBLIC_API_URL||("development"===a.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.strapiapp.com"),c=(()=>{if(a.NEXT_PUBLIC_STRAPI_MEDIA_URL)return p(l(a.NEXT_PUBLIC_STRAPI_MEDIA_URL));if(a.IMAGE_HOSTNAME)return p(l(a.IMAGE_HOSTNAME));try{let t=new URL(s);if(t.hostname.endsWith("strapiapp.com"))return"".concat(p(t.protocol),"//").concat(t.hostname.replace("strapiapp.com","media.strapiapp.com"));return p(l(s))}catch(t){return"development"===a.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.media.strapiapp.com"}})(),o=a.NEXT_PUBLIC_SITE_URL||(a.NEXT_PUBLIC_API_URL&&a.NEXT_PUBLIC_API_URL.includes("strapiapp.com")?a.NEXT_PUBLIC_API_URL.replace(".strapiapp.com",".vercel.app"):"https://naturalhealingnow.vercel.app");function p(t){return t?t.replace(/^http:/,"https:"):t}function l(t){return t&&t.endsWith("/")?t.slice(0,-1):t}function h(t){return t?t.startsWith("http://")||t.startsWith("https://")?u(t):c?"".concat(c,"/").concat(t.startsWith("/")?t.substring(1):t):(n.Ay.warn("STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaPath",{imagePath:t}),"".concat(s,"/").concat(t.startsWith("/")?t.substring(1):t)):""}function u(t){let e;if("string"==typeof t&&(t.startsWith("https://")||t.startsWith("http://")||t.startsWith("/")))return t.startsWith("http://")?t.replace(/^http:/,"https:"):t;if(!t)return"";if("object"==typeof t&&t.url&&"string"==typeof t.url)e=t.url;else{if("string"!=typeof t)return n.Ay.warn("Invalid input type for sanitizeUrl. Expected string or object with url property.",{inputType:typeof t}),"";e=t}(e=e.trim()).toLowerCase().startsWith("undefined")&&(e=e.substring(9),n.Ay.info('Removed "undefined" prefix from URL',{original:t,new:e}));let r=s.replace(/^https?:\/\//,"").split("/")[0],i=c.replace(/^https?:\/\//,"").split("/")[0];if(r&&i&&e.includes(r)&&e.includes(i)){let t=RegExp("(https?://)?(".concat(r,")(/*)(https?://)?(").concat(i,")"),"gi");if(t.test(e)){let a=e;e=e.replace(t,"https://".concat(i)),n.Ay.info("Fixed concatenated Strapi domains",{original:a,fixed:e,apiDomain:r,mediaDomain:i})}}if(e.includes("https//")){let t=e;e=e.replace(/https\/\//g,"https://"),n.Ay.info("Fixed missing colon in URL (https//)",{original:t,fixed:e})}if(e.startsWith("//")?e="https:".concat(e):(e.includes("media.strapiapp.com")||e.includes(i))&&!e.startsWith("http")?e="https://".concat(e):(e.startsWith("localhost")||e.startsWith(r.split(".")[0]))&&(e="https://".concat(e)),e.startsWith("/"))return e;if(e.startsWith("http://")||e.startsWith("https://"))try{return new URL(e),e}catch(t){if(n.Ay.error("URL parsing failed after sanitization attempts",{url:e,error:t}),!e.includes("://")&&!e.includes("."))return e;return""}return c&&e&&!e.includes("://")?(n.Ay.debug("Assuming relative media path, prepending STRAPI_MEDIA_URL",{path:e}),"/".concat(e)):(n.Ay.warn("sanitizeUrl could not produce a valid absolute or relative URL",{originalInput:t,finalSanitized:e}),e)}function f(t){if(!t)return;let e=u(t);if(e){if(e.startsWith("http://")||e.startsWith("https://"))return e.replace(/^http:/,"https:");if(c){let r="".concat(c).concat(e.startsWith("/")?"":"/").concat(e);return n.Ay.debug("Constructed OG image URL from relative path",{original:t,final:r}),r.replace(/^http:/,"https:")}if(n.Ay.warn("Could not determine OG image URL confidently",{originalUrl:t,processedUrl:e}),s)return"".concat(s).concat(e.startsWith("/")?"":"/").concat(e).replace(/^http:/,"https:")}}"development"===a.NODE_ENV&&n.Ay.debug("Media Utils Initialized:",{EFFECTIVE_STRAPI_URL:s,STRAPI_MEDIA_URL:c,SITE_URL:o})},3297:(t,e,r)=>{r.d(e,{Ay:()=>s});var n=function(t){return t.DEBUG="debug",t.INFO="info",t.WARN="warn",t.ERROR="error",t}({});let i={enabled:!1,level:"info",prefix:"[NHN]"};function a(t,e){for(var r=arguments.length,a=Array(r>2?r-2:0),s=2;s<r;s++)a[s-2]=arguments[s];if(!i.enabled)return;let c=Object.values(n),o=c.indexOf(i.level);if(c.indexOf(t)>=o){let r=i.prefix?"".concat(i.prefix," "):"",n="".concat(r).concat(e);switch(t){case"debug":console.debug(n,...a);break;case"info":console.info(n,...a);break;case"warn":console.warn(n,...a);break;case"error":console.error(n,...a)}}}let s={debug:function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];a("debug",t,...r)},info:function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];a("info",t,...r)},warn:function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];a("warn",t,...r)},error:function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];a("error",t,...r)},configure:function(t){i={...i,...t}}}}}]);