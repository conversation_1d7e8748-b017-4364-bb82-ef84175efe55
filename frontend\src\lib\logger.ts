/**
 * Utility for consistent logging throughout the application
 * Only logs in development mode by default
 */

/**
 * Log levels
 */
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

/**
 * Configuration for the logger
 */
interface LoggerConfig {
  enabled: boolean;
  level: LogLevel;
  prefix?: string;
}

/**
 * Default configuration
 * Only enabled in development mode
 * Uses INFO level by default to reduce noise
 */
const defaultConfig: LoggerConfig = {
  enabled: process.env.NODE_ENV === 'development',
  level: LogLevel.INFO, // Changed from DEBUG to INFO to reduce logging noise
  prefix: '[NHN]'
};

/**
 * Current configuration
 */
let config: LoggerConfig = { ...defaultConfig };

/**
 * Configure the logger
 * @param newConfig - New configuration to merge with current config
 */
export function configureLogger(newConfig: Partial<LoggerConfig>): void {
  config = { ...config, ...newConfig };
}

/**
 * Log a message at the specified level
 * @param level - Log level
 * @param message - Message to log
 * @param args - Additional arguments to log
 */
function log(level: LogLevel, message: string, ...args: any[]): void {
  if (!config.enabled) return;

  const logLevels = Object.values(LogLevel);
  const configLevelIndex = logLevels.indexOf(config.level);
  const messageLevelIndex = logLevels.indexOf(level);

  // Only log if the message level is >= the configured level
  if (messageLevelIndex >= configLevelIndex) {
    const prefix = config.prefix ? `${config.prefix} ` : '';
    const formattedMessage = `${prefix}${message}`;

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage, ...args);
        break;
      case LogLevel.INFO:
        console.info(formattedMessage, ...args);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage, ...args);
        break;
      case LogLevel.ERROR:
        console.error(formattedMessage, ...args);
        break;
    }
  }
}

/**
 * Log a debug message
 * @param message - Message to log
 * @param args - Additional arguments to log
 */
export function debug(message: string, ...args: any[]): void {
  log(LogLevel.DEBUG, message, ...args);
}

/**
 * Log an info message
 * @param message - Message to log
 * @param args - Additional arguments to log
 */
export function info(message: string, ...args: any[]): void {
  log(LogLevel.INFO, message, ...args);
}

/**
 * Log a warning message
 * @param message - Message to log
 * @param args - Additional arguments to log
 */
export function warn(message: string, ...args: any[]): void {
  log(LogLevel.WARN, message, ...args);
}

/**
 * Log an error message
 * @param message - Message to log
 * @param args - Additional arguments to log
 */
export function error(message: string, ...args: any[]): void {
  log(LogLevel.ERROR, message, ...args);
}

/**
 * Default logger object
 */
export default {
  debug,
  info,
  warn,
  error,
  configure: configureLogger
};
