(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12164:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.default,__next_app__:()=>c,pages:()=>o,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),l=s(31369),i=s(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,26723)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24628:(e,t,s)=>{Promise.resolve().then(s.bind(s,29737))},26723:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,generateMetadata:()=>c,revalidate:()=>o});var r=s(37413),a=s(61120),l=s(54920),i=s(29737);let n=e=>{if(!e)return null;let t="https://nice-badge-2130241d6c.strapiapp.com",s=e.author_blogs?.[0]||null,r=(e=>{if(!e)return null;let s=e=>e.includes("media.strapiapp.com")?e.startsWith("http")?e:`https://${e}`:e.startsWith("http")?e:`${t}${e.startsWith("/")?"":"/"}${e}`,r=null;return e.profilePicture?.url?r=s(e.profilePicture.url):e.profilePicture?.data?.attributes?.url?r=s(e.profilePicture.data.attributes.url):e.profilePicture?.formats?.thumbnail?.url?r=s(e.profilePicture.formats.thumbnail.url):e.profilePicture?.data?.attributes?.formats?.thumbnail?.url&&(r=s(e.profilePicture.data.attributes.formats.thumbnail.url)),r})(s),a=s?{name:s.name||"Unknown Author",slug:s.slug||"",profile_picture:r}:null,l=null;if(e.featuredImage?.url){let s=e.featuredImage.url;l=s.includes("media.strapiapp.com")?s.startsWith("http")?s:`https://${s}`:s.startsWith("http")?s:`${t}${s.startsWith("/")?"":"/"}${s}`}let i=e.id||e.documentId||"";return{id:i,title:e.title||"Untitled Post",slug:e.slug||`post-${i}`,excerpt:e.excerpt||null,featured_image:l,publish_date:e.publishDate||e.createdAt||new Date().toISOString(),content:e.content||"",reading_time:e.readingTime||2,isFeatured:e.isFeatured||!1,view_count:e.view_count||0,author:a}};var d=s(82158);let o=!1,c=async()=>{let e=[];try{let t=await (0,l.g6)({pageSize:1,sort:["isFeatured:desc","publishDate:desc"],populate:{featuredImage:!0}}),s=t?.data?.[0]?.attributes;if(s&&s.featuredImage?.data?.attributes?.url){let t=(0,d.Z5)(s.featuredImage.data.attributes.url);t&&e.push({rel:"preload",as:"image",href:t})}}catch(e){console.error("Error fetching featured post for metadata preloading:",e)}return{title:"Natural Healing Now - Find Holistic Health Practitioners & Clinics",description:"Connect with holistic health practitioners and natural healing clinics to support your wellness journey.",alternates:{canonical:"/"},other:{"cache-control":"public, max-age=3600, stale-while-revalidate=86400"},links:e}},u=async function(){let[e,t,s,d]=await Promise.all([(0,l.YA)(),(0,l.sm)(),(0,l.bW)(),(0,l.g6)({pageSize:8,sort:["publishDate:desc"],populate:{featuredImage:!0,seo:{populate:{metaImage:!0,openGraph:!0}},author_blogs:{fields:["id","name","slug","bio"],populate:{profilePicture:!0}},blog_categories:{fields:["id","name","slug"]},blog_tags:{fields:["id","name","slug"]}}})]),o=d?.data?.map(e=>e.attributes?{id:e.id,...e.attributes}:e)||[],c=o.filter(e=>e.isFeatured).slice(0,1).map(n),u=o.slice(0,4).map(n),m=o.slice(0,4).map(n),x={clinics:e?.data||[],practitioners:t?.data||[],categories:s?.data||[],featuredPosts:c.length>0?c:[u[0]],latestPosts:u,popularPosts:m};return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(i.default,{initialData:x})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29737:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\page.client.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\page.client.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61012:(e,t,s)=>{Promise.resolve().then(s.bind(s,91644))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91644:(e,t,s)=>{"use strict";s.d(t,{default:()=>g});var r=s(60687),a=s(43210),l=s(26800),i=s(30104),n=s(96647),d=s(55061),o=s(58101),c=s(47365),u=s(85814),m=s.n(u),x=s(28831);let p=({children:e,threshold:t=.1,rootMargin:s="200px",placeholder:l,disabled:i=!1,className:n="",id:d})=>{let o=(0,a.useRef)(null),[c,u]=(0,a.useState)(i);return(0,a.useEffect)(()=>{if(i)return void u(!0);let e=o.current;if(!e)return;let r=new IntersectionObserver(([t])=>{t.isIntersecting&&(u(!0),r.unobserve(e))},{threshold:t,rootMargin:s});return r.observe(e),()=>{e&&r.unobserve(e)}},[i,t,s]),(0,r.jsx)("div",{ref:o,className:n,id:d,children:c?e:l||null})},h=e=>e.includes("media.strapiapp.com")?e.startsWith("http")?e:`https://${e}`:e.startsWith("http")?e:`https://nice-badge-2130241d6c.strapiapp.com${e.startsWith("/")?"":"/"}${e}`;function g({initialData:e}){let[t,s]=(0,a.useState)(""),[u,g]=(0,a.useState)(null),[f,b]=(0,a.useState)(!e),[j,v]=(0,a.useState)(e?.clinics||[]),[N,y]=(0,a.useState)(e?.practitioners||[]),[w,P]=(0,a.useState)(e?.categories||[]),[C,A]=(0,a.useState)(e?.featuredPosts?.[0]||null),[_,I]=(0,a.useState)(e?.latestPosts||[]),[S,q]=(0,a.useState)(e?.popularPosts||[]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("section",{className:"bg-gradient-to-r from-emerald-500 to-teal-600 text-white py-16 md:py-24",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Find Natural Healing Solutions Near You"}),(0,r.jsx)("p",{className:"text-xl mb-8",children:"Connect with holistic health practitioners and clinics to support your wellness journey."}),(0,r.jsx)("div",{className:"mb-8 max-w-2xl mx-auto",children:(0,r.jsx)(x.A,{placeholder:"Search for clinics, practitioners, or health topics...",onSearch:e=>{s(e),e.trim()&&(window.location.href=`/search?q=${encodeURIComponent(e)}`)},buttonText:"Search",buttonClassName:"bg-emerald-800 text-white hover:bg-emerald-900",className:"shadow-lg"})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(m(),{href:"/clinics",className:"bg-white text-emerald-600 hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold text-center",children:"Find a Clinic"}),(0,r.jsx)(m(),{href:"/practitioners",className:"bg-emerald-700 hover:bg-emerald-800 text-white px-6 py-3 rounded-lg font-semibold text-center",children:"Find a Practitioner"}),(0,r.jsx)(m(),{href:"/blog",className:"bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 rounded-lg font-semibold text-center",children:"Read Our Blog"})]})]})})}),(0,r.jsx)(p,{threshold:.1,rootMargin:"200px",placeholder:(0,r.jsx)("section",{className:"py-12 bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Explore Categories"}),(0,r.jsx)("div",{className:"w-32 h-6 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:[,,,,].fill(0).map((e,t)=>(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t))})]})}),children:(0,r.jsx)("section",{className:"py-12 bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Explore Categories"}),(0,r.jsx)(m(),{href:"/categories",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Categories"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:f?[,,,,].fill(0).map((e,t)=>(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t)):w.filter(e=>!!e?.id).map(e=>(0,r.jsx)(n.default,{category:{id:e.id,name:e.name||"Unnamed Category",slug:e.slug||`category-${e.id}`,description:e.description,icon:e.icon?.url?h(e.icon.url):null,featured_image:e.featuredImage?.url?h(e.featuredImage.url):null}},e.id))})]})})}),C&&!f&&(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Article"}),(0,r.jsx)(m(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Articles"})]}),(0,r.jsx)(o.A,{post:C,badgeType:C.isFeatured?"featured":"recent"})]})}),(0,r.jsx)(p,{threshold:.1,rootMargin:"200px",placeholder:(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Clinics"}),(0,r.jsx)("div",{className:"w-32 h-6 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[,,,].fill(0).map((e,t)=>(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t))})]})}),children:(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Clinics"}),(0,r.jsx)(m(),{href:"/clinics",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Clinics"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f?[,,,].fill(0).map((e,t)=>(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t)):j.length>0?j.slice(0,3).map(e=>(0,r.jsx)(l.default,{clinic:e},e.id)):(0,r.jsx)("div",{className:"col-span-3 text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No featured clinics available at the moment."})})})]})})}),(0,r.jsx)(p,{threshold:.1,rootMargin:"200px",placeholder:(0,r.jsx)("section",{className:"py-16 bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Practitioners"}),(0,r.jsx)("div",{className:"w-32 h-6 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[,,,,].fill(0).map((e,t)=>(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t))})]})}),children:(0,r.jsx)("section",{className:"py-16 bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Practitioners"}),(0,r.jsx)(m(),{href:"/practitioners",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Practitioners"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:f?[,,,,].fill(0).map((e,t)=>(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t)):N.length>0?N.slice(0,4).map(e=>(0,r.jsx)(i.default,{practitioner:e},e.id)):(0,r.jsx)("div",{className:"col-span-4 text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No featured practitioners available at the moment."})})})]})})}),_.length>0&&!f&&(0,r.jsx)(p,{threshold:.1,rootMargin:"200px",placeholder:(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Latest Articles"}),(0,r.jsx)("div",{className:"w-32 h-6 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-2/3",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[,,].fill(0).map((e,t)=>(0,r.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg animate-pulse"},t))})}),(0,r.jsxs)("div",{className:"lg:w-1/3 space-y-6",children:[(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"}),(0,r.jsx)("div",{className:"h-48 bg-gray-200 rounded-lg animate-pulse"})]})]})]})}),children:(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Latest Articles"}),(0,r.jsx)(m(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Articles"})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-2/3",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:_.slice(0,2).map(e=>(0,r.jsx)(d.default,{post:e,showReadingTime:!0,showShareButton:!0,showBadge:!0},e.id))})}),(0,r.jsxs)("div",{className:"lg:w-1/3 space-y-6",children:[S.length>0&&(0,r.jsx)(c.A,{posts:S}),(0,r.jsxs)("div",{className:"bg-emerald-50 rounded-lg shadow-sm p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"Stay Updated"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Get the latest natural healing tips and articles delivered to your inbox."}),(0,r.jsx)(m(),{href:"/newsletter",className:"inline-block bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm",children:"Subscribe to Newsletter"})]})]})]})]})})}),(0,r.jsx)("section",{className:"py-16 bg-emerald-600 text-white",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Ready to Start Your Wellness Journey?"}),(0,r.jsx)("p",{className:"text-xl mb-8 max-w-3xl mx-auto",children:"Discover holistic health practitioners and clinics that can help you achieve optimal health and well-being."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(m(),{href:"/categories",className:"bg-white text-emerald-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold inline-block",children:"Explore Health Categories"}),(0,r.jsx)(m(),{href:"/blog",className:"bg-emerald-700 hover:bg-emerald-800 text-white px-8 py-3 rounded-lg font-semibold inline-block",children:"Read Health Articles"})]})]})}),u&&(0,r.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6 mx-auto max-w-4xl",children:(0,r.jsx)("span",{className:"block sm:inline",children:u})})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,1330,3376,6391,2975,255,4867,8446,270,3762,8319],()=>s(12164));module.exports=r})();