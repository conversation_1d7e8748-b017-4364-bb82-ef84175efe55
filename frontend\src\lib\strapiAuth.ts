import axios from 'axios';

// Define the base URL for Strapi API
const API_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';

// Define types for authentication responses
export interface StrapiUser {
  id: number;
  username: string;
  email: string;
  provider: string;
  confirmed: boolean;
  blocked: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface StrapiAuthResponse {
  jwt: string;
  user: StrapiUser;
}

// Create an axios instance for authentication requests
const authAPI = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Authentication helper functions
export const strapiAuth = {
  // Register a new user
  register: async (username: string, email: string, password: string) => {
    try {
      const response = await authAPI.post<StrapiAuthResponse>('/api/auth/local/register', {
        username,
        email,
        password,
      });
      
      // Store the JWT token in localStorage
      if (response.data.jwt) {
        localStorage.setItem('jwt', response.data.jwt);
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }
      
      return { data: response.data, error: null };
    } catch (error: any) {
      return { 
        data: null, 
        error: error.response?.data?.error || { 
          message: 'An error occurred during registration' 
        } 
      };
    }
  },

  // Login an existing user
  login: async (identifier: string, password: string) => {
    try {
      const response = await authAPI.post<StrapiAuthResponse>('/api/auth/local', {
        identifier, // Can be email or username
        password,
      });
      
      // Store the JWT token in localStorage
      if (response.data.jwt) {
        localStorage.setItem('jwt', response.data.jwt);
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }
      
      return { data: response.data, error: null };
    } catch (error: any) {
      return { 
        data: null, 
        error: error.response?.data?.error || { 
          message: 'Invalid credentials' 
        } 
      };
    }
  },

  // Logout the current user
  logout: () => {
    localStorage.removeItem('jwt');
    localStorage.removeItem('user');
    return { error: null };
  },

  // Get the current user from localStorage
  getCurrentUser: () => {
    if (typeof window === 'undefined') {
      return { user: null };
    }
    
    const jwt = localStorage.getItem('jwt');
    const user = localStorage.getItem('user');
    
    if (!jwt || !user) {
      return { user: null };
    }
    
    try {
      return { user: JSON.parse(user) };
    } catch {
      return { user: null };
    }
  },

  // Check if the user is authenticated
  isAuthenticated: () => {
    if (typeof window === 'undefined') {
      return false;
    }
    
    const jwt = localStorage.getItem('jwt');
    return !!jwt;
  },

  // Get the JWT token
  getToken: () => {
    if (typeof window === 'undefined') {
      return null;
    }
    
    return localStorage.getItem('jwt');
  },

  // Forgot password
  forgotPassword: async (email: string) => {
    try {
      const response = await authAPI.post('/api/auth/forgot-password', {
        email,
      });
      
      return { data: response.data, error: null };
    } catch (error: any) {
      return { 
        data: null, 
        error: error.response?.data?.error || { 
          message: 'An error occurred during password reset request' 
        } 
      };
    }
  },

  // Reset password
  resetPassword: async (code: string, password: string, passwordConfirmation: string) => {
    try {
      const response = await authAPI.post('/api/auth/reset-password', {
        code,
        password,
        passwordConfirmation,
      });
      
      // Store the JWT token in localStorage if the response includes it
      if (response.data.jwt) {
        localStorage.setItem('jwt', response.data.jwt);
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }
      
      return { data: response.data, error: null };
    } catch (error: any) {
      return { 
        data: null, 
        error: error.response?.data?.error || { 
          message: 'An error occurred during password reset' 
        } 
      };
    }
  },
};

// Create an authenticated API instance that includes the JWT token in requests
export const createAuthenticatedAPI = () => {
  const token = strapiAuth.getToken();
  
  return axios.create({
    baseURL: API_URL,
    headers: {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  });
};
