'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { getStrapiContent } from '@/lib/strapi';

interface StrapiSpecialty {
  id: number | string;
  attributes?: { name?: string; slug?: string; [key: string]: any; };
  name?: string; slug?: string; [key: string]: any;
}
interface ProcessedSpecialty { name: string; slug: string; }

// Props for the SpecialtiesList component
interface SpecialtiesListProps {
  initialSpecialties?: ProcessedSpecialty[];
}

const transformSpecialtyData = (specialty: StrapiSpecialty): ProcessedSpecialty | null => {
  if (!specialty || !specialty.id) {
    console.warn(`Skipping specialty with missing ID: ${specialty?.id}`);
    return null;
  }
  const id = specialty.id?.toString() || '';
  const attributes = specialty.attributes || {};
  const name = attributes.name || specialty.name || 'Unnamed Specialty';
  let slug = attributes.slug || specialty.slug || (name !== 'Unnamed Specialty' ? name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '') : `specialty-${id}`);

  return { name, slug };
};

export default function SpecialtiesList({ initialSpecialties }: SpecialtiesListProps) {
  const [specialties, setSpecialties] = useState<ProcessedSpecialty[]>(initialSpecialties || []);
  const [loading, setLoading] = useState(true); // Start true, effect will set to false if initial data is used
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (initialSpecialties && initialSpecialties.length > 0) {
      // Use initial data if provided
      setSpecialties(initialSpecialties);
      setLoading(false);
      return; // Don't fetch
    }

    // If no initial data, fetch from client with proper caching
    async function fetchSpecialtiesData() {
      console.log("Fetching specialties client-side...");
      setLoading(true);
      setError(null);
      try {
        // Use proper cache options to avoid redundant API calls
        const specialtiesResponse = await getStrapiContent.specialties.getAll({
          cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15
          next: {
            revalidate: 604800, // 1 week (7 days) - specialties rarely change
            tags: ['strapi-specialties', 'strapi-specialties-all']
          }
        });

        if (specialtiesResponse?.data && Array.isArray(specialtiesResponse.data)) {
          const processed = specialtiesResponse.data
            .map(transformSpecialtyData)
            .filter((s: ProcessedSpecialty | null): s is ProcessedSpecialty => s !== null);

          if (processed.length > 0) {
            setSpecialties(processed);
          } else {
            console.warn("Client fetched specialties but processed list empty. Using default fallback.");
            setSpecialties([
              { name: 'Acupuncture', slug: 'acupuncture' },
              { name: 'Naturopathy', slug: 'naturopathy' },
              { name: 'Chiropractic', slug: 'chiropractic' },
              { name: 'Massage Therapy', slug: 'massage-therapy' }
            ]);
          }
        } else {
          console.warn("Client invalid/empty specialties data from API. Using default fallback.");
          setSpecialties([
            { name: 'Acupuncture', slug: 'acupuncture' },
            { name: 'Naturopathy', slug: 'naturopathy' },
            { name: 'Chiropractic', slug: 'chiropractic' },
            { name: 'Massage Therapy', slug: 'massage-therapy' }
          ]);
        }
      } catch (err) {
        console.error("Client error fetching specialties:", err);
        setError("Failed to load specialties.");
        setSpecialties([
          { name: 'Acupuncture', slug: 'acupuncture' },
          { name: 'Naturopathy', slug: 'naturopathy' },
          { name: 'Chiropractic', slug: 'chiropractic' },
          { name: 'Massage Therapy', slug: 'massage-therapy' }
        ]);
      } finally {
        setLoading(false);
      }
    }
    fetchSpecialtiesData();
  }, [initialSpecialties]); // Re-run if initialSpecialties prop changes

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"></div>
        ))}
      </div>
    );
  }

  if (error) {
    return <div className="text-center py-4 text-red-500">{error}</div>;
  }

  if (specialties.length === 0) {
    return <div className="text-center py-4 text-gray-500">No specialties available at the moment.</div>;
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {specialties.map((specialty) => (
        <Link
          key={specialty.slug}
          href={`/specialities/${specialty.slug}`}
          prefetch={false}
          className="bg-gray-50 hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors"
        >
          <span className="text-gray-800 font-medium">{specialty.name}</span>
        </Link>
      ))}
    </div>
  );
}
