'use client';

import React, { useState } from 'react';
import useApiData from '@/hooks/useApiData';
import APIErrorFallback from '@/components/shared/APIErrorFallback';
import SkeletonLoader from '@/components/shared/SkeletonLoader';
import ErrorBoundary from '@/components/shared/ErrorBoundary';
import { formatApiErrorForUser } from '@/lib/clientErrorHandling';

interface ErrorHandlingExampleProps {
  endpoint?: string;
}

/**
 * A component that demonstrates error handling with the useApiData hook
 */
const ErrorHandlingExample: React.FC<ErrorHandlingExampleProps> = ({
  endpoint = '/blog-posts',
}) => {
  const [errorTrigger, setErrorTrigger] = useState(false);

  // Use our custom hook for data fetching with error handling
  const { data, isLoading, error, refetch, isRefetching } = useApiData({
    endpoint: errorTrigger ? '/invalid-endpoint' : endpoint,
    params: {
      pagination: {
        page: 1,
        pageSize: 3,
      },
    },
    retryOnError: false,
  });

  // Function to trigger an error for demonstration
  const triggerError = () => {
    setErrorTrigger(true);
  };

  // Function to reset the error
  const resetError = () => {
    setErrorTrigger(false);
    refetch();
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="p-4 border rounded-lg bg-white">
        <h2 className="text-lg font-semibold mb-4">Loading Data...</h2>
        <SkeletonLoader type="card" count={3} />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="p-4 border rounded-lg bg-white">
        <h2 className="text-lg font-semibold mb-4">Error Handling Example</h2>
        <APIErrorFallback
          message={formatApiErrorForUser(error)}
          onRetry={resetError}
          isLoading={isRefetching}
        />
        <div className="mt-4">
          <button
            onClick={resetError}
            className="px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors"
          >
            Reset Example
          </button>
        </div>
      </div>
    );
  }

  // Render success state
  return (
    <ErrorBoundary>
      <div className="p-4 border rounded-lg bg-white">
        <h2 className="text-lg font-semibold mb-4">Error Handling Example</h2>
        
        <div className="mb-4">
          <p className="text-green-600 mb-2">✓ Data loaded successfully!</p>
          <p className="text-sm text-gray-600">
            {data ? `Loaded ${Array.isArray(data.data) ? data.data.length : 0} items from ${endpoint}` : 'No data available'}
          </p>
        </div>
        
        <div className="flex space-x-4">
          <button
            onClick={triggerError}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Trigger API Error
          </button>
          
          <button
            onClick={() => {
              // This will trigger the error boundary
              throw new Error('Manually triggered component error');
            }}
            className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors"
          >
            Trigger Component Error
          </button>
          
          <button
            onClick={refetch}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            disabled={isRefetching}
          >
            {isRefetching ? 'Refreshing...' : 'Refresh Data'}
          </button>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default ErrorHandlingExample;
