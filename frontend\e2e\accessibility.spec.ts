import { test, expect } from '@playwright/test';
import { injectAxe, checkA11y } from '@axe-core/playwright';

test.describe('Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Inject the axe-core library
    await injectAxe(page);
  });

  test('Homepage should not have any automatically detectable accessibility issues', async ({ page }) => {
    await page.goto('/');
    await checkA11y(page);
  });

  test('Blog page should not have any automatically detectable accessibility issues', async ({ page }) => {
    await page.goto('/blog');
    await checkA11y(page);
  });

  test('Clinics page should not have any automatically detectable accessibility issues', async ({ page }) => {
    await page.goto('/clinics');
    await checkA11y(page);
  });

  test('Practitioners page should not have any automatically detectable accessibility issues', async ({ page }) => {
    await page.goto('/practitioners');
    await checkA11y(page);
  });

  // Test a blog post detail page if one exists
  test('Blog post detail page should not have any automatically detectable accessibility issues', async ({ page }) => {
    await page.goto('/blog');
    
    // Click on the first blog post if available
    const blogPostLinks = page.locator('article a').filter({ hasText: /./ });
    if (await blogPostLinks.count() > 0) {
      await blogPostLinks.first().click();
      await checkA11y(page);
    } else {
      test.skip('No blog posts found to test');
    }
  });

  // Test a clinic detail page if one exists
  test('Clinic detail page should not have any automatically detectable accessibility issues', async ({ page }) => {
    await page.goto('/clinics');
    
    // Click on the first clinic card if available
    const clinicCards = page.locator('.clinic-card, [data-testid="clinic-card"]').first();
    if (await clinicCards.count() > 0) {
      await clinicCards.click();
      await checkA11y(page);
    } else {
      test.skip('No clinic cards found to test');
    }
  });
});
