(()=>{var e={};e.id=5119,e.ids=[5119],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},60351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{GET:()=>c});var n=r(96559),o=r(48088),s=r(37719),a=r(58446);let l=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";process.env.NEXT_PUBLIC_SITE_URL;let p=l.endsWith("/")?l.slice(0,-1):l;async function c(e){let t=[];try{console.log("Generating practitioners sitemap...");let e=await a.$.practitioners.getAll({pagination:{pageSize:1,page:1},fields:["id"],sort:["id:asc"],filters:{isActive:{$eq:!0}}}),r=e?.meta?.pagination?.total||0,i=Math.ceil(r/1e3);console.log(`Found ${r} total practitioners, will fetch in ${i} pages with page size 1000`),console.log("API URL being used: https://nice-badge-2130241d6c.strapiapp.com"),console.log(`Site URL being used: ${l}`);for(let e=1;e<=i;e++){console.log(`Fetching practitioners page ${e} of ${i}...`);let r=await a.$.practitioners.getAll({pagination:{pageSize:1e3,page:e},fields:["slug","updatedAt","createdAt","isActive"],sort:["id:asc"],filters:{isActive:{$eq:!0}}}),n=r?.data||[];n.length<1e3&&e<i&&console.warn(`Warning: Got only ${n.length} practitioners on page ${e}, expected 1000. This may indicate a pagination issue.`);let o=n.map(e=>{let t=null;if(e.slug?t=e.slug:e.attributes&&e.attributes.slug&&(t=e.attributes.slug),!t)return console.warn(`Practitioner object for which slug was not found (ID: ${e.id||"unknown"}, isActive: ${e.isActive??"N/A"}):`,JSON.stringify(e,null,2)),null;let r=e.attributes||e,i=r.updatedAt||r.createdAt?new Date(r.updatedAt||r.createdAt):new Date;return{url:`${p}/practitioners/${t}`,lastModified:i,changeFrequency:"weekly",priority:.8}}).filter(Boolean);t=[...t,...o]}console.log(`Total practitioner entries in sitemap: ${t.length}`),t.unshift({url:`${p}/practitioners`,lastModified:new Date,changeFrequency:"daily",priority:.9}),console.log(`Final sitemap entries count (including index page): ${t.length}`),console.log(`Sitemap generation summary:
    - Total practitioners found: ${r}
    - Total pages fetched: ${i}
    - Page size used: 1000
    - Final sitemap entries: ${t.length}
    - Sitemap URL: ${p}/sitemap-practitioners.xml
    `);let n=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${t.map(e=>`  <url>
    <loc>${e.url}</loc>
    <lastmod>${e.lastModified.toISOString()}</lastmod>
    <changefreq>${e.changeFrequency}</changefreq>
    <priority>${e.priority}</priority>
  </url>`).join("\n")}
</urlset>`;try{if(!n.startsWith("<?xml")||!n.includes("<urlset")||!n.includes("</urlset>"))throw console.error("Generated XML appears to be malformed"),Error("Malformed XML");return new Response(n,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600","X-Content-Type-Options":"nosniff"}})}catch(t){console.error("Error validating XML:",t);let e=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${p}/practitioners</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;return new Response(e,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600","X-Content-Type-Options":"nosniff"}})}}catch(e){if(console.error("Error generating practitioners sitemap:",e),t.length>0)return console.log(`Returning partial sitemap with ${t.length} entries despite error`),t.some(e=>e.url===`${p}/practitioners`)||t.unshift({url:`${p}/practitioners`,lastModified:new Date,changeFrequency:"daily",priority:.9}),new Response(`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${t.map(e=>`  <url>
    <loc>${e.url}</loc>
    <lastmod>${e.lastModified.toISOString()}</lastmod>
    <changefreq>${e.changeFrequency}</changefreq>
    <priority>${e.priority}</priority>
  </url>`).join("\n")}
</urlset>`,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600","X-Content-Type-Options":"nosniff"}});return console.log("Returning fallback sitemap with only the practitioners index page"),new Response(`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${p}/practitioners</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"no-cache","X-Content-Type-Options":"nosniff"}})}}let u=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/sitemap-practitioners.xml/route",pathname:"/sitemap-practitioners.xml",filename:"route",bundlePath:"app/sitemap-practitioners.xml/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\sitemap-practitioners.xml\\route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:m}=u;function h(){return(0,s.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[7719,1330,3376,8446],()=>r(60351));module.exports=i})();