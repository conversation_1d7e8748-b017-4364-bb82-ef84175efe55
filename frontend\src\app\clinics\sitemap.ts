import { MetadataRoute } from 'next';
import { getStrapiContent } from '@/lib/strapi';

// Define the site URL from environment variable
// We need an absolute URL for sitemaps to work properly
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_API_URL?.replace('.strapiapp.com', '') || 'https://naturalhealingnow.vercel.app';

// Ensure SITE_URL doesn't have a trailing slash
const normalizedSiteUrl = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;

// Define interfaces for Strapi data types
interface StrapiItem {
  id?: string | number;
  documentId?: string;
  attributes?: {
    slug?: string;
    updatedAt?: string;
    publishedAt?: string;
    [key: string]: any;
  };
  slug?: string;
  updatedAt?: string;
  publishedAt?: string;
  [key: string]: any;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    console.log('Generating clinics sitemap...');
    
    // Fetch clinics
    const clinicsResponse = await getStrapiContent.clinics.getAll({
      pagination: { pageSize: 1000 }
    });
    
    const clinics = clinicsResponse?.data || [];
    
    // Generate sitemap entries for clinics
    const clinicEntries = clinics.map((clinic: StrapiItem) => {
      const attributes = clinic.attributes || clinic;
      const slug = attributes.slug || clinic.slug;
      const updatedAt = attributes.updatedAt || clinic.updatedAt || new Date().toISOString();
      
      if (!slug) return null;
      
      return {
        url: `${normalizedSiteUrl}/clinics/${slug}`,
        lastModified: new Date(updatedAt),
        changeFrequency: 'weekly',
        priority: 0.8,
      };
    }).filter(Boolean) as MetadataRoute.Sitemap;
    
    // Combine all clinic-related entries
    return [
      {
        url: `${normalizedSiteUrl}/clinics`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      ...clinicEntries,
    ];
  } catch (error) {
    console.error('Error generating clinics sitemap:', error);
    
    // Return only the main clinics URL if there's an error
    return [
      {
        url: `${normalizedSiteUrl}/clinics`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
    ];
  }
}
