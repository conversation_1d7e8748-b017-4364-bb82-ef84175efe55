(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4217],{3310:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>d,A:()=>i});var a=t(5155),s=t(2115);let n=t(3464).A.create({baseURL:"https://nice-badge-2130241d6c.strapiapp.com",headers:{"Content-Type":"application/json"}}),o={register:async(e,r,t)=>{try{let a=await n.post("/api/auth/local/register",{username:e,email:r,password:t});return a.data.jwt&&(localStorage.setItem("jwt",a.data.jwt),localStorage.setItem("user",JSON.stringify(a.data.user))),{data:a.data,error:null}}catch(e){var a,s;return{data:null,error:(null==(s=e.response)||null==(a=s.data)?void 0:a.error)||{message:"An error occurred during registration"}}}},login:async(e,r)=>{try{let t=await n.post("/api/auth/local",{identifier:e,password:r});return t.data.jwt&&(localStorage.setItem("jwt",t.data.jwt),localStorage.setItem("user",JSON.stringify(t.data.user))),{data:t.data,error:null}}catch(e){var t,a;return{data:null,error:(null==(a=e.response)||null==(t=a.data)?void 0:t.error)||{message:"Invalid credentials"}}}},logout:()=>(localStorage.removeItem("jwt"),localStorage.removeItem("user"),{error:null}),getCurrentUser:()=>{let e=localStorage.getItem("jwt"),r=localStorage.getItem("user");if(!e||!r)return{user:null};try{return{user:JSON.parse(r)}}catch(e){return{user:null}}},forgotPassword:async e=>{try{return{data:(await n.post("/api/auth/forgot-password",{email:e})).data,error:null}}catch(e){var r,t;return{data:null,error:(null==(t=e.response)||null==(r=t.data)?void 0:r.error)||{message:"An error occurred during password reset request"}}}},resetPassword:async(e,r,t)=>{try{let a=await n.post("/api/auth/reset-password",{code:e,password:r,passwordConfirmation:t});return a.data.jwt&&(localStorage.setItem("jwt",a.data.jwt),localStorage.setItem("user",JSON.stringify(a.data.user))),{data:a.data,error:null}}catch(e){var a,s;return{data:null,error:(null==(s=e.response)||null==(a=s.data)?void 0:a.error)||{message:"An error occurred during password reset"}}}}};var l=t(5695);let u=(0,s.createContext)(void 0);function d(e){let{children:r}=e,[t,n]=(0,s.useState)(null),[d,i]=(0,s.useState)(!0),[c,m]=(0,s.useState)(!1),g=(0,l.useRouter)();(0,s.useEffect)(()=>{(async()=>{i(!0);try{let{user:e}=o.getCurrentUser();n(e),m(!!e)}catch(e){console.error("Error loading user:",e),n(null),m(!1)}finally{i(!1)}})();let e=()=>{let{user:e}=o.getCurrentUser();n(e),m(!!e),g.refresh()};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[g]);let h=async(e,r)=>{i(!0);let{data:t,error:a}=await o.login(e,r);return t&&(n(t.user),m(!0),g.refresh()),i(!1),{error:a}},f=async(e,r,t)=>{i(!0);let{data:a,error:s}=await o.register(e,r,t);return a&&(n(a.user),m(!0),g.refresh()),i(!1),{error:s}},p=async e=>{i(!0);let{error:r}=await o.forgotPassword(e);return i(!1),{error:r}},w=async(e,r,t)=>{i(!0);let{data:a,error:s}=await o.resetPassword(e,r,t);return a&&(n(a.user),m(!0),g.refresh()),i(!1),{error:s}};return(0,a.jsx)(u.Provider,{value:{user:t,isLoading:d,signIn:h,signUp:f,signOut:()=>{i(!0);let{error:e}=o.logout();return n(null),m(!1),g.refresh(),i(!1),{error:e}},forgotPassword:p,resetPassword:w,isAuthenticated:c},children:r})}function i(){let e=(0,s.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},6759:(e,r,t)=>{Promise.resolve().then(t.bind(t,9753))},9753:(e,r,t)=>{"use strict";t.d(r,{default:()=>d});var a=t(5155),s=t(2115),n=t(3310),o=t(5695),l=t(6874),u=t.n(l);function d(){let[e,r]=(0,s.useState)(""),[t,l]=(0,s.useState)(""),[d,i]=(0,s.useState)(""),[c,m]=(0,s.useState)(!1),{signIn:g}=(0,n.A)(),h=(0,o.useRouter)(),f=async r=>{r.preventDefault(),i(""),m(!0);try{let{error:r}=await g(e,t);r?i(r.message||"Failed to sign in"):h.push("/")}catch(e){console.error("Error during sign in:",e),i("An unexpected error occurred")}finally{m(!1)}};return(0,a.jsxs)("div",{className:"max-w-md w-full mx-auto p-6 bg-white rounded-lg shadow-md",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-gray-800",children:"Sign In"}),d&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md",children:d}),(0,a.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,a.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>r(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsx)(u(),{href:"/forgot-password",className:"text-sm text-emerald-600 hover:text-emerald-700",children:"Forgot password?"})]}),(0,a.jsx)("input",{id:"password",type:"password",value:t,onChange:e=>l(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"})]}),(0,a.jsx)("button",{type:"submit",disabled:c,className:"w-full bg-emerald-600 text-white py-2 px-4 rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Signing in...":"Sign In"})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,a.jsx)(u(),{href:"/signup",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"Sign up"})]})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6874,3464,8441,1684,7358],()=>r(6759)),_N_E=e.O()}]);