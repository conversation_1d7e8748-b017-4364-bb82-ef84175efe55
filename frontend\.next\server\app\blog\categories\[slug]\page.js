(()=>{var e={};e.id=6948,e.ids=[6948],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7610:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(43210);let o=r.forwardRef(function({title:e,titleId:t,...a},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},9100:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,85814,23)),Promise.resolve().then(a.bind(a,55061)),Promise.resolve().then(a.bind(a,91936))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44725:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(43210);let o=r.forwardRef(function({title:e,titleId:t,...a},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},49384:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=function(){for(var e,t,a=0,r="",o=arguments.length;a<o;a++)(e=arguments[a])&&(t=function e(t){var a,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(a=0;a<s;a++)t[a]&&(r=e(t[a]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}(e))&&(r&&(r+=" "),r+=t);return r}},51892:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b,dynamicParams:()=>p,generateMetadata:()=>f,revalidate:()=>u});var r=a(37413),o=a(4536),s=a.n(o),n=a(73993),i=a(87603),l=a(58446),g=a(39916),c=a(65646),d=a(82158);let u=!1,p=!0;function m(e){let t;if(console.log("Mapping category data structure:",JSON.stringify({hasId:!!e?.id,hasAttributes:!!e?.attributes,topLevelKeys:e?Object.keys(e):[],hasBlogPosts:!!(e?.blog_posts||e?.attributes?.blog_posts||e?.blogPosts||e?.attributes?.blogPosts)})),!e)return console.warn("No category data to map"),null;let a=!!e.attributes,r=e.id,o=a?e.attributes:e;if(!r||!o)return console.warn("Invalid category data structure:",e),null;let s="https://nice-badge-2130241d6c.strapiapp.com",n=process.env.IMAGE_HOSTNAME||"";console.log("Using API URL for category mapping:",s),console.log("Using Image URL for category mapping:",n||s);let i=[];try{let e=null;a?o.blogPosts?.data?e=o.blogPosts.data:o.blog_posts?.data?e=o.blog_posts.data:o.blogPosts?e=o.blogPosts:o.blog_posts&&(e=o.blog_posts):o.blog_posts?e=o.blog_posts:o.blogPosts&&(e=o.blogPosts),console.log("Posts data found:",!!e,"Is array:",Array.isArray(e),"Length:",Array.isArray(e)?e.length:0),Array.isArray(e)&&(i=e.map(e=>{let t=e.attributes||e,a=t.author_blogs||[],r=a.length>0?a[0]:{},o=r.attributes||r,s=t.featuredImage?.data||t.featuredImage||{};console.log(`Raw featured image data for post "${t.title}":`,JSON.stringify(s));let n=(0,d.Jf)(s);if(!n&&s&&("string"==typeof s?n=(0,d.Jf)(s):s.url?n=(0,d.Jf)(s.url):s.data?.attributes?.url&&(n=(0,d.Jf)(s.data.attributes.url))),!n&&process.env.IMAGE_HOSTNAME&&s){let e=process.env.IMAGE_HOSTNAME;console.log(`Using IMAGE_HOSTNAME: ${e}`);let t="";if("string"==typeof s&&s.includes("/")){let e=s.split("/");t=e[e.length-1]}else if(s.url&&s.url.includes("/")){let e=s.url.split("/");t=e[e.length-1]}else if(s.data?.attributes?.url&&s.data.attributes.url.includes("/")){let e=s.data.attributes.url.split("/");t=e[e.length-1]}else s.data?.attributes?.name?t=s.data.attributes.name:s.name?t=s.name:s.data?.attributes?.hash&&(t=`${s.data.attributes.hash}.${s.data.attributes.ext||"jpg"}`);t&&(n=`${e}/${t}`,console.log(`Constructed image URL from filename: ${n}`))}console.log(`Featured image URL for post "${t.title}":`,n);let i=o.profilePicture?.data||o.profilePicture||{};console.log(`Raw author profile picture data for "${o.name}":`,JSON.stringify(i));let l=(0,d.Jf)(i);if(!l&&i&&("string"==typeof i?l=(0,d.Jf)(i):i.url?l=(0,d.Jf)(i.url):i.data?.attributes?.url&&(l=(0,d.Jf)(i.data.attributes.url))),!l&&process.env.IMAGE_HOSTNAME&&i){let e=process.env.IMAGE_HOSTNAME,t="";if("string"==typeof i&&i.includes("/")){let e=i.split("/");t=e[e.length-1]}else if(i.url&&i.url.includes("/")){let e=i.url.split("/");t=e[e.length-1]}else if(i.data?.attributes?.url&&i.data.attributes.url.includes("/")){let e=i.data.attributes.url.split("/");t=e[e.length-1]}else i.data?.attributes?.name?t=i.data.attributes.name:i.name&&(t=i.name);t&&(l=`${e}/${t}`,console.log(`Constructed author profile picture URL from filename: ${l}`))}console.log(`Author profile picture URL for "${o.name}":`,l);let g={id:e.id||"",title:t.title||"Untitled Post",slug:t.slug||"untitled-post",excerpt:t.excerpt||null,featured_image:n,publish_date:t.publishDate||t.published_at||t.createdAt||new Date().toISOString(),author:{name:o.name||"Unknown Author",slug:o.slug||"unknown-author",profile_picture:l}};return void 0!==t.view_count&&Object.defineProperty(g,"view_count",{value:t.view_count,enumerable:!1}),g}))}catch(e){console.warn("Error extracting posts for category:",e)}let l=o.name||"Unnamed Category",g=o.slug||"unnamed-category",c=null;if(o.description){if("string"==typeof o.description)c=o.description;else if(Array.isArray(o.description))try{c=o.description.map(e=>e.children?e.children.map(e=>e.text||"").join(" "):"").filter(Boolean).join(" ")}catch(e){console.warn("Error parsing richtext description:",e)}}let u=o.seo||null;if(console.log("Checking for pagination metadata in category response"),o.blog_posts?.meta?.pagination)console.log("Found pagination in data.blog_posts.meta.pagination:",JSON.stringify(o.blog_posts.meta.pagination)),t=o.blog_posts.meta.pagination;else if(e.meta?.pagination)console.log("Found pagination in strapiCategory.meta.pagination:",JSON.stringify(e.meta.pagination)),t=e.meta.pagination;else for(let{path:a,value:r}of[{path:"strapiCategory.blog_posts.meta.pagination",value:e.blog_posts?.meta?.pagination},{path:"data.blogPosts.meta.pagination",value:o.blogPosts?.meta?.pagination},{path:"strapiCategory.data.meta.pagination",value:e.data?.meta?.pagination}])if(r){console.log(`Found pagination in ${a}:`,JSON.stringify(r)),t=r;break}return!t&&Array.isArray(i)&&(console.log("Creating default pagination based on posts length:",i.length),t={page:1,pageSize:12,pageCount:Math.max(1,Math.ceil((i.length||0)/12)),total:i.length||0}),t&&t.pageCount<1&&(t.pageCount=1),console.log("Final pagination data:",JSON.stringify(t)),{id:r,name:l,slug:g,description:c,posts:i,pagination:t,seo:u}}async function h(e,t={}){let a=t.page||1,r=t.pageSize||12;console.log(`Fetching category data for slug: "${e}" (page ${a}, pageSize ${r})`);try{let t=await (0,l.f)("/blog-categories",{params:{filters:{slug:{$eq:e}},populate:{seo:!0}}});if(!t?.data||!Array.isArray(t.data)||0===t.data.length)return console.error(`No category found with slug: ${e}`),{data:[],meta:{pagination:{page:a,pageSize:r,pageCount:0,total:0}}};let o=t.data[0].id;console.log(`Found category with ID: ${o}`);let s=await (0,l.f)("/blog-posts",{params:{filters:{blog_categories:{id:{$eq:o}}},pagination:{page:a,pageSize:r},sort:["publishDate:desc"],populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0},fields:["name","slug"]},fields:["title","slug","excerpt","publishDate","content"]}}});console.log(`Found ${s?.data?.length||0} posts for category ID ${o}`);let n={data:[{...t.data[0],blog_posts:s.data||[]}],meta:s.meta||{pagination:{page:a,pageSize:r,pageCount:0,total:0}}};return console.log(`Combined category response for slug ${e}:`,JSON.stringify({hasData:!!n?.data,isArray:Array.isArray(n?.data),length:Array.isArray(n?.data)?n.data.length:"not an array",hasPosts:Array.isArray(n?.data)&&n.data.length>0&&Array.isArray(n.data[0].blog_posts)?n.data[0].blog_posts.length:0,hasPagination:!!n?.meta?.pagination,paginationTotal:n?.meta?.pagination?.total||0})),n}catch(t){throw console.error(`Error fetching category data for slug ${e}:`,t),t}}async function f({params:e,searchParams:t},a){let r=(await e).slug,o=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";console.log(`Using site URL for canonical URLs: ${o}`);try{var s;let e,a=Math.max(1,Number(t.page)||1),n=await h(r,{page:a,pageSize:12});if(!n||!n.data||0===n.data.length)return{title:"Category Not Found | Natural Healing Now",description:"The requested blog category could not be found."};let i=n.data[0],l=m(i);if(!l)return{title:"Category Not Found | Natural Healing Now",description:"The requested blog category could not be found."};let g=l.seo,c=Number(t.page)||1,u=c>1?` - Page ${c}`:"",p=`${l.name}${u} - Blog Category | Natural Healing Now`,f=l.description||`Explore articles in the ${l.name} category on Natural Healing Now.`,b=c>1?`?page=${c}`:"",x=`/blog/categories/${l.slug}${b}`,y=g?.canonicalURL||(o?`${o}${x}`:x);g?.openGraph?.image?.url?e=(0,d.Rb)(g.openGraph.image.url):g?.metaImage?.url&&(e=(0,d.Rb)(g.metaImage.url)),console.log("Blog category og:image:",{openGraphImageUrl:g?.openGraph?.image?.url,metaImageUrl:g?.metaImage?.url,ogImageUrl:e});let v={title:g?.metaTitle||p,description:g?.metaDescription||f,robots:g?.metaRobots||"index, follow",alternates:{canonical:y},openGraph:g?.openGraph?{title:g.openGraph.title||g.metaTitle||p,description:g.openGraph.description||g.metaDescription||f,url:g.openGraph.url||y,siteName:g.openGraph.siteName||"Natural Healing Now",type:(s=g.openGraph.type)&&["article","book","profile","website","music.song","music.album","music.playlist","music.radio_station","video.movie","video.episode","video.tv_show","video.other"].includes(s)?s:"website",...e&&{images:[{url:e}]}}:{title:g?.metaTitle||p,description:g?.metaDescription||f,url:y,siteName:"Natural Healing Now",type:"website",...e&&{images:[{url:e}]}},twitter:{card:"summary_large_image",title:g?.openGraph?.title||g?.metaTitle||p,description:g?.openGraph?.description||g?.metaDescription||f,...e&&{images:[e]}}};if(g?.structuredData&&"string"==typeof g.structuredData)try{JSON.parse(g.structuredData)}catch(e){console.error("Invalid structured data JSON:",e)}return v}catch(e){return console.error(`Error generating metadata for blog category ${r}:`,e),{title:"Blog Category | Natural Healing Now",description:"Explore our blog categories for natural healing information."}}}async function b({params:e,searchParams:t}){let{slug:a}=await e,o=Math.max(1,Number(t.page)||1),d=null,u=null;try{if(console.log(`Fetching blog category with slug: "${a}" (page ${o})`),u=await h(a,{page:o,pageSize:12}),console.log("Blog category response structure:",JSON.stringify({hasData:!!u?.data,dataKeys:u?.data&&u.data.length>0?Object.keys(u.data[0]):[],isArray:Array.isArray(u?.data),length:Array.isArray(u?.data)?u.data.length:"not an array",hasPagination:!!u?.meta?.pagination,paginationTotal:u?.meta?.pagination?.total||0})),u&&u.data&&u.data.length>0)console.log("Found category data, mapping to component props"),u.meta?.pagination&&console.log("Pagination metadata:",JSON.stringify(u.meta.pagination)),d=m(u.data[0]),console.log("Mapped category pagination:",JSON.stringify(d?.pagination||"No pagination data")),console.log("Current page from searchParams:",o),console.log("Search params:",JSON.stringify(t));else{console.log(`No blog category found with slug "${a}"`);try{let e=await l.$.blog.getCategories();console.log("All categories response:",JSON.stringify({hasData:!!e?.data,count:e?.data?.length||0,slugs:e?.data?.map(e=>e.slug||e.attributes&&e.attributes.slug)}))}catch(e){console.error("Error fetching all categories:",e)}return(0,g.notFound)()}}catch(e){return console.error(`Error fetching blog category with slug ${a}:`,e),e.response&&(console.error("Error response status:",e.response.status),console.error("Error response data:",JSON.stringify(e.response.data))),(0,g.notFound)()}if(!d)return(0,g.notFound)();let p=null;if(d?.seo?.structuredData){if("string"==typeof d.seo.structuredData)p=d.seo.structuredData;else if("object"==typeof d.seo.structuredData)try{p=JSON.stringify(d.seo.structuredData)}catch(e){console.error("Failed to stringify structuredData object:",e)}}return p||(p=JSON.stringify({"@context":"https://schema.org","@type":"CollectionPage",name:d?.name,description:d?.description||`Articles in the ${d?.name} category`,url:`${process.env.NEXT_PUBLIC_SITE_URL||""}/blog/categories/${d?.slug}`,mainEntity:{"@type":"ItemList",itemListElement:d?.posts.map((e,t)=>({"@type":"ListItem",position:t+1,url:`${process.env.NEXT_PUBLIC_SITE_URL||""}/blog/${e.slug}`,name:e.title}))||[]}})),(0,r.jsxs)(r.Fragment,{children:[p&&(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:p}}),(0,r.jsx)("div",{className:"bg-gray-100 py-3",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(s(),{href:"/",className:"hover:text-emerald-600",children:"Home"}),(0,r.jsx)("span",{className:"mx-2",children:"/"}),(0,r.jsx)(s(),{href:"/blog",className:"hover:text-emerald-600",children:"Blog"}),(0,r.jsx)("span",{className:"mx-2",children:"/"}),(0,r.jsx)("span",{className:"text-gray-800",children:d.name})]})})}),(0,r.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:d.name}),d.description&&(0,r.jsx)("p",{className:"text-lg max-w-3xl",children:d.description})]})}),(0,r.jsx)("div",{className:"bg-gray-50 py-12",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-8",children:u.meta?.pagination?.total?`${u.meta.pagination.total} Articles in ${d.name}`:d.posts?.length?`${d.posts.length} Articles in ${d.name}`:`Articles in ${d.name}`}),d.posts&&d.posts.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.posts.map(e=>(0,r.jsx)(i.default,{post:e},e.id))}):(0,r.jsx)("div",{className:"bg-white p-8 rounded-lg text-center",children:(0,r.jsx)("p",{className:"text-gray-600",children:"No articles found in this category."})}),(0,r.jsx)("div",{className:"mt-12 flex justify-center",children:u.meta?.pagination&&u.meta.pagination.pageCount>1?(0,r.jsx)(c.default,{totalPages:u.meta.pagination.pageCount}):d.posts&&d.posts.length>12?(0,r.jsx)(c.default,{totalPages:Math.max(1,Math.ceil(d.posts.length/12))}):null}),(0,r.jsx)("div",{className:"mt-12 text-center",children:(0,r.jsxs)(s(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 flex items-center justify-center",children:[(0,r.jsx)(n.kRp,{className:"mr-2"})," Back to All Articles"]})})]})})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56455:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,4536,23)),Promise.resolve().then(a.bind(a,87603)),Promise.resolve().then(a.bind(a,65646))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65646:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\Pagination.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\Pagination.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81120:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>s.default,__next_app__:()=>c,pages:()=>g,routeModule:()=>d,tree:()=>l});var r=a(65239),o=a(48088),s=a(31369),n=a(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);a.d(t,i);let l={children:["",{children:["blog",{children:["categories",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,51892)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\categories\\[slug]\\page.tsx"]}]},{"not-found":[()=>Promise.resolve().then(a.bind(a,86381)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\categories\\[slug]\\not-found.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,g=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\categories\\[slug]\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/blog/categories/[slug]/page",pathname:"/blog/categories/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86381:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var r=a(37413),o=a(4536),s=a.n(o),n=a(73993);function i(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto bg-white rounded-lg shadow-sm p-8 text-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Category Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Sorry, we couldn't find the blog category you're looking for. It may have been removed or renamed."}),(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsxs)(s(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 flex items-center",children:[(0,r.jsx)(n.kRp,{className:"mr-2"})," Back to Blog"]}),(0,r.jsx)(s(),{href:"/",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-md transition-colors",children:"Go to Homepage"})]})]})})})}},91936:(e,t,a)=>{"use strict";a.d(t,{default:()=>d});var r=a(60687),o=a(44725),s=a(7610),n=a(49384),i=a(85814),l=a.n(i),g=a(16189);let c=(e,t)=>t<=7?Array.from({length:t},(e,t)=>t+1):e<=3?[1,2,3,"...",t-1,t]:e>=t-2?[1,2,"...",t-2,t-1,t]:[1,"...",e-1,e,e+1,"...",t];function d({totalPages:e,currentPage:t}){let a=(0,g.usePathname)(),o=(0,g.useSearchParams)(),s=void 0!==t?t:Number(o.get("page"))||1,n=e=>{let t=new URLSearchParams(o);return t.set("page",e.toString()),`${a}?${t.toString()}`},i=c(s,e);return e<=1?null:(0,r.jsxs)("div",{className:"inline-flex",children:[(0,r.jsx)(p,{direction:"left",href:n(s-1),isDisabled:s<=1}),(0,r.jsx)("div",{className:"flex -space-x-px",children:i.map((e,t)=>{let a;return 0===t&&(a="first"),t===i.length-1&&(a="last"),1===i.length&&(a="single"),"..."===e&&(a="middle"),(0,r.jsx)(u,{href:n(e),page:e,position:a,isActive:s===e},`${e}-${t}`)})}),(0,r.jsx)(p,{direction:"right",href:n(s+1),isDisabled:s>=e})]})}function u({page:e,href:t,isActive:a,position:o}){let s=(0,n.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===o||"single"===o,"rounded-r-md":"last"===o||"single"===o,"z-10 bg-emerald-600 border-emerald-600 text-white":a,"hover:bg-gray-100":!a&&"middle"!==o,"text-gray-300 pointer-events-none":"middle"===o});return a||"middle"===o?(0,r.jsx)("div",{className:s,children:e}):(0,r.jsx)(l(),{href:t,className:s,children:e})}function p({href:e,direction:t,isDisabled:a}){let i=(0,n.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":a,"hover:bg-gray-100":!a,"mr-2 md:mr-4":"left"===t,"ml-2 md:ml-4":"right"===t}),g="left"===t?(0,r.jsx)(o.A,{className:"w-4"}):(0,r.jsx)(s.A,{className:"w-4"});return a?(0,r.jsx)("div",{className:i,children:g}):(0,r.jsx)(l(),{className:i,href:e,children:g})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[7719,1330,3376,6391,2975,4867,8446,270,4559],()=>a(81120));module.exports=r})();