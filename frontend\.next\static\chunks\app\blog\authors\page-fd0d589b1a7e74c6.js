(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[432],{1282:(t,e,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.t.bind(a,3063,23))},4663:(t,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>n,imageUtils:()=>c});var r=a(1890);let i={count:0,errors:0,totalTime:0,slowestTime:0,slowestImage:""},s={enableMetrics:"true"===r.env.NEXT_PUBLIC_CACHE_METRICS,useHighQuality:!0,disableOptimization:"true"===r.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION,defaultQuality:85,avifQuality:80,webpQuality:85,jpegQuality:90,pngQuality:90,maxDevicePixelRatio:3,minWidth:20,blurUpRadius:10};function o(t,e,a){let r="https://nice-badge-2130241d6c.media.strapiapp.com",i=a||(t.toLowerCase().match(/\.avif$/i)?s.avifQuality:t.toLowerCase().match(/\.webp$/i)?s.webpQuality:t.toLowerCase().match(/\.jpe?g$/i)?s.jpegQuality:t.toLowerCase().match(/\.png$/i)?s.pngQuality:t.toLowerCase().match(/\.(jpe?g|png)$/i)?s.jpegQuality:s.webpQuality),o=Math.min(window.devicePixelRatio||1,s.maxDevicePixelRatio);if(e<s.minWidth)return t;try{let a=Math.round(e*o),s=new URL(t.startsWith("http")?t:"".concat(r).concat(t.startsWith("/")?t:"/".concat(t)));if(s.hostname.includes("strapiapp.com")||s.hostname.includes("localhost")){s.searchParams.set("w",a.toString()),s.searchParams.set("q",i.toString());let e=t.toLowerCase().match(/\.(jpe?g|png)$/i);s.searchParams.has("format")||s.searchParams.set("format",e?"avif":"webp");{let t=Array.from(s.searchParams.entries()).sort();s.search=t.map(t=>{let[e,a]=t;return"".concat(e,"=").concat(a)}).join("&")}e&&s.searchParams.set("sharp","10"),"http:"===s.protocol&&(s.protocol="https:")}return s.toString()}catch(a){if(t.startsWith("/"))return"".concat(r).concat(t,"?w=").concat(e,"&q=").concat(i);return t}}function n(t){let{src:e,width:a,quality:r}=t,n=s.enableMetrics?performance.now():0;if(!e)return"";try{if(!(e&&!s.disableOptimization&&!("string"==typeof e&&[".svg",".gif",".webp",".avif"].some(t=>e.toLowerCase().endsWith(t))||e.startsWith("http")&&!e.includes("strapiapp.com")&&!e.includes("localhost:1337"))&&1))return e;let t=o(e,a,r);if(s.enableMetrics&&n){let t=performance.now()-n;i.count++,i.totalTime+=t,t>i.slowestTime&&(i.slowestTime=t,i.slowestImage=e)}return t}catch(t){return s.enableMetrics&&i.errors++,e}}let c={getBlurDataUrl:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return t?"".concat(o(t,e,10),"&blur=80"):""},preloadImage:(t,e)=>{if(!t)return;let a=new Image;return a.src=n({src:t,width:e,quality:s.defaultQuality}),a},resetMetrics:()=>{i.count=0,i.errors=0,i.totalTime=0,i.slowestTime=0,i.slowestImage=""},getMetrics:()=>({...i})}}},t=>{var e=e=>t(t.s=e);t.O(0,[6874,3063,8441,1684,7358],()=>e(1282)),_N_E=t.O()}]);