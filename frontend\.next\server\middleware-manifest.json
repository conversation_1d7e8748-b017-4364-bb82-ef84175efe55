{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "lLwlo9oDG_8hf3tBAnv-I", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQ16R5SwQu6LOATzjR3XAW3h/Rqm6S+WbmOhhW6E1Xo=", "__NEXT_PREVIEW_MODE_ID": "0824b28c66e3b6c36cc70f389e76a34d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "635cffd7d767c75f9fa6606282c9a8f9450c0598cabf2cc9df229e6fb44c8e0c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f0627de9dbec69c55c8f7f920ec2b8227ad6ce09fbc1e88acc4ef2d153ecf757"}}}, "functions": {}, "sortedMiddleware": ["/"]}