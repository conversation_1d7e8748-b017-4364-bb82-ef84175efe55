{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "1j7101_TKUIPIhyNJnUYD", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQ16R5SwQu6LOATzjR3XAW3h/Rqm6S+WbmOhhW6E1Xo=", "__NEXT_PREVIEW_MODE_ID": "f770b093d0ef7d4da358fbf969b31e46", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "87a8b7ba0fff104b7654ea77d80ebf45f04cecfe9a51a8274d998bbd33747bf1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1bf4404d73e3de90d402e0b298f309fb3a6abe4823bfe28b92be99ebd4a57ae6"}}}, "functions": {}, "sortedMiddleware": ["/"]}