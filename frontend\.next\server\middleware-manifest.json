{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bGPfyfLUi11XRO37o0J46", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQ16R5SwQu6LOATzjR3XAW3h/Rqm6S+WbmOhhW6E1Xo=", "__NEXT_PREVIEW_MODE_ID": "80b9a8307df8d6cf7c9c9771003a5fac", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e61a6cf9e45ecd892abb498719d35a0860e4bfc0257b713d635912cd341a14ab", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a1c7e49b91c07b236a3813079df7a48199793519c0bdaf594b1345265b4fc85d"}}}, "functions": {}, "sortedMiddleware": ["/"]}