import logger, { LogLevel, configureLogger } from '@/lib/logger';

describe('Logger Utility', () => {
  // Store original console methods
  const originalConsoleDebug = console.debug;
  const originalConsoleInfo = console.info;
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;
  
  // Mock console methods
  beforeEach(() => {
    console.debug = jest.fn();
    console.info = jest.fn();
    console.warn = jest.fn();
    console.log = jest.fn();
    console.error = jest.fn();
    
    // Reset logger configuration before each test
    configureLogger({
      enabled: true,
      level: LogLevel.DEBUG,
      prefix: '[TEST]'
    });
  });
  
  // Restore original console methods
  afterEach(() => {
    console.debug = originalConsoleDebug;
    console.info = originalConsoleInfo;
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
  });
  
  test('should log debug messages when enabled', () => {
    logger.debug('Debug message');
    expect(console.debug).toHaveBeenCalledWith('[TEST] Debug message');
  });
  
  test('should log info messages when enabled', () => {
    logger.info('Info message');
    expect(console.info).toHaveBeenCalledWith('[TEST] Info message');
  });
  
  test('should log warning messages when enabled', () => {
    logger.warn('Warning message');
    expect(console.warn).toHaveBeenCalledWith('[TEST] Warning message');
  });
  
  test('should log error messages when enabled', () => {
    logger.error('Error message');
    expect(console.error).toHaveBeenCalledWith('[TEST] Error message');
  });
  
  test('should not log when disabled', () => {
    configureLogger({ enabled: false });
    
    logger.debug('Debug message');
    logger.info('Info message');
    logger.warn('Warning message');
    logger.error('Error message');
    
    expect(console.debug).not.toHaveBeenCalled();
    expect(console.info).not.toHaveBeenCalled();
    expect(console.warn).not.toHaveBeenCalled();
    expect(console.error).not.toHaveBeenCalled();
  });
  
  test('should respect log level', () => {
    configureLogger({ level: LogLevel.WARN });
    
    logger.debug('Debug message');
    logger.info('Info message');
    logger.warn('Warning message');
    logger.error('Error message');
    
    expect(console.debug).not.toHaveBeenCalled();
    expect(console.info).not.toHaveBeenCalled();
    expect(console.warn).toHaveBeenCalledWith('[TEST] Warning message');
    expect(console.error).toHaveBeenCalledWith('[TEST] Error message');
  });
  
  test('should log with additional arguments', () => {
    const obj = { key: 'value' };
    logger.debug('Debug message', obj);
    expect(console.debug).toHaveBeenCalledWith('[TEST] Debug message', obj);
  });
});
