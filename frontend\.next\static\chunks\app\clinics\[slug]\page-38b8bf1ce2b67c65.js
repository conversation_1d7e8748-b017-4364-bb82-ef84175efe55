(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5900],{1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},getImageProps:function(){return n}});let a=r(8229),i=r(8883),o=r(3063),l=a._(r(4663));function n(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1440,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"custom",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=o.Image},1838:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,8887)),Promise.resolve().then(r.bind(r,2467)),Promise.resolve().then(r.bind(r,9907))},2467:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(5155),i=r(2115),o=r(6766),l=r(2112);let n=e=>{let{src:t,alt:r,fallbackChar:n,containerClassName:s,imageClassName:c="h-full w-full"}=e,u=t?"h-24 w-24 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden":"bg-emerald-100 h-24 w-24 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden",[d,m]=(0,i.useState)("object-contain"),h=t?(0,l.Jf)(t):"";t&&h!==t&&console.log("ClinicLogo URL sanitized:",{original:t,sanitized:h});let[f,p]=(0,i.useState)(!1);return(0,a.jsx)("div",{className:s||u,children:h&&!f?(0,a.jsx)(o.default,{src:h,alt:r,width:96,height:96,className:"".concat(c," ").concat(d),onLoad:e=>{let{naturalWidth:t,naturalHeight:r}=e.currentTarget;if(0===r)return void m("object-contain");let a=t/r;a>1&&a<=2?m("object-cover"):m("object-contain")},onError:()=>{console.error("ClinicLogo image failed to load:",{src:h,alt:r}),p(!0)},priority:!0}):(0,a.jsx)("span",{className:"text-emerald-700 font-bold text-3xl",children:n})})}},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>u});var a=r(2115),i={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},o=a.createContext&&a.createContext(i),l=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var a,i,o;a=e,i=t,o=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>a.createElement(d,n({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:i,size:o,title:s}=e,u=function(e,t){if(null==e)return{};var r,a,i=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(a=0;a<o.length;a++)r=o[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,l),d=o||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,i,u,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),s&&a.createElement("title",null,s),e.children)};return void 0!==o?a.createElement(o.Consumer,null,e=>t(e)):t(i)}},4663:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,imageUtils:()=>s});var a=r(1890);let i={count:0,errors:0,totalTime:0,slowestTime:0,slowestImage:""},o={enableMetrics:"true"===a.env.NEXT_PUBLIC_CACHE_METRICS,useHighQuality:!0,disableOptimization:"true"===a.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION,defaultQuality:85,avifQuality:80,webpQuality:85,jpegQuality:90,pngQuality:90,maxDevicePixelRatio:3,minWidth:20,blurUpRadius:10};function l(e,t,r){let a="https://nice-badge-2130241d6c.media.strapiapp.com",i=r||(e.toLowerCase().match(/\.avif$/i)?o.avifQuality:e.toLowerCase().match(/\.webp$/i)?o.webpQuality:e.toLowerCase().match(/\.jpe?g$/i)?o.jpegQuality:e.toLowerCase().match(/\.png$/i)?o.pngQuality:e.toLowerCase().match(/\.(jpe?g|png)$/i)?o.jpegQuality:o.webpQuality),l=Math.min(window.devicePixelRatio||1,o.maxDevicePixelRatio);if(t<o.minWidth)return e;try{let r=Math.round(t*l),o=new URL(e.startsWith("http")?e:"".concat(a).concat(e.startsWith("/")?e:"/".concat(e)));if(o.hostname.includes("strapiapp.com")||o.hostname.includes("localhost")){o.searchParams.set("w",r.toString()),o.searchParams.set("q",i.toString());let t=e.toLowerCase().match(/\.(jpe?g|png)$/i);o.searchParams.has("format")||o.searchParams.set("format",t?"avif":"webp");{let e=Array.from(o.searchParams.entries()).sort();o.search=e.map(e=>{let[t,r]=e;return"".concat(t,"=").concat(r)}).join("&")}t&&o.searchParams.set("sharp","10"),"http:"===o.protocol&&(o.protocol="https:")}return o.toString()}catch(r){if(e.startsWith("/"))return"".concat(a).concat(e,"?w=").concat(t,"&q=").concat(i);return e}}function n(e){let{src:t,width:r,quality:a}=e,n=o.enableMetrics?performance.now():0;if(!t)return"";try{if(!(t&&!o.disableOptimization&&!("string"==typeof t&&[".svg",".gif",".webp",".avif"].some(e=>t.toLowerCase().endsWith(e))||t.startsWith("http")&&!t.includes("strapiapp.com")&&!t.includes("localhost:1337"))&&1))return t;let e=l(t,r,a);if(o.enableMetrics&&n){let e=performance.now()-n;i.count++,i.totalTime+=e,e>i.slowestTime&&(i.slowestTime=e,i.slowestImage=t)}return e}catch(e){return o.enableMetrics&&i.errors++,t}}let s={getBlurDataUrl:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return e?"".concat(l(e,t,10),"&blur=80"):""},preloadImage:(e,t)=>{if(!e)return;let r=new Image;return r.src=n({src:e,width:t,quality:o.defaultQuality}),r},resetMetrics:()=>{i.count=0,i.errors=0,i.totalTime=0,i.slowestTime=0,i.slowestImage=""},getMetrics:()=>({...i})}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var a=r(1469),i=r.n(a)},8887:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(5155),i=r(6943),o=r(5006),l=r(2115),n=r(3297);async function s(e,t){try{let r=await fetch("/api/analytics/post-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:e,postSlug:t})});if(!r.ok)throw Error("Failed to track post view: ".concat(r.statusText))}catch(e){n.Ay.error("Error tracking post view:",e)}}r(1890);let c=e=>{let{content:t,postId:r,postSlug:n,applyNoFollow:c=!0}=e;return(0,l.useEffect)(()=>{if(r&&n){let e=setTimeout(()=>{s(r,n)},2e3);return()=>clearTimeout(e)}},[r,n]),(0,a.jsxs)("div",{className:"mb-8",children:[" ",(0,a.jsx)(i.oz,{components:{h1:e=>{let{node:t,...r}=e;return(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mt-8 mb-4",...r})},h2:e=>{let{node:t,...r}=e;return(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mt-6 mb-3",...r})},h3:e=>{let{node:t,...r}=e;return(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mt-5 mb-2",...r})},h4:e=>{let{node:t,...r}=e;return(0,a.jsx)("h4",{className:"text-lg font-bold text-gray-800 mt-4 mb-2",...r})},p:e=>{let{node:t,...r}=e;return(0,a.jsx)("p",{className:"text-gray-700 mb-4",...r})},a:e=>{let{node:t,href:r,...i}=e,o=r&&(r.startsWith("http://")||r.startsWith("https://")),l="";return o&&(c&&(l+="nofollow "),l+="noopener noreferrer"),(0,a.jsx)("a",{className:"text-emerald-600 hover:text-emerald-700 underline",href:r,rel:l.trim()||void 0,target:o?"_blank":void 0,...i})},ul:e=>{let{node:t,...r}=e;return(0,a.jsx)("ul",{className:"list-disc pl-6 mb-4",...r})},ol:e=>{let{node:t,...r}=e;return(0,a.jsx)("ol",{className:"list-decimal pl-6 mb-4",...r})},li:e=>{let{node:t,...r}=e;return(0,a.jsx)("li",{className:"mb-1",...r})},blockquote:e=>{let{node:t,...r}=e;return(0,a.jsx)("blockquote",{className:"border-l-4 border-emerald-500 pl-4 italic my-4",...r})}},rehypePlugins:[o.A],children:t})]})}},9907:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(5155),i=r(2115),o=r(6766),l=r(351),n=r(4663);let s=e=>{var t;let{src:r,alt:s,width:c,height:u,className:d="",fallbackClassName:m="",showPlaceholder:h=!0,advancedBlur:f=!1,preload:p=!1,fadeIn:g=!0,wrapperAs:b="div",fillContainer:v=!1,sizes:y="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",style:w,priority:j=!1,qualityOverride:x,...P}=e,O=(0,i.useRef)(!0),[I,N]=(0,i.useState)(j?"loaded":"loading"),[C,S]=(0,i.useState)({width:v?void 0:c,height:v?void 0:u}),E="string"==typeof r?r:(null==r?void 0:r.src)||(null==r?void 0:r.url)||(null==r||null==(t=r.default)?void 0:t.src)||null,M=f&&h&&E?n.imageUtils.getBlurDataUrl(E,20):h?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+":void 0;(0,i.useEffect)(()=>{if(p&&E&&!j&&1){let e=n.imageUtils.preloadImage(E,"number"==typeof c?c:500);e&&(e.onload=()=>{O.current&&N("loaded")},e.onerror=()=>{O.current&&N("error")})}return()=>{O.current=!1}},[E,p,j,c]);let T="number"==typeof c&&"number"==typeof u&&c>0&&u>0?c/u:void 0,L=(0,i.useCallback)(e=>{if(!v&&(null==e?void 0:e.target)){let{naturalWidth:t,naturalHeight:r}=e.target;t&&r&&O.current&&S({width:t,height:r})}O.current&&N("loaded")},[v]),k=(0,i.useCallback)(()=>{N("error")},[E,s]),_=j?"eager":"lazy";if("error"===I||!E)return(0,a.jsx)("div",{className:"flex items-center justify-center bg-gray-100 ".concat(m||(v?"":d)),style:{width:v?"100%":c,height:v?"100%":u,aspectRatio:T?"".concat(T):void 0,...w},role:"img","aria-label":s||"Image failed to load",children:(0,a.jsx)(l.fZZ,{className:"text-gray-400 w-1/5 h-1/5"})});let z=[d,"loaded"===I?"opacity-100":"opacity-0",g?"transition-opacity duration-300":""].filter(Boolean).join(" "),D={objectFit:(null==w?void 0:w.objectFit)||"cover",aspectRatio:v?void 0:T?"".concat(T):void 0,...w,width:v||null==w?void 0:w.width,height:v||null==w?void 0:w.height},A=(0,a.jsx)(o.default,{src:E,alt:s||"",width:v?void 0:C.width,height:v?void 0:C.height,fill:v,className:z,loading:_,fetchPriority:j?"high":p?"low":"auto",priority:j,sizes:y,style:D,placeholder:h?"blur":"empty",blurDataURL:M,onLoad:L,onError:k,quality:x,...P}),Q=v?{width:"100%",height:"100%",position:"relative",...w}:{width:C.width,height:C.height,aspectRatio:T?"".concat(T):void 0,position:"relative",...w};return(0,a.jsxs)(b,{className:"relative ".concat(v?"w-full h-full":""),style:Q,children:[A,"loading"===I&&h&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-100 animate-pulse ".concat(m||""),style:{width:"100%",height:"100%"},"aria-hidden":"true"})]})};s.displayName="LazyImage";let c=(0,i.memo)(s)}},e=>{var t=t=>e(e.s=t);e.O(0,[844,6874,3063,2780,2112,8441,1684,7358],()=>t(1838)),_N_E=e.O()}]);