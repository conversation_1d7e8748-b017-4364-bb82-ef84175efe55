(()=>{var e={};e.id=4064,e.ids=[4064],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12045:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\MarkdownContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\MarkdownContent.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19595:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\practitioners\\\\VerifiedBadge.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\practitioners\\VerifiedBadge.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},22e3:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P,dynamicParams:()=>j,generateMetadata:()=>w,generateStaticParams:()=>b,revalidate:()=>v});var s=r(37413),n=r(4536),i=r.n(n),a=r(61120),o=r(58446),l=r(39916),c=r(12045),d=r(82158),u=r(85178),p=r(19595),m=r(57292),h=r(88067),f=r(32308),g=r(58443),x=r(33156);async function b(){try{let e=await o.$.practitioners.getAllSlugs({cache:"force-cache",next:{revalidate:43200,tags:["strapi-practitioners-slugs"]}});if(e&&e.data&&Array.isArray(e.data))return e.data.filter(e=>e&&"string"==typeof e.slug).map(e=>({slug:e.slug}));return console.error("Failed to fetch practitioner slugs or data is not in expected format:",e),[]}catch(e){return console.error("Error fetching practitioner slugs for generateStaticParams:",e),[]}}let v=43200,j=!1,y=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";async function N(e,t){if(t&&t.id&&t.name&&t.slug)return console.log(`Using prefetched data for practitioner ${e}`),t;try{let t=await o.$.practitioners.getBySlug(e,{cache:"force-cache",next:{revalidate:43200,tags:["strapi-practitioner",`strapi-practitioner-${e}`]}});if(t?.data&&t.data.length>0){let r=t.data[0];if(!r.id||!r.name||!r.slug)return console.error(`Practitioner data for slug ${e} is missing required fields:`,{hasId:!!r.id,hasName:!!r.name,hasSlug:!!r.slug}),null;return r}return console.warn(`No practitioner found with slug ${e}`),null}catch(t){return console.error(`Error fetching practitioner with slug ${e}:`,t),t instanceof Error&&(console.error(`Error name: ${t.name}, message: ${t.message}`),console.error(`Error stack: ${t.stack}`)),null}}async function w({params:e,searchParams:t}){let r,{slug:s}=await e,n=await N(s);if(!n)return{title:"Practitioner Not Found | Natural Healing Now",description:"The requested practitioner could not be found."};let i=n.seo,a=`${n.name} - ${n.title||"Holistic Health Practitioner"} | Natural Healing Now`,o=n.bio?.substring(0,160)||`Learn more about ${n.name}, a ${n.title||"holistic health practitioner"}.`,l=i?.metaTitle||a,c=i?.metaDescription||o,u=`/practitioners/${n.slug}`,p=i?.canonicalURL||(y?`${y}${u}`:u),m=i?.openGraph?.ogImage?.url,h=i?.metaImage?.url,f=(0,d.tz)(n);m?r=(0,d.Rb)(m):h?r=(0,d.Rb)(h):f&&(r=(0,d.Rb)(f));let g=r?[{url:r}]:[],x="profile",b=i?.openGraph?.ogType;return b&&["article","website","book","profile","music.song","music.album","music.playlist","music.radio_station","video.movie","video.episode","video.tv_show","video.other"].includes(b)&&(x=b),{title:l,description:c,alternates:{canonical:p},openGraph:i?.openGraph?{title:i.openGraph.ogTitle||l,description:i.openGraph.ogDescription||c,url:i.openGraph.ogUrl||p,type:x,images:g}:{title:l,description:c,url:p,type:x,images:g},twitter:{card:"summary_large_image",title:i?.openGraph?.ogTitle||l,description:i?.openGraph?.ogDescription||c,images:g}}}async function P({params:e,searchParams:t}){let{slug:r}=await e,n=await N(r);n||(0,l.notFound)();let{name:o="Unnamed Practitioner",title:b=null,qualifications:v=null,bio:j="No biography available.",videoEmbed:y=null,specialties:w=null,conditions:P=null,education:C=null,clinics:A=null,contactInfo:I=null,isVerified:_=!1,seo:E=null}=n,U=null;try{n.profilePicture&&((U=(0,d.tz)(n))||"object"!=typeof n.profilePicture||(U=(0,d.Z5)(n.profilePicture,{debug:!1})))}catch(e){console.error("Error processing profile picture URL:",e),U=null}let R=y?.url?`<iframe src="${y.url.replace("watch?v=","embed/")}" title="${o} Video" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: 0;"></iframe>`:y?.oembed?.html||null,$=null;if(E?.structuredData){if("string"==typeof E.structuredData)$=E.structuredData;else if("object"==typeof E.structuredData)try{$=JSON.stringify(E.structuredData)}catch(e){console.error("Failed to stringify structuredData object:",e)}}return(0,s.jsxs)(s.Fragment,{children:[" ",(0,s.jsxs)(a.Suspense,{fallback:null,children:[" ",(0,s.jsx)(x.default,{practitioner:n})]}),(0,s.jsx)("div",{className:"bg-gray-100 py-3",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)(i(),{href:"/",className:"hover:text-emerald-600",children:"Home"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)(i(),{href:"/practitioners",className:"hover:text-emerald-600",children:"Practitioners"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)("span",{className:"text-gray-800",children:o})]})})}),(0,s.jsx)("div",{className:"bg-white border-b",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center",children:[(0,s.jsx)("div",{className:"mb-4 md:mb-0 md:mr-6 flex-shrink-0",children:(0,s.jsx)(u.default,{imageUrl:U,name:o})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-x-2 mb-1",children:[" ",(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:o}),(0,s.jsx)(p.default,{isVerified:_})]}),b&&(0,s.jsx)("p",{className:"text-xl text-gray-600 mb-2",children:b}),v&&(0,s.jsx)("p",{className:"text-gray-500 mb-3",children:v}),(0,s.jsx)(m.default,{emailAddress:I?.emailAddress})]})]})})}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"About"}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,s.jsx)(c.default,{content:j||"",applyNoFollow:!1}),(0,s.jsx)(f.default,{videoHtml:R})]})]}),w&&w.length>0&&(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Specialties"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,s.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:w.map(e=>(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"}),(0,s.jsx)(i(),{href:`/specialities/${e.slug}`,className:"no-underline hover:text-emerald-600",children:e.name})]},e.id))})})]}),P&&P.length>0&&(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Conditions Treated"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,s.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:P.map(e=>(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"}),(0,s.jsx)(i(),{href:`/conditions/${e.slug}`,className:"no-underline hover:text-emerald-600",children:e.name})]},e.id))})})]}),C&&C.length>0&&(0,s.jsxs)("section",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Education & Training"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,s.jsx)("ul",{className:"space-y-4",children:C.map((e,t)=>(0,s.jsxs)("li",{children:[" ",(0,s.jsx)("p",{className:"font-medium text-gray-800",children:e.degree}),(0,s.jsxs)("p",{className:"text-gray-600",children:[e.institution,", ",e.year]})]},e.id||t))})})]})]}),(0,s.jsxs)("div",{children:[A&&A.length>0&&(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,s.jsx)("h3",{className:"font-bold text-gray-800 mb-4",children:"Practices At"}),(0,s.jsx)("ul",{className:"space-y-4",children:A.map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:`/clinics/${e.slug}`,className:"text-emerald-600 hover:text-emerald-700 font-medium",children:e.name})},e.id))})]}),I?.emailAddress&&(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,s.jsx)("h3",{className:"font-bold text-gray-800 mb-4",children:"Contact"}),(0,s.jsx)("div",{className:"space-y-3",children:(0,s.jsx)(h.default,{emailAddress:I.emailAddress})})]}),A&&A.length>0&&(0,s.jsxs)("section",{className:"bg-emerald-50 rounded-lg p-6 border border-emerald-100",children:[(0,s.jsx)("h3",{className:"font-bold text-gray-800 mb-3",children:"Interested in an Appointment?"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-4",children:["Contact one of the clinics where ",o," practices to schedule a consultation."]}),(0,s.jsx)(i(),{href:`/clinics/${A[0].slug}`,className:"block w-full bg-emerald-600 hover:bg-emerald-700 text-white text-center py-3 rounded-lg font-medium",children:"View Clinic Details"})]})]})]})}),(0,s.jsx)(g.default,{data:$})]})}console.log(`Using site URL for canonical URLs: ${y}`)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32308:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\practitioners\\\\VideoEmbed.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\practitioners\\VideoEmbed.tsx","default")},32382:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(43210),n=r(16189),i=r(68326);let a=({practitioner:e})=>{let t=(0,n.useRouter)(),r=(0,n.usePathname)(),a=(0,n.useSearchParams)(),o=(0,s.useRef)(!1);return(0,s.useEffect)(()=>{if(!o.current){if(e&&e.id&&e.slug){let t=(0,i.b3)(e.slug);if(!t||!t._hasDetailedData){let t={...e,_hasDetailedData:!0};(0,i.tq)(t),o.current=!0}}if(r.includes("/practitioners/")&&"true"===a.get("prefetched")){let e=new URLSearchParams(a.toString());e.delete("prefetched");let s=e.toString()?`${r}?${e.toString()}`:r;t.replace(s,{scroll:!1})}}},[e,r,a,t]),null}},33156:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\practitioners\\\\PractitionerPrefetcher.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\practitioners\\PractitionerPrefetcher.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},36146:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,12045)),Promise.resolve().then(r.bind(r,57292)),Promise.resolve().then(r.bind(r,88067)),Promise.resolve().then(r.bind(r,33156)),Promise.resolve().then(r.bind(r,85178)),Promise.resolve().then(r.bind(r,19595)),Promise.resolve().then(r.bind(r,32308)),Promise.resolve().then(r.bind(r,58443))},36463:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a});var s=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let n={enabled:!1,level:"info",prefix:"[NHN]"};function i(e,t,...r){if(!n.enabled)return;let a=Object.values(s),o=a.indexOf(n.level);if(a.indexOf(e)>=o){let s=n.prefix?`${n.prefix} `:"",i=`${s}${t}`;switch(e){case"debug":console.debug(i,...r);break;case"info":console.info(i,...r);break;case"warn":console.warn(i,...r);break;case"error":console.error(i,...r)}}}let a={debug:function(e,...t){i("debug",e,...t)},info:function(e,...t){i("info",e,...t)},warn:function(e,...t){i("warn",e,...t)},error:function(e,...t){i("error",e,...t)},configure:function(e){n={...n,...e}}}},54638:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687);let n=({videoHtml:e})=>e?(0,s.jsx)("div",{className:"mt-6 rounded-lg overflow-hidden",style:{maxWidth:"100%"},children:(0,s.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0},dangerouslySetInnerHTML:{__html:e}})}):null},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57292:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\practitioners\\\\EmailContact.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\practitioners\\EmailContact.tsx","default")},57980:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(60687),n=r(30474),i=r(43210);let a=({imageUrl:e,name:t})=>{let[r,a]=(0,i.useState)(!1),[o,l]=(0,i.useState)(null);return((0,i.useEffect)(()=>{if(a(!1),e&&"string"==typeof e&&""!==e.trim())try{let t=e.startsWith("http")?e:`https://${e}`;new URL(t),l(t)}catch(t){console.error("Invalid image URL:",e,t),a(!0),l(null)}else l(null)},[e]),!o||r)?(0,s.jsx)("div",{className:"bg-emerald-100 h-32 w-32 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-emerald-700 font-bold text-3xl",children:t?t.charAt(0).toUpperCase():"?"})}):(0,s.jsx)("div",{className:"h-32 w-32 rounded-full overflow-hidden",children:(0,s.jsx)(n.default,{src:o,alt:`${t}'s profile picture`,width:128,height:128,className:"object-cover rounded-full",priority:!0,onError:e=>{console.error("Error loading practitioner image:",{practitionerName:t,profilePictureUrl:o,error:e}),a(!0)}})})}},58443:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\StructuredData.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\StructuredData.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66537:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687),n=r(20255);let i=({isVerified:e})=>e?(0,s.jsxs)("div",{className:"flex items-center gap-x-1 bg-emerald-100 text-emerald-700 px-2 py-0.5 rounded-full text-sm font-medium",children:[(0,s.jsx)(n.AI8,{color:"#009967",size:16}),(0,s.jsx)("span",{children:"VERIFIED"})]}):null},68326:(e,t,r)=>{"use strict";r.d(t,{b3:()=>n,tq:()=>s});function s(e){var t;if(!e||!e.id||!e.slug)return;let r=(e.slug,null);r&&(!e._hasDetailedData||r._hasDetailedData)||function(e,t,r=3e5){}(`practitioner_${e.slug}`,0,18e5)}function n(e){var t;return null}},72097:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687);let n=({emailAddress:e})=>e?(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-600 mb-1",children:"Email"}),(0,s.jsx)("a",{href:`mailto:${e}`,className:"font-medium text-emerald-600 hover:text-emerald-700 break-all",children:e})]}):null},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},79551:e=>{"use strict";e.exports=require("url")},80442:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687),n=r(17019);let i=({emailAddress:e})=>e?(0,s.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,s.jsx)(n.pHD,{className:"mr-2 text-emerald-500"}),(0,s.jsx)("a",{href:`mailto:${e}`,className:"text-emerald-600 hover:text-emerald-700",children:e})]}):null},81630:e=>{"use strict";e.exports=require("http")},82158:(e,t,r)=>{"use strict";r.d(t,{Jf:()=>p,Rb:()=>m,Z5:()=>d,tz:()=>u});var s=r(36463);let n={NEXT_PUBLIC_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_MEDIA_URL:"https://nice-badge-2130241d6c.media.strapiapp.com",NEXT_PUBLIC_SITE_URL:process.env.NEXT_PUBLIC_SITE_URL,IMAGE_HOSTNAME:process.env.IMAGE_HOSTNAME,NODE_ENV:"production"},i=n.NEXT_PUBLIC_STRAPI_API_URL||n.NEXT_PUBLIC_API_URL||("development"===n.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.strapiapp.com"),a=(()=>{if(n.NEXT_PUBLIC_STRAPI_MEDIA_URL)return l(c(n.NEXT_PUBLIC_STRAPI_MEDIA_URL));if(n.IMAGE_HOSTNAME)return l(c(n.IMAGE_HOSTNAME));try{let e=new URL(i);if(e.hostname.endsWith("strapiapp.com"))return`${l(e.protocol)}//${e.hostname.replace("strapiapp.com","media.strapiapp.com")}`;return l(c(i))}catch(e){return"development"===n.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.media.strapiapp.com"}})(),o=n.NEXT_PUBLIC_SITE_URL||(n.NEXT_PUBLIC_API_URL&&n.NEXT_PUBLIC_API_URL.includes("strapiapp.com")?n.NEXT_PUBLIC_API_URL.replace(".strapiapp.com",".vercel.app"):"https://naturalhealingnow.vercel.app");function l(e){return e?e.replace(/^http:/,"https:"):e}function c(e){return e&&e.endsWith("/")?e.slice(0,-1):e}function d(e,t={debug:!1}){if(t.debug&&s.Ay.debug("getStrapiMediaUrl input:",{type:typeof e,isNull:null===e,isUndefined:void 0===e,value:e}),!e)return null;let r=null;if("string"==typeof e?r=e:"object"==typeof e&&(r=e.url||e.data?.attributes?.url||e.data?.url||null),!r)return t.debug,s.Ay.warn("Could not extract initial URL from mediaInput in getStrapiMediaUrl",{mediaInput:e}),null;let n=p(r);return n?n.startsWith("http://")||n.startsWith("https://")?n:a?`${a}${n.startsWith("/")?"":"/"}${n}`:(s.Ay.warn("STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl",{sanitizedUrl:n}),`${i}${n.startsWith("/")?"":"/"}${n}`):(t.debug,s.Ay.warn("URL became empty after sanitization in getStrapiMediaUrl",{originalUrl:r}),null)}function u(e){if(!e||!e.profilePicture)return null;let t=e.profilePicture,r=t.url||t.data?.attributes?.url||t.data?.url||t.formats?.thumbnail?.url;return r?d(r):d(t)}function p(e){let t;if("string"==typeof e&&(e.startsWith("https://")||e.startsWith("http://")||e.startsWith("/")))return e.startsWith("http://")?e.replace(/^http:/,"https:"):e;if(!e)return"";if("object"==typeof e&&e.url&&"string"==typeof e.url)t=e.url;else{if("string"!=typeof e)return s.Ay.warn("Invalid input type for sanitizeUrl. Expected string or object with url property.",{inputType:typeof e}),"";t=e}(t=t.trim()).toLowerCase().startsWith("undefined")&&(t=t.substring(9),s.Ay.info('Removed "undefined" prefix from URL',{original:e,new:t}));let r=i.replace(/^https?:\/\//,"").split("/")[0],n=a.replace(/^https?:\/\//,"").split("/")[0];if(r&&n&&t.includes(r)&&t.includes(n)){let e=RegExp(`(https?://)?(${r})(/*)(https?://)?(${n})`,"gi"),i=`https://${n}`;if(e.test(t)){let a=t;t=t.replace(e,i),s.Ay.info("Fixed concatenated Strapi domains",{original:a,fixed:t,apiDomain:r,mediaDomain:n})}}if(t.includes("https//")){let e=t;t=t.replace(/https\/\//g,"https://"),s.Ay.info("Fixed missing colon in URL (https//)",{original:e,fixed:t})}if(t.startsWith("//")?t=`https:${t}`:(t.includes("media.strapiapp.com")||t.includes(n))&&!t.startsWith("http")?t=`https://${t}`:(t.startsWith("localhost")||t.startsWith(r.split(".")[0]))&&(t=`https://${t}`),t.startsWith("/"))return t;if(t.startsWith("http://")||t.startsWith("https://"))try{return new URL(t),t}catch(e){if(s.Ay.error("URL parsing failed after sanitization attempts",{url:t,error:e}),!t.includes("://")&&!t.includes("."))return t;return""}return a&&t&&!t.includes("://")?(s.Ay.debug("Assuming relative media path, prepending STRAPI_MEDIA_URL",{path:t}),`/${t}`):(s.Ay.warn("sanitizeUrl could not produce a valid absolute or relative URL",{originalInput:e,finalSanitized:t}),t)}function m(e){if(!e)return;let t=p(e);if(t){if(t.startsWith("http://")||t.startsWith("https://"))return t.replace(/^http:/,"https:");if(a){let r=`${a}${t.startsWith("/")?"":"/"}${t}`;return s.Ay.debug("Constructed OG image URL from relative path",{original:e,final:r}),r.replace(/^http:/,"https:")}if(s.Ay.warn("Could not determine OG image URL confidently",{originalUrl:e,processedUrl:t}),i)return`${i}${t.startsWith("/")?"":"/"}${t}`.replace(/^http:/,"https:")}}"development"===n.NODE_ENV&&s.Ay.debug("Media Utils Initialized:",{EFFECTIVE_STRAPI_URL:i,STRAPI_MEDIA_URL:a,SITE_URL:o})},83002:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,90612)),Promise.resolve().then(r.bind(r,80442)),Promise.resolve().then(r.bind(r,72097)),Promise.resolve().then(r.bind(r,32382)),Promise.resolve().then(r.bind(r,57980)),Promise.resolve().then(r.bind(r,66537)),Promise.resolve().then(r.bind(r,54638)),Promise.resolve().then(r.bind(r,98733))},83997:e=>{"use strict";e.exports=require("tty")},85178:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\practitioners\\\\PractitionerProfileImage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\practitioners\\PractitionerProfileImage.tsx","default")},87888:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=r(65239),n=r(48088),i=r(31369),a=r(30893),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);r.d(t,o);let l={children:["",{children:["practitioners",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,22e3)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\practitioners\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\practitioners\\[slug]\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/practitioners/[slug]/page",pathname:"/practitioners/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},88067:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\practitioners\\\\EmailContactDetail.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\practitioners\\EmailContactDetail.tsx","default")},90612:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(60687),n=r(84189),i=r(3832),a=r(43210),o=r(66501);async function l(e,t){try{let r=await fetch("/api/analytics/post-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:e,postSlug:t})});if(!r.ok)throw Error(`Failed to track post view: ${r.statusText}`)}catch(e){o.Ay.error("Error tracking post view:",e)}}let c=({content:e,postId:t,postSlug:r,applyNoFollow:o=!0})=>((0,a.useEffect)(()=>{if(t&&r){let e=setTimeout(()=>{l(t,r)},2e3);return()=>clearTimeout(e)}},[t,r]),(0,s.jsxs)("div",{className:"mb-8",children:[" ",(0,s.jsx)(n.oz,{components:{h1:({node:e,...t})=>(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mt-8 mb-4",...t}),h2:({node:e,...t})=>(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mt-6 mb-3",...t}),h3:({node:e,...t})=>(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mt-5 mb-2",...t}),h4:({node:e,...t})=>(0,s.jsx)("h4",{className:"text-lg font-bold text-gray-800 mt-4 mb-2",...t}),p:({node:e,...t})=>(0,s.jsx)("p",{className:"text-gray-700 mb-4",...t}),a:({node:e,href:t,...r})=>{let n=t&&(t.startsWith("http://")||t.startsWith("https://")),i="";return n&&(o&&(i+="nofollow "),i+="noopener noreferrer"),(0,s.jsx)("a",{className:"text-emerald-600 hover:text-emerald-700 underline",href:t,rel:i.trim()||void 0,target:n?"_blank":void 0,...r})},ul:({node:e,...t})=>(0,s.jsx)("ul",{className:"list-disc pl-6 mb-4",...t}),ol:({node:e,...t})=>(0,s.jsx)("ol",{className:"list-decimal pl-6 mb-4",...t}),li:({node:e,...t})=>(0,s.jsx)("li",{className:"mb-1",...t}),blockquote:({node:e,...t})=>(0,s.jsx)("blockquote",{className:"border-l-4 border-emerald-500 pl-4 italic my-4",...t})},rehypePlugins:[i.A],children:e})]}))},94735:e=>{"use strict";e.exports=require("events")},98733:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687);let n=({data:e})=>e?(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:e}}):null}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,1330,3376,6391,2975,255,5373,8446,270],()=>r(87888));module.exports=s})();