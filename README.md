# Next.js + Strapi Cloud Directory Template Starter Kit

This template provides a solid foundation for building a directory website (e.g., for clinics, practitioners, businesses) using a modern tech stack:

*   **Frontend:** Next.js 15 (App Router), React 19, TypeScript, Tailwind CSS 4
*   **Backend:** Strapi Cloud (for managing directory listings, blog posts, categories, authentication, etc.)

**Note:** This is a **starter kit**. Core directory features like search, filtering, sorting, and pagination are **not fully implemented** in the frontend UI but the backend structure and basic frontend pages are in place.

## Features

*   Separate Frontend (Next.js) and Backend (Strapi Cloud) codebases.
*   User authentication via Strapi's built-in Users & Permissions plugin.
*   Content management via Strapi Cloud CMS.
*   Pre-defined Strapi Content Types for typical directory needs (Clinics, Practitioners, Categories, Blog Posts, etc.).
*   Basic page structure and routing in Next.js.
*   Configurable via environment variables.
*   Tailwind CSS for styling.
*   SEO plugin integration in Strapi.
*   Google Maps integration example (requires API key).

## Prerequisites

*   Node.js (v18 - v22 recommended, see `strapi-cms/package.json` for specific engine)
*   npm or yarn
*   A Strapi Cloud account for production deployment
*   (Optional) Google Cloud account for Google Maps API Key.

## Getting Started

Follow these steps to set up the project locally:

**1. Clone/Download the Template:**

   Obtain the template files and place them in your desired project directory.

**2. Set Up Strapi CMS (Backend):**

   *   Navigate to the `strapi-cms` directory:
     ```bash
     cd strapi-cms
     ```
   *   Copy the example environment file:
     ```bash
     cp .env.example .env
     ```
   *   **Configure `.env`:**
      *   Review the settings in `.env`. For initial local setup, the default SQLite configuration should work.
      *   **Crucially, generate unique security keys:** Replace the placeholder values for `ADMIN_JWT_SECRET`, `API_TOKEN_SALT`, `APP_KEYS`, and `JWT_SECRET`. You can use the command provided in the `.env.example` file comments.
   *   Install dependencies:
     ```bash
     npm install
     # or
     yarn install
     ```
   *   Run the Strapi development server:
     ```bash
     npm run develop
     # or
     yarn develop
     ```
   *   **Create Admin User:** Open your browser to `http://localhost:1337/admin` (or the Strapi `APP_URL` you configured) and create your first administrator account.
   *   **API Permissions:** Go to `Settings` -> `Users & Permissions Plugin` -> `Roles` -> `Public`. Grant permissions for the necessary API endpoints (e.g., find/findOne for clinics, practitioners, categories, blog-posts, etc.) that the frontend needs to access publicly. For authenticated features, configure the `Authenticated` role accordingly.
   *   **(Optional) API Token:** If your frontend needs access to draft content or specific authenticated endpoints, create an API Token in `Settings` -> `API Tokens` and add it to the frontend's `.env.local` file (`STRAPI_API_TOKEN`).

**3. Configure Authentication in Strapi:**

   *   In your Strapi admin panel, go to `Settings` -> `Users & Permissions Plugin` -> `Roles`.
   *   Configure the permissions for `Public` and `Authenticated` roles as needed.
   *   Set up email templates for user registration and password reset if required.

**4. Set Up Next.js (Frontend):**

   *   Navigate to the `frontend` directory:
     ```bash
     cd ../frontend
     # (Assuming you are still in strapi-cms)
     # Or navigate directly: cd path/to/your/project/frontend
     ```
   *   Copy the example environment file:
     ```bash
     cp .env.example .env.local
     ```
   *   **Configure `.env.local`:**
      *   `NEXT_PUBLIC_STRAPI_API_URL`: Set this to your Strapi backend URL (e.g., `http://localhost:1337`).
      *   `NEXT_PUBLIC_SITE_NAME`: Change this to your desired website name.
      *   (Optional) `STRAPI_API_TOKEN`: Add if you created one in Strapi.
      *   (Optional) `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`: Add if using Google Maps features.
      *   (Optional) `NEXT_PUBLIC_SITE_URL`: Set to your intended deployment URL (e.g., `http://localhost:3000` for local dev).
   *   Install dependencies:
     ```bash
     npm install
     # or
     yarn install
     ```
   *   Run the Next.js development server:
     ```bash
     npm run dev
     # or
     yarn dev
     ```
   *   Open your browser to `http://localhost:3000` (or the port Next.js starts on).

## Configuration Details

*   **Strapi:** For local development, configuration is handled via environment variables defined in `strapi-cms/.env`. For production, these settings are managed in the Strapi Cloud dashboard. Refer to the comments in `strapi-cms/.env.example` for details on security and optional service configurations.
*   **Next.js:** Configuration is handled via environment variables in `frontend/.env.local`. Refer to `frontend/.env.example` for available options. Remember to configure `remotePatterns` in `frontend/next.config.ts` to allow images from your *production* Strapi Cloud URL when deploying.

## Testing

The project includes a comprehensive testing setup using Jest and React Testing Library:

*   **Unit Tests:** Test individual components and utility functions in isolation.
*   **Component Tests:** Test React components with simulated user interactions.
*   **Test Coverage:** Track code coverage to identify untested areas.

### Running Tests

```bash
# Navigate to the frontend directory
cd frontend

# Run all tests
npm test

# Run tests in watch mode (for development)
npm run test:watch

# Generate test coverage report
npm run test:coverage
```

### Test Structure

Tests are organized in the `frontend/src/__tests__` directory, mirroring the structure of the source code:

*   `__tests__/lib/`: Tests for utility functions
*   `__tests__/components/`: Tests for React components
*   `__tests__/pages/`: Tests for page components

### Writing Tests

When adding new features, follow these guidelines for writing tests:

1. Create test files with the `.test.ts` or `.test.tsx` extension
2. Use descriptive test names that explain the expected behavior
3. Mock external dependencies to isolate the code being tested
4. Test both success and error cases
5. Aim for high test coverage, especially for critical functionality

## Customization

*   **Branding:**
    *   **Site Name:** Set `NEXT_PUBLIC_SITE_NAME` in `frontend/.env.local`.
    *   **Colors/Theme:** Modify primary colors (emerald) and other styles in `frontend/src/app/globals.css` and Tailwind configuration (`frontend/tailwind.config.mjs` - *Note: Tailwind 4 config might differ slightly, check official docs*). Look for CSS variables if implemented, or directly modify Tailwind classes in components.
    *   **Logo:** Replace the text logo in `frontend/src/components/layout/Header.tsx` with your own SVG or Image component. Ensure image sources are allowed in `frontend/next.config.ts`.
*   **Content:** Manage all directory listings, blog posts, categories, etc., through the Strapi admin panel.
*   **Features:** Extend the frontend components in `frontend/src/components/` and pages in `frontend/src/app/` to add more features or implement search/filtering logic.

## Deployment

*   **Strapi:** Deploy to Strapi Cloud for the simplest production setup. Strapi Cloud provides hosting, database, and content management in one platform with unlimited database storage and 50GB of asset storage. Remember to configure all necessary environment variables in the Strapi Cloud dashboard.
*   **Next.js:** Can be deployed to platforms like Vercel (recommended), Netlify, AWS Amplify, Render, or self-hosted using Node.js or Docker. Ensure all `NEXT_PUBLIC_` environment variables are set in the deployment environment. Configure `remotePatterns` in `next.config.ts` for your production Strapi Cloud URL.

## License

(Add your chosen license information here - e.g., MIT, Apache 2.0, or details of your commercial license)
