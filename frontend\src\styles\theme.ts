/**
 * Theme constants for the application
 * Use these values instead of hardcoding colors, spacing, etc.
 */

/**
 * Color palette
 */
export const colors = {
  // Primary colors
  primary: {
    50: 'var(--color-emerald-50)',
    100: 'var(--color-emerald-100)',
    200: 'var(--color-emerald-200)',
    300: 'var(--color-emerald-300)',
    400: 'var(--color-emerald-400)',
    500: 'var(--color-emerald-500)',
    600: 'var(--color-emerald-600)',
    700: 'var(--color-emerald-700)',
    800: 'var(--color-emerald-800)',
    900: 'var(--color-emerald-900)',
  },
  
  // Secondary colors
  secondary: {
    50: 'var(--color-teal-50)',
    100: 'var(--color-teal-100)',
    200: 'var(--color-teal-200)',
    300: 'var(--color-teal-300)',
    400: 'var(--color-teal-400)',
    500: 'var(--color-teal-500)',
    600: 'var(--color-teal-600)',
    700: 'var(--color-teal-700)',
    800: 'var(--color-teal-800)',
    900: 'var(--color-teal-900)',
  },
  
  // Neutral colors
  gray: {
    50: 'var(--color-gray-50)',
    100: 'var(--color-gray-100)',
    200: 'var(--color-gray-200)',
    300: 'var(--color-gray-300)',
    400: 'var(--color-gray-400)',
    500: 'var(--color-gray-500)',
    600: 'var(--color-gray-600)',
    700: 'var(--color-gray-700)',
    800: 'var(--color-gray-800)',
    900: 'var(--color-gray-900)',
  },
  
  // Semantic colors
  success: 'var(--color-green-700)',
  warning: 'var(--color-amber-500)',
  error: 'var(--color-red-600)',
  info: 'var(--color-blue-500)',
  
  // Base colors
  white: 'var(--color-white)',
  black: 'var(--color-black)',
  
  // Background and foreground
  background: 'var(--background)',
  foreground: 'var(--foreground)',
};

/**
 * Font sizes
 */
export const fontSizes = {
  xs: 'var(--text-xs)',
  sm: 'var(--text-sm)',
  base: '1rem',
  lg: 'var(--text-lg)',
  xl: 'var(--text-xl)',
  '2xl': 'var(--text-2xl)',
  '3xl': 'var(--text-3xl)',
  '4xl': 'var(--text-4xl)',
  '5xl': 'var(--text-5xl)',
};

/**
 * Font weights
 */
export const fontWeights = {
  normal: '400',
  medium: 'var(--font-weight-medium)',
  semibold: 'var(--font-weight-semibold)',
  bold: 'var(--font-weight-bold)',
};

/**
 * Border radius
 */
export const borderRadius = {
  none: '0',
  sm: '0.125rem',
  md: 'var(--radius-md)',
  lg: 'var(--radius-lg)',
  xl: 'var(--radius-xl)',
  full: '9999px',
};

/**
 * Spacing
 */
export const spacing = {
  px: '1px',
  0: '0',
  0.5: 'calc(var(--spacing) * 0.5)',
  1: 'calc(var(--spacing) * 1)',
  2: 'calc(var(--spacing) * 2)',
  3: 'calc(var(--spacing) * 3)',
  4: 'calc(var(--spacing) * 4)',
  5: 'calc(var(--spacing) * 5)',
  6: 'calc(var(--spacing) * 6)',
  8: 'calc(var(--spacing) * 8)',
  10: 'calc(var(--spacing) * 10)',
  12: 'calc(var(--spacing) * 12)',
  16: 'calc(var(--spacing) * 16)',
  20: 'calc(var(--spacing) * 20)',
  24: 'calc(var(--spacing) * 24)',
  32: 'calc(var(--spacing) * 32)',
  40: 'calc(var(--spacing) * 40)',
  48: 'calc(var(--spacing) * 48)',
  56: 'calc(var(--spacing) * 56)',
  64: 'calc(var(--spacing) * 64)',
};

/**
 * Container sizes
 */
export const containers = {
  sm: '640px',
  md: 'var(--container-md)',
  lg: 'var(--container-lg)',
  xl: '1280px',
  '2xl': 'var(--container-2xl)',
  '3xl': 'var(--container-3xl)',
  '4xl': 'var(--container-4xl)',
  '5xl': '1536px',
  '6xl': '1792px',
};

/**
 * Transitions
 */
export const transitions = {
  default: {
    duration: 'var(--default-transition-duration)',
    timing: 'var(--default-transition-timing-function)',
  },
};

/**
 * Z-index values
 */
export const zIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
};

/**
 * Export all theme values
 */
export default {
  colors,
  fontSizes,
  fontWeights,
  borderRadius,
  spacing,
  containers,
  transitions,
  zIndex,
};
