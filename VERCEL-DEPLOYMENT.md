# Vercel Deployment Guide

This guide provides instructions for deploying the Natural Healing Now application to Vercel.

## Environment Variables

You need to set the following environment variables in your Vercel project settings:

### Required Variables

1. **`NEXT_PUBLIC_API_URL`** - The URL of your Strapi Cloud instance
   - Example: `https://your-strapi-domain.com` (no trailing slash)
   - This must be a publicly accessible URL

2. **`IMAGE_HOSTNAME`** - The hostname for your Strapi Cloud Media Storage
   - This is used for image optimization in Next.js
   - Usually the same as your Strapi Cloud hostname

3. **`PREVIEW_SECRET`** - The key for the preview secret
   - Used for secure preview functionality
   - Use a strong, unique value

4. **`STRAPI_API_TOKEN`** - The API token for authenticating with Strapi
   - Create this token in your Strapi Admin Panel (Settings -> API Tokens)
   - Make sure it has the necessary permissions to read content

5. **`JWT_SECRET`** - Secret for cookie encryption (if using authentication)
   - Should match Strapi's JWT secret
   - Use a strong, unique value

### Optional Variables

1. **`NEXT_PUBLIC_SITE_URL`** - The URL of your frontend application
   - Example: `https://your-frontend-domain.com`
   - Used for SEO, sitemap generation, etc.

2. **`NEXT_PUBLIC_SITE_NAME`** - The name of your website
   - Example: `Natural Healing Now`
   - Used in the header/logo area

3. **`NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`** - If using Google Maps features

## Setting Environment Variables in Vercel

1. Go to your project in the Vercel dashboard
2. Click on "Settings" tab
3. Click on "Environment Variables" in the left sidebar
4. Add each variable with its corresponding value
5. Make sure to select the appropriate environments (Production, Preview, Development)
6. Click "Save" to apply the changes

## Troubleshooting 404 Errors

If you encounter a 404 error after deployment:

1. **Check Environment Variables**: Make sure all required environment variables are set correctly
2. **Check API URL Format**: Ensure `NEXT_PUBLIC_STRAPI_API_URL` is correctly formatted without a trailing slash
3. **Check Strapi Availability**: Verify that your Strapi backend is accessible from the public internet
4. **Check API Token**: Verify that your Strapi API token is valid and has the necessary permissions
5. **Check Logs**: Review the Vercel deployment logs for any errors
6. **Check Routes**: Verify that the route you're trying to access exists in your application

## Redeploying

After making changes to your environment variables or code:

1. Trigger a new deployment in Vercel
2. You can do this by pushing a new commit to your repository or using the "Redeploy" button in the Vercel dashboard
3. Monitor the deployment logs for any errors

## Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Strapi Documentation](https://docs.strapi.io)
