"use strict";(()=>{var e={};e.id=5651,e.ids=[5651],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47278:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(5409);async function p(e,{params:r}){let t=r.slug;if(!t)return a.NextResponse.json({error:"Slug is required"},{status:400});try{let e=await (0,u.po)(t);if(e.error||!e.data){let r=e.error?.status||404,t=e.error?.message||"Clinic not found";return a.NextResponse.json({error:t},{status:r})}return a.NextResponse.json(e)}catch(r){console.error(`Error fetching clinic with slug ${t} in API route:`,r);let e=r instanceof Error?r.message:"Failed to fetch clinic";return a.NextResponse.json({error:e},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/clinics/[slug]/route",pathname:"/api/clinics/[slug]",filename:"route",bundlePath:"app/api/clinics/[slug]/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\clinics\\[slug]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:g}=c;function x(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,580,8137],()=>t(47278));module.exports=s})();