(()=>{var e={};e.id=2959,e.ids=[2959],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7610:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},9074:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>o});var s=r(65239),a=r(48088),i=r(31369),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let o={children:["",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,41951)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\search\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,93432)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\search\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,65204)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\search\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\search\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13450:(e,t,r)=>{Promise.resolve().then(r.bind(r,73773))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41951:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\search\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\search\\page.tsx","default")},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},49384:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=function(){for(var e,t,r=0,s="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=function e(t){var r,s,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(s=e(t[r]))&&(a&&(a+=" "),a+=s)}else for(s in t)t[s]&&(a&&(a+=" "),a+=s);return a}(e))&&(s&&(s+=" "),s+=t);return s}},55061:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(60687),a=r(24587),i=r(85814),l=r.n(i),n=r(44867),o=r(17019),d=r(28136);let c=({post:e,showReadingTime:t=!1,showShareButton:r=!1,showBadge:i=!1})=>{var c;let m=(0,d.Jf)(e.featured_image),u=!!e.featured_image,x=(0,d.Jf)(e.author?.profile_picture);e.author?.profile_picture;let h=e.reading_time||(e.content?(c=e.content,Math.max(1,Math.ceil((c?.split(/\s+/)?.length||0)/200))):2),p=x&&(x.startsWith("http")||x.startsWith("/")||x.startsWith("data:"))?x:"",g=(0,n.GP)(new Date(e.publish_date),"MMMM d, yyyy"),f=Object.getOwnPropertyDescriptor(e,"view_count")?.value||0;return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,s.jsxs)(l(),{href:`/blog/${e.slug}`,className:"block relative h-48 w-full overflow-hidden",children:[" ",u?(0,s.jsx)(a.default,{src:m,alt:e.title||"Blog post image",width:600,height:400,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1,showPlaceholder:!0,advancedBlur:!0,fadeIn:!0,preload:e.isFeatured||f>10}):(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-50 to-teal-100 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-emerald-700 font-semibold text-xl opacity-50",children:e.title.charAt(0)})}),i&&(!0===e.isFeatured||f>0)&&(0,s.jsx)("div",{className:`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ${!0===e.isFeatured?"bg-emerald-600 text-white":"bg-amber-500 text-white"}`,children:!0===e.isFeatured?"Featured Post":"Popular Post"})]}),(0,s.jsxs)("div",{className:"p-4 flex-grow flex flex-col",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsx)("div",{className:"text-sm text-gray-500",children:g}),t&&(0,s.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,s.jsx)(o.Ohp,{className:"mr-1"}),(0,s.jsxs)("span",{children:[h," min read"]})]})]}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800 flex-grow",children:(0,s.jsx)(l(),{href:`/blog/${e.slug}`,className:"hover:text-emerald-600 line-clamp-2",children:e.title})}),e.excerpt&&(0,s.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:e.excerpt}),e.author&&(0,s.jsxs)("div",{className:"flex items-center mt-auto pt-4 border-t border-gray-100",children:[" ",(0,s.jsxs)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-200 shadow-sm",children:[" ",p&&(e=>{try{return e&&(e.startsWith("http")||e.startsWith("/")||e.startsWith("data:"))}catch(e){return!1}})(p)?(0,s.jsx)(a.default,{src:p,alt:e.author.name||"Author image",width:40,height:40,fillContainer:!0,className:"object-cover rounded-full",sizes:"40px",showPlaceholder:!0,fadeIn:!0}):(0,s.jsx)("div",{className:"absolute inset-0 bg-emerald-100 flex items-center justify-center",children:(0,s.jsx)(o.JXP,{className:"text-emerald-700 text-lg"})})]}),(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 mb-0.5",children:"Written by"}),(0,s.jsx)(l(),{href:`/blog/authors/${e.author.slug}`,className:"font-medium text-gray-800 hover:text-emerald-600",children:e.author.name})]})]})]}),(0,s.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto flex justify-between items-center",children:[(0,s.jsx)(l(),{href:`/blog/${e.slug}`,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"Read More →"}),r&&(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors",onClick:t=>{t.preventDefault(),navigator.share?navigator.share({title:e.title,text:e.excerpt||"",url:`${window.location.origin}/blog/${e.slug}`}).catch(e=>{console.error("Error sharing:",e)}):navigator.clipboard.writeText(`${window.location.origin}/blog/${e.slug}`).then(()=>{alert("Link copied to clipboard!")}).catch(e=>{console.error("Could not copy text: ",e)})},"aria-label":"Share article",children:(0,s.jsx)(o.Pum,{size:18})}),(0,s.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors","aria-label":"Save article",children:(0,s.jsx)(o.Y19,{size:18})})]})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(37413);function a(){return(0,s.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,s.jsx)("div",{className:"flex justify-center items-center py-24",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-emerald-500"})})})}},73773:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),a=r(43210),i=r(16189);r(73076);var l=r(26800),n=r(30104),o=r(55061),d=r(96647),c=r(28831),m=r(91936),u=r(85814),x=r.n(u),h=r(17019);function p(){let e=(0,i.useSearchParams)(),t=e.get("q")||"";parseInt(e.get("page")||"1",10);let[r,u]=(0,a.useState)(t),[p,g]=(0,a.useState)({clinics:[],practitioners:[],blogPosts:[],categories:[],specialties:[],conditions:[],totalResults:0}),[f,b]=(0,a.useState)(!0),[j,v]=(0,a.useState)(null),w=Math.ceil(p.totalResults/36);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-12",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Search Results"}),(0,s.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,s.jsx)(c.A,{placeholder:"Search for clinics, practitioners, or health topics...",onSearch:e=>{u(e),e.trim()&&(window.location.href=`/search?q=${encodeURIComponent(e)}`)},buttonText:"Search",buttonClassName:"bg-emerald-800 text-white hover:bg-emerald-900",className:"shadow-lg",initialValue:t})})]})})}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-12",children:f?(0,s.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500"})}):j?(0,s.jsx)("div",{className:"text-center p-8 bg-red-50 rounded-lg text-red-600",children:j}):0===p.totalResults?(0,s.jsxs)("div",{className:"text-center p-8 bg-gray-50 rounded-lg",children:[(0,s.jsx)(h.CKj,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"No results found"}),(0,s.jsxs)("p",{className:"text-gray-600 mb-6",children:["We couldn't find any matches for \"",t,'". Please try a different search term.']}),(0,s.jsx)(x(),{href:"/",className:"inline-block bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium",children:"Return to Homepage"})]}):(0,s.jsxs)("div",{className:"space-y-12",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:["Found ",p.totalResults,' results for "',t,'"']})}),p.clinics.length>0&&(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Clinics"}),(0,s.jsx)(x(),{href:`/clinics?query=${encodeURIComponent(t)}`,className:"text-emerald-600 hover:text-emerald-700",children:"View all clinic results"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.clinics.map(e=>(0,s.jsx)(l.default,{clinic:{id:e.id,name:e.name||"Unnamed Clinic",slug:e.slug||`clinic-${e.id}`,description:e.description,logo:e.logo?.url?`https://nice-badge-2130241d6c.strapiapp.com${e.logo.url}`:null,featuredImage:e.featuredImage?.url?`https://nice-badge-2130241d6c.strapiapp.com${e.featuredImage.url}`:null,address:e.address,contactInfo:e.contactInfo,isVerified:e.isVerified||!1},showContactInfo:!1},e.id))})]}),p.practitioners.length>0&&(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Practitioners"}),(0,s.jsx)(x(),{href:`/practitioners?query=${encodeURIComponent(t)}`,className:"text-emerald-600 hover:text-emerald-700",children:"View all practitioner results"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.practitioners.map(e=>(0,s.jsx)(n.default,{practitioner:{id:e.id,name:e.name||"Unnamed Practitioner",slug:e.slug||`practitioner-${e.id}`,bio:e.bio,profilePicture:e.profilePicture?.url?`https://nice-badge-2130241d6c.strapiapp.com${e.profilePicture.url}`:null,qualifications:e.qualifications,contactInfo:e.contactInfo,isVerified:e.isVerified||!1}},e.id))})]}),p.blogPosts.length>0&&(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Blog Posts"}),(0,s.jsx)(x(),{href:`/blog?query=${encodeURIComponent(t)}`,className:"text-emerald-600 hover:text-emerald-700",children:"View all blog results"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.blogPosts.map(e=>(0,s.jsx)(o.default,{post:e,showReadingTime:!0,showShareButton:!0,showBadge:!0},e.id))})]}),p.categories.length>0&&(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Categories"}),(0,s.jsx)(x(),{href:`/categories?query=${encodeURIComponent(t)}`,className:"text-emerald-600 hover:text-emerald-700",children:"View all category results"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:p.categories.map(e=>(0,s.jsx)(d.default,{category:{id:e.id,name:e.name||"Unnamed Category",slug:e.slug||`category-${e.id}`,description:e.description,icon:e.icon?.url?`https://nice-badge-2130241d6c.strapiapp.com${e.icon.url}`:null,featured_image:e.featuredImage?.url?`https://nice-badge-2130241d6c.strapiapp.com${e.featuredImage.url}`:null}},e.id))})]}),w>1&&(0,s.jsx)("div",{className:"mt-12 flex justify-center",children:(0,s.jsx)(m.default,{totalPages:w})})]})}),(0,s.jsx)("div",{className:"bg-gray-50 py-12",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Can't find what you're looking for?"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Browse our categories or contact us for personalized assistance with your natural healing journey."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(x(),{href:"/categories",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium",children:"Explore Categories"}),(0,s.jsx)(x(),{href:"/contact",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-medium",children:"Contact Us"})]})]})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78946:(e,t,r)=>{Promise.resolve().then(r.bind(r,41951))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91936:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(60687),a=r(44725),i=r(7610),l=r(49384),n=r(85814),o=r.n(n),d=r(16189);let c=(e,t)=>t<=7?Array.from({length:t},(e,t)=>t+1):e<=3?[1,2,3,"...",t-1,t]:e>=t-2?[1,2,"...",t-2,t-1,t]:[1,"...",e-1,e,e+1,"...",t];function m({totalPages:e,currentPage:t}){let r=(0,d.usePathname)(),a=(0,d.useSearchParams)(),i=void 0!==t?t:Number(a.get("page"))||1,l=e=>{let t=new URLSearchParams(a);return t.set("page",e.toString()),`${r}?${t.toString()}`},n=c(i,e);return e<=1?null:(0,s.jsxs)("div",{className:"inline-flex",children:[(0,s.jsx)(x,{direction:"left",href:l(i-1),isDisabled:i<=1}),(0,s.jsx)("div",{className:"flex -space-x-px",children:n.map((e,t)=>{let r;return 0===t&&(r="first"),t===n.length-1&&(r="last"),1===n.length&&(r="single"),"..."===e&&(r="middle"),(0,s.jsx)(u,{href:l(e),page:e,position:r,isActive:i===e},`${e}-${t}`)})}),(0,s.jsx)(x,{direction:"right",href:l(i+1),isDisabled:i>=e})]})}function u({page:e,href:t,isActive:r,position:a}){let i=(0,l.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===a||"single"===a,"rounded-r-md":"last"===a||"single"===a,"z-10 bg-emerald-600 border-emerald-600 text-white":r,"hover:bg-gray-100":!r&&"middle"!==a,"text-gray-300 pointer-events-none":"middle"===a});return r||"middle"===a?(0,s.jsx)("div",{className:i,children:e}):(0,s.jsx)(o(),{href:t,className:i,children:e})}function x({href:e,direction:t,isDisabled:r}){let n=(0,l.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":r,"hover:bg-gray-100":!r,"mr-2 md:mr-4":"left"===t,"ml-2 md:ml-4":"right"===t}),d="left"===t?(0,s.jsx)(a.A,{className:"w-4"}):(0,s.jsx)(i.A,{className:"w-4"});return r?(0,s.jsx)("div",{className:n,children:d}):(0,s.jsx)(o(),{className:n,href:e,children:d})}},93432:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>a});var s=r(37413);let a={title:"Search Results | Natural Healing Now",description:"Find natural healing solutions, practitioners, clinics, and health information."};function i({children:e}){return(0,s.jsx)("main",{children:e})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,1330,3376,6391,2975,255,4867,9298,8446,270,3076,8319],()=>r(9074));module.exports=s})();