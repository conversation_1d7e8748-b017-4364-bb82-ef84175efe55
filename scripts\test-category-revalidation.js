/**
 * <PERSON><PERSON><PERSON> to test the category revalidation API
 * 
 * This script sends test requests to the category revalidation API to verify that it's working correctly.
 * 
 * Usage:
 * node scripts/test-category-revalidation.js
 */

const axios = require('axios');
require('dotenv').config();

// Configuration
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';
const REVALIDATE_TOKEN = process.env.PREVIEW_SECRET || process.env.REVALIDATE_TOKEN || '3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=';

// Check required environment variables
if (!REVALIDATE_TOKEN) {
  console.error('Error: PREVIEW_SECRET or REVALIDATE_TOKEN is required.');
  process.exit(1);
}

// Test the category-specific revalidation endpoint
async function testCategoryRevalidation() {
  console.log('\n🧪 Testing category-specific revalidation endpoint...');
  
  try {
    const response = await axios.post(`${SITE_URL}/api/revalidate/categories`, {
      token: REVALIDATE_TOKEN,
      model: 'category',
      entry: {
        id: 'test-category',
        slug: 'naturopathic-doctors'
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-Revalidate-Secret': REVALIDATE_TOKEN
      }
    });
    
    console.log('✅ Category-specific revalidation successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('❌ Category-specific revalidation failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    return false;
  }
}

// Test the GET endpoint for manual revalidation
async function testManualCategoryRevalidation() {
  console.log('\n🧪 Testing manual category revalidation endpoint...');
  
  try {
    const response = await axios.get(`${SITE_URL}/api/revalidate/categories?slug=naturopathic-doctors&secret=${REVALIDATE_TOKEN}`, {
      headers: {
        'X-Revalidate-Secret': REVALIDATE_TOKEN
      }
    });
    
    console.log('✅ Manual category revalidation successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('❌ Manual category revalidation failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    return false;
  }
}

// Main function to run all tests
async function runTests() {
  console.log('🚀 Testing category revalidation endpoints...');
  console.log(`Site URL: ${SITE_URL}`);
  
  let results = {
    category: false,
    manual: false
  };
  
  // Run all tests
  results.category = await testCategoryRevalidation();
  results.manual = await testManualCategoryRevalidation();
  
  // Print summary
  console.log('\n📊 Test Results:');
  console.log(`Category Revalidation: ${results.category ? '✅ Passed' : '❌ Failed'}`);
  console.log(`Manual Category Revalidation: ${results.manual ? '✅ Passed' : '❌ Failed'}`);
}

// Run the tests
runTests();
