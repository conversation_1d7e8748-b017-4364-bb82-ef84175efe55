'use client';

import { ReactNode, useState } from 'react';
import Link from 'next/link';
import { useQueryClient } from '@tanstack/react-query';
import { preloadApiData, preloadPage } from '@/lib/preload';
import { useRouter } from 'next/navigation';

interface LinkWithPreloadProps {
  href: string;
  children: ReactNode;
  className?: string;
  prefetchApiEndpoint?: string;
  prefetchApiParams?: Record<string, any>;
  prefetchQueryKey?: unknown[];
  prefetchOnHover?: boolean;
  prefetchOnMount?: boolean;
  onClick?: () => void;
  ariaLabel?: string;
  title?: string;
}

/**
 * Enhanced Link component with data preloading capabilities
 * 
 * Extends Next.js Link with the ability to preload API data when hovering
 * or mounting the link, improving perceived performance for the user.
 */
export default function LinkWithPreload({
  href,
  children,
  className,
  prefetchApiEndpoint,
  prefetchApiParams,
  prefetchQueryKey,
  prefetchOnHover = true,
  prefetchOnMount = false,
  onClick,
  ariaLabel,
  title,
}: LinkWithPreloadProps) {
  const queryClient = useQueryClient();
  const router = useRouter();
  const [hasPrefetched, setHasPrefetched] = useState(false);
  
  // Function to handle preloading
  const handlePreload = async () => {
    if (hasPrefetched) return;
    
    try {
      // Preload the page
      preloadPage(href);
      
      // Preload API data if endpoint is provided
      if (prefetchApiEndpoint) {
        await preloadApiData(
          queryClient,
          prefetchApiEndpoint,
          prefetchApiParams,
          prefetchQueryKey
        );
      }
      
      setHasPrefetched(true);
    } catch (error) {
      console.error('Error preloading data:', error);
      // Don't throw - preloading errors shouldn't affect the user experience
    }
  };
  
  // Preload on mount if enabled
  if (prefetchOnMount && !hasPrefetched) {
    handlePreload();
  }
  
  return (
    <Link
      href={href}
      className={className}
      aria-label={ariaLabel}
      title={title}
      onClick={(e) => {
        if (onClick) onClick();
      }}
      onMouseEnter={() => {
        if (prefetchOnHover) {
          handlePreload();
        }
      }}
      onTouchStart={() => {
        if (prefetchOnHover) {
          handlePreload();
        }
      }}
      prefetch={false} // We're handling prefetching manually
    >
      {children}
    </Link>
  );
}
