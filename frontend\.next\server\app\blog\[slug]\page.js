(()=>{var e={};e.id=5953,e.ids=[5953],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11036:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y,dynamicParams:()=>x,generateMetadata:()=>b,generateStaticParams:()=>m,revalidate:()=>f});var a=r(37413),s=r(4536),o=r.n(s),l=r(58446),n=r(39916),i=r(12045),c=r(36463),d=r(82158);function u(e){return Math.max(1,Math.ceil((e?.split(/\s+/)?.length||0)/200))}function p(e){if(!e)return c.Ay.warn("No blog post data to map"),null;let t=e.id;if(!t||!e)return c.Ay.warn("Invalid blog post data structure:",e),null;c.Ay.debug("Using API URL:","https://nice-badge-2130241d6c.strapiapp.com");let r=[];try{let t=e.blog_categories;Array.isArray(t)&&(r=t.map(e=>{let t=e.attributes||e;return{id:e.id||"",name:t.name||"Unnamed Category",slug:t.slug||"unnamed-category"}}))}catch(e){c.Ay.warn("Error extracting categories:",e)}let a=[];try{let t=e.blog_tags;Array.isArray(t)&&(a=t.map(e=>(e.attributes||e).name||"").filter(Boolean))}catch(e){c.Ay.warn("Error extracting tags:",e)}let s=[];try{let t=e.related_posts;Array.isArray(t)&&(s=t.map(e=>{let t=e.attributes||e;return{id:e.id||"",title:t.title||"Untitled Post",slug:t.slug||"untitled-post",excerpt:t.excerpt||null,content:t.content||""}}))}catch(e){c.Ay.warn("Error extracting related posts:",e)}let o={id:"0",name:"Unknown Author",slug:"unknown-author",profile_picture:null,bio:null};try{let t=e.author_blogs,r=Array.isArray(t)?t[0]:t;if(r){let e=(0,d.Z5)(r.profilePicture);o={id:String(r.id||"0"),name:r.name||"Unknown Author",slug:r.slug||"unknown-author",profile_picture:e,bio:r.bio||null}}}catch(e){c.Ay.warn("Error extracting author information:",e)}let l=(0,d.Z5)(e.featuredImage),n=e.title||"Untitled Post",i=e.slug||"untitled-post",p=e.excerpt||null,g=e.content||"<p>No content available.</p>",h=e.publishDate||e.publishedAt||e.createdAt||new Date().toISOString(),m=u(g),f={id:String(t),title:n,slug:i,excerpt:p,content:g,featured_image:l,published_at:h,reading_time:m,author:o,categories:r,tags:a,related_posts:s.map(e=>{let t=e.content||"",r=t?u(t):e.excerpt?u(e.excerpt.repeat(5)):2;return{...e,content:t,reading_time:r}})};return void 0!==e.view_count&&Object.defineProperty(f,"view_count",{value:e.view_count,enumerable:!1}),f}var g=r(20846),h=r(30584);async function m(){try{let e=await l.$.blog.getAllSlugs();if(e&&e.data&&Array.isArray(e.data))return e.data.filter(e=>null!==e&&"string"==typeof e.slug).map(e=>({slug:e.slug}));return c.Ay.error("Failed to fetch blog post slugs or data is not in expected format for generateStaticParams:",e),[]}catch(e){return c.Ay.error("Error fetching blog post slugs for generateStaticParams:",e),[]}}let f=!1,x=!0;async function b({params:e,searchParams:t},r){let a=(await e).slug,s=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";try{let e,t,r=await l.$.blog.getPostBySlug(a);if(!r||!r.data||0===r.data.length)return(0,n.notFound)();let o=r.data[0],i=o.seo,u=i?.metaTitle||o.title||"Blog Post",p=i?.metaDescription||function(e,t=160){if(!e)return"";let r=e.replace(/<[^>]*>/g,"").replace(/\s+/g," ").trim();return r.length<=t?r:r.substring(0,t).trimEnd()+"..."}(o.content),g=i?.metaImage?.url,h=o.featuredImage?.url;g?(e=(0,d.Rb)(g),t=(0,d.Rb)(g)):h&&(e=(0,d.Rb)(h),t=(0,d.Rb)(h)),c.Ay.debug("Blog post og:image details for [slug] page:",{seoMetaImage:g,featuredImage:h,ogImageUrl:e,twitterImageUrl:t});let m={title:u,description:p,robots:i?.metaRobots||"index, follow",alternates:{canonical:i?.canonicalURL||(s?`${s}/blog/${a}`:`/blog/${a}`)},openGraph:i?.openGraph?{title:i.openGraph.title||u,description:i.openGraph.description||p,type:i.openGraph.type||"article",url:i.openGraph.url||i?.canonicalURL||(s?`${s}/blog/${a}`:`/blog/${a}`),siteName:i.openGraph.siteName||"Natural Healing Now",publishedTime:o.published_at||o.publishDate||o.createdAt,authors:[o.author_blogs?.[0]?.name||"Natural Healing Now"],...i.openGraph.image?.url?{images:[{url:(0,d.Rb)(i.openGraph.image.url)||""}]}:e?{images:[{url:e}]}:{}}:{title:u,description:p,type:"article",publishedTime:o.published_at||o.publishDate||o.createdAt,authors:[o.author_blogs?.[0]?.name||"Natural Healing Now"],url:i?.canonicalURL||(s?`${s}/blog/${a}`:`/blog/${a}`),siteName:"Natural Healing Now",images:e?[{url:e}]:[]},twitter:{card:"summary_large_image",title:i?.twitter?.title||u,description:i?.twitter?.description||p,images:t?[t]:[]}};if(i?.structuredData&&"string"==typeof i.structuredData)try{JSON.parse(i.structuredData)}catch(e){c.Ay.error("Invalid structured data JSON in [slug] page metadata:",{error:e,data:i.structuredData})}return m}catch(e){return c.Ay.error("Error generating metadata for blog post [slug]:",{slug:a,error:e}),{title:"Error Fetching Post",description:"Could not load blog post details."}}}async function y({params:e}){let t=(await e).slug,r=null,s=[];try{c.Ay.debug(`[BlogPostPage] Fetching post by slug: ${t}`);let e=await l.$.blog.getPostBySlug(t);if(!e||!e.data||!(e.data.length>0))return c.Ay.warn(`[BlogPostPage] Post not found for slug: ${t}`),(0,n.notFound)();if((r=p(e.data[0]))&&(!r.related_posts||0===r.related_posts.length)){c.Ay.debug(`[BlogPostPage] No pre-populated related posts for ${t}. Fetching potential related posts in batch.`);let e=r.categories?.map(e=>e.id)||[],a=r.tags||[],o={id:{$ne:r.id}},n=[];if(e.length>0&&n.push({blog_categories:{id:{$in:e}}}),a.length>0&&n.push({blog_tags:{name:{$in:a}}}),n.length>0?o.$or=n:c.Ay.debug(`[BlogPostPage] No categories or tags for ${t} to find related posts. Consider fetching recent posts.`),n.length>0){let e=await l.$.blog.getPosts({sort:"publishDate:desc",pagination:{page:1,pageSize:10},filters:o,populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}},blog_categories:!0,blog_tags:!0,fields:["title","slug","excerpt","content","publishDate"]}});if(e&&Array.isArray(e.data)){let a=e.data.map(e=>p(e)).filter(e=>null!==e);a.length>0?(s=function(e,t,r=3){console.log(`Finding related posts for: "${e.title}" (ID: ${e.id})`),console.log(`Total posts to compare: ${t.length}`),e.categories||console.warn("Current post has no categories"),e.tags||console.warn("Current post has no tags");let a=t.filter(t=>t.id!==e.id).map(t=>{let r=function(e,t){let r=0,a={};if(e.id===t.id)return 0;if(e.categories&&t.categories){let s=e.categories.map(e=>e.id),o=t.categories.map(e=>e.id),l=0;s.forEach(e=>{o.includes(e)&&(l+=3)}),r+=l,a.categoryScore=l}else console.log(`Missing categories for comparison: post1 has ${e.categories?.length||0}, post2 has ${t.categories?.length||0}`);if(e.tags&&t.tags){let s=0;e.tags.forEach(e=>{t.tags?.includes(e)&&(s+=2)}),r+=s,a.tagScore=s}else console.log(`Missing tags for comparison: post1 has ${e.tags?.length||0}, post2 has ${t.tags?.length||0}`);let s=e.title.toLowerCase().split(/\s+/),o=t.title.toLowerCase().split(/\s+/),l=["the","a","an","and","or","but","in","on","at","to","for","with","by","about","as"],n=0;if(s.forEach(e=>{e.length>3&&!l.includes(e)&&o.includes(e)&&(n+=1)}),r+=n,a.titleScore=n,e.content&&t.content){let s=e.content.toLowerCase().split(/\s+/).filter(e=>e.length>4&&!l.includes(e)).slice(0,100),o=t.content.toLowerCase().split(/\s+/).filter(e=>e.length>4&&!l.includes(e)).slice(0,100),n=0;s.forEach(e=>{o.includes(e)&&n++});let i=.2*n;r+=i,a.contentScore=i}else console.log(`Missing content for comparison: post1 has content: ${!!e.content}, post2 has content: ${!!t.content}`);let i=0;e.author&&t.author&&e.author.id===t.author.id&&(r+=i=1,a.authorScore=i),new Date(e.published_at).getTime();let c=new Date(t.published_at).getTime(),d=Math.floor((new Date().getTime()-c)/864e5),u=0;return d<30&&(r+=u=1-d/30,a.recencyScore=u),console.log(`Similarity score between "${e.title}" and "${t.title}": ${r}`,a),r}(e,t);return{post:t,score:r}}).sort((e,t)=>t.score-e.score);return console.log("Top scoring posts:"),a.slice(0,Math.min(5,a.length)).forEach((e,t)=>{console.log(`${t+1}. "${e.post.title}" - Score: ${e.score}`)}),a.slice(0,r).map(e=>e.post)}(r,a,4),r.related_posts=s.map(e=>{let t="string"==typeof e.content?e.content:"",r=null===e.excerpt?void 0:e.excerpt;return{id:e.id,title:e.title,slug:e.slug,excerpt:r,content:t,reading_time:e.reading_time}}),c.Ay.debug(`[BlogPostPage] Found ${s.length} related posts via algorithm for ${t}.`)):c.Ay.debug(`[BlogPostPage] No potential related posts found from batch fetch for ${t}.`)}}}else r&&c.Ay.debug(`[BlogPostPage] Using ${r.related_posts?.length||0} pre-populated related posts for ${t}.`)}catch(e){return c.Ay.error(`PROD: Failed to fetch post ${t}. Error:`,{error:e.message}),(0,n.notFound)()}if(!r)return(0,n.notFound)();let u=null,m=r.seo;if(m?.structuredData){if("string"==typeof m.structuredData)u=m.structuredData;else if("object"==typeof m.structuredData)try{u=JSON.stringify(m.structuredData)}catch(e){c.Ay.error("Failed to stringify structuredData object in [slug] page:",{error:e,data:m.structuredData})}}if(!u){let e,t=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";r.featured_image&&(e=(0,d.Rb)(r.featured_image)),u=JSON.stringify({"@context":"https://schema.org","@type":"BlogPosting",headline:r.title,description:r.excerpt||"",image:e?[e]:[],datePublished:r.published_at||"",dateModified:r.published_at||"",author:{"@type":"Person",name:r.author?.name||"Natural Healing Now"},publisher:{"@type":"Organization",name:"Natural Healing Now",logo:{"@type":"ImageObject",url:`${t}/logo.png`}},mainEntityOfPage:{"@type":"WebPage","@id":`${t}/blog/${r.slug}`}})}return(0,a.jsxs)(a.Fragment,{children:[u&&(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:u}}),(0,a.jsx)("div",{className:"bg-gray-100 py-3",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(o(),{href:"/",className:"hover:text-emerald-600",children:"Home"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)(o(),{href:"/blog",className:"hover:text-emerald-600",children:"Blog"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)("span",{className:"text-gray-800",children:r.title})]})})}),(0,a.jsx)("article",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)(g.default,{post:r}),(0,a.jsx)(i.default,{content:r.content,postId:r.id,postSlug:r.slug}),(0,a.jsx)(h.default,{post:r})]})})]})}},12045:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\MarkdownContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\MarkdownContent.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20846:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\BlogPostHeader.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\BlogPostHeader.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30584:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\BlogPostFooter.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\BlogPostFooter.tsx","default")},32928:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,30584)),Promise.resolve().then(r.bind(r,20846)),Promise.resolve().then(r.bind(r,12045))},33873:e=>{"use strict";e.exports=require("path")},36463:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l});var a=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let s={enabled:!1,level:"info",prefix:"[NHN]"};function o(e,t,...r){if(!s.enabled)return;let l=Object.values(a),n=l.indexOf(s.level);if(l.indexOf(e)>=n){let a=s.prefix?`${s.prefix} `:"",o=`${a}${t}`;switch(e){case"debug":console.debug(o,...r);break;case"info":console.info(o,...r);break;case"warn":console.warn(o,...r);break;case"error":console.error(o,...r)}}}let l={debug:function(e,...t){o("debug",e,...t)},info:function(e,...t){o("info",e,...t)},warn:function(e,...t){o("warn",e,...t)},error:function(e,...t){o("error",e,...t)},configure:function(e){s={...s,...e}}}},45952:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,66306)),Promise.resolve().then(r.bind(r,81580)),Promise.resolve().then(r.bind(r,90612))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66306:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(60687),s=r(85814),o=r.n(s),l=r(17019),n=r(92224);let i=({post:e})=>(0,a.jsxs)("footer",{className:"border-t border-gray-200 pt-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[e.categories&&e.categories.length>0&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Categories:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.categories.map(e=>(0,a.jsx)(o(),{href:`/blog/categories/${e.slug}`,className:"bg-emerald-50 text-emerald-700 px-3 py-1 rounded-full text-sm hover:bg-emerald-100",children:e.name},e.id))})]}),e.tags&&e.tags.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Tags:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,t)=>(0,a.jsxs)(o(),{href:`/blog/tags/${e.toLowerCase().replace(/ /g,"-")}`,className:"flex items-center bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200",children:[(0,a.jsx)(l.cnX,{className:"mr-1 h-3 w-3"}),e]},t))})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[e.author.profile_picture?(0,a.jsx)("div",{className:"h-12 w-12 rounded-full overflow-hidden mr-4",children:(0,a.jsx)("img",{src:e.author.profile_picture,alt:e.author.name,className:"h-full w-full object-cover"})}):(0,a.jsx)("div",{className:"bg-emerald-100 h-12 w-12 rounded-full flex items-center justify-center mr-4",children:(0,a.jsx)("span",{className:"text-emerald-700 font-semibold",children:e.author.name.charAt(0)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:(0,a.jsx)(o(),{href:`/blog/authors/${e.author.slug}`,className:"hover:text-emerald-600",children:e.author.name})}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Contributor"})]})]}),e.author.bio?(0,a.jsx)("p",{className:"text-gray-700",children:(0,n.Bt)(e.author.bio,30)}):(0,a.jsx)("p",{className:"text-gray-700 italic",children:"No bio available for this author."})]}),e.related_posts&&e.related_posts.length>0&&(0,a.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-gray-800 mb-6 flex items-center",children:[(0,a.jsx)("span",{className:"bg-emerald-100 text-emerald-700 p-2 rounded-full mr-3",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{d:"M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"})})}),"You May Also Like"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:e.related_posts.map(e=>(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-all hover:-translate-y-1",children:(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold mb-2 line-clamp-2",children:(0,a.jsx)(o(),{href:`/blog/${e.slug}`,className:"text-gray-800 hover:text-emerald-600",children:e.title})}),e.excerpt&&(0,a.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.excerpt}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)(o(),{href:`/blog/${e.slug}`,className:"text-emerald-600 hover:text-emerald-700 font-medium flex items-center",children:["Read Article ",(0,a.jsx)("span",{className:"ml-1",children:"→"})]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,a.jsx)(l.Ohp,{className:"mr-1"}),(0,a.jsxs)("span",{children:[e.reading_time||2," min read"]})]})]})]})},e.id))}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsx)(o(),{href:"/blog",className:"inline-flex items-center justify-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors",children:"Explore More Articles"})})]}),(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsxs)(o(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 flex items-center",children:[(0,a.jsx)(l.kRp,{className:"mr-2"})," Back to Blog"]})})]})},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},79551:e=>{"use strict";e.exports=require("url")},81580:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(60687),s=r(85814),o=r.n(s),l=r(17019),n=r(92224);let i=({post:e})=>(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-800 mb-4",children:e.title}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center text-gray-600 mb-6 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.wIk,{className:"mr-2 text-emerald-500"}),(0,a.jsx)("span",{children:(0,n.Yq)(e.published_at)})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.JXP,{className:"mr-2 text-emerald-500"}),(0,a.jsx)(o(),{href:`/blog/authors/${e.author.slug}`,className:"text-emerald-600 hover:text-emerald-700",children:e.author.name})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.Ohp,{className:"mr-2 text-emerald-500"}),(0,a.jsxs)("span",{children:[e.reading_time||2," min read"]})]}),e.categories&&e.categories.length>0&&(0,a.jsxs)("div",{className:"flex items-center flex-wrap gap-2",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(l.UDU,{className:"mr-2 text-emerald-500"})}),e.categories.map(e=>(0,a.jsx)(o(),{href:`/blog/categories/${e.slug}`,className:"bg-emerald-50 text-emerald-700 px-2 py-1 rounded-full text-xs hover:bg-emerald-100 transition-colors",children:e.name},e.id))]})]}),e.featured_image&&(0,a.jsx)("div",{className:"relative h-96 w-full mb-8",children:(0,a.jsx)("img",{src:e.featured_image,alt:e.title,className:"object-cover w-full h-full rounded-lg"})})]})},81630:e=>{"use strict";e.exports=require("http")},82158:(e,t,r)=>{"use strict";r.d(t,{Jf:()=>p,Rb:()=>g,Z5:()=>d,tz:()=>u});var a=r(36463);let s={NEXT_PUBLIC_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_MEDIA_URL:"https://nice-badge-2130241d6c.media.strapiapp.com",NEXT_PUBLIC_SITE_URL:process.env.NEXT_PUBLIC_SITE_URL,IMAGE_HOSTNAME:process.env.IMAGE_HOSTNAME,NODE_ENV:"production"},o=s.NEXT_PUBLIC_STRAPI_API_URL||s.NEXT_PUBLIC_API_URL||("development"===s.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.strapiapp.com"),l=(()=>{if(s.NEXT_PUBLIC_STRAPI_MEDIA_URL)return i(c(s.NEXT_PUBLIC_STRAPI_MEDIA_URL));if(s.IMAGE_HOSTNAME)return i(c(s.IMAGE_HOSTNAME));try{let e=new URL(o);if(e.hostname.endsWith("strapiapp.com"))return`${i(e.protocol)}//${e.hostname.replace("strapiapp.com","media.strapiapp.com")}`;return i(c(o))}catch(e){return"development"===s.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.media.strapiapp.com"}})(),n=s.NEXT_PUBLIC_SITE_URL||(s.NEXT_PUBLIC_API_URL&&s.NEXT_PUBLIC_API_URL.includes("strapiapp.com")?s.NEXT_PUBLIC_API_URL.replace(".strapiapp.com",".vercel.app"):"https://naturalhealingnow.vercel.app");function i(e){return e?e.replace(/^http:/,"https:"):e}function c(e){return e&&e.endsWith("/")?e.slice(0,-1):e}function d(e,t={debug:!1}){if(t.debug&&a.Ay.debug("getStrapiMediaUrl input:",{type:typeof e,isNull:null===e,isUndefined:void 0===e,value:e}),!e)return null;let r=null;if("string"==typeof e?r=e:"object"==typeof e&&(r=e.url||e.data?.attributes?.url||e.data?.url||null),!r)return t.debug,a.Ay.warn("Could not extract initial URL from mediaInput in getStrapiMediaUrl",{mediaInput:e}),null;let s=p(r);return s?s.startsWith("http://")||s.startsWith("https://")?s:l?`${l}${s.startsWith("/")?"":"/"}${s}`:(a.Ay.warn("STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl",{sanitizedUrl:s}),`${o}${s.startsWith("/")?"":"/"}${s}`):(t.debug,a.Ay.warn("URL became empty after sanitization in getStrapiMediaUrl",{originalUrl:r}),null)}function u(e){if(!e||!e.profilePicture)return null;let t=e.profilePicture,r=t.url||t.data?.attributes?.url||t.data?.url||t.formats?.thumbnail?.url;return r?d(r):d(t)}function p(e){let t;if("string"==typeof e&&(e.startsWith("https://")||e.startsWith("http://")||e.startsWith("/")))return e.startsWith("http://")?e.replace(/^http:/,"https:"):e;if(!e)return"";if("object"==typeof e&&e.url&&"string"==typeof e.url)t=e.url;else{if("string"!=typeof e)return a.Ay.warn("Invalid input type for sanitizeUrl. Expected string or object with url property.",{inputType:typeof e}),"";t=e}(t=t.trim()).toLowerCase().startsWith("undefined")&&(t=t.substring(9),a.Ay.info('Removed "undefined" prefix from URL',{original:e,new:t}));let r=o.replace(/^https?:\/\//,"").split("/")[0],s=l.replace(/^https?:\/\//,"").split("/")[0];if(r&&s&&t.includes(r)&&t.includes(s)){let e=RegExp(`(https?://)?(${r})(/*)(https?://)?(${s})`,"gi"),o=`https://${s}`;if(e.test(t)){let l=t;t=t.replace(e,o),a.Ay.info("Fixed concatenated Strapi domains",{original:l,fixed:t,apiDomain:r,mediaDomain:s})}}if(t.includes("https//")){let e=t;t=t.replace(/https\/\//g,"https://"),a.Ay.info("Fixed missing colon in URL (https//)",{original:e,fixed:t})}if(t.startsWith("//")?t=`https:${t}`:(t.includes("media.strapiapp.com")||t.includes(s))&&!t.startsWith("http")?t=`https://${t}`:(t.startsWith("localhost")||t.startsWith(r.split(".")[0]))&&(t=`https://${t}`),t.startsWith("/"))return t;if(t.startsWith("http://")||t.startsWith("https://"))try{return new URL(t),t}catch(e){if(a.Ay.error("URL parsing failed after sanitization attempts",{url:t,error:e}),!t.includes("://")&&!t.includes("."))return t;return""}return l&&t&&!t.includes("://")?(a.Ay.debug("Assuming relative media path, prepending STRAPI_MEDIA_URL",{path:t}),`/${t}`):(a.Ay.warn("sanitizeUrl could not produce a valid absolute or relative URL",{originalInput:e,finalSanitized:t}),t)}function g(e){if(!e)return;let t=p(e);if(t){if(t.startsWith("http://")||t.startsWith("https://"))return t.replace(/^http:/,"https:");if(l){let r=`${l}${t.startsWith("/")?"":"/"}${t}`;return a.Ay.debug("Constructed OG image URL from relative path",{original:e,final:r}),r.replace(/^http:/,"https:")}if(a.Ay.warn("Could not determine OG image URL confidently",{originalUrl:e,processedUrl:t}),o)return`${o}${t.startsWith("/")?"":"/"}${t}`.replace(/^http:/,"https:")}}"development"===s.NODE_ENV&&a.Ay.debug("Media Utils Initialized:",{EFFECTIVE_STRAPI_URL:o,STRAPI_MEDIA_URL:l,SITE_URL:n})},83997:e=>{"use strict";e.exports=require("tty")},90612:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(60687),s=r(84189),o=r(3832),l=r(43210),n=r(66501);async function i(e,t){try{let r=await fetch("/api/analytics/post-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:e,postSlug:t})});if(!r.ok)throw Error(`Failed to track post view: ${r.statusText}`)}catch(e){n.Ay.error("Error tracking post view:",e)}}let c=({content:e,postId:t,postSlug:r,applyNoFollow:n=!0})=>((0,l.useEffect)(()=>{if(t&&r){let e=setTimeout(()=>{i(t,r)},2e3);return()=>clearTimeout(e)}},[t,r]),(0,a.jsxs)("div",{className:"mb-8",children:[" ",(0,a.jsx)(s.oz,{components:{h1:({node:e,...t})=>(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mt-8 mb-4",...t}),h2:({node:e,...t})=>(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mt-6 mb-3",...t}),h3:({node:e,...t})=>(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mt-5 mb-2",...t}),h4:({node:e,...t})=>(0,a.jsx)("h4",{className:"text-lg font-bold text-gray-800 mt-4 mb-2",...t}),p:({node:e,...t})=>(0,a.jsx)("p",{className:"text-gray-700 mb-4",...t}),a:({node:e,href:t,...r})=>{let s=t&&(t.startsWith("http://")||t.startsWith("https://")),o="";return s&&(n&&(o+="nofollow "),o+="noopener noreferrer"),(0,a.jsx)("a",{className:"text-emerald-600 hover:text-emerald-700 underline",href:t,rel:o.trim()||void 0,target:s?"_blank":void 0,...r})},ul:({node:e,...t})=>(0,a.jsx)("ul",{className:"list-disc pl-6 mb-4",...t}),ol:({node:e,...t})=>(0,a.jsx)("ol",{className:"list-decimal pl-6 mb-4",...t}),li:({node:e,...t})=>(0,a.jsx)("li",{className:"mb-1",...t}),blockquote:({node:e,...t})=>(0,a.jsx)("blockquote",{className:"border-l-4 border-emerald-500 pl-4 italic my-4",...t})},rehypePlugins:[o.A],children:e})]}))},92224:(e,t,r)=>{"use strict";function a(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function s(e,t){if(!e)return"";let r=e.split(" ");return r.length<=t?e:r.slice(0,t).join(" ")+"..."}r.d(t,{Bt:()=>s,Yq:()=>a}),r(66501),r(28136)},94735:e=>{"use strict";e.exports=require("events")},96882:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>i});var a=r(65239),s=r(48088),o=r(31369),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let i={children:["",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11036)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\[slug]\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/blog/[slug]/page",pathname:"/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,1330,3376,6391,2975,5373,8446,270],()=>r(96882));module.exports=a})();