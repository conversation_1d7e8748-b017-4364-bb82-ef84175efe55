(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6279],{973:(e,t,l)=>{"use strict";l.d(t,{default:()=>u});var n=l(5155),i=l(8126),o=l.n(i),a=l(2115),r=l(2112);function u(e){var t,l,i,u,s,d,c,m,p,f,v,b,j,g;let{seo:y,defaultTitle:h="Natural Healing Now - Holistic Health Directory",defaultDescription:x="Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.",defaultOgImage:_="",pageType:w="website"}=e,N=(null==y?void 0:y.metaTitle)||h,k=(null==y?void 0:y.metaDescription)||x,O=(null==y?void 0:y.metaRobots)||"index, follow",P=(null==y?void 0:y.canonicalURL)||"",S=(null==y?void 0:y.structuredData)||"",E=null==y||null==(t=y.metaSocial)?void 0:t.find(e=>"Facebook"===e.socialNetwork),H=null==y||null==(l=y.metaSocial)?void 0:l.find(e=>"Twitter"===e.socialNetwork),M=(null==E||null==(s=E.image)||null==(u=s.data)||null==(i=u.attributes)?void 0:i.url)||(null==y||null==(m=y.metaImage)||null==(c=m.data)||null==(d=c.attributes)?void 0:d.url)||_,R=(0,r.Rb)(M),C=(null==H||null==(v=H.image)||null==(f=v.data)||null==(p=f.attributes)?void 0:p.url)||(null==y||null==(g=y.metaImage)||null==(j=g.data)||null==(b=j.attributes)?void 0:b.url)||_,D=(0,r.Rb)(C),I=(null==E?void 0:E.title)||N,T=(null==E?void 0:E.description)||k,F=(null==H?void 0:H.title)||N,J=(null==H?void 0:H.description)||k,[L,U]=(0,a.useState)(null);return(0,a.useEffect)(()=>{if(S)try{let e=JSON.parse(S);U(e)}catch(e){console.error("Error parsing structured data:",e)}},[S]),(0,n.jsxs)(o(),{children:[(0,n.jsx)("title",{children:N}),(0,n.jsx)("meta",{name:"description",content:k}),O&&(0,n.jsx)("meta",{name:"robots",content:O}),P&&(0,n.jsx)("link",{rel:"canonical",href:P}),(0,n.jsx)("meta",{property:"og:type",content:w}),(0,n.jsx)("meta",{property:"og:title",content:I}),(0,n.jsx)("meta",{property:"og:description",content:T}),R&&(0,n.jsx)("meta",{property:"og:image",content:R}),(0,n.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,n.jsx)("meta",{name:"twitter:title",content:F}),(0,n.jsx)("meta",{name:"twitter:description",content:J}),D&&(0,n.jsx)("meta",{name:"twitter:image",content:D}),L&&(0,n.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(L)}})]})}},7275:(e,t,l)=>{Promise.resolve().then(l.t.bind(l,6874,23)),Promise.resolve().then(l.bind(l,927)),Promise.resolve().then(l.bind(l,973))},8126:(e,t)=>{"use strict";function l(){return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}},e=>{var t=t=>e(e.s=t);e.O(0,[844,6874,3063,6079,2112,927,8441,1684,7358],()=>t(7275)),_N_E=e.O()}]);