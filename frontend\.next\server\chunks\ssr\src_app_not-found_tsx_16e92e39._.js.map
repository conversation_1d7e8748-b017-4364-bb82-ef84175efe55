{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/app/not-found.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { FiAlertCircle, FiHome } from 'react-icons/fi';\n\nexport default function NotFound() {\n  return (\n    <div className=\"container mx-auto px-4 py-16 flex flex-col items-center justify-center min-h-[60vh]\">\n      <div className=\"text-center max-w-md\">\n        <div className=\"flex justify-center mb-6\">\n          <FiAlertCircle className=\"w-16 h-16 text-emerald-600\" />\n        </div>\n        <h1 className=\"text-4xl font-bold mb-4\">404 - Page Not Found</h1>\n        <p className=\"text-gray-600 mb-8\">\n          The page you are looking for doesn't exist or has been moved.\n        </p>\n        <Link \n          href=\"/\" \n          className=\"inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 transition-colors\"\n        >\n          <FiHome className=\"mr-2\" />\n          Back to Homepage\n        </Link>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8IAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;;;;;;8BAE3B,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,8OAAC,8IAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAS;;;;;;;;;;;;;;;;;;AAMrC", "debugId": null}}]}