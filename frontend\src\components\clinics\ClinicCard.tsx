"use client";

import Image from 'next/image';
import Link from 'next/link';
import { FiMapPin, FiPhone, FiGlobe } from 'react-icons/fi';
import { MdVerifiedUser } from "react-icons/md"; // Import the verified icon

interface ClinicCardProps {
  clinic: {
    id: string;
    name: string;
    slug: string;
    isVerified?: boolean;
    description?: string | null;
    logo?: string | null;
    featuredImage?: string | null;
    address: {
      city: string;
      stateProvince: string;
    };
    contactInfo?: {
      phoneNumber?: string;
      websiteUrl?: string;
    } | null;
    // Add all other fields that might be needed for the detail page
    location?: any;
    openingHours?: any;
    services?: any;
    specialties?: any;
    conditions?: any;
    practitioners?: any;
    appointment_options?: any;
    payment_methods?: any;
    seo?: any;
  };
  showContactInfo?: boolean;
  prefetchedData?: boolean; // Flag to indicate if this card has complete data
}

const ClinicCard = ({ clinic, showContactInfo = true, prefetchedData = false }: ClinicCardProps) => {
  // Construct the link with state if we have prefetched data
  const linkHref = prefetchedData 
    ? { 
        pathname: `/clinics/${clinic.slug}`,
        // Pass the clinic data as state to avoid refetching
        query: { prefetched: 'true' } 
      }
    : `/clinics/${clinic.slug}`;
  
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col">
      <div className="p-4 flex-grow">
        <h3 className="text-xl font-semibold text-gray-800 mb-1">
          <Link 
            href={linkHref}
            className="hover:text-emerald-600"
          >
            {clinic.name}
          </Link>
        </h3>
        {/* Verified Badge */}
        {clinic.isVerified && (
          <div className="flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium">
            <MdVerifiedUser color="#009967" size={14} />
            <span>VERIFIED</span>
          </div>
        )}

        {clinic.description && (
          <p className="text-gray-600 mb-4 line-clamp-2">{clinic.description}</p>
        )}
        
        <div className="space-y-2 text-sm text-gray-500">
          {/* Location info */}
          {clinic.address && (
            <div className="flex items-center">
              <FiMapPin className="mr-2 text-emerald-500" />
              <span>{clinic.address.city}, {clinic.address.stateProvince}</span>
            </div>
          )}
          
          {/* Conditionally render contact info */}
          {showContactInfo && clinic.contactInfo?.phoneNumber && (
            <div className="flex items-center">
              <FiPhone className="mr-2 text-emerald-500" />
              <span>{clinic.contactInfo.phoneNumber}</span> 
            </div>
          )}
          
          {showContactInfo && clinic.contactInfo?.websiteUrl && (
            <div className="flex items-center">
              <FiGlobe className="mr-2 text-emerald-500" />
              <a
                href={clinic.contactInfo.websiteUrl}
                target="_blank"
                rel="nofollow noopener noreferrer"
                className="hover:text-emerald-600"
              >
                Visit Website
              </a>
            </div>
          )}
        </div>
      </div>
      
      {/* Ensure footer stays at the bottom */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto"> 
        <Link 
          href={linkHref}
          className="text-emerald-600 hover:text-emerald-700 font-medium text-sm"
        >
          View Details →
        </Link>
      </div>
    </div>
  );
};

export default ClinicCard;
