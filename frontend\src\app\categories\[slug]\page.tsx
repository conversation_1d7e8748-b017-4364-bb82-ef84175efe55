import Link from 'next/link';
import { Fi<PERSON>apPin, FiFilter } from 'react-icons/fi';
import { getStrapiContent } from '@/lib/strapi';
import { notFound } from 'next/navigation';
import ExploreFurther from '@/components/shared/ExploreFurther';
import SearchInput from '@/components/shared/SearchInput';
import MarkdownContent from '@/components/blog/MarkdownContent';
import { Metadata } from 'next';
import TabSwitcher from '@/components/shared/TabSwitcher';
import TabContent from '@/components/shared/TabContent';

// Force static rendering for this route segment
export const dynamic = 'force-static';

// Enable ISR with both time-based and on-demand revalidation
// Use a 12-hour revalidation period
export const revalidate = 43200; // 12 hours in seconds

// Allow dynamic params for categories not generated at build time
export const dynamicParams = true;

// Define a simple type for items with a slug (can be moved to a shared types file)
interface SlugItem {
  slug: string;
}

// Generate static paths for all categories at build time
export async function generateStaticParams() {
  try {
    const response = await getStrapiContent.categories.getAllSlugs({
      cache: 'force-cache',
      next: {
        revalidate: 43200, // Revalidate slugs every 12 hours
        tags: ['strapi-categories-slugs']
      }
    });

    if (response && response.data && Array.isArray(response.data)) {
      console.log(`Pre-rendering ${response.data.length} category detail pages`);

      return response.data
        .filter((item: SlugItem | null): item is SlugItem => item !== null && typeof item.slug === 'string')
        .map((item: SlugItem) => ({
          slug: item.slug,
        }));
    }

    return [];
  } catch (error) {
    console.error('Error fetching category slugs for generateStaticParams:', error);
    return []; // Return empty array on error to prevent build failure
  }
}


const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL;

// Helper to create absolute URL for Strapi assets - enhanced for Strapi v5 and better debugging
const getStrapiMediaUrl = (mediaObject: any): string | null => {
  // console.log("getStrapiMediaUrl input:", JSON.stringify(mediaObject, null, 2)); // Keep commented unless deep debugging needed

  // If null/undefined, return null
  if (!mediaObject) return null;

  // If it's already a string URL (less likely for populated media, but check)
  if (typeof mediaObject === 'string') {
    // console.log("Input is a string:", mediaObject);
    if (mediaObject.startsWith('http://') || mediaObject.startsWith('https://')) {
      return mediaObject;
    }
    // Assume it's a relative path if it's a string but not absolute
    return STRAPI_URL ? `${STRAPI_URL}${mediaObject}` : null;
  }

  // Handle Strapi v4/v5 media object structures
  if (mediaObject && typeof mediaObject === 'object') {
    // console.log("Input is an object:", mediaObject);

    // Most common pattern for populated media (v4/v5): object.data.attributes.url
    let url = mediaObject.data?.attributes?.url;

    // Fallback: Direct url property on the object itself (less common for populated)
    if (!url) {
      url = mediaObject.url;
    }

    // Fallback: Direct url property within a 'data' object (less common)
    if (!url) {
      url = mediaObject.data?.url;
    }

    // If we found a URL string
    if (typeof url === 'string') {
      // console.log("Extracted URL:", url);
      // Check if it's already absolute
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      // Otherwise, prepend Strapi URL
      return `${STRAPI_URL}${url}`;
    }
  }

  console.warn("Could not extract URL from media object:", mediaObject);
  return null;
};

// --- Type Definitions ---

// Raw Strapi types (adjust based on actual API response)
interface RawStrapiMedia {
  url?: string;
  // Add other potential media fields if needed (e.g., alternativeText)
}

// Reusable Address type
interface RawStrapiAddress {
  city: string;
  stateProvince: string;
  // Add other address fields if needed
}

interface RawStrapiContactInfo {
  phoneNumber?: string;
  websiteUrl?: string;
}

interface RawStrapiClinic {
  id: string;
  documentId: string;
  name: string;
  slug: string;
  description?: string | null;
  logo?: RawStrapiMedia | null;
  featuredImage?: RawStrapiMedia | null;
  address?: RawStrapiAddress | null;
  contactInfo?: RawStrapiContactInfo | null;
  isVerified?: boolean; // Add isVerified
}

interface RawStrapiPractitioner {
  id: string;
  documentId: string;
  name: string;
  slug: string;
  title?: string | null;
  qualifications?: string | null;
  profilePicture?: RawStrapiMedia | null; // Assuming a profile picture field
  address?: RawStrapiAddress | null; // Add address field
  isVerified?: boolean; // Add isVerified
  bio?: string | null; // Add bio field
  // Add other practitioner fields if needed
}

// Define the structure for the nested Open Graph component
interface RawStrapiOpenGraph {
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: RawStrapiMedia | null;
  ogUrl?: string;
  ogType?: string;
}

// Define a basic structure for the SEO component data, including the nested openGraph component
interface RawStrapiSeo {
  metaTitle?: string;
  metaDescription?: string;
  metaImage?: RawStrapiMedia | null;
  openGraph?: RawStrapiOpenGraph | null; // Nested Open Graph component
  // Add other SEO fields if they exist (e.g., keywords, structuredData)
}


interface RawStrapiCategory {
  id: string;
  documentId: string;
  name: string;
  slug: string;
  description?: string | null;
  icon?: RawStrapiMedia | null;
  featuredImage?: RawStrapiMedia | null;
  clinics?: RawStrapiClinic[]; // Direct array in v5
  practitioners?: RawStrapiPractitioner[]; // Direct array in v5
  relatedConditions?: string[] | null; // Assuming this remains simple
  contentBottomCategory?: string | null; // <-- ADD THIS LINE
  seo?: RawStrapiSeo | null; // Add the SEO field
}

// Transformed types for components
interface TransformedClinic {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  logo: string | null;
  featuredImage: string | null;
  address: {
    city: string;
    stateProvince: string;
  };
  contactInfo?: {
    phoneNumber?: string;
    websiteUrl?: string;
  } | null;
  isVerified?: boolean; // Add isVerified
}

interface TransformedPractitioner {
  id: string;
  name: string;
  slug: string;
  title?: string | null;
  qualifications?: string | null;
  profilePicture: string | null;
  isVerified?: boolean; // Add isVerified
  bio?: string | null; // Add bio field
  // Removed address field
}

// Define the possible tab types to avoid TypeScript comparison errors
type TabType = 'clinics' | 'practitioners';

// --- Data Fetching & Transformation ---

// Get category data with optimized caching
async function getCategoryData(slug: string): Promise<RawStrapiCategory | null> {
  try {
    // Use the optimized getBySlug function with proper cache tags
    // The getBySlug in strapi.ts should handle appropriate next options or rely on page-level revalidate
    const response = await getStrapiContent.categories.getBySlug(slug, {
      next: {
        tags: ['strapi-categories-list', `strapi-category-${slug}`], // Ensure these tags are used
        // revalidate: 43200 // This will be inherited from page-level revalidate
      },
      cache: 'force-cache' // Opt-in to caching
    });

    // Strapi v5 returns data directly in an array when filtering
    if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
      return response.data[0] as RawStrapiCategory;
    }
    return null;
  } catch (error) {
    console.error(`Error fetching category with slug ${slug}:`, error);
    return null;
  }
}

function transformClinicData(rawClinic: RawStrapiClinic): TransformedClinic | null {
  if (!rawClinic || !rawClinic.id || !rawClinic.name) {
    return null;
  }

  // Process image URLs - Pass the whole object to the helper
  const logoUrl = getStrapiMediaUrl(rawClinic.logo);
  const featuredImageUrl = getStrapiMediaUrl(rawClinic.featuredImage);

  return {
    id: String(rawClinic.id),
    name: rawClinic.name,
    slug: rawClinic.slug || `clinic-${rawClinic.id}`,
    description: rawClinic.description,
    logo: logoUrl,
    featuredImage: featuredImageUrl,
    address: rawClinic.address || { city: 'Unknown', stateProvince: 'N/A' },
    contactInfo: rawClinic.contactInfo,
    isVerified: rawClinic.isVerified || false, // Pass through isVerified
  };
}

function transformPractitionerData(rawPractitioner: RawStrapiPractitioner): TransformedPractitioner | null {
  if (!rawPractitioner || !rawPractitioner.id || !rawPractitioner.name) {
    return null;
  }

  return {
    id: String(rawPractitioner.id),
    name: rawPractitioner.name,
    slug: rawPractitioner.slug || `practitioner-${rawPractitioner.id}`,
    title: rawPractitioner.title,
    qualifications: rawPractitioner.qualifications,
    profilePicture: getStrapiMediaUrl(rawPractitioner.profilePicture),
    isVerified: rawPractitioner.isVerified || false, // Pass through isVerified
    bio: rawPractitioner.bio, // Add bio field
  };
}

// --- Page Component ---

interface PageParams {
  slug: string;
}

// Define the site URL from environment variable with proper fallback to your actual domain
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Site URL for canonical URLs: ${SITE_URL}

// Generate Metadata
export async function generateMetadata({ params }: { params: PageParams }): Promise<Metadata> {
  const awaitedParams = await params;
  const categoryData = await getCategoryData(awaitedParams.slug); // Renamed for clarity

  if (!categoryData) {
    return {
      title: 'Category Not Found | Natural Healing Now',
      description: 'The requested category could not be found.',
      // No canonical for not found pages
    };
  }

  // Extract SEO data directly from categoryData, now that the type includes it
  const seo = categoryData.seo;
  // Use direct properties from categoryData for fallbacks as per RawStrapiCategory type
  const defaultTitle = `${categoryData.name || 'Category'} | Natural Healing Now`;
  const defaultDescription = categoryData.description || `Learn about ${categoryData.name || 'this category'} and find related clinics and practitioners.`;

  const metaTitle = seo?.metaTitle || defaultTitle;
  const metaDescription = seo?.metaDescription || defaultDescription;
  const metaImageUrl = getStrapiMediaUrl(seo?.metaImage); // Use helper for image URL

  const canonicalPath = `/categories/${categoryData.slug}`; // Use slug from original data object
  const canonicalUrl = SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath;

  // Prepare Open Graph data - Access fields from the nested seo.openGraph component
  const ogTitle = seo?.openGraph?.ogTitle || metaTitle; // Access via seo.openGraph
  const ogDescription = seo?.openGraph?.ogDescription || metaDescription; // Access via seo.openGraph
  const ogImageUrl = getStrapiMediaUrl(seo?.openGraph?.ogImage) || metaImageUrl; // Access via seo.openGraph
  const ogUrl = seo?.openGraph?.ogUrl || canonicalUrl; // Access via seo.openGraph
  const ogTypeInput = seo?.openGraph?.ogType || 'website'; // Access via seo.openGraph
  const allowedOgTypes = ['website', 'article', 'book', 'profile'];
  // Validate the input type and default to 'website' if it's not one of the allowed literals
  const finalOgType = allowedOgTypes.includes(ogTypeInput) ? ogTypeInput as 'website' | 'article' | 'book' | 'profile' : 'website';


  // Construct the openGraph object carefully, adhering to the Metadata['openGraph'] type
  const openGraphData: Metadata['openGraph'] = {
    title: ogTitle,
    description: ogDescription,
    url: ogUrl,
    type: finalOgType, // Use the validated and typed value
    siteName: 'Natural Healing Now', // Add site name
    images: ogImageUrl ? [{ url: ogImageUrl }] : undefined, // Set images only if URL exists
  };

  return {
    title: metaTitle,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: openGraphData,
    // Add Twitter card data if desired
    // twitter: { ... },
  };
}

// Define the interface for the searchParams object, adding query and page
interface CategoryPageSearchParams {
  tab?: string;
  query?: string;
  page?: string;
}

// Main Page Component
export default async function CategoryDetailPage({
  params,
  searchParams
}: {
  params: PageParams;
  searchParams: CategoryPageSearchParams; // Use updated interface
}) {
  const awaitedParams = await params;
  const slug = awaitedParams.slug;

  const awaitedSearchParams = await searchParams; // Await searchParams
  const query = awaitedSearchParams?.query || '';
  const location = awaitedSearchParams?.location || ''; // Get location from search params
  const currentPage = Number(awaitedSearchParams?.page) || 1;
  const rawTabValue = awaitedSearchParams?.tab || 'clinics';
  const activeTab: TabType = (rawTabValue === 'practitioners') ? 'practitioners' : 'clinics';



  const category = await getCategoryData(slug);

  if (!category) {
    notFound();
  }

  const {
    name = 'Unnamed Category',
    description = 'No description available.',
    icon = null,
    featuredImage = null,
    relatedConditions = null,
    contentBottomCategory
  } = category;

  const categoryIconUrl = getStrapiMediaUrl(icon); // Pass the whole icon object
  const categoryImageUrl = getStrapiMediaUrl(featuredImage); // Pass the whole featuredImage object

  let clinicCount = 0;
  let practitionerCount = 0;
  let totalPages = 1;

  // Determine caching strategy based on search parameters
  const hasFilters = query || location; // Include location in filter detection
  const fetchOptions = hasFilters
    ? { cache: 'no-store' } // Don't cache filtered results
    : {
        next: {
          tags: ['strapi-categories-list', `strapi-category-${slug}`], // General tags for category data
          revalidate: 43200 // Inherits from page
        },
        cache: 'force-cache'
      };

  // Fetch counts first - include filters if they exist
  try {
    const clinicCountResponse = await getStrapiContent.clinics.getAll({
        categorySlug: slug,
        query: hasFilters ? query : '', // Include query filter for count if filters are applied
        location: hasFilters ? location : '', // Include location filter for count if filters are applied
        page: 1, // For count, page 1 is enough
        pageSize: 1, // We only need the meta for total count
        ...fetchOptions // Apply the same caching strategy
    });
    clinicCount = clinicCountResponse?.meta?.pagination?.total || 0;

    const practitionerCountParams = {
        categorySlug: slug,
        query: hasFilters ? query : '', // Include query filter for count if filters are applied
        location: hasFilters ? location : '', // Include location filter for count if filters are applied
        page: 1,
        pageSize: 1,
        ...fetchOptions // Apply the same caching strategy
    };
    console.log(`[DEBUG] Practitioner count params:`, JSON.stringify(practitionerCountParams, null, 2));

    const practitionerCountResponse = await getStrapiContent.practitioners.getAll(practitionerCountParams);
    practitionerCount = practitionerCountResponse?.meta?.pagination?.total || 0;
    console.log(`[DEBUG] Counts for category ${slug} (hasFilters: ${hasFilters}): Clinics=${clinicCount}, Practitioners=${practitionerCount}`);
  } catch (error) {
    console.error("Error fetching counts for category page:", error);
  }

  // Fetch clinics data
  let clinics: TransformedClinic[] = [];
  let clinicPages = 1;
  try {
    console.log(`[DEBUG] About to fetch clinics for category ${slug}, activeTab: ${activeTab}, query: ${query}, location: ${location}, currentPage: ${currentPage}`);

    const clinicParams = {
      categorySlug: slug,
      query: activeTab === 'clinics' ? query : '', // Only apply query for active tab
      location: activeTab === 'clinics' ? location : '', // Only apply location for active tab
      page: activeTab === 'clinics' ? currentPage : 1, // Use page 1 for inactive tab
      pageSize: 12,
      ...fetchOptions // Use the same approach as specialities page
    };

    console.log(`[DEBUG] Clinic API call params:`, JSON.stringify(clinicParams, null, 2));

    const clinicResponse = await getStrapiContent.clinics.getAll(clinicParams);

    console.log(`Clinic response data length: ${clinicResponse?.data?.length || 0}`);
    clinics = (clinicResponse?.data || [])
      .map((item: any) => transformClinicData(item.attributes || item))
      .filter(Boolean as unknown as (value: TransformedClinic | null) => value is TransformedClinic);
    console.log(`Transformed clinics array length: ${clinics.length}`);

    clinicPages = clinicResponse?.meta?.pagination?.pageCount || 1;
  } catch (error) {
    console.error(`Error fetching clinics for category ${slug}:`, error);
  }

  // Fetch practitioners data
  let practitioners: TransformedPractitioner[] = [];
  let practitionerPages = 1;
  try {
    console.log(`[DEBUG] About to fetch practitioners for category ${slug}, activeTab: ${activeTab}, query: ${query}, location: ${location}, currentPage: ${currentPage}`);

    const practitionerParams = {
      categorySlug: slug,
      query: activeTab === 'practitioners' ? query : '', // Only apply query for active tab
      location: activeTab === 'practitioners' ? location : '', // Only apply location for active tab
      page: activeTab === 'practitioners' ? currentPage : 1, // Use page 1 for inactive tab
      pageSize: 12,
      ...fetchOptions // Use the same approach as specialities page
    };

    console.log(`[DEBUG] Practitioner API call params:`, JSON.stringify(practitionerParams, null, 2));

    const practitionerResponse = await getStrapiContent.practitioners.getAll(practitionerParams);

    console.log(`Practitioner raw response data length: ${practitionerResponse?.data?.length || 0}`);
    if (practitionerResponse?.data && practitionerResponse.data.length > 0) {
      console.log(`First raw practitioner item structure:`, JSON.stringify({
        id: practitionerResponse.data[0].id,
        hasAttributes: !!practitionerResponse.data[0].attributes,
        attributesType: practitionerResponse.data[0].attributes ? typeof practitionerResponse.data[0].attributes : 'N/A',
        keys: Object.keys(practitionerResponse.data[0]),
        attributeKeys: practitionerResponse.data[0].attributes ? Object.keys(practitionerResponse.data[0].attributes) : []
      }, null, 2));
    }

    practitioners = (practitionerResponse?.data || []).map((item: any) => {
      const practitionerRawData = item.attributes || item;
      // console.log(`Processing practitioner: ${practitionerRawData.name || 'unnamed'}, ID: ${practitionerRawData.id || 'no-id'}`);
      if (!practitionerRawData.id || !practitionerRawData.name) {
        console.warn(`Practitioner data missing id or name. ID: ${practitionerRawData.id}, Name: ${practitionerRawData.name}. Full item:`, JSON.stringify(item));
      }
      return transformPractitionerData(practitionerRawData);
    }).filter((p: TransformedPractitioner | null): p is TransformedPractitioner => {
      if (p === null) {
        // console.warn("A practitioner was filtered out by transformPractitionerData returning null.");
      }
      return p !== null;
    });

    console.log(`Transformed practitioners array length: ${practitioners.length}`);
    practitionerPages = practitionerResponse?.meta?.pagination?.pageCount || 1;
  } catch (error) {
    console.error(`Error fetching practitioners for category ${slug}:`, error);
  }

  totalPages = activeTab === 'clinics' ? clinicPages : practitionerPages;

  const conditions = relatedConditions ? [...relatedConditions] : [];

  // No longer need the isTabActive function as it's handled by the client components

  return (
    <>
      {/* Breadcrumb */}
      <div className="bg-gray-100 py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-emerald-600">Home</Link>
            <span className="mx-2">/</span>
            <Link href="/categories" className="hover:text-emerald-600">Categories</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-800">{name}</span>
          </div>
        </div>
      </div>

      {/* Category Header */}
      <div className="bg-emerald-600 text-white py-12 relative overflow-hidden">
        {categoryImageUrl && (
          <div
            className="absolute inset-0 opacity-20 bg-cover bg-center"
            style={{ backgroundImage: `url(${categoryImageUrl})` }}
          >
            {/* Fallback div with background image instead of Next.js Image component */}
          </div>
        )}
        <div className="container mx-auto px-4 relative z-10">
          <div className="flex items-center mb-4">
            {categoryIconUrl && (
              <div className="relative h-12 w-12 mr-4 bg-white rounded-full p-1 flex-shrink-0">
                <div
                  className="absolute inset-0 rounded-full bg-contain bg-center bg-no-repeat"
                  style={{ backgroundImage: `url(${categoryIconUrl})` }}
                >
                  {/* Fallback div with background image */}
                </div>
              </div>
            )}
            <h1 className="text-3xl md:text-4xl font-bold">{name}</h1>
          </div>
          {description && <p className="text-lg max-w-3xl mb-4">{description}</p>}
          <div className="flex gap-4 text-sm">
            <span>{clinicCount} Clinics</span>
            <span>&bull;</span>
            <span>{practitionerCount} Practitioners</span>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="bg-white shadow-sm sticky top-0 z-20"> {/* Make filters sticky */}
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
               <SearchInput placeholder={`Search within ${name}...`} paramName="query" />
            </div>
             {/* Location Input - Replaced with SearchInput */}
            <div className="flex-1">
               <SearchInput placeholder="City, state, or zip code" paramName="location" icon={<FiMapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />} />
            </div>
            <div>
              <button className="w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200">
                <FiFilter />
                <span>Filters</span>
              </button>
            </div>
          </div>
          {/* Placeholder for active filters */}
          {/* <div className="flex flex-wrap gap-2 mt-3"> ... </div> */}
        </div>
      </div>

      {/* Tabs & Results Section - Using client-side components */}
      <div className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          {/* Use the client-side TabSwitcher component */}
          <TabSwitcher
            slug={awaitedParams.slug}
            pageType="categories"
            clinicCount={clinicCount}
            practitionerCount={practitionerCount}
            initialTab={activeTab}
          />

          {/* Use the client-side TabContent component */}
          <TabContent
            clinics={clinics}
            practitioners={practitioners}
            totalPages={totalPages}
            initialTab={activeTab}
          />
        </div>
      </div>

      {/* Related Conditions Section */}
      {conditions.length > 0 && (
        <div className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">
              Common Conditions Addressed by {name}
            </h2>
            <div className="flex flex-wrap gap-2">
              {conditions.map((condition, index) => (
                <span
                  key={index}
                  className="bg-emerald-50 text-emerald-700 px-3 py-1 rounded-full text-sm"
                >
                  {condition}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Add Content Bottom Section */}
      {contentBottomCategory && (
        <div className="py-12 bg-white border-t border-gray-200 flex justify-center"> {/* Added flex justify-center back */}
          <div className="w-full max-w-4xl px-4 prose lg:prose-lg"> {/* Removed container, mx-auto, max-w-none; Added w-full, max-w-4xl */}
            <MarkdownContent content={contentBottomCategory} />
          </div>
        </div>
      )}

      {/* Replace Call to Action with ExploreFurther component */}
      <ExploreFurther />
    </>
  );
}
