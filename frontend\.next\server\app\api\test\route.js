(()=>{var e={};e.id=2450,e.ids=[2450],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},20381:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>p,serverHooks:()=>c,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>i});var n=r(96559),o=r(48088),a=r(37719);async function i(e){return new Response(JSON.stringify({message:"API route is working correctly",timestamp:new Date().toISOString()}),{headers:{"Content-Type":"application/json"}})}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/test/route",pathname:"/api/test",filename:"route",bundlePath:"app/api/test/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\test\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:c}=p;function l(){return(0,a.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719],()=>r(20381));module.exports=s})();