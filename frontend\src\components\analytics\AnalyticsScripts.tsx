'use client';

import { useEffect } from 'react';
import { registerScript, ScriptStrategy } from '@/lib/scriptManager';

interface AnalyticsScriptsProps {
  /**
   * Google Analytics measurement ID (e.g., G-XXXXXXXXXX)
   */
  googleAnalyticsId?: string;
  
  /**
   * Whether to enable analytics in development mode
   */
  enableInDevelopment?: boolean;
}

/**
 * Component to register analytics scripts
 * 
 * This component doesn't render anything, it just registers scripts
 * to be loaded by the OptimizedScripts component.
 */
export default function AnalyticsScripts({
  googleAnalyticsId,
  enableInDevelopment = false,
}: AnalyticsScriptsProps) {
  useEffect(() => {
    // Skip in development mode unless explicitly enabled
    if (process.env.NODE_ENV === 'development' && !enableInDevelopment) {
      return;
    }
    
    // Register Google Analytics if ID is provided
    if (googleAnalyticsId) {
      // Register the GA script
      registerScript({
        id: 'google-analytics',
        src: `https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`,
        strategy: ScriptStrategy.AFTER_INTERACTIVE,
        attributes: {
          async: 'true',
        },
      });
      
      // Register the GA initialization script
      registerScript({
        id: 'google-analytics-init',
        content: `
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${googleAnalyticsId}', {
            page_path: window.location.pathname,
          });
        `,
        strategy: ScriptStrategy.AFTER_INTERACTIVE,
      });
    }
    
    // Add more analytics scripts as needed
    
  }, [googleAnalyticsId, enableInDevelopment]);
  
  // This component doesn't render anything
  return null;
}
