exports.id=8137,exports.ids=[8137],exports.modules={5409:(e,t,r)=>{"use strict";r.d(t,{po:()=>c,cX:()=>l});var a=r(61120);let i="https://nice-badge-2130241d6c.strapiapp.com",n=process.env.STRAPI_API_TOKEN;function o(e,t,r,a={}){return{data:null,error:{status:e,name:t,message:r,details:a}}}async function s(e,t={},r={}){let a=function(e){let t=new URLSearchParams;if(e.filters&&Object.entries(e.filters).forEach(([e,r])=>{"object"==typeof r&&null!==r?Object.entries(r).forEach(([r,a])=>{t.append(`filters[${e}][${r}]`,String(a))}):t.append(`filters[${e}]`,String(r))}),e.populate)if("string"==typeof e.populate)t.append("populate",e.populate);else if(Array.isArray(e.populate))e.populate.forEach((e,r)=>{t.append(`populate[${r}]`,e)});else{let r=(e,a="populate")=>{let i=[];return Object.entries(e).forEach(([e,n])=>{let o=a?`${a}[${e}]`:e;"object"!=typeof n||null===n||Array.isArray(n)?Array.isArray(n)?n.forEach((e,r)=>t.append(`${o}[${r}]`,String(e))):!0===n?t.append(o,"*"):n&&t.append(o,String(n)):n.hasOwnProperty("populate")||n.hasOwnProperty("fields")||n.hasOwnProperty("filters")||n.hasOwnProperty("sort")?i=i.concat(r(n,o)):t.append(o,"*")}),i};"object"!=typeof e.populate||Array.isArray(e.populate)||r(e.populate)}return e.fields&&e.fields.forEach((e,r)=>{t.append(`fields[${r}]`,e)}),e.sort&&e.sort.forEach((e,r)=>{t.append(`sort[${r}]`,e)}),e.pagination&&"object"==typeof e.pagination&&(void 0!==e.pagination.page&&t.append("pagination[page]",String(e.pagination.page)),void 0!==e.pagination.pageSize&&t.append("pagination[pageSize]",String(e.pagination.pageSize)),void 0!==e.pagination.withCount&&t.append("pagination[withCount]",String(e.pagination.withCount))),t}(t),c=`${i}/api/${e}${a.toString()?`?${a.toString()}`:""}`,l={"Content-Type":"application/json",...n&&{Authorization:`Bearer ${n}`},...r.headers},p={method:r.method||"GET",headers:l,cache:r.cache||(r.next?.revalidate!==void 0?"force-cache":"no-store"),next:{revalidate:r.next?.revalidate??43200,...r.next?.tags&&{tags:r.next.tags}},...r.body&&{body:r.body}};r.next?.revalidate===!1&&(p.cache="no-store",p.next={...p.next,revalidate:0});try{let e=await fetch(c,p);if(!e.ok){let t=await e.json().catch(()=>({message:e.statusText}));return console.error(`Strapi API Error (${e.status}) for ${c}:`,t),o(e.status,"StrapiApiError",t.error?.message||t.message||e.statusText,t.error?.details||t.details||t)}return await e.json()}catch(e){return console.error(`Network or other error fetching from Strapi for ${c}:`,e),o(500,"NetworkError",e instanceof Error?e.message:"An unknown network error occurred",e)}}i||console.error("CRITICAL: NEXT_PUBLIC_STRAPI_API_URL is not defined. Please set it in your environment variables.");let c=(0,a.cache)(async e=>{let t=await s("clinics",{filters:{slug:{$eq:e}},populate:{logo:{fields:["url","alternativeText","width","height"]},address:{fields:["street","city","state","zipCode","country"]},contactInfo:{fields:["phone","email","website"]},location:{fields:["latitude","longitude","mapLink"]},services:{fields:["name","slug"]},specialties:{fields:["name","slug"]},conditions:{fields:["name","slug"]},categories:{fields:["name","slug"]},practitioners:{fields:["name","slug","title"],populate:{profilePicture:{fields:["url","alternativeText","width","height"]},specialties:{fields:["name","slug"]}}},appointment_options:{fields:["name","description"]},payment_methods:{fields:["name"]},seo:{populate:{metaImage:{fields:["url","alternativeText"]}}}},fields:["name","slug","description","rating","publishedAt"]},{next:{tags:[`strapi-clinic-${e}`,"strapi-clinics"],revalidate:1800}});return t.error||!t.data||0===t.data.length?t.error?{data:null,error:t.error}:o(404,"NotFound","Clinic not found"):{data:t.data[0],meta:t.meta}}),l=(0,a.cache)(async(e=1,t=10,r,a,i,n)=>{let o={};r&&(o.categories={slug:{$eq:r}}),a&&(o.specialties={slug:{$eq:a}}),i&&(o.conditions={slug:{$eq:i}}),n&&(o.name={$containsi:n});let c=["strapi-clinics",...r?[`strapi-clinics-category-${r}`]:[],...a?[`strapi-clinics-specialty-${a}`]:[],...i?[`strapi-clinics-condition-${i}`]:[],`strapi-clinics-page-${e}`];return s("clinics",{filters:o,populate:{logo:{fields:["url","alternativeText","width","height"]},location:{fields:["city","state"]},specialties:{fields:["name","slug"]},categories:{fields:["name","slug"]}},fields:["name","slug","description","rating","publishedAt"],sort:["name:asc"],pagination:{page:e,pageSize:t,withCount:!0}},{next:{tags:c,revalidate:1800}})});(0,a.cache)(async(e=5)=>s("clinics",{filters:{isFeatured:{$eq:!0}},populate:{logo:{fields:["url","alternativeText"]},categories:{fields:["name","slug"]}},fields:["name","slug","description"],pagination:{pageSize:e,page:1},sort:["rating:desc","name:asc"]},{next:{tags:["strapi-featured-clinics","strapi-clinics"],revalidate:3600}}))},8719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return s},throwWithStaticGenerationBailoutError:function(){return n},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return o}});let a=r(80023),i=r(3295);function n(e,t){throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function o(e,t){throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function s(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function c(){let e=i.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},43763:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let a=Reflect.get(e,t,r);return"function"==typeof a?a.bind(e):a}static set(e,t,r,a){return Reflect.set(e,t,r,a)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return a},wellKnownProperties:function(){return n}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function a(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function i(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let n=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},78335:()=>{},96487:()=>{}};