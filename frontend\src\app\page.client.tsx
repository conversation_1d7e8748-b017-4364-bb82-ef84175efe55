"use client";

import { useEffect, useState } from 'react';
import ClinicCard from '@/components/clinics/ClinicCard';
import PractitionerCard from '@/components/practitioners/PractitionerCard';
import CategoryCard from '@/components/categories/CategoryCard';
import BlogPostCard from '@/components/blog/BlogPostCard';
import FeaturedPost from '@/components/blog/FeaturedPost';
import PopularPosts from '@/components/blog/PopularPosts';
import Link from 'next/link';
import SearchInputCallback from '@/components/shared/SearchInputCallback';
import LazyLoadedSection from '@/components/shared/LazyLoadedSection';
import { mapBlogPostData, BlogPost } from '@/lib/dataMappers';

// Helper function to process image URLs
const processImageUrl = (imageUrl: string) => {
  const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || '';

  // Check if this is a Strapi Cloud URL (contains media.strapiapp.com)
  if (imageUrl.includes('media.strapiapp.com')) {
    // Ensure it has https:// prefix
    return imageUrl.startsWith('http') ? imageUrl : `https://${imageUrl}`;
  } else if (imageUrl.startsWith('http')) {
    // Already an absolute URL
    return imageUrl;
  } else {
    // Relative URL, prepend the Strapi API URL
    return `${strapiBaseUrl}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`;
  }
};

// Define the type for the initial data passed from the server component
type HomePageProps = {
  initialData?: {
    clinics: any[];
    practitioners: any[];
    categories: any[];
    featuredPosts: BlogPost[];
    latestPosts: BlogPost[];
    popularPosts: BlogPost[];
  };
};

export default function Home({ initialData }: HomePageProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(!initialData);

  // Use state to store the data
  const [clinics, setClinics] = useState<any[]>(initialData?.clinics || []);
  const [practitioners, setPractitioners] = useState<any[]>(initialData?.practitioners || []);
  const [categories, setCategories] = useState<any[]>(initialData?.categories || []);
  const [featuredPost, setFeaturedPost] = useState<BlogPost | null>(
    initialData?.featuredPosts?.[0] || null
  );
  const [latestPosts, setLatestPosts] = useState<BlogPost[]>(
    initialData?.latestPosts || []
  );
  const [popularPosts, setPopularPosts] = useState<BlogPost[]>(
    initialData?.popularPosts || []
  );

  // Only fetch data client-side if we don't have initial data from the server
  useEffect(() => {
    // If we already have data from the server, don't fetch again
    if (initialData) {
      return;
    }

    // Set loading state
    setIsLoading(true);

    // Fetch all data in parallel
    Promise.all([
      fetch('/api/clinics?filters[isFeatured][$eq]=true&populate=*').then(res => res.json()),
      fetch('/api/practitioners?filters[isFeatured][$eq]=true&populate=*').then(res => res.json()),
      fetch('/api/categories?pagination[pageSize]=4&populate=*').then(res => res.json()),
      fetch('/api/blog-posts?sort=publishDate:desc&pagination[pageSize]=8&populate[featuredImage]=true&populate[author_blogs][populate][profilePicture]=true').then(res => res.json())
    ])
      .then(([clinicsData, practitionersData, categoriesData, blogPostsData]) => {
        // Process the data
        setClinics(clinicsData?.data || []);
        setPractitioners(practitionersData?.data || []);
        setCategories(categoriesData?.data || []);

        // Process blog posts
        const allBlogPosts = blogPostsData?.data || [];

        // Extract featured posts
        const featuredPosts = allBlogPosts
          .filter((post: any) => post.isFeatured)
          .slice(0, 1)
          .map(mapBlogPostData);

        // Extract latest posts
        const latestPosts = allBlogPosts
          .slice(0, 4)
          .map(mapBlogPostData)
          .filter((post: BlogPost) => post !== null);

        // Extract popular posts
        const popularPosts = allBlogPosts
          .slice(0, 4)
          .map(mapBlogPostData)
          .filter((post: BlogPost) => post !== null);

        // Set the state
        setFeaturedPost(featuredPosts[0] || latestPosts[0] || null);
        setLatestPosts(latestPosts);
        setPopularPosts(popularPosts);
        setError(null);
      })
      .catch(err => {
        console.error('Error fetching homepage data:', err);
        setError('Failed to load some data. Please try again later.');
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [initialData]);

  // Handle search submission
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // For now, we'll just redirect to the search results page
    if (query.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
  };

  return (
    <>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Find Natural Healing Solutions Near You
            </h1>
            <p className="text-xl mb-8">
              Connect with holistic health practitioners and clinics to support your wellness journey.
            </p>

            {/* Search Bar */}
            <div className="mb-8 max-w-2xl mx-auto">
              <SearchInputCallback
                placeholder="Search for clinics, practitioners, or health topics..."
                onSearch={handleSearch}
                buttonText="Search"
                buttonClassName="bg-emerald-800 text-white hover:bg-emerald-900"
                className="shadow-lg"
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/clinics"
                className="bg-white text-emerald-600 hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold text-center"
              >
                Find a Clinic
              </Link>
              <Link
                href="/practitioners"
                className="bg-emerald-700 hover:bg-emerald-800 text-white px-6 py-3 rounded-lg font-semibold text-center"
              >
                Find a Practitioner
              </Link>
              <Link
                href="/blog"
                className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 rounded-lg font-semibold text-center"
              >
                Read Our Blog
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <LazyLoadedSection
        threshold={0.1}
        rootMargin="200px"
        placeholder={
          <section className="py-12 bg-gray-50">
            <div className="container mx-auto px-4">
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800">Explore Categories</h2>
                <div className="w-32 h-6 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {Array(4).fill(0).map((_, index) => (
                  <div key={index} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                ))}
              </div>
            </div>
          </section>
        }
      >
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800">Explore Categories</h2>
              <Link
                href="/categories"
                className="text-emerald-600 hover:text-emerald-700 font-medium"
              >
                View All Categories
              </Link>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {isLoading ? (
                // Loading state
                Array(4).fill(0).map((_, index) => (
                  <div key={index} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                ))
              ) : (
                // Real or fallback data
                categories
                  .filter(category => !!category?.id) // Filter out invalid categories
                  .map(category => (
                    <CategoryCard
                      key={category.id}
                      category={{
                        id: category.id,
                        name: category.name || 'Unnamed Category',
                        slug: category.slug || `category-${category.id}`,
                        description: category.description,
                        // Process icon URL
                        icon: category.icon?.url
                              ? processImageUrl(category.icon.url)
                              : null,
                        // Process featured image URL
                        featured_image: category.featuredImage?.url
                                       ? processImageUrl(category.featuredImage.url)
                                       : null
                      }}
                    />
                  ))
              )}
            </div>
          </div>
        </section>
      </LazyLoadedSection>

      {/* Featured Blog Post Section */}
      {featuredPost && !isLoading && (
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800">Featured Article</h2>
              <Link
                href="/blog"
                className="text-emerald-600 hover:text-emerald-700 font-medium"
              >
                View All Articles
              </Link>
            </div>

            <FeaturedPost
              post={featuredPost}
              badgeType={featuredPost.isFeatured ? 'featured' : 'recent'}
            />
          </div>
        </section>
      )}



      {/* Featured Clinics Section */}
      <LazyLoadedSection
        threshold={0.1}
        rootMargin="200px"
        placeholder={
          <section className="py-16">
            <div className="container mx-auto px-4">
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800">Featured Clinics</h2>
                <div className="w-32 h-6 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array(3).fill(0).map((_, index) => (
                  <div key={index} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                ))}
              </div>
            </div>
          </section>
        }
      >
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800">Featured Clinics</h2>
              <Link
                href="/clinics"
                className="text-emerald-600 hover:text-emerald-700 font-medium"
              >
                View All Clinics
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {isLoading ? (
                // Loading state
                Array(3).fill(0).map((_, index) => (
                  <div key={index} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                ))
              ) : clinics.length > 0 ? (
                // Real data
                clinics.slice(0, 3).map((clinic) => (
                  <ClinicCard key={clinic.id} clinic={clinic} />
                ))
              ) : (
                // Fallback message
                <div className="col-span-3 text-center py-8">
                  <p className="text-gray-500">No featured clinics available at the moment.</p>
                </div>
              )}
            </div>
          </div>
        </section>
      </LazyLoadedSection>

      {/* Featured Practitioners Section */}
      <LazyLoadedSection
        threshold={0.1}
        rootMargin="200px"
        placeholder={
          <section className="py-16 bg-gray-50">
            <div className="container mx-auto px-4">
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800">Featured Practitioners</h2>
                <div className="w-32 h-6 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {Array(4).fill(0).map((_, index) => (
                  <div key={index} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                ))}
              </div>
            </div>
          </section>
        }
      >
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800">Featured Practitioners</h2>
              <Link
                href="/practitioners"
                className="text-emerald-600 hover:text-emerald-700 font-medium"
              >
                View All Practitioners
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {isLoading ? (
                // Loading state
                Array(4).fill(0).map((_, index) => (
                  <div key={index} className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                ))
              ) : practitioners.length > 0 ? (
                // Real data
                practitioners.slice(0, 4).map((practitioner) => (
                  <PractitionerCard key={practitioner.id} practitioner={practitioner} />
                ))
              ) : (
                // Fallback message
                <div className="col-span-4 text-center py-8">
                  <p className="text-gray-500">No featured practitioners available at the moment.</p>
                </div>
              )}
            </div>
          </div>
        </section>
      </LazyLoadedSection>

      {/* Latest Blog Posts Section with Popular Posts Sidebar */}
      {latestPosts.length > 0 && !isLoading && (
        <LazyLoadedSection
          threshold={0.1}
          rootMargin="200px"
          placeholder={
            <section className="py-16">
              <div className="container mx-auto px-4">
                <div className="flex justify-between items-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-800">Latest Articles</h2>
                  <div className="w-32 h-6 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div className="flex flex-col lg:flex-row gap-8">
                  <div className="lg:w-2/3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {Array(2).fill(0).map((_, index) => (
                        <div key={index} className="h-96 bg-gray-200 rounded-lg animate-pulse"></div>
                      ))}
                    </div>
                  </div>
                  <div className="lg:w-1/3 space-y-6">
                    <div className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
                    <div className="h-48 bg-gray-200 rounded-lg animate-pulse"></div>
                  </div>
                </div>
              </div>
            </section>
          }
        >
          <section className="py-16">
            <div className="container mx-auto px-4">
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800">Latest Articles</h2>
                <Link
                  href="/blog"
                  className="text-emerald-600 hover:text-emerald-700 font-medium"
                >
                  View All Articles
                </Link>
              </div>

              <div className="flex flex-col lg:flex-row gap-8">
                {/* Main Content - Latest Posts */}
                <div className="lg:w-2/3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {latestPosts.slice(0, 2).map((post: BlogPost) => (
                      <BlogPostCard
                        key={post.id}
                        post={post}
                        showReadingTime={true}
                        showShareButton={true}
                        showBadge={true}
                      />
                    ))}
                  </div>
                </div>

                {/* Sidebar - Popular Posts */}
                <div className="lg:w-1/3 space-y-6">
                  {popularPosts.length > 0 && (
                    <PopularPosts posts={popularPosts} />
                  )}

                  {/* Newsletter Signup Teaser */}
                  <div className="bg-emerald-50 rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">Stay Updated</h3>
                    <p className="text-gray-600 mb-4">Get the latest natural healing tips and articles delivered to your inbox.</p>
                    <Link
                      href="/newsletter"
                      className="inline-block bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm"
                    >
                      Subscribe to Newsletter
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </LazyLoadedSection>
      )}

      {/* Call to Action */}
      <section className="py-16 bg-emerald-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Start Your Wellness Journey?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Discover holistic health practitioners and clinics that can help you achieve optimal health and well-being.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/categories"
              className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold inline-block"
            >
              Explore Health Categories
            </Link>
            <Link
              href="/blog"
              className="bg-emerald-700 hover:bg-emerald-800 text-white px-8 py-3 rounded-lg font-semibold inline-block"
            >
              Read Health Articles
            </Link>
          </div>
        </div>
      </section>

      {/* Error message if data loading failed */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6 mx-auto max-w-4xl">
          <span className="block sm:inline">{error}</span>
        </div>
      )}
    </>
  );
}
