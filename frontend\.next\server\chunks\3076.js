"use strict";exports.id=3076,exports.ids=[3076],exports.modules={73076:(t,e,a)=>{a.d(e,{$:()=>c,f:()=>n});var s=a(79298),i=a.n(s);let r="https://nice-badge-2130241d6c.strapiapp.com";r||console.error("CRITICAL ERROR: NEXT_PUBLIC_API_URL is not defined in environment variables");let o=r||"";console.log("Using Strapi API URL:",o||"No API URL found in environment variables");let l=o?o.replace("strapiapp.com","media.strapiapp.com"):"";l&&console.log("Strapi Media URL:",l);let n=async(t,e={})=>{let a=Math.random().toString(36).substring(2,8),s=t.startsWith("/")?t:`/${t}`,r=`${o}/api${s}`;try{let t=process.env.STRAPI_API_TOKEN,s={"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`}},o="no-store";o=e.cache?e.cache:e.next?.revalidate!==void 0&&0===e.next.revalidate?"no-store":"force-cache";let l={method:e.method||"GET",headers:{...s,...e.headers||{}},cache:o,next:{tags:e.next?.tags||[],...e.next?.revalidate!==void 0&&{revalidate:e.next.revalidate}}};if(e.next?.revalidate===!1&&l.next&&(l.next.revalidate=!1),("GET"===l.method||"HEAD"===l.method)&&e.params){console.log("[DEBUG] fetchAPI - Raw params before qs.stringify:",JSON.stringify(e.params,null,2));let t=i().stringify(e.params,{encodeValuesOnly:!0});console.log("[DEBUG] fetchAPI - Generated query string:",t),t&&(r=`${r}?${t}`),console.log("[DEBUG] fetchAPI - Final URL:",r)}else e.params&&"GET"!==l.method&&"HEAD"!==l.method&&(l.body=JSON.stringify(e.params));let n=await fetch(r,l);if(!n.ok){let t;try{t=await n.json()}catch(e){t={message:n.statusText,details:await n.text().catch(()=>"")}}console.error(`[${a}] API Error (${r}): Status ${n.status}`,t);let e=Error(`API Error: ${n.status} ${n.statusText}`);throw e.response={status:n.status,data:t},e}return await n.json()}catch(t){throw console.error(`[${a}] API Error (${r}):`,t.message||"Unknown error"),t.response?.status&&console.error(`[${a}] Status:`,t.response.status),t.message&&t.message.includes("ECONNREFUSED")&&(console.error(`[${a}] CRITICAL ERROR: Connection refused for ${r}.`),(r.includes("127.0.0.1")||r.includes("localhost"))&&console.error(`[${a}] Attempting to connect to localhost. Check NEXT_PUBLIC_API_URL in Vercel environment variables if this is a deployed environment.`)),t.response?.status===400&&console.error(`[${a}] Bad Request (400) - Check query parameters for Strapi v5 compatibility.`),t.requestId=a,t}},c={clinics:{getAll:async(t={})=>{let{query:e,location:a,specialtySlug:s,conditionSlug:i,categorySlug:r,page:o=1,pageSize:l=12,cache:c,next:p,...g}=t;console.log("[DEBUG] clinics.getAll called with:",{query:e,location:a,specialtySlug:s,conditionSlug:i,categorySlug:r,page:o,pageSize:l,cache:c,next:p,restParams:g});let u={...g,populate:g.populate||"*",publicationState:"live",pagination:{page:o,pageSize:l},filters:{...g.filters||{}}},d={...g.filters||{}},$=[];if(console.log("[DEBUG] Starting filter construction for clinics..."),e){let t={$or:[{name:{$containsi:e}},{services:{name:{$containsi:e}}},{specialties:{name:{$containsi:e}}},{conditions:{name:{$containsi:e}}},{categories:{name:{$containsi:e}}},{description:{$containsi:e}}]};$.push(t),console.log("[DEBUG] Added query filter:",JSON.stringify(t,null,2))}if(a){let t={address:{$or:[{streetAddress1:{$containsi:a}},{city:{$containsi:a}},{stateProvince:{$containsi:a}},{postalCode:{$containsi:a}}]}};$.push(t),console.log("[DEBUG] Added location filter:",JSON.stringify(t,null,2))}if(s){let t={specialties:{slug:{$eq:s}}};$.push(t),console.log("[DEBUG] Added specialty filter:",JSON.stringify(t,null,2))}if(i){let t={conditions:{slug:{$eq:i}}};$.push(t),console.log("[DEBUG] Added condition filter:",JSON.stringify(t,null,2))}if(r){let t={categories:{slug:{$eq:r}}};$.push(t),console.log("[DEBUG] Added category filter:",JSON.stringify(t,null,2))}if(console.log("[DEBUG] Total combined filters:",$.length),$.length>1?(d.$and=$,console.log("[DEBUG] Using $and with multiple filters:",JSON.stringify(d,null,2))):1===$.length?(Object.assign(d,$[0]),console.log("[DEBUG] Using single filter:",JSON.stringify(d,null,2))):console.log("[DEBUG] No filters applied, using base filters:",JSON.stringify(d,null,2)),u.filters=d,console.log("[DEBUG] Final queryParams for clinics:",JSON.stringify(u,null,2)),s){let t=["strapi-clinics","strapi-specialties",`strapi-specialty-${s}`,`strapi-specialty-${s}-clinics`];t.push(`strapi-specialty-${s}-page-${o}`),e&&t.push(`strapi-specialty-${s}-query-${e}`),a&&t.push(`strapi-specialty-${s}-location-${a}`);let i={tags:t};return p?.revalidate!==void 0&&(i.revalidate=p.revalidate),n("/clinics",{params:u,cache:c,next:i})}if(r){let t=["strapi-clinics","strapi-categories",`strapi-category-${r}`,`strapi-category-${r}-clinics`];t.push(`strapi-category-${r}-page-${o}`),e&&t.push(`strapi-category-${r}-query-${e}`),a&&t.push(`strapi-category-${r}-location-${a}`);let s={tags:t};return p?.revalidate!==void 0&&(s.revalidate=p.revalidate),n("/clinics",{params:u,cache:c,next:s})}let h=["strapi-clinics-list"],f=u.pagination?.page||1;h.push(`strapi-clinics-page-${f}`),e&&h.push(`strapi-clinics-query-${e}`),a&&h.push(`strapi-clinics-location-${a}`);let y={tags:[...p?.tags||[],...h]};return p?.revalidate!==void 0&&(y.revalidate=p.revalidate),n("/clinics",{params:u,cache:c,next:y})},getAllSlugs:async(t={})=>{let e=["strapi-clinics-slugs",...t.next?.tags||[]];return n("/clinics",{params:{fields:["slug"],pagination:{pageSize:250}},cache:t.cache||"force-cache",next:{tags:e,revalidate:t.next?.revalidate??43200,...t.next||{}}})},getBySlug:async(t,e={})=>{let a=["strapi-clinics",`strapi-clinic-${t}`,...e.next?.tags||[]];return n("/clinics",{params:{filters:{slug:{$eq:t}},populate:{logo:!0,address:!0,contactInfo:!0,location:!0,openingHours:!0,services:!0,specialties:!0,conditions:!0,practitioners:{populate:{profilePicture:!0}},appointment_options:!0,payment_methods:!0,seo:!0}},cache:e.cache||"force-cache",next:{...e.next||{},tags:a,revalidate:e.next?.revalidate??43200}})},getFeatured:async(t={})=>n("/clinics",{params:{filters:{isFeatured:{$eq:!0}},populate:"*"},...t}),getByCategorySlug:async({slug:t,query:e="",location:a="",page:s=1,pageSize:i=12})=>{try{console.log(`Fetching clinics for category slug: ${t}, page: ${s}, query: ${e}, location: ${a}`);let r=[];r.push({categories:{slug:{$eq:t}}}),e&&r.push({$or:[{name:{$containsi:e}},{services:{name:{$containsi:e}}},{specialties:{name:{$containsi:e}}},{conditions:{name:{$containsi:e}}}]}),a&&r.push({address:{$or:[{streetAddress1:{$containsi:a}},{city:{$containsi:a}},{stateProvince:{$containsi:a}},{postalCode:{$containsi:a}}]}});let o=r.length>1?{$and:r}:{categories:{slug:{$eq:t}}},l=["strapi-clinics","strapi-categories",`strapi-category-${t}`,`strapi-category-${t}-clinics`,`strapi-category-${t}-page-${s}`];return e&&l.push(`strapi-category-${t}-query-${e}`),a&&l.push(`strapi-category-${t}-location-${a}`),n("/clinics",{params:{filters:o,pagination:{page:s,pageSize:i},populate:{logo:!0,featuredImage:!0,address:!0,contactInfo:!0,categories:!0},publicationState:"live"},next:{tags:l}})}catch(e){throw console.error(`Error in getByCategorySlug for clinics with slug ${t}:`,e),e}}},practitioners:{getAll:async(t={})=>{let{query:e,location:a,specialtySlug:s,conditionSlug:i,categorySlug:r,page:o=1,pageSize:l=12,cache:c,next:p,...g}=t;console.log("[DEBUG] practitioners.getAll called with:",{query:e,location:a,specialtySlug:s,conditionSlug:i,categorySlug:r,page:o,pageSize:l,cache:c,next:p,restParams:g});let u={...g,populate:g.populate||"*",publicationState:"live",pagination:{page:o,pageSize:l},filters:{...g.filters||{}}},d={...g.filters||{}},$=[];if(e&&$.push({$or:[{name:{$containsi:e}},{specialties:{name:{$containsi:e}}},{conditions:{name:{$containsi:e}}}]}),a&&$.push({address:{$or:[{streetAddress1:{$containsi:a}},{city:{$containsi:a}},{stateProvince:{$containsi:a}},{postalCode:{$containsi:a}}]}}),s&&$.push({specialties:{slug:{$eq:s}}}),i&&$.push({conditions:{slug:{$eq:i}}}),r&&$.push({categories:{slug:{$eq:r}}}),$.length>1?d.$and=$:1===$.length&&Object.assign(d,$[0]),u.filters=d,s){let t=["strapi-practitioners","strapi-specialties",`strapi-specialty-${s}`,`strapi-specialty-${s}-practitioners`];t.push(`strapi-specialty-${s}-page-${o}`),e&&t.push(`strapi-specialty-${s}-query-${e}`),a&&t.push(`strapi-specialty-${s}-location-${a}`);let i={tags:t};return p?.revalidate!==void 0&&(i.revalidate=p.revalidate),n("/practitioners",{params:u,cache:c,next:i})}if(r){let t=["strapi-practitioners","strapi-categories",`strapi-category-${r}`,`strapi-category-${r}-practitioners`];t.push(`strapi-category-${r}-page-${o}`),e&&t.push(`strapi-category-${r}-query-${e}`),a&&t.push(`strapi-category-${r}-location-${a}`);let s={tags:t};return p?.revalidate!==void 0&&(s.revalidate=p.revalidate),n("/practitioners",{params:u,cache:c,next:s})}let h=["strapi-practitioners-list"],f=u.pagination?.page||1;h.push(`strapi-practitioners-page-${f}`),e&&h.push(`strapi-practitioners-query-${e}`),a&&h.push(`strapi-practitioners-location-${a}`);let y={tags:[...p?.tags||[],...h]};return p?.revalidate!==void 0&&(y.revalidate=p.revalidate),n("/practitioners",{params:u,cache:c,next:y})},getAllSlugs:async(t={})=>{let e=[],a=1,s=!0,i={cache:t.cache||"force-cache",next:{revalidate:t.next?.revalidate??43200,tags:["strapi-practitioners-slugs",...t.next?.tags||[]],...t.next||{}}};for(i.next.tags&&(i.next.tags=Array.from(new Set(i.next.tags)));s;)try{let t=await n("/practitioners",{params:{fields:["slug"],pagination:{page:a,pageSize:100}},cache:i.cache,next:i.next});if(t?.data&&Array.isArray(t.data)){t.data.forEach(t=>{t&&t.slug&&e.push({slug:t.slug})});let i=t.meta?.pagination;t.data.length<100||!i||a>=i.pageCount?s=!1:a++}else s=!1}catch(t){console.error(`Error fetching page ${a} of practitioner slugs:`,t),s=!1}return{data:e}},getBySlug:async(t,e={})=>{let a=["strapi-practitioner",`strapi-practitioner-${t}`];return n("/practitioners",{params:{filters:{slug:{$eq:t}},populate:{profilePicture:!0,contactInfo:!0,specialties:!0,conditions:!0,clinics:!0,seo:!0}},cache:e.cache||"force-cache",next:{...e.next||{},tags:[...a,...e.next?.tags||[]],revalidate:e.next?.revalidate??43200}})},getFeatured:async(t={})=>n("/practitioners",{params:{filters:{isFeatured:{$eq:!0}},populate:"*"},...t}),getByCategorySlug:async({slug:t,query:e="",location:a="",page:s=1,pageSize:i=12})=>{try{let r=[];r.push({categories:{slug:{$eq:t}}}),e&&r.push({$or:[{name:{$containsi:e}},{title:{$containsi:e}},{qualifications:{$containsi:e}},{specialties:{name:{$containsi:e}}},{conditions:{name:{$containsi:e}}}]}),a&&r.push({address:{$or:[{streetAddress1:{$containsi:a}},{city:{$containsi:a}},{stateProvince:{$containsi:a}},{postalCode:{$containsi:a}}]}});let o=r.length>1?{$and:r}:{categories:{slug:{$eq:t}}},l=["strapi-practitioners","strapi-categories",`strapi-category-${t}`,`strapi-category-${t}-practitioners`,`strapi-category-${t}-page-${s}`];return e&&l.push(`strapi-category-${t}-query-${e}`),a&&l.push(`strapi-category-${t}-location-${a}`),n("/practitioners",{params:{filters:o,pagination:{page:s,pageSize:i},populate:{profilePicture:!0,contactInfo:!0,specialties:!0,conditions:!0,categories:!0},publicationState:"live"},next:{tags:l}})}catch(e){throw console.error(`Error in getByCategorySlug for practitioners with slug ${t}:`,e),e}}},categories:{getAll:async(t={})=>{let{query:e,location:a,page:s=1,pageSize:i=12,cache:r,next:o,...l}=t,c={...l,populate:l.populate||{icon:!0,featuredImage:!0},publicationState:"live",pagination:{page:s,pageSize:i},filters:{...l.filters||{}}};e&&c.filters&&(c.filters.name={$containsi:e});let p=["strapi-categories","strapi-categories-slugs"];c.pagination?.page&&p.push(`strapi-categories-page-${c.pagination.page}`),e&&p.push(`strapi-categories-query-${e}`);let g={tags:[...o?.tags||[],...p]};return o?.revalidate!==void 0&&(g.revalidate=o.revalidate),n("/categories",{params:c,cache:r,next:g})},getBySlug:async(t,e={})=>{let a=["strapi-categories",`strapi-category-${t}`,`strapi-category-${t}-clinics`,`strapi-category-${t}-practitioners`];return n("/categories",{params:{filters:{slug:{$eq:t}},populate:{seo:{populate:{openGraph:{populate:{ogImage:!0}},metaImage:!0}},icon:!0,featuredImage:!0,clinics:{populate:{logo:!0,featuredImage:!0,address:!0,contactInfo:!0}},practitioners:{populate:{profilePicture:!0,contactInfo:!0}}},publicationState:"live"},next:{...e.next||{},tags:[...a,...e.next?.tags||[]]}})},getAllSlugs:async(t={})=>n("/categories",{params:{fields:["slug"],pagination:{pageSize:1e3}},cache:t.cache||"force-cache",next:{...t.next||{},tags:["strapi-categories-slugs",...t.next?.tags||[]],revalidate:t.next?.revalidate??43200}}),getFooterCategories:async(t={})=>n("/categories",{params:{filters:{showInFooter:{$eq:!0}},populate:"*",publicationState:"live"},next:{...t.next||{},tags:["strapi-categories","strapi-categories-footer",...t.next?.tags||[]]}})},blog:{getPosts:async(t={})=>{try{let e={publicationState:"live"},a=1,s=10;t.pagination?(e.pagination=t.pagination,a=t.pagination.page||1,s=t.pagination.pageSize||10):(t.page||t.pageSize)&&(a=t.page||1,s=t.pageSize||10,e.pagination={page:a,pageSize:s}),t.sort?e.sort=t.sort:e.sort=["publishDate:desc"],t.filters?e.filters=t.filters:t.categorySlug?e.filters={...e.filters||{},blog_categories:{slug:{$eq:t.categorySlug}}}:t.tagSlug?e.filters={...e.filters||{},blog_tags:{slug:{$eq:t.tagSlug}}}:t.query&&(e.filters={...e.filters||{},$or:[{title:{$containsi:t.query}},{excerpt:{$containsi:t.query}}]}),t.populate?e.populate=t.populate:e.populate={featuredImage:!0,author_blogs:{populate:{profilePicture:!0}},blog_categories:!0,blog_tags:!0};let i=["strapi-blog-posts"];return t.categorySlug&&(i.push(`strapi-category-${t.categorySlug}`),i.push(`strapi-category-${t.categorySlug}-page-${a}`)),t.tagSlug&&(i.push(`strapi-tag-${t.tagSlug}`),i.push(`strapi-tag-${t.tagSlug}-page-${a}`)),t.query&&i.push(`strapi-blog-query-${t.query}`),await n("/blog-posts",{params:e,next:{tags:i}})}catch(t){throw console.error("Error in blog.getPosts:",t),t.response&&(console.error("Response status:",t.response.status),console.error("Response data:",t.response.data),console.error("Response headers:",t.response.headers)),t}},getAllSlugs:async(t={})=>n("/blog-posts",{params:{fields:["slug"],pagination:{pageSize:1e3}},next:{...t.next||{},tags:["strapi-blog-posts-slugs",...t.next?.tags||[]]}}),getPostBySlug:async(t,e={})=>{let a=["strapi-blog-posts",`strapi-blog-post-${t}`];return n("/blog-posts",{params:{filters:{slug:{$eq:t}},populate:{seo:{populate:{metaImage:!0,openGraph:!0}},featuredImage:!0,author_blogs:{fields:["id","name","slug","bio"],populate:{profilePicture:!0}},blog_categories:{fields:["id","name","slug"]},blog_tags:{fields:["id","name","slug"]}}},next:{...e.next||{},tags:[...a,...e.next?.tags||[]]}})},getCategories:async(t={},e={})=>{let{next:a,...s}=t;return n("/blog-categories",{params:{...s,publicationState:"live",populate:s.populate||"*"},next:{...e.next||{},...a||{},tags:["strapi-categories","strapi-blog-categories",...e.next?.tags||[],...a?.tags||[]]}})},getCategoryBySlug:async(t,e={},a={})=>{try{let a=e.pagination?.page||1,s=e.pagination?.pageSize||12,i=e.sort||"publishDate:desc",r=["strapi-categories","strapi-blog-categories",`strapi-category-${t}`,`strapi-category-${t}-page-${a}`],o={filters:{slug:{$eq:t}},pagination:{page:1,pageSize:1},sort:[i],populate:{blog_posts:{fields:["title","slug","excerpt","publishDate","publishedAt"],populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}},pagination:{page:a,pageSize:s},sort:[i]},seo:!0}};return await n("/blog-categories",{params:o,next:{tags:r}})}catch(s){console.error(`Error in getCategoryBySlug for slug ${t}:`,s);try{let{next:s,...i}=e;return await n("/blog-categories",{params:{...i,filters:{slug:{$eq:t}},populate:{blog_posts:{populate:["featuredImage","author_blogs.profilePicture"]},seo:!0}},next:{...a.next||{},...s||{}}})}catch(e){throw console.error(`Fallback also failed for slug ${t}:`,e),e}}},getTags:async(t={})=>n("/blog-tags",{params:{populate:"*"},next:{...t.next||{},tags:["strapi-tags","strapi-blog-tags",...t.next?.tags||[]]}}),getTagBySlug:async(t,e={})=>{let a=["strapi-tags","strapi-blog-tags",`strapi-tag-${t}`];return n("/blog-tags",{params:{filters:{slug:{$eq:t}},populate:{blog_posts:{populate:{featuredImage:{fields:["url","alternativeText","width","height","formats"]},author_blogs:{populate:{profilePicture:{fields:["url","alternativeText","width","height","formats"]}}}}}}},next:{...e.next||{},tags:[...a,...e.next?.tags||[]]}})},getAuthors: <AUTHORS>