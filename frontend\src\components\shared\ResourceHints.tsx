'use client';

// Using Script component from next/script for client-side resource hints
import Script from 'next/script';

interface ResourceHintsProps {
  /**
   * DNS prefetch domains
   */
  dnsPrefetch?: string[];

  /**
   * Preconnect domains
   */
  preconnect?: string[];

  /**
   * Preload resources
   */
  preload?: {
    href: string;
    as: 'script' | 'style' | 'image' | 'font' | 'fetch' | 'document';
    type?: string;
    crossOrigin?: 'anonymous' | 'use-credentials';
  }[];

  /**
   * Prefetch resources
   */
  prefetch?: {
    href: string;
    as?: 'script' | 'style' | 'image' | 'font' | 'fetch' | 'document';
  }[];
}

/**
 * Component to add resource hints to the document head
 *
 * This component adds DNS prefetch, preconnect, preload, and prefetch
 * resource hints to improve performance.
 */
export default function ResourceHints({
  dnsPrefetch = [],
  preconnect = [],
  preload = [],
  prefetch = [],
}: ResourceHintsProps) {
  // In Next.js 15 with App Router, we need to inject these hints using a script
  // that runs on the client side to add the resource hints to the document head

  // Create a script that will inject the resource hints
  const injectResourceHints = `
    (function() {
      const head = document.head;

      // DNS Prefetch
      ${dnsPrefetch.map(domain => `
        const dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')} = document.createElement('link');
        dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.rel = 'dns-prefetch';
        dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.href = '${domain}';
        head.appendChild(dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')});
      `).join('')}

      // Preconnect
      ${preconnect.map(domain => `
        const preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')} = document.createElement('link');
        preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.rel = 'preconnect';
        preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.href = '${domain}';
        preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.crossOrigin = 'anonymous';
        head.appendChild(preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')});
      `).join('')}

      // Preload
      ${preload.map((resource, index) => `
        const preload_${index} = document.createElement('link');
        preload_${index}.rel = 'preload';
        preload_${index}.href = '${resource.href}';
        preload_${index}.as = '${resource.as}';
        ${resource.type ? `preload_${index}.type = '${resource.type}';` : ''}
        ${resource.crossOrigin ? `preload_${index}.crossOrigin = '${resource.crossOrigin}';` : ''}
        head.appendChild(preload_${index});
      `).join('')}

      // Prefetch
      ${prefetch.map((resource, index) => `
        const prefetch_${index} = document.createElement('link');
        prefetch_${index}.rel = 'prefetch';
        prefetch_${index}.href = '${resource.href}';
        ${resource.as ? `prefetch_${index}.as = '${resource.as}';` : ''}
        head.appendChild(prefetch_${index});
      `).join('')}
    })();
  `;

  return (
    <Script id="resource-hints" strategy="beforeInteractive" dangerouslySetInnerHTML={{ __html: injectResourceHints }} />
  );
}
