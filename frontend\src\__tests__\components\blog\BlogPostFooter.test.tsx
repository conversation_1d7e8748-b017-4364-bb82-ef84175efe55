import React from 'react';
import { render, screen } from '@testing-library/react';
import BlogPostFooter from '@/components/blog/BlogPostFooter';
import { BlogPost } from '@/lib/blogUtils';

// Mock truncateByWords function
jest.mock('@/lib/blogUtils', () => ({
  ...jest.requireActual('@/lib/blogUtils'),
  truncateByWords: jest.fn((text) => text ? `${text} (truncated)` : '')
}));

describe('BlogPostFooter Component', () => {
  const mockPost: BlogPost = {
    id: '1',
    title: 'Test Blog Post',
    slug: 'test-blog-post',
    content: '<p>Test content</p>',
    excerpt: 'Test excerpt',
    featured_image: 'https://example.com/image.jpg',
    published_at: '2023-06-15T10:30:00Z',
    reading_time: 5,
    author: {
      id: '2',
      name: 'Test Author',
      slug: 'test-author',
      profile_picture: 'https://example.com/profile.jpg',
      bio: 'Author bio'
    },
    categories: [
      {
        id: '3',
        name: 'Test Category',
        slug: 'test-category'
      },
      {
        id: '4',
        name: 'Another Category',
        slug: 'another-category'
      }
    ],
    tags: ['tag1', 'tag2'],
    related_posts: [
      {
        id: '5',
        title: 'Related Post 1',
        slug: 'related-post-1',
        excerpt: 'Related excerpt 1',
        reading_time: 3
      },
      {
        id: '6',
        title: 'Related Post 2',
        slug: 'related-post-2',
        excerpt: 'Related excerpt 2',
        reading_time: 4
      }
    ]
  };

  test('renders categories section with links', () => {
    render(<BlogPostFooter post={mockPost} />);
    
    expect(screen.getByText('Categories:')).toBeInTheDocument();
    
    const category1 = screen.getByText('Test Category');
    expect(category1).toBeInTheDocument();
    expect(category1.closest('a')).toHaveAttribute('href', '/blog/categories/test-category');
    
    const category2 = screen.getByText('Another Category');
    expect(category2).toBeInTheDocument();
    expect(category2.closest('a')).toHaveAttribute('href', '/blog/categories/another-category');
  });

  test('renders tags section with links', () => {
    render(<BlogPostFooter post={mockPost} />);
    
    expect(screen.getByText('Tags:')).toBeInTheDocument();
    
    const tag1 = screen.getByText('tag1');
    expect(tag1).toBeInTheDocument();
    expect(tag1.closest('a')).toHaveAttribute('href', '/blog/tags/tag1');
    
    const tag2 = screen.getByText('tag2');
    expect(tag2).toBeInTheDocument();
    expect(tag2.closest('a')).toHaveAttribute('href', '/blog/tags/tag2');
  });

  test('renders author bio section', () => {
    render(<BlogPostFooter post={mockPost} />);
    
    expect(screen.getByText('Test Author')).toBeInTheDocument();
    expect(screen.getByText('Contributor')).toBeInTheDocument();
    expect(screen.getByText('Author bio (truncated)')).toBeInTheDocument();
  });

  test('renders default message when author has no bio', () => {
    const postWithoutAuthorBio = {
      ...mockPost,
      author: {
        ...mockPost.author,
        bio: null
      }
    };
    
    render(<BlogPostFooter post={postWithoutAuthorBio} />);
    expect(screen.getByText('No bio available for this author.')).toBeInTheDocument();
  });

  test('renders related posts section', () => {
    render(<BlogPostFooter post={mockPost} />);
    
    expect(screen.getByText('You May Also Like')).toBeInTheDocument();
    
    const relatedPost1 = screen.getByText('Related Post 1');
    expect(relatedPost1).toBeInTheDocument();
    expect(relatedPost1.closest('a')).toHaveAttribute('href', '/blog/related-post-1');
    
    const relatedPost2 = screen.getByText('Related Post 2');
    expect(relatedPost2).toBeInTheDocument();
    expect(relatedPost2.closest('a')).toHaveAttribute('href', '/blog/related-post-2');
    
    expect(screen.getByText('Related excerpt 1')).toBeInTheDocument();
    expect(screen.getByText('Related excerpt 2')).toBeInTheDocument();
    
    expect(screen.getAllByText('3 min read')).toHaveLength(1);
    expect(screen.getAllByText('4 min read')).toHaveLength(1);
  });

  test('renders back to blog link', () => {
    render(<BlogPostFooter post={mockPost} />);
    
    const backLink = screen.getByText('Back to Blog');
    expect(backLink).toBeInTheDocument();
    expect(backLink.closest('a')).toHaveAttribute('href', '/blog');
  });

  test('does not render categories section when no categories are provided', () => {
    const postWithoutCategories = {
      ...mockPost,
      categories: []
    };
    
    render(<BlogPostFooter post={postWithoutCategories} />);
    expect(screen.queryByText('Categories:')).not.toBeInTheDocument();
  });

  test('does not render tags section when no tags are provided', () => {
    const postWithoutTags = {
      ...mockPost,
      tags: []
    };
    
    render(<BlogPostFooter post={postWithoutTags} />);
    expect(screen.queryByText('Tags:')).not.toBeInTheDocument();
  });

  test('does not render related posts section when no related posts are provided', () => {
    const postWithoutRelatedPosts = {
      ...mockPost,
      related_posts: []
    };
    
    render(<BlogPostFooter post={postWithoutRelatedPosts} />);
    expect(screen.queryByText('You May Also Like')).not.toBeInTheDocument();
  });

  test('renders author profile picture when provided', () => {
    render(<BlogPostFooter post={mockPost} />);
    const image = screen.getByAltText('Test Author');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/profile.jpg');
  });

  test('renders author initial when profile picture is not provided', () => {
    const postWithoutAuthorPicture = {
      ...mockPost,
      author: {
        ...mockPost.author,
        profile_picture: null
      }
    };
    
    render(<BlogPostFooter post={postWithoutAuthorPicture} />);
    expect(screen.getByText('T')).toBeInTheDocument(); // First letter of "Test Author"
  });
});
