{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/SearchInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react'; // Import React\r\nimport { MagnifyingGlassIcon } from '@heroicons/react/24/outline';\r\nimport { useSearchParams, usePathname, useRouter } from 'next/navigation';\r\nimport { useDebouncedCallback } from 'use-debounce';\r\nimport { FiSearch } from 'react-icons/fi'; // Using FiSearch as seen in clinics/page.tsx\r\n\r\ninterface SearchInputProps {\r\n  placeholder: string;\r\n  paramName?: string; // Optional: allows specifying a different URL param name (defaults to 'query')\r\n  icon?: React.ReactNode; // Optional: allows passing a custom icon component\r\n}\r\n\r\nexport default function SearchInput({ placeholder, paramName = 'query', icon }: SearchInputProps) { // Destructure icon prop\r\n  const searchParams = useSearchParams();\r\n  const pathname = usePathname();\r\n  const { replace } = useRouter();\r\n\r\n  const handleSearch = useDebouncedCallback((term: string) => {\r\n    console.log(`Searching... ${term}`); // For debugging\r\n    const params = new URLSearchParams(searchParams);\r\n    params.set('page', '1'); // Reset page to 1 on new search\r\n\r\n    if (term) {\r\n      params.set(paramName, term);\r\n    } else {\r\n      params.delete(paramName);\r\n    }\r\n    replace(`${pathname}?${params.toString()}`);\r\n  }, 500); // Increased debounce to 500ms\r\n\r\n  return (\r\n    <div className=\"relative flex flex-1 flex-shrink-0\">\r\n      <label htmlFor={paramName} className=\"sr-only\">\r\n        Search\r\n      </label>\r\n      <input\r\n        id={paramName} // Add id for label association\r\n        className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\" // Style copied from clinics/page.tsx\r\n        placeholder={placeholder}\r\n        onChange={(e) => {\r\n          handleSearch(e.target.value);\r\n        }}\r\n        defaultValue={searchParams.get(paramName)?.toString()} // Read initial value from URL params\r\n      />\r\n      {/* Render provided icon or default FiSearch */}\r\n      {icon ? icon : <FiSearch className=\"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900\" />}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA,8PAA2C,6CAA6C;;;AANxF;;;;AAce,SAAS,YAAY,EAAE,WAAW,EAAE,YAAY,OAAO,EAAE,IAAI,EAAoB;;IAC9F,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAE5B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD;0DAAE,CAAC;YACzC,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,GAAG,gBAAgB;YACrD,MAAM,SAAS,IAAI,gBAAgB;YACnC,OAAO,GAAG,CAAC,QAAQ,MAAM,gCAAgC;YAEzD,IAAI,MAAM;gBACR,OAAO,GAAG,CAAC,WAAW;YACxB,OAAO;gBACL,OAAO,MAAM,CAAC;YAChB;YACA,QAAQ,GAAG,SAAS,CAAC,EAAE,OAAO,QAAQ,IAAI;QAC5C;yDAAG,MAAM,8BAA8B;IAEvC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAM,SAAS;gBAAW,WAAU;0BAAU;;;;;;0BAG/C,6LAAC;gBACC,IAAI;gBACJ,WAAU,wHAAwH,qCAAqC;;gBACvK,aAAa;gBACb,UAAU,CAAC;oBACT,aAAa,EAAE,MAAM,CAAC,KAAK;gBAC7B;gBACA,cAAc,aAAa,GAAG,CAAC,YAAY;;;;;;YAG5C,OAAO,qBAAO,6LAAC,iJAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;;;;;;;AAGzC;GApCwB;;QACD,qIAAA,CAAA,kBAAe;QACnB,qIAAA,CAAA,cAAW;QACR,qIAAA,CAAA,YAAS;QAER,6JAAA,CAAA,uBAAoB;;;KALnB", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/analytics.ts"], "sourcesContent": ["/**\n * Utility functions for analytics tracking\n */\nimport logger from './logger';\n\n/**\n * Track a post view\n * @param postId The ID of the post\n * @param postSlug The slug of the post\n */\nexport async function trackPostView(postId: string, postSlug: string): Promise<void> {\n  try {\n    // Don't track views in development mode\n    if (process.env.NODE_ENV === 'development') {\n      logger.debug(`Post view tracked: ${postId} (${postSlug})`);\n      return;\n    }\n\n    // Send the view to the API endpoint\n    const response = await fetch('/api/analytics/post-view', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ postId, postSlug }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`Failed to track post view: ${response.statusText}`);\n    }\n\n  } catch (error) {\n    // Don't let tracking errors affect the user experience\n    logger.error('Error tracking post view:', error);\n  }\n}\n\n/**\n * Get popular posts based on view count from Strapi\n * @param limit Number of popular posts to return\n * @param timeframe Timeframe to consider for popularity ('day', 'week', 'month', 'all')\n */\nexport async function getPopularPosts(limit: number = 4, timeframe: 'day' | 'week' | 'month' | 'all' = 'week'): Promise<any[]> {\n  try {\n    // Strapi API token for authentication - MUST be set in environment variables\n    const strapiToken = process.env.STRAPI_API_TOKEN;\n\n    if (!strapiToken) {\n      logger.error('STRAPI_API_TOKEN environment variable is not set');\n      return [];\n    }\n\n    // Strapi API URL\n    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';\n\n    // Skip the popular posts endpoint for now since it's not working\n    // Instead, directly fetch recent posts as a fallback\n    logger.info('Fetching recent posts as fallback for popular posts');\n\n    const recentPostsResponse = await fetch(\n      `${strapiUrl}/api/blog-posts?sort=publishDate:desc&pagination[limit]=${limit}`,\n      {\n        headers: {\n          'Authorization': `Bearer ${strapiToken}`\n        },\n        next: { revalidate: 3600 } // Cache for 1 hour\n      }\n    );\n\n    if (!recentPostsResponse.ok) {\n      throw new Error(`Failed to fetch recent posts: ${recentPostsResponse.statusText}`);\n    }\n\n    const recentData = await recentPostsResponse.json();\n\n    if (recentData && recentData.data && Array.isArray(recentData.data) && recentData.data.length > 0) {\n      // Map recent posts to the same format as popular posts\n      return recentData.data.map((post: any) => {\n        // Ensure post and post.attributes exist before accessing properties\n        if (!post || !post.attributes) {\n          logger.warn('Invalid post data structure:', post);\n          return null;\n        }\n\n        const attributes = post.attributes;\n\n        return {\n          id: post.id,\n          title: attributes.title || 'Untitled Post',\n          slug: attributes.slug || `post-${post.id}`,\n          excerpt: attributes.excerpt || '',\n          featured_image: attributes.featuredImage?.data?.attributes?.url\n            ? `${strapiUrl}${attributes.featuredImage.data.attributes.url}`\n            : null,\n          publish_date: attributes.publishDate || attributes.createdAt || new Date().toISOString(),\n          view_count: 0,\n          isFeatured: attributes.isFeatured || false\n        };\n      }).filter(Boolean); // Remove any null entries\n    }\n\n    // If we still don't have any posts, return an empty array\n    logger.info('No posts found, returning empty array');\n    return [];\n\n  } catch (error) {\n    logger.error('Error fetching posts:', error);\n    // If all else fails, return an empty array\n    return [];\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAWO;AAVR;;AAOO,eAAe,cAAc,MAAc,EAAE,QAAgB;IAClE,IAAI;QACF,wCAAwC;QACxC,wCAA4C;YAC1C,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,mBAAmB,EAAE,OAAO,EAAE,EAAE,SAAS,CAAC,CAAC;YACzD;QACF;;QAEA,oCAAoC;QACpC,MAAM;IAYR,EAAE,OAAO,OAAO;QACd,uDAAuD;QACvD,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,6BAA6B;IAC5C;AACF;AAOO,eAAe,gBAAgB,QAAgB,CAAC,EAAE,YAA8C,MAAM;IAC3G,IAAI;QACF,6EAA6E;QAC7E,MAAM,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB;QAEhD,IAAI,CAAC,aAAa;YAChB,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC;YACb,OAAO,EAAE;QACX;QAEA,iBAAiB;QACjB,MAAM,YAAY,6DAA0C;QAE5D,iEAAiE;QACjE,qDAAqD;QACrD,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC;QAEZ,MAAM,sBAAsB,MAAM,MAChC,GAAG,UAAU,wDAAwD,EAAE,OAAO,EAC9E;YACE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa;YAC1C;YACA,MAAM;gBAAE,YAAY;YAAK,EAAE,mBAAmB;QAChD;QAGF,IAAI,CAAC,oBAAoB,EAAE,EAAE;YAC3B,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,oBAAoB,UAAU,EAAE;QACnF;QAEA,MAAM,aAAa,MAAM,oBAAoB,IAAI;QAEjD,IAAI,cAAc,WAAW,IAAI,IAAI,MAAM,OAAO,CAAC,WAAW,IAAI,KAAK,WAAW,IAAI,CAAC,MAAM,GAAG,GAAG;YACjG,uDAAuD;YACvD,OAAO,WAAW,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC1B,oEAAoE;gBACpE,IAAI,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;oBAC7B,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,gCAAgC;oBAC5C,OAAO;gBACT;gBAEA,MAAM,aAAa,KAAK,UAAU;gBAElC,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,WAAW,KAAK,IAAI;oBAC3B,MAAM,WAAW,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBAC1C,SAAS,WAAW,OAAO,IAAI;oBAC/B,gBAAgB,WAAW,aAAa,EAAE,MAAM,YAAY,MACxD,GAAG,YAAY,WAAW,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,GAC7D;oBACJ,cAAc,WAAW,WAAW,IAAI,WAAW,SAAS,IAAI,IAAI,OAAO,WAAW;oBACtF,YAAY;oBACZ,YAAY,WAAW,UAAU,IAAI;gBACvC;YACF,GAAG,MAAM,CAAC,UAAU,0BAA0B;QAChD;QAEA,0DAA0D;QAC1D,uHAAA,CAAA,UAAM,CAAC,IAAI,CAAC;QACZ,OAAO,EAAE;IAEX,EAAE,OAAO,OAAO;QACd,uHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,yBAAyB;QACtC,2CAA2C;QAC3C,OAAO,EAAE;IACX;AACF", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/blog/MarkdownContent.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport ReactMarkdown from 'react-markdown';\r\nimport rehypeRaw from 'rehype-raw';\r\nimport { useEffect } from 'react';\r\nimport { trackPostView } from '@/lib/analytics';\r\n\r\ninterface MarkdownContentProps {\r\n  content: string;\r\n  postId?: string;\r\n  postSlug?: string;\r\n  applyNoFollow?: boolean; // Add optional prop, defaults to true\r\n}\r\n\r\n// Default applyNoFollow to true\r\nconst MarkdownContent = ({\r\n  content,\r\n  postId,\r\n  postSlug,\r\n  applyNoFollow = true\r\n}: MarkdownContentProps) => {\r\n\r\n  // Track post view when the component mounts\r\n  useEffect(() => {\r\n    if (postId && postSlug) {\r\n      // Small delay to ensure the page has loaded\r\n      const timer = setTimeout(() => {\r\n        trackPostView(postId, postSlug);\r\n      }, 2000);\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [postId, postSlug]);\r\n  return (\r\n    <div className=\"mb-8\"> {/* Removed prose prose-lg max-w-none */}\r\n      <ReactMarkdown\r\n        components={{\r\n          h1: ({node, ...props}) => <h1 className=\"text-3xl font-bold text-gray-800 mt-8 mb-4\" {...props} />,\r\n          h2: ({node, ...props}) => <h2 className=\"text-2xl font-bold text-gray-800 mt-6 mb-3\" {...props} />,\r\n          h3: ({node, ...props}) => <h3 className=\"text-xl font-bold text-gray-800 mt-5 mb-2\" {...props} />,\r\n          h4: ({node, ...props}) => <h4 className=\"text-lg font-bold text-gray-800 mt-4 mb-2\" {...props} />,\r\n          p: ({node, ...props}) => <p className=\"text-gray-700 mb-4\" {...props} />,\r\n          a: ({node, href, ...props}) => { // Destructure href here\r\n            const isExternal = href && (href.startsWith('http://') || href.startsWith('https://'));\r\n\r\n            // Construct rel attribute based on applyNoFollow prop\r\n            let rel = '';\r\n            if (isExternal) {\r\n              if (applyNoFollow) {\r\n                rel += 'nofollow '; // Add nofollow only if prop is true\r\n              }\r\n              rel += 'noopener noreferrer'; // Always add these for security\r\n            }\r\n\r\n            const target = isExternal ? '_blank' : undefined;\r\n\r\n            return (\r\n              <a\r\n                className=\"text-emerald-600 hover:text-emerald-700 underline\"\r\n                href={href}\r\n                rel={rel.trim() || undefined} // Set rel only if it has content, otherwise undefined\r\n                target={target}\r\n                {...props}\r\n              />\r\n            );\r\n          },\r\n          ul: ({node, ...props}) => <ul className=\"list-disc pl-6 mb-4\" {...props} />,\r\n          ol: ({node, ...props}) => <ol className=\"list-decimal pl-6 mb-4\" {...props} />,\r\n          li: ({node, ...props}) => <li className=\"mb-1\" {...props} />,\r\n          blockquote: ({node, ...props}) => <blockquote className=\"border-l-4 border-emerald-500 pl-4 italic my-4\" {...props} />\r\n        }}\r\n        rehypePlugins={[rehypeRaw]}\r\n      >\r\n        {content}\r\n      </ReactMarkdown>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MarkdownContent;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAcA,gCAAgC;AAChC,MAAM,kBAAkB,CAAC,EACvB,OAAO,EACP,MAAM,EACN,QAAQ,EACR,gBAAgB,IAAI,EACC;;IAErB,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,UAAU,UAAU;gBACtB,4CAA4C;gBAC5C,MAAM,QAAQ;uDAAW;wBACvB,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;oBACxB;sDAAG;gBAEH;iDAAO,IAAM,aAAa;;YAC5B;QACF;oCAAG;QAAC;QAAQ;KAAS;IACrB,qBACE,6LAAC;QAAI,WAAU;;YAAO;0BACpB,6LAAC,2LAAA,CAAA,UAAa;gBACZ,YAAY;oBACV,IAAI,CAAC,EAAC,IAAI,EAAE,GAAG,OAAM,iBAAK,6LAAC;4BAAG,WAAU;4BAA8C,GAAG,KAAK;;;;;;oBAC9F,IAAI,CAAC,EAAC,IAAI,EAAE,GAAG,OAAM,iBAAK,6LAAC;4BAAG,WAAU;4BAA8C,GAAG,KAAK;;;;;;oBAC9F,IAAI,CAAC,EAAC,IAAI,EAAE,GAAG,OAAM,iBAAK,6LAAC;4BAAG,WAAU;4BAA6C,GAAG,KAAK;;;;;;oBAC7F,IAAI,CAAC,EAAC,IAAI,EAAE,GAAG,OAAM,iBAAK,6LAAC;4BAAG,WAAU;4BAA6C,GAAG,KAAK;;;;;;oBAC7F,GAAG,CAAC,EAAC,IAAI,EAAE,GAAG,OAAM,iBAAK,6LAAC;4BAAE,WAAU;4BAAsB,GAAG,KAAK;;;;;;oBACpE,GAAG,CAAC,EAAC,IAAI,EAAE,IAAI,EAAE,GAAG,OAAM;wBACxB,MAAM,aAAa,QAAQ,CAAC,KAAK,UAAU,CAAC,cAAc,KAAK,UAAU,CAAC,WAAW;wBAErF,sDAAsD;wBACtD,IAAI,MAAM;wBACV,IAAI,YAAY;4BACd,IAAI,eAAe;gCACjB,OAAO,aAAa,oCAAoC;4BAC1D;4BACA,OAAO,uBAAuB,gCAAgC;wBAChE;wBAEA,MAAM,SAAS,aAAa,WAAW;wBAEvC,qBACE,6LAAC;4BACC,WAAU;4BACV,MAAM;4BACN,KAAK,IAAI,IAAI,MAAM;4BACnB,QAAQ;4BACP,GAAG,KAAK;;;;;;oBAGf;oBACA,IAAI,CAAC,EAAC,IAAI,EAAE,GAAG,OAAM,iBAAK,6LAAC;4BAAG,WAAU;4BAAuB,GAAG,KAAK;;;;;;oBACvE,IAAI,CAAC,EAAC,IAAI,EAAE,GAAG,OAAM,iBAAK,6LAAC;4BAAG,WAAU;4BAA0B,GAAG,KAAK;;;;;;oBAC1E,IAAI,CAAC,EAAC,IAAI,EAAE,GAAG,OAAM,iBAAK,6LAAC;4BAAG,WAAU;4BAAQ,GAAG,KAAK;;;;;;oBACxD,YAAY,CAAC,EAAC,IAAI,EAAE,GAAG,OAAM,iBAAK,6LAAC;4BAAW,WAAU;4BAAkD,GAAG,KAAK;;;;;;gBACpH;gBACA,eAAe;oBAAC,gJAAA,CAAA,UAAS;iBAAC;0BAEzB;;;;;;;;;;;;AAIT;GA9DM;KAAA;uCAgES", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/TabSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useSearchParams, usePathname, useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport { FiHome, FiUser } from 'react-icons/fi';\nimport Link from 'next/link';\n\ntype TabType = 'clinics' | 'practitioners';\n\ninterface TabSwitcherProps {\n  slug: string;\n  pageType: 'specialities' | 'conditions' | 'categories';\n  clinicCount: number;\n  practitionerCount: number;\n  initialTab?: TabType;\n}\n\n/**\n * Client-side component that handles tab switching between clinics and practitioners\n * This ensures proper re-rendering when the tab changes via client-side navigation\n */\nexport default function TabSwitcher({\n  slug,\n  pageType,\n  clinicCount,\n  practitionerCount,\n  initialTab = 'clinics'\n}: TabSwitcherProps) {\n  const searchParams = useSearchParams();\n  const pathname = usePathname();\n  const router = useRouter();\n  \n  // Get the active tab from URL or use the initial tab\n  const [activeTab, setActiveTab] = useState<TabType>(\n    (searchParams.get('tab') === 'practitioners' ? 'practitioners' : 'clinics') || initialTab\n  );\n\n  // Update the active tab when the URL changes\n  useEffect(() => {\n    const tabParam = searchParams.get('tab');\n    const newActiveTab: TabType = tabParam === 'practitioners' ? 'practitioners' : 'clinics';\n    \n    if (newActiveTab !== activeTab) {\n      setActiveTab(newActiveTab);\n    }\n  }, [searchParams, activeTab]);\n\n  // Function to check if a tab is active\n  const isTabActive = (tabName: TabType) => activeTab === tabName;\n\n  // Handle tab click - programmatically navigate to ensure proper state update\n  const handleTabClick = (tabName: TabType) => (e: React.MouseEvent) => {\n    e.preventDefault();\n    \n    // Create new URL with the selected tab\n    const params = new URLSearchParams(searchParams);\n    params.set('tab', tabName);\n    \n    // Update the URL and state\n    router.push(`/${pageType}/${slug}?${params.toString()}`);\n    setActiveTab(tabName);\n  };\n\n  return (\n    <div className=\"mb-6 border-b border-gray-200\">\n      <nav className=\"-mb-px flex space-x-8\" aria-label=\"Tabs\">\n        <Link\n          href={`/${pageType}/${slug}?tab=clinics`}\n          className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\n            ${isTabActive('clinics')\n              ? 'border-emerald-500 text-emerald-600'\n              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}\n          aria-current={isTabActive('clinics') ? 'page' : undefined}\n          onClick={handleTabClick('clinics')}\n        >\n          <FiHome className=\"h-4 w-4\" />\n          <span>\n            {pageType === 'categories' ? 'Clinics' : 'Related Clinics'} ({clinicCount})\n          </span>\n        </Link>\n        <Link\n          href={`/${pageType}/${slug}?tab=practitioners`}\n          className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\n            ${isTabActive('practitioners')\n              ? 'border-emerald-500 text-emerald-600'\n              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}\n          aria-current={isTabActive('practitioners') ? 'page' : undefined}\n          onClick={handleTabClick('practitioners')}\n        >\n          <FiUser className=\"h-4 w-4\" />\n          <span>\n            {pageType === 'categories' ? 'Practitioners' : 'Related Practitioners'} ({practitionerCount})\n          </span>\n        </Link>\n      </nav>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAqBe,SAAS,YAAY,EAClC,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,iBAAiB,EACjB,aAAa,SAAS,EACL;;IACjB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,qDAAqD;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvC,CAAC,aAAa,GAAG,CAAC,WAAW,kBAAkB,kBAAkB,SAAS,KAAK;IAGjF,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,MAAM,eAAwB,aAAa,kBAAkB,kBAAkB;YAE/E,IAAI,iBAAiB,WAAW;gBAC9B,aAAa;YACf;QACF;gCAAG;QAAC;QAAc;KAAU;IAE5B,uCAAuC;IACvC,MAAM,cAAc,CAAC,UAAqB,cAAc;IAExD,6EAA6E;IAC7E,MAAM,iBAAiB,CAAC,UAAqB,CAAC;YAC5C,EAAE,cAAc;YAEhB,uCAAuC;YACvC,MAAM,SAAS,IAAI,gBAAgB;YACnC,OAAO,GAAG,CAAC,OAAO;YAElB,2BAA2B;YAC3B,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,QAAQ,IAAI;YACvD,aAAa;QACf;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;YAAwB,cAAW;;8BAChD,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,YAAY,CAAC;oBACxC,WAAW,CAAC;YACV,EAAE,YAAY,aACV,wCACA,8EAA8E;oBACpF,gBAAc,YAAY,aAAa,SAAS;oBAChD,SAAS,eAAe;;sCAExB,6LAAC,iJAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;;gCACE,aAAa,eAAe,YAAY;gCAAkB;gCAAG;gCAAY;;;;;;;;;;;;;8BAG9E,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,kBAAkB,CAAC;oBAC9C,WAAW,CAAC;YACV,EAAE,YAAY,mBACV,wCACA,8EAA8E;oBACpF,gBAAc,YAAY,mBAAmB,SAAS;oBACtD,SAAS,eAAe;;sCAExB,6LAAC,iJAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;;gCACE,aAAa,eAAe,kBAAkB;gCAAwB;gCAAG;gCAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAMxG;GA5EwB;;QAOD,qIAAA,CAAA,kBAAe;QACnB,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KATF", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/clinics/ClinicCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { FiMapPin, FiPhone, FiGlobe } from 'react-icons/fi';\r\nimport { MdVerifiedUser } from \"react-icons/md\"; // Import the verified icon\r\n\r\ninterface ClinicCardProps {\r\n  clinic: {\r\n    id: string;\r\n    name: string;\r\n    slug: string;\r\n    isVerified?: boolean;\r\n    description?: string | null;\r\n    logo?: string | null;\r\n    featuredImage?: string | null;\r\n    address: {\r\n      city: string;\r\n      stateProvince: string;\r\n    };\r\n    contactInfo?: {\r\n      phoneNumber?: string;\r\n      websiteUrl?: string;\r\n    } | null;\r\n    // Add all other fields that might be needed for the detail page\r\n    location?: any;\r\n    openingHours?: any;\r\n    services?: any;\r\n    specialties?: any;\r\n    conditions?: any;\r\n    practitioners?: any;\r\n    appointment_options?: any;\r\n    payment_methods?: any;\r\n    seo?: any;\r\n  };\r\n  showContactInfo?: boolean;\r\n  prefetchedData?: boolean; // Flag to indicate if this card has complete data\r\n}\r\n\r\nconst ClinicCard = ({ clinic, showContactInfo = true, prefetchedData = false }: ClinicCardProps) => {\r\n  // Construct the link with state if we have prefetched data\r\n  const linkHref = prefetchedData \r\n    ? { \r\n        pathname: `/clinics/${clinic.slug}`,\r\n        // Pass the clinic data as state to avoid refetching\r\n        query: { prefetched: 'true' } \r\n      }\r\n    : `/clinics/${clinic.slug}`;\r\n  \r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col\">\r\n      <div className=\"p-4 flex-grow\">\r\n        <h3 className=\"text-xl font-semibold text-gray-800 mb-1\">\r\n          <Link \r\n            href={linkHref}\r\n            className=\"hover:text-emerald-600\"\r\n          >\r\n            {clinic.name}\r\n          </Link>\r\n        </h3>\r\n        {/* Verified Badge */}\r\n        {clinic.isVerified && (\r\n          <div className=\"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium\">\r\n            <MdVerifiedUser color=\"#009967\" size={14} />\r\n            <span>VERIFIED</span>\r\n          </div>\r\n        )}\r\n\r\n        {clinic.description && (\r\n          <p className=\"text-gray-600 mb-4 line-clamp-2\">{clinic.description}</p>\r\n        )}\r\n        \r\n        <div className=\"space-y-2 text-sm text-gray-500\">\r\n          {/* Location info */}\r\n          {clinic.address && (\r\n            <div className=\"flex items-center\">\r\n              <FiMapPin className=\"mr-2 text-emerald-500\" />\r\n              <span>{clinic.address.city}, {clinic.address.stateProvince}</span>\r\n            </div>\r\n          )}\r\n          \r\n          {/* Conditionally render contact info */}\r\n          {showContactInfo && clinic.contactInfo?.phoneNumber && (\r\n            <div className=\"flex items-center\">\r\n              <FiPhone className=\"mr-2 text-emerald-500\" />\r\n              <span>{clinic.contactInfo.phoneNumber}</span> \r\n            </div>\r\n          )}\r\n          \r\n          {showContactInfo && clinic.contactInfo?.websiteUrl && (\r\n            <div className=\"flex items-center\">\r\n              <FiGlobe className=\"mr-2 text-emerald-500\" />\r\n              <a\r\n                href={clinic.contactInfo.websiteUrl}\r\n                target=\"_blank\"\r\n                rel=\"nofollow noopener noreferrer\"\r\n                className=\"hover:text-emerald-600\"\r\n              >\r\n                Visit Website\r\n              </a>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Ensure footer stays at the bottom */}\r\n      <div className=\"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto\"> \r\n        <Link \r\n          href={linkHref}\r\n          className=\"text-emerald-600 hover:text-emerald-700 font-medium text-sm\"\r\n        >\r\n          View Details →\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ClinicCard;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA,8PAAiD,2BAA2B;AAL5E;;;;;AAuCA,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,kBAAkB,IAAI,EAAE,iBAAiB,KAAK,EAAmB;IAC7F,2DAA2D;IAC3D,MAAM,WAAW,iBACb;QACE,UAAU,CAAC,SAAS,EAAE,OAAO,IAAI,EAAE;QACnC,oDAAoD;QACpD,OAAO;YAAE,YAAY;QAAO;IAC9B,IACA,CAAC,SAAS,EAAE,OAAO,IAAI,EAAE;IAE7B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM;4BACN,WAAU;sCAET,OAAO,IAAI;;;;;;;;;;;oBAIf,OAAO,UAAU,kBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,iBAAc;gCAAC,OAAM;gCAAU,MAAM;;;;;;0CACtC,6LAAC;0CAAK;;;;;;;;;;;;oBAIT,OAAO,WAAW,kBACjB,6LAAC;wBAAE,WAAU;kCAAmC,OAAO,WAAW;;;;;;kCAGpE,6LAAC;wBAAI,WAAU;;4BAEZ,OAAO,OAAO,kBACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;;4CAAM,OAAO,OAAO,CAAC,IAAI;4CAAC;4CAAG,OAAO,OAAO,CAAC,aAAa;;;;;;;;;;;;;4BAK7D,mBAAmB,OAAO,WAAW,EAAE,6BACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAM,OAAO,WAAW,CAAC,WAAW;;;;;;;;;;;;4BAIxC,mBAAmB,OAAO,WAAW,EAAE,4BACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCACC,MAAM,OAAO,WAAW,CAAC,UAAU;wCACnC,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAM;oBACN,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;KA7EM;uCA+ES", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/clientCache.ts"], "sourcesContent": ["/**\n * Client-side caching utilities\n * This file provides functions to store and retrieve data on the client side\n */\n\n// Define a type for the cache entry\ntype CacheEntry<T> = {\n  data: T;\n  timestamp: number;\n  expiry: number;\n};\n\n// Define a type for the cache\ntype Cache = {\n  [key: string]: CacheEntry<any>;\n};\n\n// Initialize the cache\nlet cache: Cache = {};\n\n/**\n * Set a value in the cache\n * @param key The key to store the value under\n * @param value The value to store\n * @param ttl Time to live in milliseconds (default: 5 minutes)\n */\nexport function setCacheValue<T>(key: string, value: T, ttl: number = 5 * 60 * 1000): void {\n  if (typeof window === 'undefined') {\n    return; // Don't run on the server\n  }\n  \n  const now = Date.now();\n  cache[key] = {\n    data: value,\n    timestamp: now,\n    expiry: now + ttl,\n  };\n  \n  // Also store in sessionStorage for persistence across page navigations\n  try {\n    sessionStorage.setItem(\n      `cache_${key}`,\n      JSON.stringify({\n        data: value,\n        timestamp: now,\n        expiry: now + ttl,\n      })\n    );\n  } catch (error) {\n    console.error('Error storing data in sessionStorage:', error);\n  }\n}\n\n/**\n * Get a value from the cache\n * @param key The key to retrieve the value for\n * @returns The value, or null if not found or expired\n */\nexport function getCacheValue<T>(key: string): T | null {\n  if (typeof window === 'undefined') {\n    return null; // Don't run on the server\n  }\n  \n  // First check the in-memory cache\n  const entry = cache[key];\n  const now = Date.now();\n  \n  if (entry && entry.expiry > now) {\n    return entry.data as T;\n  }\n  \n  // If not in memory or expired, check sessionStorage\n  try {\n    const storedEntry = sessionStorage.getItem(`cache_${key}`);\n    if (storedEntry) {\n      const parsed = JSON.parse(storedEntry) as CacheEntry<T>;\n      if (parsed.expiry > now) {\n        // Restore to in-memory cache and return\n        cache[key] = parsed;\n        return parsed.data;\n      } else {\n        // Remove expired entry\n        sessionStorage.removeItem(`cache_${key}`);\n      }\n    }\n  } catch (error) {\n    console.error('Error retrieving data from sessionStorage:', error);\n  }\n  \n  return null;\n}\n\n/**\n * Clear a specific value from the cache\n * @param key The key to clear\n */\nexport function clearCacheValue(key: string): void {\n  if (typeof window === 'undefined') {\n    return; // Don't run on the server\n  }\n  \n  delete cache[key];\n  try {\n    sessionStorage.removeItem(`cache_${key}`);\n  } catch (error) {\n    console.error('Error removing data from sessionStorage:', error);\n  }\n}\n\n/**\n * Clear all values from the cache\n */\nexport function clearCache(): void {\n  if (typeof window === 'undefined') {\n    return; // Don't run on the server\n  }\n  \n  cache = {};\n  try {\n    // Only clear our cache entries, not all sessionStorage\n    Object.keys(sessionStorage).forEach(key => {\n      if (key.startsWith('cache_')) {\n        sessionStorage.removeItem(key);\n      }\n    });\n  } catch (error) {\n    console.error('Error clearing data from sessionStorage:', error);\n  }\n}\n\n/**\n * Store practitioner data in the cache\n * @param practitioner The practitioner data to store\n */\nexport function cachePractitioner(practitioner: any): void {\n  if (!practitioner || !practitioner.id || !practitioner.slug) {\n    return; // Don't cache invalid data\n  }\n  \n  // Check if we already have this practitioner in the cache\n  const existingData = getCacheValue(`practitioner_${practitioner.slug}`);\n  \n  // Only update the cache if:\n  // 1. We don't have this practitioner cached yet, or\n  // 2. The new data has the _hasDetailedData flag and the existing data doesn't\n  if (!existingData || \n      (practitioner._hasDetailedData && (!existingData._hasDetailedData))) {\n    setCacheValue(`practitioner_${practitioner.slug}`, practitioner, 30 * 60 * 1000); // 30 minutes\n  }\n}\n\n/**\n * Get practitioner data from the cache\n * @param slug The practitioner slug\n * @returns The practitioner data, or null if not found or expired\n */\nexport function getCachedPractitioner(slug: string): any | null {\n  return getCacheValue(`practitioner_${slug}`);\n}"], "names": [], "mappings": "AAAA;;;CAGC,GAED,oCAAoC;;;;;;;;;AAYpC,uBAAuB;AACvB,IAAI,QAAe,CAAC;AAQb,SAAS,cAAiB,GAAW,EAAE,KAAQ,EAAE,MAAc,IAAI,KAAK,IAAI;IACjF,uCAAmC;;IAEnC;IAEA,MAAM,MAAM,KAAK,GAAG;IACpB,KAAK,CAAC,IAAI,GAAG;QACX,MAAM;QACN,WAAW;QACX,QAAQ,MAAM;IAChB;IAEA,uEAAuE;IACvE,IAAI;QACF,eAAe,OAAO,CACpB,CAAC,MAAM,EAAE,KAAK,EACd,KAAK,SAAS,CAAC;YACb,MAAM;YACN,WAAW;YACX,QAAQ,MAAM;QAChB;IAEJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;IACzD;AACF;AAOO,SAAS,cAAiB,GAAW;IAC1C,uCAAmC;;IAEnC;IAEA,kCAAkC;IAClC,MAAM,QAAQ,KAAK,CAAC,IAAI;IACxB,MAAM,MAAM,KAAK,GAAG;IAEpB,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK;QAC/B,OAAO,MAAM,IAAI;IACnB;IAEA,oDAAoD;IACpD,IAAI;QACF,MAAM,cAAc,eAAe,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK;QACzD,IAAI,aAAa;YACf,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,IAAI,OAAO,MAAM,GAAG,KAAK;gBACvB,wCAAwC;gBACxC,KAAK,CAAC,IAAI,GAAG;gBACb,OAAO,OAAO,IAAI;YACpB,OAAO;gBACL,uBAAuB;gBACvB,eAAe,UAAU,CAAC,CAAC,MAAM,EAAE,KAAK;YAC1C;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;IAC9D;IAEA,OAAO;AACT;AAMO,SAAS,gBAAgB,GAAW;IACzC,uCAAmC;;IAEnC;IAEA,OAAO,KAAK,CAAC,IAAI;IACjB,IAAI;QACF,eAAe,UAAU,CAAC,CAAC,MAAM,EAAE,KAAK;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;IAC5D;AACF;AAKO,SAAS;IACd,uCAAmC;;IAEnC;IAEA,QAAQ,CAAC;IACT,IAAI;QACF,uDAAuD;QACvD,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,CAAA;YAClC,IAAI,IAAI,UAAU,CAAC,WAAW;gBAC5B,eAAe,UAAU,CAAC;YAC5B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;IAC5D;AACF;AAMO,SAAS,kBAAkB,YAAiB;IACjD,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE;QAC3D,QAAQ,2BAA2B;IACrC;IAEA,0DAA0D;IAC1D,MAAM,eAAe,cAAc,CAAC,aAAa,EAAE,aAAa,IAAI,EAAE;IAEtE,4BAA4B;IAC5B,oDAAoD;IACpD,8EAA8E;IAC9E,IAAI,CAAC,gBACA,aAAa,gBAAgB,IAAK,CAAC,aAAa,gBAAgB,EAAI;QACvE,cAAc,CAAC,aAAa,EAAE,aAAa,IAAI,EAAE,EAAE,cAAc,KAAK,KAAK,OAAO,aAAa;IACjG;AACF;AAOO,SAAS,sBAAsB,IAAY;IAChD,OAAO,cAAc,CAAC,aAAa,EAAE,MAAM;AAC7C", "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/practitioners/PractitionerCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link';\r\nimport { <PERSON>U<PERSON>, FiAward } from 'react-icons/fi';\r\nimport { MdVerifiedUser } from \"react-icons/md\";\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport { getCachedPractitioner, cachePractitioner } from '@/lib/clientCache';\r\n\r\ninterface PractitionerCardProps {\r\n  practitioner: {\r\n    id: string;\r\n    name: string;\r\n    slug: string;\r\n    isVerified?: boolean;\r\n    bio?: string | null;\r\n    profilePicture?: string | null;\r\n    qualifications?: string | null;\r\n    contactInfo?: { \r\n      phoneNumber?: string;\r\n      emailAddress?: string;\r\n    } | null;\r\n    // Add all other fields that might be needed for the detail page\r\n    title?: string | null;\r\n    videoEmbed?: any;\r\n    specialties?: any;\r\n    conditions?: any;\r\n    education?: any;\r\n    clinics?: any;\r\n    seo?: any;\r\n    _hasDetailedData?: boolean; // Flag from server prefetching\r\n  };\r\n  prefetchedData?: boolean; // Flag to indicate if this card has complete data\r\n}\r\n\r\nconst PractitionerCard = ({ practitioner, prefetchedData = false }: PractitionerCardProps) => {\r\n  // Check if we have this practitioner in the client-side cache\r\n  const [isCached, setIsCached] = useState(false);\r\n  const hasCached = useRef(false);\r\n  \r\n  useEffect(() => {\r\n    // Only run this effect once per component instance\r\n    if (hasCached.current) return;\r\n    \r\n    // Check if this practitioner is in the client-side cache\r\n    const cachedData = getCachedPractitioner(practitioner.slug);\r\n    if (cachedData) {\r\n      setIsCached(true);\r\n    }\r\n    \r\n    // If we have server-prefetched data, store it in the client cache\r\n    if (practitioner._hasDetailedData && !cachedData) {\r\n      cachePractitioner(practitioner);\r\n      setIsCached(true);\r\n    }\r\n    \r\n    hasCached.current = true;\r\n  }, [practitioner]);\r\n  \r\n  // Determine if we should use the prefetched flag\r\n  // We'll use it if we have server-prefetched data or client-cached data\r\n  const hasPrefetchedData = prefetchedData || practitioner._hasDetailedData || isCached;\r\n  \r\n  // Construct the link with state if we have prefetched data\r\n  const linkHref = hasPrefetchedData \r\n    ? { \r\n        pathname: `/practitioners/${practitioner.slug}`,\r\n        // Pass the practitioner data as state to avoid refetching\r\n        query: { prefetched: 'true' } \r\n      }\r\n    : `/practitioners/${practitioner.slug}`;\r\n  \r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col\"> \r\n      <div className=\"p-4 flex-grow\">\r\n        <h3 className=\"text-xl font-semibold text-gray-800 mb-1\">\r\n          <Link href={linkHref} className=\"hover:text-emerald-600\">\r\n            {practitioner.name}\r\n          </Link>\r\n        </h3>\r\n        {/* Verified Badge */}\r\n        {practitioner.isVerified && (\r\n          <div className=\"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium\">\r\n            <MdVerifiedUser color=\"#009967\" size={14} />\r\n            <span>VERIFIED</span>\r\n          </div>\r\n        )}\r\n        \r\n        {practitioner.bio && (\r\n          <p className=\"text-gray-600 mb-4 line-clamp-3\">{practitioner.bio}</p>\r\n        )}\r\n      </div>\r\n      \r\n      {/* Ensure footer stays at the bottom */}\r\n      <div className=\"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto\"> \r\n        <Link \r\n          href={linkHref}\r\n          className=\"text-emerald-600 hover:text-emerald-700 font-medium text-sm\"\r\n        >\r\n          View Profile →\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PractitionerCard;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;;;AANA;;;;;AAkCA,MAAM,mBAAmB,CAAC,EAAE,YAAY,EAAE,iBAAiB,KAAK,EAAyB;;IACvF,8DAA8D;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,mDAAmD;YACnD,IAAI,UAAU,OAAO,EAAE;YAEvB,yDAAyD;YACzD,MAAM,aAAa,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,IAAI;YAC1D,IAAI,YAAY;gBACd,YAAY;YACd;YAEA,kEAAkE;YAClE,IAAI,aAAa,gBAAgB,IAAI,CAAC,YAAY;gBAChD,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;gBAClB,YAAY;YACd;YAEA,UAAU,OAAO,GAAG;QACtB;qCAAG;QAAC;KAAa;IAEjB,iDAAiD;IACjD,uEAAuE;IACvE,MAAM,oBAAoB,kBAAkB,aAAa,gBAAgB,IAAI;IAE7E,2DAA2D;IAC3D,MAAM,WAAW,oBACb;QACE,UAAU,CAAC,eAAe,EAAE,aAAa,IAAI,EAAE;QAC/C,0DAA0D;QAC1D,OAAO;YAAE,YAAY;QAAO;IAC9B,IACA,CAAC,eAAe,EAAE,aAAa,IAAI,EAAE;IAEzC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAU,WAAU;sCAC7B,aAAa,IAAI;;;;;;;;;;;oBAIrB,aAAa,UAAU,kBACtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,iBAAc;gCAAC,OAAM;gCAAU,MAAM;;;;;;0CACtC,6LAAC;0CAAK;;;;;;;;;;;;oBAIT,aAAa,GAAG,kBACf,6LAAC;wBAAE,WAAU;kCAAmC,aAAa,GAAG;;;;;;;;;;;;0BAKpE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAM;oBACN,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;GArEM;KAAA;uCAuES", "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/ClientPagination.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/outline';\r\nimport clsx from 'clsx';\r\nimport { usePathname, useSearchParams } from 'next/navigation';\r\nimport { useCallback } from 'react';\r\n\r\n// Helper function to generate pagination numbers (can be moved to utils later)\r\nconst generatePagination = (currentPage: number, totalPages: number): (number | string)[] => {\r\n  // If the total number of pages is 7 or less,\r\n  // display all pages without any ellipsis.\r\n  if (totalPages <= 7) {\r\n    return Array.from({ length: totalPages }, (_, i) => i + 1);\r\n  }\r\n\r\n  // If the current page is among the first 3 pages,\r\n  // show the first 3, an ellipsis, and the last 2 pages.\r\n  if (currentPage <= 3) {\r\n    return [1, 2, 3, '...', totalPages - 1, totalPages];\r\n  }\r\n\r\n  // If the current page is among the last 3 pages,\r\n  // show the first 2, an ellipsis, and the last 3 pages.\r\n  if (currentPage >= totalPages - 2) {\r\n    return [1, 2, '...', totalPages - 2, totalPages - 1, totalPages];\r\n  }\r\n\r\n  // If the current page is somewhere in the middle,\r\n  // show the first page, an ellipsis, the current page and its neighbors,\r\n  // another ellipsis, and the last page.\r\n  return [\r\n    1,\r\n    '...',\r\n    currentPage - 1,\r\n    currentPage,\r\n    currentPage + 1,\r\n    '...',\r\n    totalPages,\r\n  ];\r\n};\r\n\r\nexport default function ClientPagination({ totalPages }: { totalPages: number }) {\r\n  const pathname = usePathname();\r\n  const searchParams = useSearchParams();\r\n  const currentPage = Number(searchParams.get('page')) || 1;\r\n\r\n  const createPageURL = useCallback((pageNumber: number | string) => {\r\n    const params = new URLSearchParams(searchParams.toString());\r\n    params.set('page', pageNumber.toString());\r\n    return `${pathname}?${params.toString()}`;\r\n  }, [pathname, searchParams]);\r\n\r\n  const handlePageChange = useCallback((pageNumber: number | string) => {\r\n    if (pageNumber === '...') return;\r\n    \r\n    const params = new URLSearchParams(searchParams.toString());\r\n    params.set('page', pageNumber.toString());\r\n    \r\n    // Update URL without full page reload\r\n    window.history.pushState(null, '', `${pathname}?${params.toString()}`);\r\n    \r\n    // Dispatch a custom event that the page component can listen for\r\n    window.dispatchEvent(new CustomEvent('paginationchange', { \r\n      detail: { page: pageNumber } \r\n    }));\r\n  }, [pathname, searchParams]);\r\n\r\n  const allPages = generatePagination(currentPage, totalPages);\r\n\r\n  // If there's only one page, don't render pagination\r\n  if (totalPages <= 1) {\r\n    return null; \r\n  }\r\n\r\n  return (\r\n    <div className=\"inline-flex\">\r\n      <PaginationArrow\r\n        direction=\"left\"\r\n        href={createPageURL(currentPage - 1)}\r\n        onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}\r\n        isDisabled={currentPage <= 1}\r\n      />\r\n\r\n      <div className=\"flex -space-x-px\">\r\n        {allPages.map((page, index) => {\r\n          let position: 'first' | 'last' | 'single' | 'middle' | undefined;\r\n\r\n          if (index === 0) position = 'first';\r\n          if (index === allPages.length - 1) position = 'last';\r\n          if (allPages.length === 1) position = 'single';\r\n          if (page === '...') position = 'middle';\r\n\r\n          return (\r\n            <PaginationNumber\r\n              key={`${page}-${index}`} // Use index for unique key with ellipsis\r\n              page={page}\r\n              href={createPageURL(page)}\r\n              onClick={() => handlePageChange(page)}\r\n              position={position}\r\n              isActive={currentPage === page}\r\n            />\r\n          );\r\n        })}\r\n      </div>\r\n\r\n      <PaginationArrow\r\n        direction=\"right\"\r\n        href={createPageURL(currentPage + 1)}\r\n        onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}\r\n        isDisabled={currentPage >= totalPages}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction PaginationNumber({\r\n  page,\r\n  href,\r\n  onClick,\r\n  isActive,\r\n  position,\r\n}: {\r\n  page: number | string;\r\n  href: string;\r\n  onClick: () => void;\r\n  position?: 'first' | 'last' | 'middle' | 'single';\r\n  isActive: boolean;\r\n}) {\r\n  const className = clsx(\r\n    'flex h-10 w-10 items-center justify-center text-sm border',\r\n    {\r\n      'rounded-l-md': position === 'first' || position === 'single',\r\n      'rounded-r-md': position === 'last' || position === 'single',\r\n      'z-10 bg-emerald-600 border-emerald-600 text-white': isActive,\r\n      'hover:bg-gray-100': !isActive && position !== 'middle',\r\n      'text-gray-300 pointer-events-none': position === 'middle', // Disable clicks on ellipsis\r\n    },\r\n  );\r\n\r\n  return isActive || position === 'middle' ? (\r\n    <div className={className}>{page}</div>\r\n  ) : (\r\n    <button onClick={onClick} className={className}>\r\n      {page}\r\n    </button>\r\n  );\r\n}\r\n\r\nfunction PaginationArrow({\r\n  href,\r\n  onClick,\r\n  direction,\r\n  isDisabled,\r\n}: {\r\n  href: string;\r\n  onClick: () => void;\r\n  direction: 'left' | 'right';\r\n  isDisabled?: boolean;\r\n}) {\r\n  const className = clsx(\r\n    'flex h-10 w-10 items-center justify-center rounded-md border',\r\n    {\r\n      'pointer-events-none text-gray-300': isDisabled,\r\n      'hover:bg-gray-100': !isDisabled,\r\n      'mr-2 md:mr-4': direction === 'left',\r\n      'ml-2 md:ml-4': direction === 'right',\r\n    },\r\n  );\r\n\r\n  const icon =\r\n    direction === 'left' ? (\r\n      <ArrowLeftIcon className=\"w-4\" />\r\n    ) : (\r\n      <ArrowRightIcon className=\"w-4\" />\r\n    );\r\n\r\n  return isDisabled ? (\r\n    <div className={className}>{icon}</div>\r\n  ) : (\r\n    <button onClick={onClick} className={className}>\r\n      {icon}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAOA,+EAA+E;AAC/E,MAAM,qBAAqB,CAAC,aAAqB;IAC/C,6CAA6C;IAC7C,0CAA0C;IAC1C,IAAI,cAAc,GAAG;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAW,GAAG,CAAC,GAAG,IAAM,IAAI;IAC1D;IAEA,kDAAkD;IAClD,uDAAuD;IACvD,IAAI,eAAe,GAAG;QACpB,OAAO;YAAC;YAAG;YAAG;YAAG;YAAO,aAAa;YAAG;SAAW;IACrD;IAEA,iDAAiD;IACjD,uDAAuD;IACvD,IAAI,eAAe,aAAa,GAAG;QACjC,OAAO;YAAC;YAAG;YAAG;YAAO,aAAa;YAAG,aAAa;YAAG;SAAW;IAClE;IAEA,kDAAkD;IAClD,wEAAwE;IACxE,uCAAuC;IACvC,OAAO;QACL;QACA;QACA,cAAc;QACd;QACA,cAAc;QACd;QACA;KACD;AACH;AAEe,SAAS,iBAAiB,EAAE,UAAU,EAA0B;;IAC7E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,OAAO,aAAa,GAAG,CAAC,YAAY;IAExD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACjC,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;YACxD,OAAO,GAAG,CAAC,QAAQ,WAAW,QAAQ;YACtC,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,QAAQ,IAAI;QAC3C;sDAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACpC,IAAI,eAAe,OAAO;YAE1B,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;YACxD,OAAO,GAAG,CAAC,QAAQ,WAAW,QAAQ;YAEtC,sCAAsC;YACtC,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,GAAG,SAAS,CAAC,EAAE,OAAO,QAAQ,IAAI;YAErE,iEAAiE;YACjE,OAAO,aAAa,CAAC,IAAI,YAAY,oBAAoB;gBACvD,QAAQ;oBAAE,MAAM;gBAAW;YAC7B;QACF;yDAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,WAAW,mBAAmB,aAAa;IAEjD,oDAAoD;IACpD,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,MAAM,cAAc,cAAc;gBAClC,SAAS,IAAM,cAAc,KAAK,iBAAiB,cAAc;gBACjE,YAAY,eAAe;;;;;;0BAG7B,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,MAAM;oBACnB,IAAI;oBAEJ,IAAI,UAAU,GAAG,WAAW;oBAC5B,IAAI,UAAU,SAAS,MAAM,GAAG,GAAG,WAAW;oBAC9C,IAAI,SAAS,MAAM,KAAK,GAAG,WAAW;oBACtC,IAAI,SAAS,OAAO,WAAW;oBAE/B,qBACE,6LAAC;wBAEC,MAAM;wBACN,MAAM,cAAc;wBACpB,SAAS,IAAM,iBAAiB;wBAChC,UAAU;wBACV,UAAU,gBAAgB;uBALrB,GAAG,KAAK,CAAC,EAAE,OAAO;;;;;gBAQ7B;;;;;;0BAGF,6LAAC;gBACC,WAAU;gBACV,MAAM,cAAc,cAAc;gBAClC,SAAS,IAAM,cAAc,cAAc,iBAAiB,cAAc;gBAC1E,YAAY,eAAe;;;;;;;;;;;;AAInC;GAxEwB;;QACL,qIAAA,CAAA,cAAW;QACP,qIAAA,CAAA,kBAAe;;;KAFd;AA0ExB,SAAS,iBAAiB,EACxB,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,QAAQ,EAOT;IACC,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACnB,6DACA;QACE,gBAAgB,aAAa,WAAW,aAAa;QACrD,gBAAgB,aAAa,UAAU,aAAa;QACpD,qDAAqD;QACrD,qBAAqB,CAAC,YAAY,aAAa;QAC/C,qCAAqC,aAAa;IACpD;IAGF,OAAO,YAAY,aAAa,yBAC9B,6LAAC;QAAI,WAAW;kBAAY;;;;;6BAE5B,6LAAC;QAAO,SAAS;QAAS,WAAW;kBAClC;;;;;;AAGP;MA/BS;AAiCT,SAAS,gBAAgB,EACvB,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EAMX;IACC,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACnB,gEACA;QACE,qCAAqC;QACrC,qBAAqB,CAAC;QACtB,gBAAgB,cAAc;QAC9B,gBAAgB,cAAc;IAChC;IAGF,MAAM,OACJ,cAAc,uBACZ,6LAAC,4NAAA,CAAA,gBAAa;QAAC,WAAU;;;;;6BAEzB,6LAAC,8NAAA,CAAA,iBAAc;QAAC,WAAU;;;;;;IAG9B,OAAO,2BACL,6LAAC;QAAI,WAAW;kBAAY;;;;;6BAE5B,6LAAC;QAAO,SAAS;QAAS,WAAW;kBAClC;;;;;;AAGP;MAnCS", "debugId": null}}, {"offset": {"line": 1208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/TabContent.tsx"], "sourcesContent": ["'use client';\n\nimport { useSearchParams } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport ClinicCard from '@/components/clinics/ClinicCard';\nimport PractitionerCard from '@/components/practitioners/PractitionerCard';\nimport ClientPagination from '@/components/shared/ClientPagination';\n\ninterface TransformedClinic {\n  id: string;\n  name: string;\n  slug: string;\n  description?: string | null;\n  logo: string | null;\n  featuredImage: string | null;\n  address: {\n    city: string;\n    stateProvince: string;\n  };\n  contactInfo?: {\n    phoneNumber?: string;\n    websiteUrl?: string;\n  } | null;\n  isVerified?: boolean;\n}\n\ninterface TransformedPractitioner {\n  id: string;\n  name: string;\n  slug: string;\n  title?: string | null;\n  qualifications?: string | null;\n  profilePicture: string | null;\n  isVerified?: boolean;\n}\n\ntype TabType = 'clinics' | 'practitioners';\n\ninterface TabContentProps {\n  clinics: TransformedClinic[];\n  practitioners: TransformedPractitioner[];\n  totalPages: number;\n  initialTab?: TabType;\n}\n\n/**\n * Client-side component that displays the content based on the active tab\n * This ensures proper re-rendering when the tab changes via client-side navigation\n */\nexport default function TabContent({\n  clinics,\n  practitioners,\n  totalPages,\n  initialTab = 'clinics'\n}: TabContentProps) {\n  const searchParams = useSearchParams();\n  \n  // Get the active tab from URL or use the initial tab\n  const [activeTab, setActiveTab] = useState<TabType>(\n    (searchParams.get('tab') === 'practitioners' ? 'practitioners' : 'clinics') || initialTab\n  );\n\n  // Update the active tab when the URL changes\n  useEffect(() => {\n    const tabParam = searchParams.get('tab');\n    const newActiveTab: TabType = tabParam === 'practitioners' ? 'practitioners' : 'clinics';\n    \n    if (newActiveTab !== activeTab) {\n      setActiveTab(newActiveTab);\n    }\n  }, [searchParams, activeTab]);\n\n  return (\n    <div key={activeTab}>\n      {activeTab === 'clinics' ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {clinics.length > 0 ? (\n            clinics.map((clinic) => (\n              <ClinicCard key={clinic.id} clinic={clinic} showContactInfo={false} />\n            ))\n          ) : (\n            <div className=\"col-span-full text-center py-8\">\n              <p className=\"text-gray-500\">No clinics found matching your criteria.</p>\n            </div>\n          )}\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {practitioners.length > 0 ? (\n            practitioners.map((practitioner) => (\n              <PractitionerCard key={practitioner.id} practitioner={practitioner} />\n            ))\n          ) : (\n            <div className=\"col-span-full text-center py-8\">\n              <p className=\"text-gray-500\">No practitioners found matching your criteria.</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"mt-8 flex justify-center\">\n          <ClientPagination totalPages={totalPages} />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAiDe,SAAS,WAAW,EACjC,OAAO,EACP,aAAa,EACb,UAAU,EACV,aAAa,SAAS,EACN;;IAChB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAEnC,qDAAqD;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvC,CAAC,aAAa,GAAG,CAAC,WAAW,kBAAkB,kBAAkB,SAAS,KAAK;IAGjF,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,MAAM,eAAwB,aAAa,kBAAkB,kBAAkB;YAE/E,IAAI,iBAAiB,WAAW;gBAC9B,aAAa;YACf;QACF;+BAAG;QAAC;QAAc;KAAU;IAE5B,qBACE,6LAAC;;YACE,cAAc,0BACb,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,MAAM,GAAG,IAChB,QAAQ,GAAG,CAAC,CAAC,uBACX,6LAAC,8IAAA,CAAA,UAAU;wBAAiB,QAAQ;wBAAQ,iBAAiB;uBAA5C,OAAO,EAAE;;;;8CAG5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;qCAKnC,6LAAC;gBAAI,WAAU;0BACZ,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,6BACjB,6LAAC,0JAAA,CAAA,UAAgB;wBAAuB,cAAc;uBAA/B,aAAa,EAAE;;;;8CAGxC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;YAOpC,aAAa,mBACZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mJAAA,CAAA,UAAgB;oBAAC,YAAY;;;;;;;;;;;;OA9B1B;;;;;AAmCd;GA3DwB;;QAMD,qIAAA,CAAA,kBAAe;;;KANd", "debugId": null}}]}