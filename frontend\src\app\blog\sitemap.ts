import { MetadataRoute } from 'next';
import { getStrapiContent } from '@/lib/strapi';

// Define the site URL from environment variable
// We need an absolute URL for sitemaps to work properly
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_API_URL?.replace('.strapiapp.com', '') || 'https://naturalhealingnow.vercel.app';

// Ensure SITE_URL doesn't have a trailing slash
const normalizedSiteUrl = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;

// Define interfaces for Strapi data types
interface StrapiItem {
  id?: string | number;
  documentId?: string;
  attributes?: {
    slug?: string;
    updatedAt?: string;
    publishedAt?: string;
    [key: string]: any;
  };
  slug?: string;
  updatedAt?: string;
  publishedAt?: string;
  [key: string]: any;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    console.log('Generating blog sitemap...');
    
    // Fetch blog posts
    const blogPostsResponse = await getStrapiContent.blog.getPosts({
      pagination: { pageSize: 1000 }
    });
    
    const blogPosts = blogPostsResponse?.data || [];
    
    // Generate sitemap entries for blog posts
    const blogPostEntries = blogPosts.map((post: StrapiItem) => {
      const attributes = post.attributes || post;
      const slug = attributes.slug || post.slug;
      const updatedAt = attributes.updatedAt || post.updatedAt || new Date().toISOString();
      
      if (!slug) return null;
      
      return {
        url: `${normalizedSiteUrl}/blog/${slug}`,
        lastModified: new Date(updatedAt),
        changeFrequency: 'weekly',
        priority: 0.7,
      };
    }).filter(Boolean) as MetadataRoute.Sitemap;
    
    // Fetch blog categories
    const blogCategoriesResponse = await getStrapiContent.blog.getCategories({
      pagination: { pageSize: 100 }
    });
    
    const blogCategories = blogCategoriesResponse?.data || [];
    
    const blogCategoryEntries = blogCategories.map((category: StrapiItem) => {
      const slug = category.attributes?.slug || category.slug;
      
      if (!slug) return null;
      
      return {
        url: `${normalizedSiteUrl}/blog/categories/${slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.6,
      };
    }).filter(Boolean) as MetadataRoute.Sitemap;
    
    // Fetch blog tags
    const blogTagsResponse = await getStrapiContent.blog.getTags();
    const blogTags = blogTagsResponse?.data || [];
    
    const blogTagEntries = blogTags.map((tag: StrapiItem) => {
      const slug = tag.attributes?.slug || tag.slug;
      
      if (!slug) return null;
      
      return {
        url: `${normalizedSiteUrl}/blog/tags/${slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.6,
      };
    }).filter(Boolean) as MetadataRoute.Sitemap;
    
    // Combine all blog-related entries
    return [
      {
        url: `${normalizedSiteUrl}/blog`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      ...blogPostEntries,
      ...blogCategoryEntries,
      ...blogTagEntries,
    ];
  } catch (error) {
    console.error('Error generating blog sitemap:', error);
    
    // Return only the main blog URL if there's an error
    return [
      {
        url: `${normalizedSiteUrl}/blog`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
    ];
  }
}
