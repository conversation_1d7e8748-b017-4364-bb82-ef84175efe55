(()=>{var e={};e.id=3648,e.ids=[3648],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7610:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},10592:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\SearchInput.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\SearchInput.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},49384:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=function(){for(var e,t,r=0,n="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,n,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(s&&(s+=" "),s+=n)}else for(n in t)t[n]&&(s&&(s+=" "),s+=n);return s}(e))&&(n&&(n+=" "),n+=t);return n}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57554:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(37413),s=r(4536),a=r.n(s),i=r(58446),o=r(10592),l=r(65646),c=r(71397);function u(e){return e&&e.id?{id:e.id,name:e.name,slug:e.slug,description:e.description||null,count:e.blog_posts?.length||0}:(console.warn("Received invalid category data:",e),{id:e?.id||"",name:"Invalid Category Data",slug:`invalid-category-${e?.id||"unknown"}`,description:null,count:0})}async function d({searchParams:e}){let t=await e,r=t?.query||"",s=Number(t?.page)||1,d=[],m=1;try{let e=await i.$.blog.getCategories({filters:r?{name:{$containsi:r}}:void 0,pagination:{page:s,pageSize:9},populate:["blog_posts"]});e&&Array.isArray(e.data)?(d=e.data.map(u),m=e.meta?.pagination?.pageCount||1):console.error("Invalid categoriesResponse structure:",e)}catch(e){console.error("Error fetching blog categories:",e)}return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.default,{defaultTitle:"Blog Categories | Natural Healing Now",defaultDescription:"Explore our blog categories to find articles on specific holistic health topics and natural healing approaches."}),(0,n.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Blog Categories"}),(0,n.jsx)("p",{className:"text-lg max-w-3xl",children:"Explore our articles by category to find information on specific holistic health topics."})]})}),(0,n.jsx)("div",{className:"bg-white shadow-md",children:(0,n.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,n.jsx)("div",{className:"max-w-md mx-auto",children:(0,n.jsx)(o.default,{placeholder:"Search categories"})})})}),(0,n.jsx)("div",{className:"py-12 bg-gray-50",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-8",children:"All Categories"}),(0,n.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:d.length>0?d.map(e=>(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow",children:[(0,n.jsx)("div",{className:"h-40 bg-emerald-100 flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-emerald-700 text-2xl font-semibold",children:e.name.charAt(0)})}),(0,n.jsxs)("div",{className:"p-5",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:(0,n.jsx)(a(),{href:`/blog/categories/${e.slug}`,className:"hover:text-emerald-600",children:e.name})}),(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.description||`Articles about ${e.name.toLowerCase()}`}),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("span",{className:"text-sm text-gray-500",children:[e.count," ",1===e.count?"article":"articles"]}),(0,n.jsx)(a(),{href:`/blog/categories/${e.slug}`,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Articles →"})]})]})]},e.id)):(0,n.jsx)("div",{className:"col-span-3 text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:r?`No categories found matching "${r}".`:"No categories found. Check back soon!"})})}),m>1&&(0,n.jsx)("div",{className:"mt-12 flex justify-center",children:(0,n.jsx)(l.default,{totalPages:m})})]})}),(0,n.jsx)("div",{className:"py-8 bg-white",children:(0,n.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,n.jsx)(a(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"← Back to Blog"})})})]})}},62209:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,71397)),Promise.resolve().then(r.bind(r,65646)),Promise.resolve().then(r.bind(r,10592))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65646:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\Pagination.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\Pagination.tsx","default")},68016:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(60687),s=r(43210),a=r(16189),i=r(17019);function o({placeholder:e,paramName:t="query",icon:r}){let o=(0,a.useSearchParams)(),l=(0,a.usePathname)(),{replace:c}=(0,a.useRouter)(),u=function(e,t,r){var n=this,a=(0,s.useRef)(null),i=(0,s.useRef)(0),o=(0,s.useRef)(null),l=(0,s.useRef)([]),c=(0,s.useRef)(),u=(0,s.useRef)(),d=(0,s.useRef)(e),m=(0,s.useRef)(!0);d.current=e;var p="undefined"!=typeof window,f=!t&&0!==t&&p;if("function"!=typeof e)throw TypeError("Expected a function");t=+t||0;var h=!!(r=r||{}).leading,g=!("trailing"in r)||!!r.trailing,x="maxWait"in r,v="debounceOnServer"in r&&!!r.debounceOnServer,b=x?Math.max(+r.maxWait||0,t):null;return(0,s.useEffect)(function(){return m.current=!0,function(){m.current=!1}},[]),(0,s.useMemo)(function(){var e=function(e){var t=l.current,r=c.current;return l.current=c.current=null,i.current=e,u.current=d.current.apply(r,t)},r=function(e,t){f&&cancelAnimationFrame(o.current),o.current=f?requestAnimationFrame(e):setTimeout(e,t)},s=function(e){if(!m.current)return!1;var r=e-a.current;return!a.current||r>=t||r<0||x&&e-i.current>=b},j=function(t){return o.current=null,g&&l.current?e(t):(l.current=c.current=null,u.current)},w=function e(){var n=Date.now();if(s(n))return j(n);if(m.current){var o=t-(n-a.current);r(e,x?Math.min(o,b-(n-i.current)):o)}},y=function(){if(p||v){var d=Date.now(),f=s(d);if(l.current=[].slice.call(arguments),c.current=n,a.current=d,f){if(!o.current&&m.current)return i.current=a.current,r(w,t),h?e(a.current):u.current;if(x)return r(w,t),e(a.current)}return o.current||r(w,t),u.current}};return y.cancel=function(){o.current&&(f?cancelAnimationFrame(o.current):clearTimeout(o.current)),i.current=0,l.current=a.current=c.current=o.current=null},y.isPending=function(){return!!o.current},y.flush=function(){return o.current?j(Date.now()):u.current},y},[h,x,t,b,g,f,p,v])}(e=>{console.log(`Searching... ${e}`);let r=new URLSearchParams(o);r.set("page","1"),e?r.set(t,e):r.delete(t),c(`${l}?${r.toString()}`)},500);return(0,n.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,n.jsx)("label",{htmlFor:t,className:"sr-only",children:"Search"}),(0,n.jsx)("input",{id:t,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:e,onChange:e=>{u(e.target.value)},defaultValue:o.get(t)?.toString()}),r||(0,n.jsx)(i.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},68731:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(60687),s=r(95282),a=r.n(s),i=r(43210),o=r(28136);function l({seo:e,defaultTitle:t="Natural Healing Now - Holistic Health Directory",defaultDescription:r="Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.",defaultOgImage:s="",pageType:l="website"}){let c=e?.metaTitle||t,u=e?.metaDescription||r,d=e?.metaRobots||"index, follow",m=e?.canonicalURL||"";e?.structuredData;let p=e?.metaSocial?.find(e=>"Facebook"===e.socialNetwork),f=e?.metaSocial?.find(e=>"Twitter"===e.socialNetwork),h=p?.image?.data?.attributes?.url||e?.metaImage?.data?.attributes?.url||s,g=(0,o.Rb)(h),x=f?.image?.data?.attributes?.url||e?.metaImage?.data?.attributes?.url||s,v=(0,o.Rb)(x),b=p?.title||c,j=p?.description||u,w=f?.title||c,y=f?.description||u,[C,P]=(0,i.useState)(null);return(0,n.jsxs)(a(),{children:[(0,n.jsx)("title",{children:c}),(0,n.jsx)("meta",{name:"description",content:u}),d&&(0,n.jsx)("meta",{name:"robots",content:d}),m&&(0,n.jsx)("link",{rel:"canonical",href:m}),(0,n.jsx)("meta",{property:"og:type",content:l}),(0,n.jsx)("meta",{property:"og:title",content:b}),(0,n.jsx)("meta",{property:"og:description",content:j}),g&&(0,n.jsx)("meta",{property:"og:image",content:g}),(0,n.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,n.jsx)("meta",{name:"twitter:title",content:w}),(0,n.jsx)("meta",{name:"twitter:description",content:y}),v&&(0,n.jsx)("meta",{name:"twitter:image",content:v}),C&&(0,n.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(C)}})]})}},71397:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\SEOHead.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\SEOHead.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90708:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>u,pages:()=>c,routeModule:()=>d,tree:()=>l});var n=r(65239),s=r(48088),a=r(31369),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let l={children:["",{children:["blog",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57554)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\categories\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\categories\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/blog/categories/page",pathname:"/blog/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91936:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(60687),s=r(44725),a=r(7610),i=r(49384),o=r(85814),l=r.n(o),c=r(16189);let u=(e,t)=>t<=7?Array.from({length:t},(e,t)=>t+1):e<=3?[1,2,3,"...",t-1,t]:e>=t-2?[1,2,"...",t-2,t-1,t]:[1,"...",e-1,e,e+1,"...",t];function d({totalPages:e,currentPage:t}){let r=(0,c.usePathname)(),s=(0,c.useSearchParams)(),a=void 0!==t?t:Number(s.get("page"))||1,i=e=>{let t=new URLSearchParams(s);return t.set("page",e.toString()),`${r}?${t.toString()}`},o=u(a,e);return e<=1?null:(0,n.jsxs)("div",{className:"inline-flex",children:[(0,n.jsx)(p,{direction:"left",href:i(a-1),isDisabled:a<=1}),(0,n.jsx)("div",{className:"flex -space-x-px",children:o.map((e,t)=>{let r;return 0===t&&(r="first"),t===o.length-1&&(r="last"),1===o.length&&(r="single"),"..."===e&&(r="middle"),(0,n.jsx)(m,{href:i(e),page:e,position:r,isActive:a===e},`${e}-${t}`)})}),(0,n.jsx)(p,{direction:"right",href:i(a+1),isDisabled:a>=e})]})}function m({page:e,href:t,isActive:r,position:s}){let a=(0,i.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===s||"single"===s,"rounded-r-md":"last"===s||"single"===s,"z-10 bg-emerald-600 border-emerald-600 text-white":r,"hover:bg-gray-100":!r&&"middle"!==s,"text-gray-300 pointer-events-none":"middle"===s});return r||"middle"===s?(0,n.jsx)("div",{className:a,children:e}):(0,n.jsx)(l(),{href:t,className:a,children:e})}function p({href:e,direction:t,isDisabled:r}){let o=(0,i.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":r,"hover:bg-gray-100":!r,"mr-2 md:mr-4":"left"===t,"ml-2 md:ml-4":"right"===t}),c="left"===t?(0,n.jsx)(s.A,{className:"w-4"}):(0,n.jsx)(a.A,{className:"w-4"});return r?(0,n.jsx)("div",{className:o,children:c}):(0,n.jsx)(l(),{className:o,href:e,children:c})}},94735:e=>{"use strict";e.exports=require("events")},95282:(e,t)=>{"use strict";function r(){return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98657:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,68731)),Promise.resolve().then(r.bind(r,91936)),Promise.resolve().then(r.bind(r,68016))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,1330,3376,6391,2975,8446,270],()=>r(90708));module.exports=n})();