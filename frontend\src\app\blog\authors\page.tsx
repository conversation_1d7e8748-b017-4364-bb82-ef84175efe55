import Link from 'next/link';
import { Fi<PERSON>earch, FiUser } from 'react-icons/fi';
import Image from 'next/image';
import { getStrapiContent } from '@/lib/strapi';
import { getStrapiMediaUrl } from '@/lib/mediaUtils';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';

// Enable ISR with time-based revalidation (12 hours)
export const revalidate = 43200; // 12 hours in seconds

// Define the author interface
interface Author {
  id: string | number;
  name: string;
  slug: string;
  bio?: string | null;
  profilePicture?: any;
  email?: string | null;
  website?: string | null;
  blog_posts?: any[];
  post_count: number;
}

// Function to fetch authors data from Strapi with ISR caching
async function getAuthorsData() {
  try {
    // Use ISR caching with tags for targeted revalidation
    // Explicitly add cache: 'force-cache' for Next.js 15 ISR
    const response = await getStrapiContent.blog.getAuthors({
      cache: 'force-cache', // Ensure data is cached for ISR
      next: {
        revalidate: 43200, // 12 hours in seconds
        tags: ['strapi-authors', 'strapi-blog-authors']
      }
    });

    if (!response || !response.data) {
      console.error('Failed to fetch authors data');
      return [];
    }

    // Transform the Strapi response to match our expected format
    return response.data.map((author: any) => {
      // Get the author data (Strapi v5 flattened structure)
      const authorData = author;

      // Get profile picture URL
      const profilePictureUrl = getStrapiMediaUrl(authorData.profilePicture);

      // Count blog posts
      const postCount = Array.isArray(authorData.blog_posts) ? authorData.blog_posts.length : 0;

      // Extract bio text from richtext if needed
      let bioText = authorData.bio;
      if (bioText && typeof bioText === 'string') {
        // Remove HTML tags if present
        bioText = bioText.replace(/<[^>]*>/g, '');
      }

      return {
        id: authorData.id,
        name: authorData.name || 'Unknown Author',
        slug: authorData.slug || '',
        bio: bioText || null,
        profilePicture: profilePictureUrl,
        email: authorData.email || null,
        website: authorData.website || null,
        post_count: postCount
      };
    });
  } catch (error) {
    console.error('Error fetching authors:', error);
    return [];
  }
}

export const metadata: Metadata = {
  title: 'Blog Authors | Natural Healing Now',
  description: 'Meet our expert contributors who share their knowledge and insights on holistic health topics.',
};

// Function to handle client-side search
function filterAuthors(authors: Author[], searchQuery: string): Author[] {
  if (!searchQuery) return authors;

  const query = searchQuery.toLowerCase();
  return authors.filter(author =>
    author.name.toLowerCase().includes(query) ||
    (author.bio && author.bio.toLowerCase().includes(query))
  );
}

export default async function BlogAuthorsPage({
  searchParams,
}: {
  searchParams?: { query?: string };
}) {
  // Fetch authors data
  const authors = await getAuthorsData();

  // If no authors found, show a not found page
  if (!authors || authors.length === 0) {
    return notFound();
  }

  // Get search query from URL params
  const searchQuery = searchParams?.query || '';

  // Filter authors based on search query
  const filteredAuthors = searchQuery ? filterAuthors(authors, searchQuery) : authors;
  return (
    <>
      {/* Page Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Blog Authors</h1>
          <p className="text-lg max-w-3xl">
            Meet our expert contributors who share their knowledge and insights on holistic health topics.
          </p>
        </div>
      </div>

      {/* Search Section */}
      <div className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-md mx-auto">
            <form action="/blog/authors" method="GET">
              <div className="relative">
                <input
                  type="text"
                  name="query"
                  placeholder="Search authors"
                  defaultValue={searchQuery}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                />
                <FiSearch className="absolute left-3 top-3 text-gray-400" />
                <button
                  type="submit"
                  className="absolute right-2 top-2 bg-emerald-500 text-white px-3 py-1 rounded-md text-sm hover:bg-emerald-600"
                >
                  Search
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Authors List */}
      <div className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            {searchQuery ? `Search Results for "${searchQuery}"` : 'All Authors'}
            {searchQuery && (
              <span className="ml-2 text-sm font-normal text-gray-500">
                ({filteredAuthors.length} {filteredAuthors.length === 1 ? 'author' : 'authors'} found)
              </span>
            )}
          </h2>

          {filteredAuthors.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredAuthors.map((author: Author) => (
              <div
                key={author.id.toString()}
                className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    {/* Author Image */}
                    {author.profilePicture ? (
                      <div className="h-16 w-16 rounded-full overflow-hidden mr-4 flex-shrink-0 border border-gray-200">
                        <Image
                          src={author.profilePicture}
                          alt={author.name}
                          width={64}
                          height={64}
                          className="object-cover w-full h-full"
                          sizes="64px"
                        />
                      </div>
                    ) : (
                      <div className="bg-emerald-100 h-16 w-16 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                        <FiUser className="text-emerald-700 text-xl" />
                      </div>
                    )}

                    <div>
                      <h3 className="text-xl font-semibold text-gray-800">
                        <Link
                          href={`/blog/authors/${author.slug}`}
                          className="hover:text-emerald-600"
                        >
                          {author.name}
                        </Link>
                      </h3>
                      {/* We don't have qualifications in the Strapi schema, so we'll show email instead if available */}
                      {author.email && (
                        <p className="text-gray-500 text-sm truncate">{author.email}</p>
                      )}
                    </div>
                  </div>

                  {author.bio && (
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {author.bio}
                    </p>
                  )}

                  <div className="flex justify-between items-center mt-4">
                    <span className="text-sm text-gray-500">
                      {author.post_count} {author.post_count === 1 ? 'article' : 'articles'}
                    </span>

                    <Link
                      href={`/blog/authors/${author.slug}`}
                      className="text-emerald-600 hover:text-emerald-700 font-medium text-sm"
                    >
                      View Profile →
                    </Link>
                  </div>
                </div>
              </div>
            ))}
            </div>
          ) : (
            <div className="bg-white p-8 rounded-lg text-center shadow">
              <p className="text-gray-600">No authors found matching your search criteria.</p>
              <Link href="/blog/authors" className="mt-4 inline-block text-emerald-600 hover:text-emerald-700">
                View all authors
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Back to Blog Link */}
      <div className="py-8 bg-white">
        <div className="container mx-auto px-4 text-center">
          <Link
            href="/blog"
            className="text-emerald-600 hover:text-emerald-700 font-medium"
          >
            ← Back to Blog
          </Link>
        </div>
      </div>
    </>
  );
}
