'use client';

import { useState } from 'react';
import {
  useFeaturedClinics,
  useFeaturedPractitioners,
  useCategories,
  useFeaturedBlogPosts,
  useLatestBlogPosts,
  usePopularBlogPosts
} from '@/hooks/useOptimizedFetch';
import OptimizationNav from '@/components/examples/OptimizationNav';

/**
 * Example page demonstrating optimized client-side data fetching with React Query
 */
export default function OptimizedClientExamplePage() {
  const [activeTab, setActiveTab] = useState<string>('featured');

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-4">Optimized Client-Side Data Fetching</h1>
      <OptimizationNav />

      <div className="mb-8">
        <p className="text-lg mb-4">
          This page demonstrates client-side optimized data fetching from Strapi.
          The data is cached in memory and shared between components.
        </p>

        <div className="flex border-b">
          <button
            className={`px-4 py-2 ${activeTab === 'featured' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
            onClick={() => setActiveTab('featured')}
          >
            Featured Content
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'categories' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
            onClick={() => setActiveTab('categories')}
          >
            Categories
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'blog' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
            onClick={() => setActiveTab('blog')}
          >
            Blog Posts
          </button>
        </div>
      </div>

      {activeTab === 'featured' && <FeaturedContent />}
      {activeTab === 'categories' && <CategoriesContent />}
      {activeTab === 'blog' && <BlogContent />}

      <div className="mt-8 p-6 bg-blue-50 rounded-lg">
        <h2 className="text-2xl font-semibold mb-4">How This Reduces API Calls</h2>

        <p className="mb-2">This implementation reduces API calls to Strapi in several ways:</p>

        <ol className="list-decimal pl-5 space-y-2">
          <li>
            <strong>In-memory caching:</strong> Responses are cached with content-type specific TTLs,
            preventing unnecessary refetching of data that hasn't changed.
          </li>
          <li>
            <strong>Request deduplication:</strong> Multiple components requesting the same data
            will share a single API call instead of each making their own request.
          </li>
          <li>
            <strong>Content-type specific caching:</strong> Different cache durations based on
            how frequently content changes (e.g., global settings are cached longer than blog posts).
          </li>
          <li>
            <strong>Prefetching:</strong> Common data is preloaded, reducing the need for
            API calls when navigating between pages.
          </li>
        </ol>

        <p className="mt-4">
          For more details, see the documentation in <code>frontend/src/docs/OPTIMIZED_API_CALLS.md</code>.
        </p>
      </div>
    </div>
  );
}

/**
 * Featured Content Tab
 */
function FeaturedContent() {
  const {
    data: clinicsData,
    isLoading: isLoadingClinics,
    error: clinicsError
  } = useFeaturedClinics();

  const {
    data: practitionersData,
    isLoading: isLoadingPractitioners,
    error: practitionersError
  } = useFeaturedPractitioners();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {/* Featured Clinics */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Featured Clinics</h2>

        {isLoadingClinics ? (
          <LoadingSkeleton />
        ) : clinicsError ? (
          <ErrorMessage message="Failed to load clinics" />
        ) : (
          <ul className="list-disc pl-5">
            {clinicsData?.data?.map((clinic: any) => (
              <li key={clinic.id || clinic.documentId}>
                {clinic.name || clinic.attributes?.name}
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Featured Practitioners */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-semibold mb-4">Featured Practitioners</h2>

        {isLoadingPractitioners ? (
          <LoadingSkeleton />
        ) : practitionersError ? (
          <ErrorMessage message="Failed to load practitioners" />
        ) : (
          <ul className="list-disc pl-5">
            {practitionersData?.data?.map((practitioner: any) => {
              const firstName = practitioner.firstName || practitioner.attributes?.firstName;
              const lastName = practitioner.lastName || practitioner.attributes?.lastName;
              return (
                <li key={practitioner.id || practitioner.documentId}>
                  {firstName} {lastName}
                </li>
              );
            })}
          </ul>
        )}
      </div>
    </div>
  );
}

/**
 * Categories Content Tab
 */
function CategoriesContent() {
  const {
    data: categoriesData,
    isLoading: isLoadingCategories,
    error: categoriesError
  } = useCategories(10);

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-semibold mb-4">Categories</h2>

      {isLoadingCategories ? (
        <LoadingSkeleton />
      ) : categoriesError ? (
        <ErrorMessage message="Failed to load categories" />
      ) : (
        <ul className="list-disc pl-5">
          {categoriesData?.data?.map((category: any) => (
            <li key={category.id || category.documentId}>
              {category.name || category.attributes?.name}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

/**
 * Blog Content Tab
 */
function BlogContent() {
  const [blogType, setBlogType] = useState<string>('featured');

  const {
    data: featuredPostsData,
    isLoading: isLoadingFeaturedPosts,
    error: featuredPostsError
  } = useFeaturedBlogPosts();

  const {
    data: latestPostsData,
    isLoading: isLoadingLatestPosts,
    error: latestPostsError
  } = useLatestBlogPosts(5);

  const {
    data: popularPostsData,
    isLoading: isLoadingPopularPosts,
    error: popularPostsError
  } = usePopularBlogPosts(5);

  // Determine which data to display based on the selected blog type
  const data = blogType === 'featured'
    ? featuredPostsData
    : blogType === 'latest'
      ? latestPostsData
      : popularPostsData;

  const isLoading = blogType === 'featured'
    ? isLoadingFeaturedPosts
    : blogType === 'latest'
      ? isLoadingLatestPosts
      : isLoadingPopularPosts;

  const error = blogType === 'featured'
    ? featuredPostsError
    : blogType === 'latest'
      ? latestPostsError
      : popularPostsError;

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex mb-4">
        <button
          className={`px-4 py-2 mr-2 rounded ${blogType === 'featured' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          onClick={() => setBlogType('featured')}
        >
          Featured
        </button>
        <button
          className={`px-4 py-2 mr-2 rounded ${blogType === 'latest' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          onClick={() => setBlogType('latest')}
        >
          Latest
        </button>
        <button
          className={`px-4 py-2 rounded ${blogType === 'popular' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          onClick={() => setBlogType('popular')}
        >
          Popular
        </button>
      </div>

      <h2 className="text-2xl font-semibold mb-4">
        {blogType === 'featured'
          ? 'Featured Blog Posts'
          : blogType === 'latest'
            ? 'Latest Blog Posts'
            : 'Popular Blog Posts'}
      </h2>

      {isLoading ? (
        <LoadingSkeleton />
      ) : error ? (
        <ErrorMessage message={`Failed to load ${blogType} blog posts`} />
      ) : (
        <ul className="list-disc pl-5">
          {data?.data?.map((post: any) => (
            <li key={post.id || post.documentId}>
              {post.title || post.attributes?.title}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

/**
 * Loading Skeleton
 */
function LoadingSkeleton() {
  return (
    <div className="animate-pulse space-y-3">
      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
      <div className="h-4 bg-gray-200 rounded w-2/3"></div>
    </div>
  );
}

/**
 * Error Message
 */
function ErrorMessage({ message }: { message: string }) {
  return (
    <div className="text-red-500 p-4 bg-red-50 rounded">
      <p>{message}</p>
    </div>
  );
}
