"use strict";exports.id=7880,exports.ids=[7880],exports.modules={36463:(t,e,r)=>{r.d(e,{Ay:()=>n});var i=function(t){return t.DEBUG="debug",t.INFO="info",t.WARN="warn",t.ERROR="error",t}({});let a={enabled:!1,level:"info",prefix:"[NHN]"};function s(t,e,...r){if(!a.enabled)return;let n=Object.values(i),o=n.indexOf(a.level);if(n.indexOf(t)>=o){let i=a.prefix?`${a.prefix} `:"",s=`${i}${e}`;switch(t){case"debug":console.debug(s,...r);break;case"info":console.info(s,...r);break;case"warn":console.warn(s,...r);break;case"error":console.error(s,...r)}}}let n={debug:function(t,...e){s("debug",t,...e)},info:function(t,...e){s("info",t,...e)},warn:function(t,...e){s("warn",t,...e)},error:function(t,...e){s("error",t,...e)},configure:function(t){a={...a,...t}}}},52815:(t,e,r)=>{r.r(e),r.d(e,{default:()=>n,imageUtils:()=>o});let i={count:0,errors:0,totalTime:0,slowestTime:0,slowestImage:""},a={enableMetrics:"true"===process.env.NEXT_PUBLIC_CACHE_METRICS,useHighQuality:!0,disableOptimization:"true"===process.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION,defaultQuality:85,avifQuality:80,webpQuality:85,jpegQuality:90,pngQuality:90,maxDevicePixelRatio:3,minWidth:20,blurUpRadius:10};function s(t,e,r){let i="https://nice-badge-2130241d6c.media.strapiapp.com",s=r||(t.toLowerCase().match(/\.avif$/i)?a.avifQuality:t.toLowerCase().match(/\.webp$/i)?a.webpQuality:t.toLowerCase().match(/\.jpe?g$/i)?a.jpegQuality:t.toLowerCase().match(/\.png$/i)?a.pngQuality:t.toLowerCase().match(/\.(jpe?g|png)$/i)?a.jpegQuality:a.webpQuality);if(e<a.minWidth)return t;try{let r=Math.round(+e),a=new URL(t.startsWith("http")?t:`${i}${t.startsWith("/")?t:`/${t}`}`);if(a.hostname.includes("strapiapp.com")||a.hostname.includes("localhost")){a.searchParams.set("w",r.toString()),a.searchParams.set("q",s.toString());let e=t.toLowerCase().match(/\.(jpe?g|png)$/i);a.searchParams.has("format")||a.searchParams.set("format",e?"avif":"webp");{let t=Array.from(a.searchParams.entries()).sort();a.search=t.map(([t,e])=>`${t}=${e}`).join("&")}e&&a.searchParams.set("sharp","10"),"http:"===a.protocol&&(a.protocol="https:")}return a.toString()}catch(r){if(t.startsWith("/"))return`${i}${t}?w=${e}&q=${s}`;return t}}function n({src:t,width:e,quality:r}){let n=a.enableMetrics?performance.now():0;if(!t)return"";try{if(!(t&&!a.disableOptimization&&!("string"==typeof t&&[".svg",".gif",".webp",".avif"].some(e=>t.toLowerCase().endsWith(e))||t.startsWith("http")&&!t.includes("strapiapp.com")&&!t.includes("localhost:1337"))&&1))return t;let o=s(t,e,r);if(a.enableMetrics&&n){let e=performance.now()-n;i.count++,i.totalTime+=e,e>i.slowestTime&&(i.slowestTime=e,i.slowestImage=t)}return o}catch(e){return a.enableMetrics&&i.errors++,t}}let o={getBlurDataUrl:(t,e=10)=>t?`${s(t,e,10)}&blur=80`:"",preloadImage:(t,e)=>{},resetMetrics:()=>{i.count=0,i.errors=0,i.totalTime=0,i.slowestTime=0,i.slowestImage=""},getMetrics:()=>({...i})}},82158:(t,e,r)=>{r.d(e,{Jf:()=>d,Rb:()=>h,Z5:()=>u,tz:()=>c});var i=r(36463);let a={NEXT_PUBLIC_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_MEDIA_URL:"https://nice-badge-2130241d6c.media.strapiapp.com",NEXT_PUBLIC_SITE_URL:process.env.NEXT_PUBLIC_SITE_URL,IMAGE_HOSTNAME:process.env.IMAGE_HOSTNAME,NODE_ENV:"production"},s=a.NEXT_PUBLIC_STRAPI_API_URL||a.NEXT_PUBLIC_API_URL||("development"===a.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.strapiapp.com"),n=(()=>{if(a.NEXT_PUBLIC_STRAPI_MEDIA_URL)return l(p(a.NEXT_PUBLIC_STRAPI_MEDIA_URL));if(a.IMAGE_HOSTNAME)return l(p(a.IMAGE_HOSTNAME));try{let t=new URL(s);if(t.hostname.endsWith("strapiapp.com"))return`${l(t.protocol)}//${t.hostname.replace("strapiapp.com","media.strapiapp.com")}`;return l(p(s))}catch(t){return"development"===a.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.media.strapiapp.com"}})(),o=a.NEXT_PUBLIC_SITE_URL||(a.NEXT_PUBLIC_API_URL&&a.NEXT_PUBLIC_API_URL.includes("strapiapp.com")?a.NEXT_PUBLIC_API_URL.replace(".strapiapp.com",".vercel.app"):"https://naturalhealingnow.vercel.app");function l(t){return t?t.replace(/^http:/,"https:"):t}function p(t){return t&&t.endsWith("/")?t.slice(0,-1):t}function u(t,e={debug:!1}){if(e.debug&&i.Ay.debug("getStrapiMediaUrl input:",{type:typeof t,isNull:null===t,isUndefined:void 0===t,value:t}),!t)return null;let r=null;if("string"==typeof t?r=t:"object"==typeof t&&(r=t.url||t.data?.attributes?.url||t.data?.url||null),!r)return e.debug,i.Ay.warn("Could not extract initial URL from mediaInput in getStrapiMediaUrl",{mediaInput:t}),null;let a=d(r);return a?a.startsWith("http://")||a.startsWith("https://")?a:n?`${n}${a.startsWith("/")?"":"/"}${a}`:(i.Ay.warn("STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl",{sanitizedUrl:a}),`${s}${a.startsWith("/")?"":"/"}${a}`):(e.debug,i.Ay.warn("URL became empty after sanitization in getStrapiMediaUrl",{originalUrl:r}),null)}function c(t){if(!t||!t.profilePicture)return null;let e=t.profilePicture,r=e.url||e.data?.attributes?.url||e.data?.url||e.formats?.thumbnail?.url;return r?u(r):u(e)}function d(t){let e;if("string"==typeof t&&(t.startsWith("https://")||t.startsWith("http://")||t.startsWith("/")))return t.startsWith("http://")?t.replace(/^http:/,"https:"):t;if(!t)return"";if("object"==typeof t&&t.url&&"string"==typeof t.url)e=t.url;else{if("string"!=typeof t)return i.Ay.warn("Invalid input type for sanitizeUrl. Expected string or object with url property.",{inputType:typeof t}),"";e=t}(e=e.trim()).toLowerCase().startsWith("undefined")&&(e=e.substring(9),i.Ay.info('Removed "undefined" prefix from URL',{original:t,new:e}));let r=s.replace(/^https?:\/\//,"").split("/")[0],a=n.replace(/^https?:\/\//,"").split("/")[0];if(r&&a&&e.includes(r)&&e.includes(a)){let t=RegExp(`(https?://)?(${r})(/*)(https?://)?(${a})`,"gi"),s=`https://${a}`;if(t.test(e)){let n=e;e=e.replace(t,s),i.Ay.info("Fixed concatenated Strapi domains",{original:n,fixed:e,apiDomain:r,mediaDomain:a})}}if(e.includes("https//")){let t=e;e=e.replace(/https\/\//g,"https://"),i.Ay.info("Fixed missing colon in URL (https//)",{original:t,fixed:e})}if(e.startsWith("//")?e=`https:${e}`:(e.includes("media.strapiapp.com")||e.includes(a))&&!e.startsWith("http")?e=`https://${e}`:(e.startsWith("localhost")||e.startsWith(r.split(".")[0]))&&(e=`https://${e}`),e.startsWith("/"))return e;if(e.startsWith("http://")||e.startsWith("https://"))try{return new URL(e),e}catch(t){if(i.Ay.error("URL parsing failed after sanitization attempts",{url:e,error:t}),!e.includes("://")&&!e.includes("."))return e;return""}return n&&e&&!e.includes("://")?(i.Ay.debug("Assuming relative media path, prepending STRAPI_MEDIA_URL",{path:e}),`/${e}`):(i.Ay.warn("sanitizeUrl could not produce a valid absolute or relative URL",{originalInput:t,finalSanitized:e}),e)}function h(t){if(!t)return;let e=d(t);if(e){if(e.startsWith("http://")||e.startsWith("https://"))return e.replace(/^http:/,"https:");if(n){let r=`${n}${e.startsWith("/")?"":"/"}${e}`;return i.Ay.debug("Constructed OG image URL from relative path",{original:t,final:r}),r.replace(/^http:/,"https:")}if(i.Ay.warn("Could not determine OG image URL confidently",{originalUrl:t,processedUrl:e}),s)return`${s}${e.startsWith("/")?"":"/"}${e}`.replace(/^http:/,"https:")}}"development"===a.NODE_ENV&&i.Ay.debug("Media Utils Initialized:",{EFFECTIVE_STRAPI_URL:s,STRAPI_MEDIA_URL:n,SITE_URL:o})}};