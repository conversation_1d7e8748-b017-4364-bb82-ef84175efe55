(()=>{var e={};e.id=8181,e.ids=[8181],e.modules={513:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,10592))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10592:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\SearchInput.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\SearchInput.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13665:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,68016))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35413:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,dynamic:()=>c,metadata:()=>u,revalidate:()=>d});var n=t(37413),i=t(4536),a=t.n(i),s=t(58446),o=t(10592),l=t(73993);let c="force-static",d=604800,u={title:"Health Conditions - Natural Healing Now",description:"Browse all health conditions and find practitioners and clinics that specialize in treating these conditions."};async function p({searchParams:e}){let r=await e,t=r?.query||"",i=Number(r?.page)||1,c=[],d=1;try{let e=t?{cache:"no-store"}:{next:{tags:["strapi-conditions","strapi-conditions-all"],revalidate:604800},cache:"force-cache"},r=await s.$.conditions.getAll({query:t,page:i,pageSize:12,...e});r?.data&&Array.isArray(r.data)&&(c=r.data.map(e=>(function(e){if(!e||!e.name||!e.slug)return console.warn(`Skipping invalid condition: ID ${e?.id}`),null;let r="attributes"in e?e.attributes:e;return{id:e.id,name:r.name||e.name||"Unnamed Condition",slug:r.slug||e.slug||`condition-${e.id}`,description:r.description||e.description||null,icon:r.icon||e.icon||null,featuredImage:r.featuredImage||e.featuredImage||null}})(e)).filter(e=>null!==e),d=r.meta?.pagination?.pageCount||1)}catch(e){console.error("Error fetching conditions:",e),c=[],d=1}return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Health Conditions"}),(0,n.jsx)("p",{className:"text-lg max-w-3xl",children:"Explore various health conditions and find practitioners and clinics that specialize in treating them naturally."})]})}),(0,n.jsx)("div",{className:"bg-white py-8 border-b",children:(0,n.jsx)("div",{className:"container mx-auto px-4",children:(0,n.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,n.jsx)(o.default,{placeholder:"Search conditions...",defaultValue:t,icon:(0,n.jsx)(l.CKj,{className:"text-gray-400"})})})})}),(0,n.jsx)("div",{className:"py-12 bg-gray-50",children:(0,n.jsxs)("div",{className:"container mx-auto px-4",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-8",children:"All Health Conditions"}),(0,n.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:c.length>0?c.map(e=>(0,n.jsxs)(a(),{href:`/conditions/${e.slug}`,className:"bg-white hover:bg-emerald-50 border border-gray-200 rounded-lg p-6 shadow-sm transition-colors",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:e.name}),e.description&&(0,n.jsx)("p",{className:"text-gray-600 text-sm line-clamp-3",children:"string"==typeof e.description?e.description:"Learn more about this condition"})]},e.id)):(0,n.jsx)("div",{className:"col-span-full text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:"No conditions found. Please check back later or try a different search."})})}),d>1&&(0,n.jsx)("div",{className:"mt-12 flex justify-center",children:(0,n.jsx)("nav",{className:"inline-flex",children:Array.from({length:d},(e,r)=>r+1).map(e=>(0,n.jsx)(a(),{href:{pathname:"/conditions",query:{...t?{query:t}:{},page:e}},className:`px-4 py-2 border ${i===e?"bg-emerald-600 text-white border-emerald-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"}`,children:e},e))})})]})}),(0,n.jsx)("div",{className:"py-16 bg-emerald-50",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold mb-6 text-gray-800",children:"Find Natural Solutions for Your Health"}),(0,n.jsx)("p",{className:"text-lg mb-8 max-w-3xl mx-auto text-gray-600",children:"Connect with practitioners and clinics that specialize in treating your specific health conditions naturally."}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,n.jsx)(a(),{href:"/clinics",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold",children:"Find a Clinic"}),(0,n.jsx)(a(),{href:"/practitioners",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-semibold",children:"Find a Practitioner"})]})]})})]})}},50478:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>l});var n=t(65239),i=t(48088),a=t(31369),s=t(30893),o={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>s[e]);t.d(r,o);let l={children:["",{children:["conditions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,35413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\conditions\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\conditions\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/conditions/page",pathname:"/conditions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68016:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});var n=t(60687),i=t(43210),a=t(16189),s=t(17019);function o({placeholder:e,paramName:r="query",icon:t}){let o=(0,a.useSearchParams)(),l=(0,a.usePathname)(),{replace:c}=(0,a.useRouter)(),d=function(e,r,t){var n=this,a=(0,i.useRef)(null),s=(0,i.useRef)(0),o=(0,i.useRef)(null),l=(0,i.useRef)([]),c=(0,i.useRef)(),d=(0,i.useRef)(),u=(0,i.useRef)(e),p=(0,i.useRef)(!0);u.current=e;var m="undefined"!=typeof window,x=!r&&0!==r&&m;if("function"!=typeof e)throw TypeError("Expected a function");r=+r||0;var h=!!(t=t||{}).leading,f=!("trailing"in t)||!!t.trailing,g="maxWait"in t,b="debounceOnServer"in t&&!!t.debounceOnServer,v=g?Math.max(+t.maxWait||0,r):null;return(0,i.useEffect)(function(){return p.current=!0,function(){p.current=!1}},[]),(0,i.useMemo)(function(){var e=function(e){var r=l.current,t=c.current;return l.current=c.current=null,s.current=e,d.current=u.current.apply(t,r)},t=function(e,r){x&&cancelAnimationFrame(o.current),o.current=x?requestAnimationFrame(e):setTimeout(e,r)},i=function(e){if(!p.current)return!1;var t=e-a.current;return!a.current||t>=r||t<0||g&&e-s.current>=v},y=function(r){return o.current=null,f&&l.current?e(r):(l.current=c.current=null,d.current)},j=function e(){var n=Date.now();if(i(n))return y(n);if(p.current){var o=r-(n-a.current);t(e,g?Math.min(o,v-(n-s.current)):o)}},w=function(){if(m||b){var u=Date.now(),x=i(u);if(l.current=[].slice.call(arguments),c.current=n,a.current=u,x){if(!o.current&&p.current)return s.current=a.current,t(j,r),h?e(a.current):d.current;if(g)return t(j,r),e(a.current)}return o.current||t(j,r),d.current}};return w.cancel=function(){o.current&&(x?cancelAnimationFrame(o.current):clearTimeout(o.current)),s.current=0,l.current=a.current=c.current=o.current=null},w.isPending=function(){return!!o.current},w.flush=function(){return o.current?y(Date.now()):d.current},w},[h,g,r,v,f,x,m,b])}(e=>{console.log(`Searching... ${e}`);let t=new URLSearchParams(o);t.set("page","1"),e?t.set(r,e):t.delete(r),c(`${l}?${t.toString()}`)},500);return(0,n.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,n.jsx)("label",{htmlFor:r,className:"sr-only",children:"Search"}),(0,n.jsx)("input",{id:r,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:e,onChange:e=>{d(e.target.value)},defaultValue:o.get(r)?.toString()}),t||(0,n.jsx)(s.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[7719,1330,3376,6391,2975,8446,270],()=>t(50478));module.exports=n})();