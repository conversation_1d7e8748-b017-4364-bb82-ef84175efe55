'use client';

interface VideoEmbedProps {
  videoHtml: string | null;
}

const VideoEmbed = ({ videoHtml }: VideoEmbedProps) => {
  if (!videoHtml) {
    return null;
  }

  return (
    <div className="mt-6 rounded-lg overflow-hidden" style={{ maxWidth: '100%' }}>
      <div
        style={{ position: 'relative', paddingBottom: '56.25%', height: 0 }}
        dangerouslySetInnerHTML={{ __html: videoHtml }}
      />
    </div>
  );
};

export default VideoEmbed;
