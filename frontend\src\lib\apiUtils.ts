/**
 * API utilities for making authenticated requests to Strapi
 * Handles both client-side and server-side requests
 */
import axios, { AxiosRequestConfig } from 'axios';

// Get Strapi URL from environment variable
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1337';
const API_PATH = '/api';

/**
 * Creates an authenticated API client for server-side requests
 * Uses the API token from environment variables
 */
export const createServerSideApiClient = () => {
  const API_TOKEN = process.env.STRAPI_API_TOKEN;

  if (!API_TOKEN) {
    console.warn('No STRAPI_API_TOKEN found in environment variables for server-side API client');
  }

  return axios.create({
    baseURL: `${API_URL}${API_PATH}`,
    headers: {
      'Content-Type': 'application/json',
      ...(API_TOKEN ? { Authorization: `Bearer ${API_TOKEN}` } : {}),
    },
  });
};

/**
 * Creates an authenticated API client for client-side requests
 * Uses the JWT token from localStorage for user authentication
 */
export const createClientSideApiClient = () => {
  // Only access localStorage in the browser
  const getToken = () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('jwt');
    }
    return null;
  };

  const token = getToken();

  return axios.create({
    baseURL: `${API_URL}${API_PATH}`,
    headers: {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  });
};

/**
 * Makes a GET request to the Strapi API
 * Automatically determines whether to use server-side or client-side authentication
 */
export const fetchFromApi = async <T>(
  endpoint: string,
  options: AxiosRequestConfig = {},
  isServerSide = typeof window === 'undefined'
): Promise<T> => {
  try {
    const apiClient = isServerSide
      ? createServerSideApiClient()
      : createClientSideApiClient();

    const response = await apiClient.get<T>(endpoint, options);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching from API (${endpoint}):`, error);

    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }

    throw error;
  }
};

/**
 * Makes a POST request to the Strapi API
 * Automatically determines whether to use server-side or client-side authentication
 */
export const postToApi = async <T>(
  endpoint: string,
  data: any,
  options: AxiosRequestConfig = {},
  isServerSide = typeof window === 'undefined'
): Promise<T> => {
  try {
    const apiClient = isServerSide
      ? createServerSideApiClient()
      : createClientSideApiClient();

    const response = await apiClient.post<T>(endpoint, data, options);
    return response.data;
  } catch (error: any) {
    console.error(`Error posting to API (${endpoint}):`, error);

    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }

    throw error;
  }
};

/**
 * Makes a PUT request to the Strapi API
 * Automatically determines whether to use server-side or client-side authentication
 */
export const putToApi = async <T>(
  endpoint: string,
  data: any,
  options: AxiosRequestConfig = {},
  isServerSide = typeof window === 'undefined'
): Promise<T> => {
  try {
    const apiClient = isServerSide
      ? createServerSideApiClient()
      : createClientSideApiClient();

    const response = await apiClient.put<T>(endpoint, data, options);
    return response.data;
  } catch (error: any) {
    console.error(`Error putting to API (${endpoint}):`, error);

    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }

    throw error;
  }
};

/**
 * Makes a DELETE request to the Strapi API
 * Automatically determines whether to use server-side or client-side authentication
 */
export const deleteFromApi = async <T>(
  endpoint: string,
  options: AxiosRequestConfig = {},
  isServerSide = typeof window === 'undefined'
): Promise<T> => {
  try {
    const apiClient = isServerSide
      ? createServerSideApiClient()
      : createClientSideApiClient();

    const response = await apiClient.delete<T>(endpoint, options);
    return response.data;
  } catch (error: any) {
    console.error(`Error deleting from API (${endpoint}):`, error);

    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }

    throw error;
  }
};
