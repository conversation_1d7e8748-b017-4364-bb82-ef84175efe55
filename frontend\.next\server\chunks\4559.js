"use strict";exports.id=4559,exports.ids=[4559],exports.modules={163:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},36463:(e,t,r)=>{r.d(t,{Ay:()=>o});var n=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let a={enabled:!1,level:"info",prefix:"[NHN]"};function i(e,t,...r){if(!a.enabled)return;let o=Object.values(n),l=o.indexOf(a.level);if(o.indexOf(e)>=l){let n=a.prefix?`${a.prefix} `:"",i=`${n}${t}`;switch(e){case"debug":console.debug(i,...r);break;case"info":console.info(i,...r);break;case"warn":console.warn(i,...r);break;case"error":console.error(i,...r)}}}let o={debug:function(e,...t){i("debug",e,...t)},info:function(e,...t){i("info",e,...t)},warn:function(e,...t){i("warn",e,...t)},error:function(e,...t){i("error",e,...t)},configure:function(e){a={...a,...e}}}},39916:(e,t,r)=>{var n=r(97576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"unauthorized")&&r.d(t,{unauthorized:function(){return n.unauthorized}})},48976:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55061:(e,t,r)=>{r.d(t,{default:()=>d});var n=r(60687),a=r(24587),i=r(85814),o=r.n(i),l=r(44867),s=r(17019),u=r(28136);let d=({post:e,showReadingTime:t=!1,showShareButton:r=!1,showBadge:i=!1})=>{var d;let c=(0,u.Jf)(e.featured_image),f=!!e.featured_image,p=(0,u.Jf)(e.author?.profile_picture);e.author?.profile_picture;let h=e.reading_time||(e.content?(d=e.content,Math.max(1,Math.ceil((d?.split(/\s+/)?.length||0)/200))):2),m=p&&(p.startsWith("http")||p.startsWith("/")||p.startsWith("data:"))?p:"",b=(0,l.GP)(new Date(e.publish_date),"MMMM d, yyyy"),_=Object.getOwnPropertyDescriptor(e,"view_count")?.value||0;return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)(o(),{href:`/blog/${e.slug}`,className:"block relative h-48 w-full overflow-hidden",children:[" ",f?(0,n.jsx)(a.default,{src:c,alt:e.title||"Blog post image",width:600,height:400,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1,showPlaceholder:!0,advancedBlur:!0,fadeIn:!0,preload:e.isFeatured||_>10}):(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-50 to-teal-100 flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-emerald-700 font-semibold text-xl opacity-50",children:e.title.charAt(0)})}),i&&(!0===e.isFeatured||_>0)&&(0,n.jsx)("div",{className:`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ${!0===e.isFeatured?"bg-emerald-600 text-white":"bg-amber-500 text-white"}`,children:!0===e.isFeatured?"Featured Post":"Popular Post"})]}),(0,n.jsxs)("div",{className:"p-4 flex-grow flex flex-col",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,n.jsx)("div",{className:"text-sm text-gray-500",children:b}),t&&(0,n.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,n.jsx)(s.Ohp,{className:"mr-1"}),(0,n.jsxs)("span",{children:[h," min read"]})]})]}),(0,n.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800 flex-grow",children:(0,n.jsx)(o(),{href:`/blog/${e.slug}`,className:"hover:text-emerald-600 line-clamp-2",children:e.title})}),e.excerpt&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:e.excerpt}),e.author&&(0,n.jsxs)("div",{className:"flex items-center mt-auto pt-4 border-t border-gray-100",children:[" ",(0,n.jsxs)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-200 shadow-sm",children:[" ",m&&(e=>{try{return e&&(e.startsWith("http")||e.startsWith("/")||e.startsWith("data:"))}catch(e){return!1}})(m)?(0,n.jsx)(a.default,{src:m,alt:e.author.name||"Author image",width:40,height:40,fillContainer:!0,className:"object-cover rounded-full",sizes:"40px",showPlaceholder:!0,fadeIn:!0}):(0,n.jsx)("div",{className:"absolute inset-0 bg-emerald-100 flex items-center justify-center",children:(0,n.jsx)(s.JXP,{className:"text-emerald-700 text-lg"})})]}),(0,n.jsxs)("div",{className:"text-sm",children:[(0,n.jsx)("span",{className:"block text-xs text-gray-500 mb-0.5",children:"Written by"}),(0,n.jsx)(o(),{href:`/blog/authors/${e.author.slug}`,className:"font-medium text-gray-800 hover:text-emerald-600",children:e.author.name})]})]})]}),(0,n.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto flex justify-between items-center",children:[(0,n.jsx)(o(),{href:`/blog/${e.slug}`,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"Read More →"}),r&&(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors",onClick:t=>{t.preventDefault(),navigator.share?navigator.share({title:e.title,text:e.excerpt||"",url:`${window.location.origin}/blog/${e.slug}`}).catch(e=>{console.error("Error sharing:",e)}):navigator.clipboard.writeText(`${window.location.origin}/blog/${e.slug}`).then(()=>{alert("Link copied to clipboard!")}).catch(e=>{console.error("Could not copy text: ",e)})},"aria-label":"Share article",children:(0,n.jsx)(s.Pum,{size:18})}),(0,n.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors","aria-label":"Save article",children:(0,n.jsx)(s.Y19,{size:18})})]})]})]})}},62765:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70899:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,s.isDynamicServerError)(t)||(0,l.isDynamicPostpone)(t)||(0,a.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),a=r(52637),i=r(51846),o=r(31162),l=r(84971),s=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82158:(e,t,r)=>{r.d(t,{Jf:()=>f,Rb:()=>p,Z5:()=>d,tz:()=>c});var n=r(36463);let a={NEXT_PUBLIC_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_MEDIA_URL:"https://nice-badge-2130241d6c.media.strapiapp.com",NEXT_PUBLIC_SITE_URL:process.env.NEXT_PUBLIC_SITE_URL,IMAGE_HOSTNAME:process.env.IMAGE_HOSTNAME,NODE_ENV:"production"},i=a.NEXT_PUBLIC_STRAPI_API_URL||a.NEXT_PUBLIC_API_URL||("development"===a.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.strapiapp.com"),o=(()=>{if(a.NEXT_PUBLIC_STRAPI_MEDIA_URL)return s(u(a.NEXT_PUBLIC_STRAPI_MEDIA_URL));if(a.IMAGE_HOSTNAME)return s(u(a.IMAGE_HOSTNAME));try{let e=new URL(i);if(e.hostname.endsWith("strapiapp.com"))return`${s(e.protocol)}//${e.hostname.replace("strapiapp.com","media.strapiapp.com")}`;return s(u(i))}catch(e){return"development"===a.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.media.strapiapp.com"}})(),l=a.NEXT_PUBLIC_SITE_URL||(a.NEXT_PUBLIC_API_URL&&a.NEXT_PUBLIC_API_URL.includes("strapiapp.com")?a.NEXT_PUBLIC_API_URL.replace(".strapiapp.com",".vercel.app"):"https://naturalhealingnow.vercel.app");function s(e){return e?e.replace(/^http:/,"https:"):e}function u(e){return e&&e.endsWith("/")?e.slice(0,-1):e}function d(e,t={debug:!1}){if(t.debug&&n.Ay.debug("getStrapiMediaUrl input:",{type:typeof e,isNull:null===e,isUndefined:void 0===e,value:e}),!e)return null;let r=null;if("string"==typeof e?r=e:"object"==typeof e&&(r=e.url||e.data?.attributes?.url||e.data?.url||null),!r)return t.debug,n.Ay.warn("Could not extract initial URL from mediaInput in getStrapiMediaUrl",{mediaInput:e}),null;let a=f(r);return a?a.startsWith("http://")||a.startsWith("https://")?a:o?`${o}${a.startsWith("/")?"":"/"}${a}`:(n.Ay.warn("STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl",{sanitizedUrl:a}),`${i}${a.startsWith("/")?"":"/"}${a}`):(t.debug,n.Ay.warn("URL became empty after sanitization in getStrapiMediaUrl",{originalUrl:r}),null)}function c(e){if(!e||!e.profilePicture)return null;let t=e.profilePicture,r=t.url||t.data?.attributes?.url||t.data?.url||t.formats?.thumbnail?.url;return r?d(r):d(t)}function f(e){let t;if("string"==typeof e&&(e.startsWith("https://")||e.startsWith("http://")||e.startsWith("/")))return e.startsWith("http://")?e.replace(/^http:/,"https:"):e;if(!e)return"";if("object"==typeof e&&e.url&&"string"==typeof e.url)t=e.url;else{if("string"!=typeof e)return n.Ay.warn("Invalid input type for sanitizeUrl. Expected string or object with url property.",{inputType:typeof e}),"";t=e}(t=t.trim()).toLowerCase().startsWith("undefined")&&(t=t.substring(9),n.Ay.info('Removed "undefined" prefix from URL',{original:e,new:t}));let r=i.replace(/^https?:\/\//,"").split("/")[0],a=o.replace(/^https?:\/\//,"").split("/")[0];if(r&&a&&t.includes(r)&&t.includes(a)){let e=RegExp(`(https?://)?(${r})(/*)(https?://)?(${a})`,"gi"),i=`https://${a}`;if(e.test(t)){let o=t;t=t.replace(e,i),n.Ay.info("Fixed concatenated Strapi domains",{original:o,fixed:t,apiDomain:r,mediaDomain:a})}}if(t.includes("https//")){let e=t;t=t.replace(/https\/\//g,"https://"),n.Ay.info("Fixed missing colon in URL (https//)",{original:e,fixed:t})}if(t.startsWith("//")?t=`https:${t}`:(t.includes("media.strapiapp.com")||t.includes(a))&&!t.startsWith("http")?t=`https://${t}`:(t.startsWith("localhost")||t.startsWith(r.split(".")[0]))&&(t=`https://${t}`),t.startsWith("/"))return t;if(t.startsWith("http://")||t.startsWith("https://"))try{return new URL(t),t}catch(e){if(n.Ay.error("URL parsing failed after sanitization attempts",{url:t,error:e}),!t.includes("://")&&!t.includes("."))return t;return""}return o&&t&&!t.includes("://")?(n.Ay.debug("Assuming relative media path, prepending STRAPI_MEDIA_URL",{path:t}),`/${t}`):(n.Ay.warn("sanitizeUrl could not produce a valid absolute or relative URL",{originalInput:e,finalSanitized:t}),t)}function p(e){if(!e)return;let t=f(e);if(t){if(t.startsWith("http://")||t.startsWith("https://"))return t.replace(/^http:/,"https:");if(o){let r=`${o}${t.startsWith("/")?"":"/"}${t}`;return n.Ay.debug("Constructed OG image URL from relative path",{original:e,final:r}),r.replace(/^http:/,"https:")}if(n.Ay.warn("Could not determine OG image URL confidently",{originalUrl:e,processedUrl:t}),i)return`${i}${t.startsWith("/")?"":"/"}${t}`.replace(/^http:/,"https:")}}"development"===a.NODE_ENV&&n.Ay.debug("Media Utils Initialized:",{EFFECTIVE_STRAPI_URL:i,STRAPI_MEDIA_URL:o,SITE_URL:l})},86897:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return s},redirect:function(){return l}});let n=r(52836),a=r(49026),i=r(19121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=a.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function l(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?a.RedirectType.push:a.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function s(e,t){throw void 0===t&&(t=a.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87603:(e,t,r)=>{r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\BlogPostCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\BlogPostCard.tsx","default")},97576:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return l.unauthorized},unstable_rethrow:function(){return s.unstable_rethrow}});let n=r(86897),a=r(49026),i=r(62765),o=r(48976),l=r(70899),s=r(163);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};