(()=>{var e={};e.id=4066,e.ids=[4066],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36463:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>o});var a=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let s={enabled:!1,level:"info",prefix:"[NHN]"};function i(e,r,...t){if(!s.enabled)return;let o=Object.values(a),n=o.indexOf(s.level);if(o.indexOf(e)>=n){let a=s.prefix?`${s.prefix} `:"",i=`${a}${r}`;switch(e){case"debug":console.debug(i,...t);break;case"info":console.info(i,...t);break;case"warn":console.warn(i,...t);break;case"error":console.error(i,...t)}}}let o={debug:function(e,...r){i("debug",e,...r)},info:function(e,...r){i("info",e,...r)},warn:function(e,...r){i("warn",e,...r)},error:function(e,...r){i("error",e,...r)},configure:function(e){s={...s,...e}}}},38906:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{GET:()=>g,POST:()=>d});var s=t(96559),i=t(48088),o=t(37719),n=t(32190),c=t(62351),l=t(36463);let u=process.env.PREVIEW_SECRET||process.env.REVALIDATE_TOKEN;async function d(e){if(!u)return l.Ay.error("CRITICAL: REVALIDATE_SECRET is not set. Categories revalidation endpoint is disabled."),n.NextResponse.json({message:"Revalidation secret not configured."},{status:500});if(e.headers.get("X-Revalidate-Secret")!==u)return l.Ay.warn("Invalid categories revalidation attempt: Incorrect or missing X-Revalidate-Secret header."),n.NextResponse.json({message:"Invalid token"},{status:401});try{let{event:r,model:t,entry:a}=await e.json();if("category"!==t)return l.Ay.info(`Revalidation skipped: Not a category model. Model received: ${t}`),n.NextResponse.json({message:"Revalidation skipped: Not a category model."},{status:200});let s=[];s.push("strapi-categories"),s.push("strapi-categories-slugs");for(let e=1;e<=10;e++)s.push(`strapi-categories-page-${e}`);try{(0,c.revalidatePath)("/categories"),l.Ay.info("Successfully revalidated path: /categories")}catch(e){l.Ay.error("Error revalidating path /categories:",e)}let i=["/categories"];if(a&&a.slug){let e=a.slug;s.push(`strapi-category-${e}`),s.push(`strapi-category-${e}-clinics`),s.push(`strapi-category-${e}-practitioners`);try{let r=`/categories/${e}`;(0,c.revalidatePath)(r),i.push(r),l.Ay.info(`Successfully revalidated path: ${r}`)}catch(r){l.Ay.error(`Error revalidating path /categories/${e}:`,r)}l.Ay.info(`Attempting to revalidate tags for category slug: ${e}`)}if(!(s.length>0))return l.Ay.info("No specific category tags to revalidate."),n.NextResponse.json({revalidated:!1,message:"No specific category tags to revalidate."});for(let e of s)(0,c.revalidateTag)(e);return l.Ay.info("Category revalidation successful for tags:",s.join(", ")),n.NextResponse.json({revalidated:!0,revalidatedTags:s,revalidatedPaths:i,timestamp:new Date().toISOString()})}catch(e){return l.Ay.error("Error during category revalidation:",e),n.NextResponse.json({message:"Error revalidating categories",error:e.message},{status:500})}}async function g(e){if(!u)return n.NextResponse.json({message:"Revalidation secret not configured."},{status:500});let r=e.headers.get("X-Revalidate-Secret"),t=new URL(e.url),a=t.searchParams.get("tag"),s=t.searchParams.get("slug");if(r!==u&&t.searchParams.get("secret")!==u)return l.Ay.warn("Invalid GET category revalidation attempt: Incorrect or missing secret."),n.NextResponse.json({message:"Invalid token"},{status:401});try{let e=[];if(a)e.push(a);else if(s)e.push(`strapi-category-${s}`),e.push(`strapi-category-${s}-clinics`),e.push(`strapi-category-${s}-practitioners`);else{e.push("strapi-categories"),e.push("strapi-categories-slugs");for(let r=1;r<=10;r++)e.push(`strapi-categories-page-${r}`);try{(0,c.revalidatePath)("/categories"),l.Ay.info("Successfully revalidated path: /categories")}catch(e){l.Ay.error("Error revalidating path /categories:",e)}}for(let r of e)(0,c.revalidateTag)(r);let r=["/categories"];return s&&r.push(`/categories/${s}`),l.Ay.info("Manual category revalidation successful for tags:",e.join(", ")),l.Ay.info("Manual category revalidation successful for paths:",r.join(", ")),n.NextResponse.json({revalidated:!0,revalidatedTags:e,revalidatedPaths:r,timestamp:new Date().toISOString()})}catch(e){return l.Ay.error("Error during manual category revalidation:",e),n.NextResponse.json({message:"Error revalidating categories",error:e.message},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/revalidate/categories/route",pathname:"/api/revalidate/categories",filename:"route",bundlePath:"app/api/revalidate/categories/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\revalidate\\categories\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:v,serverHooks:y}=p;function h(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:v})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[7719,6391,580],()=>t(38906));module.exports=a})();