(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2959],{1350:(e,t,l)=>{Promise.resolve().then(l.bind(l,8933))},2461:(e,t,l)=>{"use strict";l.d(t,{A:()=>r});var a=l(2115);let r=a.forwardRef(function(e,t){let{title:l,titleId:r,...s}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),l?a.createElement("title",{id:r},l):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},2596:(e,t,l)=>{"use strict";l.d(t,{A:()=>a});let a=function(){for(var e,t,l=0,a="",r=arguments.length;l<r;l++)(e=arguments[l])&&(t=function e(t){var l,a,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(l=0;l<s;l++)t[l]&&(a=e(t[l]))&&(r&&(r+=" "),r+=a)}else for(a in t)t[a]&&(r&&(r+=" "),r+=a);return r}(e))&&(a&&(a+=" "),a+=t);return a}},5695:(e,t,l)=>{"use strict";var a=l(8999);l.o(a,"useParams")&&l.d(t,{useParams:function(){return a.useParams}}),l.o(a,"usePathname")&&l.d(t,{usePathname:function(){return a.usePathname}}),l.o(a,"useRouter")&&l.d(t,{useRouter:function(){return a.useRouter}}),l.o(a,"useSearchParams")&&l.d(t,{useSearchParams:function(){return a.useSearchParams}})},5922:(e,t,l)=>{"use strict";l.d(t,{default:()=>u});var a=l(5155),r=l(2461),s=l(9416),i=l(2596),n=l(6874),c=l.n(n),o=l(5695);let d=(e,t)=>t<=7?Array.from({length:t},(e,t)=>t+1):e<=3?[1,2,3,"...",t-1,t]:e>=t-2?[1,2,"...",t-2,t-1,t]:[1,"...",e-1,e,e+1,"...",t];function u(e){let{totalPages:t,currentPage:l}=e,r=(0,o.usePathname)(),s=(0,o.useSearchParams)(),i=void 0!==l?l:Number(s.get("page"))||1,n=e=>{let t=new URLSearchParams(s);return t.set("page",e.toString()),"".concat(r,"?").concat(t.toString())},c=d(i,t);return t<=1?null:(0,a.jsxs)("div",{className:"inline-flex",children:[(0,a.jsx)(g,{direction:"left",href:n(i-1),isDisabled:i<=1}),(0,a.jsx)("div",{className:"flex -space-x-px",children:c.map((e,t)=>{let l;return 0===t&&(l="first"),t===c.length-1&&(l="last"),1===c.length&&(l="single"),"..."===e&&(l="middle"),(0,a.jsx)(m,{href:n(e),page:e,position:l,isActive:i===e},"".concat(e,"-").concat(t))})}),(0,a.jsx)(g,{direction:"right",href:n(i+1),isDisabled:i>=t})]})}function m(e){let{page:t,href:l,isActive:r,position:s}=e,n=(0,i.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===s||"single"===s,"rounded-r-md":"last"===s||"single"===s,"z-10 bg-emerald-600 border-emerald-600 text-white":r,"hover:bg-gray-100":!r&&"middle"!==s,"text-gray-300 pointer-events-none":"middle"===s});return r||"middle"===s?(0,a.jsx)("div",{className:n,children:t}):(0,a.jsx)(c(),{href:l,className:n,children:t})}function g(e){let{href:t,direction:l,isDisabled:n}=e,o=(0,i.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":n,"hover:bg-gray-100":!n,"mr-2 md:mr-4":"left"===l,"ml-2 md:ml-4":"right"===l}),d="left"===l?(0,a.jsx)(r.A,{className:"w-4"}):(0,a.jsx)(s.A,{className:"w-4"});return n?(0,a.jsx)("div",{className:o,children:d}):(0,a.jsx)(c(),{className:o,href:t,children:d})}},8933:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>f});var a=l(5155),r=l(2115),s=l(5695),i=l(6500),n=l(6772),c=l(2204),o=l(927),d=l(9981),u=l(3199),m=l(5922),g=l(6874),h=l.n(g),x=l(351);function f(){let e=(0,s.useSearchParams)(),t=e.get("q")||"",l=parseInt(e.get("page")||"1",10),[g,f]=(0,r.useState)(t),[p,b]=(0,r.useState)({clinics:[],practitioners:[],blogPosts:[],categories:[],specialties:[],conditions:[],totalResults:0}),[v,j]=(0,r.useState)(!0),[N,y]=(0,r.useState)(null),w=e=>{var t,l,a,r,s,i,n,c,o,d,u,m,g,h;if(!e)return null;let x="https://nice-badge-2130241d6c.strapiapp.com",f=(null==(t=e.author_blogs)?void 0:t[0])||null,p=f?(null==(a=f.profilePicture)?void 0:a.url)?"".concat(x).concat(f.profilePicture.url):(null==(i=f.profilePicture)||null==(s=i.data)||null==(r=s.attributes)?void 0:r.url)?"".concat(x).concat(f.profilePicture.data.attributes.url):(null==(o=f.profilePicture)||null==(c=o.formats)||null==(n=c.thumbnail)?void 0:n.url)?"".concat(x).concat(f.profilePicture.formats.thumbnail.url):(null==(h=f.profilePicture)||null==(g=h.data)||null==(m=g.attributes)||null==(u=m.formats)||null==(d=u.thumbnail)?void 0:d.url)?"".concat(x).concat(f.profilePicture.data.attributes.formats.thumbnail.url):null:null,b=f?{name:f.name||"Unknown Author",slug:f.slug||"",profile_picture:p}:null,v=(null==(l=e.featuredImage)?void 0:l.url)?"".concat(x).concat(e.featuredImage.url):null,j=e.id||e.documentId||"",N={id:j,title:e.title||"Untitled Post",slug:e.slug||"post-".concat(j),excerpt:e.excerpt||null,featured_image:v,publish_date:e.publishDate||e.createdAt||new Date().toISOString(),content:e.content||"",reading_time:e.readingTime||2,isFeatured:e.isFeatured||!1,author:b};return Object.defineProperty(N,"view_count",{value:e.view_count||0,enumerable:!1}),N};(0,r.useEffect)(()=>{(async()=>{if(!t)return j(!1);j(!0),y(null);try{var e,a,r,s,n;let[c,o,d,u,m,g]=await Promise.all([i.$.clinics.getAll({query:t,page:l,pageSize:6,populate:"*"}),i.$.practitioners.getAll({query:t,page:l,pageSize:6,populate:"*"}),i.$.blog.getPosts({filters:{$or:[{title:{$containsi:t}},{excerpt:{$containsi:t}},{content:{$containsi:t}}]},pagination:{page:l,pageSize:6},populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}}),i.$.categories.getAll({query:t,page:l,pageSize:6,populate:"*"}),i.$.specialties.getAll({query:t,page:l,pageSize:6,populate:"*"}),i.$.conditions.getAll({query:t,page:l,pageSize:6,populate:"*"})]),h=(null==d?void 0:d.data)?d.data.map(w).filter(e=>null!==e):[],x=((null==c||null==(e=c.data)?void 0:e.length)||0)+((null==o||null==(a=o.data)?void 0:a.length)||0)+((null==h?void 0:h.length)||0)+((null==u||null==(r=u.data)?void 0:r.length)||0)+((null==m||null==(s=m.data)?void 0:s.length)||0)+((null==g||null==(n=g.data)?void 0:n.length)||0);b({clinics:(null==c?void 0:c.data)||[],practitioners:(null==o?void 0:o.data)||[],blogPosts:h,categories:(null==u?void 0:u.data)||[],specialties:(null==m?void 0:m.data)||[],conditions:(null==g?void 0:g.data)||[],totalResults:x})}catch(e){console.error("Error fetching search results:",e),y("Failed to load search results. Please try again later.")}finally{j(!1)}})()},[t,l,6]);let P=Math.ceil(p.totalResults/36);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-12",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Search Results"}),(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsx)(u.A,{placeholder:"Search for clinics, practitioners, or health topics...",onSearch:e=>{f(e),e.trim()&&(window.location.href="/search?q=".concat(encodeURIComponent(e)))},buttonText:"Search",buttonClassName:"bg-emerald-800 text-white hover:bg-emerald-900",className:"shadow-lg",initialValue:t})})]})})}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-12",children:v?(0,a.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500"})}):N?(0,a.jsx)("div",{className:"text-center p-8 bg-red-50 rounded-lg text-red-600",children:N}):0===p.totalResults?(0,a.jsxs)("div",{className:"text-center p-8 bg-gray-50 rounded-lg",children:[(0,a.jsx)(x.CKj,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"No results found"}),(0,a.jsxs)("p",{className:"text-gray-600 mb-6",children:["We couldn't find any matches for \"",t,'". Please try a different search term.']}),(0,a.jsx)(h(),{href:"/",className:"inline-block bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium",children:"Return to Homepage"})]}):(0,a.jsxs)("div",{className:"space-y-12",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:["Found ",p.totalResults,' results for "',t,'"']})}),p.clinics.length>0&&(0,a.jsxs)("div",{className:"mb-12",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Clinics"}),(0,a.jsx)(h(),{href:"/clinics?query=".concat(encodeURIComponent(t)),className:"text-emerald-600 hover:text-emerald-700",children:"View all clinic results"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.clinics.map(e=>{var t,l;return(0,a.jsx)(n.default,{clinic:{id:e.id,name:e.name||"Unnamed Clinic",slug:e.slug||"clinic-".concat(e.id),description:e.description,logo:(null==(t=e.logo)?void 0:t.url)?"".concat("https://nice-badge-2130241d6c.strapiapp.com").concat(e.logo.url):null,featuredImage:(null==(l=e.featuredImage)?void 0:l.url)?"".concat("https://nice-badge-2130241d6c.strapiapp.com").concat(e.featuredImage.url):null,address:e.address,contactInfo:e.contactInfo,isVerified:e.isVerified||!1},showContactInfo:!1},e.id)})})]}),p.practitioners.length>0&&(0,a.jsxs)("div",{className:"mb-12",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Practitioners"}),(0,a.jsx)(h(),{href:"/practitioners?query=".concat(encodeURIComponent(t)),className:"text-emerald-600 hover:text-emerald-700",children:"View all practitioner results"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.practitioners.map(e=>{var t;return(0,a.jsx)(c.default,{practitioner:{id:e.id,name:e.name||"Unnamed Practitioner",slug:e.slug||"practitioner-".concat(e.id),bio:e.bio,profilePicture:(null==(t=e.profilePicture)?void 0:t.url)?"".concat("https://nice-badge-2130241d6c.strapiapp.com").concat(e.profilePicture.url):null,qualifications:e.qualifications,contactInfo:e.contactInfo,isVerified:e.isVerified||!1}},e.id)})})]}),p.blogPosts.length>0&&(0,a.jsxs)("div",{className:"mb-12",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Blog Posts"}),(0,a.jsx)(h(),{href:"/blog?query=".concat(encodeURIComponent(t)),className:"text-emerald-600 hover:text-emerald-700",children:"View all blog results"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.blogPosts.map(e=>(0,a.jsx)(o.default,{post:e,showReadingTime:!0,showShareButton:!0,showBadge:!0},e.id))})]}),p.categories.length>0&&(0,a.jsxs)("div",{className:"mb-12",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Categories"}),(0,a.jsx)(h(),{href:"/categories?query=".concat(encodeURIComponent(t)),className:"text-emerald-600 hover:text-emerald-700",children:"View all category results"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:p.categories.map(e=>{var t,l;return(0,a.jsx)(d.default,{category:{id:e.id,name:e.name||"Unnamed Category",slug:e.slug||"category-".concat(e.id),description:e.description,icon:(null==(t=e.icon)?void 0:t.url)?"".concat("https://nice-badge-2130241d6c.strapiapp.com").concat(e.icon.url):null,featured_image:(null==(l=e.featuredImage)?void 0:l.url)?"".concat("https://nice-badge-2130241d6c.strapiapp.com").concat(e.featuredImage.url):null}},e.id)})})]}),P>1&&(0,a.jsx)("div",{className:"mt-12 flex justify-center",children:(0,a.jsx)(m.default,{totalPages:P})})]})}),(0,a.jsx)("div",{className:"bg-gray-50 py-12",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Can't find what you're looking for?"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Browse our categories or contact us for personalized assistance with your natural healing journey."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(h(),{href:"/categories",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium",children:"Explore Categories"}),(0,a.jsx)(h(),{href:"/contact",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-medium",children:"Contact Us"})]})]})})]})}},9416:(e,t,l)=>{"use strict";l.d(t,{A:()=>r});var a=l(2115);let r=a.forwardRef(function(e,t){let{title:l,titleId:r,...s}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),l?a.createElement("title",{id:r},l):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[844,5479,6874,3063,6079,6052,2112,927,5825,2773,8441,1684,7358],()=>t(1350)),_N_E=e.O()}]);