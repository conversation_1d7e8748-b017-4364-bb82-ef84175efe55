# Manual Webhook Configuration in Strapi

This document provides step-by-step instructions for configuring webhooks in Strapi to trigger on-demand revalidation in your Next.js application.

## Prerequisites

- Access to the Strapi admin panel
- The revalidation token: `3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
- The Next.js site URL: `https://www.naturalhealingnow.com`

## Option 1: Configure Default Headers (Recommended)

The most secure and efficient way to set up webhook authentication is by configuring default headers in your Strapi server configuration. This approach applies the authentication token to all webhooks automatically.

1. Access your Strapi project files
2. Create or edit the file `./config/server.js` (or `./config/server.ts` for TypeScript)
3. Add the following configuration:

```javascript
module.exports = {
  webhooks: {
    defaultHeaders: {
      Authorization: "Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
    },
  },
};
```

4. Restart your Strapi server to apply the changes

With this configuration, all webhooks will automatically include the Authorization header with your token.

## Option 2: Configure Individual Webhooks

If you don't have access to modify the server configuration files, you can set up individual webhooks through the Strapi admin panel:

1. Log in to your Strapi admin panel at `https://nice-badge-2130241d6c.strapiapp.com/admin`
2. Navigate to Settings > Webhooks
3. You should see a list of existing webhooks (if any)

For each content type (blog posts, practitioners, clinics, etc.), create or update a webhook with the following configuration:

### Blog Posts Webhook

1. Click "Create new webhook" or edit the existing "Revalidate Blog Posts" webhook
2. Configure the webhook as follows:
   - **Name**: `Revalidate Blog Posts`
   - **URL**: `https://www.naturalhealingnow.com/api/revalidate`
   - **Headers**:
     - Key: `Content-Type`
     - Value: `application/json`
     - Key: `Authorization`
     - Value: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
   - **Events**:
     - Under "Entry", select all events for the "blog-post" content type:
       - `create`
       - `update`
       - `delete`
       - `publish`
       - `unpublish`
3. Click "Save" to create or update the webhook

### Practitioners Webhook

1. Click "Create new webhook" or edit the existing "Revalidate Practitioners" webhook
2. Configure the webhook as follows:
   - **Name**: `Revalidate Practitioners`
   - **URL**: `https://www.naturalhealingnow.com/api/revalidate`
   - **Headers**:
     - Key: `Content-Type`
     - Value: `application/json`
     - Key: `Authorization`
     - Value: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
   - **Events**:
     - Under "Entry", select all events for the "practitioner" content type:
       - `create`
       - `update`
       - `delete`
       - `publish`
       - `unpublish`
3. Click "Save" to create or update the webhook

### Clinics Webhook

1. Click "Create new webhook" or edit the existing "Revalidate Clinics" webhook
2. Configure the webhook as follows:
   - **Name**: `Revalidate Clinics`
   - **URL**: `https://www.naturalhealingnow.com/api/revalidate`
   - **Headers**:
     - Key: `Content-Type`
     - Value: `application/json`
     - Key: `Authorization`
     - Value: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
   - **Events**:
     - Under "Entry", select all events for the "clinic" content type:
       - `create`
       - `update`
       - `delete`
       - `publish`
       - `unpublish`
3. Click "Save" to create or update the webhook

### Categories Webhook

1. Click "Create new webhook" or edit the existing "Revalidate Categories" webhook
2. Configure the webhook as follows:
   - **Name**: `Revalidate Categories`
   - **URL**: `https://www.naturalhealingnow.com/api/revalidate`
   - **Headers**:
     - Key: `Content-Type`
     - Value: `application/json`
     - Key: `Authorization`
     - Value: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
   - **Events**:
     - Under "Entry", select all events for the "category" content type:
       - `create`
       - `update`
       - `delete`
       - `publish`
       - `unpublish`
3. Click "Save" to create or update the webhook

### Specialties Webhook

1. Click "Create new webhook" or edit the existing "Revalidate Specialties" webhook
2. Configure the webhook as follows:
   - **Name**: `Revalidate Specialties`
   - **URL**: `https://www.naturalhealingnow.com/api/revalidate`
   - **Headers**:
     - Key: `Content-Type`
     - Value: `application/json`
     - Key: `Authorization`
     - Value: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
   - **Events**:
     - Under "Entry", select all events for the "specialty" content type:
       - `create`
       - `update`
       - `delete`
       - `publish`
       - `unpublish`
3. Click "Save" to create or update the webhook

## Understanding Webhook Payloads

When Strapi sends a webhook, it includes a specific payload format. Here's an example of what the payload looks like for an `entry.update` event:

```json
{
  "event": "entry.update",
  "createdAt": "2020-01-10T08:58:26.563Z",
  "model": "api::blog-post.blog-post",
  "entry": {
    "id": 1,
    "title": "My Blog Post",
    "slug": "my-blog-post",
    "content": "This is my blog post content",
    "createdAt": "2020-01-10T08:47:36.264Z",
    "updatedAt": "2020-01-10T08:58:26.210Z"
  }
}
```

Our revalidation API has been updated to handle this payload format. It extracts the content type from the `model` field and the ID and slug from the `entry` field.

## Testing the Webhooks

After configuring the webhooks, you should test them to ensure they're working correctly:

1. In the Strapi admin panel, go to Settings > Webhooks
2. For each webhook, click the "Trigger" button to send a test request
3. Check the response status to ensure it's successful (200 OK)
4. You can also check the Vercel logs to see if the revalidation API was called

## Verifying Revalidation

To verify that the webhooks are triggering revalidation correctly:

1. Make a change to a content item in Strapi (e.g., update a blog post)
2. Publish the change
3. Check the Strapi webhook logs to ensure the webhook was triggered
4. Check the Vercel logs to ensure the revalidation API was called
5. Visit the page on your site to ensure the change is reflected

## Troubleshooting

If the webhooks aren't working as expected, check the following:

1. **Webhook URL**: Make sure the webhook URL is correct and accessible from the internet.
2. **Authorization Header**: Verify that the Authorization header is correctly set with the Bearer token.
3. **Events**: Make sure the correct events are selected for each webhook.
4. **Server Configuration**: If using Option 1, ensure your server configuration is correct and the server has been restarted.
5. **Vercel Logs**: Check the Vercel logs for any errors in the revalidation API.
6. **Strapi Webhook Logs**: Check the Strapi webhook logs for any failed webhook deliveries.

## Additional Resources

- [Strapi Webhooks Documentation](https://docs.strapi.io/dev-docs/webhooks)
- [Next.js Revalidation Documentation](https://nextjs.org/docs/app/building-your-application/data-fetching/revalidating)
