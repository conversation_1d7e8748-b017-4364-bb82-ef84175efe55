(()=>{var e={};e.id=5197,e.ids=[5197],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32144:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{POST:()=>d});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(62351);let p=process.env.REVALIDATION_TOKEN;async function d(e){try{if(e.nextUrl.searchParams.get("secret")!==p)return o.NextResponse.json({message:"Invalid revalidation token"},{status:401});let t=await e.json();return console.log("Received specialities revalidation webhook:",JSON.stringify({event:t.event,model:t.model,entry:t.entry?{id:t.entry.id,slug:t.entry.slug}:null})),(0,l.revalidatePath)("/specialities"),(0,l.revalidateTag)("strapi-specialties"),(0,l.revalidateTag)("strapi-specialties-all"),t.entry&&t.entry.slug&&((0,l.revalidatePath)(`/specialities/${t.entry.slug}`),(0,l.revalidateTag)(`strapi-specialty-${t.entry.slug}`)),o.NextResponse.json({revalidated:!0,message:"Specialities revalidated successfully",timestamp:new Date().toISOString()},{status:200})}catch(e){return console.error("Error revalidating specialities:",e),o.NextResponse.json({revalidated:!1,message:"Error revalidating specialities",error:e instanceof Error?e.message:String(e)},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/revalidate/specialities/route",pathname:"/api/revalidate/specialities",filename:"route",bundlePath:"app/api/revalidate/specialities/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\revalidate\\specialities\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:v,serverHooks:g}=u;function x(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:v})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,6391,580],()=>r(32144));module.exports=s})();