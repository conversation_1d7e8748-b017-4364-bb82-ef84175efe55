/**
 * Server-side API utilities for making requests to Strapi
 * This file should only be imported in server components or API routes
 */
import { cookies } from 'next/headers';
import qs from 'qs';

// Get Strapi URL from environment variable
const API_URL = process.env.NEXT_PUBLIC_API_URL;
const API_PATH = '/api'; // Default API path for Strapi

// Log the API URL for debugging
console.log('Server API Client using URL:', API_URL || 'No API URL found in environment variables');

// Critical check to prevent localhost fallback in production
if (!API_URL && process.env.NODE_ENV === 'production') {
  const errorMessage = 'CRITICAL ERROR: No Strapi API URL found in environment variables. Please set NEXT_PUBLIC_API_URL in your Vercel environment variables.';
  console.error(errorMessage);
  // Potentially throw an error here if running in a context where it can be caught,
  // or ensure this check prevents app startup if critical.
}

const getBaseUrl = (): string => {
  if (!API_URL) {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('No Strapi API URL found in environment variables. Please set NEXT_PUBLIC_API_URL.');
    }
    // In development, if NEXT_PUBLIC_API_URL is not set, we might assume a local Strapi instance.
    // However, it's better to require it to be set explicitly.
    // For now, let's throw an error or return a clearly invalid URL to force configuration.
    console.warn('WARNING: NEXT_PUBLIC_API_URL is not set. API calls will likely fail.');
    return `http://localhost:1337${API_PATH}`; // Fallback for local dev, ensure Strapi runs here
  }
  return `${API_URL}${API_PATH}`;
};

interface StrapiRequestOptions extends RequestInit {
  params?: Record<string, any>; // For query parameters, to be stringified by qs
}

/**
 * Makes a GET request to the Strapi API from the server
 * Uses the API token from environment variables for authentication.
 * Properly implements Next.js ISR caching with revalidate and tags options.
 */
export const fetchFromApiServer = async <T>(
  endpoint: string,
  options: StrapiRequestOptions = {}
): Promise<T> => {
  const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;
  const baseUrl = getBaseUrl();
  let url = `${baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...(STRAPI_API_TOKEN && { Authorization: `Bearer ${STRAPI_API_TOKEN}` }),
    ...(options.headers || {}),
  };

  // Build query string from params
  if (options.params && (options.method === 'GET' || !options.method)) {
    const queryString = qs.stringify(options.params, { encodeValuesOnly: true });
    if (queryString) {
      url = `${url}?${queryString}`;
    }
  }

  // Remove params from options as it's not a standard RequestInit property
  const { params, ...fetchOptions } = options;

  try {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Server API] Requesting: ${options.method || 'GET'} ${url}`);
      if (fetchOptions.next) console.log(`[Server API] Using Next.js cache options:`, fetchOptions.next);
    }

    // Ensure we have proper next.js cache options
    const nextOptions = fetchOptions.next || {};

    // If revalidate is not explicitly set to false, default to 3600 seconds (1 hour)
    if (nextOptions.revalidate === undefined && nextOptions.revalidate !== false) {
      nextOptions.revalidate = 3600; // Default to 1 hour
    }

    // If no tags are provided, add a default tag based on the endpoint
    if (!nextOptions.tags || nextOptions.tags.length === 0) {
      const contentType = endpoint.split('/')[0].replace(/^\/+/, '');
      if (contentType) {
        nextOptions.tags = [`strapi-${contentType}`];
      }
    }

    const response = await fetch(url, {
      ...fetchOptions,
      headers,
      next: nextOptions,
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText, details: await response.text().catch(() => '') };
      }
      console.error(`Server: API Error (${url}): Status ${response.status}`, errorData);
      const error = new Error(`API Error: ${response.status} ${response.statusText}`);
      // @ts-ignore
      error.response = { status: response.status, data: errorData };
      throw error;
    }
    return response.json() as T;
  } catch (error: any) {
    console.error(`Server: Error fetching from API (${url}):`, error.message || error);
    if (error.response) {
      console.error('Server: Response status:', error.response.status);
      console.error('Server: Response data:', error.response.data);
    }
    throw error;
  }
};

/**
 * Makes a GET request to the Strapi API from the server with user authentication
 * Uses the JWT token from cookies for authentication.
 * Note: Using this function during rendering (e.g., in a Server Component) will make the route dynamic.
 */
export const fetchFromApiServerWithUserAuth = async <T>(
  endpoint: string,
  options: StrapiRequestOptions = {}
): Promise<T> => {
  const c = cookies(); // Assign to a variable first
  const token = c.get('jwt')?.value;
  const baseUrl = getBaseUrl();
  let url = `${baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
    ...(options.headers || {}),
  };

  if (options.params && (options.method === 'GET' || !options.method)) {
    const queryString = qs.stringify(options.params, { encodeValuesOnly: true });
    if (queryString) {
      url = `${url}?${queryString}`;
    }
  }

  const { params, ...fetchOptions } = options;

  try {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Server API User Auth] Requesting: ${options.method || 'GET'} ${url}`);
      if (fetchOptions.next) console.log(`[Server API User Auth] Using Next.js cache options:`, fetchOptions.next);
    }
    const response = await fetch(url, {
      ...fetchOptions,
      headers,
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText, details: await response.text().catch(() => '') };
      }
      console.error(`Server: API Error with user auth (${url}): Status ${response.status}`, errorData);
      const error = new Error(`API Error with user auth: ${response.status} ${response.statusText}`);
      // @ts-ignore
      error.response = { status: response.status, data: errorData };
      throw error;
    }
    return response.json() as T;
  } catch (error: any) {
    console.error(`Server: Error fetching from API with user auth (${url}):`, error.message || error);
     if (error.response) {
      console.error('Server: Response status:', error.response.status);
      console.error('Server: Response data:', error.response.data);
    }
    throw error;
  }
};

/**
 * Makes a POST request to the Strapi API from the server
 * Uses the API token for authentication.
 */
export const postToApiServer = async <T>(
  endpoint: string,
  data: any,
  options: StrapiRequestOptions = {} // Changed from AxiosRequestConfig
): Promise<T> => {
  const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...(STRAPI_API_TOKEN && { Authorization: `Bearer ${STRAPI_API_TOKEN}` }),
    ...(options.headers || {}),
  };

  // Remove params from options if it exists, as body is used for POST
  const { params, ...fetchOptions } = options;

  try {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Server API] Posting to: ${url}`);
      if (fetchOptions.next) console.log(`[Server API] Using Next.js cache options:`, fetchOptions.next);
    }
    const response = await fetch(url, {
      ...fetchOptions,
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText, details: await response.text().catch(() => '') };
      }
      console.error(`Server: API Error posting to (${url}): Status ${response.status}`, errorData);
      const error = new Error(`API Error posting: ${response.status} ${response.statusText}`);
      // @ts-ignore
      error.response = { status: response.status, data: errorData };
      throw error;
    }
    return response.json() as T;
  } catch (error: any) {
    console.error(`Server: Error posting to API (${url}):`, error.message || error);
    if (error.response) {
      console.error('Server: Response status:', error.response.status);
      console.error('Server: Response data:', error.response.data);
    }
    throw error;
  }
};

/**
 * Makes a POST request to the Strapi API from the server with user authentication
 * Uses the JWT token from cookies for authentication.
 * Note: Using this function during rendering will make the route dynamic.
 */
export const postToApiServerWithUserAuth = async <T>(
  endpoint: string,
  data: any,
  options: StrapiRequestOptions = {} // Changed from AxiosRequestConfig
): Promise<T> => {
  const c = cookies(); // Assign to a variable first
  const token = c.get('jwt')?.value;
  const baseUrl = getBaseUrl();
  const url = `${baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
    ...(options.headers || {}),
  };

  const { params, ...fetchOptions } = options;

  try {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Server API User Auth] Posting to: ${url}`);
      if (fetchOptions.next) console.log(`[Server API User Auth] Using Next.js cache options:`, fetchOptions.next);
    }
    const response = await fetch(url, {
      ...fetchOptions,
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText, details: await response.text().catch(() => '') };
      }
      console.error(`Server: API Error posting with user auth (${url}): Status ${response.status}`, errorData);
      const error = new Error(`API Error posting with user auth: ${response.status} ${response.statusText}`);
      // @ts-ignore
      error.response = { status: response.status, data: errorData };
      throw error;
    }
    return response.json() as T;
  } catch (error: any) {
    console.error(`Server: Error posting to API with user auth (${url}):`, error.message || error);
    if (error.response) {
      console.error('Server: Response status:', error.response.status);
      console.error('Server: Response data:', error.response.data);
    }
    throw error;
  }
};
