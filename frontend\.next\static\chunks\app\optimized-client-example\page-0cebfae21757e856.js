(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4735],{2306:(e,t,a)=>{Promise.resolve().then(a.bind(a,7868))},5695:(e,t,a)=>{"use strict";var s=a(8999);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},7868:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var s=a(5155),r=a(2115),n=a(1890);let l=(0,r.cache)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{let a=e.startsWith("/")?e:"/".concat(e),{params:s,revalidate:r=3600,tags:l=[]}=t,i=s?"?"+new URLSearchParams(Object.entries(s).reduce((e,t)=>{let[a,s]=t;return"object"==typeof s&&null!==s?e[a]=JSON.stringify(s):e[a]=String(s),e},{})).toString():"",o=await fetch("".concat("https://nice-badge-2130241d6c.strapiapp.com").concat("/api").concat(a).concat(i),{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(n.env.STRAPI_API_TOKEN||"")},next:{revalidate:r,tags:l}});if(!o.ok)throw Error("Error fetching from API: ".concat(o.status," ").concat(o.statusText));return o.json()}catch(t){if(console.error("Error fetching from API (".concat(e,"):"),t),e.includes("/")&&e.split("/").filter(Boolean).length>0)return{data:[]};return{}}}),i=(0,r.cache)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{id:a,params:s,revalidate:r=!1,isDetailPage:n=!1}=t,i=a?"/".concat(e,"/").concat(a):"/".concat(e),o=function(e,t){let a=["strapi-".concat(e)];return t&&a.push("strapi-".concat(e,"-").concat(t)),a}(e,a);n||o.push("strapi-content");try{return l(i,{params:s,revalidate:!n&&(r||3600),tags:o})}catch(t){return console.error("Error in fetchContentType for ".concat(e,":"),t),{data:[]}}});function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},[a,s]=(0,r.useState)(null),[n,l]=(0,r.useState)(!0),[o,c]=(0,r.useState)(null),d=async function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0],l(!0),c(null);try{let a=await i(e,t);s(a)}catch(e){c(e instanceof Error?e:Error(String(e)))}finally{l(!1)}};return(0,r.useEffect)(()=>{d()},[e,JSON.stringify(t)]),{data:a,isLoading:n,error:o,refetch:()=>d(!0)}}var c=a(8102);function d(){let[e,t]=(0,r.useState)("featured");return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Optimized Client-Side Data Fetching"}),(0,s.jsx)(c.default,{}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("p",{className:"text-lg mb-4",children:"This page demonstrates client-side optimized data fetching from Strapi. The data is cached in memory and shared between components."}),(0,s.jsxs)("div",{className:"flex border-b",children:[(0,s.jsx)("button",{className:"px-4 py-2 ".concat("featured"===e?"border-b-2 border-blue-500 text-blue-500":"text-gray-500"),onClick:()=>t("featured"),children:"Featured Content"}),(0,s.jsx)("button",{className:"px-4 py-2 ".concat("categories"===e?"border-b-2 border-blue-500 text-blue-500":"text-gray-500"),onClick:()=>t("categories"),children:"Categories"}),(0,s.jsx)("button",{className:"px-4 py-2 ".concat("blog"===e?"border-b-2 border-blue-500 text-blue-500":"text-gray-500"),onClick:()=>t("blog"),children:"Blog Posts"})]})]}),"featured"===e&&(0,s.jsx)(u,{}),"categories"===e&&(0,s.jsx)(h,{}),"blog"===e&&(0,s.jsx)(m,{}),(0,s.jsxs)("div",{className:"mt-8 p-6 bg-blue-50 rounded-lg",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"How This Reduces API Calls"}),(0,s.jsx)("p",{className:"mb-2",children:"This implementation reduces API calls to Strapi in several ways:"}),(0,s.jsxs)("ol",{className:"list-decimal pl-5 space-y-2",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"In-memory caching:"})," Responses are cached with content-type specific TTLs, preventing unnecessary refetching of data that hasn't changed."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Request deduplication:"})," Multiple components requesting the same data will share a single API call instead of each making their own request."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Content-type specific caching:"})," Different cache durations based on how frequently content changes (e.g., global settings are cached longer than blog posts)."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Prefetching:"})," Common data is preloaded, reducing the need for API calls when navigating between pages."]})]}),(0,s.jsxs)("p",{className:"mt-4",children:["For more details, see the documentation in ",(0,s.jsx)("code",{children:"frontend/src/docs/OPTIMIZED_API_CALLS.md"}),"."]})]})]})}function u(){var e,t;let{data:a,isLoading:r,error:n}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:4;return o("clinics",{params:{filters:{isFeatured:{$eq:!0}},pagination:{pageSize:e},populate:"*"}})}(),{data:l,isLoading:i,error:c}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:4;return o("practitioners",{params:{filters:{isFeatured:{$eq:!0}},pagination:{pageSize:e},populate:"*"}})}();return(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Featured Clinics"}),r?(0,s.jsx)(p,{}):n?(0,s.jsx)(g,{message:"Failed to load clinics"}):(0,s.jsx)("ul",{className:"list-disc pl-5",children:null==a||null==(e=a.data)?void 0:e.map(e=>{var t;return(0,s.jsx)("li",{children:e.name||(null==(t=e.attributes)?void 0:t.name)},e.id||e.documentId)})})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Featured Practitioners"}),i?(0,s.jsx)(p,{}):c?(0,s.jsx)(g,{message:"Failed to load practitioners"}):(0,s.jsx)("ul",{className:"list-disc pl-5",children:null==l||null==(t=l.data)?void 0:t.map(e=>{var t,a;let r=e.firstName||(null==(t=e.attributes)?void 0:t.firstName),n=e.lastName||(null==(a=e.attributes)?void 0:a.lastName);return(0,s.jsxs)("li",{children:[r," ",n]},e.id||e.documentId)})})]})]})}function h(){var e;let{data:t,isLoading:a,error:r}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return o("categories",{params:{pagination:{pageSize:e},populate:"*"}})}(10);return(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Categories"}),a?(0,s.jsx)(p,{}):r?(0,s.jsx)(g,{message:"Failed to load categories"}):(0,s.jsx)("ul",{className:"list-disc pl-5",children:null==t||null==(e=t.data)?void 0:e.map(e=>{var t;return(0,s.jsx)("li",{children:e.name||(null==(t=e.attributes)?void 0:t.name)},e.id||e.documentId)})})]})}function m(){var e;let[t,a]=(0,r.useState)("featured"),{data:n,isLoading:l,error:i}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;return o("blog-posts",{params:{filters:{isFeatured:{$eq:!0}},sort:"publishDate:desc",pagination:{pageSize:e},populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}}})}(),{data:c,isLoading:d,error:u}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;return o("blog-posts",{params:{sort:"publishDate:desc",pagination:{pageSize:e},populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}}})}(5),{data:h,isLoading:m,error:x}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;return o("blog-posts",{params:{sort:"viewCount:desc",pagination:{pageSize:e},populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}}})}(5),b="featured"===t?n:"latest"===t?c:h;return(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsxs)("div",{className:"flex mb-4",children:[(0,s.jsx)("button",{className:"px-4 py-2 mr-2 rounded ".concat("featured"===t?"bg-blue-500 text-white":"bg-gray-200"),onClick:()=>a("featured"),children:"Featured"}),(0,s.jsx)("button",{className:"px-4 py-2 mr-2 rounded ".concat("latest"===t?"bg-blue-500 text-white":"bg-gray-200"),onClick:()=>a("latest"),children:"Latest"}),(0,s.jsx)("button",{className:"px-4 py-2 rounded ".concat("popular"===t?"bg-blue-500 text-white":"bg-gray-200"),onClick:()=>a("popular"),children:"Popular"})]}),(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"featured"===t?"Featured Blog Posts":"latest"===t?"Latest Blog Posts":"Popular Blog Posts"}),("featured"===t?l:"latest"===t?d:m)?(0,s.jsx)(p,{}):("featured"===t?i:"latest"===t?u:x)?(0,s.jsx)(g,{message:"Failed to load ".concat(t," blog posts")}):(0,s.jsx)("ul",{className:"list-disc pl-5",children:null==b||null==(e=b.data)?void 0:e.map(e=>{var t;return(0,s.jsx)("li",{children:e.title||(null==(t=e.attributes)?void 0:t.title)},e.id||e.documentId)})})]})}function p(){return(0,s.jsxs)("div",{className:"animate-pulse space-y-3",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]})}function g(e){let{message:t}=e;return(0,s.jsx)("div",{className:"text-red-500 p-4 bg-red-50 rounded",children:(0,s.jsx)("p",{children:t})})}},8102:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});var s=a(5155),r=a(6874),n=a.n(r),l=a(5695);function i(){let e=(0,l.usePathname)();return(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Optimization Examples"}),(0,s.jsx)("p",{className:"mb-4",children:"These examples demonstrate different approaches to reducing API calls to Strapi."}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:[{href:"/optimized-example",label:"Server-Side Optimization"},{href:"/optimized-client-example",label:"Client-Side Optimization"}].map(t=>(0,s.jsx)(n(),{href:t.href,className:"px-4 py-2 rounded ".concat(e===t.href?"bg-blue-500 text-white":"bg-white text-blue-500 hover:bg-blue-100"),children:t.label},t.href))})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(2306)),_N_E=e.O()}]);