(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2379],{1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},getImageProps:function(){return o}});let a=r(8229),n=r(8883),i=r(3063),l=a._(r(4663));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1440,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"custom",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=i.Image},2461:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(2115);let n=a.forwardRef(function(e,t){let{title:r,titleId:n,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},i),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},2596:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=function(){for(var e,t,r=0,a="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,a,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(a=e(t[r]))&&(n&&(n+=" "),n+=a)}else for(a in t)t[a]&&(n&&(n+=" "),n+=a);return n}(e))&&(a&&(a+=" "),a+=t);return a}},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>u});var a=r(2115),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=a.createContext&&a.createContext(n),l=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var a,n,i;a=e,n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in a?Object.defineProperty(a,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>a.createElement(d,o({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:n,size:i,title:s}=e,u=function(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)r=i[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,l),d=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,u,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),s&&a.createElement("title",null,s),e.children)};return void 0!==i?a.createElement(i.Consumer,null,e=>t(e)):t(n)}},4663:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,imageUtils:()=>s});var a=r(1890);let n={count:0,errors:0,totalTime:0,slowestTime:0,slowestImage:""},i={enableMetrics:"true"===a.env.NEXT_PUBLIC_CACHE_METRICS,useHighQuality:!0,disableOptimization:"true"===a.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION,defaultQuality:85,avifQuality:80,webpQuality:85,jpegQuality:90,pngQuality:90,maxDevicePixelRatio:3,minWidth:20,blurUpRadius:10};function l(e,t,r){let a="https://nice-badge-2130241d6c.media.strapiapp.com",n=r||(e.toLowerCase().match(/\.avif$/i)?i.avifQuality:e.toLowerCase().match(/\.webp$/i)?i.webpQuality:e.toLowerCase().match(/\.jpe?g$/i)?i.jpegQuality:e.toLowerCase().match(/\.png$/i)?i.pngQuality:e.toLowerCase().match(/\.(jpe?g|png)$/i)?i.jpegQuality:i.webpQuality),l=Math.min(window.devicePixelRatio||1,i.maxDevicePixelRatio);if(t<i.minWidth)return e;try{let r=Math.round(t*l),i=new URL(e.startsWith("http")?e:"".concat(a).concat(e.startsWith("/")?e:"/".concat(e)));if(i.hostname.includes("strapiapp.com")||i.hostname.includes("localhost")){i.searchParams.set("w",r.toString()),i.searchParams.set("q",n.toString());let t=e.toLowerCase().match(/\.(jpe?g|png)$/i);i.searchParams.has("format")||i.searchParams.set("format",t?"avif":"webp");{let e=Array.from(i.searchParams.entries()).sort();i.search=e.map(e=>{let[t,r]=e;return"".concat(t,"=").concat(r)}).join("&")}t&&i.searchParams.set("sharp","10"),"http:"===i.protocol&&(i.protocol="https:")}return i.toString()}catch(r){if(e.startsWith("/"))return"".concat(a).concat(e,"?w=").concat(t,"&q=").concat(n);return e}}function o(e){let{src:t,width:r,quality:a}=e,o=i.enableMetrics?performance.now():0;if(!t)return"";try{if(!(t&&!i.disableOptimization&&!("string"==typeof t&&[".svg",".gif",".webp",".avif"].some(e=>t.toLowerCase().endsWith(e))||t.startsWith("http")&&!t.includes("strapiapp.com")&&!t.includes("localhost:1337"))&&1))return t;let e=l(t,r,a);if(i.enableMetrics&&o){let e=performance.now()-o;n.count++,n.totalTime+=e,e>n.slowestTime&&(n.slowestTime=e,n.slowestImage=t)}return e}catch(e){return i.enableMetrics&&n.errors++,t}}let s={getBlurDataUrl:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return e?"".concat(l(e,t,10),"&blur=80"):""},preloadImage:(e,t)=>{if(!e)return;let r=new Image;return r.src=o({src:e,width:t,quality:i.defaultQuality}),r},resetMetrics:()=>{n.count=0,n.errors=0,n.totalTime=0,n.slowestTime=0,n.slowestImage=""},getMetrics:()=>({...n})}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},5922:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(5155),n=r(2461),i=r(9416),l=r(2596),o=r(6874),s=r.n(o),c=r(5695);let u=(e,t)=>t<=7?Array.from({length:t},(e,t)=>t+1):e<=3?[1,2,3,"...",t-1,t]:e>=t-2?[1,2,"...",t-2,t-1,t]:[1,"...",e-1,e,e+1,"...",t];function d(e){let{totalPages:t,currentPage:r}=e,n=(0,c.usePathname)(),i=(0,c.useSearchParams)(),l=void 0!==r?r:Number(i.get("page"))||1,o=e=>{let t=new URLSearchParams(i);return t.set("page",e.toString()),"".concat(n,"?").concat(t.toString())},s=u(l,t);return t<=1?null:(0,a.jsxs)("div",{className:"inline-flex",children:[(0,a.jsx)(f,{direction:"left",href:o(l-1),isDisabled:l<=1}),(0,a.jsx)("div",{className:"flex -space-x-px",children:s.map((e,t)=>{let r;return 0===t&&(r="first"),t===s.length-1&&(r="last"),1===s.length&&(r="single"),"..."===e&&(r="middle"),(0,a.jsx)(m,{href:o(e),page:e,position:r,isActive:l===e},"".concat(e,"-").concat(t))})}),(0,a.jsx)(f,{direction:"right",href:o(l+1),isDisabled:l>=t})]})}function m(e){let{page:t,href:r,isActive:n,position:i}=e,o=(0,l.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===i||"single"===i,"rounded-r-md":"last"===i||"single"===i,"z-10 bg-emerald-600 border-emerald-600 text-white":n,"hover:bg-gray-100":!n&&"middle"!==i,"text-gray-300 pointer-events-none":"middle"===i});return n||"middle"===i?(0,a.jsx)("div",{className:o,children:t}):(0,a.jsx)(s(),{href:r,className:o,children:t})}function f(e){let{href:t,direction:r,isDisabled:o}=e,c=(0,l.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":o,"hover:bg-gray-100":!o,"mr-2 md:mr-4":"left"===r,"ml-2 md:ml-4":"right"===r}),u="left"===r?(0,a.jsx)(n.A,{className:"w-4"}):(0,a.jsx)(i.A,{className:"w-4"});return o?(0,a.jsx)("div",{className:c,children:u}):(0,a.jsx)(s(),{className:c,href:t,children:u})}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var a=r(1469),n=r.n(a)},8400:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(5155),n=r(2115),i=r(5695),l=r(351);function o(e){var t;let{placeholder:r,paramName:o="query",icon:s}=e,c=(0,i.useSearchParams)(),u=(0,i.usePathname)(),{replace:d}=(0,i.useRouter)(),m=function(e,t,r){var a=this,i=(0,n.useRef)(null),l=(0,n.useRef)(0),o=(0,n.useRef)(null),s=(0,n.useRef)([]),c=(0,n.useRef)(),u=(0,n.useRef)(),d=(0,n.useRef)(e),m=(0,n.useRef)(!0);d.current=e;var f="undefined"!=typeof window,h=!t&&0!==t&&f;if("function"!=typeof e)throw TypeError("Expected a function");t=+t||0;var g=!!(r=r||{}).leading,p=!("trailing"in r)||!!r.trailing,v="maxWait"in r,w="debounceOnServer"in r&&!!r.debounceOnServer,b=v?Math.max(+r.maxWait||0,t):null;return(0,n.useEffect)(function(){return m.current=!0,function(){m.current=!1}},[]),(0,n.useMemo)(function(){var e=function(e){var t=s.current,r=c.current;return s.current=c.current=null,l.current=e,u.current=d.current.apply(r,t)},r=function(e,t){h&&cancelAnimationFrame(o.current),o.current=h?requestAnimationFrame(e):setTimeout(e,t)},n=function(e){if(!m.current)return!1;var r=e-i.current;return!i.current||r>=t||r<0||v&&e-l.current>=b},y=function(t){return o.current=null,p&&s.current?e(t):(s.current=c.current=null,u.current)},x=function e(){var a=Date.now();if(n(a))return y(a);if(m.current){var o=t-(a-i.current);r(e,v?Math.min(o,b-(a-l.current)):o)}},j=function(){if(f||w){var d=Date.now(),h=n(d);if(s.current=[].slice.call(arguments),c.current=a,i.current=d,h){if(!o.current&&m.current)return l.current=i.current,r(x,t),g?e(i.current):u.current;if(v)return r(x,t),e(i.current)}return o.current||r(x,t),u.current}};return j.cancel=function(){o.current&&(h?cancelAnimationFrame(o.current):clearTimeout(o.current)),l.current=0,s.current=i.current=c.current=o.current=null},j.isPending=function(){return!!o.current},j.flush=function(){return o.current?y(Date.now()):u.current},j},[g,v,t,b,p,h,f,w])}(e=>{console.log("Searching... ".concat(e));let t=new URLSearchParams(c);t.set("page","1"),e?t.set(o,e):t.delete(o),d("".concat(u,"?").concat(t.toString()))},500);return(0,a.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,a.jsx)("label",{htmlFor:o,className:"sr-only",children:"Search"}),(0,a.jsx)("input",{id:o,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:r,onChange:e=>{m(e.target.value)},defaultValue:null==(t=c.get(o))?void 0:t.toString()}),s||(0,a.jsx)(l.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},9416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(2115);let n=a.forwardRef(function(e,t){let{title:r,titleId:n,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},i),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},9636:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,9981)),Promise.resolve().then(r.bind(r,5922)),Promise.resolve().then(r.bind(r,8400))},9907:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(5155),n=r(2115),i=r(6766),l=r(351),o=r(4663);let s=e=>{var t;let{src:r,alt:s,width:c,height:u,className:d="",fallbackClassName:m="",showPlaceholder:f=!0,advancedBlur:h=!1,preload:g=!1,fadeIn:p=!0,wrapperAs:v="div",fillContainer:w=!1,sizes:b="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",style:y,priority:x=!1,qualityOverride:j,...P}=e,N=(0,n.useRef)(!0),[O,I]=(0,n.useState)(x?"loaded":"loading"),[S,C]=(0,n.useState)({width:w?void 0:c,height:w?void 0:u}),R="string"==typeof r?r:(null==r?void 0:r.src)||(null==r?void 0:r.url)||(null==r||null==(t=r.default)?void 0:t.src)||null,E=h&&f&&R?o.imageUtils.getBlurDataUrl(R,20):f?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+":void 0;(0,n.useEffect)(()=>{if(g&&R&&!x&&1){let e=o.imageUtils.preloadImage(R,"number"==typeof c?c:500);e&&(e.onload=()=>{N.current&&I("loaded")},e.onerror=()=>{N.current&&I("error")})}return()=>{N.current=!1}},[R,g,x,c]);let M="number"==typeof c&&"number"==typeof u&&c>0&&u>0?c/u:void 0,A=(0,n.useCallback)(e=>{if(!w&&(null==e?void 0:e.target)){let{naturalWidth:t,naturalHeight:r}=e.target;t&&r&&N.current&&C({width:t,height:r})}N.current&&I("loaded")},[w]),L=(0,n.useCallback)(()=>{I("error")},[R,s]),k=x?"eager":"lazy";if("error"===O||!R)return(0,a.jsx)("div",{className:"flex items-center justify-center bg-gray-100 ".concat(m||(w?"":d)),style:{width:w?"100%":c,height:w?"100%":u,aspectRatio:M?"".concat(M):void 0,...y},role:"img","aria-label":s||"Image failed to load",children:(0,a.jsx)(l.fZZ,{className:"text-gray-400 w-1/5 h-1/5"})});let D=[d,"loaded"===O?"opacity-100":"opacity-0",p?"transition-opacity duration-300":""].filter(Boolean).join(" "),z={objectFit:(null==y?void 0:y.objectFit)||"cover",aspectRatio:w?void 0:M?"".concat(M):void 0,...y,width:w||null==y?void 0:y.width,height:w||null==y?void 0:y.height},T=(0,a.jsx)(i.default,{src:R,alt:s||"",width:w?void 0:S.width,height:w?void 0:S.height,fill:w,className:D,loading:k,fetchPriority:x?"high":g?"low":"auto",priority:x,sizes:b,style:z,placeholder:f?"blur":"empty",blurDataURL:E,onLoad:A,onError:L,quality:j,...P}),_=w?{width:"100%",height:"100%",position:"relative",...y}:{width:S.width,height:S.height,aspectRatio:M?"".concat(M):void 0,position:"relative",...y};return(0,a.jsxs)(v,{className:"relative ".concat(w?"w-full h-full":""),style:_,children:[T,"loading"===O&&f&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-100 animate-pulse ".concat(m||""),style:{width:"100%",height:"100%"},"aria-hidden":"true"})]})};s.displayName="LazyImage";let c=(0,n.memo)(s)},9981:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var a=r(5155),n=r(9907),i=r(6874),l=r.n(i),o=r(2112);let s=e=>{let{category:t}=e,r=e=>{if(!e)return"";if("object"==typeof e){var r,a;return e.url?e.url:(null==(a=e.data)||null==(r=a.attributes)?void 0:r.url)?e.data.attributes.url:(console.log("Could not extract URL from image object for ".concat(t.name,":"),e),"")}return e},i=r(t.featured_image),s=r(t.icon),c=i?(0,o.Jf)(i):"",u=s?(0,o.Jf)(s):"",d=!!c;return console.log("CategoryCard for ".concat(t.name,":"),{originalFeaturedImage:t.featured_image,extractedFeaturedImage:i,sanitizedFeaturedImage:c,originalIcon:t.icon,extractedIcon:s,sanitizedIcon:u,hasImage:d}),(0,a.jsx)(l(),{href:"/categories/".concat(t.slug),className:"block group",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform group-hover:shadow-lg group-hover:-translate-y-1",children:[(0,a.jsxs)("div",{className:"relative h-40 w-full",children:[d&&c?(0,a.jsx)(n.default,{src:c,alt:t.name,width:400,height:300,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1,showPlaceholder:!0}):(0,a.jsx)("div",{className:"absolute inset-0 bg-purple-200 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-purple-700 font-semibold text-xl",children:t.name.charAt(0)})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"}),t.icon&&u&&(0,a.jsx)("div",{className:"absolute top-4 left-4 bg-white p-2 rounded-full shadow-md",children:(0,a.jsxs)("div",{className:"relative h-8 w-8",children:[" ",(0,a.jsx)(n.default,{src:u,alt:"".concat(t.name," icon"),width:32,height:32,fillContainer:!0,className:"object-cover rounded-full",sizes:"32px",showPlaceholder:!1})]})}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:t.name})})]}),t.description&&(0,a.jsx)("div",{className:"p-4",children:(0,a.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:t.description})}),(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100",children:(0,a.jsxs)("span",{className:"text-emerald-600 group-hover:text-emerald-700 font-medium text-sm flex items-center",children:["Browse Clinics",(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[844,6874,3063,2112,8441,1684,7358],()=>t(9636)),_N_E=e.O()}]);