import Link from 'next/link';

interface ExploreFurtherProps {
  currentPath?: string;
}

const ExploreFurther: React.FC<ExploreFurtherProps> = ({ currentPath }) => {
  return (
    <div className="py-16 bg-emerald-50">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl font-bold mb-6 text-gray-800">Explore Further</h2>
        <p className="text-lg mb-8 max-w-3xl mx-auto text-gray-600">
          Didn't find what you were looking for?<br></br>Explore our complete listings of clinics, practitioners, and categories.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {currentPath !== '/clinics' && (
            <Link
              href="/clinics"
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold"
            >
              Find All Clinics
            </Link>
          )}
          {currentPath !== '/practitioners' && (
            <Link
              href="/practitioners"
              className="bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-semibold"
            >
              Find All Practitioners
            </Link>
          )}
          <Link
            href="/categories"
            className="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-semibold"
          >
            View All Categories
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ExploreFurther;
