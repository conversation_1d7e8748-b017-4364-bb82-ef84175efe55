import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Disable TypeScript-related rules
      "@typescript-eslint/no-unused-vars": "off",
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-empty-object-type": "off",

      // Disable React-related rules
      "react/no-unescaped-entities": "off",
      "react-hooks/exhaustive-deps": "off",

      // Disable Next.js-specific rules
      "@next/next/no-img-element": "off",
      "@next/next/no-before-interactive-script-outside-document": "off",

      // Disable other rules
      "prefer-const": "off",
      "import/no-anonymous-default-export": "off"
    },
  },
];

export default eslintConfig;
