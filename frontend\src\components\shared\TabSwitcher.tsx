'use client';

import { useSearchParams, usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FiHome, FiUser } from 'react-icons/fi';
import Link from 'next/link';

type TabType = 'clinics' | 'practitioners';

interface TabSwitcherProps {
  slug: string;
  pageType: 'specialities' | 'conditions' | 'categories';
  clinicCount: number;
  practitionerCount: number;
  initialTab?: TabType;
}

/**
 * Client-side component that handles tab switching between clinics and practitioners
 * This ensures proper re-rendering when the tab changes via client-side navigation
 */
export default function TabSwitcher({
  slug,
  pageType,
  clinicCount,
  practitionerCount,
  initialTab = 'clinics'
}: TabSwitcherProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  
  // Get the active tab from URL or use the initial tab
  const [activeTab, setActiveTab] = useState<TabType>(
    (searchParams.get('tab') === 'practitioners' ? 'practitioners' : 'clinics') || initialTab
  );

  // Update the active tab when the URL changes
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    const newActiveTab: TabType = tabParam === 'practitioners' ? 'practitioners' : 'clinics';
    
    if (newActiveTab !== activeTab) {
      setActiveTab(newActiveTab);
    }
  }, [searchParams, activeTab]);

  // Function to check if a tab is active
  const isTabActive = (tabName: TabType) => activeTab === tabName;

  // Handle tab click - programmatically navigate to ensure proper state update
  const handleTabClick = (tabName: TabType) => (e: React.MouseEvent) => {
    e.preventDefault();
    
    // Create new URL with the selected tab
    const params = new URLSearchParams(searchParams);
    params.set('tab', tabName);
    
    // Update the URL and state
    router.push(`/${pageType}/${slug}?${params.toString()}`);
    setActiveTab(tabName);
  };

  return (
    <div className="mb-6 border-b border-gray-200">
      <nav className="-mb-px flex space-x-8" aria-label="Tabs">
        <Link
          href={`/${pageType}/${slug}?tab=clinics`}
          className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2
            ${isTabActive('clinics')
              ? 'border-emerald-500 text-emerald-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          aria-current={isTabActive('clinics') ? 'page' : undefined}
          onClick={handleTabClick('clinics')}
        >
          <FiHome className="h-4 w-4" />
          <span>
            {pageType === 'categories' ? 'Clinics' : 'Related Clinics'} ({clinicCount})
          </span>
        </Link>
        <Link
          href={`/${pageType}/${slug}?tab=practitioners`}
          className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2
            ${isTabActive('practitioners')
              ? 'border-emerald-500 text-emerald-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          aria-current={isTabActive('practitioners') ? 'page' : undefined}
          onClick={handleTabClick('practitioners')}
        >
          <FiUser className="h-4 w-4" />
          <span>
            {pageType === 'categories' ? 'Practitioners' : 'Related Practitioners'} ({practitionerCount})
          </span>
        </Link>
      </nav>
    </div>
  );
}
