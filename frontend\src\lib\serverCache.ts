/**
 * Advanced server-side caching system for Next.js 15 and Strapi 5
 * 
 * Key improvements:
 * - Adaptive caching based on content type and environment
 * - Stale-while-revalidate strategy using tags for optimal performance
 * - Tiered cache hierarchy with contextual invalidation
 * - Performance monitoring for cache operations
 * - Optimized for Next.js 15's improved cache API
 */
import { cache } from 'react';
import { revalidateTag, unstable_cache } from 'next/cache';
import { getStrapiContent } from './strapi';

// Performance monitoring
const ENABLE_CACHE_METRICS = process.env.NEXT_PUBLIC_CACHE_METRICS === 'true';
const cacheMetrics = {
  hits: 0,
  misses: 0,
  revalidations: 0,
  requestTimes: [] as number[],
};

// Content categories with their respective cache durations based on environment
// Tiered caching approach with shorter times in development
const CONTENT_CACHE_CONFIG = {
  // Static/rarely changing content 
  'global-setting': process.env.NODE_ENV === 'production' ? 604800 : 60, // 7 days in prod, 1 min in dev
  'categories': process.env.NODE_ENV === 'production' ? 604800 : 300,    // 7 days in prod, 5 min in dev
  'specialties': process.env.NODE_ENV === 'production' ? 604800 : 300,   // 7 days in prod, 5 min in dev
  'conditions': process.env.NODE_ENV === 'production' ? 604800 : 300,    // 7 days in prod, 5 min in dev
  
  // Semi-dynamic content
  'clinics': process.env.NODE_ENV === 'production' ? 86400 : 180,        // 24 hours in prod, 3 min in dev
  'practitioners': process.env.NODE_ENV === 'production' ? 86400 : 180,  // 24 hours in prod, 3 min in dev
  
  // Frequently changing content
  'blog-posts': process.env.NODE_ENV === 'production' ? 21600 : 120,     // 6 hours in prod, 2 min in dev
  
  // Default fallback
  'default': process.env.NODE_ENV === 'production' ? 3600 : 60           // 1 hour in prod, 1 min in dev
};

// Enhanced related content mapping for intelligent revalidation cascades
// This defines which content types should be revalidated together
const RELATED_CONTENT = {
  'blog-posts': ['blog-categories', 'blog-tags', 'blog-homepage', 'homepage'],
  'clinics': ['categories', 'specialties', 'homepage'],
  'practitioners': ['specialties', 'homepage'],
  'categories': ['clinics'],
  'specialties': ['clinics', 'practitioners'],
  'conditions': ['clinics', 'practitioners'],
};

// Stale-while-revalidate configuration (in seconds)
// This extends cache lifetime while fetching fresh data in background
const SWR_EXTENSION = {
  'global-setting': 86400,  // 24 hour stale extension
  'categories': 86400,      // 24 hour stale extension
  'specialties': 86400,     // 24 hour stale extension
  'conditions': 86400,      // 24 hour stale extension
  'clinics': 43200,         // 12 hour stale extension
  'practitioners': 43200,   // 12 hour stale extension
  'blog-posts': 21600,      // 6 hour stale extension
  'default': 3600           // 1 hour stale extension
};

/**
 * Enhanced cache tag generation for more precise invalidation
 * Creates a hierarchical tag structure for granular cache control
 * 
 * @param contentType - The content type to generate tags for
 * @param id - Optional ID for entity-specific tags
 * @param additionalTags - Any extra tags to include
 * @returns Array of cache tags
 */
function generateCacheTags(
  contentType: string, 
  id?: string | number, 
  additionalTags: string[] = []
): string[] {
  // Start with base content type tag
  const tags = [`strapi-${contentType}`];
  
  // Add hierarchy tags for better organization and targeted invalidation
  if (contentType.includes('-')) {
    const [parent, child] = contentType.split('-');
    tags.push(`strapi-${parent}`);
    
    // For collection types, add a collection tag
    if (child === 'posts' || child === 'categories' || child === 'tags') {
      tags.push(`strapi-collection-${parent}`);
    }
  }
  
  // Add page-specific tag for paginated content
  if (id && !isNaN(Number(id)) && Number(id) > 0) {
    tags.push(`strapi-${contentType}-page-${id}`);
  }
  // Add slug/ID-specific tag for single entities
  else if (id) {
    tags.push(`strapi-${contentType}-${id}`);
  }
  
  // Add additional custom tags
  if (additionalTags.length > 0) {
    tags.push(...additionalTags);
  }
  
  return tags;
}

/**
 * Advanced revalidation with performance monitoring and logging
 * 
 * @param contentType - The content type to revalidate
 * @param id - Optional specific ID to revalidate
 * @param options - Additional options for revalidation behavior
 */
export function revalidateContent(
  contentType: string,
  id?: string | number,
  options: {
    revalidateRelated?: boolean;
    reason?: string;
    priority?: 'high' | 'normal' | 'low';
  } = {}
): void {
  const startTime = performance.now();
  const { revalidateRelated = true, reason = 'manual', priority = 'normal' } = options;
  
  // Revalidate the primary content
  const tags = generateCacheTags(contentType, id);
  
  // Log operation start for debugging
  if (process.env.NODE_ENV === 'development' || ENABLE_CACHE_METRICS) {
    console.log(`🔄 Revalidating ${contentType}${id ? ` (ID: ${id})` : ''} - Reason: ${reason}`);
  }
  
  // Execute revalidation
  tags.forEach(tag => {
    try {
      revalidateTag(tag);
      if (ENABLE_CACHE_METRICS) {
        cacheMetrics.revalidations++;
      }
    } catch (error) {
      console.error(`Error revalidating tag ${tag}:`, error);
    }
  });
  
  // Revalidate related content types if specified
  if (revalidateRelated && contentType in RELATED_CONTENT) {
    const relatedTypes = RELATED_CONTENT[contentType as keyof typeof RELATED_CONTENT];
    relatedTypes.forEach(relatedType => {
      // Skip high-volume related invalidations for low priority updates
      if (priority === 'low' && (relatedType === 'homepage' || relatedType === 'blog-homepage')) {
        return;
      }
      
      const relatedTags = generateCacheTags(relatedType);
      relatedTags.forEach(tag => {
        try {
          revalidateTag(tag);
          if (ENABLE_CACHE_METRICS) {
            cacheMetrics.revalidations++;
          }
        } catch (error) {
          console.error(`Error revalidating related tag ${tag}:`, error);
        }
      });
    });
  }
  
  // Performance tracking
  if (ENABLE_CACHE_METRICS) {
    const duration = performance.now() - startTime;
    cacheMetrics.requestTimes.push(duration);
    
    // Log performance metrics periodically
    if (cacheMetrics.revalidations % 10 === 0) {
      const avgTime = cacheMetrics.requestTimes.reduce((sum, time) => sum + time, 0) / 
                     cacheMetrics.requestTimes.length;
      console.log(`Cache metrics: ${cacheMetrics.revalidations} revalidations, avg time: ${avgTime.toFixed(2)}ms`);
    }
  }
  
  // Log completion
  if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Revalidated cache for ${contentType}${id ? ` (ID: ${id})` : ''} in ${
      (performance.now() - startTime).toFixed(2)
    }ms`);
  }
}

/**
 * Smart batch revalidation with prioritization
 * Efficiently revalidates multiple content types while minimizing redundant operations
 */
export function revalidateAllContent(options: { 
  reason?: string; 
  excludeTypes?: string[];
  highPriorityOnly?: boolean;
} = {}): void {
  const startTime = performance.now();
  const { reason = 'manual', excludeTypes = [], highPriorityOnly = false } = options;
  
  // Content types organized by priority
  const highPriorityTypes = ['global-setting', 'homepage', 'blog-homepage'];
  const mediumPriorityTypes = ['blog-posts', 'clinics', 'practitioners'];
  const lowPriorityTypes = ['categories', 'specialties', 'conditions', 'blog-categories', 'blog-tags'];
  
  // Determine which types to revalidate
  const typesToRevalidate = [
    ...highPriorityTypes,
    ...(highPriorityOnly ? [] : [...mediumPriorityTypes, ...lowPriorityTypes])
  ].filter(type => !excludeTypes.includes(type));
  
  // Track already revalidated tags to avoid duplicates
  const revalidatedTags = new Set<string>();
  
  // Log operation start
  console.log(`🔄 Starting batch revalidation (${reason}): ${typesToRevalidate.length} content types`);
  
  // Process each content type
  typesToRevalidate.forEach(contentType => {
    const tags = generateCacheTags(contentType);
    
    tags.forEach(tag => {
      // Skip if already revalidated in this batch
      if (!revalidatedTags.has(tag)) {
        try {
          revalidateTag(tag);
          revalidatedTags.add(tag);
          
          if (ENABLE_CACHE_METRICS) {
            cacheMetrics.revalidations++;
          }
        } catch (error) {
          console.error(`Error in batch revalidation for tag ${tag}:`, error);
        }
      }
    });
  });
  
  // Log completion with performance metrics
  const duration = performance.now() - startTime;
  console.log(`✅ Batch revalidation complete. ${revalidatedTags.size} unique tags processed in ${duration.toFixed(2)}ms`);
}

/**
 * Enhanced cache configuration generator with stale-while-revalidate support
 * 
 * @param contentType - The content type to get cache config for
 * @param options - Optional configuration overrides
 * @returns Next.js cache configuration object
 */
function getCacheConfig(
  contentType: string, 
  options?: { 
    revalidate?: number | false; 
    tags?: string[];
    staleWhileRevalidate?: boolean | number;
  }
) {
  // Determine revalidation time from options, type-specific config, or default
  const fallbackRevalidate = 
    options?.revalidate !== undefined ? options.revalidate :
    contentType in CONTENT_CACHE_CONFIG 
      ? CONTENT_CACHE_CONFIG[contentType as keyof typeof CONTENT_CACHE_CONFIG]
      : CONTENT_CACHE_CONFIG.default;
  
  // Generate appropriate cache tags
  const defaultTags = generateCacheTags(contentType);
  const cacheTags = options?.tags || defaultTags;
  
  // Determine if we should use stale-while-revalidate
  const enableSWR = options?.staleWhileRevalidate !== false;
  
  // Configure stale-while-revalidate if enabled
  if (enableSWR && fallbackRevalidate !== false) {
    // Get SWR extension value (either from options, type-specific config, or default)
    const swrValue = typeof options?.staleWhileRevalidate === 'number' 
      ? options.staleWhileRevalidate
      : contentType in SWR_EXTENSION
        ? SWR_EXTENSION[contentType as keyof typeof SWR_EXTENSION]
        : SWR_EXTENSION.default;
    
    // Add Cache-Control header with stale-while-revalidate
    return {
      revalidate: fallbackRevalidate,
      tags: cacheTags,
      extraHeaders: {
        'Cache-Control': `public, max-age=${fallbackRevalidate}, stale-while-revalidate=${swrValue}`
      }
    };
  }
  
  // Basic config without SWR
  return {
    revalidate: fallbackRevalidate,
    tags: cacheTags
  };
}

/**
 * createOptimizedFetch: Higher-order function that wraps data fetching with advanced caching
 * 
 * @param fetcher - The actual data fetching function
 * @param options - Cache configuration options
 * @returns Cached function with the same signature as the original fetcher
 */
function createOptimizedFetch<T extends (...args: any[]) => Promise<any>>(
  fetcher: T,
  options: {
    contentType: string;
    revalidate?: number;
    tags?: string[];
    enableSWR?: boolean;
  }
) {
  // Use simplified version with fixed tags for better TypeScript compatibility
  const cacheTags = options.tags || [`strapi-${options.contentType}`];
  
  // Determine revalidation time from options or config
  const revalidateTime = options.revalidate ?? 
                       CONTENT_CACHE_CONFIG[options.contentType as keyof typeof CONTENT_CACHE_CONFIG] ?? 
                       CONTENT_CACHE_CONFIG.default;
  
  // Return a cached version of the fetcher
  return unstable_cache(
    async (...args: Parameters<T>) => {
      const startTime = ENABLE_CACHE_METRICS ? performance.now() : 0;
      try {
        const result = await fetcher(...args);
        
        // Track performance if enabled
        if (ENABLE_CACHE_METRICS && startTime) {
          const duration = performance.now() - startTime;
          cacheMetrics.requestTimes.push(duration);
          cacheMetrics.hits++;
        }
        
        return result;
      } catch (error) {
        if (ENABLE_CACHE_METRICS) {
          cacheMetrics.misses++;
        }
        throw error;
      }
    },
    // Use simpler string keys to avoid type issues
    [`${options.contentType}`],
    // Cache configuration with fixed tags
    {
      revalidate: revalidateTime,
      tags: cacheTags
    }
  );
}

/**
 * Cached function to fetch global settings
 * Using a longer cache time (24 hours) since global settings rarely change
 * Still using tags for on-demand revalidation when needed
 */
export const getGlobalSettings = cache(async () => {
  return getStrapiContent.global.getSettings({
    cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15
    next: {
      revalidate: 3600, // Changed to 1 hour cache
      tags: ['strapi-global-setting']
    }
  });
});

/**
 * Cached function to fetch categories
 * Using a longer cache time (1 week) since categories rarely change
 * Still using tags for on-demand revalidation when needed
 */
export const getCategories = cache(async (options?: {
  page?: number;
  pageSize?: number;
  query?: string;
  next?: { revalidate?: number | false; tags?: string[] };
}) => {
  // Default to 1 week revalidation and specific tags if not provided
  const nextOptions = options?.next || {
    revalidate: 604800, // 1 week (7 days)
    tags: ['strapi-categories', 'strapi-categories-all'],
  };
  return getStrapiContent.categories.getAll({
    ...options,
    cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15
    next: nextOptions
  });
});

/**
 * Cached function to fetch footer categories
 * Using a longer cache time (1 week) since footer categories rarely change
 * Still using tags for on-demand revalidation when needed
 */
export const getFooterCategories = cache(async (options?: {
  next?: { revalidate?: number | false; tags?: string[] };
}) => {
  // Default to 1 week revalidation and specific tags if not provided
  const nextOptions = options?.next || {
    revalidate: 604800, // 1 week (7 days)
    tags: ['strapi-categories', 'strapi-categories-footer'],
  };
  return getStrapiContent.categories.getFooterCategories({
    ...options,
    cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15
    next: nextOptions
  });
});

/**
 * Cached function to fetch specialties
 * Using a longer cache time (1 week) since specialties rarely change
 * Still using tags for on-demand revalidation when needed
 */
export const getSpecialties = cache(async (options?: {
  page?: number;
  pageSize?: number;
  query?: string;
  next?: { revalidate?: number | false; tags?: string[] }; // Add next options
}) => {
  // Default to 1 week revalidation and specific tags if not provided
  const nextOptions = options?.next || {
    revalidate: 604800, // 1 week (7 days)
    tags: ['strapi-specialties', 'strapi-specialties-all'],
  };
  return getStrapiContent.specialties.getAll({
    ...options,
    cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15
    next: nextOptions
  });
});

/**
 * Cached function to fetch clinics
 */
export const getClinics = cache(async (options: {
  page?: number;
  pageSize?: number;
  query?: string;
  location?: string;
  specialtySlug?: string;
  conditionSlug?: string;
  next?: { revalidate?: number | false; tags?: string[] };
} = {}) => {
  const nextOptions = options.next || {
    revalidate: 3600,
    tags: ['strapi-clinics', `page-${options.page || 1}`], // Add more specific tags as needed
  };
  return getStrapiContent.clinics.getAll({ ...options, next: nextOptions });
});

/**
 * Cached function to fetch a single clinic by slug
 */
export const getClinicBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-clinics', `strapi-clinic-${slug}`],
  };
  return getStrapiContent.clinics.getBySlug(slug, { next: nextOptions });
});

/**
 * Cached function to fetch featured clinics
 */
export const getFeaturedClinics = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-clinics', 'strapi-clinics-featured'],
  };
  return getStrapiContent.clinics.getFeatured({ next: nextOptions });
});

/**
 * Cached function to fetch practitioners
 */
export const getPractitioners = cache(async (options: {
  page?: number;
  pageSize?: number;
  query?: string;
  location?: string;
  specialtySlug?: string;
  conditionSlug?: string;
  next?: { revalidate?: number | false; tags?: string[] };
} = {}) => {
  const nextOptions = options.next || {
    revalidate: 3600,
    tags: ['strapi-practitioners', `page-${options.page || 1}`], // Add more specific tags as needed
  };
  return getStrapiContent.practitioners.getAll({ ...options, next: nextOptions });
});

/**
 * Cached function to fetch a single practitioner by slug
 */
export const getPractitionerBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-practitioner', `strapi-practitioner-${slug}`], // Changed 'strapi-practitioners' to 'strapi-practitioner'
  };
  return getStrapiContent.practitioners.getBySlug(slug, { next: nextOptions });
});

/**
 * Cached function to fetch featured practitioners
 */
export const getFeaturedPractitioners = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-practitioners', 'strapi-practitioners-featured'],
  };
  return getStrapiContent.practitioners.getFeatured({ next: nextOptions });
});

/**
 * Cached function to fetch blog posts
 */
export const getBlogPosts = cache(async (options: {
  page?: number;
  pageSize?: number;
  query?: string;
  categorySlug?: string;
  tagSlug?: string;
  sort?: string | string[];
  populate?: string | string[] | Record<string, any>;
  next?: { revalidate?: number | false; tags?: string[] };
} = {}) => {
  const nextOptions = options.next || {
    revalidate: 3600,
    tags: ['strapi-blog-posts', `page-${options.page || 1}`], // Add more specific tags as needed
  };
  return getStrapiContent.blog.getPosts({
    ...options,
    sort: options.sort || ['publishDate:desc'],
    next: nextOptions,
  });
});

/**
 * Cached function to fetch a single blog post by slug
 */
export const getBlogPostBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-blog-posts', `strapi-blog-post-${slug}`],
  };
  return getStrapiContent.blog.getPostBySlug(slug, { next: nextOptions });
});

/**
 * Cached function to fetch featured blog posts
 */
export const getFeaturedBlogPosts = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-blog-posts', 'strapi-blog-posts-featured'],
  };
  return getStrapiContent.blog.getPosts({
    filters: { isFeatured: { $eq: true } },
    sort: ['publishDate:desc'],
    pagination: { page: 1, pageSize: 10 },
    next: nextOptions,
  });
});

/**
 * Cached function to fetch blog categories
 */
export const getBlogCategories = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-blog-categories', 'strapi-categories'],
  };
  return getStrapiContent.blog.getCategories({ next: nextOptions });
});

/**
 * Cached function to fetch blog tags
 */
export const getBlogTags = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-blog-tags', 'strapi-tags'],
  };
  return getStrapiContent.blog.getTags({ next: nextOptions });
});

/**
 * Cached function to fetch blog homepage
 */
export const getBlogHomepage = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-global-setting', 'strapi-blog-homepage'],
  };
  return getStrapiContent.global.getBlogHomepage({ next: nextOptions });
});

/**
 * Cached function to fetch a specialty by slug
 */
export const getSpecialtyBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-specialties', `strapi-specialty-${slug}`],
  };
  return getStrapiContent.specialties.getBySlug(slug, { next: nextOptions });
});

/**
 * Cached function to fetch a condition by slug
 */
export const getConditionBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-conditions', `strapi-condition-${slug}`],
  };
  return getStrapiContent.conditions.getBySlug(slug, { next: nextOptions });
});

/**
 * Cached function to fetch conditions
 */
export const getConditions = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-conditions', 'strapi-conditions-all'],
  };
  return getStrapiContent.conditions.getAll({ next: nextOptions });
});

/**
 * Cached function to fetch a category by slug
 */
export const getCategoryBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {
  const nextOptions = options?.next || {
    revalidate: 3600,
    tags: ['strapi-categories', `strapi-category-${slug}`],
  };
  return getStrapiContent.categories.getBySlug(slug, { next: nextOptions });
});
