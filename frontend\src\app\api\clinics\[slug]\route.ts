import { NextRequest, NextResponse } from 'next/server';
import { getClinicBySlug } from '@/lib/api/services/clinicService'; // Adjust path as needed

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  const slug = params.slug;

  if (!slug) {
    return NextResponse.json({ error: 'Slug is required' }, { status: 400 });
  }

  try {
    // This uses the server-side cached function
    const clinicResponse = await getClinicBySlug(slug);

    if (clinicResponse.error || !clinicResponse.data) {
      const status = clinicResponse.error?.status || 404;
      const message = clinicResponse.error?.message || 'Clinic not found';
      return NextResponse.json({ error: message }, { status });
    }

    // Return the data part of the StrapiResponse
    return NextResponse.json(clinicResponse);
  } catch (error) {
    console.error(`Error fetching clinic with slug ${slug} in API route:`, error);
    const message = error instanceof Error ? error.message : 'Failed to fetch clinic';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}
