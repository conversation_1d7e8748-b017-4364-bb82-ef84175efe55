import Link from 'next/link';
import { FiSearch } from 'react-icons/fi';
import { Metadata } from 'next'; // Import Metadata type
import { getStrapiContent } from '@/lib/strapi'; // Import Strapi helper
import SearchInput from '@/components/shared/SearchInput';

// Use force-static to ensure the page is statically generated during build
export const dynamic = 'force-static';

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL; // Get base site URL

// No mock data - we'll only use real data from the API

// Generate metadata for the specialities listing page
export async function generateMetadata(): Promise<Metadata> {
  const title = "Explore Holistic Health Specialities | Natural Healing Now";
  const description = "Discover different holistic health specialities like Acupuncture, Naturopathy, Chiropractic, and more. Find clinics offering these services.";
  const canonicalPath = "/specialities";
  const canonicalUrl = SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath;

  return {
    title: title,
    description: description,
    alternates: {
      canonical: canonicalUrl,
    },
    // Add Open Graph and Twitter card data if desired
    // openGraph: { ... },
    // twitter: { ... },
  };
}

// Fallback component to handle errors
function ErrorFallback() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-6">Specialties</h1>
          <div className="text-center py-6">
            <h3 className="text-xl font-semibold mb-4 text-gray-800">Unable to Load Specialties</h3>
            <p className="text-gray-600 mb-4">
              We're having trouble loading our specialties at the moment.
            </p>
            <p className="text-gray-500 mb-6">
              Please try again later. In the meantime, you can explore our clinics and practitioners directly.
            </p>
            <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/clinics"
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium"
              >
                Browse Clinics
              </Link>
              <Link
                href="/practitioners"
                className="bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-4 py-2 rounded-lg font-medium"
              >
                Find Practitioners
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Define a type for the processed specialty data
interface ProcessedSpecialty {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: any;
  featured_image?: any;
  clinics: any[];
}

// Define static specialties for prerendering
const staticSpecialties: ProcessedSpecialty[] = [
  { id: '1', name: 'Acupuncture', slug: 'acupuncture', description: 'Traditional Chinese medicine technique involving thin needles.', clinics: [] },
  { id: '2', name: 'Naturopathy', slug: 'naturopathy', description: 'Natural approach to health and healing.', clinics: [] },
  { id: '3', name: 'Chiropractic', slug: 'chiropractic', description: 'Focus on diagnosis and treatment of mechanical disorders of the musculoskeletal system.', clinics: [] },
  { id: '4', name: 'Massage Therapy', slug: 'massage-therapy', description: 'Manual manipulation of soft body tissues to enhance health and wellbeing.', clinics: [] }
];

// Make the component async to fetch data
export default async function SpecialitiesPage() {
  try {
    // Fetch global settings and footer categories
    let siteName = 'Natural Healing Now'; // Default site name
    let logoLight = null; // Default logo
    let footerCategories: any[] = []; // Default empty array

  try {
    const globalSettingsResponse = await getStrapiContent.global.getSettings();

    // Log the response structure for debugging
    console.log('Global settings response structure:',
      JSON.stringify({
        hasData: !!globalSettingsResponse?.data,
        dataKeys: globalSettingsResponse?.data ? Object.keys(globalSettingsResponse.data) : [],
        hasAttributes: !!(globalSettingsResponse?.data as any)?.attributes,
      })
    );

    // Handle both Strapi v4 and v5 response formats
    if (globalSettingsResponse?.data) {
      // Check if we have a v4-style response with attributes
      if ((globalSettingsResponse.data as any).attributes) {
        siteName = (globalSettingsResponse.data as any).attributes.siteName || siteName;
        // Assuming logoLight is directly under attributes and has a 'data' wrapper
        logoLight = (globalSettingsResponse.data as any).attributes.logoLight?.data?.attributes || null;
      } else {
        // v5-style flattened response
        siteName = globalSettingsResponse.data.siteName || siteName;
        // Check both possible structures for logoLight
        if (globalSettingsResponse.data.logoLight?.data?.attributes) {
          logoLight = globalSettingsResponse.data.logoLight.data.attributes;
        } else if (globalSettingsResponse.data.logoLight) {
          logoLight = globalSettingsResponse.data.logoLight;
        }
      }
    }

    const footerCategoriesResponse = await getStrapiContent.categories.getFooterCategories();
    if (footerCategoriesResponse?.data) {
      footerCategories = footerCategoriesResponse.data;
    }
  } catch (error) {
    console.error("Error fetching layout data for SpecialitiesPage:", error);
    // Keep default values if fetching fails
  }

  // Fetch specialties data from Strapi
  let specialities: ProcessedSpecialty[] = [];

  // Use static specialties during prerendering to avoid dynamic data issues
  if (process.env.NEXT_PHASE === 'phase-production-build') {
    console.log("Using static specialties during prerendering");
    specialities = staticSpecialties;
    return;
  }

  try {
    console.log("Attempting to fetch specialties data...");
    const specialtiesResponse = await getStrapiContent.specialties.getAll();
    console.log("Specialties response received:",
      specialtiesResponse ?
      `Data exists: ${!!specialtiesResponse.data}, Is array: ${Array.isArray(specialtiesResponse.data)}, Length: ${specialtiesResponse.data?.length || 0}` :
      "No response");

    // Log the first item to see its structure
    if (specialtiesResponse?.data && Array.isArray(specialtiesResponse.data) && specialtiesResponse.data.length > 0) {
      const firstItem = specialtiesResponse.data[0];
      console.log("First specialty item structure:", JSON.stringify({
        id: firstItem.id,
        hasAttributes: !!firstItem.attributes,
        attributesKeys: firstItem.attributes ? Object.keys(firstItem.attributes) : [],
        hasSlug: !!firstItem.attributes?.slug,
        slug: firstItem.attributes?.slug || 'no slug',
        // Check for direct properties too
        hasDirectName: !!firstItem.name,
        hasDirectSlug: !!firstItem.slug,
        directName: firstItem.name || 'no direct name',
        directSlug: firstItem.slug || 'no direct slug'
      }, null, 2));

      // Additional debug logging to understand the full structure
      console.log("Complete first specialty item:", JSON.stringify(firstItem, null, 2));

      // Log all items to see if there's variation in the data structure
      console.log("All specialty items structure summary:",
        specialtiesResponse.data.map((item, index) => ({
          index,
          id: item.id,
          hasAttributes: !!item.attributes,
          hasSlug: !!(item.attributes?.slug || item.slug),
          source: item.attributes?.slug ? 'attributes' : (item.slug ? 'direct' : 'missing')
        }))
      );
    }

    // Check if we have valid data
    if (specialtiesResponse?.data) {
      try {
        // Log the raw data structure for debugging
        console.log("Raw specialties data structure:", JSON.stringify({
          isArray: Array.isArray(specialtiesResponse.data),
          length: Array.isArray(specialtiesResponse.data) ? specialtiesResponse.data.length : 'not an array',
          firstItem: Array.isArray(specialtiesResponse.data) && specialtiesResponse.data.length > 0 ?
            Object.keys(specialtiesResponse.data[0]).join(', ') : 'no items'
        }));

        // Ensure data is an array
        const dataArray = Array.isArray(specialtiesResponse.data)
          ? specialtiesResponse.data
          : [specialtiesResponse.data];

        console.log(`Processing ${dataArray.length} specialty items`);

        // Map the API response to the format we need, with extra safety checks
        specialities = dataArray
          .filter(specialty => specialty && specialty.id) // Filter out null/undefined items and ensure ID exists
          .map(specialty => {
            try {
              // Safely access properties with optional chaining
              const id = specialty.id?.toString() || '';

              // Check if the data has already been transformed by the Strapi client
              const hasAttributes = !!specialty.attributes;
              const attributes = specialty.attributes || {};

              // Log the structure of each specialty item for debugging
              console.log(`Specialty ${id} structure:`, JSON.stringify({
                hasAttributes,
                directName: specialty.name ? 'has direct name' : 'no direct name',
                directSlug: specialty.slug ? 'has direct slug' : 'no direct slug',
                attributesName: attributes.name ? 'has attributes.name' : 'no attributes.name',
                attributesSlug: attributes.slug ? 'has attributes.slug' : 'no attributes.slug'
              }));

              // Extract name with fallbacks - check both direct properties and attributes
              const name = attributes.name || specialty.name || 'Unnamed Specialty';

              // Extract slug with fallbacks - check both direct properties and attributes
              let slug = attributes.slug || specialty.slug || '';

              // If slug is still empty, generate one from name or ID
              if (!slug) {
                if (name && name !== 'Unnamed Specialty') {
                  // Generate slug from name - convert to lowercase, replace spaces with hyphens
                  slug = name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
                } else {
                  // Fallback to ID-based slug
                  slug = `specialty-${id}`;
                }
              }

              console.log(`Processing specialty ID ${id}: name=${name}, slug=${slug}`);

              // Extract description with fallbacks
              const description = attributes.description || specialty.description || '';

              // Safely handle clinics relationship
              let clinics = [];
              try {
                // Check for nested clinics data structure in attributes
                if (attributes.clinics?.data && Array.isArray(attributes.clinics.data)) {
                  clinics = attributes.clinics.data
                    .filter(clinic => clinic && clinic.id)
                    .map(clinic => {
                      const clinicId = clinic.id?.toString() || '';
                      const clinicAttrs = clinic.attributes || {};
                      const clinicName = clinicAttrs.name || clinic.name || 'Unnamed Clinic';
                      const clinicSlug = clinicAttrs.slug || clinic.slug ||
                        clinicName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '') ||
                        `clinic-${clinicId}`;

                      return {
                        id: clinicId,
                        name: clinicName,
                        slug: clinicSlug
                      };
                    });
                }
                // Check for direct clinics array
                else if (Array.isArray(specialty.clinics)) {
                  clinics = specialty.clinics
                    .filter(clinic => clinic && clinic.id)
                    .map(clinic => {
                      const clinicId = clinic.id?.toString() || '';
                      const clinicName = clinic.name || 'Unnamed Clinic';
                      const clinicSlug = clinic.slug ||
                        clinicName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '') ||
                        `clinic-${clinicId}`;

                      return {
                        id: clinicId,
                        name: clinicName,
                        slug: clinicSlug
                      };
                    });
                }
                // Check for clinics as direct property objects
                else if (attributes.clinics && !Array.isArray(attributes.clinics) && typeof attributes.clinics === 'object') {
                  console.log("Found clinics as direct property object");
                  // Try to extract clinics from a non-array object structure
                  const clinicsData = attributes.clinics.data || attributes.clinics;
                  if (Array.isArray(clinicsData)) {
                    clinics = clinicsData
                      .filter(clinic => clinic && clinic.id)
                      .map(clinic => {
                        const clinicId = clinic.id?.toString() || '';
                        const clinicAttrs = clinic.attributes || {};
                        const clinicName = clinicAttrs.name || clinic.name || 'Unnamed Clinic';
                        const clinicSlug = clinicAttrs.slug || clinic.slug || `clinic-${clinicId}`;

                        return {
                          id: clinicId,
                          name: clinicName,
                          slug: clinicSlug
                        };
                      });
                  }
                }
              } catch (clinicError) {
                console.error("Error processing clinics for specialty:", clinicError);
                // Return empty array on error
                clinics = [];
              }

              // Return a normalized specialty object with consistent structure
              return {
                id,
                name,
                slug,
                description,
                icon: attributes.icon?.data?.attributes || specialty.icon || null,
                featured_image: attributes.featuredImage?.data?.attributes || specialty.featured_image || null,
                clinics
              };
            } catch (itemError) {
              console.error("Error processing specialty item:", itemError);
              // Return null for this item, it will be filtered out below
              return null;
            }
          })
          .filter(Boolean); // Filter out any null items that had errors
      } catch (error) {
        console.error("Error processing specialties data:", error);
        specialities = [];
      }
    }
  } catch (error) {
    console.error("Error fetching specialties:", error);
    // Use static specialties when API fails
    console.log("Using static specialties due to API error");
    specialities = staticSpecialties;
  }

  // If specialities is not an array, use static specialties
  if (!Array.isArray(specialities)) {
    console.log("specialities is not an array, using static specialties");
    specialities = staticSpecialties;
  }

  // If specialities is an empty array, use static specialties
  if (specialities.length === 0) {
    console.log("specialities is empty, using static specialties");
    specialities = staticSpecialties;
  }

  // Final safety check - ensure every item has a slug and log all processed specialties
  specialities = specialities.map(item => {
    if (!item) {
      console.log(`Skipping null/undefined specialty item`);
      return null;
    }

    try {
      // Ensure ID exists
      if (!item.id) {
        console.log(`Skipping specialty item without ID: ${JSON.stringify(item)}`);
        return null;
      }

      // Ensure slug exists - if not, generate one from the ID or name
      if (!item.slug) {
        const slugFromName = item.name ?
          item.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '') :
          `specialty-${item.id}`;

        console.log(`Adding missing slug for specialty with id ${item.id}: ${slugFromName}`);

        return {
          ...item,
          slug: slugFromName
        };
      }

      // Item has both ID and slug, return as is
      return item;
    } catch (error) {
      console.error(`Error processing specialty item: ${error}`);
      return null;
    }
  }).filter(item => {
    // Ensure item exists, has an ID and a slug
    const isValid = item && item.id && item.slug;
    if (!isValid) {
      console.log(`Filtering out invalid specialty item: ${JSON.stringify(item)}`);
    }
    return isValid;
  });

  // Log the final processed specialties for debugging
  console.log(`Final processed specialties count: ${specialities.length}`);
  if (specialities.length > 0) {
    console.log(`First processed specialty: id=${specialities[0].id}, name=${specialities[0].name}, slug=${specialities[0].slug}`);
  } else {
    // If we still have no specialties after all processing, use static specialties
    console.log("No specialties after processing, using static specialties");
    specialities = staticSpecialties;
  }

  return (
    <>
      {/* Page Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Explore Specialities</h1>
          <p className="text-lg max-w-3xl">
            Discover different holistic health specialities and find clinics offering these services.
          </p>
        </div>
      </div>

      {/* Search Section */}
      <div className="bg-white py-8 border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <SearchInput
              placeholder="Search specialties..."
              defaultValue=""
              icon={<FiSearch className="text-gray-400" />}
            />
          </div>
        </div>
      </div>

      {/* Specialities Grid */}
      <div className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            All Specialities
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {(() => {
              try {
                // Check if specialities is a valid array with items
                if (!Array.isArray(specialities)) {
                  console.error("specialities is not an array:", specialities);
                  return (
                    <div className="col-span-full text-center py-12">
                      <div className="bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto">
                        <h3 className="text-xl font-semibold mb-4 text-gray-800">Unable to Load Specialties</h3>
                        <p className="text-gray-600 mb-4">
                          We're having trouble loading our specialties at the moment.
                        </p>
                        <p className="text-gray-500">
                          Please try again later. In the meantime, you can explore our clinics and practitioners directly.
                        </p>
                        <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
                          <Link
                            href="/clinics"
                            className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium"
                          >
                            Browse Clinics
                          </Link>
                          <Link
                            href="/practitioners"
                            className="bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-4 py-2 rounded-lg font-medium"
                          >
                            Find Practitioners
                          </Link>
                        </div>
                      </div>
                    </div>
                  );
                }

                if (specialities.length === 0) {
                  return (
                    <div className="col-span-full text-center py-12">
                      <div className="bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto">
                        <h3 className="text-xl font-semibold mb-4 text-gray-800">No Specialties Found</h3>
                        <p className="text-gray-600 mb-4">
                          We're currently updating our database of holistic health specialties.
                        </p>
                        <p className="text-gray-500">
                          Please check back soon as we continue to expand our offerings. In the meantime,
                          you can explore our clinics and practitioners directly.
                        </p>
                        <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
                          <Link
                            href="/clinics"
                            className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium"
                          >
                            Browse Clinics
                          </Link>
                          <Link
                            href="/practitioners"
                            className="bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-4 py-2 rounded-lg font-medium"
                          >
                            Find Practitioners
                          </Link>
                        </div>
                      </div>
                    </div>
                  );
                }

                // If we have specialities, map and render them
                return specialities.map(speciality => {
                  try {
                    // Skip rendering if speciality is undefined or missing required properties
                    if (!speciality || !speciality.id) {
                      console.log(`Skipping specialty with missing id: ${JSON.stringify(speciality)}`);
                      return null;
                    }

                    // Double check that slug exists - if not, generate one from the ID
                    // This should never happen due to our earlier processing, but just in case
                    if (!speciality.slug) {
                      console.log(`Specialty ${speciality.id} missing slug, generating one from ID`);
                      speciality.slug = `specialty-${speciality.id}`;
                    }

                    // Safely access properties with fallbacks
                    const name = speciality.name || 'Unnamed Specialty';
                    const description = speciality.description || 'No description available';
                    const clinics = Array.isArray(speciality.clinics) ? speciality.clinics : [];

                    return (
                      <Link
                        key={speciality.id}
                        href={`/specialities/${speciality.slug}`}
                        className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow block"
                      >
                        {/* Speciality Image/Icon Placeholder */}
                        <div className="h-32 bg-emerald-100 flex items-center justify-center hover:bg-emerald-200 transition-colors">
                          <span className="text-emerald-700 text-2xl font-semibold">
                            {name && name.length > 0 ? name.charAt(0) : 'S'}
                          </span>
                        </div>

                        <div className="p-5 text-center">
                          <h3 className="text-xl font-semibold text-gray-800 hover:text-emerald-700 transition-colors">
                            {name}
                          </h3>

                        </div>
                      </Link>
                    );
                  } catch (specialtyError) {
                    console.error("Error rendering specialty:", specialtyError, speciality);
                    return null;
                  }
                });
              } catch (error) {
                console.error("Fatal error rendering specialties:", error);
                return (
                  <div className="col-span-full text-center py-12">
                    <div className="bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto">
                      <h3 className="text-xl font-semibold mb-4 text-gray-800">Unable to Load Specialties</h3>
                      <p className="text-gray-600 mb-4">
                        We're having trouble loading our specialties at the moment.
                      </p>
                      <p className="text-gray-500">
                        Please try again later. In the meantime, you can explore our clinics and practitioners directly.
                      </p>
                      <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
                        <Link
                          href="/clinics"
                          className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium"
                        >
                          Browse Clinics
                        </Link>
                        <Link
                          href="/practitioners"
                          className="bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-4 py-2 rounded-lg font-medium"
                        >
                          Find Practitioners
                        </Link>
                      </div>
                    </div>
                  </div>
                );
              }
            })()}
          </div>
        </div>
      </div>

      {/* Popular Conditions Section */}
      <div className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            Popular Health Conditions
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['Anxiety', 'Chronic Pain', 'Digestive Issues', 'Stress',
              'Insomnia', 'Allergies', 'Fatigue', 'Headaches'].map((condition, index) => (
              <Link
                key={index}
                href={`/conditions/${condition.toLowerCase().replace(' ', '-')}`}
                className="bg-gray-50 hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors"
              >
                <span className="text-gray-800 font-medium">{condition}</span>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="py-16 bg-emerald-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6 text-gray-800">Ready to Start Your Wellness Journey?</h2>
          <p className="text-lg mb-8 max-w-3xl mx-auto text-gray-600">
            Find clinics and practitioners offering the specialities that match your health needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/clinics"
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold"
            >
              Find a Clinic
            </Link>
            <Link
              href="/practitioners"
              className="bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-semibold"
            >
              Find a Practitioner
            </Link>
          </div>
        </div>
      </div>
    </>
  );
  } catch (error) {
    console.error("Fatal error in SpecialitiesPage:", error);
    return <ErrorFallback />;
  }
}
