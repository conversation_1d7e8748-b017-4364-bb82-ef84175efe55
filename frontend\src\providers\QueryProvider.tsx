'use client'; // This directive is essential for providers using client-side hooks

import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

interface QueryProviderProps {
  children: React.ReactNode;
}

export default function QueryProvider({ children }: QueryProviderProps) {
  // Initialize QueryClient. It's good practice to create it once.
  // useState ensures it's not recreated on every render.
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Default staleTime: 0 means queries are considered stale immediately.
            // You might want to set a global staleTime, e.g., 5 minutes.
            staleTime: 1000 * 60 * 5, // 5 minutes
            // gcTime (garbage collection time): Data is kept in cache for this long after all observers are unmounted.
            // Renamed from cacheTime in v4. Default is 5 minutes.
            gcTime: 1000 * 60 * 30, // 30 minutes
            // Default refetchOnWindowFocus: true. Refetches query on window focus.
            refetchOnWindowFocus: process.env.NODE_ENV === 'production', // Disable in dev for less noise
            // Default retry: 3 times with exponential backoff.
            // retry: 1, // Or configure as needed
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* React Query Devtools are super helpful for debugging */}
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
}
