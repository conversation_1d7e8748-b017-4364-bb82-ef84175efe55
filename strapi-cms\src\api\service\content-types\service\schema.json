{"kind": "collectionType", "collectionName": "services", "info": {"singularName": "service", "pluralName": "services", "displayName": "Service (Therapy/Treatment)", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name"}, "description": {"type": "richtext"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "clinics": {"type": "relation", "relation": "manyToMany", "target": "api::clinic.clinic", "mappedBy": "services"}, "practitioners": {"type": "relation", "relation": "manyToMany", "target": "api::practitioner.practitioner", "mappedBy": "services"}}}