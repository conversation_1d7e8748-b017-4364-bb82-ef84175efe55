(()=>{var e={};e.id=1174,e.ids=[1174],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return u},throwForSearchParamsAccessInUseCache:function(){return a},throwWithStaticGenerationBailoutError:function(){return s},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let n=r(80023),o=r(3295);function s(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function a(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function u(){let e=o.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35091:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var n={};r.r(n),r.d(n,{GET:()=>u,POST:()=>c});var o=r(96559),s=r(48088),i=r(37719),a=r(32190);async function u(e){try{let t="https://nice-badge-2130241d6c.strapiapp.com";if(!t)return a.NextResponse.json({error:"NEXT_PUBLIC_API_URL is not defined in environment variables"},{status:500});let{searchParams:r}=new URL(e.url),n=r.get("endpoint");if(!n)return a.NextResponse.json({error:"Missing endpoint parameter"},{status:400});let o=new URL(`/api${n}`,t);console.log(`Proxying request to: ${o.toString()}`);let s=process.env.STRAPI_API_TOKEN,i={"Content-Type":"application/json"};s&&(i.Authorization=`Bearer ${s}`),r.forEach((e,t)=>{"endpoint"!==t&&o.searchParams.append(t,e)});let u=await fetch(o.toString(),{headers:i,method:"GET",cache:"no-store"});if(!u.ok)throw Error(`Strapi API responded with status: ${u.status}`);let c=await u.json();return a.NextResponse.json(c)}catch(e){return console.error("Error in Strapi proxy:",e),a.NextResponse.json({error:e.message||"An error occurred while proxying the request to Strapi",status:e.status||500},{status:e.status||500})}}async function c(e){try{let t="https://nice-badge-2130241d6c.strapiapp.com";if(!t)return a.NextResponse.json({error:"NEXT_PUBLIC_API_URL is not defined in environment variables"},{status:500});let{searchParams:r}=new URL(e.url),n=r.get("endpoint");if(!n)return a.NextResponse.json({error:"Missing endpoint parameter"},{status:400});let o=new URL(`/api${n}`,t);console.log(`Proxying POST request to: ${o.toString()}`);let s=process.env.STRAPI_API_TOKEN,i={"Content-Type":"application/json"};s&&(i.Authorization=`Bearer ${s}`);let u=await e.json(),c=await fetch(o.toString(),{method:"POST",headers:i,body:JSON.stringify(u),cache:"no-store"});if(!c.ok)throw Error(`Strapi API responded with status: ${c.status}`);let p=await c.json();return a.NextResponse.json(p)}catch(e){return console.error("Error in Strapi proxy:",e),a.NextResponse.json({error:e.message||"An error occurred while proxying the request to Strapi",status:e.status||500},{status:e.status||500})}}let p=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/strapi-proxy/route",pathname:"/api/strapi-proxy",filename:"route",bundlePath:"app/api/strapi-proxy/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\strapi-proxy\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:f}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},43763:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return o},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return s}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function o(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let s=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,580],()=>r(35091));module.exports=n})();