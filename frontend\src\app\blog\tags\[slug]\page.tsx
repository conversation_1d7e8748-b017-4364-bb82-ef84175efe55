import Link from 'next/link';
import { Fi<PERSON>rrowLeft, FiTag } from 'react-icons/fi';
import BlogPostCard from '@/components/blog/BlogPostCard';
import { getStrapiContent } from '@/lib/strapi'; // Import Strapi functions
import { sanitizeUrl } from '@/lib/mediaUtils'; // Added import for sanitizeUrl
import { notFound } from 'next/navigation'; // Import notFound for error handling
import SEOHead from '@/components/SEOHead'; // Import SEO component

// Interface for the blog post structure within the tag
interface BlogPostInTag {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  featured_image: string | null;
  publish_date: string; // Use publish_date for consistency with BlogPostCard
  author: {
    name: string;
    slug: string;
    profile_picture?: string | null; // Add optional profile picture
  };
}

// Interface for the blog tag structure
interface BlogTag {
  id: string;
  name: string;
  slug: string;
  description?: string | null; // Add optional description if tags have it
  posts: BlogPostInTag[];
}

// Map Strapi API response to our blog tag structure
function mapStrapiTagToProps(strapiTag: any): BlogTag | null {
  console.log("Mapping tag data:", JSON.stringify(strapiTag, null, 2));

  if (!strapiTag) {
    console.warn("No tag data to map");
    return null;
  }

  // Handle both potential response formats: with attributes (v4 style) or direct properties (v5 style)
  const hasAttributes = !!strapiTag.attributes;
  const id = strapiTag.id;
  const data = hasAttributes ? strapiTag.attributes : strapiTag;

  if (!id || !data) {
    console.warn("Invalid tag data structure:", strapiTag);
    return null;
  }

  const apiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || '';
  console.log("Using API URL for tag mapping:", apiUrl);

  // Extract posts - handle both v4 and v5 structures
  let posts: BlogPostInTag[] = [];
  try {
    // Adjust based on how posts are related to tags (e.g., 'blog_posts' or 'blogPosts')
    const postsData = hasAttributes
      ? data.blog_posts?.data // Check common relation names
      : (data.blog_posts || data.blogPosts);

    if (Array.isArray(postsData)) {
      posts = postsData.map((post: any) => {
        const postData = post.attributes || post;
        // Correctly access author from author_blogs array (take the first one)
        const authorArray = postData.author_blogs || [];
        // Robust author data access: check for attributes first, then direct access
        const rawAuthorData = authorArray.length > 0 ? authorArray[0] : {};
        const authorData = rawAuthorData.attributes || rawAuthorData;

        // --- Start: More robust featured image URL extraction (similar to category page) ---
        const rawFeaturedImageData = postData.featuredImage?.data || postData.featuredImage || {};
        let featuredImageUrl = sanitizeUrl(rawFeaturedImageData); // Use sanitizeUrl first

        if (!featuredImageUrl && rawFeaturedImageData) {
          if (typeof rawFeaturedImageData === 'string') {
            featuredImageUrl = sanitizeUrl(rawFeaturedImageData);
          } else if (rawFeaturedImageData.url) {
            featuredImageUrl = sanitizeUrl(rawFeaturedImageData.url);
          } else if (rawFeaturedImageData.data?.attributes?.url) {
            featuredImageUrl = sanitizeUrl(rawFeaturedImageData.data.attributes.url);
          }
        }

        if (!featuredImageUrl && process.env.IMAGE_HOSTNAME && rawFeaturedImageData) {
          const imageHostname = process.env.IMAGE_HOSTNAME;
          let filename = '';
          if (typeof rawFeaturedImageData === 'string' && rawFeaturedImageData.includes('/')) {
            const parts = rawFeaturedImageData.split('/');
            filename = parts[parts.length - 1];
          } else if (rawFeaturedImageData.url && rawFeaturedImageData.url.includes('/')) {
            const parts = rawFeaturedImageData.url.split('/');
            filename = parts[parts.length - 1];
          } else if (rawFeaturedImageData.data?.attributes?.url && rawFeaturedImageData.data.attributes.url.includes('/')) {
            const parts = rawFeaturedImageData.data.attributes.url.split('/');
            filename = parts[parts.length - 1];
          } else if (rawFeaturedImageData.data?.attributes?.name) {
            filename = rawFeaturedImageData.data.attributes.name;
          } else if (rawFeaturedImageData.name) {
            filename = rawFeaturedImageData.name;
          } else if (rawFeaturedImageData.data?.attributes?.hash) {
            filename = `${rawFeaturedImageData.data.attributes.hash}.${rawFeaturedImageData.data.attributes.ext || 'jpg'}`;
          }
          if (filename) {
            featuredImageUrl = `${imageHostname}/${filename}`; // Construct with IMAGE_HOSTNAME
          }
        }
        // --- End: More robust featured image URL extraction ---

        // --- Start: More robust author profile picture URL extraction (similar to category page) ---
        const rawAuthorProfilePicData = authorData.profilePicture?.data || authorData.profilePicture || {};
        let authorProfilePicUrl = sanitizeUrl(rawAuthorProfilePicData); // Use sanitizeUrl first

        if (!authorProfilePicUrl && rawAuthorProfilePicData) {
          if (typeof rawAuthorProfilePicData === 'string') {
            authorProfilePicUrl = sanitizeUrl(rawAuthorProfilePicData);
          } else if (rawAuthorProfilePicData.url) {
            authorProfilePicUrl = sanitizeUrl(rawAuthorProfilePicData.url);
          } else if (rawAuthorProfilePicData.data?.attributes?.url) {
            authorProfilePicUrl = sanitizeUrl(rawAuthorProfilePicData.data.attributes.url);
          }
        }
        if (!authorProfilePicUrl && process.env.IMAGE_HOSTNAME && rawAuthorProfilePicData) {
          const imageHostname = process.env.IMAGE_HOSTNAME;
          let filename = '';
          if (typeof rawAuthorProfilePicData === 'string' && rawAuthorProfilePicData.includes('/')) {
            const parts = rawAuthorProfilePicData.split('/');
            filename = parts[parts.length - 1];
          } else if (rawAuthorProfilePicData.url && rawAuthorProfilePicData.url.includes('/')) {
            const parts = rawAuthorProfilePicData.url.split('/');
            filename = parts[parts.length - 1];
          } else if (rawAuthorProfilePicData.data?.attributes?.url && rawAuthorProfilePicData.data.attributes.url.includes('/')) {
            const parts = rawAuthorProfilePicData.data.attributes.url.split('/');
            filename = parts[parts.length - 1];
          } else if (rawAuthorProfilePicData.data?.attributes?.name) {
            filename = rawAuthorProfilePicData.data.attributes.name;
          } else if (rawAuthorProfilePicData.name) {
            filename = rawAuthorProfilePicData.name;
          }
          if (filename) {
            authorProfilePicUrl = `${imageHostname}/${filename}`; // Construct with IMAGE_HOSTNAME
          }
        }
        // --- End: More robust author profile picture URL extraction ---

        // Create the blog post object without view_count directly in the object
        const blogPost = {
          id: post.id || '',
          title: postData.title || 'Untitled Post',
          slug: postData.slug || 'untitled-post',
          excerpt: postData.excerpt || null,
          featured_image: featuredImageUrl, // Use the potentially corrected URL
          // Prioritize publishDate, then fall back
          publish_date: postData.publishDate || postData.published_at || postData.createdAt || new Date().toISOString(),
          author: {
            name: authorData.name || 'Unknown Author',
            slug: authorData.slug || 'unknown-author', // Assuming author has a slug field
            profile_picture: authorProfilePicUrl // Use the potentially corrected URL
          }
        };

        // Add view_count as a non-enumerable property if it exists
        if (postData.view_count !== undefined) {
          Object.defineProperty(blogPost, 'view_count', {
            value: postData.view_count,
            enumerable: false
          });
        }

        return blogPost;
      });
    }
  } catch (e) {
    console.warn("Error extracting posts for tag:", e);
  }

  // Get tag fields with fallbacks
  const name = data.name || 'Unnamed Tag';
  const slug = data.slug || 'unnamed-tag';
  const description = data.description || null; // Add description if available

  // Return the mapped tag
  return {
    id,
    name,
    slug,
    description,
    posts
  };
}

// Make the component async to fetch data
export default async function BlogTagPage({ params: paramsProp }: { params: { slug: string } }) {
  // Await the params object before destructuring
  const params = await paramsProp;
  const { slug } = params;
  let tag: BlogTag | null = null;
  let seoData = null; // Placeholder for SEO data

  try {
    console.log(`Fetching blog tag with slug: "${slug}"`);
    // Use the new getTagBySlug function
    const response = await getStrapiContent.blog.getTagBySlug(slug);
    console.log("Raw API response for tag:", JSON.stringify(response, null, 2));

    if (response && response.data && response.data.length > 0) {
      console.log("Found tag data, mapping to component props");
      tag = mapStrapiTagToProps(response.data[0]);

      // Get SEO data if available (assuming SEO component is attached to blog tags)
      // seoData = getStrapiContent.seo.getMetadata(response.data[0]);
      // Note: Need to confirm if SEO component is used for blog tags
    } else {
      console.log(`No blog tag found with slug "${slug}"`);
      return notFound(); // Use notFound() if tag doesn't exist
    }
  } catch (error) {
    console.error(`Error fetching blog tag with slug ${slug}:`, error);
    // In production, show 404 if the API fails
    return notFound();
  }

  // If tag not found after mapping or API error, show 404
  if (!tag) {
    return notFound();
  }

  // Format tag name for display (capitalize each word) - Keep this logic
  const formattedTagName = tag.name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  // Create SEO props (adjust based on actual SEO data availability)
  const seoProps = {
    defaultTitle: `Blog Tag: ${formattedTagName}`,
    defaultDescription: tag.description || `Articles tagged with "${tag.name}".`,
    // defaultOgImage: tag.featured_image || '', // Add if tags have images
    pageType: 'website' as 'article' | 'website',
    // ...(seoData ? { /* Map seoData fields */ } : {})
  };

  return (
    <>
      {/* SEO Head */}
      <SEOHead {...seoProps} />

      {/* Breadcrumb */}
      <div className="bg-gray-100 py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-emerald-600">Home</Link>
            <span className="mx-2">/</span>
            <Link href="/blog" className="hover:text-emerald-600">Blog</Link>
            <span className="mx-2">/</span>
            {/* Consider adding a link to a tags overview page if it exists */}
            {/* <Link href="/blog/tags" className="hover:text-emerald-600">Tags</Link>
            <span className="mx-2">/</span> */}
            <span className="text-gray-800">{formattedTagName}</span>
          </div>
        </div>
      </div>

      {/* Tag Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-3 mb-4">
            <FiTag className="text-3xl" />
            <h1 className="text-3xl md:text-4xl font-bold">{formattedTagName}</h1>
          </div>
          {/* Display tag description if available */}
          {tag.description && (
            <p className="text-lg max-w-3xl">
              {tag.description}
            </p>
          )}
          {/* Fallback description if none exists */}
          {!tag.description && (
             <p className="text-lg max-w-3xl">
               Articles tagged with "{tag.name}"
             </p>
          )}
        </div>
      </div>

      {/* Tag Posts */}
      <div className="bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            {/* Use fetched posts data */}
            {tag.posts.length} {tag.posts.length === 1 ? 'Article' : 'Articles'} Found
          </h2>

          {tag.posts && tag.posts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Map over fetched posts */}
              {tag.posts.map(post => (
                <BlogPostCard
                  key={post.id}
                  post={post} // Pass the mapped post directly
                />
              ))}
            </div>
          ) : (
            <div className="bg-white p-8 rounded-lg text-center">
              <p className="text-gray-600">No articles found with this tag.</p>
            </div>
          )}

          {/* Removed hardcoded Popular Tags section */}
          {/* Consider adding dynamic popular tags later if needed */}

          {/* Back to Blog Link */}
          <div className="mt-12 text-center">
            <Link
              href="/blog"
              className="text-emerald-600 hover:text-emerald-700 flex items-center justify-center"
            >
              <FiArrowLeft className="mr-2" /> Back to All Articles
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
