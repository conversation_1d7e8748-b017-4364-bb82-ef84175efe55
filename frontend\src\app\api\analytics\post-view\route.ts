import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import logger from '@/lib/logger';

/**
 * API route to track post views
 * Stores view data in Strapi's post-view collection
 */
export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();
    const { postId, postSlug } = body;

    if (!postId || !postSlug) {
      return NextResponse.json(
        { error: 'Missing required fields: postId and postSlug' },
        { status: 400 }
      );
    }

    // Get the visitor's cookie or set a new one
    const cookieStore = cookies();
    const visitorId = cookieStore.get('visitor_id')?.value ||
      `visitor_${Math.random().toString(36).substring(2, 15)}`;

    // Set the cookie if it doesn't exist
    if (!cookieStore.get('visitor_id')) {
      cookieStore.set('visitor_id', visitorId, {
        maxAge: 60 * 60 * 24 * 365, // 1 year
        path: '/',
        sameSite: 'strict',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
      });
    }

    // Get referrer if available
    const referrer = request.headers.get('referer') || null;

    // Strapi API token for authentication - MUST be set in environment variables
    const strapiToken = process.env.STRAPI_API_TOKEN;

    if (!strapiToken) {
      logger.error('STRAPI_API_TOKEN environment variable is not set');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Strapi API URL
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';

    // Check if this visitor has already viewed this post in the last 24 hours
    const existingViewResponse = await fetch(
      `${strapiUrl}/api/post-views?filters[visitorId][$eq]=${visitorId}&filters[post][id][$eq]=${postId}&filters[timestamp][$gt]=${new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()}`,
      {
        headers: {
          'Authorization': `Bearer ${strapiToken}`
        }
      }
    );

    if (!existingViewResponse.ok) {
      logger.error('Error checking for existing views:', await existingViewResponse.text());
      throw new Error(`Failed to check for existing views: ${existingViewResponse.statusText}`);
    }

    const existingViewData = await existingViewResponse.json();

    // If the visitor has already viewed this post in the last 24 hours, don't record a new view
    if (existingViewData.data && existingViewData.data.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'View already recorded within the last 24 hours'
      });
    }

    // Record the view in Strapi
    const viewResponse = await fetch(`${strapiUrl}/api/post-views`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${strapiToken}`
      },
      body: JSON.stringify({
        data: {
          post: postId,
          visitorId,
          referrer,
          timestamp: new Date().toISOString() // Set the current timestamp
        }
      })
    });

    if (!viewResponse.ok) {
      logger.error('Error recording view:', await viewResponse.text());
      throw new Error(`Failed to record view: ${viewResponse.statusText}`);
    }

    // Log success only in development
    logger.info(`Post view recorded: ${postId} (${postSlug}) by ${visitorId}`);

    return NextResponse.json({ success: true, message: 'View recorded successfully' });

  } catch (error) {
    logger.error('Error tracking post view:', error);
    return NextResponse.json(
      { error: 'Failed to track post view' },
      { status: 500 }
    );
  }
}
