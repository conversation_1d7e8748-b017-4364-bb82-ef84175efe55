const strapiHelpers = require('../helpers/strapi');

// This test suite uses real Strapi data
describe('Strapi Real Data Tests', () => {
  // Skip tests if we're not in a test environment that has access to the real API
  const runTests = process.env.TEST_WITH_REAL_DATA === 'true';
  
  // Test reading clinics data
  (runTests ? it : it.skip)('should fetch clinics from the real API', async () => {
    try {
      const result = await strapiHelpers.getEntries('clinics', {
        'pagination[page]': 1,
        'pagination[pageSize]': 5,
      });
      
      expect(result).toBeDefined();
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      
      // Log the first clinic for debugging
      if (result.data.length > 0) {
        console.log('First clinic:', result.data[0].attributes.name);
      }
    } catch (error) {
      // If the test fails, it might be because the API is not accessible
      console.error('API Error:', error.message);
      throw error;
    }
  });
  
  // Test reading blog posts data
  (runTests ? it : it.skip)('should fetch blog posts from the real API', async () => {
    try {
      const result = await strapiHelpers.getEntries('blogs', {
        'pagination[page]': 1,
        'pagination[pageSize]': 5,
      });
      
      expect(result).toBeDefined();
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      
      // Log the first blog post for debugging
      if (result.data.length > 0) {
        console.log('First blog post:', result.data[0].attributes.title);
      }
    } catch (error) {
      console.error('API Error:', error.message);
      throw error;
    }
  });
  
  // Test reading practitioners data
  (runTests ? it : it.skip)('should fetch practitioners from the real API', async () => {
    try {
      const result = await strapiHelpers.getEntries('practitioners', {
        'pagination[page]': 1,
        'pagination[pageSize]': 5,
      });
      
      expect(result).toBeDefined();
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      
      // Log the first practitioner for debugging
      if (result.data.length > 0) {
        console.log('First practitioner:', result.data[0].attributes.name);
      }
    } catch (error) {
      console.error('API Error:', error.message);
      throw error;
    }
  });
});
