'use client';

import React from 'react';
import { FiWifiOff, FiRefreshCw } from 'react-icons/fi';

interface APIErrorFallbackProps {
  message?: string;
  onRetry?: () => void;
  isLoading?: boolean;
  statusCode?: number;
}

/**
 * A specialized error fallback component for API errors
 * with a retry button and appropriate messaging based on status code.
 */
const APIErrorFallback: React.FC<APIErrorFallbackProps> = ({
  message,
  onRetry,
  isLoading = false,
  statusCode,
}) => {
  // Determine appropriate message based on status code
  let displayMessage = message;
  let icon = <FiWifiOff className="w-6 h-6 mr-2" />;
  
  if (!displayMessage) {
    if (statusCode === 404) {
      displayMessage = 'The requested resource was not found.';
    } else if (statusCode === 403) {
      displayMessage = 'You don\'t have permission to access this resource.';
    } else if (statusCode === 401) {
      displayMessage = 'Authentication is required to access this resource.';
    } else if (statusCode >= 500) {
      displayMessage = 'The server encountered an error. Please try again later.';
    } else {
      displayMessage = 'Failed to load data. Please check your connection and try again.';
    }
  }

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 my-2">
      <div className="flex items-center text-gray-700 mb-2">
        {icon}
        <h3 className="font-medium">Data Loading Error</h3>
      </div>
      
      <p className="text-gray-600 mb-3 text-sm">{displayMessage}</p>
      
      {onRetry && (
        <button
          onClick={onRetry}
          disabled={isLoading}
          className={`flex items-center px-3 py-1.5 text-sm rounded transition-colors ${
            isLoading 
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
              : 'bg-emerald-600 text-white hover:bg-emerald-700'
          }`}
        >
          <FiRefreshCw className={`mr-1.5 ${isLoading ? 'animate-spin' : ''}`} />
          {isLoading ? 'Retrying...' : 'Retry'}
        </button>
      )}
    </div>
  );
};

export default APIErrorFallback;
