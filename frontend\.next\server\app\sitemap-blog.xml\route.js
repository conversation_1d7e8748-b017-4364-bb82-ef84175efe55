(()=>{var e={};e.id=2150,e.ids=[2150],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},40076:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>m,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var o={};a.r(o),a.d(o,{GET:()=>p});var r=a(96559),s=a(48088),n=a(37719),l=a(58446);let i=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";process.env.NEXT_PUBLIC_SITE_URL,console.log(`Using site URL for blog sitemap: ${i}`);let g=i.endsWith("/")?i.slice(0,-1):i;async function p(e){let t=[];try{console.log("Generating blog sitemap...");let e=await l.$.blog.getPosts({pagination:{pageSize:1,page:1},fields:["id","slug"]});console.log("Count response structure:",JSON.stringify({hasData:!!e?.data,dataIsArray:Array.isArray(e?.data),dataLength:Array.isArray(e?.data)?e.data.length:"not an array",hasMeta:!!e?.meta,hasPagination:!!e?.meta?.pagination,totalItems:e?.meta?.pagination?.total||"unknown"}));let a=e?.meta?.pagination?.total||0;if(0===a&&Array.isArray(e?.data)){console.log("No pagination metadata found, fetching all posts to count them");try{let e=await l.$.blog.getPosts({pagination:{pageSize:1e3},fields:["id"]});Array.isArray(e?.data)&&(a=e.data.length,console.log(`Counted ${a} posts from direct data array`))}catch(e){console.error("Error fetching all posts for counting:",e)}}let o=Math.ceil(a/500);console.log(`Found ${a} total blog posts, will fetch in ${o} pages with page size 500`),console.log("API URL being used: https://nice-badge-2130241d6c.strapiapp.com"),console.log(`Site URL being used: ${i}`),e?.meta?.pagination&&console.log("Pagination details:",JSON.stringify(e.meta.pagination));for(let e=1;e<=o;e++){console.log(`Fetching blog posts page ${e} of ${o}...`);let a=await l.$.blog.getPosts({pagination:{pageSize:500,page:e},fields:["slug","updatedAt","publishDate","createdAt"],sort:"publishDate:desc",publicationState:"live"});a?.meta?.pagination&&console.log(`Pagination details for page ${e}:`,JSON.stringify(a.meta.pagination));let r=a?.data||[];console.log(`Retrieved ${r.length} blog posts on page ${e}`),r.length>0?console.log("First post structure:",JSON.stringify({hasAttributes:!!r[0].attributes,hasDirectSlug:!!r[0].slug,attributesSlug:r[0].attributes?.slug,keys:Object.keys(r[0])})):(console.log("No posts found in the response"),console.log("Full response:",JSON.stringify(a))),r.length<500&&e<o&&console.warn(`Warning: Got only ${r.length} blog posts on page ${e}, expected 500. This may indicate a pagination issue.`);let s=r.map(e=>{let t,a=e.attributes||e,o=null;return(a.slug?o=a.slug:e.slug?o=e.slug:e.documentId&&console.log(`Post with documentId ${e.documentId} has no slug`),t=a.updatedAt?new Date(a.updatedAt):a.publishDate?new Date(a.publishDate):a.createdAt?new Date(a.createdAt):e.updatedAt?new Date(e.updatedAt):e.publishDate?new Date(e.publishDate):e.createdAt?new Date(e.createdAt):new Date,o)?{url:`${g}/blog/${o}`,lastModified:t,changeFrequency:"weekly",priority:.7}:(console.log("Post with no slug:",JSON.stringify(e)),null)}).filter(Boolean);t=[...t,...s]}t.unshift({url:`${g}/blog`,lastModified:new Date,changeFrequency:"daily",priority:.9});let r=await l.$.blog.getCategories({pagination:{pageSize:100}}),s=(r?.data||[]).map(e=>{let t=(e.attributes||e).slug||e.slug;return t?{url:`${g}/blog/categories/${t}`,lastModified:new Date,changeFrequency:"weekly",priority:.6}:null}).filter(Boolean);t=[...t,...s];let n=await l.$.blog.getTags(),p=(n?.data||[]).map(e=>{let t=(e.attributes||e).slug||e.slug;return t?{url:`${g}/blog/tags/${t}`,lastModified:new Date,changeFrequency:"weekly",priority:.6}:null}).filter(Boolean);if(t=[...t,...p],console.log(`Final sitemap entries count (including index page): ${t.length}`),t.length<=1&&a>0){console.log("No blog posts were added to the sitemap, trying direct approach");try{let e=await l.$.blog.getPosts({pagination:{pageSize:1e3},fields:["slug","updatedAt","publishDate","createdAt"]});if(Array.isArray(e?.data)&&e.data.length>0){console.log(`Retrieved ${e.data.length} blog posts directly`);let a=e.data.map(e=>{let t=e.attributes||e,a=null;if(t.slug?a=t.slug:e.slug&&(a=e.slug),!a)return null;let o=new Date(t.updatedAt||t.publishDate||t.createdAt||e.updatedAt||e.publishDate||e.createdAt||new Date);return{url:`${g}/blog/${a}`,lastModified:o,changeFrequency:"weekly",priority:.7}}).filter(Boolean);t=[...t,...a],console.log(`Added ${a.length} blog posts to sitemap via direct approach`)}}catch(e){console.error("Error in direct approach:",e)}}console.log(`Sitemap generation summary:
    - Total blog posts found: ${a}
    - Total pages fetched: ${o}
    - Page size used: 500
    - Final sitemap entries: ${t.length}
    - Sitemap URL: ${g}/sitemap-blog.xml
    `);let u=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${t.map(e=>`
  <url>
    <loc>${e.url}</loc>
    <lastmod>${e.lastModified.toISOString()}</lastmod>
    <changefreq>${e.changeFrequency}</changefreq>
    <priority>${e.priority}</priority>
  </url>
  `).join("")}
</urlset>`;return new Response(u,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600","X-Content-Type-Options":"nosniff"}})}catch(e){return console.error("Error generating blog sitemap:",e),e.response?(console.error("Error response data:",e.response.data),console.error("Error response status:",e.response.status),console.error("Error response headers:",e.response.headers)):e.request?console.error("Error request:",e.request):console.error("Error message:",e.message),console.error("API URL:","https://nice-badge-2130241d6c.strapiapp.com"),console.error("Site URL:",process.env.NEXT_PUBLIC_SITE_URL),new Response(`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${g}/blog</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"no-cache","X-Content-Type-Options":"nosniff"}})}}let u=new r.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/sitemap-blog.xml/route",pathname:"/sitemap-blog.xml",filename:"route",bundlePath:"app/sitemap-blog.xml/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\sitemap-blog.xml\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:h}=u;function m(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,a)=>{"use strict";e.exports=a(44870)}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[7719,1330,3376,8446],()=>a(40076));module.exports=o})();