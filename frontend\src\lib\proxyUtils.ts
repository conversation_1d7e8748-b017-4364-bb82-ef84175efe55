import axios, { AxiosRequestConfig } from 'axios';

/**
 * Utility function to fetch data from Strapi through the Next.js proxy
 * This avoids CORS issues by making the request through your own domain
 * 
 * @param endpoint The Strapi API endpoint (e.g., '/posts', '/categories')
 * @param options Axios request options
 * @returns The response data from Strapi
 */
export const fetchFromProxy = async <T>(
  endpoint: string,
  options: AxiosRequestConfig = {}
): Promise<T> => {
  try {
    // Extract params from options
    const { params, ...restOptions } = options;
    
    // Build the URL to the proxy endpoint
    const proxyUrl = `/api/strapi-proxy?endpoint=${encodeURIComponent(endpoint)}`;
    
    // Add any additional query parameters
    const queryParams = new URLSearchParams();
    if (params) {
      // Convert params object to URLSearchParams
      Object.entries(params).forEach(([key, value]) => {
        // Handle nested objects by stringifying them
        if (typeof value === 'object' && value !== null) {
          queryParams.append(key, JSON.stringify(value));
        } else {
          queryParams.append(key, String(value));
        }
      });
    }
    
    // Make the request through the proxy
    const response = await axios.get<T>(proxyUrl, {
      ...restOptions,
      params: queryParams,
    });
    
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching from proxy (${endpoint}):`, error);
    
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    
    throw error;
  }
};

/**
 * Utility function to post data to Strapi through the Next.js proxy
 * 
 * @param endpoint The Strapi API endpoint (e.g., '/posts', '/categories')
 * @param data The data to send in the request body
 * @param options Axios request options
 * @returns The response data from Strapi
 */
export const postToProxy = async <T>(
  endpoint: string,
  data: any,
  options: AxiosRequestConfig = {}
): Promise<T> => {
  try {
    // Build the URL to the proxy endpoint
    const proxyUrl = `/api/strapi-proxy?endpoint=${encodeURIComponent(endpoint)}`;
    
    // Make the request through the proxy
    const response = await axios.post<T>(proxyUrl, data, options);
    
    return response.data;
  } catch (error: any) {
    console.error(`Error posting to proxy (${endpoint}):`, error);
    
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    
    throw error;
  }
};
