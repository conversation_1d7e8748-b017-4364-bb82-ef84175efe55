import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getStrapiContent } from '@/lib/strapi';

/**
 * Component to display a list of clinics
 */
const ClinicsList: React.FC = () => {
  const { data: clinics, isLoading, error } = useQuery({
    queryKey: ['clinics'],
    queryFn: () => getStrapiContent.clinics.getAll(),
  });

  if (isLoading) {
    return <div>Loading clinics...</div>;
  }

  if (error) {
    return <div>Error loading clinics: {(error as Error).message}</div>;
  }

  if (!clinics || clinics.length === 0) {
    return <div>No clinics found</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {clinics.map((clinic) => (
        <div key={clinic.id} className="border rounded-lg p-4 shadow-sm">
          <h3 className="text-xl font-semibold">{clinic.name}</h3>
          <p className="mt-2 text-gray-600">{clinic.description}</p>
        </div>
      ))}
    </div>
  );
};

export default ClinicsList;
