/**
 * Optimized data fetching utilities for Strapi using Next.js native fetch
 * This file provides functions to reduce API calls to Strapi with proper ISR support
 */
import { cache } from 'react';

// Get Strapi URL from environment variable
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1337';
const API_PATH = '/api';

// In-flight requests cache to prevent duplicate requests (for client-side only)
const inFlightRequests = new Map<string, Promise<any>>();

/**
 * Generate a cache key from endpoint and params
 */
function generateCacheKey(endpoint: string, params?: any): string {
  return `${endpoint}:${JSON.stringify(params || {})}`;
}

/**
 * Generate cache tags for a specific content type
 */
function generateCacheTags(contentType: string, id?: string | number): string[] {
  const tags = [`strapi-${contentType}`];
  if (id) {
    tags.push(`strapi-${contentType}-${id}`);
  }
  return tags;
}

/**
 * Fetch data from API with Next.js native fetch and proper ISR support
 *
 * @param endpoint API endpoint
 * @param options Request options
 * @param revalidate Revalidation period in seconds (default: 3600 = 1 hour)
 * @returns Promise with the API response
 */
export const fetchWithOptimization = cache(async <T>(
  endpoint: string,
  options: {
    params?: Record<string, any>;
    revalidate?: number | false;
    tags?: string[];
  } = {},
  isServerSide = typeof window === 'undefined'
): Promise<T> => {
  try {
    // Ensure endpoint starts with a slash
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const { params, revalidate = 3600, tags = [] } = options;

    // Build query string from params
    const queryString = params
      ? '?' + new URLSearchParams(
          Object.entries(params).reduce((acc, [key, value]) => {
            // Handle nested objects by stringifying them
            if (typeof value === 'object' && value !== null) {
              acc[key] = JSON.stringify(value);
            } else {
              acc[key] = String(value);
            }
            return acc;
          }, {} as Record<string, string>)
        ).toString()
      : '';

    if (process.env.NODE_ENV === 'development') {
      console.log(`Fetching from API: ${normalizedEndpoint}${queryString}`);
      console.log(`Using cache options: revalidate=${revalidate}, tags=${tags.join(', ')}`);
    }

    // Use Next.js fetch with caching options
    const response = await fetch(`${API_URL}${API_PATH}${normalizedEndpoint}${queryString}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.STRAPI_API_TOKEN || ''}`,
      },
      next: {
        revalidate,
        tags,
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching from API: ${response.status} ${response.statusText}`);
    }

    return response.json();
  } catch (error) {
    console.error(`Error fetching from API (${endpoint}):`, error);

    // Return a safe default response instead of throwing
    if (endpoint.includes('/') && endpoint.split('/').filter(Boolean).length > 0) {
      return { data: [] } as unknown as T;
    }

    return {} as T;
  }
});

/**
 * Batch multiple API requests into a single Promise.all call
 * This reduces the number of separate network requests
 */
export async function batchRequests<T>(
  requests: Array<{
    endpoint: string;
    options?: {
      params?: Record<string, any>;
      revalidate?: number | false;
      tags?: string[];
    }
  }>
): Promise<T[]> {
  return Promise.all(
    requests.map(({ endpoint, options = {} }) =>
      fetchWithOptimization<T>(endpoint, options)
    )
  );
}

/**
 * Optimized fetch for specific content types with appropriate cache times and tags
 * Using Next.js native fetch with proper ISR support
 */
export const fetchContentType = cache(async <T>(
  contentType: string,
  options: {
    id?: string | number;
    params?: Record<string, any>;
    revalidate?: number | false;
    isDetailPage?: boolean;
  } = {}
): Promise<T> => {
  const { id, params, revalidate = false, isDetailPage = false } = options;

  // Generate endpoint
  const endpoint = id ? `/${contentType}/${id}` : `/${contentType}`;

  // Generate cache tags
  const tags = generateCacheTags(contentType, id);

  // Add global content type tag for list pages
  if (!isDetailPage) {
    tags.push('strapi-content');
  }

  // For detail pages, we want on-demand revalidation only (revalidate: false)
  // For list pages, we want a fallback revalidation period (default: 3600 seconds)
  const effectiveRevalidate = isDetailPage ? false : (revalidate || 3600);

  try {
    return fetchWithOptimization<T>(endpoint, {
      params,
      revalidate: effectiveRevalidate,
      tags,
    });
  } catch (error) {
    console.error(`Error in fetchContentType for ${contentType}:`, error);
    // Return a safe default response
    return { data: [] } as unknown as T;
  }
});

/**
 * Prefetch data for common pages
 * This reduces API calls when navigating between pages
 */
export async function prefetchCommonData(): Promise<void> {
  try {
    console.log('Prefetching common data...');

    // Use fetchContentType with proper cache options
    await Promise.allSettled([
      // Prefetch global settings
      fetchContentType(
        'global-setting',
        {
          params: { populate: '*' },
          revalidate: 86400, // 24 hours
          isDetailPage: false
        }
      ),

      // Prefetch categories
      fetchContentType(
        'categories',
        {
          params: { pagination: { pageSize: 10 } },
          revalidate: 86400, // 24 hours
          isDetailPage: false
        }
      ),

      // Prefetch specialties
      fetchContentType(
        'specialties',
        {
          params: { pagination: { pageSize: 10 } },
          revalidate: 86400, // 24 hours
          isDetailPage: false
        }
      )
    ]);

    console.log('Prefetching completed');
  } catch (error) {
    console.error('Error prefetching common data:', error);
    // Silently fail - this is just prefetching
  }
}
