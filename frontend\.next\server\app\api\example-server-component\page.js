(()=>{var e={};e.id=6706,e.ids=[6706],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28166:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413),a=t(54086);async function o(){try{let e=await (0,a.M4)("/global-setting",{params:{populate:"*",publicationState:"live"}});console.log("Global settings response structure:",JSON.stringify({hasData:!!e?.data,dataKeys:e?.data?Object.keys(e.data):[],hasAttributes:!!e?.data?.attributes}));let r="Default Site Name",t="Default site description";return e?.data&&(e.data.attributes?(r=e.data.attributes.siteName||r,t=e.data.attributes.siteDescription||t):(r=e.data.siteName||r,t=e.data.siteDescription||t)),(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-4",children:r}),(0,s.jsx)("p",{className:"text-lg mb-6",children:t}),(0,s.jsxs)("div",{className:"bg-green-100 p-4 rounded-md border border-green-300",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Server-Side Fetching Example"}),(0,s.jsxs)("p",{children:["This data was fetched on the server using the ",(0,s.jsx)("code",{children:"fetchFromApiServer"})," utility. The API token is stored securely on the server and never exposed to the client."]})]})]})}catch(e){return console.error("Error fetching data in server component:",e),(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Server Component Example"}),(0,s.jsxs)("div",{className:"bg-red-100 p-4 rounded-md border border-red-300",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Error Fetching Data"}),(0,s.jsx)("p",{children:"There was an error fetching data from the API. Please check the server logs for more details."})]})]})}}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32944:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>l});var s=t(65239),a=t(48088),o=t(31369),n=t(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);t.d(r,i);let l={children:["",{children:["api",{children:["example-server-component",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,28166)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\example-server-component\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\example-server-component\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/api/example-server-component/page",pathname:"/api/example-server-component",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33873:e=>{"use strict";e.exports=require("path")},54086:(e,r,t)=>{"use strict";t.d(r,{M4:()=>l,lX:()=>d});var s=t(44999),a=t(33376),o=t.n(a);let n="https://nice-badge-2130241d6c.strapiapp.com";console.log("Server API Client using URL:",n||"No API URL found in environment variables"),n||console.error("CRITICAL ERROR: No Strapi API URL found in environment variables. Please set NEXT_PUBLIC_API_URL in your Vercel environment variables.");let i=()=>{if(!n)throw Error("No Strapi API URL found in environment variables. Please set NEXT_PUBLIC_API_URL.");return`${n}/api`},l=async(e,r={})=>{let t=process.env.STRAPI_API_TOKEN,s=i(),a=`${s}${e.startsWith("/")?e:`/${e}`}`,n={"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`},...r.headers||{}};if(r.params&&("GET"===r.method||!r.method)){let e=o().stringify(r.params,{encodeValuesOnly:!0});e&&(a=`${a}?${e}`)}let{params:l,...d}=r;try{let r=d.next||{};if(void 0===r.revalidate&&!1!==r.revalidate&&(r.revalidate=3600),!r.tags||0===r.tags.length){let t=e.split("/")[0].replace(/^\/+/,"");t&&(r.tags=[`strapi-${t}`])}let t=await fetch(a,{...d,headers:n,next:r});if(!t.ok){let e;try{e=await t.json()}catch(r){e={message:t.statusText,details:await t.text().catch(()=>"")}}console.error(`Server: API Error (${a}): Status ${t.status}`,e);let r=Error(`API Error: ${t.status} ${t.statusText}`);throw r.response={status:t.status,data:e},r}return t.json()}catch(e){throw console.error(`Server: Error fetching from API (${a}):`,e.message||e),e.response&&(console.error("Server: Response status:",e.response.status),console.error("Server: Response data:",e.response.data)),e}},d=async(e,r={})=>{let t=(0,s.UL)(),a=t.get("jwt")?.value,n=i(),l=`${n}${e.startsWith("/")?e:`/${e}`}`,d={"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`},...r.headers||{}};if(r.params&&("GET"===r.method||!r.method)){let e=o().stringify(r.params,{encodeValuesOnly:!0});e&&(l=`${l}?${e}`)}let{params:c,...p}=r;try{let e=await fetch(l,{...p,headers:d});if(!e.ok){let r;try{r=await e.json()}catch(t){r={message:e.statusText,details:await e.text().catch(()=>"")}}console.error(`Server: API Error with user auth (${l}): Status ${e.status}`,r);let t=Error(`API Error with user auth: ${e.status} ${e.statusText}`);throw t.response={status:e.status,data:r},t}return e.json()}catch(e){throw console.error(`Server: Error fetching from API with user auth (${l}):`,e.message||e),e.response&&(console.error("Server: Response status:",e.response.status),console.error("Server: Response data:",e.response.data)),e}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,1330,3376,6391,2975,4999,8446,270],()=>t(32944));module.exports=s})();