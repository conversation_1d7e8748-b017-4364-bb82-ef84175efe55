import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag, revalidatePath } from 'next/cache';
import { StrapiWebhookPayload } from '@/types/strapi-webhook'; // We'll define this type next

// Ensure REVALIDATION_SECRET is set in your environment variables
const REVALIDATION_SECRET = process.env.REVALIDATION_SECRET;

export async function POST(request: NextRequest) {
  // 1. Verify the secret token
  // It's common to pass the secret as a query parameter or a header.
  // For this example, let's assume it's passed as a query parameter `secret`.
  const secret = request.nextUrl.searchParams.get('secret');

  if (!REVALIDATION_SECRET) {
    console.error('REVALIDATION_SECRET is not set. Skipping revalidation.');
    return NextResponse.json({ error: 'Revalidation secret not configured on server' }, { status: 500 });
  }

  if (secret !== REVALIDATION_SECRET) {
    console.warn('Invalid revalidation secret received.');
    return NextResponse.json({ error: 'Invalid revalidation secret' }, { status: 401 });
  }

  // 2. Parse the webhook payload
  let payload: StrapiWebhookPayload;
  try {
    payload = await request.json();
  } catch (error) {
    console.error('Failed to parse webhook payload:', error);
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }

  const { event, model, entry } = payload;

  if (!model || !entry) {
    console.warn('Webhook payload missing model or entry:', payload);
    return NextResponse.json({ error: 'Missing model or entry in payload' }, { status: 400 });
  }

  console.log(`Received webhook: event='${event}', model='${model}', id='${entry.id}', slug='${entry.slug || 'N/A'}'`);

  try {
    // 3. Determine which tags/paths to revalidate based on the model
    // This is a critical part and needs to be tailored to your content structure and tagging strategy.

    const pathsToRevalidate: string[] = [];
    const tagsToRevalidate: string[] = [`strapi-${model}`]; // General tag for the model type

    switch (model) {
      case 'clinic':
        if (entry.slug) {
          pathsToRevalidate.push(`/clinics/${entry.slug}`);
          tagsToRevalidate.push(`strapi-clinic-${entry.slug}`);
        }
        tagsToRevalidate.push('strapi-clinics'); // For clinic list pages
        tagsToRevalidate.push('strapi-featured-clinics'); // If it could be featured
        // If clinics are linked to categories, specialties, etc., revalidate those too.
        // This requires the webhook payload to include information about related entities,
        // or you might need to fetch them based on the entry.id if not present.
        if (entry.categories && Array.isArray(entry.categories)) {
          entry.categories.forEach((category: any) => {
            if (category.slug) {
              tagsToRevalidate.push(`strapi-clinics-category-${category.slug}`);
              pathsToRevalidate.push(`/categories/${category.slug}`); // Revalidate category page showing clinics
            }
          });
        }
        // Add more specific tags/paths as needed for clinics
        break;

      case 'practitioner':
        if (entry.slug) {
          pathsToRevalidate.push(`/practitioners/${entry.slug}`);
          tagsToRevalidate.push(`strapi-practitioner-${entry.slug}`);
        }
        tagsToRevalidate.push('strapi-practitioners');
        // If practitioners are linked to clinics, revalidate affected clinic pages
        if (entry.clinic?.slug) { // Assuming relation is named 'clinic' and has a slug
            pathsToRevalidate.push(`/clinics/${entry.clinic.slug}`);
            tagsToRevalidate.push(`strapi-clinic-${entry.clinic.slug}`);
        }
        break;

      case 'category':
        if (entry.slug) {
          pathsToRevalidate.push(`/categories/${entry.slug}`);
          tagsToRevalidate.push(`strapi-category-${entry.slug}`);
          tagsToRevalidate.push(`strapi-clinics-category-${entry.slug}`); // For clinic lists filtered by this category
        }
        tagsToRevalidate.push('strapi-categories');
        tagsToRevalidate.push('strapi-global'); // Categories are often global
        break;

      case 'specialty':
        if (entry.slug) {
          pathsToRevalidate.push(`/specialties/${entry.slug}`); // Assuming you have specialty pages
          tagsToRevalidate.push(`strapi-specialty-${entry.slug}`);
          tagsToRevalidate.push(`strapi-clinics-specialty-${entry.slug}`);
        }
        tagsToRevalidate.push('strapi-specialties');
        tagsToRevalidate.push('strapi-global');
        break;

      case 'condition':
         if (entry.slug) {
          pathsToRevalidate.push(`/conditions/${entry.slug}`); // Assuming you have condition pages
          tagsToRevalidate.push(`strapi-condition-${entry.slug}`);
          tagsToRevalidate.push(`strapi-clinics-condition-${entry.slug}`);
        }
        tagsToRevalidate.push('strapi-conditions');
        tagsToRevalidate.push('strapi-global');
        break;

      case 'service':
        // Services might be displayed on clinic pages or service-specific pages
        tagsToRevalidate.push('strapi-services');
        // Potentially revalidate all clinic pages if services are generic and widely used,
        // or more targeted if linked. This can be broad, so use with caution.
        // Consider revalidating related clinics if the payload provides that info.
        break;

      case 'setting': // For global settings (if your API ID is 'setting')
        tagsToRevalidate.push('strapi-settings');
        tagsToRevalidate.push('strapi-global'); // Global data often affects many pages
        // Consider revalidating common layout-dependent paths or all paths if settings are critical
        pathsToRevalidate.push('/'); // Revalidate homepage at least
        // Revalidating all paths ('/') can be resource-intensive.
        // Prefer tag-based revalidation for global data.
        break;

      // Add cases for other models: articles, pages, etc.
      // Example for a generic page model:
      // case 'page':
      //   if (entry.slug) {
      //     pathsToRevalidate.push(`/${entry.slug}`); // Assuming top-level slugs for pages
      //     tagsToRevalidate.push(`strapi-page-${entry.slug}`);
      //   }
      //   tagsToRevalidate.push('strapi-pages');
      //   break;

      default:
        console.warn(`No specific revalidation logic for model: ${model}. Revalidating general tags.`);
        // Fallback: revalidate common global tags and homepage
        tagsToRevalidate.push('strapi-global');
        pathsToRevalidate.push('/');
        break;
    }

    // 4. Perform revalidation
    let revalidatedPathsCount = 0;
    let revalidatedTagsCount = 0;

    // Deduplicate paths and tags
    const uniquePaths = [...new Set(pathsToRevalidate)];
    const uniqueTags = [...new Set(tagsToRevalidate)];

    for (const path of uniquePaths) {
      console.log(`Revalidating path: ${path}`);
      await revalidatePath(path);
      revalidatedPathsCount++;
    }

    for (const tag of uniqueTags) {
      console.log(`Revalidating tag: ${tag}`);
      await revalidateTag(tag);
      revalidatedTagsCount++;
    }

    console.log(`Revalidation complete. Paths: ${revalidatedPathsCount}, Tags: ${revalidatedTagsCount}.`);
    return NextResponse.json({
      revalidated: true,
      timestamp: new Date().toISOString(),
      model,
      event,
      entryId: entry.id,
      revalidatedPaths: uniquePaths,
      revalidatedTags: uniqueTags,
    });

  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown revalidation error';
    console.error('Error during revalidation process:', errorMessage, err);
    return NextResponse.json({ error: 'Revalidation failed', details: errorMessage }, { status: 500 });
  }
}
