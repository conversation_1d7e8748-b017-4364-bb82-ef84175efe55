'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface ErrorContextType {
  globalError: Error | null;
  setGlobalError: (error: Error | null) => void;
  clearGlobalError: () => void;
  addErrorLog: (error: Error, source?: string) => void;
  errorLogs: ErrorLog[];
}

interface ErrorLog {
  id: string;
  error: Error;
  timestamp: Date;
  source?: string;
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

interface ErrorProviderProps {
  children: ReactNode;
}

/**
 * Provider component for the global error context
 */
export const ErrorProvider: React.FC<ErrorProviderProps> = ({ children }) => {
  const [globalError, setGlobalError] = useState<Error | null>(null);
  const [errorLogs, setErrorLogs] = useState<ErrorLog[]>([]);

  const clearGlobalError = () => {
    setGlobalError(null);
  };

  const addErrorLog = (error: Error, source?: string) => {
    const newErrorLog: ErrorLog = {
      id: Date.now().toString(),
      error,
      timestamp: new Date(),
      source,
    };

    // Keep only the last 10 errors to avoid memory issues
    setErrorLogs(prevLogs => [newErrorLog, ...prevLogs].slice(0, 10));
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`Error from ${source || 'unknown source'}:`, error);
    }
  };

  return (
    <ErrorContext.Provider
      value={{
        globalError,
        setGlobalError,
        clearGlobalError,
        addErrorLog,
        errorLogs,
      }}
    >
      {children}
    </ErrorContext.Provider>
  );
};

/**
 * Hook to use the error context
 */
export const useError = (): ErrorContextType => {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useError must be used within an ErrorProvider');
  }
  return context;
};

export default ErrorContext;
