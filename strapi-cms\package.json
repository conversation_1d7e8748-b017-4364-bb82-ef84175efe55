{"name": "strapi-cms", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "test": "cross-env NODE_ENV=test jest --forceExit --detectOpenHandles tests/unit", "test:all": "cross-env NODE_ENV=test jest --forceExit --detectOpenHandles", "test:integration": "cross-env NODE_ENV=test jest --forceExit --detectOpenHandles tests/integration", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:real": "cross-env NODE_ENV=test TEST_WITH_REAL_DATA=true jest --forceExit --detectOpenHandles tests/integration/real-data.test.js"}, "dependencies": {"@strapi/plugin-cloud": "5.12.6", "@strapi/plugin-seo": "^2.0.8", "@strapi/plugin-users-permissions": "5.12.6", "@strapi/strapi": "5.12.6", "better-sqlite3": "11.3.0", "rate-limiter-flexible": "^7.1.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-plugin-multi-select": "^2.1.1", "strapi-plugin-oembed": "^2.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/supertest": "^6.0.3", "axios": "^1.6.7", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "jest": "^29.7.0", "sqlite3": "^5.1.7", "supertest": "^6.3.4", "ts-jest": "^29.3.2", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}