/**
 * Example server component that uses the server API utilities
 * This demonstrates how to fetch data from Strapi in a server component
 */
import { fetchFromApiServer } from '@/lib/serverApi';
import { notFound } from 'next/navigation';

// Define the response type for the global settings in Strapi v5 format
// In Strapi v5, the response is flattened (no nested attributes object)
interface GlobalSettingsResponse {
  data: {
    id: number;
    documentId?: string; // Strapi v5 uses documentId
    siteName: string;
    siteDescription: string;
    // Add other fields as needed
  };
  meta: {
    // Metadata fields
  };
}

export default async function ExampleServerComponent() {
  try {
    // Fetch global settings from Strapi using the server API utility
    const globalSettings = await fetchFromApiServer<GlobalSettingsResponse>(
      '/global-setting',
      {
        params: {
          populate: '*',
          publicationState: 'live',
        },
      }
    );

    // Log the response structure for debugging
    console.log('Global settings response structure:',
      JSON.stringify({
        hasData: !!globalSettings?.data,
        dataKeys: globalSettings?.data ? Object.keys(globalSettings.data) : [],
        hasAttributes: !!(globalSettings?.data as any)?.attributes,
      })
    );

    // Extract the data from the response, handling both v4 and v5 formats
    // In Strapi v5, properties are directly on data object, not in attributes
    let siteName = 'Default Site Name';
    let siteDescription = 'Default site description';

    if (globalSettings?.data) {
      // Check if we have a v4-style response with attributes
      if ((globalSettings.data as any).attributes) {
        siteName = (globalSettings.data as any).attributes.siteName || siteName;
        siteDescription = (globalSettings.data as any).attributes.siteDescription || siteDescription;
      } else {
        // v5-style flattened response
        siteName = globalSettings.data.siteName || siteName;
        siteDescription = globalSettings.data.siteDescription || siteDescription;
      }
    }

    return (
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-4">{siteName}</h1>
        <p className="text-lg mb-6">{siteDescription}</p>

        <div className="bg-green-100 p-4 rounded-md border border-green-300">
          <h2 className="text-xl font-semibold mb-2">Server-Side Fetching Example</h2>
          <p>
            This data was fetched on the server using the <code>fetchFromApiServer</code> utility.
            The API token is stored securely on the server and never exposed to the client.
          </p>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error fetching data in server component:', error);
    // Return a more graceful error UI instead of 404
    return (
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-4">Server Component Example</h1>
        <div className="bg-red-100 p-4 rounded-md border border-red-300">
          <h2 className="text-xl font-semibold mb-2">Error Fetching Data</h2>
          <p>
            There was an error fetching data from the API. Please check the server logs for more details.
          </p>
        </div>
      </div>
    );
  }
}
