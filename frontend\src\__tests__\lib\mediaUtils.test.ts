import { getStrapiMediaUrl, getProfilePictureUrl, getFeaturedImageUrl } from '@/lib/mediaUtils';

// Mock environment variables
const originalEnv = process.env;

describe('Media Utilities', () => {
  beforeEach(() => {
    // Setup environment variables for tests
    process.env = {
      ...originalEnv,
      NEXT_PUBLIC_STRAPI_API_URL: 'http://localhost:1337',
    };
    
    // Mock console methods to prevent logs during tests
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'debug').mockImplementation(() => {});
  });
  
  afterEach(() => {
    // Restore environment variables
    process.env = originalEnv;
    
    // Restore console methods
    jest.restoreAllMocks();
  });
  
  describe('getStrapiMediaUrl', () => {
    test('should return null for null or undefined input', () => {
      expect(getStrapiMediaUrl(null)).toBeNull();
      expect(getStrapiMediaUrl(undefined)).toBeNull();
    });
    
    test('should return the same URL if it is already absolute', () => {
      const absoluteUrl = 'https://example.com/image.jpg';
      expect(getStrapiMediaUrl(absoluteUrl)).toBe(absoluteUrl);
    });
    
    test('should prepend Strapi URL to relative URL strings', () => {
      const relativeUrl = '/uploads/image.jpg';
      expect(getStrapiMediaUrl(relativeUrl)).toBe('http://localhost:1337/uploads/image.jpg');
    });
    
    test('should extract URL from Strapi v5 media object with direct url property', () => {
      const mediaObject = {
        url: '/uploads/image.jpg'
      };
      expect(getStrapiMediaUrl(mediaObject)).toBe('http://localhost:1337/uploads/image.jpg');
    });
    
    test('should extract URL from Strapi v4 media object with nested data.attributes', () => {
      const mediaObject = {
        data: {
          attributes: {
            url: '/uploads/image.jpg'
          }
        }
      };
      expect(getStrapiMediaUrl(mediaObject)).toBe('http://localhost:1337/uploads/image.jpg');
    });
    
    test('should extract URL from object with data.url structure', () => {
      const mediaObject = {
        data: {
          url: '/uploads/image.jpg'
        }
      };
      expect(getStrapiMediaUrl(mediaObject)).toBe('http://localhost:1337/uploads/image.jpg');
    });
    
    test('should return null for invalid media object', () => {
      const invalidObject = { foo: 'bar' };
      expect(getStrapiMediaUrl(invalidObject)).toBeNull();
    });
  });
  
  describe('getProfilePictureUrl', () => {
    test('should return null for null or undefined input', () => {
      expect(getProfilePictureUrl(null)).toBeNull();
      expect(getProfilePictureUrl(undefined)).toBeNull();
    });
    
    test('should extract URL from direct profilePicture.url property', () => {
      const authorData = {
        profilePicture: {
          url: '/uploads/profile.jpg'
        }
      };
      expect(getProfilePictureUrl(authorData)).toBe('http://localhost:1337/uploads/profile.jpg');
    });
    
    test('should extract URL from nested data.attributes structure', () => {
      const authorData = {
        profilePicture: {
          data: {
            attributes: {
              url: '/uploads/profile.jpg'
            }
          }
        }
      };
      expect(getProfilePictureUrl(authorData)).toBe('http://localhost:1337/uploads/profile.jpg');
    });
    
    test('should extract URL from formats.thumbnail structure', () => {
      const authorData = {
        profilePicture: {
          formats: {
            thumbnail: {
              url: '/uploads/thumbnail_profile.jpg'
            }
          }
        }
      };
      expect(getProfilePictureUrl(authorData)).toBe('http://localhost:1337/uploads/thumbnail_profile.jpg');
    });
  });
  
  describe('getFeaturedImageUrl', () => {
    test('should return null for null or undefined input', () => {
      expect(getFeaturedImageUrl(null)).toBeNull();
      expect(getFeaturedImageUrl(undefined)).toBeNull();
    });
    
    test('should extract URL from direct featuredImage.url property', () => {
      const postData = {
        featuredImage: {
          url: '/uploads/featured.jpg'
        }
      };
      expect(getFeaturedImageUrl(postData)).toBe('http://localhost:1337/uploads/featured.jpg');
    });
    
    test('should extract URL from nested data.attributes structure', () => {
      const postData = {
        featuredImage: {
          data: {
            attributes: {
              url: '/uploads/featured.jpg'
            }
          }
        }
      };
      expect(getFeaturedImageUrl(postData)).toBe('http://localhost:1337/uploads/featured.jpg');
    });
  });
});
