(()=>{var e={};e.id=6418,e.ids=[6418],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36463:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>n});var s=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let a={enabled:!1,level:"info",prefix:"[NHN]"};function o(e,t,...r){if(!a.enabled)return;let n=Object.values(s),i=n.indexOf(a.level);if(n.indexOf(e)>=i){let s=a.prefix?`${a.prefix} `:"",o=`${s}${t}`;switch(e){case"debug":console.debug(o,...r);break;case"info":console.info(o,...r);break;case"warn":console.warn(o,...r);break;case"error":console.error(o,...r)}}}let n={debug:function(e,...t){o("debug",e,...t)},info:function(e,...t){o("info",e,...t)},warn:function(e,...t){o("warn",e,...t)},error:function(e,...t){o("error",e,...t)},configure:function(e){a={...a,...e}}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},88227:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>R,routeModule:()=>c,serverHooks:()=>b,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>d});var a=r(96559),o=r(48088),n=r(37719),i=r(32190),l=r(62351),g=r(36463);let u=process.env.STRAPI_REVALIDATE_SECRET||process.env.REVALIDATE_TOKEN||process.env.PREVIEW_SECRET;async function d(e){if(!u)return g.Ay.error("CRITICAL: STRAPI_REVALIDATE_SECRET, REVALIDATE_TOKEN, or PREVIEW_SECRET is not set. Blog revalidation endpoint is disabled."),i.NextResponse.json({message:"Revalidation secret not configured."},{status:500});if(e.headers.get("X-Revalidate-Secret")!==u)return g.Ay.warn("Invalid blog revalidation attempt: Incorrect or missing X-Revalidate-Secret header."),i.NextResponse.json({message:"Invalid token"},{status:401});try{let{model:t,entry:r}=await e.json();if("blog-post"!==t)return g.Ay.info(`Revalidation skipped: Not a blog-post model. Model received: ${t}`),i.NextResponse.json({message:`Revalidation skipped: Not a blog-post model. Received: ${t}`},{status:200});let s=[];if(r&&r.slug){let e=r.slug,t=`strapi-blog-post-${e}`;s.push(t),g.Ay.info(`Attempting to revalidate tag: ${t} for blog post slug: ${e}`)}s.push("strapi-blog-posts-slugs"),g.Ay.info("Attempting to revalidate tag: strapi-blog-posts-slugs"),s.push("strapi-blog-posts"),s.push("strapi-blog-posts-featured"),s.push("strapi-blog-posts-recent");for(let e=1;e<=5;e++)s.push(`page-${e}`);if(r?.blog_categories&&Array.isArray(r.blog_categories)&&r.blog_categories.forEach(e=>{e.slug&&s.push(`strapi-category-${e.slug}`)}),r?.blog_tags&&Array.isArray(r.blog_tags)&&r.blog_tags.forEach(e=>{e.slug&&s.push(`strapi-tag-${e.slug}`)}),s.push("strapi-blog-categories"),s.push("strapi-blog-tags"),!(s.length>0))return g.Ay.info("No specific blog post slug found in webhook payload for revalidation, but revalidated general blog tags."),(0,l.revalidateTag)("strapi-blog-posts-slugs"),(0,l.revalidateTag)("strapi-blog-posts"),i.NextResponse.json({revalidated:!0,message:"General blog tags revalidated."});for(let e of s)(0,l.revalidateTag)(e);return g.Ay.info("Blog revalidation successful for tags:",s.join(", ")),i.NextResponse.json({revalidated:!0,revalidatedTags:s,timestamp:new Date().toISOString()})}catch(e){return g.Ay.error("Error during blog revalidation:",e),i.NextResponse.json({message:"Error revalidating blog posts",error:e.message},{status:500})}}async function p(e){if(!u)return i.NextResponse.json({message:"Revalidation secret not configured."},{status:500});let t=e.headers.get("X-Revalidate-Secret"),r=new URL(e.url),s=r.searchParams.get("tag");if(t!==u&&r.searchParams.get("secret")!==u)return g.Ay.warn("Invalid GET blog revalidation attempt: Incorrect or missing X-Revalidate-Secret header/secret query param."),i.NextResponse.json({message:"Invalid token"},{status:401});if(!s)return i.NextResponse.json({message:"Missing tag parameter for GET revalidation"},{status:400});try{return(0,l.revalidateTag)(s),g.Ay.info(`Manual blog revalidation successful for tag: ${s}`),i.NextResponse.json({revalidated:!0,revalidatedTag:s,timestamp:new Date().toISOString()})}catch(e){return g.Ay.error(`Error during manual blog revalidation for tag ${s}:`,e),i.NextResponse.json({message:"Error revalidating blog tag",error:e.message},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/revalidate/blog/route",pathname:"/api/revalidate/blog",filename:"route",bundlePath:"app/api/revalidate/blog/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\revalidate\\blog\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:v,serverHooks:b}=c;function R(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:v})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,6391,580],()=>r(88227));module.exports=s})();