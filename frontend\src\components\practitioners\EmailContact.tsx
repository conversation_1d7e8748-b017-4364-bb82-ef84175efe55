'use client';

import { FiMail } from 'react-icons/fi';

interface EmailContactProps {
  emailAddress: string | undefined;
}

const EmailContact = ({ emailAddress }: EmailContactProps) => {
  if (!emailAddress) {
    return null;
  }

  return (
    <div className="flex items-center text-gray-600">
      <FiMail className="mr-2 text-emerald-500" />
      <a
        href={`mailto:${emailAddress}`}
        className="text-emerald-600 hover:text-emerald-700"
      >
        {emailAddress}
      </a>
    </div>
  );
};

export default EmailContact;
