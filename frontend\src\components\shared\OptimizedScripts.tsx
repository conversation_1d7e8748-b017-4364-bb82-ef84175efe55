'use client';

import { useEffect } from 'react';
import Script from 'next/script';
import { getRegisteredScripts, ScriptStrategy } from '@/lib/scriptManager';

/**
 * Component to render all registered scripts with proper optimization
 */
export default function OptimizedScripts() {
  const scripts = getRegisteredScripts();
  
  return (
    <>
      {scripts.map((script) => {
        // Skip scripts without src or content
        if (!script.src && !script.content) {
          return null;
        }
        
        // Prepare additional attributes
        const attributes = script.attributes || {};
        
        if (script.content) {
          // Render inline script
          return (
            <Script
              key={script.id}
              id={script.id}
              strategy={script.strategy || ScriptStrategy.AFTER_INTERACTIVE}
              dangerouslySetInnerHTML={{ __html: script.content }}
              onLoad={script.onLoad}
              onError={script.onError ? 
                (e) => script.onError?.(new Error(`Failed to load script: ${script.id}`)) : 
                undefined
              }
              {...attributes}
            />
          );
        } else {
          // Render external script
          return (
            <Script
              key={script.id}
              id={script.id}
              src={script.src}
              strategy={script.strategy || ScriptStrategy.AFTER_INTERACTIVE}
              onLoad={script.onLoad}
              onError={script.onError ? 
                (e) => script.onError?.(new Error(`Failed to load script: ${script.id}`)) : 
                undefined
              }
              {...attributes}
            />
          );
        }
      })}
    </>
  );
}
