(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5425],{4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var n=r(2115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=n.createContext&&n.createContext(s),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,s,a;n=e,s=t,a=r[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in n?Object.defineProperty(n,s,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[s]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>n.createElement(m,i({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,o({key:r},t.attr),e(t.child)))}(e.child))}function m(e){var t=t=>{var r,{attr:s,size:a,title:c}=e,d=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,l),m=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,d,{className:r,style:o(o({color:e.color||t.color},t.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==a?n.createElement(a.Consumer,null,e=>t(e)):t(s)}},6772:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var n=r(5155),s=r(6874),a=r.n(s),l=r(351),i=r(2515);let c=e=>{var t,r;let{clinic:s,showContactInfo:c=!0,prefetchedData:o=!1}=e,d=o?{pathname:"/clinics/".concat(s.slug),query:{prefetched:"true"}}:"/clinics/".concat(s.slug);return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 flex-grow",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,n.jsx)(a(),{href:d,className:"hover:text-emerald-600",children:s.name})}),s.isVerified&&(0,n.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,n.jsx)(i.AI8,{color:"#009967",size:14}),(0,n.jsx)("span",{children:"VERIFIED"})]}),s.description&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:s.description}),(0,n.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[s.address&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(l.HzC,{className:"mr-2 text-emerald-500"}),(0,n.jsxs)("span",{children:[s.address.city,", ",s.address.stateProvince]})]}),c&&(null==(t=s.contactInfo)?void 0:t.phoneNumber)&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(l.QFc,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("span",{children:s.contactInfo.phoneNumber})]}),c&&(null==(r=s.contactInfo)?void 0:r.websiteUrl)&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(l.VeH,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("a",{href:s.contactInfo.websiteUrl,target:"_blank",rel:"nofollow noopener noreferrer",className:"hover:text-emerald-600",children:"Visit Website"})]})]})]}),(0,n.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,n.jsx)(a(),{href:d,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Details →"})})]})}},9348:(e,t,r)=>{Promise.resolve().then(r.bind(r,6772))}},e=>{var t=t=>e(e.s=t);e.O(0,[844,5479,6874,8441,1684,7358],()=>t(9348)),_N_E=e.O()}]);