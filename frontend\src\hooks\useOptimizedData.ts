'use client';

import { useState, useEffect, useCallback } from 'react';
import { AxiosRequestConfig } from 'axios';
import { fetchWithCache, clearCacheEntry } from '@/lib/apiOptimization';
import { useError } from '@/contexts/ErrorContext';

interface UseOptimizedDataOptions<T> {
  /**
   * API endpoint to fetch data from
   */
  endpoint: string;
  
  /**
   * Request parameters
   */
  params?: AxiosRequestConfig['params'];
  
  /**
   * Initial data to use before fetching
   */
  initialData?: T;
  
  /**
   * Whether to enable automatic fetching
   */
  enabled?: boolean;
  
  /**
   * Cache TTL in milliseconds
   */
  cacheTTL?: number;
  
  /**
   * Whether to bypass the cache
   */
  bypassCache?: boolean;
  
  /**
   * Callback on successful fetch
   */
  onSuccess?: (data: T) => void;
  
  /**
   * Callback on fetch error
   */
  onError?: (error: Error) => void;
  
  /**
   * Custom error message
   */
  errorMessage?: string;
  
  /**
   * Whether to retry on error
   */
  retryOnError?: boolean;
  
  /**
   * Delay between retries in milliseconds
   */
  retryDelay?: number;
  
  /**
   * Maximum number of retries
   */
  maxRetries?: number;
}

interface UseOptimizedDataResult<T> {
  /**
   * Fetched data
   */
  data: T | null;
  
  /**
   * Whether data is being loaded
   */
  isLoading: boolean;
  
  /**
   * Error if fetch failed
   */
  error: Error | null;
  
  /**
   * Function to manually refetch data
   */
  refetch: (bypassCache?: boolean) => Promise<void>;
  
  /**
   * Whether data is being refetched
   */
  isRefetching: boolean;
  
  /**
   * Function to invalidate the cache and refetch
   */
  invalidateCache: () => Promise<void>;
}

/**
 * A custom hook for fetching data with optimization
 * - Caches responses to reduce API calls
 * - Handles loading and error states
 * - Provides refetch and cache invalidation
 * - Supports retry on error
 */
function useOptimizedData<T>({
  endpoint,
  params,
  initialData = null as unknown as T,
  enabled = true,
  cacheTTL = 5 * 60 * 1000, // 5 minutes default
  bypassCache = false,
  onSuccess,
  onError,
  errorMessage = 'Failed to fetch data',
  retryOnError = false,
  retryDelay = 3000,
  maxRetries = 3,
}: UseOptimizedDataOptions<T>): UseOptimizedDataResult<T> {
  const [data, setData] = useState<T | null>(initialData);
  const [isLoading, setIsLoading] = useState<boolean>(enabled);
  const [error, setError] = useState<Error | null>(null);
  const [retryCount, setRetryCount] = useState<number>(0);
  const [isRefetching, setIsRefetching] = useState<boolean>(false);
  const { addErrorLog } = useError();
  
  const fetchData = useCallback(async (isRefetch = false, forceBypassCache = false) => {
    if (!endpoint) return;
    
    if (isRefetch) {
      setIsRefetching(true);
    } else if (!isLoading) {
      setIsLoading(true);
    }
    
    setError(null);
    
    try {
      const options: AxiosRequestConfig = {};
      if (params) {
        options.params = params;
      }
      
      const result = await fetchWithCache<T>(
        endpoint,
        options,
        cacheTTL,
        forceBypassCache || bypassCache
      );
      
      setData(result);
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      
      setError(error);
      addErrorLog(error, `useOptimizedData(${endpoint})`);
      
      if (onError) {
        onError(error);
      }
      
      // Handle retry logic
      if (retryOnError && retryCount < maxRetries) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          fetchData(isRefetch, forceBypassCache);
        }, retryDelay);
      }
    } finally {
      if (isRefetch) {
        setIsRefetching(false);
      } else {
        setIsLoading(false);
      }
    }
  }, [
    endpoint,
    params,
    cacheTTL,
    bypassCache,
    onSuccess,
    onError,
    retryOnError,
    retryCount,
    maxRetries,
    retryDelay,
    addErrorLog,
  ]);
  
  // Initial fetch when enabled
  useEffect(() => {
    if (enabled) {
      fetchData();
    }
  }, [enabled, fetchData]);
  
  // Reset retry count when endpoint or params change
  useEffect(() => {
    setRetryCount(0);
  }, [endpoint, params]);
  
  // Function to manually refetch data
  const refetch = useCallback(
    (forceBypassCache = false) => fetchData(true, forceBypassCache),
    [fetchData]
  );
  
  // Function to invalidate cache and refetch
  const invalidateCache = useCallback(async () => {
    clearCacheEntry(endpoint, params);
    await refetch(true);
  }, [endpoint, params, refetch]);
  
  return {
    data,
    isLoading,
    error,
    refetch,
    isRefetching,
    invalidateCache,
  };
}

export default useOptimizedData;
