/**
 * Test script for the ISR fix
 *
 * This script tests the ISR implementation by:
 * 1. Sending a revalidation request to the revalidation API
 * 2. Verifying that the revalidation API returns a success response
 * 3. Checking that the revalidated paths and tags are correct
 *
 * Usage:
 * node scripts/test-isr-fix.js
 */

// Use native fetch in Node.js 18+
require('dotenv').config();

// Get the revalidation token from environment variables
const token = process.env.PREVIEW_SECRET || process.env.REVALIDATE_TOKEN;
if (!token) {
  console.error('Error: No revalidation token found in environment variables.');
  console.error('Please set PREVIEW_SECRET or REVALIDATE_TOKEN in your .env file.');
  process.exit(1);
}

// Get the site URL from environment variables or use a default
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Construct the revalidation API URL
const revalidateUrl = `${siteUrl}/api/revalidate`;

// Log the URL being used
console.log(`Using site URL: ${siteUrl}`);
console.log(`Revalidation API URL: ${revalidateUrl}`);
console.log(`Revalidation token: ${token ? token.substring(0, 5) + '...' : 'Not found'}`);

// Create test payloads for different scenarios
const testPayloads = [
  // Test 1: Revalidate a blog post
  {
    name: 'Revalidate blog post',
    payload: {
      token,
      contentType: 'blog-post',
      id: 123,
      slug: 'test-blog-post'
    },
    headers: {
      'Content-Type': 'application/json'
    }
  },
  
  // Test 2: Revalidate a clinic
  {
    name: 'Revalidate clinic',
    payload: {
      token,
      contentType: 'clinic',
      id: 456,
      slug: 'test-clinic'
    },
    headers: {
      'Content-Type': 'application/json'
    }
  },
  
  // Test 3: Revalidate a practitioner
  {
    name: 'Revalidate practitioner',
    payload: {
      token,
      contentType: 'practitioner',
      id: 789,
      slug: 'test-practitioner'
    },
    headers: {
      'Content-Type': 'application/json'
    }
  },
  
  // Test 4: Revalidate everything
  {
    name: 'Revalidate everything',
    payload: {
      token
    },
    headers: {
      'Content-Type': 'application/json'
    }
  }
];

// Function to test a single payload
async function testPayload(test) {
  console.log(`\n🧪 Running test: ${test.name}`);
  console.log(`URL: ${revalidateUrl}`);
  console.log('Headers:', JSON.stringify(test.headers, null, 2));
  console.log('Payload:', JSON.stringify(test.payload, null, 2));

  try {
    const response = await fetch(revalidateUrl, {
      method: 'POST',
      headers: test.headers,
      body: JSON.stringify(test.payload),
    });

    const data = await response.json();

    if (response.ok) {
      console.log('✅ Test passed!');
      console.log('Status:', response.status);
      console.log('Response:', JSON.stringify(data, null, 2));
    } else {
      console.error('❌ Test failed!');
      console.error('Status:', response.status);
      console.error('Response:', JSON.stringify(data, null, 2));
    }

    return response.ok;
  } catch (error) {
    console.error('❌ Error testing revalidation API:', error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Testing ISR fix...');

  let passedTests = 0;

  for (const test of testPayloads) {
    const passed = await testPayload(test);
    if (passed) passedTests++;
  }

  console.log(`\n📊 Test results: ${passedTests}/${testPayloads.length} tests passed`);
}

runTests();
