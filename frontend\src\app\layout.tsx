import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Analytics } from "@vercel/analytics/next";
import { AuthProvider } from '@/contexts/AuthContext';
import { ErrorProvider } from '@/contexts/ErrorContext';
// Remove direct fetchFromApiServer import, use cached functions instead
import Layout from '@/components/layout/Layout'; // Import Layout component
import ReactQueryProvider from '@/providers/QueryProvider'; // Updated import path for React Query Provider
import { getGlobalSettings, getFooterCategories } from '@/lib/serverCache'; // Import cached functions
import { GoogleTagManager } from '@next/third-parties/google';

const inter = Inter({ subsets: ['latin'] });

// Define interfaces for Strapi data structures
interface StrapiMediaAttributes {
  id?: number | string;
  name?: string;
  alternativeText?: string | null;
  caption?: string | null;
  width?: number;
  height?: number;
  formats?: any; // Can be more specific if needed
  hash?: string;
  ext?: string;
  mime?: string;
  size?: number;
  url: string;
  previewUrl?: string | null;
  provider?: string;
  provider_metadata?: any;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string | null;
}

interface StrapiMedia {
  data?: {
    id: number | string;
    attributes: StrapiMediaAttributes;
  };
  // For v5 flattened media directly in the field
  id?: number | string;
  attributes?: StrapiMediaAttributes; // if it's a component still
  url?: string; // if it's truly flat
  // Add other direct media fields if Strapi v5 flattens them this way
  name?: string;
  alternativeText?: string | null;
  caption?: string | null;
  width?: number;
  height?: number;
  // ... other StrapiMediaAttributes
}


interface GlobalSettingsAttributes {
  siteName?: string;
  defaultSeoDescription?: string;
  favicon?: StrapiMedia; // Can be StrapiMedia or StrapiMediaAttributes if fully flat
  logoLight?: StrapiMedia; // Can be StrapiMedia or StrapiMediaAttributes if fully flat
  // other attributes...
}

interface CategoryAttributes {
  name: string;
  slug: string;
  // other attributes...
}

// Strapi API response types
// T is the type of the attributes object for a single item, or the flat item itself for v5
interface StrapiSingleResponse<T> {
  data: { id: number | string; attributes: T } | T | null; // Allow null for error cases or empty single types
  meta?: Record<string, any>;
}

interface StrapiCollectionResponse<T> {
  data: Array<{ id: number | string; attributes: T } | T> | null; // Allow null for error cases
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// Function to generate dynamic metadata
export async function generateMetadata(): Promise<Metadata> {
  let faviconUrl = '/favicon.ico';
  let siteTitle = 'Natural Healing Now - Holistic Health Directory';
  let siteDescription = 'Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.';
  const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL;

  try {
    // Fetch global settings using the cached function with longer cache time
    const globalSettingsResponse = await getGlobalSettings();

    if (globalSettingsResponse?.data) {
      let attributesData: GlobalSettingsAttributes | null = null;

      // Handle both Strapi v4 and v5 response formats
      if ('attributes' in globalSettingsResponse.data && globalSettingsResponse.data.attributes) {
        attributesData = globalSettingsResponse.data.attributes as GlobalSettingsAttributes;
      } else {
        attributesData = globalSettingsResponse.data as GlobalSettingsAttributes;
      }

      if (attributesData) {
        // Process favicon data
        const faviconField = attributesData.favicon;
        const faviconActualMedia = faviconField?.data?.attributes ?? faviconField?.attributes ?? faviconField;

        if (faviconActualMedia?.url) {
          const faviconPath = faviconActualMedia.url;
          if (faviconPath && strapiBaseUrl) {
            faviconUrl = faviconPath.startsWith('/') ? `${strapiBaseUrl}${faviconPath}` : faviconPath;
          }
        }

        // Set title and description from global settings
        siteTitle = attributesData.siteName || siteTitle;
        siteDescription = attributesData.defaultSeoDescription || siteDescription;
      }
    }
  } catch (error) {
    console.error("Failed to fetch global settings for metadata:", error);
    // Use default values if the fetch fails
  }

  return {
    title: siteTitle,
    description: siteDescription,
    icons: { icon: faviconUrl, shortcut: faviconUrl, apple: faviconUrl },
  };
}

// Make the component async to fetch data
export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  let siteName = 'Natural Healing Now';
  let logoLight = null;
  let footerCategories: any[] = [];

  try {
    // Fetch global settings and footer categories in parallel using cached functions
    // These functions now use longer cache times (24 hours for global settings, 1 week for categories)
    // and explicit 'force-cache' option for Next.js 15 compatibility
    const [globalSettingsResponse, categoriesResponse] = await Promise.all([
      getGlobalSettings(),
      getFooterCategories()
    ]);

    // Process global settings
    if (globalSettingsResponse?.data) {
      let attributesData: GlobalSettingsAttributes | null = null;

      // Handle both Strapi v4 and v5 response formats
      if ('attributes' in globalSettingsResponse.data && globalSettingsResponse.data.attributes) {
        attributesData = globalSettingsResponse.data.attributes as GlobalSettingsAttributes;
      } else {
        attributesData = globalSettingsResponse.data as GlobalSettingsAttributes;
      }

      if (attributesData) {
        // Set site name from global settings
        siteName = attributesData.siteName || siteName;

        // Process logo data
        const logoField = attributesData.logoLight;
        const logoActualMedia = logoField?.data?.attributes ?? logoField?.attributes ?? logoField;

        if (logoActualMedia?.url) {
          const mediaAttrs = logoActualMedia as StrapiMediaAttributes;

          // Parse ID safely
          let parsedId: number | undefined =
            typeof mediaAttrs.id === 'string' ? parseInt(mediaAttrs.id, 10) :
            typeof mediaAttrs.id === 'number' ? mediaAttrs.id :
            undefined;

          if (parsedId !== undefined && isNaN(parsedId)) parsedId = undefined;

          // Create logo object with all required properties
          logoLight = {
            id: parsedId || 0,
            name: mediaAttrs.name || '',
            alternativeText: mediaAttrs.alternativeText || siteName,
            caption: mediaAttrs.caption,
            width: mediaAttrs.width,
            height: mediaAttrs.height,
            formats: mediaAttrs.formats,
            hash: mediaAttrs.hash || '',
            ext: mediaAttrs.ext || '',
            mime: mediaAttrs.mime || '',
            size: mediaAttrs.size || 0,
            url: mediaAttrs.url,
            previewUrl: mediaAttrs.previewUrl,
            provider: mediaAttrs.provider || 'local',
            provider_metadata: mediaAttrs.provider_metadata,
            createdAt: mediaAttrs.createdAt || '',
            updatedAt: mediaAttrs.updatedAt || '',
            publishedAt: mediaAttrs.publishedAt === null ? undefined : mediaAttrs.publishedAt,
          };
        }
      }
    }

    // Process categories for footer
    if (categoriesResponse?.data && Array.isArray(categoriesResponse.data)) {
      footerCategories = categoriesResponse.data
        .map((catEntry: { id: string | number; attributes: CategoryAttributes } | CategoryAttributes) => {
          // Handle both Strapi v4 and v5 response formats
          let catAttributes: CategoryAttributes;
          let catId: string | number;

          if ('attributes' in catEntry && catEntry.attributes) {
            catAttributes = catEntry.attributes as CategoryAttributes;
            catId = catEntry.id;
          } else {
            catAttributes = catEntry as CategoryAttributes;
            catId = (catEntry as any).id;
          }

          return {
            id: catId,
            attributes: {
              name: catAttributes.name || 'Unknown Category',
              slug: catAttributes.slug || 'unknown-category',
            },
          };
        })
        .filter(Boolean);
    } else if (categoriesResponse === null) {
      console.warn("Received null for categoriesResponse in RootLayout, likely due to fetch error.");
    }
  } catch (error) {
    console.error("Failed to fetch initial data in RootLayout:", error);
    // Use default values if the fetch fails
  }

  return (
    <html lang="en">
      {process.env.NEXT_PUBLIC_GTM_ID && (
        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID} />
      )}
      <body className={inter.className}>
        <ErrorProvider>
          <AuthProvider>
            <ReactQueryProvider>
              {/* Wrap children with Layout and pass fetched data, including footerCategories */}
              <Layout siteName={siteName} logoLight={logoLight} footerCategories={footerCategories}>
                {children}
              </Layout>
            </ReactQueryProvider>
          </AuthProvider>
        </ErrorProvider>
        <Analytics />
      </body>
    </html>
  );
}
