{"kind": "collectionType", "collectionName": "conditions", "info": {"singularName": "condition", "pluralName": "conditions", "displayName": "Condition Treated"}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "richtext"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "clinics": {"type": "relation", "relation": "manyToMany", "target": "api::clinic.clinic", "mappedBy": "conditions"}, "practitioners": {"type": "relation", "relation": "manyToMany", "target": "api::practitioner.practitioner", "mappedBy": "conditions"}}}