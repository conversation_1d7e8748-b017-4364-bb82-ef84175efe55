"use client";

import LazyImage from '@/components/shared/LazyImage';
import Link from 'next/link';
import { format } from 'date-fns';
import { FiUser, <PERSON>Clock, FiShare2, FiBookmark } from 'react-icons/fi'; // Import icons
import { sanitizeUrl } from '@/lib/mediaUtils';

interface BlogPostCardProps {
  post: {
    id: string;
    title: string;
    slug: string;
    excerpt?: string | null;
    featured_image?: string | null;
    publish_date: string;
    content?: string;
    reading_time?: number; // Added reading_time field
    isFeatured?: boolean;
    view_count?: number;
    author?: {
      name: string;
      slug: string;
      profile_picture?: string | null;
    } | null;
  };
  showReadingTime?: boolean;
  showShareButton?: boolean;
  showBadge?: boolean;
}

const BlogPostCard = ({ post, showReadingTime = false, showShareButton = false, showBadge = false }: BlogPostCardProps) => {
  // Use the imported sanitizeUrl function from mediaUtils

  // Sanitize image URLs and log in development
  const imageSrc = sanitizeUrl(post.featured_image);
  const hasImage = !!post.featured_image;
  const authorImageSrc = sanitizeUrl(post.author?.profile_picture);
  const hasAuthorImage = !!post.author?.profile_picture;

  // Debug image URLs in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`BlogPostCard image URLs for "${post.title}":`, {
      original: { featured: post.featured_image, author: post.author?.profile_picture },
      sanitized: { featured: imageSrc, author: authorImageSrc }
    });
  }

  // Use pre-calculated reading time if available, otherwise calculate it
  const calculateReadingTime = (content: string): number => {
    const wordsPerMinute = 200;
    const wordCount = content?.split(/\s+/)?.length || 0;
    return Math.max(1, Math.ceil(wordCount / wordsPerMinute));
  };

  // Use pre-calculated reading time if available, otherwise calculate from content or default to 2 min
  const readingTime = post.reading_time || (post.content ? calculateReadingTime(post.content) : 2);

  // Ensure authorImageSrc is a valid URL
  const validAuthorImageSrc = authorImageSrc && (
    authorImageSrc.startsWith('http') ||
    authorImageSrc.startsWith('/') ||
    authorImageSrc.startsWith('data:') ||
    // Handle relative URLs that might not start with a slash
    (typeof window !== 'undefined' && window.location.origin && !authorImageSrc.startsWith('http'))
  ) ? authorImageSrc : '';

  // Always debug author image URL in development to help troubleshoot
  if (process.env.NODE_ENV === 'development') {
    console.log(`Author image for "${post.title}":`, {
      author: post.author,
      original: post.author?.profile_picture,
      processed: authorImageSrc,
      valid: validAuthorImageSrc,
      hasAuthorImage: hasAuthorImage
    });
  }

  // Additional check to ensure the URL is valid
  const isValidUrl = (url: string) => {
    try {
      return url && (
        url.startsWith('http') ||
        url.startsWith('/') ||
        url.startsWith('data:')
      );
    } catch (e) {
      return false;
    }
  };

  // Format the publish date
  const formattedDate = format(new Date(post.publish_date), 'MMMM d, yyyy');

  // Handle share functionality
  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();

    // Check if navigator.share is available (mostly on mobile devices)
    if (navigator.share) {
      navigator.share({
        title: post.title,
        text: post.excerpt || '',
        url: `${window.location.origin}/blog/${post.slug}`,
      }).catch(err => {
        console.error('Error sharing:', err);
      });
    } else {
      // Fallback: copy link to clipboard
      navigator.clipboard.writeText(`${window.location.origin}/blog/${post.slug}`)
        .then(() => {
          alert('Link copied to clipboard!');
        })
        .catch(err => {
          console.error('Could not copy text: ', err);
        });
    }
  };

  // Get the view_count safely (it might be a non-enumerable property)
  const viewCount = Object.getOwnPropertyDescriptor(post, 'view_count')?.value || 0;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col">
      <Link href={`/blog/${post.slug}`} className="block relative h-48 w-full overflow-hidden"> {/* Added overflow-hidden */}
        {hasImage ? (
          <LazyImage
            src={imageSrc}
            alt={post.title || 'Blog post image'}
            width={600} // Used for aspect ratio hint
            height={400} // Used for aspect ratio hint
            fillContainer={true}
            className="object-cover" // w-full h-full handled by fillContainer
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={false}
            showPlaceholder={true}
            advancedBlur={true}
            fadeIn={true}
            preload={post.isFeatured || viewCount > 10} // Preload featured or popular posts
          />
        ) : (
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 to-teal-100 flex items-center justify-center">
            {/* Placeholder Icon or Text */}
             <span className="text-emerald-700 font-semibold text-xl opacity-50">
               {post.title.charAt(0)}
             </span>
          </div>
        )}

        {/* Badge - only show if showBadge is true and post has a badge type */}
        {showBadge && (post.isFeatured === true || viewCount > 0) && (
          <div className={`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ${
            post.isFeatured === true ? 'bg-emerald-600 text-white' : 'bg-amber-500 text-white'
          }`}>
            {post.isFeatured === true ? 'Featured Post' : 'Popular Post'}
          </div>
        )}
      </Link>

      <div className="p-4 flex-grow flex flex-col">
        <div className="flex justify-between items-center mb-2">
          <div className="text-sm text-gray-500">{formattedDate}</div>
          {showReadingTime && (
            <div className="flex items-center text-gray-500 text-sm">
              <FiClock className="mr-1" />
              <span>{readingTime} min read</span>
            </div>
          )}
        </div>

        <h3 className="text-xl font-semibold mb-2 text-gray-800 flex-grow">
          <Link href={`/blog/${post.slug}`} className="hover:text-emerald-600 line-clamp-2">
            {post.title}
          </Link>
        </h3>

        {post.excerpt && (
          <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>
        )}

        {post.author && (
          <div className="flex items-center mt-auto pt-4 border-t border-gray-100"> {/* Added border for separation */}
            <div className="relative h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-200 shadow-sm"> {/* Increased size and added border */}
              {validAuthorImageSrc && isValidUrl(validAuthorImageSrc) ? (
                <LazyImage
                  src={validAuthorImageSrc}
                  alt={post.author.name || 'Author image'}
                  width={40} // Used for aspect ratio hint
                  height={40} // Used for aspect ratio hint
                  fillContainer={true}
                  className="object-cover rounded-full" // fillContainer will make it fill the parent div
                  sizes="40px" // Still useful for next/image optimization
                  showPlaceholder={true}
                  fadeIn={true}
                />
              ) : (
                 <div className="absolute inset-0 bg-emerald-100 flex items-center justify-center">
                   <FiUser className="text-emerald-700 text-lg" />
                   {/* Increased icon size */}
                 </div>
              )}
            </div>
            <div className="text-sm">
              <span className="block text-xs text-gray-500 mb-0.5">Written by</span>
              {/* Added "Written by" text */}
              <Link
                href={`/blog/authors/${post.author.slug}`}
                className="font-medium text-gray-800 hover:text-emerald-600"
              >
                {post.author.name}
              </Link>
            </div>
          </div>
        )}
      </div>

      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto flex justify-between items-center">
        <Link
          href={`/blog/${post.slug}`}
          className="text-emerald-600 hover:text-emerald-700 font-medium text-sm"
        >
          Read More →
        </Link>

        {showShareButton && (
          <div className="flex space-x-2">
            <button
              className="text-gray-400 hover:text-emerald-600 p-1 transition-colors"
              onClick={handleShare}
              aria-label="Share article"
            >
              <FiShare2 size={18} />
            </button>
            <button
              className="text-gray-400 hover:text-emerald-600 p-1 transition-colors"
              aria-label="Save article"
            >
              <FiBookmark size={18} />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default BlogPostCard;
