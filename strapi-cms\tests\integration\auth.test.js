const request = require('supertest');

// User mock data
const mockUserData = {
  username: "tester",
  email: "<EMAIL>",
  provider: "local",
  password: "1234abc",
  confirmed: true,
  blocked: null,
};

describe('Authentication API', () => {
  let jwt;
  let userId;

  // Clean up after all tests
  afterAll(async () => {
    // Delete the test user if it exists
    if (userId) {
      await strapi.plugins['users-permissions'].services.user.deleteOne({ id: userId });
    }
  });

  it("should register a new user", async () => {
    const response = await request(strapi.server.httpServer)
      .post("/api/auth/local/register")
      .set("accept", "application/json")
      .set("Content-Type", "application/json")
      .send({
        username: mockUserData.username,
        email: mockUserData.email,
        password: mockUserData.password,
      });

    expect(response.status).toBe(200);
    expect(response.body.jwt).toBeDefined();
    expect(response.body.user).toBeDefined();
    expect(response.body.user.email).toBe(mockUserData.email);
    
    // Save user ID for cleanup
    userId = response.body.user.id;
    
    // Save JWT for later tests
    jwt = response.body.jwt;
  });

  it("should login user and return jwt token", async () => {
    const response = await request(strapi.server.httpServer)
      .post("/api/auth/local")
      .set("accept", "application/json")
      .set("Content-Type", "application/json")
      .send({
        identifier: mockUserData.email,
        password: mockUserData.password,
      });

    expect(response.status).toBe(200);
    expect(response.body.jwt).toBeDefined();
    expect(response.body.user).toBeDefined();
    expect(response.body.user.email).toBe(mockUserData.email);
  });

  it('should return user data for authenticated user', async () => {
    if (!jwt) {
      throw new Error('JWT not available from previous test');
    }

    const response = await request(strapi.server.httpServer)
      .get('/api/users/me')
      .set('accept', 'application/json')
      .set('Content-Type', 'application/json')
      .set('Authorization', `Bearer ${jwt}`);

    expect(response.status).toBe(200);
    expect(response.body).toBeDefined();
    expect(response.body.id).toBe(userId);
    expect(response.body.username).toBe(mockUserData.username);
    expect(response.body.email).toBe(mockUserData.email);
  });

  it('should not allow access to protected routes without authentication', async () => {
    const response = await request(strapi.server.httpServer)
      .get('/api/users/me')
      .set('accept', 'application/json');

    expect(response.status).toBe(401);
  });
});
