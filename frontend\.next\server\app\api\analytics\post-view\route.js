(()=>{var e={};e.id=1299,e.ids=[1299],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return s},throwWithStaticGenerationBailoutError:function(){return o},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return a}});let n=r(80023),i=r(3295);function o(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function a(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function s(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function c(){let e=i.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15650:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var n={};r.r(n),r.d(n,{POST:()=>l});var i=r(96559),o=r(48088),a=r(37719),s=r(32190),c=r(44999),u=r(36463);async function l(e){try{let{postId:t,postSlug:r}=await e.json();if(!t||!r)return s.NextResponse.json({error:"Missing required fields: postId and postSlug"},{status:400});let n=(0,c.UL)(),i=n.get("visitor_id")?.value||`visitor_${Math.random().toString(36).substring(2,15)}`;n.get("visitor_id")||n.set("visitor_id",i,{maxAge:31536e3,path:"/",sameSite:"strict",secure:!0,httpOnly:!0});let o=e.headers.get("referer")||null,a=process.env.STRAPI_API_TOKEN;if(!a)return u.Ay.error("STRAPI_API_TOKEN environment variable is not set"),s.NextResponse.json({error:"Server configuration error"},{status:500});let l="https://nice-badge-2130241d6c.strapiapp.com",d=await fetch(`${l}/api/post-views?filters[visitorId][$eq]=${i}&filters[post][id][$eq]=${t}&filters[timestamp][$gt]=${new Date(Date.now()-864e5).toISOString()}`,{headers:{Authorization:`Bearer ${a}`}});if(!d.ok)throw u.Ay.error("Error checking for existing views:",await d.text()),Error(`Failed to check for existing views: ${d.statusText}`);let p=await d.json();if(p.data&&p.data.length>0)return s.NextResponse.json({success:!0,message:"View already recorded within the last 24 hours"});let f=await fetch(`${l}/api/post-views`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify({data:{post:t,visitorId:i,referrer:o,timestamp:new Date().toISOString()}})});if(!f.ok)throw u.Ay.error("Error recording view:",await f.text()),Error(`Failed to record view: ${f.statusText}`);return u.Ay.info(`Post view recorded: ${t} (${r}) by ${i}`),s.NextResponse.json({success:!0,message:"View recorded successfully"})}catch(e){return u.Ay.error("Error tracking post view:",e),s.NextResponse.json({error:"Failed to track post view"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/analytics/post-view/route",pathname:"/api/analytics/post-view",filename:"route",bundlePath:"app/api/analytics/post-view/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\analytics\\post-view\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:g}=d;function y(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36463:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a});var n=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let i={enabled:!1,level:"info",prefix:"[NHN]"};function o(e,t,...r){if(!i.enabled)return;let a=Object.values(n),s=a.indexOf(i.level);if(a.indexOf(e)>=s){let n=i.prefix?`${i.prefix} `:"",o=`${n}${t}`;switch(e){case"debug":console.debug(o,...r);break;case"info":console.info(o,...r);break;case"warn":console.warn(o,...r);break;case"error":console.error(o,...r)}}}let a={debug:function(e,...t){o("debug",e,...t)},info:function(e,...t){o("info",e,...t)},warn:function(e,...t){o("warn",e,...t)},error:function(e,...t){o("error",e,...t)},configure:function(e){i={...i,...e}}}},43763:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return o}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function i(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let o=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(61120));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let o={current:null},a="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}a(e=>{try{s(o.current)}finally{o.current=null}})},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,580,4999],()=>r(15650));module.exports=n})();