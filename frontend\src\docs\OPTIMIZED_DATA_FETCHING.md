# Optimized Data Fetching from Strapi

This document outlines the strategies and utilities implemented to optimize data fetching from Strapi in our Next.js 15 application.

## Table of Contents

1. [Overview](#overview)
2. [Server-Side Data Fetching](#server-side-data-fetching)
3. [Client-Side Data Fetching](#client-side-data-fetching)
4. [Caching Strategies](#caching-strategies)
5. [Query Optimization](#query-optimization)
6. [Batch Requests](#batch-requests)
7. [Best Practices](#best-practices)

## Overview

Our application uses several strategies to reduce the number of API calls to Strapi:

1. **Server Components with Next.js Cache**: Leveraging React Server Components and Next.js built-in caching
2. **React Query for Client Components**: Using React Query for client-side data fetching with optimized caching
3. **Request Deduplication**: Preventing duplicate requests for the same data
4. **Query Optimization**: Requesting only the data we need
5. **Batch Requests**: Combining multiple requests into fewer API calls
6. **Content-Type Specific Caching**: Different cache durations based on content type

## Server-Side Data Fetching

For server components, use the utilities in `serverFetch.ts` and `serverActions.ts`:

```tsx
import { getGlobalSettings, getCategories, getClinics } from '@/lib/serverActions';

export default async function MyPage() {
  // These calls are automatically cached and deduplicated
  const globalSettings = await getGlobalSettings();
  const categories = await getCategories(5);
  const clinics = await getClinics({ featured: true });
  
  return (
    <div>
      {/* Render your data */}
    </div>
  );
}
```

Benefits:
- Automatic caching with Next.js Data Cache
- Deduplication of requests within the same render
- Cache invalidation with tags
- Suspense support for streaming

## Client-Side Data Fetching

For client components, use the hooks in `useStrapiData.ts`:

```tsx
'use client';

import { useFeaturedClinics, useCategories } from '@/hooks/useStrapiData';

export default function MyClientComponent() {
  const { data: clinics, isLoading, error } = useFeaturedClinics();
  const { data: categories } = useCategories(5);
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      {/* Render your data */}
    </div>
  );
}
```

Benefits:
- Automatic caching with React Query
- Deduplication of requests
- Stale-while-revalidate pattern
- Loading and error states
- Automatic refetching on window focus

## Caching Strategies

We use different caching strategies based on content type:

| Content Type | Stale Time | Cache Time | Notes |
|--------------|------------|------------|-------|
| Global Settings | 12 hours | 24 hours | Rarely changes |
| Categories | 6 hours | 12 hours | Semi-static |
| Specialties | 6 hours | 12 hours | Semi-static |
| Clinics | 3 hours | 6 hours | Changes occasionally |
| Practitioners | 3 hours | 6 hours | Changes occasionally |
| Blog Posts | 30 minutes | 1 hour | Changes frequently |

You can customize these in `reactQuery.ts`.

## Query Optimization

Use the utilities in `strapiQueryBuilder.ts` to optimize your queries:

```tsx
import { 
  selectFields, 
  populateRelations, 
  paginate, 
  filterEquals,
  mergeParams
} from '@/lib/strapiQueryBuilder';

// Build optimized query parameters
const params = mergeParams(
  selectFields(['name', 'slug', 'description']),
  populateRelations({
    featuredImage: true,
    categories: {
      fields: ['name', 'slug']
    }
  }),
  paginate(1, 10),
  filterEquals('isFeatured', true)
);

// Use with any data fetching utility
const data = await fetchContentType('clinics', { params });
```

This reduces the amount of data transferred by only requesting what you need.

## Batch Requests

For fetching multiple related items, use the batch utilities:

```tsx
import { batchRequestsParallel, batchRequestsByContentType } from '@/lib/batchFetch';

// Fetch multiple endpoints in parallel
const [globalSettings, categories, specialties] = await batchRequestsParallel([
  { endpoint: '/global-setting', params: { populate: '*' } },
  { endpoint: '/categories', params: { pagination: { pageSize: 10 } } },
  { endpoint: '/specialties', params: { pagination: { pageSize: 10 } } }
]);

// Or batch requests for the same content type
const clinics = await batchRequestsByContentType(
  'clinics',
  [1, 2, 3, 4, 5], // IDs to fetch
  ['name', 'slug', 'description'], // Fields to select
  { featuredImage: true } // Relations to populate
);
```

## Best Practices

1. **Use Server Components When Possible**: They provide the best caching with Next.js.

2. **Be Specific with Your Queries**: Only request the fields and relations you need.

3. **Use Content-Type Specific Hooks**: They have optimized cache settings.

4. **Prefetch Data for Common Navigation Paths**: Use `prefetchQuery` for anticipated user journeys.

5. **Implement Proper Cache Invalidation**: Use `revalidateTag` or `revalidatePath` when data changes.

6. **Use Suspense for Loading States**: It provides a better user experience with streaming.

7. **Monitor API Usage**: Regularly check your API usage to identify optimization opportunities.

8. **Consider SSG for Static Pages**: Use static generation for pages that don't need fresh data.

9. **Use Incremental Static Regeneration**: For pages that need fresh data but can be cached.

10. **Implement Error Boundaries**: Handle API errors gracefully.

By following these practices, you can significantly reduce the number of API calls to Strapi while maintaining a responsive user experience.
