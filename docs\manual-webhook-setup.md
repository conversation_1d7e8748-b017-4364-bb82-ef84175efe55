# Manual Webhook Setup for Strapi

This guide will help you manually set up webhooks in Strapi to trigger on-demand revalidation in your Next.js application.

## Prerequisites

- Access to the Strapi admin panel
- The revalidation token: `3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
- The Next.js site URL: `https://www.naturalhealingnow.com`

## Step 1: Access the Webhooks Section

1. Log in to your Strapi admin panel at `https://nice-badge-2130241d6c.strapiapp.com/admin`
2. Navigate to Settings > Webhooks

## Step 2: Configure Webhooks for Each Content Type

You need to create a webhook for each content type that should trigger revalidation. Here are the configurations for each:

### Blog Posts Webhook

1. **Name**: `Revalidate Blog Posts`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "blog-post" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "blog-post",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

### Practitioners Webhook

1. **Name**: `Revalidate Practitioners`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "practitioner" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "practitioner",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

### Clinics Webhook

1. **Name**: `Revalidate Clinics`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "clinic" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "clinic",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

### Categories Webhook

1. **Name**: `Revalidate Categories`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "category" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "category",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

### Specialties Webhook

1. **Name**: `Revalidate Specialties`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "specialty" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "specialty",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

## Step 3: Test the Webhooks

After configuring the webhooks, you should test them to ensure they're working correctly:

1. Make a change to a content item in Strapi (e.g., update a blog post)
2. Publish the change
3. Check the Strapi webhook logs to ensure the webhook was triggered
4. Check the Vercel logs to ensure the revalidation API was called
5. Visit the page on your site to ensure the change is reflected

## Troubleshooting

If the webhooks aren't working as expected, check the following:

1. **Webhook URL**: Make sure the webhook URL is correct and accessible from the internet.
2. **Token**: Verify that the token in the request body matches the token in your Next.js environment variables.
3. **Content Type**: Ensure the content type in the request body matches the content type in Strapi.
4. **Events**: Make sure the correct events are selected for each webhook.
5. **Request Body**: Verify that the request body is correctly formatted JSON.

## Alternative: Using the Authorization Header

Instead of including the token in the request body, you can also use the Authorization header:

1. **Name**: `Revalidate Specialties`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
   - `Authorization`: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
4. **Events**: (same as above)
5. **Request body**:
   ```json
   {
     "contentType": "specialty",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

This approach is more secure as it follows standard authentication practices.
