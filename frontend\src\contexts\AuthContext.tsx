"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { strapiAuth, StrapiUser } from '@/lib/strapiAuth';
import { useRouter } from 'next/navigation';

type User = StrapiUser | null;

type AuthContextType = {
  user: User;
  isLoading: boolean;
  signIn: (identifier: string, password: string) => Promise<{ error: any }>;
  signUp: (username: string, email: string, password: string) => Promise<{ error: any }>;
  signOut: () => { error: any };
  forgotPassword: (email: string) => Promise<{ error: any }>;
  resetPassword: (code: string, password: string, passwordConfirmation: string) => Promise<{ error: any }>;
  isAuthenticated: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check for active session on initial load
    const initializeAuth = async () => {
      setIsLoading(true);

      try {
        // Get user from localStorage (set during login)
        const { user } = strapiAuth.getCurrentUser();
        setUser(user);
        setIsAuthenticated(!!user);
      } catch (error) {
        console.error('Error loading user:', error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    // Add event listener for storage changes (for multi-tab support)
    const handleStorageChange = () => {
      const { user } = strapiAuth.getCurrentUser();
      setUser(user);
      setIsAuthenticated(!!user);
      router.refresh();
    };

    window.addEventListener('storage', handleStorageChange);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [router]);

  const signIn = async (identifier: string, password: string) => {
    setIsLoading(true);
    const { data, error } = await strapiAuth.login(identifier, password);

    if (data) {
      setUser(data.user);
      setIsAuthenticated(true);
      router.refresh();
    }

    setIsLoading(false);
    return { error };
  };

  const signUp = async (username: string, email: string, password: string) => {
    setIsLoading(true);
    const { data, error } = await strapiAuth.register(username, email, password);

    if (data) {
      setUser(data.user);
      setIsAuthenticated(true);
      router.refresh();
    }

    setIsLoading(false);
    return { error };
  };

  const signOut = () => {
    setIsLoading(true);
    const { error } = strapiAuth.logout();
    setUser(null);
    setIsAuthenticated(false);
    router.refresh();
    setIsLoading(false);
    return { error };
  };

  const forgotPassword = async (email: string) => {
    setIsLoading(true);
    const { error } = await strapiAuth.forgotPassword(email);
    setIsLoading(false);
    return { error };
  };

  const resetPassword = async (code: string, password: string, passwordConfirmation: string) => {
    setIsLoading(true);
    const { data, error } = await strapiAuth.resetPassword(code, password, passwordConfirmation);

    if (data) {
      setUser(data.user);
      setIsAuthenticated(true);
      router.refresh();
    }

    setIsLoading(false);
    return { error };
  };

  const value = {
    user,
    isLoading,
    signIn,
    signUp,
    signOut,
    forgotPassword,
    resetPassword,
    isAuthenticated
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
