import { getStrapiContent } from '@/lib/strapi';
import MarkdownContent from '@/components/blog/MarkdownContent';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

// Define the expected shape of the data (fields directly under data)
interface AffiliateDisclosureData {
  id: number;
  title?: string; // Title might be in seo.metaTitle, making this optional
  content: string;
  seo?: any; // Using 'any' for flexibility, refine if SEO structure is known
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

interface StrapiSingleResponse<T> {
  data: T | null;
  meta: object;
}

// Function to generate metadata
export async function generateMetadata(): Promise<Metadata> {
  const defaultTitle = 'Affiliate Disclosure';
  const defaultDescription = 'Read our Affiliate Disclosure.';

  try {
    const response: StrapiSingleResponse<AffiliateDisclosureData> = await getStrapiContent.affiliateDisclosure.get();
    const pageData = response?.data;

    if (!pageData) {
      return { title: defaultTitle, description: defaultDescription };
    }

    const seoData = pageData?.seo;
    const title = seoData?.metaTitle || pageData?.title || defaultTitle;
    const description = seoData?.metaDescription || defaultDescription;

    return {
      title: title,
      description: description,
      ...(seoData?.canonicalURL && { alternates: { canonical: seoData.canonicalURL } }),
      ...(seoData?.metaRobots && { robots: seoData.metaRobots }),
    };
  } catch (error) {
    console.error('Error fetching Affiliate Disclosure metadata:', error);
    return { title: defaultTitle, description: 'Error loading page information.' };
  }
}

// The page component
export default async function AffiliateDisclosurePage() {
  let pageData: AffiliateDisclosureData | null = null;

  try {
    const rawResponse = await getStrapiContent.affiliateDisclosure.get();
    pageData = rawResponse?.data;
  } catch (error) {
    console.error('Failed to fetch Affiliate Disclosure page data:', error);
  }

  if (!pageData) {
     console.error('Affiliate Disclosure data object is null or undefined after fetch.');
     notFound();
  }

  if (!pageData.content) {
     console.error('Affiliate Disclosure data fetched, but content field is missing or empty.');
     // Allow rendering with a message if content is missing
  }

  const title = pageData.seo?.metaTitle || pageData.title || 'Affiliate Disclosure';
  const content = pageData.content;

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Title is handled by metadata, not shown in body */}
      <div className="prose lg:prose-xl max-w-none mx-auto">
        {content ? (
          <MarkdownContent content={content} />
        ) : (
          <p>Affiliate Disclosure content is not available at the moment.</p>
        )}
      </div>
    </div>
  );
}
