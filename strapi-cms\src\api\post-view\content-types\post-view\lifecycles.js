'use strict';

/**
 * Lifecycle hooks for the Post View content type
 */

module.exports = {
  /**
   * Before creating a post view, set the timestamp if not provided
   */
  beforeCreate(event) {
    const { data } = event.params;
    
    // Set timestamp to current date if not provided
    if (!data.timestamp) {
      data.timestamp = new Date().toISOString();
    }
    
    // Log the post view creation
    console.log(`Creating post view for post ID ${data.post} by visitor ${data.visitorId}`);
  },
};
