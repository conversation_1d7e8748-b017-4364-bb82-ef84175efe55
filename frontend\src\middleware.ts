import { NextRequest, NextResponse } from 'next/server';

/**
 * Optimized middleware for Next.js that enhances performance through:
 * 1. Improved static asset caching with appropriate Cache-Control headers
 * 2. Content security improvements
 * 3. Static path handling
 */
export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const response = NextResponse.next();
  
  // Add security headers to all responses
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // Enhanced cache control headers for static assets to improve performance
  if (isStaticAsset(pathname)) {
    const cacheSettings = getCacheControlForPath(pathname);
    response.headers.set('Cache-Control', cacheSettings);
  }
  
  // Special handling for image optimization API routes
  if (pathname.startsWith('/_next/image')) {
    // Extend cache duration for improved performance
    // This is important as image optimization is a CPU-intensive task
    response.headers.set('Cache-Control', 'public, max-age=86400, immutable');
  }
  
  return response;
}

/**
 * Determine if a path is a static asset
 */
function isStaticAsset(pathname: string): boolean {
  return (
    // Next.js static files
    pathname.startsWith('/_next/static') ||
    // Public folder assets
    pathname.startsWith('/images/') ||
    pathname.endsWith('.svg') ||
    pathname.endsWith('.png') ||
    pathname.endsWith('.jpg') ||
    pathname.endsWith('.jpeg') ||
    pathname.endsWith('.gif') ||
    pathname.endsWith('.ico') ||
    pathname.endsWith('.woff') ||
    pathname.endsWith('.woff2') ||
    pathname.endsWith('.ttf') ||
    pathname.endsWith('.otf')
  );
}

/**
 * Get appropriate Cache-Control header value based on the path
 */
function getCacheControlForPath(pathname: string): string {
  // JavaScript and CSS bundles - longer cache since they have content hashed filenames
  if (pathname.match(/\/_next\/static\/(chunks|css)\/.+/)) {
    return 'public, max-age=31536000, immutable'; // 1 year
  }
  
  // Next.js build assets with hashed filenames
  if (pathname.match(/\/_next\/static\/.+\.[a-z0-9]+\.(js|css)$/)) {
    return 'public, max-age=31536000, immutable'; // 1 year
  }
  
  // Font files - rarely change
  if (pathname.match(/\.(woff|woff2|ttf|otf)$/)) {
    return 'public, max-age=31536000, immutable'; // 1 year
  }
  
  // Images in public folder
  if (pathname.match(/\/images\/.+\.(jpg|jpeg|png|gif|svg|webp|avif)$/)) {
    return 'public, max-age=604800, stale-while-revalidate=86400'; // 1 week, stale for 1 day
  }
  
  // Other static assets
  if (pathname.match(/\.(ico|svg|jpg|jpeg|png|gif|webp|avif)$/)) {
    return 'public, max-age=86400, stale-while-revalidate=3600'; // 1 day, stale for 1 hour
  }
  
  // Default for other static assets
  return 'public, max-age=3600'; // 1 hour
}

// Only run middleware on specific paths
export const config = {
  matcher: [
    // Run on all paths except for specific ones
    '/((?!api|_next/static|_next/image|favicon.ico).*)'
  ],
};
