# Frontend Testing Guide

This guide explains how to run tests for the Next.js frontend.

## Prerequisites

Before running tests, make sure you have:

1. Installed all dependencies with `npm install`
2. For E2E tests, installed Playwright browsers with `npx playwright install`

## Running Tests

### Unit and Component Tests

To run unit and component tests:

```bash
npm test
```

To run tests in watch mode:

```bash
npm run test:watch
```

To generate a test coverage report:

```bash
npm run test:coverage
```

### End-to-End Tests

To run end-to-end tests with Playwright:

```bash
npm run test:e2e
```

To run E2E tests with the Playwright UI:

```bash
npm run test:e2e:ui
```

### Accessibility Tests

To run accessibility tests:

```bash
npm run test:a11y
```

## Test Structure

The tests are organized as follows:

- `src/__tests__/components/`: Component tests
- `src/__tests__/lib/`: Utility function tests
- `src/__tests__/a11y/`: Accessibility tests
- `e2e/`: End-to-end tests

## Writing New Tests

### Component Tests

When writing component tests:

```javascript
import { render, screen } from '@testing-library/react';
import MyComponent from '@/components/MyComponent';

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent />);
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });
});
```

### End-to-End Tests

When writing E2E tests:

```javascript
import { test, expect } from '@playwright/test';

test('user can navigate to about page', async ({ page }) => {
  await page.goto('/');
  await page.getByRole('link', { name: 'About' }).click();
  await expect(page).toHaveURL('/about');
});
```

### Accessibility Tests

When writing accessibility tests:

```javascript
import { render } from '@testing-library/react';
import MyComponent from '@/components/MyComponent';
import { testA11y } from '../a11y/setup';

describe('MyComponent Accessibility', () => {
  it('should not have any accessibility violations', async () => {
    const { container } = render(<MyComponent />);
    await testA11y(container);
  });
});
```

## Mocking

### Mocking API Calls

For tests that require API mocking:

```javascript
// Mock the API module
jest.mock('@/lib/api', () => ({
  fetchData: jest.fn().mockResolvedValue({ data: 'mocked data' }),
}));
```

### Mocking Next.js Features

For tests that use Next.js features:

```javascript
// Mock the Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  })),
  usePathname: jest.fn(() => '/test'),
  useSearchParams: jest.fn(() => new URLSearchParams()),
}));
```

## Troubleshooting

If you encounter issues:

1. Make sure all dependencies are installed
2. Check that the test environment is properly set up
3. Look for error messages in the test output
4. Try running tests with the `--verbose` flag for more details
