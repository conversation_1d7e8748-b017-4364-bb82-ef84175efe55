import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test('should load the homepage successfully', async ({ page }) => {
    await page.goto('/');

    // Check that the page title is correct
    await expect(page).toHaveTitle(/Natural Healing Now/);

    // Check that main sections are visible
    await expect(page.getByRole('heading', { name: /Featured Clinics/i, exact: false })).toBeVisible();
    await expect(page.getByRole('heading', { name: /Featured Practitioners/i, exact: false })).toBeVisible();
    await expect(page.getByRole('heading', { name: /Popular Categories/i, exact: false })).toBeVisible();
  });

  test('should navigate to clinics page', async ({ page }) => {
    await page.goto('/');

    // Click on "View All Clinics" button/link
    await page.getByRole('link', { name: /View All Clinics/i, exact: false }).click();

    // Check that we've navigated to the clinics page
    await expect(page).toHaveURL(/\/clinics/, { timeout: 10000 });
    await expect(page.getByRole('heading', { name: /Clinics/i })).toBeVisible({ timeout: 10000 });
  });

  test('should navigate to practitioners page', async ({ page }) => {
    await page.goto('/');

    // Click on "View All Practitioners" button/link
    await page.getByRole('link', { name: /View All Practitioners/i, exact: false }).click();

    // Check that we've navigated to the practitioners page
    await expect(page).toHaveURL(/\/practitioners/, { timeout: 10000 });
    await expect(page.getByRole('heading', { name: /Practitioners/i })).toBeVisible({ timeout: 10000 });
  });

  test('should search for content', async ({ page }) => {
    await page.goto('/');

    // Fill the search input
    await page.getByPlaceholder(/Search/i).fill('wellness');

    // Press Enter to submit the search
    await page.keyboard.press('Enter');

    // Check that search results are displayed
    await expect(page.getByText(/Search Results/i)).toBeVisible();
  });
});
