/**
 * Enhanced server-side data fetching utilities with Next.js caching
 */
import { cache } from 'react';
import { revalidateTag } from 'next/cache';

// Get Strapi URL from environment variable
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1337';
const API_PATH = '/api';

// No longer need axios client creation

/**
 * Generate a cache key from endpoint and params
 */
function generateCacheKey(endpoint: string, params?: any): string {
  return `${endpoint}:${JSON.stringify(params || {})}`;
}

/**
 * Generate cache tags for a specific content type
 */
function generateCacheTags(contentType: string, id?: string | number): string[] {
  const tags = [`strapi-${contentType}`];
  if (id) {
    tags.push(`strapi-${contentType}-${id}`);
  }
  return tags;
}

/**
 * Cached server-side fetch function using <PERSON>act's cache
 * This will deduplicate requests within the same render pass
 * Uses Next.js native fetch with proper cache options
 */
export const fetchFromServer = cache(async <T>(
  endpoint: string,
  options: {
    params?: Record<string, any>;
    revalidate?: number | false;
    tags?: string[];
  } = {}
): Promise<T> => {
  try {
    const { params, revalidate = 3600, tags = [] } = options;

    // Build query string from params
    const queryString = params
      ? '?' + new URLSearchParams(
          Object.entries(params).reduce((acc, [key, value]) => {
            // Handle nested objects by stringifying them
            if (typeof value === 'object' && value !== null) {
              acc[key] = JSON.stringify(value);
            } else {
              acc[key] = String(value);
            }
            return acc;
          }, {} as Record<string, string>)
        ).toString()
      : '';

    // Use Next.js fetch with caching options
    const response = await fetch(`${API_URL}${API_PATH}${endpoint}${queryString}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.STRAPI_API_TOKEN || ''}`,
      },
      next: {
        revalidate,
        tags,
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching from API: ${response.status} ${response.statusText}`);
    }

    return response.json();
  } catch (error: any) {
    console.error(`Server: Error fetching from API (${endpoint}):`, error);
    throw error;
  }
});

/**
 * Fetch data with Next.js fetch and caching
 * This leverages Next.js built-in fetch caching
 * Using longer cache times with on-demand revalidation
 */
export async function fetchWithNextCache<T>(
  endpoint: string,
  options: {
    params?: Record<string, any>;
    revalidate?: number | false;
    tags?: string[];
  } = {}
): Promise<T> {
  const { params, revalidate = false, tags = [] } = options;

  // Build query string from params
  const queryString = params
    ? '?' + new URLSearchParams(
        Object.entries(params).reduce((acc, [key, value]) => {
          // Handle nested objects by stringifying them
          if (typeof value === 'object' && value !== null) {
            acc[key] = JSON.stringify(value);
          } else {
            acc[key] = String(value);
          }
          return acc;
        }, {} as Record<string, string>)
      ).toString()
    : '';

  // Use Next.js fetch with caching options
  const response = await fetch(`${API_URL}${API_PATH}${endpoint}${queryString}`, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.STRAPI_API_TOKEN || ''}`,
    },
    next: {
      revalidate,
      tags,
    },
    // Explicitly set cache option for Next.js 15 compatibility
    // This ensures the request is cached according to the revalidate setting:
    // - revalidate === 0 means 'no-store' (dynamic rendering)
    // - revalidate === false or revalidate > 0 means 'force-cache' (ISR or specific fetch revalidation)
    cache: revalidate === 0 ? 'no-store' : 'force-cache',
  });

  if (!response.ok) {
    throw new Error(`Error fetching from API: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Fetch content type data with appropriate cache tags
 * Using on-demand revalidation instead of time-based revalidation
 */
export async function fetchContentType<T>(
  contentType: string,
  options: {
    id?: string | number;
    params?: Record<string, any>;
    revalidate?: number | false;
    isDetailPage?: boolean; // New option to indicate if this is a detail page
  } = {}
): Promise<T> {
  const { id, params, revalidate = false, isDetailPage = false } = options;

  // Generate endpoint
  const endpoint = id ? `/${contentType}/${id}` : `/${contentType}`;

  // Generate cache tags
  const tags = generateCacheTags(contentType, id);

  // Add global content type tag for list pages
  if (!isDetailPage) {
    tags.push('strapi-content');
  }

  // For detail pages, we want on-demand revalidation only (revalidate: false)
  // For list pages, we want a fallback revalidation period (default: 3600 seconds)
  const effectiveRevalidate = isDetailPage ? false : (revalidate || 3600);

  return fetchWithNextCache<T>(endpoint, {
    params,
    revalidate: effectiveRevalidate,
    tags,
  });
}

/**
 * Revalidate content by type
 * This function is more targeted and less aggressive with global revalidations
 */
export function revalidateContent(contentType: string, id?: string | number): void {
  // Log the revalidation request
  console.log(`Revalidating content: ${contentType}${id ? ` with ID ${id}` : ''}`);

  // Revalidate specific content type tags
  const tags = generateCacheTags(contentType, id);
  tags.forEach(tag => revalidateTag(tag));

  // For certain content types, revalidate related content
  if (contentType === 'blog-post') {
    // Revalidate blog-related tags
    revalidateTag('strapi-blog-posts');
    revalidateTag('strapi-blog-posts-slugs');

    // If we have an ID or slug, revalidate the specific blog post page
    if (id) {
      revalidateTag(`strapi-blog-post-${id}`);

      // Note: We don't revalidate all categories, only the ones associated with this blog post
      // This would be handled by the blog-specific webhook handler
    }
  }
  else if (contentType === 'blog-category' || contentType === 'category') {
    // Revalidate category-related tags
    revalidateTag('strapi-categories');
    revalidateTag('strapi-categories-slugs');

    // If it's a blog category, also revalidate blog category tags
    if (contentType === 'blog-category') {
      revalidateTag('strapi-blog-categories');
    }

    // If we have an ID or slug, revalidate the specific category page
    if (id) {
      revalidateTag(`strapi-category-${id}`);

      // If it's a blog category, also revalidate blog category tag
      if (contentType === 'blog-category') {
        revalidateTag(`strapi-blog-category-${id}`);
      }
    }
  }
  else if (contentType === 'blog-tag' || contentType === 'tag') {
    // Revalidate tag-related tags
    revalidateTag('strapi-tags');
    revalidateTag('strapi-blog-tags');

    // If we have an ID or slug, revalidate the specific tag page
    if (id) {
      revalidateTag(`strapi-tag-${id}`);
    }
  }
  else if (contentType === 'author' || contentType === 'author-blog') {
    // Revalidate author-related tags
    revalidateTag('strapi-authors');
    revalidateTag('strapi-blog-authors');

    // If we have an ID or slug, revalidate the specific author page
    if (id) {
      revalidateTag(`strapi-author-${id}`);
    }
  }
  else if (contentType === 'practitioner') {
    // Revalidate practitioner-related tags
    revalidateTag('strapi-practitioners');
    revalidateTag('strapi-practitioners-slugs');
    revalidateTag('strapi-practitioners-list');

    // If we have an ID or slug, revalidate the specific practitioner page
    if (id) {
      revalidateTag(`strapi-practitioner-${id}`);
    }
  }
  else if (contentType === 'clinic') {
    // Revalidate clinic-related tags
    revalidateTag('strapi-clinics');
    revalidateTag('strapi-clinics-slugs');
    revalidateTag('strapi-clinics-list');

    // If we have an ID or slug, revalidate the specific clinic page
    if (id) {
      revalidateTag(`strapi-clinic-${id}`);
    }
  }
  else if (contentType === 'specialty') {
    // Revalidate specialty-related tags
    revalidateTag('strapi-specialties');
    revalidateTag('strapi-specialties-slugs');

    // If we have an ID, revalidate the specific specialty page
    if (id) {
      revalidateTag(`strapi-specialty-${id}`);
    }
  }
  else {
    // For any other content type, only revalidate its specific tags
    // We don't revalidate the global content tag anymore to avoid excessive revalidations
    console.log(`Revalidating unknown content type: ${contentType}`);
  }
}
