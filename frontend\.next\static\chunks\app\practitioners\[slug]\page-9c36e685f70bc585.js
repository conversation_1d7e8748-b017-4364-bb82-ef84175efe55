(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4064],{1238:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,8887)),Promise.resolve().then(r.bind(r,6390)),Promise.resolve().then(r.bind(r,4381)),Promise.resolve().then(r.bind(r,1454)),Promise.resolve().then(r.bind(r,8224)),Promise.resolve().then(r.bind(r,6843)),Promise.resolve().then(r.bind(r,1486)),Promise.resolve().then(r.bind(r,1415))},1415:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(5155);let n=e=>{let{data:t}=e;return t?(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:t}}):null}},1454:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(2115),n=r(5695),o=r(8864);let i=e=>{let{practitioner:t}=e,r=(0,n.useRouter)(),i=(0,n.usePathname)(),s=(0,n.useSearchParams)(),l=(0,a.useRef)(!1);return(0,a.useEffect)(()=>{if(!l.current){if(t&&t.id&&t.slug){let e=(0,o.b3)(t.slug);if(!e||!e._hasDetailedData){let e={...t,_hasDetailedData:!0};(0,o.tq)(e),l.current=!0}}if(i.includes("/practitioners/")&&"true"===s.get("prefetched")){let e=new URLSearchParams(s.toString());e.delete("prefetched");let t=e.toString()?"".concat(i,"?").concat(e.toString()):i;r.replace(t,{scroll:!1})}}},[t,i,s,r]),null}},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return s}});let a=r(8229),n=r(8883),o=r(3063),i=a._(r(4663));function s(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1440,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"custom",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=o.Image},1486:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(5155);let n=e=>{let{videoHtml:t}=e;return t?(0,a.jsx)("div",{className:"mt-6 rounded-lg overflow-hidden",style:{maxWidth:"100%"},children:(0,a.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0},dangerouslySetInnerHTML:{__html:t}})}):null}},3297:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i});var a=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let n={enabled:!1,level:"info",prefix:"[NHN]"};function o(e,t){for(var r=arguments.length,o=Array(r>2?r-2:0),i=2;i<r;i++)o[i-2]=arguments[i];if(!n.enabled)return;let s=Object.values(a),l=s.indexOf(n.level);if(s.indexOf(e)>=l){let r=n.prefix?"".concat(n.prefix," "):"",a="".concat(r).concat(t);switch(e){case"debug":console.debug(a,...o);break;case"info":console.info(a,...o);break;case"warn":console.warn(a,...o);break;case"error":console.error(a,...o)}}}let i={debug:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];o("debug",e,...r)},info:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];o("info",e,...r)},warn:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];o("warn",e,...r)},error:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];o("error",e,...r)},configure:function(e){n={...n,...e}}}},4381:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(5155);let n=e=>{let{emailAddress:t}=e;return t?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 mb-1",children:"Email"}),(0,a.jsx)("a",{href:"mailto:".concat(t),className:"font-medium text-emerald-600 hover:text-emerald-700 break-all",children:t})]}):null}},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>u});var a=r(2115),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},o=a.createContext&&a.createContext(n),i=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var a,n,o;a=e,n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in a?Object.defineProperty(a,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>a.createElement(d,s({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:n,size:o,title:l}=e,u=function(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(a=0;a<o.length;a++)r=o[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,i),d=o||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,u,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),l&&a.createElement("title",null,l),e.children)};return void 0!==o?a.createElement(o.Consumer,null,e=>t(e)):t(n)}},4663:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,imageUtils:()=>l});var a=r(1890);let n={count:0,errors:0,totalTime:0,slowestTime:0,slowestImage:""},o={enableMetrics:"true"===a.env.NEXT_PUBLIC_CACHE_METRICS,useHighQuality:!0,disableOptimization:"true"===a.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION,defaultQuality:85,avifQuality:80,webpQuality:85,jpegQuality:90,pngQuality:90,maxDevicePixelRatio:3,minWidth:20,blurUpRadius:10};function i(e,t,r){let a="https://nice-badge-2130241d6c.media.strapiapp.com",n=r||(e.toLowerCase().match(/\.avif$/i)?o.avifQuality:e.toLowerCase().match(/\.webp$/i)?o.webpQuality:e.toLowerCase().match(/\.jpe?g$/i)?o.jpegQuality:e.toLowerCase().match(/\.png$/i)?o.pngQuality:e.toLowerCase().match(/\.(jpe?g|png)$/i)?o.jpegQuality:o.webpQuality),i=Math.min(window.devicePixelRatio||1,o.maxDevicePixelRatio);if(t<o.minWidth)return e;try{let r=Math.round(t*i),o=new URL(e.startsWith("http")?e:"".concat(a).concat(e.startsWith("/")?e:"/".concat(e)));if(o.hostname.includes("strapiapp.com")||o.hostname.includes("localhost")){o.searchParams.set("w",r.toString()),o.searchParams.set("q",n.toString());let t=e.toLowerCase().match(/\.(jpe?g|png)$/i);o.searchParams.has("format")||o.searchParams.set("format",t?"avif":"webp");{let e=Array.from(o.searchParams.entries()).sort();o.search=e.map(e=>{let[t,r]=e;return"".concat(t,"=").concat(r)}).join("&")}t&&o.searchParams.set("sharp","10"),"http:"===o.protocol&&(o.protocol="https:")}return o.toString()}catch(r){if(e.startsWith("/"))return"".concat(a).concat(e,"?w=").concat(t,"&q=").concat(n);return e}}function s(e){let{src:t,width:r,quality:a}=e,s=o.enableMetrics?performance.now():0;if(!t)return"";try{if(!(t&&!o.disableOptimization&&!("string"==typeof t&&[".svg",".gif",".webp",".avif"].some(e=>t.toLowerCase().endsWith(e))||t.startsWith("http")&&!t.includes("strapiapp.com")&&!t.includes("localhost:1337"))&&1))return t;let e=i(t,r,a);if(o.enableMetrics&&s){let e=performance.now()-s;n.count++,n.totalTime+=e,e>n.slowestTime&&(n.slowestTime=e,n.slowestImage=t)}return e}catch(e){return o.enableMetrics&&n.errors++,t}}let l={getBlurDataUrl:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return e?"".concat(i(e,t,10),"&blur=80"):""},preloadImage:(e,t)=>{if(!e)return;let r=new Image;return r.src=s({src:e,width:t,quality:o.defaultQuality}),r},resetMetrics:()=>{n.count=0,n.errors=0,n.totalTime=0,n.slowestTime=0,n.slowestImage=""},getMetrics:()=>({...n})}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},6390:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(5155),n=r(351);let o=e=>{let{emailAddress:t}=e;return t?(0,a.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,a.jsx)(n.pHD,{className:"mr-2 text-emerald-500"}),(0,a.jsx)("a",{href:"mailto:".concat(t),className:"text-emerald-600 hover:text-emerald-700",children:t})]}):null}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var a=r(1469),n=r.n(a)},6843:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(5155),n=r(2515);let o=e=>{let{isVerified:t}=e;return t?(0,a.jsxs)("div",{className:"flex items-center gap-x-1 bg-emerald-100 text-emerald-700 px-2 py-0.5 rounded-full text-sm font-medium",children:[(0,a.jsx)(n.AI8,{color:"#009967",size:16}),(0,a.jsx)("span",{children:"VERIFIED"})]}):null}},8224:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(5155),n=r(6766),o=r(2115);let i=e=>{let{imageUrl:t,name:r}=e,[i,s]=(0,o.useState)(!1),[l,c]=(0,o.useState)(null);return((0,o.useEffect)(()=>{if(s(!1),t&&"string"==typeof t&&""!==t.trim())try{let e=t.startsWith("http")?t:"https://".concat(t);new URL(e),c(e)}catch(e){console.error("Invalid image URL:",t,e),s(!0),c(null)}else c(null)},[t]),!l||i)?(0,a.jsx)("div",{className:"bg-emerald-100 h-32 w-32 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-emerald-700 font-bold text-3xl",children:r?r.charAt(0).toUpperCase():"?"})}):(0,a.jsx)("div",{className:"h-32 w-32 rounded-full overflow-hidden",children:(0,a.jsx)(n.default,{src:l,alt:"".concat(r,"'s profile picture"),width:128,height:128,className:"object-cover rounded-full",priority:!0,onError:e=>{console.error("Error loading practitioner image:",{practitionerName:r,profilePictureUrl:l,error:e}),s(!0)}})})}},8864:(e,t,r)=>{"use strict";r.d(t,{b3:()=>i,tq:()=>o});let a={};function n(e){let t=a[e],r=Date.now();if(t&&t.expiry>r)return t.data;try{let t=sessionStorage.getItem("cache_".concat(e));if(t){let n=JSON.parse(t);if(n.expiry>r)return a[e]=n,n.data;sessionStorage.removeItem("cache_".concat(e))}}catch(e){console.error("Error retrieving data from sessionStorage:",e)}return null}function o(e){if(!e||!e.id||!e.slug)return;let t=n("practitioner_".concat(e.slug));t&&(!e._hasDetailedData||t._hasDetailedData)||function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5,n=Date.now();a[e]={data:t,timestamp:n,expiry:n+r};try{sessionStorage.setItem("cache_".concat(e),JSON.stringify({data:t,timestamp:n,expiry:n+r}))}catch(e){console.error("Error storing data in sessionStorage:",e)}}("practitioner_".concat(e.slug),e,18e5)}function i(e){return n("practitioner_".concat(e))}},8887:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(5155),n=r(6943),o=r(5006),i=r(2115),s=r(3297);async function l(e,t){try{let r=await fetch("/api/analytics/post-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:e,postSlug:t})});if(!r.ok)throw Error("Failed to track post view: ".concat(r.statusText))}catch(e){s.Ay.error("Error tracking post view:",e)}}r(1890);let c=e=>{let{content:t,postId:r,postSlug:s,applyNoFollow:c=!0}=e;return(0,i.useEffect)(()=>{if(r&&s){let e=setTimeout(()=>{l(r,s)},2e3);return()=>clearTimeout(e)}},[r,s]),(0,a.jsxs)("div",{className:"mb-8",children:[" ",(0,a.jsx)(n.oz,{components:{h1:e=>{let{node:t,...r}=e;return(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mt-8 mb-4",...r})},h2:e=>{let{node:t,...r}=e;return(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mt-6 mb-3",...r})},h3:e=>{let{node:t,...r}=e;return(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mt-5 mb-2",...r})},h4:e=>{let{node:t,...r}=e;return(0,a.jsx)("h4",{className:"text-lg font-bold text-gray-800 mt-4 mb-2",...r})},p:e=>{let{node:t,...r}=e;return(0,a.jsx)("p",{className:"text-gray-700 mb-4",...r})},a:e=>{let{node:t,href:r,...n}=e,o=r&&(r.startsWith("http://")||r.startsWith("https://")),i="";return o&&(c&&(i+="nofollow "),i+="noopener noreferrer"),(0,a.jsx)("a",{className:"text-emerald-600 hover:text-emerald-700 underline",href:r,rel:i.trim()||void 0,target:o?"_blank":void 0,...n})},ul:e=>{let{node:t,...r}=e;return(0,a.jsx)("ul",{className:"list-disc pl-6 mb-4",...r})},ol:e=>{let{node:t,...r}=e;return(0,a.jsx)("ol",{className:"list-decimal pl-6 mb-4",...r})},li:e=>{let{node:t,...r}=e;return(0,a.jsx)("li",{className:"mb-1",...r})},blockquote:e=>{let{node:t,...r}=e;return(0,a.jsx)("blockquote",{className:"border-l-4 border-emerald-500 pl-4 italic my-4",...r})}},rehypePlugins:[o.A],children:t})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[844,5479,6874,3063,2780,8441,1684,7358],()=>t(1238)),_N_E=e.O()}]);