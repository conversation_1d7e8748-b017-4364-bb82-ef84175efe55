(()=>{var e={};e.id=4735,e.ids=[4735],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11358:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=r(65239),a=r(48088),i=r(31369),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let l={children:["",{children:["optimized-client-example",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,99723)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\optimized-client-example\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\optimized-client-example\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/optimized-client-example/page",pathname:"/optimized-client-example",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},12412:e=>{"use strict";e.exports=require("assert")},13710:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),a=r(85814),i=r.n(a),n=r(16189);function o(){let e=(0,n.usePathname)();return(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Optimization Examples"}),(0,s.jsx)("p",{className:"mb-4",children:"These examples demonstrate different approaches to reducing API calls to Strapi."}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:[{href:"/optimized-example",label:"Server-Side Optimization"},{href:"/optimized-client-example",label:"Client-Side Optimization"}].map(t=>(0,s.jsx)(i(),{href:t.href,className:`px-4 py-2 rounded ${e===t.href?"bg-blue-500 text-white":"bg-white text-blue-500 hover:bg-blue-100"}`,children:t.label},t.href))})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20990:(e,t,r)=>{Promise.resolve().then(r.bind(r,99723))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71967:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),a=r(43210);let i=(0,a.cache)(async(e,t={},r=!0)=>{try{let r=e.startsWith("/")?e:`/${e}`,{params:s,revalidate:a=3600,tags:i=[]}=t,n=s?"?"+new URLSearchParams(Object.entries(s).reduce((e,[t,r])=>("object"==typeof r&&null!==r?e[t]=JSON.stringify(r):e[t]=String(r),e),{})).toString():"",o=await fetch(`https://nice-badge-2130241d6c.strapiapp.com/api${r}${n}`,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.STRAPI_API_TOKEN||""}`},next:{revalidate:a,tags:i}});if(!o.ok)throw Error(`Error fetching from API: ${o.status} ${o.statusText}`);return o.json()}catch(t){if(console.error(`Error fetching from API (${e}):`,t),e.includes("/")&&e.split("/").filter(Boolean).length>0)return{data:[]};return{}}}),n=(0,a.cache)(async(e,t={})=>{let{id:r,params:s,revalidate:a=!1,isDetailPage:n=!1}=t,o=r?`/${e}/${r}`:`/${e}`,l=function(e,t){let r=[`strapi-${e}`];return t&&r.push(`strapi-${e}-${t}`),r}(e,r);n||l.push("strapi-content");try{return i(o,{params:s,revalidate:!n&&(a||3600),tags:l})}catch(t){return console.error(`Error in fetchContentType for ${e}:`,t),{data:[]}}});function o(e,t={}){let[r,s]=(0,a.useState)(null),[i,l]=(0,a.useState)(!0),[d,c]=(0,a.useState)(null),u=async(r=!1)=>{l(!0),c(null);try{let r=await n(e,t);s(r)}catch(e){c(e instanceof Error?e:Error(String(e)))}finally{l(!1)}};return(0,a.useEffect)(()=>{u()},[e,JSON.stringify(t)]),{data:r,isLoading:i,error:d,refetch:()=>u(!0)}}var l=r(13710);function d(){let[e,t]=(0,a.useState)("featured");return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Optimized Client-Side Data Fetching"}),(0,s.jsx)(l.default,{}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("p",{className:"text-lg mb-4",children:"This page demonstrates client-side optimized data fetching from Strapi. The data is cached in memory and shared between components."}),(0,s.jsxs)("div",{className:"flex border-b",children:[(0,s.jsx)("button",{className:`px-4 py-2 ${"featured"===e?"border-b-2 border-blue-500 text-blue-500":"text-gray-500"}`,onClick:()=>t("featured"),children:"Featured Content"}),(0,s.jsx)("button",{className:`px-4 py-2 ${"categories"===e?"border-b-2 border-blue-500 text-blue-500":"text-gray-500"}`,onClick:()=>t("categories"),children:"Categories"}),(0,s.jsx)("button",{className:`px-4 py-2 ${"blog"===e?"border-b-2 border-blue-500 text-blue-500":"text-gray-500"}`,onClick:()=>t("blog"),children:"Blog Posts"})]})]}),"featured"===e&&(0,s.jsx)(c,{}),"categories"===e&&(0,s.jsx)(u,{}),"blog"===e&&(0,s.jsx)(p,{}),(0,s.jsxs)("div",{className:"mt-8 p-6 bg-blue-50 rounded-lg",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"How This Reduces API Calls"}),(0,s.jsx)("p",{className:"mb-2",children:"This implementation reduces API calls to Strapi in several ways:"}),(0,s.jsxs)("ol",{className:"list-decimal pl-5 space-y-2",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"In-memory caching:"})," Responses are cached with content-type specific TTLs, preventing unnecessary refetching of data that hasn't changed."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Request deduplication:"})," Multiple components requesting the same data will share a single API call instead of each making their own request."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Content-type specific caching:"})," Different cache durations based on how frequently content changes (e.g., global settings are cached longer than blog posts)."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Prefetching:"})," Common data is preloaded, reducing the need for API calls when navigating between pages."]})]}),(0,s.jsxs)("p",{className:"mt-4",children:["For more details, see the documentation in ",(0,s.jsx)("code",{children:"frontend/src/docs/OPTIMIZED_API_CALLS.md"}),"."]})]})]})}function c(){let{data:e,isLoading:t,error:r}=function(e=4){return o("clinics",{params:{filters:{isFeatured:{$eq:!0}},pagination:{pageSize:e},populate:"*"}})}(),{data:a,isLoading:i,error:n}=function(e=4){return o("practitioners",{params:{filters:{isFeatured:{$eq:!0}},pagination:{pageSize:e},populate:"*"}})}();return(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Featured Clinics"}),t?(0,s.jsx)(m,{}):r?(0,s.jsx)(h,{message:"Failed to load clinics"}):(0,s.jsx)("ul",{className:"list-disc pl-5",children:e?.data?.map(e=>(0,s.jsx)("li",{children:e.name||e.attributes?.name},e.id||e.documentId))})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Featured Practitioners"}),i?(0,s.jsx)(m,{}):n?(0,s.jsx)(h,{message:"Failed to load practitioners"}):(0,s.jsx)("ul",{className:"list-disc pl-5",children:a?.data?.map(e=>{let t=e.firstName||e.attributes?.firstName,r=e.lastName||e.attributes?.lastName;return(0,s.jsxs)("li",{children:[t," ",r]},e.id||e.documentId)})})]})]})}function u(){let{data:e,isLoading:t,error:r}=function(e=10){return o("categories",{params:{pagination:{pageSize:e},populate:"*"}})}(10);return(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Categories"}),t?(0,s.jsx)(m,{}):r?(0,s.jsx)(h,{message:"Failed to load categories"}):(0,s.jsx)("ul",{className:"list-disc pl-5",children:e?.data?.map(e=>(0,s.jsx)("li",{children:e.name||e.attributes?.name},e.id||e.documentId))})]})}function p(){let[e,t]=(0,a.useState)("featured"),{data:r,isLoading:i,error:n}=function(e=3){return o("blog-posts",{params:{filters:{isFeatured:{$eq:!0}},sort:"publishDate:desc",pagination:{pageSize:e},populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}}})}(),{data:l,isLoading:d,error:c}=function(e=3){return o("blog-posts",{params:{sort:"publishDate:desc",pagination:{pageSize:e},populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}}})}(5),{data:u,isLoading:p,error:x}=function(e=3){return o("blog-posts",{params:{sort:"viewCount:desc",pagination:{pageSize:e},populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}}})}(5);return(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,s.jsxs)("div",{className:"flex mb-4",children:[(0,s.jsx)("button",{className:`px-4 py-2 mr-2 rounded ${"featured"===e?"bg-blue-500 text-white":"bg-gray-200"}`,onClick:()=>t("featured"),children:"Featured"}),(0,s.jsx)("button",{className:`px-4 py-2 mr-2 rounded ${"latest"===e?"bg-blue-500 text-white":"bg-gray-200"}`,onClick:()=>t("latest"),children:"Latest"}),(0,s.jsx)("button",{className:`px-4 py-2 rounded ${"popular"===e?"bg-blue-500 text-white":"bg-gray-200"}`,onClick:()=>t("popular"),children:"Popular"})]}),(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"featured"===e?"Featured Blog Posts":"latest"===e?"Latest Blog Posts":"Popular Blog Posts"}),("featured"===e?i:"latest"===e?d:p)?(0,s.jsx)(m,{}):("featured"===e?n:"latest"===e?c:x)?(0,s.jsx)(h,{message:`Failed to load ${e} blog posts`}):(0,s.jsx)("ul",{className:"list-disc pl-5",children:("featured"===e?r:"latest"===e?l:u)?.data?.map(e=>(0,s.jsx)("li",{children:e.title||e.attributes?.title},e.id||e.documentId))})]})}function m(){return(0,s.jsxs)("div",{className:"animate-pulse space-y-3",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]})}function h({message:e}){return(0,s.jsx)("div",{className:"text-red-500 p-4 bg-red-50 rounded",children:(0,s.jsx)("p",{children:e})})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87334:(e,t,r)=>{Promise.resolve().then(r.bind(r,71967))},94735:e=>{"use strict";e.exports=require("events")},99723:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\optimized-client-example\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\optimized-client-example\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,1330,3376,6391,2975,8446,270],()=>r(11358));module.exports=s})();