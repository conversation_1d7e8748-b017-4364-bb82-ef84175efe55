/**
 * Import clinics from CSV file into Strapi 5 using the REST API
 * 
 * This script reads a CSV file and imports the data into the Strapi clinics collection
 * using the REST API with an API token for authentication.
 * 
 * Usage: node import-clinics-strapi5.js
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const axios = require('axios');
require('dotenv').config();

// Configuration
const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1337';
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;
const CSV_FILE_PATH = path.resolve(__dirname, '../strapi-cms/Import-Strapi-directory-Sheet2.csv');

// Axios instance with authorization header
const strapiAPI = axios.create({
  baseURL: STRAPI_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `<PERSON><PERSON> ${STRAPI_API_TOKEN}`
  }
});

// Helper function to convert string boolean to actual boolean
function parseBoolean(value) {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') {
    return value.toLowerCase() === 'true';
  }
  return false;
}

// Helper function to clean slug
function cleanSlug(slug) {
  if (!slug) return '';
  // Replace any characters that aren't allowed in Strapi slugs
  return slug.replace(/[^A-Za-z0-9-_.~]/g, '-');
}

// Main function to import clinics
async function importClinics() {
  try {
    // Test connection first
    console.log('Testing connection to Strapi...');
    console.log('API URL:', STRAPI_URL);
    console.log('API Token available:', !!STRAPI_API_TOKEN);

    try {
      const testResponse = await strapiAPI.get('/api/clinics?pagination[pageSize]=1');
      console.log('Connection successful!');
    } catch (error) {
      console.error('Connection test failed:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      }
      throw new Error('Failed to connect to Strapi API');
    }

    const results = [];

    // Read CSV file
    return new Promise((resolve, reject) => {
      fs.createReadStream(CSV_FILE_PATH)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', async () => {
          console.log(`Read ${results.length} rows from CSV file`);

          let created = 0;
          let updated = 0;
          let skipped = 0;
          let errors = 0;

          // Process each row
          for (const row of results) {
            try {
              // Check if clinic with this slug already exists
              const slug = row.slug;
              if (!slug) {
                console.log('Skipping row without slug');
                skipped++;
                continue;
              }

              // Clean the slug to ensure it passes validation
              const cleanedSlug = cleanSlug(slug);
              console.log(`Processing clinic with slug: ${cleanedSlug}`);

              // Search for existing clinic with this slug
              const existingResponse = await strapiAPI.get(`/api/clinics?filters[slug][$eq]=${encodeURIComponent(cleanedSlug)}`);
              const existingClinics = existingResponse.data.data;

              if (existingClinics.length > 0) {
                // Clinic exists, update it
                const existingClinic = existingClinics[0];
                console.log(`Found existing clinic with ID: ${existingClinic.id}, documentId: ${existingClinic.documentId}`);
                
                // Prepare update data
                const updateData = {
                  // Basic fields
                  name: row.name,
                  description: row.description || '',
                  isActive: parseBoolean(row.isActive),
                  isFeatured: parseBoolean(row.isFeatured),
                  isVerified: false,
                  paid: false,
                  appointmentOptions: ["In-person"],
                  paymentMethod: ["Cash", "Mastercard", "Visa", "Amex", "Apple Pay"],
                  
                  // Components
                  contactInfo: {
                    phoneNumber: row.phoneNumber || '',
                    emailAddress: row.emailAddress || '',
                    websiteUrl: row.websiteUrl || ''
                  },
                  address: {
                    streetAddress1: row.streetAddress1 || '',
                    city: row.city || '',
                    stateProvince: row.stateProvince || '',
                    country: row.country || 'USA',
                    postalCode: row.postalCode || ''
                  },
                  location: {
                    googlePlaceId: row.googlePlaceId || '',
                    latitude: row.latitude ? parseFloat(row.latitude) : null,
                    longitude: row.longitude ? parseFloat(row.longitude) : null
                  }
                };

                // Add video embed if it exists
                if (row.videoEmbedUrl) {
                  updateData.videoEmbed = {
                    url: row.videoEmbedUrl
                  };
                }

                try {
                  // Update the clinic
                  const updateResponse = await strapiAPI.put(`/api/clinics/${existingClinic.documentId}`, {
                    data: updateData
                  });
                  console.log(`Updated clinic: ${slug}`);
                  updated++;
                } catch (updateError) {
                  console.error(`Error updating clinic ${slug}:`, updateError.message);
                  if (updateError.response) {
                    console.error('Response status:', updateError.response.status);
                    console.error('Response data:', JSON.stringify(updateError.response.data, null, 2));
                  }
                  errors++;
                }
              } else {
                // Clinic doesn't exist, create new entry
                const createData = {
                  // Basic fields
                  name: row.name,
                  slug: cleanedSlug,
                  description: row.description || '',
                  isActive: parseBoolean(row.isActive),
                  isFeatured: parseBoolean(row.isFeatured),
                  isVerified: false,
                  paid: false,
                  appointmentOptions: ["In-person"],
                  paymentMethod: ["Cash", "Mastercard", "Visa", "Amex", "Apple Pay"],
                  
                  // Components
                  contactInfo: {
                    phoneNumber: row.phoneNumber || '',
                    emailAddress: row.emailAddress || '',
                    websiteUrl: row.websiteUrl || ''
                  },
                  address: {
                    streetAddress1: row.streetAddress1 || '',
                    city: row.city || '',
                    stateProvince: row.stateProvince || '',
                    country: row.country || 'USA',
                    postalCode: row.postalCode || ''
                  },
                  location: {
                    googlePlaceId: row.googlePlaceId || '',
                    latitude: row.latitude ? parseFloat(row.latitude) : null,
                    longitude: row.longitude ? parseFloat(row.longitude) : null
                  }
                };

                // Add video embed if it exists
                if (row.videoEmbedUrl) {
                  createData.videoEmbed = {
                    url: row.videoEmbedUrl
                  };
                }

                try {
                  // Create the clinic
                  const createResponse = await strapiAPI.post('/api/clinics', {
                    data: createData
                  });
                  console.log(`Created new clinic: ${slug}`);
                  created++;
                } catch (createError) {
                  console.error(`Error creating clinic ${slug}:`, createError.message);
                  if (createError.response) {
                    console.error('Response status:', createError.response.status);
                    console.error('Response data:', JSON.stringify(createError.response.data, null, 2));
                  }
                  errors++;
                }
              }
            } catch (error) {
              console.error(`Error processing row with slug ${row.slug}:`, error.message);
              if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', JSON.stringify(error.response.data, null, 2));
              } else {
                console.error('Full error:', error);
              }
              errors++;
            }
          }

          console.log('\nImport Summary:');
          console.log(`Created: ${created}`);
          console.log(`Updated: ${updated}`);
          console.log(`Skipped: ${skipped}`);
          console.log(`Errors: ${errors}`);
          console.log(`Total processed: ${created + updated + skipped + errors}`);

          resolve({ created, updated, skipped, errors });
        })
        .on('error', (error) => {
          console.error('Error reading CSV file:', error);
          reject(error);
        });
    });
  } catch (error) {
    console.error('Import failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
}

// Run the import
console.log('Starting import process...');
importClinics()
  .then(result => {
    console.log('Import completed successfully:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('Import failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  });
