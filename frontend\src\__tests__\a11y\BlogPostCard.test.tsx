import React from 'react';
import { render } from '@testing-library/react';
import BlogPostCard from '@/components/blog/BlogPostCard';

describe('BlogPostCard Accessibility', () => {
  const mockPost = {
    id: '1',
    title: 'Test Blog Post',
    slug: 'test-blog-post',
    excerpt: 'This is a test excerpt for the blog post.',
    featured_image: 'https://example.com/image.jpg',
    published_at: '2023-06-15T10:30:00Z',
    publish_date: '2023-06-15T10:30:00Z', // Added this field
    reading_time: 5,
    author: {
      id: '2',
      name: 'Test Author',
      slug: 'test-author',
      profile_picture: 'https://example.com/profile.jpg',
    },
    categories: [
      {
        id: '3',
        name: 'Test Category',
        slug: 'test-category',
      },
    ],
  };

  it('should render without crashing', () => {
    const { container } = render(<BlogPostCard post={mockPost} />);
    expect(container).toBeTruthy();
  });
});
