/**
 * Enhanced client-side data fetching utilities with optimized caching
 */
import axios, { AxiosRequestConfig } from 'axios';
import { QueryClient } from '@tanstack/react-query';

// Get Strapi URL from environment variable
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1337';
const API_PATH = '/api';

/**
 * Creates a client-side API client
 */
const createClientSideApiClient = () => {
  return axios.create({
    baseURL: `${API_URL}${API_PATH}`,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// Create a singleton axios instance for client-side requests
const clientApiInstance = createClientSideApiClient();

/**
 * Generate a query key from endpoint and params
 */
export function generateQueryKey(endpoint: string, params?: any): any[] {
  return [endpoint, params ? JSON.stringify(params) : null];
}

/**
 * Fetch data from API on client side
 */
export async function fetchFromClient<T>(
  endpoint: string,
  options: AxiosRequestConfig = {}
): Promise<T> {
  try {
    const response = await clientApiInstance.get<T>(endpoint, options);
    return response.data;
  } catch (error: any) {
    console.error(`Client: Error fetching from API (${endpoint}):`, error);

    if (error.response) {
      console.error('Client: Response status:', error.response.status);
    }

    throw error;
  }
}

/**
 * Prefetch data and store in React Query cache
 * This reduces API calls by preloading data that will be needed soon
 */
export async function prefetchQuery<T>(
  queryClient: QueryClient,
  endpoint: string,
  params?: any,
  options: {
    staleTime?: number;
    cacheTime?: number;
  } = {}
): Promise<void> {
  const { staleTime = 5 * 60 * 1000, cacheTime = 10 * 60 * 1000 } = options;
  
  const queryKey = generateQueryKey(endpoint, params);
  
  // Skip if already in cache and not stale
  if (queryClient.getQueryData(queryKey)) {
    return;
  }
  
  await queryClient.prefetchQuery({
    queryKey,
    queryFn: async () => {
      const axiosOptions: AxiosRequestConfig = {};
      if (params) {
        axiosOptions.params = params;
      }
      
      return fetchFromClient<T>(endpoint, axiosOptions);
    },
    staleTime,
    gcTime: cacheTime,
  });
}

/**
 * Batch multiple API requests into a single Promise.all call
 * This reduces the number of separate network requests
 */
export async function batchRequests<T>(
  requests: Array<{ endpoint: string; options?: AxiosRequestConfig }>
): Promise<T[]> {
  return Promise.all(
    requests.map(({ endpoint, options = {} }) => 
      fetchFromClient<T>(endpoint, options)
    )
  );
}

/**
 * Optimized fetch function that uses a shared request deduplication cache
 * This prevents duplicate in-flight requests for the same data
 */
const inFlightRequests = new Map<string, Promise<any>>();

export async function fetchWithDeduplication<T>(
  endpoint: string,
  options: AxiosRequestConfig = {}
): Promise<T> {
  // Create a cache key from the endpoint and options
  const cacheKey = `${endpoint}:${JSON.stringify(options)}`;
  
  // Check if there's already an in-flight request for this data
  if (inFlightRequests.has(cacheKey)) {
    return inFlightRequests.get(cacheKey) as Promise<T>;
  }
  
  // Create a new request and store it in the in-flight cache
  const request = fetchFromClient<T>(endpoint, options)
    .finally(() => {
      // Remove from in-flight cache when done
      inFlightRequests.delete(cacheKey);
    });
  
  inFlightRequests.set(cacheKey, request);
  return request;
}
