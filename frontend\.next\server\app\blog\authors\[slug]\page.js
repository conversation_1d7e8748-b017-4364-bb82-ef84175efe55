(()=>{var e={};e.id=6628,e.ids=[6628],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5973:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v,dynamic:()=>m,dynamicParams:()=>p,generateMetadata:()=>x,generateStaticParams:()=>f,revalidate:()=>h});var a=r(37413),s=r(4536),l=r.n(s),i=r(73993),o=r(58446),n=r(82158),d=r(53384),c=r(98917),u=r(39916);let h=43200,m="force-static",p=!0,g=e=>e?e.startsWith("http")||e.startsWith("//")?e:`https://nice-badge-2130241d6c.strapiapp.com${e}`:null;async function x({params:e,searchParams:t={}}){let r=e.slug,a=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";console.log(`Using site URL for canonical URLs: ${a}`);try{let e=Number(t.page)||1,s=await b(r,{page:e,pageSize:12});if(!s||!s.data||0===s.data.length)return{title:"Author Not Found | Natural Healing Now",description:"The requested author could not be found."};let l=s.data[0],i=Number(t.page)||1,o=i>1?` - Page ${i}`:"",n=`${l.name}${o} - Author | Natural Healing Now`,d=l.bio||`Explore articles by ${l.name} on Natural Healing Now.`,c=i>1?`?page=${i}`:"",u=`/blog/authors/${r}${c}`,h=a?`${a}${u}`:u;return{title:n,description:d,robots:"index, follow",alternates:{canonical:h},openGraph:{title:n,description:d,url:h,siteName:"Natural Healing Now",type:"profile"},twitter:{card:"summary",title:n,description:d}}}catch(e){return console.error(`Error generating metadata for author ${r}:`,e),{title:"Author | Natural Healing Now",description:"Explore our authors and their articles on natural healing."}}}async function f(){try{let e=await o.$.blog.getAuthors({cache:"force-cache",next:{revalidate:43200,tags:["strapi-authors-slugs"]}});if(e&&e.data&&Array.isArray(e.data))return console.log(`Pre-rendering ${e.data.length} author detail pages`),e.data.filter(e=>null!==e&&"string"==typeof e.slug).map(e=>({slug:e.slug}));return console.error("Failed to fetch author slugs for generateStaticParams"),[]}catch(e){return console.error("Error fetching author slugs for generateStaticParams:",e),[]}}async function b(e,t={}){let r=t.page||1,a=t.pageSize||12;console.log(`Fetching author data for slug: "${e}" (page ${r}, pageSize ${a})`);try{let t=await (0,o.f)("/authors",{params:{filters:{slug:{$eq:e}},populate:{profilePicture:!0}},cache:"force-cache",next:{revalidate:43200,tags:["strapi-authors","strapi-blog-authors",`strapi-author-${e}`]}});if(!t?.data||!Array.isArray(t.data)||0===t.data.length)return console.error(`No author found with slug: ${e}`),{data:[],meta:{pagination:{page:r,pageSize:a,pageCount:0,total:0}}};let s=t.data[0].id;console.log(`Found author with ID: ${s}`);let l=await (0,o.f)("/blog-posts",{params:{filters:{author_blogs:{id:{$eq:s}}},pagination:{page:r,pageSize:a},sort:["publishDate:desc"],populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}},cache:"force-cache",next:{revalidate:43200,tags:["strapi-blog-posts","strapi-authors",`strapi-author-${e}`,`strapi-author-${e}-page-${r}`]}});console.log(`Found ${l?.data?.length||0} posts for author ID ${s}`);let i={data:[{...t.data[0],blog_posts:l.data||[]}],meta:l.meta||{pagination:{page:r,pageSize:a,pageCount:0,total:0}}};return console.log(`Combined author response for slug ${e}:`,JSON.stringify({hasData:!!i?.data,isArray:Array.isArray(i?.data),length:Array.isArray(i?.data)?i.data.length:"not an array",hasPosts:Array.isArray(i?.data)&&i.data.length>0&&Array.isArray(i.data[0].blog_posts)?i.data[0].blog_posts.length:0,hasPagination:!!i?.meta?.pagination,paginationTotal:i?.meta?.pagination?.total||0})),i}catch(t){throw console.error(`Error fetching author data for slug ${e}:`,t),t}}async function v({params:e,searchParams:t={}}){let r=await e,s=await b(r.slug,{page:1,pageSize:12});if(!s||!s.data||0===s.data.length)return console.error(`Author not found with slug: ${r.slug}`),(0,u.notFound)();let o=s.data[0];if(!o)return console.error(`Author entry missing in response for slug: ${r.slug}`),(0,u.notFound)();let h=Array.isArray(o.blog_posts)?o.blog_posts:[],m=null;if(console.log("Author profile picture data:",JSON.stringify(o.profilePicture||"No profile picture")),o.profilePicture&&(m="string"==typeof o.profilePicture?g(o.profilePicture):o.profilePicture.url?g(o.profilePicture.url):o.profilePicture.data?.attributes?.url?g(o.profilePicture.data.attributes.url):g(o.profilePicture)),!m&&process.env.IMAGE_HOSTNAME){let e=process.env.IMAGE_HOSTNAME;if(o.profilePicture?.data?.attributes?.name){let t=o.profilePicture.data.attributes.name;m=`${e}/${t}`}}return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-gray-100 py-3",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(l(),{href:"/",className:"hover:text-emerald-600",children:"Home"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)(l(),{href:"/blog",className:"hover:text-emerald-600",children:"Blog"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)(l(),{href:"/blog/authors",className:"hover:text-emerald-600",children:"Authors"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)("span",{className:"text-gray-800",children:o.name})]})})}),(0,a.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center gap-6",children:[(0,a.jsx)("div",{className:`relative h-24 w-24 rounded-full overflow-hidden ${!m?"bg-emerald-500":""} flex items-center justify-center flex-shrink-0`,children:m?(0,a.jsx)(d.default,{src:m,alt:o.name||"Author profile picture",width:96,height:96,className:"object-cover rounded-full",sizes:"96px"}):(0,a.jsx)(i.JXP,{className:"text-white text-4xl"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-2",children:o.name}),(0,a.jsx)("p",{className:"text-emerald-100",children:o.qualifications})]})]})})}),(0,a.jsx)("div",{className:"bg-white py-12 border-b",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-8 lg:gap-12",children:[(0,a.jsxs)("div",{className:"md:w-2/3",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:["About ",o.name]}),(0,a.jsx)("div",{className:"prose max-w-none text-gray-600",children:(0,a.jsx)("p",{children:o.bio||"No bio available."})})]}),(0,a.jsxs)("div",{className:"md:w-1/3 flex-shrink-0 bg-[#f9f9f9] p-6 rounded-[10px]",children:[" ",(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"Contact Information"}),(0,a.jsxs)("div",{className:"space-y-3 text-gray-600",children:[o.email&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",(0,a.jsx)("a",{href:`mailto:${o.email}`,className:"text-emerald-600 hover:underline",children:o.email})]}),o.website&&(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Website:"})," ",(0,a.jsx)("a",{href:o.website,target:"_blank",rel:"noopener noreferrer",className:"text-emerald-600 hover:underline",children:o.website})]}),!o.email&&!o.website&&(0,a.jsx)("p",{children:"No contact information provided."})]})]})]})})}),(0,a.jsx)(c.default,{author:o,initialPosts:h.map(e=>{let t=(0,n.Jf)(e.featuredImage);return console.log(`[Author Page] Post "${e.title}": Generated Image URL:`,t),{id:e.id.toString(),title:e.title,slug:e.slug,excerpt:e.excerpt,featured_image:t,publish_date:e.publishDate||e.publishedAt||new Date().toISOString(),author:{name:o.name,slug:o.slug,profile_picture:m}}}),totalPages:s.meta?.pagination?.pageCount||1,currentPage:1,authorProfilePicUrl:m})]})}},7610:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25614:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var a=r(60687),s=r(43210),l=r(55061),i=r(91936);function o({totalPages:e}){return e<=1?null:(0,a.jsx)("div",{className:"mt-12 flex justify-center",children:(0,a.jsx)(i.default,{totalPages:e})})}var n=r(17019),d=r(85814),c=r.n(d);function u(){return(0,a.jsx)("div",{className:"mt-12 h-10"})}function h({author:e,initialPosts:t,totalPages:r,currentPage:i,authorProfilePicUrl:d}){return(0,a.jsx)("div",{className:"bg-gray-50 py-12",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-8",children:r*(t.length/(i||1))>0?`${Math.ceil(r*(t.length/(i||1)))} Articles by ${e.name}`:`Articles by ${e.name}`}),t.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>{let t={...e,author:{...e.author,profile_picture:d||e.author.profile_picture}};return(0,a.jsx)(l.default,{post:t},e.id)})}):(0,a.jsx)("div",{className:"bg-white p-8 rounded-lg text-center shadow",children:(0,a.jsx)("p",{className:"text-gray-600",children:"This author hasn't published any articles yet."})}),(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)(u,{}),children:(0,a.jsx)(o,{totalPages:r})}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)(c(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 flex items-center justify-center",children:[(0,a.jsx)(n.kRp,{className:"mr-2"})," Back to All Articles"]})})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32016:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>n});var a=r(65239),s=r(48088),l=r(31369),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let n={children:["",{children:["blog",{children:["authors",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5973)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\authors\\[slug]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\authors\\[slug]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/blog/authors/[slug]/page",pathname:"/blog/authors/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},33873:e=>{"use strict";e.exports=require("path")},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},49384:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=function(){for(var e,t,r=0,a="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,a,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t)if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(a=e(t[r]))&&(s&&(s+=" "),s+=a)}else for(a in t)t[a]&&(s&&(s+=" "),s+=a);return s}(e))&&(a&&(a+=" "),a+=t);return a}},55061:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(60687),s=r(24587),l=r(85814),i=r.n(l),o=r(44867),n=r(17019),d=r(28136);let c=({post:e,showReadingTime:t=!1,showShareButton:r=!1,showBadge:l=!1})=>{var c;let u=(0,d.Jf)(e.featured_image),h=!!e.featured_image,m=(0,d.Jf)(e.author?.profile_picture);e.author?.profile_picture;let p=e.reading_time||(e.content?(c=e.content,Math.max(1,Math.ceil((c?.split(/\s+/)?.length||0)/200))):2),g=m&&(m.startsWith("http")||m.startsWith("/")||m.startsWith("data:"))?m:"",x=(0,o.GP)(new Date(e.publish_date),"MMMM d, yyyy"),f=Object.getOwnPropertyDescriptor(e,"view_count")?.value||0;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,a.jsxs)(i(),{href:`/blog/${e.slug}`,className:"block relative h-48 w-full overflow-hidden",children:[" ",h?(0,a.jsx)(s.default,{src:u,alt:e.title||"Blog post image",width:600,height:400,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1,showPlaceholder:!0,advancedBlur:!0,fadeIn:!0,preload:e.isFeatured||f>10}):(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-50 to-teal-100 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-emerald-700 font-semibold text-xl opacity-50",children:e.title.charAt(0)})}),l&&(!0===e.isFeatured||f>0)&&(0,a.jsx)("div",{className:`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ${!0===e.isFeatured?"bg-emerald-600 text-white":"bg-amber-500 text-white"}`,children:!0===e.isFeatured?"Featured Post":"Popular Post"})]}),(0,a.jsxs)("div",{className:"p-4 flex-grow flex flex-col",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:x}),t&&(0,a.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,a.jsx)(n.Ohp,{className:"mr-1"}),(0,a.jsxs)("span",{children:[p," min read"]})]})]}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800 flex-grow",children:(0,a.jsx)(i(),{href:`/blog/${e.slug}`,className:"hover:text-emerald-600 line-clamp-2",children:e.title})}),e.excerpt&&(0,a.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:e.excerpt}),e.author&&(0,a.jsxs)("div",{className:"flex items-center mt-auto pt-4 border-t border-gray-100",children:[" ",(0,a.jsxs)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-200 shadow-sm",children:[" ",g&&(e=>{try{return e&&(e.startsWith("http")||e.startsWith("/")||e.startsWith("data:"))}catch(e){return!1}})(g)?(0,a.jsx)(s.default,{src:g,alt:e.author.name||"Author image",width:40,height:40,fillContainer:!0,className:"object-cover rounded-full",sizes:"40px",showPlaceholder:!0,fadeIn:!0}):(0,a.jsx)("div",{className:"absolute inset-0 bg-emerald-100 flex items-center justify-center",children:(0,a.jsx)(n.JXP,{className:"text-emerald-700 text-lg"})})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"block text-xs text-gray-500 mb-0.5",children:"Written by"}),(0,a.jsx)(i(),{href:`/blog/authors/${e.author.slug}`,className:"font-medium text-gray-800 hover:text-emerald-600",children:e.author.name})]})]})]}),(0,a.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto flex justify-between items-center",children:[(0,a.jsx)(i(),{href:`/blog/${e.slug}`,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"Read More →"}),r&&(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors",onClick:t=>{t.preventDefault(),navigator.share?navigator.share({title:e.title,text:e.excerpt||"",url:`${window.location.origin}/blog/${e.slug}`}).catch(e=>{console.error("Error sharing:",e)}):navigator.clipboard.writeText(`${window.location.origin}/blog/${e.slug}`).then(()=>{alert("Link copied to clipboard!")}).catch(e=>{console.error("Could not copy text: ",e)})},"aria-label":"Share article",children:(0,a.jsx)(n.Pum,{size:18})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors","aria-label":"Save article",children:(0,a.jsx)(n.Y19,{size:18})})]})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62932:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23)),Promise.resolve().then(r.bind(r,98917))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91936:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(60687),s=r(44725),l=r(7610),i=r(49384),o=r(85814),n=r.n(o),d=r(16189);let c=(e,t)=>t<=7?Array.from({length:t},(e,t)=>t+1):e<=3?[1,2,3,"...",t-1,t]:e>=t-2?[1,2,"...",t-2,t-1,t]:[1,"...",e-1,e,e+1,"...",t];function u({totalPages:e,currentPage:t}){let r=(0,d.usePathname)(),s=(0,d.useSearchParams)(),l=void 0!==t?t:Number(s.get("page"))||1,i=e=>{let t=new URLSearchParams(s);return t.set("page",e.toString()),`${r}?${t.toString()}`},o=c(l,e);return e<=1?null:(0,a.jsxs)("div",{className:"inline-flex",children:[(0,a.jsx)(m,{direction:"left",href:i(l-1),isDisabled:l<=1}),(0,a.jsx)("div",{className:"flex -space-x-px",children:o.map((e,t)=>{let r;return 0===t&&(r="first"),t===o.length-1&&(r="last"),1===o.length&&(r="single"),"..."===e&&(r="middle"),(0,a.jsx)(h,{href:i(e),page:e,position:r,isActive:l===e},`${e}-${t}`)})}),(0,a.jsx)(m,{direction:"right",href:i(l+1),isDisabled:l>=e})]})}function h({page:e,href:t,isActive:r,position:s}){let l=(0,i.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===s||"single"===s,"rounded-r-md":"last"===s||"single"===s,"z-10 bg-emerald-600 border-emerald-600 text-white":r,"hover:bg-gray-100":!r&&"middle"!==s,"text-gray-300 pointer-events-none":"middle"===s});return r||"middle"===s?(0,a.jsx)("div",{className:l,children:e}):(0,a.jsx)(n(),{href:t,className:l,children:e})}function m({href:e,direction:t,isDisabled:r}){let o=(0,i.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":r,"hover:bg-gray-100":!r,"mr-2 md:mr-4":"left"===t,"ml-2 md:ml-4":"right"===t}),d="left"===t?(0,a.jsx)(s.A,{className:"w-4"}):(0,a.jsx)(l.A,{className:"w-4"});return r?(0,a.jsx)("div",{className:o,children:d}):(0,a.jsx)(n(),{className:o,href:e,children:d})}},94735:e=>{"use strict";e.exports=require("events")},98917:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\AuthorPostsList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\AuthorPostsList.tsx","default")},99316:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23)),Promise.resolve().then(r.bind(r,25614))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,1330,3376,6391,2975,4867,4959,8446,270,7880],()=>r(32016));module.exports=a})();