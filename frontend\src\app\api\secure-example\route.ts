import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { unauthorized } from 'next/navigation';

/**
 * Secure API route example with proper error handling
 * This demonstrates how to properly secure an API route in Next.js
 */
export async function GET(request: NextRequest) {
  try {
    // Get the JWT token from cookies
    const cookieStore = cookies();
    const token = cookieStore.get('jwt')?.value;
    
    // If no token exists, return unauthorized
    if (!token) {
      // Use Next.js built-in unauthorized function
      unauthorized();
    }
    
    // Verify the token (implement your token verification logic here)
    // This is a placeholder for your actual verification logic
    const isValidToken = await verifyToken(token);
    
    if (!isValidToken) {
      unauthorized();
    }
    
    // If authenticated, return the protected data
    return NextResponse.json(
      { 
        message: 'Authenticated successfully',
        data: {
          // Your protected data here
          example: 'This is protected data',
        }
      },
      { status: 200 }
    );
  } catch (error) {
    // Log the error but don't expose details to the client
    console.error('Error in secure API route:', error);
    
    // Return a generic error message
    return NextResponse.json(
      { error: 'An error occurred processing your request' },
      { status: 500 }
    );
  }
}

/**
 * Example token verification function
 * Replace this with your actual token verification logic
 */
async function verifyToken(token: string): Promise<boolean> {
  try {
    // In a real implementation, you would:
    // 1. Verify the JWT signature
    // 2. Check if the token is expired
    // 3. Validate any claims in the token
    
    // This is a placeholder - implement your actual verification logic
    // For example, you might call Strapi's /api/users/me endpoint with the token
    
    // For demonstration purposes only:
    return token.length > 10;
  } catch (error) {
    console.error('Token verification error:', error);
    return false;
  }
}
