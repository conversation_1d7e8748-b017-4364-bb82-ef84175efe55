'use client';

import React from 'react';

interface SkeletonLoaderProps {
  type?: 'card' | 'text' | 'image' | 'profile' | 'table';
  count?: number;
  className?: string;
}

/**
 * A reusable skeleton loader component that displays different types of loading placeholders.
 */
const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  type = 'text',
  count = 1,
  className = '',
}) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'card':
        return (
          <div className={`animate-pulse rounded-lg bg-gray-200 h-48 ${className}`}></div>
        );
      case 'image':
        return (
          <div className={`animate-pulse rounded bg-gray-200 h-32 ${className}`}></div>
        );
      case 'profile':
        return (
          <div className="animate-pulse flex items-center">
            <div className="rounded-full bg-gray-200 h-12 w-12"></div>
            <div className="ml-4 space-y-2 flex-1">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        );
      case 'table':
        return (
          <div className="animate-pulse space-y-2">
            <div className="h-8 bg-gray-200 rounded w-full"></div>
            <div className="h-8 bg-gray-200 rounded w-full"></div>
            <div className="h-8 bg-gray-200 rounded w-full"></div>
          </div>
        );
      case 'text':
      default:
        return (
          <div className={`animate-pulse space-y-2 ${className}`}>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        );
    }
  };

  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="mb-4">
          {renderSkeleton()}
        </div>
      ))}
    </>
  );
};

export default SkeletonLoader;
