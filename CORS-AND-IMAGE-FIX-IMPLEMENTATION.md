# CORS and Image URL Fix Implementation

This document outlines the changes made to fix CORS issues and image URL problems in the Natural Healing Now application.

## Issues Fixed

1. **CORS Configuration**: Updated the CORS configuration in Strapi to properly allow requests from the Vercel domain.
2. **Image URL Sanitization**: Enhanced the URL sanitization logic to fix issues with "undefined" being prepended to URLs and double URL problems.
3. **API Client Configuration**: Improved the API client configuration to better handle CORS and include proper headers.
4. **Next.js Middleware**: Updated the middleware to set appropriate CSP and CORS headers.

## Changes Made

### 1. Strapi CORS Configuration (`strapi-cms/config/middlewares.ts`)

Updated the CORS configuration to include all necessary Vercel domains and added the `Access-Control-Allow-Origin` header to the allowed headers list:

```javascript
{
  name: 'strapi::cors',
  config: {
    origin: [
      env('FRONTEND_URL', 'http://localhost:3000'),
      'http://localhost:3001', // Add localhost:3001 for development
      'https://naturalhealingnow-directory-2hysxhy6h.vercel.app', // Add your Vercel domain
      'https://naturalhealingnow-directory-lmdcdb3lh.vercel.app', // Current Vercel deployment
      'https://naturalhealingnow-directory-vvsaiv4st.vercel.app', // Latest Vercel deployment
      'https://*.vercel.app', // Allow all Vercel preview deployments
      '*', // Temporarily allow all origins for debugging
    ],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
    headers: ['Content-Type', 'Authorization', 'Origin', 'Accept', 'Access-Control-Allow-Origin'],
    keepHeaderOnError: true,
    credentials: true,
    // Increase the max age to reduce preflight requests
    maxAge: 86400, // 24 hours
  },
}
```

### 2. Enhanced URL Sanitization (`frontend/src/lib/mediaUtils.ts`)

Improved the `sanitizeUrl` function to handle various URL issues:

```javascript
export function sanitizeUrl(url: string | null | undefined): string {
  if (!url) return '';

  // Remove 'undefined' prefix if present
  let sanitized = url.replace(/^undefined/i, '').trim();

  // Fix double domain issues with Strapi Cloud
  sanitized = sanitized.replace(/nice-badge-2130241d6c\.strapiapp\.com\/nice-badge-2130241d6c\.media\.strapiapp\.com/g, 
                               'nice-badge-2130241d6c.media.strapiapp.com').trim();

  // Fix double URLs (e.g., https://domain.comhttps://domain.com)
  sanitized = sanitized.replace(/^(https?:\/\/[^\/]+)(https?:\/\/)/, '$1/');

  // Fix URLs with slashes between domains
  sanitized = sanitized.replace(/(https?:\/\/[^\/]+)\/+(https?:\/\/)/, '$1/');

  // Handle double slashes in URLs (except after protocol)
  sanitized = sanitized.replace(/([^:])\/\//g, '$1/');

  // Fix Strapi media URLs that have incorrect domain structure
  if (sanitized.includes('strapiapp.com') && !sanitized.startsWith('http')) {
    sanitized = `https://${sanitized}`;
  }

  return sanitized;
}
```

### 3. Enhanced Media URL Handling (`frontend/src/lib/mediaUtils.ts`)

Updated the `getStrapiMediaUrl` function to better handle Strapi Cloud media URLs:

```javascript
// Special case for Strapi Cloud media URLs
if (sanitizedUrl.includes('media.strapiapp.com')) {
  // Ensure it has https:// prefix
  if (!sanitizedUrl.startsWith('http')) {
    return `https://${sanitizedUrl}`;
  }
  return sanitizedUrl;
}
```

### 4. Improved LazyImage Component (`frontend/src/components/shared/LazyImage.tsx`)

Enhanced the URL sanitization in the LazyImage component:

```javascript
const sanitizeUrl = (url: string): string => {
  if (!url) return '';

  // Remove 'undefined' prefix if present
  let sanitized = url.replace(/^undefined/i, '').trim();

  // Fix double domain issues with Strapi Cloud
  sanitized = sanitized.replace(/nice-badge-2130241d6c\.strapiapp\.com\/nice-badge-2130241d6c\.media\.strapiapp\.com/g, 
                               'nice-badge-2130241d6c.media.strapiapp.com').trim();

  // Fix double URLs (e.g., https://domain.comhttps://domain.com)
  sanitized = sanitized.replace(/^(https?:\/\/[^\/]+)(https?:\/\/)/, '$1/');

  // Fix URLs with slashes between domains
  sanitized = sanitized.replace(/(https?:\/\/[^\/]+)\/+(https?:\/\/)/, '$1/');

  // Handle double slashes in URLs (except after protocol)
  sanitized = sanitized.replace(/([^:])\/\//g, '$1/');

  // Fix Strapi media URLs that have incorrect domain structure
  if (sanitized.includes('strapiapp.com') && !sanitized.startsWith('http')) {
    sanitized = `https://${sanitized}`;
  }

  return sanitized;
};
```

### 5. Updated Next.js Middleware (`frontend/src/middleware.ts`)

Enhanced the middleware to better handle CORS and CSP headers:

```javascript
// Log the Strapi URLs being used
console.log('Middleware using Strapi URLs:', { strapiUrl, strapiMediaUrl });

// In production, use a more permissive CSP to avoid blocking resources
response.headers.set(
  'Content-Security-Policy',
  `default-src 'self';
   script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.vercel.app https://*.strapiapp.com;
   style-src 'self' 'unsafe-inline' https://*.vercel.app https://*.strapiapp.com;
   img-src 'self' data: blob: ${strapiUrl} ${strapiMediaUrl} https://*.strapiapp.com https://*.media.strapiapp.com https://*.vercel.app;
   font-src 'self' data: https://*.vercel.app;
   connect-src 'self' ${strapiUrl} ${strapiMediaUrl} https://*.strapiapp.com https://*.media.strapiapp.com https://*.vercel.app https:;
   frame-ancestors 'none';
   form-action 'self';
   base-uri 'self';
   object-src 'none';`.replace(/\s+/g, ' ')
);

// Add CORS headers to allow requests from Strapi
response.headers.set('Access-Control-Allow-Origin', '*');
response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, Origin, Accept');
```

### 6. Enhanced API Client Configuration (`frontend/src/lib/strapi.ts`)

Updated the API client configuration to include proper headers and handle CORS:

```javascript
// Create a configured axios instance for Strapi
export const strapiApi = axios.create({
  // Use the effective API URL with /api path
  baseURL: EFFECTIVE_API_URL ? `${EFFECTIVE_API_URL}/api` : '/api',
  headers: {
    'Content-Type': 'application/json',
    'Origin': typeof window !== 'undefined' ? window.location.origin : 'https://naturalhealingnow-directory.vercel.app',
  },
  // Add timeout to prevent hanging requests
  timeout: 15000, // 15 seconds - increased for potential network issues
  // Add withCredentials for CORS with credentials
  withCredentials: false, // Set to true only if your Strapi server is configured to accept credentials
});
```

## Deployment Steps

1. **Deploy Strapi Changes**:
   - Update the CORS configuration in Strapi Cloud
   - Restart the Strapi application

2. **Deploy Next.js Changes**:
   - Push the changes to your repository
   - Deploy the Next.js application on Vercel

3. **Verify the Fix**:
   - Check that images are loading correctly
   - Verify that API requests are working without CORS errors
   - Check the browser console for any remaining errors

## Security Considerations

The current configuration includes a temporary wildcard (`*`) for CORS origins. After verifying that everything works correctly, you should remove this wildcard and only allow specific domains:

```javascript
origin: [
  env('FRONTEND_URL', 'http://localhost:3000'),
  'http://localhost:3001',
  'https://naturalhealingnow-directory-2hysxhy6h.vercel.app',
  'https://naturalhealingnow-directory-lmdcdb3lh.vercel.app',
  'https://naturalhealingnow-directory-vvsaiv4st.vercel.app',
  'https://*.vercel.app',
  // Remove the wildcard '*' after testing
],
```
