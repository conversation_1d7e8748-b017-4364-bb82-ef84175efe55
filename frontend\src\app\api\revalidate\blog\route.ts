import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag } from 'next/cache';
import logger from '@/lib/logger'; // Assuming you have a logger utility

// Ensure the secret token is set in environment variables
const REVALIDATE_SECRET = process.env.STRAPI_REVALIDATE_SECRET || process.env.REVALIDATE_TOKEN || process.env.PREVIEW_SECRET;

export async function POST(request: NextRequest) {
  if (!REVALIDATE_SECRET) {
    logger.error('CRITICAL: STRAPI_REVALIDATE_SECRET, REVALIDATE_TOKEN, or PREVIEW_SECRET is not set. Blog revalidation endpoint is disabled.');
    return NextResponse.json({ message: 'Revalidation secret not configured.' }, { status: 500 });
  }

  const secretFromHeader = request.headers.get('X-Revalidate-Secret');

  if (secretFromHeader !== REVALIDATE_SECRET) {
    logger.warn('Invalid blog revalidation attempt: Incorrect or missing X-Revalidate-Secret header.');
    return NextResponse.json({ message: 'Invalid token' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { model, entry } = body; // Strapi v4/v5 webhook payload structure

    // Ensure this webhook is for the 'blog-post' model (or whatever your blog post collection type is named in Strapi)
    // Adjust 'blog-post' if your Strapi collection API ID is different (e.g., 'blogs', 'posts')
    if (model !== 'blog-post') {
      logger.info(`Revalidation skipped: Not a blog-post model. Model received: ${model}`);
      return NextResponse.json({ message: `Revalidation skipped: Not a blog-post model. Received: ${model}` }, { status: 200 });
    }

    const tagsToRevalidate: string[] = [];

    if (entry && entry.slug) {
      const blogPostSlug = entry.slug;
      const blogPostTag = `strapi-blog-post-${blogPostSlug}`;
      tagsToRevalidate.push(blogPostTag);
      logger.info(`Attempting to revalidate tag: ${blogPostTag} for blog post slug: ${blogPostSlug}`);
    }

    // Always revalidate the list of blog post slugs
    tagsToRevalidate.push('strapi-blog-posts-slugs');
    logger.info(`Attempting to revalidate tag: strapi-blog-posts-slugs`);

    // Also revalidate general blog listing pages, like /blog or category/tag pages if they use tags
    tagsToRevalidate.push('strapi-blog-posts'); // For general blog roll
    tagsToRevalidate.push('strapi-blog-posts-featured'); // For featured posts
    tagsToRevalidate.push('strapi-blog-posts-recent'); // For recent posts

    // Revalidate the first few pages of the blog
    for (let i = 1; i <= 5; i++) {
      tagsToRevalidate.push(`page-${i}`);
    }

    // Revalidate categories
    if (entry?.blog_categories && Array.isArray(entry.blog_categories)) {
      entry.blog_categories.forEach((category: any) => {
        if (category.slug) {
          tagsToRevalidate.push(`strapi-category-${category.slug}`);
        }
      });
    }

    // Revalidate tags
    if (entry?.blog_tags && Array.isArray(entry.blog_tags)) {
      entry.blog_tags.forEach((tag: any) => {
        if (tag.slug) {
          tagsToRevalidate.push(`strapi-tag-${tag.slug}`);
        }
      });
    }

    // Also revalidate blog categories and tags collections
    tagsToRevalidate.push('strapi-blog-categories');
    tagsToRevalidate.push('strapi-blog-tags');


    if (tagsToRevalidate.length > 0) {
      for (const tag of tagsToRevalidate) {
        revalidateTag(tag);
      }
      logger.info('Blog revalidation successful for tags:', tagsToRevalidate.join(', '));
      return NextResponse.json({ revalidated: true, revalidatedTags: tagsToRevalidate, timestamp: new Date().toISOString() });
    } else {
      logger.info('No specific blog post slug found in webhook payload for revalidation, but revalidated general blog tags.');
      // Still revalidate general tags even if no specific slug
      revalidateTag('strapi-blog-posts-slugs');
      revalidateTag('strapi-blog-posts');
      return NextResponse.json({ revalidated: true, message: 'General blog tags revalidated.' });
    }

  } catch (error: any) {
    logger.error('Error during blog revalidation:', error);
    return NextResponse.json({ message: 'Error revalidating blog posts', error: error.message }, { status: 500 });
  }
}

// Optional: GET handler for testing or manual trigger if needed
export async function GET(request: NextRequest) {
    if (!REVALIDATE_SECRET) {
        return NextResponse.json({ message: 'Revalidation secret not configured.' }, { status: 500 });
    }

    const secretFromHeader = request.headers.get('X-Revalidate-Secret');
    const requestUrl = new URL(request.url);
    const tag = requestUrl.searchParams.get('tag');

    if (secretFromHeader !== REVALIDATE_SECRET) {
        const secretFromQuery = requestUrl.searchParams.get('secret');
        if (secretFromQuery !== REVALIDATE_SECRET) {
            logger.warn('Invalid GET blog revalidation attempt: Incorrect or missing X-Revalidate-Secret header/secret query param.');
            return NextResponse.json({ message: 'Invalid token' }, { status: 401 });
        }
    }

    if (!tag) {
        return NextResponse.json({ message: 'Missing tag parameter for GET revalidation' }, { status: 400 });
    }

    try {
        revalidateTag(tag);
        logger.info(`Manual blog revalidation successful for tag: ${tag}`);
        return NextResponse.json({ revalidated: true, revalidatedTag: tag, timestamp: new Date().toISOString() });
    } catch (error: any) {
        logger.error(`Error during manual blog revalidation for tag ${tag}:`, error);
        return NextResponse.json({ message: 'Error revalidating blog tag', error: error.message }, { status: 500 });
    }
}
