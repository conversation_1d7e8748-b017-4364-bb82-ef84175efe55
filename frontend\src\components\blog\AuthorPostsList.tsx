'use client';

import { Suspense } from 'react';
import BlogPostCard from '@/components/blog/BlogPostCard';
import AuthorPagePagination from '@/components/blog/AuthorPagePagination';
import { FiArrowLeft } from 'react-icons/fi';
import Link from 'next/link';

// Define interfaces for props
interface Post {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  featured_image?: string | null;
  publish_date: string;
  author: {
    name: string;
    slug: string;
    profile_picture?: string | null;
  };
}

interface AuthorAttributes {
  name: string;
  // Add other author attributes if needed by this component
}

interface AuthorPostsListProps {
  author: AuthorAttributes;
  initialPosts: Post[];
  totalPages: number;
  currentPage: number; // For initial pagination state
  authorProfilePicUrl?: string | null; // Pass down for BlogPostCard
}

// Fallback component for Suspense
function PaginationFallback() {
  return <div className="mt-12 h-10" />; // Placeholder with similar height to pagination
}

export default function AuthorPostsList({
  author,
  initialPosts,
  totalPages,
  currentPage,
  authorProfilePicUrl,
}: AuthorPostsListProps) {
  // In a real scenario with client-side fetching for pagination,
  // you might fetch posts here based on the current page from useSearchParams.
  // For this example, we'll use the initialPosts for the current page.
  // The AuthorPagePagination component will handle URL updates.

  const postsToDisplay = initialPosts; // In a full client-side setup, this would update

  return (
    <div className="bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <h2 className="text-2xl font-bold text-gray-800 mb-8">
          {totalPages * (initialPosts.length / (currentPage || 1)) > 0 // Estimate total posts
            ? `${Math.ceil(totalPages * (initialPosts.length / (currentPage || 1)))} Articles by ${author.name}`
            : `Articles by ${author.name}`}
        </h2>

        {postsToDisplay.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {postsToDisplay.map((post) => {
              // Ensure the author object for BlogPostCard has the profile picture
              const postForCard = {
                ...post,
                author: {
                  ...post.author,
                  profile_picture: authorProfilePicUrl || post.author.profile_picture,
                },
              };
              return <BlogPostCard key={post.id} post={postForCard} />;
            })}
          </div>
        ) : (
          <div className="bg-white p-8 rounded-lg text-center shadow">
            <p className="text-gray-600">This author hasn't published any articles yet.</p>
          </div>
        )}

        <Suspense fallback={<PaginationFallback />}>
          <AuthorPagePagination totalPages={totalPages} />
        </Suspense>

        {/* Back to Blog Link */}
        <div className="mt-8 text-center">
          <Link
            href="/blog"
            className="text-emerald-600 hover:text-emerald-700 flex items-center justify-center"
          >
            <FiArrowLeft className="mr-2" /> Back to All Articles
          </Link>
        </div>
      </div>
    </div>
  );
}
