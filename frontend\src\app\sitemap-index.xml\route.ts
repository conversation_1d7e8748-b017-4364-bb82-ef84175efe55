// Define the site URL from environment variable
// We need an absolute URL for sitemaps to work properly
// For sitemaps, we should use the frontend URL, not the Strapi API URL
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log a warning if SITE_URL is not set
if (!process.env.NEXT_PUBLIC_SITE_URL && !process.env.NEXT_PUBLIC_API_URL) {
  console.warn('WARNING: Neither NEXT_PUBLIC_SITE_URL nor NEXT_PUBLIC_API_URL environment variables are set. Using default vercel.app URL as a placeholder.');
}

// Log the URL being used for debugging
console.log(`Using site URL for sitemap index: ${SITE_URL}`);

// Ensure SITE_URL doesn't have a trailing slash
const normalizedSiteUrl = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;

// This is a Route Handler for Next.js App Router
// See: https://nextjs.org/docs/app/building-your-application/routing/route-handlers
export async function GET(_request: Request): Promise<Response> {
  try {
    console.log('Generating sitemap index...');

    // Generate the sitemap index XML with proper XML declaration
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${normalizedSiteUrl}/sitemap.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${normalizedSiteUrl}/sitemap-blog.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${normalizedSiteUrl}/sitemap-clinics.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${normalizedSiteUrl}/sitemap-practitioners.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
</sitemapindex>`;

    // Return the XML with the correct content type
    // Explicitly set the Content-Type header to application/xml
    return new Response(xml, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Content-Type-Options': 'nosniff',
      },
    });
  } catch (error) {
    console.error('Error generating sitemap index:', error);

    // Return a basic XML in case of error
    const errorXml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${normalizedSiteUrl}/sitemap.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
</sitemapindex>`;

    return new Response(errorXml, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'no-cache', // Don't cache error responses
        'X-Content-Type-Options': 'nosniff',
      },
    });
  }
}
