(()=>{var e={};e.id=3831,e.ids=[3831],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7610:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},10592:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\SearchInput.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\SearchInput.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14256:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\BlogContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\BlogContent.tsx","default")},18543:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(60687),s=r(85814),n=r.n(s),i=r(16189);let l=({categories:e})=>{let t=(0,i.useSearchParams)().get("category")||"";return(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Browse by:"}),(0,a.jsx)(n(),{href:"/blog",className:`px-3 py-1 rounded-full text-sm ${!t?"bg-emerald-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"}`,children:"All"}),e.filter(e=>e.count>0).map(e=>(0,a.jsx)(n(),{href:`/blog?category=${e.slug}`,className:`px-3 py-1 rounded-full text-sm ${t===e.slug?"bg-emerald-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"}`,children:e.name},e.id))]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22618:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var a=r(65239),s=r(48088),n=r(31369),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let o={children:["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30371)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30371:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y,dynamicParams:()=>h,generateStaticParams:()=>f,revalidate:()=>m});var a=r(37413),s=r(58446),n=r(71397),i=r(10592),l=r(61120),o=r(87089),d=r(36463);async function c(e=4,t="week"){try{let t=process.env.STRAPI_API_TOKEN;if(!t)return d.Ay.error("STRAPI_API_TOKEN environment variable is not set"),[];let r="https://nice-badge-2130241d6c.strapiapp.com";d.Ay.info("Fetching recent posts as fallback for popular posts");let a=await fetch(`${r}/api/blog-posts?sort=publishDate:desc&pagination[limit]=${e}`,{headers:{Authorization:`Bearer ${t}`},next:{revalidate:3600}});if(!a.ok)throw Error(`Failed to fetch recent posts: ${a.statusText}`);let s=await a.json();if(s&&s.data&&Array.isArray(s.data)&&s.data.length>0)return s.data.map(e=>{if(!e||!e.attributes)return d.Ay.warn("Invalid post data structure:",e),null;let t=e.attributes;return{id:e.id,title:t.title||"Untitled Post",slug:t.slug||`post-${e.id}`,excerpt:t.excerpt||"",featured_image:t.featuredImage?.data?.attributes?.url?`${r}${t.featuredImage.data.attributes.url}`:null,publish_date:t.publishDate||t.createdAt||new Date().toISOString(),view_count:0,isFeatured:t.isFeatured||!1}}).filter(Boolean);return d.Ay.info("No posts found, returning empty array"),[]}catch(e){return d.Ay.error("Error fetching posts:",e),[]}}var u=r(82158),g=r(54920),p=r(14256);let m=!1,h=!0;async function f(){try{let e=[1,2,3].map(e=>({searchParams:{page:e.toString()}}));e.push({searchParams:{page:""}});let t=await (0,g.tz)();return t?.data&&t.data.map(e=>b(e)).filter(e=>null!==e&&""!==e.id).sort((e,t)=>t.count-e.count).slice(0,5).forEach(t=>{e.push({searchParams:{page:"",category:t.slug}})}),d.Ay.debug(`Pre-rendering ${e.length} blog page variants`),e}catch(e){return d.Ay.error("Error generating static params for blog page:",e),[{searchParams:{}}]}}function x(e){var t;if(!e||!e.id)return d.Ay.warn("Received invalid post data in mapStrapiBlogPostToProps:",{postData:e}),{id:e?.id||`invalid-${Date.now()}`,title:"Invalid Post Data",slug:`invalid-post-${e?.id||Date.now()}`,excerpt:null,featured_image:null,publish_date:new Date().toISOString(),author:null,reading_time:0,content:""};let r=e.author_blogs?.[0],a=e.content||"",s=(t=a||((e.excerpt||"")+" "+(e.title||"")).repeat(5),Math.max(1,Math.ceil((t?.split(/\s+/)?.length||0)/200))),n={id:e.id,title:e.title||"Untitled Post",slug:e.slug||`post-${e.id}`,excerpt:e.excerpt||null,featured_image:(0,u.Z5)(e.featuredImage),publish_date:e.publishDate||e.createdAt||new Date().toISOString(),isFeatured:e.isFeatured||!1,reading_time:s,content:a,author:r?{name:r.name||"Unknown Author",slug:r.slug||"unknown-author",profile_picture:(0,u.Z5)(r.profilePicture)}:null};return Object.defineProperty(n,"view_count",{value:e.view_count||0,enumerable:!1}),n}function b(e){return e&&e.id?{id:e.id,name:e.name,slug:e.slug,count:e.blog_posts?.length||0}:(d.Ay.warn("Received invalid category data in mapStrapiBlogCategoryToProps:",{categoryData:e}),{id:e?.id||`invalid-${Date.now()}`,name:"Invalid Category Data",slug:`invalid-category-${e?.id||Date.now()}`,count:0})}async function y({searchParams:e}){let t=[],r=null,u=[],m=[],h=[],f=null,y=1;try{let e=await (0,g.g6)({page:1,pageSize:6,next:{tags:["strapi-blog-posts","page-1"]}}),a=await (0,g.Sk)({next:{tags:["strapi-blog-posts-featured"]}}),s=await (0,g.g6)({page:1,pageSize:4,next:{tags:["strapi-blog-posts","strapi-blog-posts-recent"]}});d.Ay.debug("Raw postsResponse:",{data:e}),e?.data?(e.data.length>0&&d.Ay.debug("First post data structure (from postsResponse):",{post:e.data[0]}),t=e.data.map(e=>x(e)).filter(e=>null!==e&&""!==e.id)):d.Ay.error("Invalid postsResponse structure or no data",{postsResponse:e});let n=[];if(a?.data)if(n=a.data.map(e=>x(e)).filter(e=>null!==e&&""!==e.id),d.Ay.debug(`Found ${n.length} manually featured posts`),n.length>1){let e=new Date,t=Math.floor((e.getTime()-new Date(e.getFullYear(),0,0).getTime())/864e5),a=t%n.length;r=n[a],d.Ay.debug(`Rotating featured posts: Day ${t}, showing post index ${a}`)}else 1===n.length&&(r=n[0]);if(!r)try{d.Ay.debug("No manually featured posts, trying to get most popular post from analytics");let e=await c(1,"month");e?.length>0&&(d.Ay.debug("Using most popular post from analytics as featured"),r=e[0])}catch(e){d.Ay.error("Error fetching popular post for featuring from analytics:",{error:e})}!r&&t.length>0&&(d.Ay.debug("No featured or popular (analytics) posts found, using most recent post from current list"),r=t[0]),r&&(t=t.filter(e=>e.id!==r.id));try{d.Ay.debug("Attempting to fetch popular posts from analytics");let e=await c(4,"all");if(e?.length>0)u=e.filter(e=>e.id!==r?.id).slice(0,4),d.Ay.debug("Using popular posts from analytics",{count:u.length});else throw Error("No popular posts from analytics")}catch(e){d.Ay.warn("Failed to get popular posts from analytics, falling back to recent posts",{error:e}),s?.data&&(u=s.data.map(e=>x(e)).filter(e=>null!==e&&""!==e.id&&e.id!==r?.id).slice(0,4),d.Ay.debug("Using recent posts as popular posts fallback",{count:u.length}))}let i=e?.meta?.pagination?.total||0;y=Math.ceil(i/6);let l=await (0,g.tz)({next:{tags:["strapi-blog-categories","strapi-categories"]}});d.Ay.debug("Raw categoriesResponse:",{data:l}),l?.data?m=l.data.map(e=>b(e)).filter(e=>null!==e&&""!==e.id):d.Ay.error("Invalid categoriesResponse structure or no data",{categoriesResponse:l});let o=await (0,g.U0)({next:{tags:["strapi-blog-tags","strapi-tags"]}});d.Ay.debug("Raw tagsResponse:",{data:o}),o?.data?h=o.data.map(e=>e?.name?e.name:(d.Ay.warn(`Blog tag with ID ${e?.id} is missing name. Skipping.`),null)).filter(e=>null!==e):d.Ay.error("Invalid tagsResponse structure or no data",{tagsResponse:o});let p=await (0,g.TW)({next:{tags:["strapi-global-setting","strapi-blog-homepage"]}});p?.data?f=p.data:d.Ay.warn("Blog homepage data not found or invalid structure",{blogHomepageResponse:p})}catch(e){d.Ay.error("Error fetching blog data in BlogPage:",{error:e}),e.response?(d.Ay.error("Response status:",e.response.status),d.Ay.error("Response data:",e.response.data)):e.request?d.Ay.error("No response received. Request details:",e.request):d.Ay.error("Error details:",e.message),r=null,u=[],m=[],h=[],f=null,y=1,console.error("Failed to load blog data. Please try again later.")}let v=f?s.$.seo.getMetadata(f):null,j=v?{defaultTitle:v.title||"Natural Healing Blog",defaultDescription:v.description||"Insights, tips, and information about holistic health approaches and natural healing.",defaultOgImage:v.openGraph?.image||"",seo:{metaTitle:v.title,metaDescription:v.description,metaRobots:v.metaRobots,structuredData:v.structuredData,canonicalURL:v.canonicalURL,metaSocial:[v.openGraph&&{socialNetwork:"Facebook",title:v.openGraph.title,description:v.openGraph.description},v.twitter&&{socialNetwork:"Twitter",title:v.twitter.title,description:v.twitter.description}].filter(Boolean)}}:{defaultTitle:"Natural Healing Blog",defaultDescription:"Insights, tips, and information about holistic health approaches and natural healing."};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.default,{...j}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-emerald-700 to-teal-600 text-white py-16",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:"Natural Healing Blog"}),(0,a.jsx)("p",{className:"text-xl opacity-90 mb-8",children:"Discover insights, tips, and expert advice on holistic health approaches and natural healing methods."}),(0,a.jsx)("div",{className:"max-w-lg mx-auto",children:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg bg-gray-100 animate-pulse",children:(0,a.jsx)("span",{className:"sr-only",children:"Loading search..."})}),children:(0,a.jsx)(i.default,{placeholder:"Search for topics, remedies, or health concerns..."})})})]})})}),(0,a.jsx)("div",{className:"border-b",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsx)(l.Suspense,{fallback:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,a.jsx)("span",{className:"font-medium text-gray-700",children:"Browse by:"}),(0,a.jsx)("div",{className:"px-3 py-1 rounded-full text-sm bg-gray-100 animate-pulse w-16"}),(0,a.jsx)("div",{className:"px-3 py-1 rounded-full text-sm bg-gray-100 animate-pulse w-20"}),(0,a.jsx)("div",{className:"px-3 py-1 rounded-full text-sm bg-gray-100 animate-pulse w-24"})]}),children:(0,a.jsx)(o.default,{categories:m})})})}),(0,a.jsx)(l.Suspense,{fallback:(0,a.jsx)("div",{className:"py-12",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-12",children:[(0,a.jsxs)("div",{className:"lg:w-2/3",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-8"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden h-96 animate-pulse",children:[(0,a.jsx)("div",{className:"h-48 bg-gray-200"}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]})]},t))})]}),(0,a.jsx)("div",{className:"lg:w-1/3 space-y-8",children:(0,a.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6 animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,a.jsx)("div",{className:"h-12 w-12 bg-gray-200 rounded"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t))})]})})]})})}),children:(0,a.jsx)(p.default,{initialPosts:t,initialFeaturedPost:r,initialPopularPosts:u,categories:m,tags:h,totalPages:y})})]})}},33873:e=>{"use strict";e.exports=require("path")},38868:(e,t,r)=>{Promise.resolve().then(r.bind(r,53400)),Promise.resolve().then(r.bind(r,18543)),Promise.resolve().then(r.bind(r,68731)),Promise.resolve().then(r.bind(r,68016))},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},49384:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=function(){for(var e,t,r=0,a="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,a,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t)if(Array.isArray(t)){var n=t.length;for(r=0;r<n;r++)t[r]&&(a=e(t[r]))&&(s&&(s+=" "),s+=a)}else for(a in t)t[a]&&(s&&(s+=" "),s+=a);return s}(e))&&(a&&(a+=" "),a+=t);return a}},53400:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var a=r(60687),s=r(16189),n=r(55061),i=r(91936),l=r(43210),o=r(58101),d=r(47365),c=r(17019);let u=()=>{let[e,t]=(0,l.useState)(""),[r,s]=(0,l.useState)(!1),[n,i]=(0,l.useState)(!1),[o,d]=(0,l.useState)(""),u=async r=>{if(r.preventDefault(),!e||!/^\S+@\S+\.\S+$/.test(e))return void d("Please enter a valid email address");d(""),s(!0);try{await new Promise(e=>setTimeout(e,1e3)),i(!0),t("")}catch(e){d("Something went wrong. Please try again.")}finally{s(!1)}};return(0,a.jsxs)("div",{className:"bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-6 border border-emerald-100",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Get Wellness Tips"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Subscribe to receive the latest health insights and natural healing tips directly in your inbox."}),n?(0,a.jsxs)("div",{className:"bg-emerald-100 text-emerald-700 p-4 rounded-lg flex items-center",children:[(0,a.jsx)(c.YrT,{className:"mr-2 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-sm",children:"Thank you for subscribing! Check your inbox for a confirmation email."})]}):(0,a.jsxs)("form",{onSubmit:u,className:"space-y-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(c.pHD,{className:"text-gray-400"})}),(0,a.jsx)("input",{type:"email",placeholder:"Your email address",className:`w-full pl-10 pr-4 py-2 border ${o?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 focus:ring-emerald-500 focus:border-emerald-500"} rounded-lg`,value:e,onChange:e=>t(e.target.value),disabled:r})]}),o&&(0,a.jsx)("p",{className:"text-red-500 text-xs",children:o}),(0,a.jsxs)("button",{type:"submit",className:"w-full bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed",disabled:r,children:[r?"Subscribing...":"Subscribe"," ",(0,a.jsx)("span",{className:"ml-2",children:"→"})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-3",children:"We respect your privacy. Unsubscribe at any time."})]})]})};var g=r(85814),p=r.n(g);let m=({tags:e})=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-4 flex items-center",children:[(0,a.jsx)(c.cnX,{className:"mr-2 text-emerald-600"}),"Popular Topics"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,a.jsx)(p(),{href:`/blog/tags/${e.toLowerCase().replace(/\s+/g,"-")}`,className:"px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 transition-colors",children:e},e))})]});function h({initialPosts:e,initialFeaturedPost:t,initialPopularPosts:r,categories:c,tags:g,totalPages:h}){let f=(0,s.useSearchParams)(),x=(0,s.usePathname)(),b=(0,s.useRouter)(),y=(0,l.useMemo)(()=>f.get("query")||"",[f]),v=(0,l.useMemo)(()=>f.get("category")||"",[f]);(0,l.useMemo)(()=>Number(f.get("page"))||1,[f]);let[j,w]=(0,l.useState)(e),[N,A]=(0,l.useState)(t),[P,S]=(0,l.useState)(h),[C,R]=(0,l.useState)(!1),k=(0,l.useMemo)(()=>!y&&!v&&null!==N,[y,v,N]);return(0,l.useMemo)(()=>c.find(e=>e.slug===v),[c,v]),(0,l.useCallback)((e,t)=>{let r=new URLSearchParams(f.toString());t?(r.set(e,t),r.delete("page")):r.delete(e),b.push(`${x}?${r.toString()}`)},[x,b,f]),(0,a.jsxs)(a.Fragment,{children:[k&&N&&(0,a.jsx)("div",{className:"bg-gray-50 py-12",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)(o.A,{post:N,badgeType:!0===N.isFeatured?"featured":"number"==typeof N.view_count&&N.view_count>0?"popular":"recent"})})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-12",children:[(0,a.jsxs)("div",{className:"lg:w-2/3",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6 flex items-center",children:y?(0,a.jsxs)(a.Fragment,{children:['Search Results for "',y,'"']}):v?(0,a.jsxs)(a.Fragment,{children:["Articles in ",c.find(e=>e.slug===v)?.name||v]}):(0,a.jsx)(a.Fragment,{children:"Latest Articles"})}),j.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:j.map(e=>(0,a.jsx)(n.default,{post:e,showReadingTime:!0,showShareButton:!0,showBadge:!0},e.id))}):(0,a.jsx)("div",{className:"text-center p-8 bg-gray-50 rounded-lg",children:(0,a.jsx)("p",{className:"text-gray-500",children:y?`No blog posts found matching "${y}".`:v?"No articles found in this category.":"No blog posts found. Check back soon!"})}),(0,a.jsx)("div",{className:"mt-12 flex justify-center",children:(0,a.jsx)(i.default,{totalPages:P})})]}),(0,a.jsxs)("div",{className:"lg:w-1/3 space-y-8",children:[r.length>0&&(0,a.jsx)(d.A,{posts:r}),(0,a.jsx)(u,{}),g.length>0&&(0,a.jsx)(m,{tags:g})]})]}),c.filter(e=>e.count>0).length>0&&!y&&!v&&(0,a.jsx)("div",{className:"mt-16 py-16 bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-8 text-center",children:"Explore Topics"}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-8",children:c.filter(e=>e.count>0).slice(0,10).map(e=>(0,a.jsx)(p(),{href:`/blog?category=${e.slug}`,className:"group block",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden transition-all group-hover:shadow-md group-hover:-translate-y-1",children:[(0,a.jsx)("div",{className:"h-32 bg-emerald-100 relative",children:(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-emerald-700 text-2xl font-semibold",children:e.name.charAt(0)})})}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 group-hover:text-emerald-600 text-center",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-2 text-center",children:[e.count," ",1===e.count?"article":"articles"]})]})]})},e.id))}),c.filter(e=>e.count>0).length>10&&(0,a.jsx)("div",{className:"text-center mt-10",children:(0,a.jsx)(p(),{href:"/blog/categories",className:"inline-flex items-center justify-center px-5 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors",children:"View All Topics"})})]})})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68016:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(60687),s=r(43210),n=r(16189),i=r(17019);function l({placeholder:e,paramName:t="query",icon:r}){let l=(0,n.useSearchParams)(),o=(0,n.usePathname)(),{replace:d}=(0,n.useRouter)(),c=function(e,t,r){var a=this,n=(0,s.useRef)(null),i=(0,s.useRef)(0),l=(0,s.useRef)(null),o=(0,s.useRef)([]),d=(0,s.useRef)(),c=(0,s.useRef)(),u=(0,s.useRef)(e),g=(0,s.useRef)(!0);u.current=e;var p="undefined"!=typeof window,m=!t&&0!==t&&p;if("function"!=typeof e)throw TypeError("Expected a function");t=+t||0;var h=!!(r=r||{}).leading,f=!("trailing"in r)||!!r.trailing,x="maxWait"in r,b="debounceOnServer"in r&&!!r.debounceOnServer,y=x?Math.max(+r.maxWait||0,t):null;return(0,s.useEffect)(function(){return g.current=!0,function(){g.current=!1}},[]),(0,s.useMemo)(function(){var e=function(e){var t=o.current,r=d.current;return o.current=d.current=null,i.current=e,c.current=u.current.apply(r,t)},r=function(e,t){m&&cancelAnimationFrame(l.current),l.current=m?requestAnimationFrame(e):setTimeout(e,t)},s=function(e){if(!g.current)return!1;var r=e-n.current;return!n.current||r>=t||r<0||x&&e-i.current>=y},v=function(t){return l.current=null,f&&o.current?e(t):(o.current=d.current=null,c.current)},j=function e(){var a=Date.now();if(s(a))return v(a);if(g.current){var l=t-(a-n.current);r(e,x?Math.min(l,y-(a-i.current)):l)}},w=function(){if(p||b){var u=Date.now(),m=s(u);if(o.current=[].slice.call(arguments),d.current=a,n.current=u,m){if(!l.current&&g.current)return i.current=n.current,r(j,t),h?e(n.current):c.current;if(x)return r(j,t),e(n.current)}return l.current||r(j,t),c.current}};return w.cancel=function(){l.current&&(m?cancelAnimationFrame(l.current):clearTimeout(l.current)),i.current=0,o.current=n.current=d.current=l.current=null},w.isPending=function(){return!!l.current},w.flush=function(){return l.current?v(Date.now()):c.current},w},[h,x,t,y,f,m,p,b])}(e=>{console.log(`Searching... ${e}`);let r=new URLSearchParams(l);r.set("page","1"),e?r.set(t,e):r.delete(t),d(`${o}?${r.toString()}`)},500);return(0,a.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,a.jsx)("label",{htmlFor:t,className:"sr-only",children:"Search"}),(0,a.jsx)("input",{id:t,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:e,onChange:e=>{c(e.target.value)},defaultValue:l.get(t)?.toString()}),r||(0,a.jsx)(i.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},68731:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(60687),s=r(95282),n=r.n(s),i=r(43210),l=r(28136);function o({seo:e,defaultTitle:t="Natural Healing Now - Holistic Health Directory",defaultDescription:r="Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.",defaultOgImage:s="",pageType:o="website"}){let d=e?.metaTitle||t,c=e?.metaDescription||r,u=e?.metaRobots||"index, follow",g=e?.canonicalURL||"";e?.structuredData;let p=e?.metaSocial?.find(e=>"Facebook"===e.socialNetwork),m=e?.metaSocial?.find(e=>"Twitter"===e.socialNetwork),h=p?.image?.data?.attributes?.url||e?.metaImage?.data?.attributes?.url||s,f=(0,l.Rb)(h),x=m?.image?.data?.attributes?.url||e?.metaImage?.data?.attributes?.url||s,b=(0,l.Rb)(x),y=p?.title||d,v=p?.description||c,j=m?.title||d,w=m?.description||c,[N,A]=(0,i.useState)(null);return(0,a.jsxs)(n(),{children:[(0,a.jsx)("title",{children:d}),(0,a.jsx)("meta",{name:"description",content:c}),u&&(0,a.jsx)("meta",{name:"robots",content:u}),g&&(0,a.jsx)("link",{rel:"canonical",href:g}),(0,a.jsx)("meta",{property:"og:type",content:o}),(0,a.jsx)("meta",{property:"og:title",content:y}),(0,a.jsx)("meta",{property:"og:description",content:v}),f&&(0,a.jsx)("meta",{property:"og:image",content:f}),(0,a.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,a.jsx)("meta",{name:"twitter:title",content:j}),(0,a.jsx)("meta",{name:"twitter:description",content:w}),b&&(0,a.jsx)("meta",{name:"twitter:image",content:b}),N&&(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(N)}})]})}},71397:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\SEOHead.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\SEOHead.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86132:(e,t,r)=>{Promise.resolve().then(r.bind(r,14256)),Promise.resolve().then(r.bind(r,87089)),Promise.resolve().then(r.bind(r,71397)),Promise.resolve().then(r.bind(r,10592))},87089:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\CategoryFilter.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\CategoryFilter.tsx","default")},91936:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(60687),s=r(44725),n=r(7610),i=r(49384),l=r(85814),o=r.n(l),d=r(16189);let c=(e,t)=>t<=7?Array.from({length:t},(e,t)=>t+1):e<=3?[1,2,3,"...",t-1,t]:e>=t-2?[1,2,"...",t-2,t-1,t]:[1,"...",e-1,e,e+1,"...",t];function u({totalPages:e,currentPage:t}){let r=(0,d.usePathname)(),s=(0,d.useSearchParams)(),n=void 0!==t?t:Number(s.get("page"))||1,i=e=>{let t=new URLSearchParams(s);return t.set("page",e.toString()),`${r}?${t.toString()}`},l=c(n,e);return e<=1?null:(0,a.jsxs)("div",{className:"inline-flex",children:[(0,a.jsx)(p,{direction:"left",href:i(n-1),isDisabled:n<=1}),(0,a.jsx)("div",{className:"flex -space-x-px",children:l.map((e,t)=>{let r;return 0===t&&(r="first"),t===l.length-1&&(r="last"),1===l.length&&(r="single"),"..."===e&&(r="middle"),(0,a.jsx)(g,{href:i(e),page:e,position:r,isActive:n===e},`${e}-${t}`)})}),(0,a.jsx)(p,{direction:"right",href:i(n+1),isDisabled:n>=e})]})}function g({page:e,href:t,isActive:r,position:s}){let n=(0,i.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===s||"single"===s,"rounded-r-md":"last"===s||"single"===s,"z-10 bg-emerald-600 border-emerald-600 text-white":r,"hover:bg-gray-100":!r&&"middle"!==s,"text-gray-300 pointer-events-none":"middle"===s});return r||"middle"===s?(0,a.jsx)("div",{className:n,children:e}):(0,a.jsx)(o(),{href:t,className:n,children:e})}function p({href:e,direction:t,isDisabled:r}){let l=(0,i.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":r,"hover:bg-gray-100":!r,"mr-2 md:mr-4":"left"===t,"ml-2 md:ml-4":"right"===t}),d="left"===t?(0,a.jsx)(s.A,{className:"w-4"}):(0,a.jsx)(n.A,{className:"w-4"});return r?(0,a.jsx)("div",{className:l,children:d}):(0,a.jsx)(o(),{className:l,href:e,children:d})}},94735:e=>{"use strict";e.exports=require("events")},95282:(e,t)=>{"use strict";function r(){return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,1330,3376,6391,2975,4867,8446,270,3762],()=>r(22618));module.exports=a})();