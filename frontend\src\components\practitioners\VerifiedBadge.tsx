'use client';

import { MdVerifiedUser } from "react-icons/md";

interface VerifiedBadgeProps {
  isVerified: boolean;
}

const VerifiedBadge = ({ isVerified }: VerifiedBadgeProps) => {
  if (!isVerified) {
    return null;
  }

  return (
    <div className="flex items-center gap-x-1 bg-emerald-100 text-emerald-700 px-2 py-0.5 rounded-full text-sm font-medium">
      <MdVerifiedUser color="#009967" size={16} />
      <span>VERIFIED</span>
    </div>
  );
};

export default VerifiedBadge;
