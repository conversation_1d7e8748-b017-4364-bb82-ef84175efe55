(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7228],{111:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.bind(t,2204)),Promise.resolve().then(t.bind(t,5922)),Promise.resolve().then(t.bind(t,8400))},2204:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var n=t(5155),a=t(6874),i=t.n(a),c=t(2515),s=t(2115),o=t(8864);let l=e=>{let{practitioner:r,prefetchedData:t=!1}=e,[a,l]=(0,s.useState)(!1),u=(0,s.useRef)(!1);(0,s.useEffect)(()=>{if(u.current)return;let e=(0,o.b3)(r.slug);e&&l(!0),r._hasDetailedData&&!e&&((0,o.tq)(r),l(!0)),u.current=!0},[r]);let f=t||r._hasDetailedData||a?{pathname:"/practitioners/".concat(r.slug),query:{prefetched:"true"}}:"/practitioners/".concat(r.slug);return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 flex-grow",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,n.jsx)(i(),{href:f,className:"hover:text-emerald-600",children:r.name})}),r.isVerified&&(0,n.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,n.jsx)(c.AI8,{color:"#009967",size:14}),(0,n.jsx)("span",{children:"VERIFIED"})]}),r.bio&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:r.bio})]}),(0,n.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,n.jsx)(i(),{href:f,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Profile →"})})]})}},2461:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var n=t(2115);let a=n.forwardRef(function(e,r){let{title:t,titleId:a,...i}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},i),t?n.createElement("title",{id:a},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},2596:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=function(){for(var e,r,t=0,n="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,n,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var i=r.length;for(t=0;t<i;t++)r[t]&&(n=e(r[t]))&&(a&&(a+=" "),a+=n)}else for(n in r)r[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=r);return n}},4436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>u});var n=t(2115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(a),c=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function o(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?o(Object(t),!0).forEach(function(r){var n,a,i;n=e,a=r,i=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return r=>n.createElement(f,s({attr:l({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,l({key:t},r.attr),e(r.child)))}(e.child))}function f(e){var r=r=>{var t,{attr:a,size:i,title:o}=e,u=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,c),f=i||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,u,{className:t,style:l(l({color:e.color||r.color},r.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),o&&n.createElement("title",null,o),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>r(e)):r(a)}},5695:(e,r,t)=>{"use strict";var n=t(8999);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},5922:(e,r,t)=>{"use strict";t.d(r,{default:()=>f});var n=t(5155),a=t(2461),i=t(9416),c=t(2596),s=t(6874),o=t.n(s),l=t(5695);let u=(e,r)=>r<=7?Array.from({length:r},(e,r)=>r+1):e<=3?[1,2,3,"...",r-1,r]:e>=r-2?[1,2,"...",r-2,r-1,r]:[1,"...",e-1,e,e+1,"...",r];function f(e){let{totalPages:r,currentPage:t}=e,a=(0,l.usePathname)(),i=(0,l.useSearchParams)(),c=void 0!==t?t:Number(i.get("page"))||1,s=e=>{let r=new URLSearchParams(i);return r.set("page",e.toString()),"".concat(a,"?").concat(r.toString())},o=u(c,r);return r<=1?null:(0,n.jsxs)("div",{className:"inline-flex",children:[(0,n.jsx)(m,{direction:"left",href:s(c-1),isDisabled:c<=1}),(0,n.jsx)("div",{className:"flex -space-x-px",children:o.map((e,r)=>{let t;return 0===r&&(t="first"),r===o.length-1&&(t="last"),1===o.length&&(t="single"),"..."===e&&(t="middle"),(0,n.jsx)(d,{href:s(e),page:e,position:t,isActive:c===e},"".concat(e,"-").concat(r))})}),(0,n.jsx)(m,{direction:"right",href:s(c+1),isDisabled:c>=r})]})}function d(e){let{page:r,href:t,isActive:a,position:i}=e,s=(0,c.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===i||"single"===i,"rounded-r-md":"last"===i||"single"===i,"z-10 bg-emerald-600 border-emerald-600 text-white":a,"hover:bg-gray-100":!a&&"middle"!==i,"text-gray-300 pointer-events-none":"middle"===i});return a||"middle"===i?(0,n.jsx)("div",{className:s,children:r}):(0,n.jsx)(o(),{href:t,className:s,children:r})}function m(e){let{href:r,direction:t,isDisabled:s}=e,l=(0,c.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":s,"hover:bg-gray-100":!s,"mr-2 md:mr-4":"left"===t,"ml-2 md:ml-4":"right"===t}),u="left"===t?(0,n.jsx)(a.A,{className:"w-4"}):(0,n.jsx)(i.A,{className:"w-4"});return s?(0,n.jsx)("div",{className:l,children:u}):(0,n.jsx)(o(),{className:l,href:r,children:u})}},8400:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});var n=t(5155),a=t(2115),i=t(5695),c=t(351);function s(e){var r;let{placeholder:t,paramName:s="query",icon:o}=e,l=(0,i.useSearchParams)(),u=(0,i.usePathname)(),{replace:f}=(0,i.useRouter)(),d=function(e,r,t){var n=this,i=(0,a.useRef)(null),c=(0,a.useRef)(0),s=(0,a.useRef)(null),o=(0,a.useRef)([]),l=(0,a.useRef)(),u=(0,a.useRef)(),f=(0,a.useRef)(e),d=(0,a.useRef)(!0);f.current=e;var m="undefined"!=typeof window,h=!r&&0!==r&&m;if("function"!=typeof e)throw TypeError("Expected a function");r=+r||0;var g=!!(t=t||{}).leading,p=!("trailing"in t)||!!t.trailing,v="maxWait"in t,b="debounceOnServer"in t&&!!t.debounceOnServer,x=v?Math.max(+t.maxWait||0,r):null;return(0,a.useEffect)(function(){return d.current=!0,function(){d.current=!1}},[]),(0,a.useMemo)(function(){var e=function(e){var r=o.current,t=l.current;return o.current=l.current=null,c.current=e,u.current=f.current.apply(t,r)},t=function(e,r){h&&cancelAnimationFrame(s.current),s.current=h?requestAnimationFrame(e):setTimeout(e,r)},a=function(e){if(!d.current)return!1;var t=e-i.current;return!i.current||t>=r||t<0||v&&e-c.current>=x},y=function(r){return s.current=null,p&&o.current?e(r):(o.current=l.current=null,u.current)},j=function e(){var n=Date.now();if(a(n))return y(n);if(d.current){var s=r-(n-i.current);t(e,v?Math.min(s,x-(n-c.current)):s)}},w=function(){if(m||b){var f=Date.now(),h=a(f);if(o.current=[].slice.call(arguments),l.current=n,i.current=f,h){if(!s.current&&d.current)return c.current=i.current,t(j,r),g?e(i.current):u.current;if(v)return t(j,r),e(i.current)}return s.current||t(j,r),u.current}};return w.cancel=function(){s.current&&(h?cancelAnimationFrame(s.current):clearTimeout(s.current)),c.current=0,o.current=i.current=l.current=s.current=null},w.isPending=function(){return!!s.current},w.flush=function(){return s.current?y(Date.now()):u.current},w},[g,v,r,x,p,h,m,b])}(e=>{console.log("Searching... ".concat(e));let r=new URLSearchParams(l);r.set("page","1"),e?r.set(s,e):r.delete(s),f("".concat(u,"?").concat(r.toString()))},500);return(0,n.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,n.jsx)("label",{htmlFor:s,className:"sr-only",children:"Search"}),(0,n.jsx)("input",{id:s,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:t,onChange:e=>{d(e.target.value)},defaultValue:null==(r=l.get(s))?void 0:r.toString()}),o||(0,n.jsx)(c.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},8864:(e,r,t)=>{"use strict";t.d(r,{b3:()=>c,tq:()=>i});let n={};function a(e){let r=n[e],t=Date.now();if(r&&r.expiry>t)return r.data;try{let r=sessionStorage.getItem("cache_".concat(e));if(r){let a=JSON.parse(r);if(a.expiry>t)return n[e]=a,a.data;sessionStorage.removeItem("cache_".concat(e))}}catch(e){console.error("Error retrieving data from sessionStorage:",e)}return null}function i(e){if(!e||!e.id||!e.slug)return;let r=a("practitioner_".concat(e.slug));r&&(!e._hasDetailedData||r._hasDetailedData)||function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5,a=Date.now();n[e]={data:r,timestamp:a,expiry:a+t};try{sessionStorage.setItem("cache_".concat(e),JSON.stringify({data:r,timestamp:a,expiry:a+t}))}catch(e){console.error("Error storing data in sessionStorage:",e)}}("practitioner_".concat(e.slug),e,18e5)}function c(e){return a("practitioner_".concat(e))}},9416:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var n=t(2115);let a=n.forwardRef(function(e,r){let{title:t,titleId:a,...i}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},i),t?n.createElement("title",{id:a},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})}},e=>{var r=r=>e(e.s=r);e.O(0,[844,5479,6874,8441,1684,7358],()=>r(111)),_N_E=e.O()}]);