(()=>{var e={};e.id=6279,e.ids=[6279],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>u,pages:()=>d,routeModule:()=>c,tree:()=>o});var s=r(65239),a=r(48088),n=r(31369),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let o={children:["",{children:["blog",{children:["tags",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66491)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\tags\\[slug]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\tags\\[slug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/blog/tags/[slug]/page",pathname:"/blog/tags/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},51165:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,87603)),Promise.resolve().then(r.bind(r,71397))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62781:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,55061)),Promise.resolve().then(r.bind(r,68731))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66491:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(37413),a=r(4536),n=r.n(a),i=r(73993),l=r(87603),o=r(58446),d=r(82158),u=r(39916),c=r(71397);async function p({params:e}){let{slug:t}=await e,r=null;try{console.log(`Fetching blog tag with slug: "${t}"`);let e=await o.$.blog.getTagBySlug(t);if(console.log("Raw API response for tag:",JSON.stringify(e,null,2)),!e||!e.data||!(e.data.length>0))return console.log(`No blog tag found with slug "${t}"`),(0,u.notFound)();console.log("Found tag data, mapping to component props"),r=function(e){if(console.log("Mapping tag data:",JSON.stringify(e,null,2)),!e)return console.warn("No tag data to map"),null;let t=!!e.attributes,r=e.id,s=t?e.attributes:e;if(!r||!s)return console.warn("Invalid tag data structure:",e),null;console.log("Using API URL for tag mapping:","https://nice-badge-2130241d6c.strapiapp.com");let a=[];try{let e=t?s.blog_posts?.data:s.blog_posts||s.blogPosts;Array.isArray(e)&&(a=e.map(e=>{let t=e.attributes||e,r=t.author_blogs||[],s=r.length>0?r[0]:{},a=s.attributes||s,n=t.featuredImage?.data||t.featuredImage||{},i=(0,d.Jf)(n);if(!i&&n&&("string"==typeof n?i=(0,d.Jf)(n):n.url?i=(0,d.Jf)(n.url):n.data?.attributes?.url&&(i=(0,d.Jf)(n.data.attributes.url))),!i&&process.env.IMAGE_HOSTNAME&&n){let e=process.env.IMAGE_HOSTNAME,t="";if("string"==typeof n&&n.includes("/")){let e=n.split("/");t=e[e.length-1]}else if(n.url&&n.url.includes("/")){let e=n.url.split("/");t=e[e.length-1]}else if(n.data?.attributes?.url&&n.data.attributes.url.includes("/")){let e=n.data.attributes.url.split("/");t=e[e.length-1]}else n.data?.attributes?.name?t=n.data.attributes.name:n.name?t=n.name:n.data?.attributes?.hash&&(t=`${n.data.attributes.hash}.${n.data.attributes.ext||"jpg"}`);t&&(i=`${e}/${t}`)}let l=a.profilePicture?.data||a.profilePicture||{},o=(0,d.Jf)(l);if(!o&&l&&("string"==typeof l?o=(0,d.Jf)(l):l.url?o=(0,d.Jf)(l.url):l.data?.attributes?.url&&(o=(0,d.Jf)(l.data.attributes.url))),!o&&process.env.IMAGE_HOSTNAME&&l){let e=process.env.IMAGE_HOSTNAME,t="";if("string"==typeof l&&l.includes("/")){let e=l.split("/");t=e[e.length-1]}else if(l.url&&l.url.includes("/")){let e=l.url.split("/");t=e[e.length-1]}else if(l.data?.attributes?.url&&l.data.attributes.url.includes("/")){let e=l.data.attributes.url.split("/");t=e[e.length-1]}else l.data?.attributes?.name?t=l.data.attributes.name:l.name&&(t=l.name);t&&(o=`${e}/${t}`)}let u={id:e.id||"",title:t.title||"Untitled Post",slug:t.slug||"untitled-post",excerpt:t.excerpt||null,featured_image:i,publish_date:t.publishDate||t.published_at||t.createdAt||new Date().toISOString(),author:{name:a.name||"Unknown Author",slug:a.slug||"unknown-author",profile_picture:o}};return void 0!==t.view_count&&Object.defineProperty(u,"view_count",{value:t.view_count,enumerable:!1}),u}))}catch(e){console.warn("Error extracting posts for tag:",e)}let n=s.name||"Unnamed Tag",i=s.slug||"unnamed-tag";return{id:r,name:n,slug:i,description:s.description||null,posts:a}}(e.data[0])}catch(e){return console.error(`Error fetching blog tag with slug ${t}:`,e),(0,u.notFound)()}if(!r)return(0,u.notFound)();let a=r.name.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),p={defaultTitle:`Blog Tag: ${a}`,defaultDescription:r.description||`Articles tagged with "${r.name}".`,pageType:"website"};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.default,{...p}),(0,s.jsx)("div",{className:"bg-gray-100 py-3",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)(n(),{href:"/",className:"hover:text-emerald-600",children:"Home"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)(n(),{href:"/blog",className:"hover:text-emerald-600",children:"Blog"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)("span",{className:"text-gray-800",children:a})]})})}),(0,s.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(i.cnX,{className:"text-3xl"}),(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold",children:a})]}),r.description&&(0,s.jsx)("p",{className:"text-lg max-w-3xl",children:r.description}),!r.description&&(0,s.jsxs)("p",{className:"text-lg max-w-3xl",children:['Articles tagged with "',r.name,'"']})]})}),(0,s.jsx)("div",{className:"bg-gray-50 py-12",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-8",children:[r.posts.length," ",1===r.posts.length?"Article":"Articles"," Found"]}),r.posts&&r.posts.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.posts.map(e=>(0,s.jsx)(l.default,{post:e},e.id))}):(0,s.jsx)("div",{className:"bg-white p-8 rounded-lg text-center",children:(0,s.jsx)("p",{className:"text-gray-600",children:"No articles found with this tag."})}),(0,s.jsx)("div",{className:"mt-12 text-center",children:(0,s.jsxs)(n(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 flex items-center justify-center",children:[(0,s.jsx)(i.kRp,{className:"mr-2"})," Back to All Articles"]})})]})})]})}},68731:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),a=r(95282),n=r.n(a),i=r(43210),l=r(28136);function o({seo:e,defaultTitle:t="Natural Healing Now - Holistic Health Directory",defaultDescription:r="Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.",defaultOgImage:a="",pageType:o="website"}){let d=e?.metaTitle||t,u=e?.metaDescription||r,c=e?.metaRobots||"index, follow",p=e?.canonicalURL||"";e?.structuredData;let g=e?.metaSocial?.find(e=>"Facebook"===e.socialNetwork),m=e?.metaSocial?.find(e=>"Twitter"===e.socialNetwork),x=g?.image?.data?.attributes?.url||e?.metaImage?.data?.attributes?.url||a,h=(0,l.Rb)(x),f=m?.image?.data?.attributes?.url||e?.metaImage?.data?.attributes?.url||a,b=(0,l.Rb)(f),j=g?.title||d,v=g?.description||u,y=m?.title||d,w=m?.description||u,[P,_]=(0,i.useState)(null);return(0,s.jsxs)(n(),{children:[(0,s.jsx)("title",{children:d}),(0,s.jsx)("meta",{name:"description",content:u}),c&&(0,s.jsx)("meta",{name:"robots",content:c}),p&&(0,s.jsx)("link",{rel:"canonical",href:p}),(0,s.jsx)("meta",{property:"og:type",content:o}),(0,s.jsx)("meta",{property:"og:title",content:j}),(0,s.jsx)("meta",{property:"og:description",content:v}),h&&(0,s.jsx)("meta",{property:"og:image",content:h}),(0,s.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,s.jsx)("meta",{name:"twitter:title",content:y}),(0,s.jsx)("meta",{name:"twitter:description",content:w}),b&&(0,s.jsx)("meta",{name:"twitter:image",content:b}),P&&(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(P)}})]})}},71397:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\SEOHead.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\SEOHead.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},95282:(e,t)=>{"use strict";function r(){return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,1330,3376,6391,2975,4867,8446,270,4559],()=>r(36238));module.exports=s})();