(()=>{var e={};e.id=4096,e.ids=[4096],e.modules={3127:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>v,serverHooks:()=>b,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>h});var i={};r.r(i),r.d(i,{default:()=>p});var n={};r.r(n),r.d(n,{GET:()=>m});var o=r(96559),a=r(48088),l=r(37719),s=r(32190),u=r(58446);let c=process.env.NEXT_PUBLIC_SITE_URL||"https://nice-badge-2130241d6c.strapiapp.com".replace(".strapiapp.com","")||0,d=c.endsWith("/")?c.slice(0,-1):c;async function p(){try{console.log("Generating blog sitemap...");let e=await u.$.blog.getPosts({pagination:{pageSize:1e3}}),t=(e?.data||[]).map(e=>{let t=e.attributes||e,r=t.slug||e.slug,i=t.updatedAt||e.updatedAt||new Date().toISOString();return r?{url:`${d}/blog/${r}`,lastModified:new Date(i),changeFrequency:"weekly",priority:.7}:null}).filter(Boolean),r=await u.$.blog.getCategories({pagination:{pageSize:100}}),i=(r?.data||[]).map(e=>{let t=e.attributes?.slug||e.slug;return t?{url:`${d}/blog/categories/${t}`,lastModified:new Date,changeFrequency:"weekly",priority:.6}:null}).filter(Boolean),n=await u.$.blog.getTags(),o=(n?.data||[]).map(e=>{let t=e.attributes?.slug||e.slug;return t?{url:`${d}/blog/tags/${t}`,lastModified:new Date,changeFrequency:"weekly",priority:.6}:null}).filter(Boolean);return[{url:`${d}/blog`,lastModified:new Date,changeFrequency:"daily",priority:.9},...t,...i,...o]}catch(e){return console.error("Error generating blog sitemap:",e),[{url:`${d}/blog`,lastModified:new Date,changeFrequency:"daily",priority:.9}]}}var f=r(12127);let g={...i}.default;if("function"!=typeof g)throw Error('Default export is missing in "C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\sitemap.ts"');async function m(e,t){let{__metadata_id__:r,...i}=await t.params||{},n=!!r&&r.endsWith(".xml");if(r&&!n)return new s.NextResponse("Not Found",{status:404});let o=r&&n?r.slice(0,-4):void 0,a=await g({id:o}),l=(0,f.resolveRouteData)(a,"sitemap");return new s.NextResponse(l,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let v=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/blog/sitemap.xml/route",pathname:"/blog/sitemap.xml",filename:"sitemap",bundlePath:"app/blog/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CAI%20Coding%20Projects%5Cnaturalhealingnow%5Cfrontend%5Csrc%5Capp%5Cblog%5Csitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:y,workUnitAsyncStorage:h,serverHooks:b}=v;function _(){return(0,l.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:h})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return s},throwForSearchParamsAccessInUseCache:function(){return l},throwWithStaticGenerationBailoutError:function(){return o},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return a}});let i=r(80023),n=r(3295);function o(e,t){throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function a(e,t){throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function l(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function s(){let e=n.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{resolveManifest:function(){return a},resolveRobots:function(){return n},resolveRouteData:function(){return l},resolveSitemap:function(){return o}});let i=r(77341);function n(e){let t="";for(let r of Array.isArray(e.rules)?e.rules:[e.rules]){for(let e of(0,i.resolveArray)(r.userAgent||["*"]))t+=`User-Agent: ${e}
`;if(r.allow)for(let e of(0,i.resolveArray)(r.allow))t+=`Allow: ${e}
`;if(r.disallow)for(let e of(0,i.resolveArray)(r.disallow))t+=`Disallow: ${e}
`;r.crawlDelay&&(t+=`Crawl-delay: ${r.crawlDelay}
`),t+="\n"}return e.host&&(t+=`Host: ${e.host}
`),e.sitemap&&(0,i.resolveArray)(e.sitemap).forEach(e=>{t+=`Sitemap: ${e}
`}),t}function o(e){let t=e.some(e=>Object.keys(e.alternates??{}).length>0),r=e.some(e=>{var t;return!!(null==(t=e.images)?void 0:t.length)}),i=e.some(e=>{var t;return!!(null==(t=e.videos)?void 0:t.length)}),n="";for(let s of(n+='<?xml version="1.0" encoding="UTF-8"?>\n',n+='<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',r&&(n+=' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"'),i&&(n+=' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"'),t?n+=' xmlns:xhtml="http://www.w3.org/1999/xhtml">\n':n+=">\n",e)){var o,a,l;n+="<url>\n",n+=`<loc>${s.url}</loc>
`;let e=null==(o=s.alternates)?void 0:o.languages;if(e&&Object.keys(e).length)for(let t in e)n+=`<xhtml:link rel="alternate" hreflang="${t}" href="${e[t]}" />
`;if(null==(a=s.images)?void 0:a.length)for(let e of s.images)n+=`<image:image>
<image:loc>${e}</image:loc>
</image:image>
`;if(null==(l=s.videos)?void 0:l.length)for(let e of s.videos)n+=["<video:video>",`<video:title>${e.title}</video:title>`,`<video:thumbnail_loc>${e.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${e.description}</video:description>`,e.content_loc&&`<video:content_loc>${e.content_loc}</video:content_loc>`,e.player_loc&&`<video:player_loc>${e.player_loc}</video:player_loc>`,e.duration&&`<video:duration>${e.duration}</video:duration>`,e.view_count&&`<video:view_count>${e.view_count}</video:view_count>`,e.tag&&`<video:tag>${e.tag}</video:tag>`,e.rating&&`<video:rating>${e.rating}</video:rating>`,e.expiration_date&&`<video:expiration_date>${e.expiration_date}</video:expiration_date>`,e.publication_date&&`<video:publication_date>${e.publication_date}</video:publication_date>`,e.family_friendly&&`<video:family_friendly>${e.family_friendly}</video:family_friendly>`,e.requires_subscription&&`<video:requires_subscription>${e.requires_subscription}</video:requires_subscription>`,e.live&&`<video:live>${e.live}</video:live>`,e.restriction&&`<video:restriction relationship="${e.restriction.relationship}">${e.restriction.content}</video:restriction>`,e.platform&&`<video:platform relationship="${e.platform.relationship}">${e.platform.content}</video:platform>`,e.uploader&&`<video:uploader${e.uploader.info&&` info="${e.uploader.info}"`}>${e.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(s.lastModified){let e=s.lastModified instanceof Date?s.lastModified.toISOString():s.lastModified;n+=`<lastmod>${e}</lastmod>
`}s.changeFrequency&&(n+=`<changefreq>${s.changeFrequency}</changefreq>
`),"number"==typeof s.priority&&(n+=`<priority>${s.priority}</priority>
`),n+="</url>\n"}return n+"</urlset>\n"}function a(e){return JSON.stringify(e)}function l(e,t){return"robots"===t?n(e):"sitemap"===t?o(e):"manifest"===t?a(e):""}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43763:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return n},describeStringPropertyAccess:function(){return i},wellKnownProperties:function(){return o}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function i(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function n(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let o=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},77341:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e:[e]}function i(e){if(null!=e)return r(e)}function n(e){let t;if("string"==typeof e)try{t=(e=new URL(e)).origin}catch{}return t}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getOrigin:function(){return n},resolveArray:function(){return r},resolveAsArrayOrUndefined:function(){return i}})},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[7719,1330,3376,580,8446],()=>r(3127));module.exports=i})();