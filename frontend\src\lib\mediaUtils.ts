/**
 * Optimized utility functions for handling media URLs and assets
 * Simplified for better performance and reliability
 */
import logger from './logger';

// Environment variables for media handling
const ENV = {
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  NEXT_PUBLIC_STRAPI_API_URL: process.env.NEXT_PUBLIC_STRAPI_API_URL,
  NEXT_PUBLIC_STRAPI_MEDIA_URL: process.env.NEXT_PUBLIC_STRAPI_MEDIA_URL,
  NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
  IMAGE_HOSTNAME: process.env.IMAGE_HOSTNAME,
  NODE_ENV: process.env.NODE_ENV
};

// Resolve Strapi API URL with fallback
const EFFECTIVE_STRAPI_URL = ENV.NEXT_PUBLIC_STRAPI_API_URL || 
                             ENV.NEXT_PUBLIC_API_URL || 
                             (ENV.NODE_ENV === 'development' ? 'http://localhost:1337' : 'https://nice-badge-2130241d6c.strapiapp.com');

// Determine media URL with smarter defaults
const STRAPI_MEDIA_URL = (() => {
  // Use explicit media URL if provided
  if (ENV.NEXT_PUBLIC_STRAPI_MEDIA_URL) {
    return ensureHttps(removeTrailingSlash(ENV.NEXT_PUBLIC_STRAPI_MEDIA_URL));
  }
  
  // Use IMAGE_HOSTNAME if provided
  if (ENV.IMAGE_HOSTNAME) {
    return ensureHttps(removeTrailingSlash(ENV.IMAGE_HOSTNAME));
  }
  
  // Derive from API URL
  try {
    const apiUrl = new URL(EFFECTIVE_STRAPI_URL);
    // For Strapi Cloud, generate media URL
    if (apiUrl.hostname.endsWith('strapiapp.com')) {
      return `${ensureHttps(apiUrl.protocol)}//${apiUrl.hostname.replace('strapiapp.com', 'media.strapiapp.com')}`;
    }
    // For other URLs, use as-is
    return ensureHttps(removeTrailingSlash(EFFECTIVE_STRAPI_URL));
  } catch (e) {
    // Fallback for invalid URLs
    return ENV.NODE_ENV === 'development' 
      ? 'http://localhost:1337' 
      : 'https://nice-badge-2130241d6c.media.strapiapp.com';
  }
})();

// Site URL for SEO and Open Graph
const SITE_URL = ENV.NEXT_PUBLIC_SITE_URL ||
  (ENV.NEXT_PUBLIC_API_URL && ENV.NEXT_PUBLIC_API_URL.includes('strapiapp.com')
    ? ENV.NEXT_PUBLIC_API_URL.replace('.strapiapp.com', '.vercel.app')
    : 'https://naturalhealingnow.vercel.app');

// Helper functions
function ensureHttps(url: string): string {
  if (!url) return url;
  return url.replace(/^http:/, 'https:');
}

function removeTrailingSlash(url: string): string {
  if (!url) return url;
  return url.endsWith('/') ? url.slice(0, -1) : url;
}

// Log configuration in development
if (ENV.NODE_ENV === 'development') {
  logger.debug('Media Utils Initialized:', {
    EFFECTIVE_STRAPI_URL,
    STRAPI_MEDIA_URL,
    SITE_URL
  });
}

/**
 * Gets the correct Strapi media URL for an image path.
 * Ensures that relative paths are correctly prepended with the Strapi media URL.
 * Attempts to fix malformed full URLs.
 *
 * @param imagePath - The image path or filename, or a full URL.
 * @returns The properly formatted media URL.
 */
export function getStrapiMediaPath(imagePath: string): string {
  if (!imagePath) return '';

  // If it's already a full URL, try to sanitize it, especially for concatenated domains
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return sanitizeUrl(imagePath); // sanitizeUrl will handle malformed full URLs
  }

  // If it's a relative path, prepend the STRAPI_MEDIA_URL
  // This ensures we're using the correct media domain
  if (STRAPI_MEDIA_URL) {
    return `${STRAPI_MEDIA_URL}/${imagePath.startsWith('/') ? imagePath.substring(1) : imagePath}`;
  }
  // Fallback if STRAPI_MEDIA_URL is somehow not set (should be rare with new logic)
  logger.warn('STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaPath', { imagePath });
  return `${EFFECTIVE_STRAPI_URL}/${imagePath.startsWith('/') ? imagePath.substring(1) : imagePath}`;
}


/**
 * Creates an absolute URL for Strapi media assets.
 * Handles different Strapi response formats and sanitizes the URL.
 *
 * @param mediaInput - The media object or URL string from Strapi.
 * @param options - Optional configuration.
 * @returns The absolute URL or null if no URL could be extracted.
 */
export function getStrapiMediaUrl(
  mediaInput: any,
  options: { debug?: boolean } = { debug: false }
): string | null {
  if (options.debug) {
    logger.debug("getStrapiMediaUrl input:", {
      type: typeof mediaInput,
      isNull: mediaInput === null,
      isUndefined: mediaInput === undefined,
      value: mediaInput
    });
  }

  if (!mediaInput) return null;

  let urlToProcess: string | null = null;

  if (typeof mediaInput === 'string') {
    urlToProcess = mediaInput;
  } else if (typeof mediaInput === 'object') {
    urlToProcess =
      mediaInput.url ||
      mediaInput.data?.attributes?.url ||
      mediaInput.data?.url ||
      null;
  }

  if (!urlToProcess) {
    if (options.debug || process.env.NODE_ENV === 'production') {
      logger.warn("Could not extract initial URL from mediaInput in getStrapiMediaUrl", { mediaInput });
    }
    return null;
  }

  const sanitizedUrl = sanitizeUrl(urlToProcess);

  if (!sanitizedUrl) {
     if (options.debug || process.env.NODE_ENV === 'production') {
      logger.warn("URL became empty after sanitization in getStrapiMediaUrl", { originalUrl: urlToProcess });
    }
    return null;
  }

  // If sanitizeUrl returned an absolute URL, use it
  if (sanitizedUrl.startsWith('http://') || sanitizedUrl.startsWith('https://')) {
    return sanitizedUrl;
  }

  // If it's a relative path after sanitization, prepend the STRAPI_MEDIA_URL
  if (STRAPI_MEDIA_URL) {
    return `${STRAPI_MEDIA_URL}${sanitizedUrl.startsWith('/') ? '' : '/'}${sanitizedUrl}`;
  }
  
  // Fallback: if STRAPI_MEDIA_URL is not available, use EFFECTIVE_STRAPI_URL (less ideal for media)
  logger.warn('STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl', { sanitizedUrl });
  return `${EFFECTIVE_STRAPI_URL}${sanitizedUrl.startsWith('/') ? '' : '/'}${sanitizedUrl}`;
}

/**
 * Gets the profile picture URL for an author
 *
 * @param authorData - The author data from Strapi
 * @returns The absolute URL or null if no URL could be extracted
 */
export function getProfilePictureUrl(authorData: any): string | null {
  if (!authorData || !authorData.profilePicture) return null;

  const profilePic = authorData.profilePicture;
  
  // Try to get URL from various possible structures
  const url = profilePic.url || 
              profilePic.data?.attributes?.url || 
              profilePic.data?.url ||
              profilePic.formats?.thumbnail?.url;

  return url ? getStrapiMediaUrl(url) : getStrapiMediaUrl(profilePic); // Fallback to passing the whole object
}

/**
 * Gets the featured image URL for a post
 *
 * @param postData - The post data from Strapi
 * @returns The absolute URL or null if no URL could be extracted
 */
export function getFeaturedImageUrl(postData: any): string | null {
  if (!postData || !postData.featuredImage) return null;

  const featuredImg = postData.featuredImage;

  const url = featuredImg.url ||
              featuredImg.data?.attributes?.url ||
              featuredImg.data?.url;

  return url ? getStrapiMediaUrl(url) : getStrapiMediaUrl(featuredImg); // Fallback
}

/**
 * Sanitizes a URL by attempting to fix common issues like concatenated domains
 * and ensure it uses HTTPS.
 *
 * @param urlInput - The URL to sanitize (string or object with a 'url' property).
 * @returns The sanitized URL string, or an empty string if input is invalid or unfixable.
 */
export function sanitizeUrl(urlInput: any): string {
  // Fast path for common case: already valid URL or relative path starting with /
  if (typeof urlInput === 'string' && (urlInput.startsWith('https://') || urlInput.startsWith('http://') || urlInput.startsWith('/'))) {
    // Ensure https for absolute URLs
    if (urlInput.startsWith('http://')) {
      return urlInput.replace(/^http:/, 'https:');
    }
    return urlInput;
  }

  if (!urlInput) return '';

  let currentUrl: string;

  if (typeof urlInput === 'object' && urlInput.url && typeof urlInput.url === 'string') {
    currentUrl = urlInput.url;
  } else if (typeof urlInput === 'string') {
    currentUrl = urlInput;
  } else {
    logger.warn('Invalid input type for sanitizeUrl. Expected string or object with url property.', { inputType: typeof urlInput });
    return '';
  }

  // Trim whitespace
  currentUrl = currentUrl.trim();

  // Remove 'undefined' prefix if present
  if (currentUrl.toLowerCase().startsWith('undefined')) {
    currentUrl = currentUrl.substring('undefined'.length);
    logger.info('Removed "undefined" prefix from URL', { original: urlInput, new: currentUrl });
  }
  
  // Attempt to fix the specific concatenation: base_strapi_url + media_strapi_url
  // Example: https://nice-badge-2130241d6c.strapiapp.comhttps://nice-badge-2130241d6c.media.strapiapp.com/image.jpg
  // Should become: https://nice-badge-2130241d6c.media.strapiapp.com/image.jpg
  const strapiApiDomain = EFFECTIVE_STRAPI_URL.replace(/^https?:\/\//, '').split('/')[0]; // e.g., nice-badge-2130241d6c.strapiapp.com
  const strapiMediaDomain = STRAPI_MEDIA_URL.replace(/^https?:\/\//, '').split('/')[0]; // e.g., nice-badge-2130241d6c.media.strapiapp.com

  if (strapiApiDomain && strapiMediaDomain && currentUrl.includes(strapiApiDomain) && currentUrl.includes(strapiMediaDomain)) {
    const regex = new RegExp(`(https?:\/\/)?(${strapiApiDomain})(\/*)(https?:\/\/)?(${strapiMediaDomain})`, 'gi');
    const replacement = `https://${strapiMediaDomain}`;
    if (regex.test(currentUrl)) {
      const originalForLog = currentUrl;
      currentUrl = currentUrl.replace(regex, replacement);
      logger.info('Fixed concatenated Strapi domains', { original: originalForLog, fixed: currentUrl, apiDomain: strapiApiDomain, mediaDomain: strapiMediaDomain });
    }
  }
  
  // Fix missing colon in https// -> https://
  if (currentUrl.includes('https//')) {
    const originalForLog = currentUrl;
    currentUrl = currentUrl.replace(/https\/\//g, 'https://');
    logger.info('Fixed missing colon in URL (https//)', { original: originalForLog, fixed: currentUrl });
  }

  // Ensure HTTPS for known media domains or if it's a full URL without protocol
  if (currentUrl.startsWith('//')) {
     currentUrl = `https:${currentUrl}`;
  } else if ((currentUrl.includes('media.strapiapp.com') || currentUrl.includes(strapiMediaDomain)) && !currentUrl.startsWith('http')) {
     currentUrl = `https://${currentUrl}`;
  } else if (currentUrl.startsWith('localhost') || currentUrl.startsWith(strapiApiDomain.split('.')[0])) { // Heuristic for relative paths that look like hostnames
     currentUrl = `https://${currentUrl}`; // Assume https if protocol missing for these
  }


  // If it's a relative path (starts with /), it's fine as is for now.
  // getStrapiMediaUrl will prepend the correct base.
  if (currentUrl.startsWith('/')) {
    return currentUrl;
  }

  // If it's now a valid absolute URL, return it
  if (currentUrl.startsWith('http://') || currentUrl.startsWith('https://')) {
    try {
      // Validate if it's a proper URL
      new URL(currentUrl);
      return currentUrl;
    } catch (e) {
      logger.error('URL parsing failed after sanitization attempts', { url: currentUrl, error: e });
      // If parsing fails, it might be a relative path that doesn't start with /
      // or completely malformed.
      // If it looks like a path (no protocol, no domain), return it for further processing.
      if (!currentUrl.includes('://') && !currentUrl.includes('.')) { // Simple check for path-like string
          return currentUrl;
      }
      return ''; // Unfixable
    }
  }
  
  // If it's not an absolute URL and not a relative path starting with /,
  // it might be a path without a leading slash.
  // This case should be handled by the caller (getStrapiMediaUrl) by prepending the base URL.
  // However, if STRAPI_MEDIA_URL is available, we can assume it's a media path.
  if (STRAPI_MEDIA_URL && currentUrl && !currentUrl.includes('://')) {
    logger.debug('Assuming relative media path, prepending STRAPI_MEDIA_URL', { path: currentUrl });
    return `/${currentUrl}`; // Return as relative path for getStrapiMediaUrl to handle
  }

  logger.warn('sanitizeUrl could not produce a valid absolute or relative URL', { originalInput: urlInput, finalSanitized: currentUrl });
  return currentUrl; // Return what we have, or empty if it was invalid from start
}

/**
 * Transforms a Strapi media URL to use the site domain for SEO purposes
 * This is especially useful for og:image URLs that should use your domain
 * instead of the Strapi media domain
 *
 * NOTE: For OG images, we now use the direct Strapi media URL instead of transforming
 * This function is kept for backward compatibility with other parts of the codebase
 *
 * @param url - The original Strapi media URL
 * @returns The transformed URL using the site domain or the original URL for media
 */
export function transformToSiteDomainUrl(url: string | null | undefined): string | null {
  if (!url) return null;
  const sanitizedUrl = sanitizeUrl(url);
  if (!sanitizedUrl) return null;

  // For media URLs from media.strapiapp.com, return them directly (ensuring https)
  if (sanitizedUrl.includes('media.strapiapp.com')) {
    const directMediaUrl = sanitizedUrl.startsWith('http') ? sanitizedUrl : `https://${sanitizedUrl}`;
    logger.debug('Using direct Strapi media URL (transformToSiteDomainUrl)', { url: directMediaUrl });
    return directMediaUrl.replace(/^http:/, 'https:');
  }

  // If it's not a Strapi URL (doesn't include strapiapp.com), return as is
  if (!sanitizedUrl.includes('strapiapp.com')) {
    return sanitizedUrl.replace(/^http:/, 'https:');
  }

  try {
    const urlObj = new URL(sanitizedUrl.startsWith('http') ? sanitizedUrl : `https://${sanitizedUrl}`);
    const path = urlObj.pathname;
    const siteDomain = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;
    const transformedUrl = `${siteDomain}${path}`;
    logger.debug('Transformed URL for SEO (transformToSiteDomainUrl)', { original: sanitizedUrl, transformed: transformedUrl });
    return transformedUrl.replace(/^http:/, 'https:');
  } catch (error) {
    logger.error('Error transforming URL in transformToSiteDomainUrl', { url: sanitizedUrl, error });
    return sanitizedUrl.replace(/^http:/, 'https:'); // Fallback
  }
}

/**
 * Gets the correct OG image URL for social sharing.
 * Ensures it's an absolute URL, preferably from STRAPI_MEDIA_URL.
 *
 * @param url The image URL from Strapi or a relative path.
 * @returns The properly formatted URL for OG tags, or undefined.
 */
export function getOgImageUrl(url: string | null | undefined): string | undefined {
  if (!url) return undefined;

  let processedUrl = sanitizeUrl(url);
  if (!processedUrl) return undefined;

  // If it's already an absolute URL (likely from sanitizeUrl fixing it or it was already absolute)
  if (processedUrl.startsWith('http://') || processedUrl.startsWith('https://')) {
    return processedUrl.replace(/^http:/, 'https:');
  }

  // If it's a relative path (e.g., /uploads/image.jpg or image.jpg)
  if (STRAPI_MEDIA_URL) {
    const finalUrl = `${STRAPI_MEDIA_URL}${processedUrl.startsWith('/') ? '' : '/'}${processedUrl}`;
    logger.debug('Constructed OG image URL from relative path', { original: url, final: finalUrl });
    return finalUrl.replace(/^http:/, 'https:');
  }
  
  logger.warn('Could not determine OG image URL confidently', { originalUrl: url, processedUrl });
  // Fallback: if STRAPI_MEDIA_URL is not set, try with EFFECTIVE_STRAPI_URL (less ideal)
  if (EFFECTIVE_STRAPI_URL) {
    return `${EFFECTIVE_STRAPI_URL}${processedUrl.startsWith('/') ? '' : '/'}${processedUrl}`.replace(/^http:/, 'https:');
  }
  return undefined;
}

export default {
  getStrapiMediaUrl,
  getProfilePictureUrl,
  getFeaturedImageUrl,
  sanitizeUrl,
  transformToSiteDomainUrl,
  getOgImageUrl,
  SITE_URL // Export SITE_URL for use in other files
};
