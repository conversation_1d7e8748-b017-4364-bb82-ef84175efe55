{"kind": "collectionType", "collectionName": "authors", "info": {"singularName": "author", "pluralName": "authors", "displayName": "Author (Blog)", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name"}, "profilePicture": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "bio": {"type": "richtext"}, "email": {"type": "email"}, "website": {"type": "string"}, "blog_posts": {"type": "relation", "relation": "manyToMany", "target": "api::blog-post.blog-post", "inversedBy": "author_blogs"}}}