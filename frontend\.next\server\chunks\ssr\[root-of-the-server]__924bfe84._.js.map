{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx <module evaluation>\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/contexts/ErrorContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ErrorProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorProvider() from the server but ErrorProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ErrorContext.tsx <module evaluation>\",\n    \"ErrorProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/contexts/ErrorContext.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ErrorContext.tsx <module evaluation>\",\n    \"default\",\n);\nexport const useError = registerClientReference(\n    function() { throw new Error(\"Attempted to call useError() from the server but useError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ErrorContext.tsx <module evaluation>\",\n    \"useError\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/contexts/ErrorContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ErrorProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorProvider() from the server but ErrorProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ErrorContext.tsx\",\n    \"ErrorProvider\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/contexts/ErrorContext.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ErrorContext.tsx\",\n    \"default\",\n);\nexport const useError = registerClientReference(\n    function() { throw new Error(\"Attempted to call useError() from the server but useError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ErrorContext.tsx\",\n    \"useError\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/layout/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/layout/Layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/providers/QueryProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/QueryProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/QueryProvider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/providers/QueryProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/QueryProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/QueryProvider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/strapi.ts"], "sourcesContent": ["import qs from 'qs'; // Import qs\r\n\r\n// Define parameter type interfaces for Strapi v5\r\ninterface StrapiPagination {\r\n  page?: number;\r\n  pageSize?: number;\r\n  start?: number; // For offset pagination\r\n  limit?: number; // For limit pagination\r\n}\r\n\r\ninterface SortParams {\r\n  sort?: string | string[];\r\n}\r\n\r\ninterface FilterParams {\r\n  [key: string]: any;\r\n  filters?: FilterParams;\r\n  populate?: string | string[] | Record<string, any>;\r\n  fields?: string[]; // To select specific fields\r\n  publicationState?: 'live' | 'preview';\r\n  locale?: string;\r\n}\r\n\r\n// Combine interfaces for the main query parameters object\r\ninterface QueryParams extends SortParams, FilterParams {\r\n  pagination?: StrapiPagination;\r\n  populate?: string | string[] | Record<string, any>;\r\n  fields?: string[];\r\n  publicationState?: 'live' | 'preview';\r\n  locale?: string;\r\n  // Allow other potential top-level params, though filters/pagination/sort/populate/fields are standard\r\n  [key: string]: any;\r\n}\r\n\r\n// Get the API URL from environment variables\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL;\r\n\r\n// Ensure we have a valid API URL, especially in production\r\nif (!API_URL) {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    console.error('CRITICAL ERROR: NEXT_PUBLIC_API_URL is not defined in environment variables');\r\n  } else {\r\n    console.warn('WARNING: NEXT_PUBLIC_API_URL is not defined in environment variables, using fallback');\r\n  }\r\n}\r\n\r\n// Define a fallback URL for development only\r\nconst FALLBACK_URL = process.env.NODE_ENV === 'development' ? 'http://localhost:1337' : '';\r\n\r\n// Use the API_URL if available, otherwise use the fallback (only in development)\r\nconst EFFECTIVE_API_URL = API_URL || FALLBACK_URL;\r\n\r\n// Always log the API URL being used\r\nconsole.log('Using Strapi API URL:', EFFECTIVE_API_URL || 'No API URL found in environment variables');\r\n\r\n// Get the Strapi Media URL for images (used for debugging)\r\nconst STRAPI_MEDIA_URL = EFFECTIVE_API_URL ?\r\n  EFFECTIVE_API_URL.replace('strapiapp.com', 'media.strapiapp.com') :\r\n  '';\r\n\r\n// Log the media URL for debugging\r\nif (STRAPI_MEDIA_URL) {\r\n  console.log('Strapi Media URL:', STRAPI_MEDIA_URL);\r\n}\r\n\r\n// Define interface for Next.js cache options\r\ninterface NextCacheOptions {\r\n  revalidate?: number | false;\r\n  tags?: string[];\r\n}\r\n\r\n// Define interface for fetchAPI options\r\ninterface FetchAPIOptions {\r\n  params?: any;\r\n  headers?: Record<string, string>;\r\n  next?: NextCacheOptions;\r\n  [key: string]: any;\r\n}\r\n\r\n// Helper function to handle API responses and errors\r\nexport const fetchAPI = async (endpoint: string, options: FetchAPIOptions = {}) => {\r\n  const requestId = Math.random().toString(36).substring(2, 8);\r\n  // Construct the full URL for the fetch request\r\n  // Ensure endpoint starts with a slash if it's not already relative from /api\r\n  const apiPath = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\r\n  let urlForFetch = `${EFFECTIVE_API_URL}/api${apiPath}`;\r\n\r\n  try {\r\n    const API_TOKEN = process.env.STRAPI_API_TOKEN;\r\n    const baseHeaders: HeadersInit = {\r\n      'Content-Type': 'application/json',\r\n      ...(API_TOKEN && { Authorization: `Bearer ${API_TOKEN}` }),\r\n    };\r\n\r\n    // In Next.js 15, fetch is uncached by default\r\n    // We need to explicitly opt-in to caching with 'force-cache'\r\n    // or set a revalidation period\r\n\r\n    // Determine the cache setting based on revalidate option\r\n    let cacheOption: RequestCache = 'no-store'; // Default to no-store in Next.js 15\r\n\r\n    // If options.cache is explicitly provided, use it\r\n    if (options.cache) {\r\n      cacheOption = options.cache as RequestCache;\r\n    }\r\n    // Otherwise, derive from revalidate for Next.js 15 ISR behavior\r\n    // For ISR (revalidate = false or revalidate > 0), we need 'force-cache'.\r\n    // For dynamic rendering (revalidate = 0), we need 'no-store'.\r\n    // If revalidate is undefined, default to 'force-cache' to support ISR pages\r\n    // that don't explicitly pass revalidate: false in every fetch call.\r\n    else if (options.next?.revalidate !== undefined) {\r\n      cacheOption = options.next.revalidate === 0 ? 'no-store' : 'force-cache';\r\n    } else {\r\n      // Default to 'force-cache' if revalidate is not specified,\r\n      // to ensure ISR pages (which might not pass revalidate on every fetch) still cache.\r\n      // This aligns with the need to opt-into caching for ISR.\r\n      cacheOption = 'force-cache';\r\n    }\r\n\r\n    // Log cache settings in development for debugging\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`[${requestId}] Fetch cache settings for ${endpoint}:`, {\r\n        cache: cacheOption,\r\n        revalidate: options.next?.revalidate,\r\n        tags: options.next?.tags || []\r\n      });\r\n    }\r\n\r\n    const fetchOptions: RequestInit = {\r\n      method: options.method || 'GET',\r\n      headers: {\r\n        ...baseHeaders,\r\n        ...(options.headers || {}),\r\n      },\r\n      // Explicitly set cache option for Next.js 15 compatibility\r\n      cache: cacheOption,\r\n      // Ensure next options (like tags and revalidate) are consistently applied\r\n      next: {\r\n        // Default to an empty array for tags if not provided\r\n        tags: options.next?.tags || [],\r\n        // Preserve revalidate if explicitly set\r\n        ...(options.next?.revalidate !== undefined && { revalidate: options.next.revalidate }),\r\n      },\r\n    };\r\n\r\n    // If revalidate is specifically false (cache forever), ensure it's passed correctly\r\n    if (options.next?.revalidate === false && fetchOptions.next) {\r\n      fetchOptions.next.revalidate = false;\r\n    }\r\n\r\n    // Handle query parameters for GET requests using 'qs'\r\n    if ((fetchOptions.method === 'GET' || fetchOptions.method === 'HEAD') && options.params) {\r\n      const queryString = qs.stringify(options.params, { encodeValuesOnly: true });\r\n      if (queryString) {\r\n        urlForFetch = `${urlForFetch}?${queryString}`;\r\n      }\r\n\r\n    } else if (options.params && fetchOptions.method !== 'GET' && fetchOptions.method !== 'HEAD') {\r\n      // For POST, PUT, etc., set the body\r\n      fetchOptions.body = JSON.stringify(options.params);\r\n    }\r\n\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`[${requestId}] Requesting Strapi API (native fetch): ${urlForFetch}`);\r\n      if (fetchOptions.next?.tags) console.log(`[${requestId}] Using cache tags:`, fetchOptions.next.tags);\r\n      if (fetchOptions.next?.revalidate !== undefined) console.log(`[${requestId}] Using revalidation:`, fetchOptions.next.revalidate);\r\n      if (options.params) console.log(`[${requestId}] Request params (raw):`, JSON.stringify(options.params, null, 2));\r\n    }\r\n\r\n    const response = await fetch(urlForFetch, fetchOptions);\r\n\r\n    if (!response.ok) {\r\n      let errorData;\r\n      try {\r\n        errorData = await response.json();\r\n      } catch (e) {\r\n        errorData = { message: response.statusText, details: await response.text().catch(() => '') };\r\n      }\r\n      console.error(`[${requestId}] API Error (${urlForFetch}): Status ${response.status}`, errorData);\r\n      const error = new Error(`API Error: ${response.status} ${response.statusText}`);\r\n      // @ts-ignore\r\n      error.response = { status: response.status, data: errorData };\r\n      throw error;\r\n    }\r\n\r\n    const responseData = await response.json();\r\n\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`[${requestId}] API request successful for ${urlForFetch}`);\r\n      if (responseData) {\r\n        console.log(`[${requestId}] Response structure:`, JSON.stringify({\r\n          hasData: !!responseData?.data,\r\n          dataType: responseData?.data ? (Array.isArray(responseData.data) ? 'array' : 'object') : 'none',\r\n          dataLength: responseData?.data && Array.isArray(responseData.data) ? responseData.data.length : 'n/a',\r\n          hasMeta: !!responseData?.meta,\r\n        }, null, 2));\r\n      }\r\n    }\r\n    return responseData;\r\n\r\n  } catch (error: any) {\r\n    const isProduction = process.env.NODE_ENV === 'production';\r\n    if (isProduction) {\r\n      console.error(`[${requestId}] API Error (${urlForFetch}):`, error.message || 'Unknown error');\r\n      if (error.response?.status) {\r\n        console.error(`[${requestId}] Status:`, error.response.status);\r\n      }\r\n    } else {\r\n      console.error(`[${requestId}] Error fetching from API (${urlForFetch}):`, error.message || 'Unknown error');\r\n      if (error.response) {\r\n        console.error(`[${requestId}] Response status:`, error.response.status);\r\n        console.error(`[${requestId}] Response data:`, error.response.data);\r\n      } else if (error.request) { // This part is more relevant for Axios, fetch won't have error.request\r\n        console.error(`[${requestId}] No response received. Request details for ${urlForFetch}`);\r\n      } else {\r\n        console.error(`[${requestId}] Error details:`, error);\r\n      }\r\n    }\r\n    if (error.message && error.message.includes('ECONNREFUSED')) { // General ECONNREFUSED\r\n      console.error(`[${requestId}] CRITICAL ERROR: Connection refused for ${urlForFetch}.`);\r\n      if (urlForFetch.includes('127.0.0.1') || urlForFetch.includes('localhost')) {\r\n        console.error(`[${requestId}] Attempting to connect to localhost. Check NEXT_PUBLIC_API_URL in Vercel environment variables if this is a deployed environment.`);\r\n      }\r\n    }\r\n    if (error.response?.status === 400) {\r\n      console.error(`[${requestId}] Bad Request (400) - Check query parameters for Strapi v5 compatibility.`);\r\n    }\r\n    error.requestId = requestId;\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Define specific params type for getAll functions needing search/pagination\r\ninterface GetAllParams extends QueryParams {\r\n  cache?: RequestCache; // Cache option for fetch\r\n  next?: NextCacheOptions; // Explicitly add next for Next.js caching options\r\n  query?: string; // For name/keyword search\r\n  location?: string; // For location search\r\n  specialtySlug?: string; // For filtering by specialty\r\n  conditionSlug?: string; // For filtering by condition\r\n  categorySlug?: string; // For filtering by category\r\n  page?: number;\r\n  pageSize?: number; // Allow overriding default page size\r\n}\r\n\r\n// Typed API functions for specific content types\r\nexport const getStrapiContent = {\r\n  // Clinic related queries\r\n  clinics: {\r\n    getAll: async (params: GetAllParams = {}) => {\r\n      const { query, location, specialtySlug, conditionSlug, categorySlug, page = 1, pageSize = 12, cache, next, ...restParams } = params; // Destructure slugs and separate cache/next options\r\n\r\n\r\n\r\n      // Base query params (exclude cache and next options)\r\n      const queryParams: QueryParams = {\r\n        ...restParams,\r\n        populate: restParams.populate || '*',\r\n        publicationState: 'live',\r\n        pagination: { // Add pagination object\r\n          page: page,\r\n          pageSize: pageSize,\r\n        },\r\n        filters: { // Add filters object\r\n          ...(restParams.filters || {}), // Keep existing filters if any\r\n        }\r\n      };\r\n\r\n      // Build filters dynamically\r\n      const filters: FilterParams = { ...(restParams.filters || {}) };\r\n      let combinedFilters: any[] = []; // Use an array for $and\r\n\r\n      // Name/Service/Specialty/Condition filter using $or\r\n      if (query) {\r\n        combinedFilters.push({\r\n          $or: [\r\n            { name: { $containsi: query } },\r\n            { services: { name: { $containsi: query } } },\r\n            { specialties: { name: { $containsi: query } } },\r\n            { conditions: { name: { $containsi: query } } },\r\n            { categories: { name: { $containsi: query } } },\r\n            { description: { $containsi: query } } // Added clinic description search\r\n          ]\r\n        });\r\n      }\r\n\r\n      // Location filter - Revised structure: $or inside the component filter\r\n      if (location) {\r\n        combinedFilters.push({\r\n          address: { // Target the component\r\n            $or: [ // Apply OR logic *within* the component's fields (Removed streetAddress2)\r\n              { streetAddress1: { $containsi: location } },\r\n              // { streetAddress2: { $containsi: location } }, // Removed invalid key\r\n              { city: { $containsi: location } },\r\n              { stateProvince: { $containsi: location } },\r\n              { postalCode: { $containsi: location } },\r\n            ]\r\n          }\r\n        });\r\n      }\r\n\r\n      // Specialty filter\r\n      if (specialtySlug) {\r\n         combinedFilters.push({ specialties: { slug: { $eq: specialtySlug } } });\r\n      }\r\n\r\n      // Condition filter\r\n      if (conditionSlug) {\r\n         combinedFilters.push({ conditions: { slug: { $eq: conditionSlug } } });\r\n      }\r\n\r\n      // Category filter\r\n      if (categorySlug) {\r\n         combinedFilters.push({ categories: { slug: { $eq: categorySlug } } });\r\n      }\r\n\r\n      // Combine all filters using $and if multiple filters exist\r\n      if (combinedFilters.length > 1) {\r\n        filters.$and = combinedFilters;\r\n      } else if (combinedFilters.length === 1) {\r\n        Object.assign(filters, combinedFilters[0]);\r\n      }\r\n\r\n      // Assign the constructed filters back to queryParams\r\n      queryParams.filters = filters;\r\n\r\n      // Add cache tags if specialtySlug is provided\r\n      if (specialtySlug) {\r\n        // Define cache tags for specialty-related queries\r\n        const cacheTags = [\r\n          'strapi-clinics',\r\n          'strapi-specialties',\r\n          `strapi-specialty-${specialtySlug}`,\r\n          `strapi-specialty-${specialtySlug}-clinics`\r\n        ];\r\n\r\n        // Add page number to cache tags\r\n        cacheTags.push(`strapi-specialty-${specialtySlug}-page-${page}`);\r\n\r\n        // Add query to cache tags if provided\r\n        if (query) {\r\n          cacheTags.push(`strapi-specialty-${specialtySlug}-query-${query}`);\r\n        }\r\n\r\n        // Add location to cache tags if provided\r\n        if (location) {\r\n          cacheTags.push(`strapi-specialty-${specialtySlug}-location-${location}`);\r\n        }\r\n\r\n        // Fetch clinics with cache tags\r\n        const nextOptionsForSpecialty: NextCacheOptions = { tags: cacheTags };\r\n        if (next?.revalidate !== undefined) {\r\n          nextOptionsForSpecialty.revalidate = next.revalidate;\r\n        }\r\n        return fetchAPI(`/clinics`, {\r\n          params: queryParams,\r\n          cache: cache,\r\n          next: nextOptionsForSpecialty\r\n        });\r\n      }\r\n\r\n      // Add cache tags if categorySlug is provided\r\n      if (categorySlug) {\r\n        // Define cache tags for category-related queries\r\n        const cacheTags = [\r\n          'strapi-clinics',\r\n          'strapi-categories',\r\n          `strapi-category-${categorySlug}`,\r\n          `strapi-category-${categorySlug}-clinics`\r\n        ];\r\n\r\n        // Add page number to cache tags\r\n        cacheTags.push(`strapi-category-${categorySlug}-page-${page}`);\r\n\r\n        // Add query to cache tags if provided\r\n        if (query) {\r\n          cacheTags.push(`strapi-category-${categorySlug}-query-${query}`);\r\n        }\r\n\r\n        // Add location to cache tags if provided\r\n        if (location) {\r\n          cacheTags.push(`strapi-category-${categorySlug}-location-${location}`);\r\n        }\r\n\r\n        // Fetch clinics with cache tags\r\n        const nextOptionsForCategory: NextCacheOptions = { tags: cacheTags };\r\n        if (next?.revalidate !== undefined) {\r\n          nextOptionsForCategory.revalidate = next.revalidate;\r\n        }\r\n        return fetchAPI(`/clinics`, {\r\n          params: queryParams,\r\n          cache: cache,\r\n          next: nextOptionsForCategory\r\n        });\r\n      }\r\n\r\n      // Define default cache tags for clinic list\r\n      const defaultCacheTags = ['strapi-clinics-list'];\r\n\r\n      // Add page number to cache tags\r\n      const pageNumber = queryParams.pagination?.page || 1;\r\n      defaultCacheTags.push(`strapi-clinics-page-${pageNumber}`);\r\n\r\n      // Add query to cache tags if provided\r\n      if (query) {\r\n        defaultCacheTags.push(`strapi-clinics-query-${query}`);\r\n      }\r\n\r\n      // Add location to cache tags if provided\r\n      if (location) {\r\n        defaultCacheTags.push(`strapi-clinics-location-${location}`);\r\n      }\r\n\r\n      // Merge with any existing cache tags from next options\r\n      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];\r\n\r\n      const nextOptionsDefault: NextCacheOptions = { tags: effectiveTags };\r\n      if (next?.revalidate !== undefined) {\r\n        nextOptionsDefault.revalidate = next.revalidate;\r\n      }\r\n      // Fetch clinics with cache tags\r\n      return fetchAPI(`/clinics`, {\r\n        params: queryParams,\r\n        cache: cache,\r\n        next: nextOptionsDefault\r\n      });\r\n    },\r\n    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => { // Fetch only slugs\r\n      // Default tags for clinic slugs\r\n      const defaultTags = ['strapi-clinics-slugs'];\r\n\r\n      // Combine default tags with any provided tags\r\n      const combinedTags = [...defaultTags, ...(options.next?.tags || [])];\r\n\r\n      // In Next.js 15, we need to explicitly opt-in to caching\r\n      return fetchAPI(`/clinics`, {\r\n        params: {\r\n          fields: ['slug'], // Only fetch the slug field\r\n          pagination: {\r\n            pageSize: 250, // Adjust if you have more clinics, or implement proper pagination\r\n          },\r\n        },\r\n        // Explicitly set cache option for Next.js 15\r\n        cache: options.cache || 'force-cache',\r\n        next: {\r\n          // Use combined tags\r\n          tags: combinedTags,\r\n          // Default to 12 hours revalidation if not specified\r\n          revalidate: options.next?.revalidate ?? 43200,\r\n          // Spread other next options\r\n          ...(options.next || {})\r\n        },\r\n      });\r\n    },\r\n    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {\r\n      const defaultTags = ['strapi-clinics', `strapi-clinic-${slug}`];\r\n\r\n      // Combine default tags with any provided tags\r\n      const combinedTags = [...defaultTags, ...(options.next?.tags || [])];\r\n\r\n      // In Next.js 15, we need to explicitly opt-in to caching\r\n      return fetchAPI(`/clinics`, {\r\n        params: {\r\n          filters: { slug: { $eq: slug } },\r\n          populate: {\r\n            logo: true,\r\n            address: true,\r\n            contactInfo: true,\r\n            location: true,\r\n            openingHours: true,\r\n            services: true,\r\n            specialties: true,\r\n            conditions: true,\r\n            practitioners: { populate: { profilePicture: true } },\r\n            appointment_options: true,\r\n            payment_methods: true,\r\n            seo: true,\r\n          }\r\n        },\r\n        // Explicitly set cache option for Next.js 15\r\n        cache: options.cache || 'force-cache',\r\n        next: {\r\n          ...(options.next || {}),\r\n          // Use combined tags\r\n          tags: combinedTags,\r\n          // Default to 12 hours revalidation if not specified\r\n          revalidate: options.next?.revalidate ?? 43200\r\n        }\r\n      });\r\n    },\r\n    getFeatured: async (options: FetchAPIOptions = {}) => fetchAPI(`/clinics`, {\r\n      params: {\r\n        filters: { isFeatured: { $eq: true } },\r\n        populate: '*'\r\n      },\r\n      ...options\r\n    }),\r\n    // New function to get clinics by category slug with pagination and filtering\r\n    getByCategorySlug: async ({\r\n      slug,\r\n      query = '',\r\n      location = '',\r\n      page = 1,\r\n      pageSize = 12\r\n    }: {\r\n      slug: string;\r\n      query?: string;\r\n      location?: string;\r\n      page?: number;\r\n      pageSize?: number;\r\n    }) => {\r\n      try {\r\n        console.log(`Fetching clinics for category slug: ${slug}, page: ${page}, query: ${query}, location: ${location}`);\r\n\r\n        // Build filters using the same approach as getAll function\r\n        const filters: FilterParams = {\r\n          categories: { slug: { $eq: slug } }\r\n        };\r\n\r\n        // Build combined filters array for complex queries\r\n        const combinedFilters: any[] = [];\r\n\r\n        // Always include the category filter\r\n        combinedFilters.push({ categories: { slug: { $eq: slug } } });\r\n\r\n        // Add search query if provided\r\n        if (query) {\r\n          combinedFilters.push({\r\n            $or: [\r\n              { name: { $containsi: query } },\r\n              { services: { name: { $containsi: query } } },\r\n              { specialties: { name: { $containsi: query } } },\r\n              { conditions: { name: { $containsi: query } } }\r\n            ]\r\n          });\r\n        }\r\n\r\n        // Add location filter if provided\r\n        if (location) {\r\n          combinedFilters.push({\r\n            address: {\r\n              $or: [\r\n                { streetAddress1: { $containsi: location } },\r\n                { city: { $containsi: location } },\r\n                { stateProvince: { $containsi: location } },\r\n                { postalCode: { $containsi: location } },\r\n              ]\r\n            }\r\n          });\r\n        }\r\n\r\n        // Use $and only if we have multiple filters\r\n        const finalFilters: FilterParams = combinedFilters.length > 1\r\n          ? { $and: combinedFilters }\r\n          : filters;\r\n\r\n        // Construct query parameters\r\n        const queryParams: QueryParams = {\r\n          filters: finalFilters,\r\n          pagination: {\r\n            page,\r\n            pageSize\r\n          },\r\n          populate: {\r\n            logo: true,\r\n            featuredImage: true,\r\n            address: true,\r\n            contactInfo: true,\r\n            categories: true\r\n          },\r\n          publicationState: 'live'\r\n        };\r\n\r\n        // Define cache tags for this request to enable targeted revalidation\r\n        const cacheTags = [\r\n          'strapi-clinics',\r\n          'strapi-categories',\r\n          `strapi-category-${slug}`,\r\n          `strapi-category-${slug}-clinics`,\r\n          `strapi-category-${slug}-page-${page}`\r\n        ];\r\n\r\n        // Add the query to the cache tag if it exists\r\n        if (query) {\r\n          cacheTags.push(`strapi-category-${slug}-query-${query}`);\r\n        }\r\n\r\n        // Add the location to the cache tag if it exists\r\n        if (location) {\r\n          cacheTags.push(`strapi-category-${slug}-location-${location}`);\r\n        }\r\n\r\n\r\n\r\n        // Use fetchAPI with cache tags\r\n        return fetchAPI(`/clinics`, {\r\n          params: queryParams,\r\n          next: {\r\n            tags: cacheTags\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(`Error in getByCategorySlug for clinics with slug ${slug}:`, error);\r\n        throw error;\r\n      }\r\n    },\r\n  },\r\n\r\n  // Practitioner related queries\r\n  practitioners: {\r\n    getAll: async (params: GetAllParams = {}) => { // Use GetAllParams\r\n      const { query, location, specialtySlug, conditionSlug, categorySlug, page = 1, pageSize = 12, cache, next, ...restParams } = params; // Destructure slugs and separate cache/next options\r\n\r\n\r\n\r\n      // Base query params (exclude cache and next options)\r\n      const queryParams: QueryParams = {\r\n        ...restParams,\r\n        populate: restParams.populate || '*',\r\n        publicationState: 'live',\r\n        pagination: { // Add pagination object\r\n          page: page,\r\n          pageSize: pageSize,\r\n        },\r\n        filters: { // Add filters object\r\n          ...(restParams.filters || {}), // Keep existing filters if any\r\n        }\r\n      };\r\n\r\n      // Build filters dynamically\r\n      const filters: FilterParams = { ...(restParams.filters || {}) };\r\n      let combinedFilters: any[] = []; // Use an array for $and\r\n\r\n      // Name/Specialty/Condition filter using $or\r\n      if (query) {\r\n        combinedFilters.push({\r\n          $or: [\r\n            { name: { $containsi: query } },\r\n            { specialties: { name: { $containsi: query } } }, // Search in specialty names\r\n            { conditions: { name: { $containsi: query } } },  // Search in condition names\r\n          ]\r\n        });\r\n      }\r\n\r\n      // Location filter (assuming practitioners have direct address component) - Revised structure\r\n      // NOTE: If practitioners are linked via clinics, this filter might need adjustment.\r\n      // PRACTITIONERS DO NOT HAVE LOCATION - DO NOT FILTER BY IT\r\n      // if (location) {\r\n      //    combinedFilters.push({\r\n      //     address: { // Target the component\r\n      //       $or: [ // Apply OR logic *within* the component's fields (Removed streetAddress2)\r\n      //         { streetAddress1: { $containsi: location } },\r\n      //         // { streetAddress2: { $containsi: location } }, // Removed invalid key\r\n      //         { city: { $containsi: location } },\r\n      //         { stateProvince: { $containsi: location } },\r\n      //         { postalCode: { $containsi: location } },\r\n      //       ]\r\n      //     }\r\n      //   });\r\n      // }\r\n\r\n      // Specialty filter\r\n      if (specialtySlug) {\r\n         combinedFilters.push({ specialties: { slug: { $eq: specialtySlug } } });\r\n      }\r\n\r\n      // Condition filter\r\n      if (conditionSlug) {\r\n         combinedFilters.push({ conditions: { slug: { $eq: conditionSlug } } });\r\n      }\r\n\r\n      // Category filter\r\n      if (categorySlug) {\r\n         combinedFilters.push({ categories: { slug: { $eq: categorySlug } } });\r\n      }\r\n\r\n      // Combine all filters using $and if multiple filters exist\r\n      if (combinedFilters.length > 1) {\r\n        filters.$and = combinedFilters;\r\n      } else if (combinedFilters.length === 1) {\r\n        Object.assign(filters, combinedFilters[0]);\r\n      }\r\n\r\n      // Assign the constructed filters back to queryParams\r\n      queryParams.filters = filters;\r\n\r\n      // Add cache tags if specialtySlug is provided\r\n      if (specialtySlug) {\r\n        // Define cache tags for specialty-related queries\r\n        const cacheTags = [\r\n          'strapi-practitioners',\r\n          'strapi-specialties',\r\n          `strapi-specialty-${specialtySlug}`,\r\n          `strapi-specialty-${specialtySlug}-practitioners`\r\n        ];\r\n\r\n        // Add page number to cache tags\r\n        cacheTags.push(`strapi-specialty-${specialtySlug}-page-${page}`);\r\n\r\n        // Add query to cache tags if provided\r\n        if (query) {\r\n          cacheTags.push(`strapi-specialty-${specialtySlug}-query-${query}`);\r\n        }\r\n\r\n        // Add location to cache tags if provided\r\n        if (location) {\r\n          cacheTags.push(`strapi-specialty-${specialtySlug}-location-${location}`);\r\n        }\r\n\r\n        // Fetch practitioners with cache tags\r\n        const nextOptionsForSpecialty: NextCacheOptions = { tags: cacheTags };\r\n        if (next?.revalidate !== undefined) {\r\n          nextOptionsForSpecialty.revalidate = next.revalidate;\r\n        }\r\n        return fetchAPI(`/practitioners`, {\r\n          params: queryParams,\r\n          cache: cache,\r\n          next: nextOptionsForSpecialty\r\n        });\r\n      }\r\n\r\n      // Add cache tags if categorySlug is provided\r\n      if (categorySlug) {\r\n        // Define cache tags for category-related queries\r\n        const cacheTags = [\r\n          'strapi-practitioners',\r\n          'strapi-categories',\r\n          `strapi-category-${categorySlug}`,\r\n          `strapi-category-${categorySlug}-practitioners`\r\n        ];\r\n\r\n        // Add page number to cache tags\r\n        cacheTags.push(`strapi-category-${categorySlug}-page-${page}`);\r\n\r\n        // Add query to cache tags if provided\r\n        if (query) {\r\n          cacheTags.push(`strapi-category-${categorySlug}-query-${query}`);\r\n        }\r\n\r\n        // Add location to cache tags if provided\r\n        if (location) {\r\n          cacheTags.push(`strapi-category-${categorySlug}-location-${location}`);\r\n        }\r\n\r\n        // Fetch practitioners with cache tags\r\n        const nextOptionsForCategory: NextCacheOptions = { tags: cacheTags };\r\n        if (next?.revalidate !== undefined) {\r\n          nextOptionsForCategory.revalidate = next.revalidate;\r\n        }\r\n        return fetchAPI(`/practitioners`, {\r\n          params: queryParams,\r\n          cache: cache,\r\n          next: nextOptionsForCategory\r\n        });\r\n      }\r\n\r\n      // Define default cache tags for practitioner list\r\n      const defaultCacheTags = ['strapi-practitioners-list'];\r\n\r\n      // Add page number to cache tags\r\n      const pageNumber = queryParams.pagination?.page || 1;\r\n      defaultCacheTags.push(`strapi-practitioners-page-${pageNumber}`);\r\n\r\n      // Add query to cache tags if provided\r\n      if (query) {\r\n        defaultCacheTags.push(`strapi-practitioners-query-${query}`);\r\n      }\r\n\r\n      // Add location to cache tags if provided\r\n      if (location) {\r\n        defaultCacheTags.push(`strapi-practitioners-location-${location}`);\r\n      }\r\n\r\n      // Merge with any existing cache tags from next options\r\n      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];\r\n\r\n      const nextOptionsDefault: NextCacheOptions = { tags: effectiveTags };\r\n      if (next?.revalidate !== undefined) {\r\n        nextOptionsDefault.revalidate = next.revalidate;\r\n      }\r\n      // Fetch practitioners with cache tags\r\n      return fetchAPI(`/practitioners`, {\r\n        params: queryParams,\r\n        cache: cache,\r\n        next: nextOptionsDefault\r\n      });\r\n    },\r\n    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {\r\n      const allSlugs: { slug: string }[] = [];\r\n      let page = 1;\r\n      // Strapi's default max page size is often 100. Using a safe common limit.\r\n      // If your Strapi instance is configured for a higher limit, this can be adjusted.\r\n      const effectivePageSize = 100;\r\n      let moreSlugsToFetch = true;\r\n\r\n      const defaultTags = ['strapi-practitioners-slugs'];\r\n      // Consolidate fetch options for reuse, ensuring cache and next options from params are respected\r\n      const fetchOptionsBase = {\r\n        cache: options.cache || 'force-cache',\r\n        next: {\r\n          revalidate: options.next?.revalidate ?? 43200, // Default to 12 hours\r\n          tags: [...defaultTags, ...(options.next?.tags || [])], // Combine default and provided tags\r\n          ...(options.next || {}), // Spread other next options like specific revalidate or additional tags\r\n        }\r\n      };\r\n      // Ensure tags are unique if combined from multiple sources\r\n      if (fetchOptionsBase.next.tags) {\r\n        fetchOptionsBase.next.tags = Array.from(new Set(fetchOptionsBase.next.tags));\r\n      }\r\n\r\n\r\n      while (moreSlugsToFetch) {\r\n        try {\r\n          const response = await fetchAPI(`/practitioners`, {\r\n            params: {\r\n              fields: ['slug'],\r\n              pagination: {\r\n                page: page,\r\n                pageSize: effectivePageSize,\r\n              },\r\n            },\r\n            // Pass the consolidated cache/next options\r\n            cache: fetchOptionsBase.cache,\r\n            next: fetchOptionsBase.next,\r\n          });\r\n\r\n          if (response?.data && Array.isArray(response.data)) {\r\n            response.data.forEach((item: any) => {\r\n              // Strapi v5 flattens the response, so attributes are directly on the item.\r\n              // No need to check item.attributes.slug typically.\r\n              if (item && item.slug) {\r\n                allSlugs.push({ slug: item.slug });\r\n              }\r\n            });\r\n\r\n            // Determine if there are more pages to fetch\r\n            const paginationInfo = response.meta?.pagination;\r\n            if (response.data.length < effectivePageSize || !paginationInfo || page >= paginationInfo.pageCount) {\r\n              moreSlugsToFetch = false;\r\n            } else {\r\n              page++;\r\n            }\r\n          } else {\r\n            // No data or unexpected format\r\n            moreSlugsToFetch = false;\r\n          }\r\n        } catch (error) {\r\n          console.error(`Error fetching page ${page} of practitioner slugs:`, error);\r\n          moreSlugsToFetch = false; // Stop on error\r\n        }\r\n      }\r\n      return { data: allSlugs };\r\n    },\r\n    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {\r\n      const defaultTags = ['strapi-practitioner', `strapi-practitioner-${slug}`]; // Changed 'strapi-practitioners' to 'strapi-practitioner'\r\n      return fetchAPI(`/practitioners`, {\r\n        params: {\r\n          filters: { slug: { $eq: slug } },\r\n          populate: {\r\n            profilePicture: true,\r\n            contactInfo: true,\r\n            specialties: true,\r\n            conditions: true,\r\n            clinics: true,\r\n            seo: true\r\n          }\r\n        },\r\n        // In Next.js 15, we need to explicitly opt-in to caching\r\n        cache: options.cache || 'force-cache',\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultTags, ...(options.next?.tags || [])],\r\n          // Default to 12 hours revalidation if not specified\r\n          revalidate: options.next?.revalidate ?? 43200\r\n        }\r\n      });\r\n    },\r\n    getFeatured: async (options: FetchAPIOptions = {}) => fetchAPI(`/practitioners`, {\r\n      params: {\r\n        filters: { isFeatured: { $eq: true } },\r\n        populate: '*'\r\n      },\r\n      ...options\r\n    }),\r\n    // New function to get practitioners by category slug with pagination and filtering\r\n    getByCategorySlug: async ({\r\n      slug,\r\n      query = '',\r\n      location = '',\r\n      page = 1,\r\n      pageSize = 12\r\n    }: {\r\n      slug: string;\r\n      query?: string;\r\n      location?: string;\r\n      page?: number;\r\n      pageSize?: number;\r\n    }) => {\r\n      try {\r\n\r\n        // Build filters using the same approach as getAll function\r\n        const filters: FilterParams = {\r\n          categories: { slug: { $eq: slug } }\r\n        };\r\n\r\n        // Build combined filters array for complex queries\r\n        const combinedFilters: any[] = [];\r\n\r\n        // Always include the category filter\r\n        combinedFilters.push({ categories: { slug: { $eq: slug } } });\r\n\r\n        // Add search query if provided\r\n        if (query) {\r\n          combinedFilters.push({\r\n            $or: [\r\n              { name: { $containsi: query } },\r\n              { title: { $containsi: query } },\r\n              { qualifications: { $containsi: query } },\r\n              { specialties: { name: { $containsi: query } } },\r\n              { conditions: { name: { $containsi: query } } }\r\n            ]\r\n          });\r\n        }\r\n\r\n        // Add location filter if provided\r\n        if (location) {\r\n          combinedFilters.push({\r\n            address: {\r\n              $or: [\r\n                { streetAddress1: { $containsi: location } },\r\n                { city: { $containsi: location } },\r\n                { stateProvince: { $containsi: location } },\r\n                { postalCode: { $containsi: location } },\r\n              ]\r\n            }\r\n          });\r\n        }\r\n\r\n        // Use $and only if we have multiple filters\r\n        const finalFilters: FilterParams = combinedFilters.length > 1\r\n          ? { $and: combinedFilters }\r\n          : filters;\r\n\r\n        // Construct query parameters\r\n        const queryParams: QueryParams = {\r\n          filters: finalFilters,\r\n          pagination: {\r\n            page,\r\n            pageSize\r\n          },\r\n          populate: {\r\n            profilePicture: true,\r\n            contactInfo: true,\r\n            specialties: true,\r\n            conditions: true,\r\n            categories: true\r\n          },\r\n          publicationState: 'live'\r\n        };\r\n\r\n        // Define cache tags for this request to enable targeted revalidation\r\n        const cacheTags = [\r\n          'strapi-practitioners',\r\n          'strapi-categories',\r\n          `strapi-category-${slug}`,\r\n          `strapi-category-${slug}-practitioners`,\r\n          `strapi-category-${slug}-page-${page}`\r\n        ];\r\n\r\n        // Add the query to the cache tag if it exists\r\n        if (query) {\r\n          cacheTags.push(`strapi-category-${slug}-query-${query}`);\r\n        }\r\n\r\n        // Add the location to the cache tag if it exists\r\n        if (location) {\r\n          cacheTags.push(`strapi-category-${slug}-location-${location}`);\r\n        }\r\n\r\n\r\n\r\n        // Use fetchAPI with cache tags\r\n        return fetchAPI(`/practitioners`, {\r\n          params: queryParams,\r\n          next: {\r\n            tags: cacheTags\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(`Error in getByCategorySlug for practitioners with slug ${slug}:`, error);\r\n        throw error;\r\n      }\r\n    },\r\n  },\r\n\r\n  // Category related queries\r\n  categories: {\r\n    getAll: async (params: GetAllParams = {}) => { // Use GetAllParams\r\n       const { query, location, page = 1, pageSize = 12, cache, next, ...restParams } = params; // Destructure cache/next options\r\n\r\n      // Base query params (exclude cache and next options)\r\n      const queryParams: QueryParams = {\r\n        ...restParams,\r\n        populate: restParams.populate || {\r\n          // Explicitly populate media fields to ensure proper structure\r\n          icon: true,\r\n          featuredImage: true,\r\n          // Include any other fields that need to be populated\r\n        },\r\n        publicationState: 'live',\r\n        pagination: { // Add pagination object\r\n          page: page,\r\n          pageSize: pageSize,\r\n        },\r\n        filters: { // Add filters object\r\n          ...(restParams.filters || {}), // Keep existing filters if any\r\n        }\r\n      };\r\n\r\n      // Add name filter if query is provided\r\n      if (query && queryParams.filters) {\r\n        queryParams.filters.name = { $containsi: query };\r\n      }\r\n\r\n      // Define cache tags for this request to enable targeted revalidation\r\n      const defaultCacheTags = ['strapi-categories', 'strapi-categories-slugs'];\r\n\r\n      // Add page number to cache tags\r\n      if (queryParams.pagination?.page) defaultCacheTags.push(`strapi-categories-page-${queryParams.pagination.page}`);\r\n\r\n      // Add query to cache tags if provided\r\n      if (query) defaultCacheTags.push(`strapi-categories-query-${query}`);\r\n\r\n      // Merge with any existing cache tags from next options\r\n      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];\r\n\r\n      const nextOptions: NextCacheOptions = { tags: effectiveTags };\r\n      if (next?.revalidate !== undefined) {\r\n        nextOptions.revalidate = next.revalidate;\r\n      }\r\n\r\n      return fetchAPI(`/categories`, {\r\n        params: queryParams, // queryParams should be built excluding 'next' from original params\r\n        cache: cache,\r\n        next: nextOptions\r\n      });\r\n    },\r\n    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {\r\n      const defaultCacheTags = [\r\n        'strapi-categories',\r\n        `strapi-category-${slug}`,\r\n        `strapi-category-${slug}-clinics`,\r\n        `strapi-category-${slug}-practitioners`\r\n      ];\r\n      return fetchAPI(`/categories`, {\r\n        params: {\r\n          filters: { slug: { $eq: slug } },\r\n          populate: {\r\n            seo: { populate: { openGraph: { populate: { ogImage: true } }, metaImage: true } },\r\n            icon: true,\r\n            featuredImage: true,\r\n            clinics: { populate: { logo: true, featuredImage: true, address: true, contactInfo: true } },\r\n            practitioners: { populate: { profilePicture: true, contactInfo: true } }\r\n          },\r\n          publicationState: 'live'\r\n        },\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultCacheTags, ...(options.next?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {\r\n      const defaultTags = ['strapi-categories-slugs'];\r\n      return fetchAPI(`/categories`, {\r\n        params: {\r\n          fields: ['slug'],\r\n          pagination: { pageSize: 1000 }, // Adjust if more items or implement pagination\r\n        },\r\n        cache: options.cache || 'force-cache',\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultTags, ...(options.next?.tags || [])],\r\n          revalidate: options.next?.revalidate ?? 43200, // Default to 12 hours\r\n        }\r\n      });\r\n    },\r\n    // Function to get categories specifically for the footer\r\n    getFooterCategories: async (options: FetchAPIOptions = {}) => {\r\n      const defaultTags = ['strapi-categories', 'strapi-categories-footer'];\r\n      return fetchAPI(`/categories`, {\r\n        params: {\r\n          filters: { showInFooter: { $eq: true } },\r\n          populate: '*',\r\n          publicationState: 'live'\r\n        },\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultTags, ...(options.next?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n  },\r\n\r\n  // Blog related queries\r\n  blog: {\r\n    getPosts: async (params: QueryParams = {}) => {\r\n      try {\r\n        // Create a clean query params object with proper structure for Strapi v5\r\n        const queryParams: Record<string, any> = {\r\n          publicationState: 'live',\r\n        };\r\n\r\n        // Extract pagination parameters\r\n        let page = 1;\r\n        let pageSize = 10;\r\n\r\n        // Handle pagination separately to ensure correct structure\r\n        if (params.pagination) {\r\n          queryParams.pagination = params.pagination;\r\n          page = params.pagination.page || 1;\r\n          pageSize = params.pagination.pageSize || 10;\r\n        } else if (params.page || params.pageSize) {\r\n          // Support for legacy pagination parameters\r\n          page = params.page || 1;\r\n          pageSize = params.pageSize || 10;\r\n          queryParams.pagination = {\r\n            page,\r\n            pageSize\r\n          };\r\n        }\r\n\r\n        // Handle sort parameter\r\n        if (params.sort) {\r\n          queryParams.sort = params.sort;\r\n        } else {\r\n          // Default to newest first if no sort parameter is provided\r\n          queryParams.sort = ['publishDate:desc'];\r\n        }\r\n\r\n        // Handle filters\r\n        if (params.filters) {\r\n          queryParams.filters = params.filters;\r\n        } else if (params.categorySlug) {\r\n          // Support for direct categorySlug parameter\r\n          queryParams.filters = {\r\n            ...(queryParams.filters || {}),\r\n            blog_categories: { slug: { $eq: params.categorySlug } }\r\n          };\r\n        } else if (params.tagSlug) {\r\n          // Support for direct tagSlug parameter\r\n          queryParams.filters = {\r\n            ...(queryParams.filters || {}),\r\n            blog_tags: { slug: { $eq: params.tagSlug } }\r\n          };\r\n        } else if (params.query) {\r\n          // Support for direct query parameter\r\n          queryParams.filters = {\r\n            ...(queryParams.filters || {}),\r\n            $or: [\r\n              { title: { $containsi: params.query } },\r\n              { excerpt: { $containsi: params.query } }\r\n            ]\r\n          };\r\n        }\r\n\r\n        // Handle populate parameter with fallback to default comprehensive fields\r\n        if (params.populate) {\r\n          queryParams.populate = params.populate;\r\n        } else {\r\n          queryParams.populate = {\r\n            featuredImage: true,\r\n            author_blogs: {\r\n              populate: {\r\n                profilePicture: true\r\n              }\r\n            },\r\n            blog_categories: true,\r\n            blog_tags: true\r\n          };\r\n        }\r\n\r\n        // Query parameters prepared for blog posts\r\n\r\n        // Define cache tags for this request to enable targeted revalidation\r\n        const cacheTags = ['strapi-blog-posts'];\r\n\r\n        // Add category-specific cache tag if filtering by category\r\n        if (params.categorySlug) {\r\n          cacheTags.push(`strapi-category-${params.categorySlug}`);\r\n          cacheTags.push(`strapi-category-${params.categorySlug}-page-${page}`);\r\n        }\r\n\r\n        // Add tag-specific cache tag if filtering by tag\r\n        if (params.tagSlug) {\r\n          cacheTags.push(`strapi-tag-${params.tagSlug}`);\r\n          cacheTags.push(`strapi-tag-${params.tagSlug}-page-${page}`);\r\n        }\r\n\r\n        // Add query-specific cache tag if filtering by query\r\n        if (params.query) {\r\n          cacheTags.push(`strapi-blog-query-${params.query}`);\r\n        }\r\n\r\n        // Fetch blog posts with the constructed query parameters and cache tags\r\n        const response = await fetchAPI(`/blog-posts`, {\r\n          params: queryParams,\r\n          next: {\r\n            tags: cacheTags\r\n          }\r\n        });\r\n\r\n        // Log the response structure for debugging\r\n        if (process.env.NODE_ENV === 'development') {\r\n          console.log('Blog getPosts response structure:', JSON.stringify({\r\n            hasData: !!response?.data,\r\n            dataIsArray: Array.isArray(response?.data),\r\n            dataLength: Array.isArray(response?.data) ? response.data.length : 'not an array',\r\n            hasMeta: !!response?.meta,\r\n            hasPagination: !!response?.meta?.pagination,\r\n            totalItems: response?.meta?.pagination?.total || 'unknown'\r\n          }));\r\n        }\r\n\r\n        return response;\r\n      } catch (error: any) { // Type error as any to access response property\r\n        console.error('Error in blog.getPosts:', error);\r\n        // Add more detailed error logging\r\n        if (error.response) {\r\n          console.error('Response status:', error.response.status);\r\n          console.error('Response data:', error.response.data);\r\n          console.error('Response headers:', error.response.headers);\r\n        }\r\n        throw error;\r\n      }\r\n    },\r\n    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {\r\n      const defaultTags = ['strapi-blog-posts-slugs'];\r\n      return fetchAPI(`/blog-posts`, {\r\n        params: {\r\n          fields: ['slug'],\r\n          pagination: { pageSize: 1000 },\r\n        },\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultTags, ...(options.next?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n    getPostBySlug: async (slug: string, options: FetchAPIOptions = {}) => {\r\n      const defaultCacheTags = ['strapi-blog-posts', `strapi-blog-post-${slug}`];\r\n      return fetchAPI(`/blog-posts`, {\r\n        params: {\r\n          filters: { slug: { $eq: slug } },\r\n          populate: {\r\n            seo: { populate: { metaImage: true, openGraph: true } },\r\n            featuredImage: true,\r\n            author_blogs: { fields: ['id', 'name', 'slug', 'bio'], populate: { profilePicture: true } },\r\n            blog_categories: { fields: ['id', 'name', 'slug'] },\r\n            blog_tags: { fields: ['id', 'name', 'slug'] }\r\n          }\r\n        },\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultCacheTags, ...(options.next?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n    getCategories: async (params: QueryParams = {}, fetchOptions: FetchAPIOptions = {}) => {\r\n      const { next: paramsNext, ...strapiParams } = params; // Separate next from Strapi query params\r\n      const queryParams = {\r\n        ...strapiParams,\r\n        publicationState: 'live',\r\n        populate: strapiParams.populate || '*'\r\n      };\r\n      const defaultCacheTags = ['strapi-categories', 'strapi-blog-categories'];\r\n      return fetchAPI(`/blog-categories`, {\r\n        params: queryParams,\r\n        next: {\r\n          ...(fetchOptions.next || {}), // Options passed directly to getCategories\r\n          ...(paramsNext || {}), // Options passed within the params object\r\n          tags: [...defaultCacheTags, ...(fetchOptions.next?.tags || []), ...(paramsNext?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n    getCategoryBySlug: async (slug: string, params: QueryParams = {}, fetchOptions: FetchAPIOptions = {}) => {\r\n      try {\r\n        // Default pagination to show the last 12 blog posts\r\n        const page = params.pagination?.page || 1;\r\n        const pageSize = params.pagination?.pageSize || 12;\r\n\r\n        // Default sort to show the most recent posts first\r\n        const sort = params.sort || 'publishDate:desc';\r\n\r\n        // Prepare to make API call to /blog-categories\r\n\r\n        // Define cache tags for this request to enable targeted revalidation\r\n        const cacheTags = [\r\n          'strapi-categories',\r\n          'strapi-blog-categories',\r\n          `strapi-category-${slug}`,\r\n          `strapi-category-${slug}-page-${page}`\r\n        ];\r\n\r\n        // Improved query structure for Strapi v5\r\n        // The key change is to separate the blog_posts population from pagination\r\n        // This ensures we get the category with ALL its posts, but paginated\r\n        const queryParams = {\r\n          filters: {\r\n            slug: {\r\n              $eq: slug\r\n            }\r\n          },\r\n          // This pagination is for the blog categories themselves\r\n          pagination: {\r\n            page: 1, // We only need the first page as we're filtering by slug\r\n            pageSize: 1 // We only need one category\r\n          },\r\n          sort: [sort],\r\n          populate: {\r\n            blog_posts: {\r\n              fields: ['title', 'slug', 'excerpt', 'publishDate', 'publishedAt'],\r\n              populate: {\r\n                featuredImage: true,\r\n                author_blogs: {\r\n                  populate: {\r\n                    profilePicture: true\r\n                  }\r\n                }\r\n              },\r\n              // This pagination is for the blog posts within the category\r\n              pagination: {\r\n                page,\r\n                pageSize\r\n              },\r\n              sort: [sort]\r\n            },\r\n            seo: true\r\n          }\r\n        };\r\n\r\n        // Query params prepared for slug ${slug}\r\n\r\n        // In Strapi 5, use a simpler query structure\r\n        const response = await fetchAPI(`/blog-categories`, {\r\n          params: queryParams,\r\n          next: {\r\n            tags: cacheTags\r\n          }\r\n        });\r\n\r\n        // Response received for category with slug ${slug}\r\n\r\n        return response;\r\n      } catch (error) {\r\n        console.error(`Error in getCategoryBySlug for slug ${slug}:`, error);\r\n\r\n        // Try a simpler approach as fallback\r\n        try {\r\n\r\n          // Use a very simple query structure as fallback\r\n          const { next: paramsNextFallback, ...strapiParamsFallback } = params;\r\n          return await fetchAPI(`/blog-categories`, {\r\n            params: {\r\n              ...strapiParamsFallback,\r\n              filters: { slug: { $eq: slug } },\r\n              populate: { blog_posts: { populate: ['featuredImage', 'author_blogs.profilePicture'] }, seo: true }\r\n            },\r\n            next: { ...(fetchOptions.next || {}), ...(paramsNextFallback || {}) }\r\n          });\r\n        } catch (fallbackError) {\r\n          console.error(`Fallback also failed for slug ${slug}:`, fallbackError);\r\n          throw fallbackError;\r\n        }\r\n      }\r\n    },\r\n    getTags: async (options: FetchAPIOptions = {}) => {\r\n      const defaultTags = ['strapi-tags', 'strapi-blog-tags'];\r\n      return fetchAPI('/blog-tags', {\r\n        params: { populate: '*' },\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultTags, ...(options.next?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n    getTagBySlug: async (slug: string, options: FetchAPIOptions = {}) => {\r\n      const defaultTags = ['strapi-tags', 'strapi-blog-tags', `strapi-tag-${slug}`];\r\n      return fetchAPI(`/blog-tags`, {\r\n        params: {\r\n          filters: { slug: { $eq: slug } },\r\n          populate: {\r\n            blog_posts: {\r\n              populate: {\r\n                featuredImage: {\r\n                  fields: ['url', 'alternativeText', 'width', 'height', 'formats'] // Be explicit\r\n                },\r\n                author_blogs: {\r\n                  populate: {\r\n                    profilePicture: {\r\n                      fields: ['url', 'alternativeText', 'width', 'height', 'formats'] // Be explicit\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        },\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultTags, ...(options.next?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n    getAuthors: <AUTHORS>\n      // Default to 'force-cache' and 12hr revalidation if not specified by caller\r\n      const cacheSetting = options.cache ?? 'force-cache';\r\n      const revalidateSetting = options.next?.revalidate ?? 43200;\r\n      const defaultTags = ['strapi-authors', 'strapi-blog-authors'];\r\n      return fetchAPI(`/authors`, {\r\n        params: { populate: '*' },\r\n        cache: cacheSetting,\r\n        next: {\r\n          ...options.next, // Spread caller's next options first\r\n          revalidate: revalidateSetting,\r\n          tags: [...defaultTags, ...(options.next?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n    getAuthorBySlug: async (slug: string, options: FetchAPIOptions = {}) => {\r\n      // Default to 'force-cache' and 12hr revalidation if not specified by caller\r\n      const cacheSetting = options.cache ?? 'force-cache';\r\n      const revalidateSetting = options.next?.revalidate ?? 43200;\r\n      const defaultTags = ['strapi-authors', 'strapi-blog-authors', `strapi-author-${slug}`];\r\n      return fetchAPI(`/authors`, {\r\n        params: { filters: { slug: { $eq: slug } }, populate: '*' },\r\n        cache: cacheSetting,\r\n        next: {\r\n          ...options.next, // Spread caller's next options first\r\n          revalidate: revalidateSetting,\r\n          tags: [...defaultTags, ...(options.next?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n  },\r\n\r\n  // Condition related queries\r\n  conditions: {\r\n    getAll: async (params: GetAllParams = {}) => {\r\n      const { query, location, page = 1, pageSize = 12, cache, next, ...restParams } = params;\r\n      const queryParams: QueryParams = {\r\n        ...restParams,\r\n        populate: restParams.populate || '*',\r\n        publicationState: 'live',\r\n        pagination: { page: page, pageSize: pageSize },\r\n        filters: { ...(restParams.filters || {}) }\r\n      };\r\n      if (query && queryParams.filters) queryParams.filters.name = { $containsi: query };\r\n\r\n      // Define cache tags for this request\r\n      const defaultCacheTags = ['strapi-conditions'];\r\n      if (queryParams.pagination?.page) defaultCacheTags.push(`strapi-conditions-page-${queryParams.pagination.page}`);\r\n      if (query) defaultCacheTags.push(`strapi-conditions-query-${query}`);\r\n\r\n      // Merge with any existing cache tags from next options\r\n      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];\r\n\r\n      const nextOptions: NextCacheOptions = { tags: effectiveTags };\r\n      if (next?.revalidate !== undefined) {\r\n        nextOptions.revalidate = next.revalidate;\r\n      }\r\n\r\n      return fetchAPI(`/conditions`, {\r\n        params: queryParams,\r\n        cache: cache,\r\n        next: nextOptions\r\n      });\r\n    },\r\n    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {\r\n      const defaultTags = ['strapi-conditions', `strapi-condition-${slug}`];\r\n      return fetchAPI(`/conditions`, {\r\n        params: { filters: { slug: { $eq: slug } }, populate: { seo: true } },\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultTags, ...(options.next?.tags || [])]\r\n        }\r\n      });\r\n    },\r\n    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {\r\n      const defaultTags = ['strapi-conditions-slugs'];\r\n      return fetchAPI(`/conditions`, {\r\n        params: {\r\n          fields: ['slug'],\r\n          pagination: { pageSize: 1000 }, // Adjust if more items or implement pagination\r\n        },\r\n        cache: options.cache || 'force-cache',\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultTags, ...(options.next?.tags || [])],\r\n          revalidate: options.next?.revalidate ?? 43200,\r\n        }\r\n      });\r\n    },\r\n  },\r\n\r\n  // About Us (Single Type)\r\n  aboutUs: {\r\n    get: async (options: FetchAPIOptions = {}) => fetchAPI(`/about-us`, { params: { populate: '*', publicationState: 'live' }, ...options }),\r\n  },\r\n\r\n  // Privacy Policy (Single Type)\r\n  privacyPolicy: {\r\n    get: async (options: FetchAPIOptions = {}) => fetchAPI(`/privacy-policy`, { params: { populate: '*', publicationState: 'live' }, ...options }),\r\n  },\r\n\r\n  // Terms of Service (Single Type)\r\n  termsOfService: {\r\n    get: async (options: FetchAPIOptions = {}) => fetchAPI(`/terms-of-service`, { params: { populate: '*', publicationState: 'live' }, ...options }),\r\n  },\r\n\r\n  // Affiliate Disclosure (Single Type)\r\n  affiliateDisclosure: {\r\n    get: async (options: FetchAPIOptions = {}) => fetchAPI(`/affiliate-disclosure`, { params: { populate: '*', publicationState: 'live' }, ...options }),\r\n  },\r\n\r\n  // Global settings\r\n  global: {\r\n    getSettings: async (options: FetchAPIOptions = {}) => fetchAPI(`/global-setting`, { params: { populate: '*', publicationState: 'live' }, ...options }),\r\n    getHomepage: async (options: FetchAPIOptions = {}) => fetchAPI(`/homepage`, { params: { populate: '*', publicationState: 'live' }, ...options }),\r\n    getBlogHomepage: async (options: FetchAPIOptions = {}) => fetchAPI(`/blog-homepage`, { params: { populate: '*', publicationState: 'live' }, ...options }),\r\n  },\r\n\r\n  // Specialty related queries\r\n  specialties: {\r\n    getAll: async (params: GetAllParams = {}) => {\r\n      const { query, location, page = 1, pageSize = 12, cache, next, ...restParams } = params;\r\n      const queryParams: QueryParams = {\r\n        ...restParams,\r\n        populate: restParams.populate || '*',\r\n        publicationState: 'live',\r\n        pagination: { page: page, pageSize: pageSize },\r\n        filters: { ...(restParams.filters || {}) }\r\n      };\r\n      if (query && queryParams.filters) queryParams.filters.name = { $containsi: query };\r\n\r\n      // Define cache tags for this request\r\n      const defaultCacheTags = ['strapi-specialties'];\r\n      if (queryParams.pagination?.page) defaultCacheTags.push(`strapi-specialties-page-${queryParams.pagination.page}`);\r\n      if (query) defaultCacheTags.push(`strapi-specialties-query-${query}`);\r\n\r\n      // Merge with any existing cache tags from next options\r\n      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];\r\n\r\n      const nextOptions: NextCacheOptions = { tags: effectiveTags };\r\n      if (next?.revalidate !== undefined) {\r\n        nextOptions.revalidate = next.revalidate;\r\n      }\r\n\r\n      try {\r\n        const response = await fetchAPI(`/specialties`, {\r\n          params: queryParams,\r\n          cache: cache,\r\n          next: nextOptions\r\n        });\r\n\r\n        // If the data is an array and has items, check the first item\r\n        if (response?.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          const firstItem = response.data[0];\r\n\r\n          // If the item doesn't have attributes but has direct properties,\r\n          // transform it to match the expected structure\r\n          if (!firstItem.attributes && firstItem.id) {\r\n            // Create a new array with transformed items\r\n            response.data = response.data.map((item: any) => {\r\n              // Skip if already has attributes or is invalid\r\n              if (item.attributes || !item.id) return item;\r\n\r\n              // Create a copy of the item without the properties we'll move to attributes\r\n              const { id, ...rest } = item;\r\n\r\n              // Return a new object with the expected structure\r\n              return {\r\n                id,\r\n                attributes: { ...rest }\r\n              };\r\n            });\r\n          }\r\n        }\r\n\r\n        return response;\r\n      } catch (error) {\r\n        console.error(\"Error in getAll for specialties:\", error);\r\n        throw error;\r\n      }\r\n    },\r\n    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {\r\n      const defaultTags = ['strapi-specialties-slugs'];\r\n      return fetchAPI(`/specialties`, {\r\n        params: {\r\n          fields: ['slug'],\r\n          pagination: { pageSize: 1000 }, // Adjust if more items or implement pagination\r\n        },\r\n        cache: options.cache || 'force-cache',\r\n        next: {\r\n          ...(options.next || {}),\r\n          tags: [...defaultTags, ...(options.next?.tags || [])],\r\n          revalidate: options.next?.revalidate ?? 43200,\r\n        }\r\n      });\r\n    },\r\n    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {\r\n      const defaultCacheTags = ['strapi-specialties', `strapi-specialty-${slug}`];\r\n      try {\r\n        const response = await fetchAPI(`/specialties`, {\r\n          params: {\r\n            filters: { slug: { $eq: slug } },\r\n            populate: { seo: true, featuredImage: true, clinics: true, practitioners: true }\r\n          },\r\n          next: {\r\n            ...(options.next || {}),\r\n            tags: [...defaultCacheTags, ...(options.next?.tags || [])]\r\n          }\r\n        });\r\n\r\n        // If the data is an array and has items, check the first item\r\n        if (response?.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          const firstItem = response.data[0];\r\n\r\n          // If the item doesn't have attributes but has direct properties,\r\n          // transform it to match the expected structure\r\n          if (!firstItem.attributes && firstItem.id) {\r\n            // Create a new array with transformed items\r\n            response.data = response.data.map((item: any) => {\r\n              // Skip if already has attributes or is invalid\r\n              if (item.attributes || !item.id) return item;\r\n\r\n              // Create a copy of the item without the properties we'll move to attributes\r\n              const { id, ...rest } = item;\r\n\r\n              // Return a new object with the expected structure\r\n              return {\r\n                id,\r\n                attributes: { ...rest }\r\n              };\r\n            });\r\n          }\r\n        }\r\n\r\n        return response;\r\n      } catch (error) {\r\n        console.error(`Error in getBySlug for specialty ${slug}:`, error);\r\n        throw error;\r\n      }\r\n    },\r\n  },\r\n\r\n  // SEO helpers\r\n  seo: {\r\n    getMetadata: (entity: any) => {\r\n      if (!entity) return null;\r\n\r\n      // Extract SEO data from the entity's SEO field (from the SEO plugin)\r\n      const seo = entity.attributes?.seo?.data?.attributes || null;\r\n      const metaSocial = entity.attributes?.metaSocial || [];\r\n\r\n      return {\r\n        title: seo?.metaTitle || entity.attributes?.title || entity.attributes?.name,\r\n        description: seo?.metaDescription || entity.attributes?.description || entity.attributes?.excerpt,\r\n        openGraph: metaSocial?.find((item: any) => item.socialNetwork === 'Facebook') || null,\r\n        twitter: metaSocial?.find((item: any) => item.socialNetwork === 'Twitter') || null,\r\n        structuredData: seo?.structuredData || null,\r\n        canonicalURL: seo?.canonicalURL || null,\r\n        metaRobots: seo?.metaRobots || null,\r\n      };\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA,mOAAqB,YAAY;;AAkCjC,6CAA6C;AAC7C,MAAM;AAEN,2DAA2D;AAC3D,uCAAc;;AAMd;AAEA,6CAA6C;AAC7C,MAAM,eAAe,uCAAyC;AAE9D,iFAAiF;AACjF,MAAM,oBAAoB,WAAW;AAErC,oCAAoC;AACpC,QAAQ,GAAG,CAAC,yBAAyB,qBAAqB;AAE1D,2DAA2D;AAC3D,MAAM,mBAAmB,uCACvB,kBAAkB,OAAO,CAAC,iBAAiB;AAG7C,kCAAkC;AAClC,IAAI,kBAAkB;IACpB,QAAQ,GAAG,CAAC,qBAAqB;AACnC;AAiBO,MAAM,WAAW,OAAO,UAAkB,UAA2B,CAAC,CAAC;IAC5E,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAC1D,+CAA+C;IAC/C,6EAA6E;IAC7E,MAAM,UAAU,SAAS,UAAU,CAAC,OAAO,WAAW,CAAC,CAAC,EAAE,UAAU;IACpE,IAAI,cAAc,GAAG,kBAAkB,IAAI,EAAE,SAAS;IAEtD,IAAI;QACF,MAAM,YAAY,QAAQ,GAAG,CAAC,gBAAgB;QAC9C,MAAM,cAA2B;YAC/B,gBAAgB;YAChB,GAAI,aAAa;gBAAE,eAAe,CAAC,OAAO,EAAE,WAAW;YAAC,CAAC;QAC3D;QAEA,8CAA8C;QAC9C,6DAA6D;QAC7D,+BAA+B;QAE/B,yDAAyD;QACzD,IAAI,cAA4B,YAAY,oCAAoC;QAEhF,kDAAkD;QAClD,IAAI,QAAQ,KAAK,EAAE;YACjB,cAAc,QAAQ,KAAK;QAC7B,OAMK,IAAI,QAAQ,IAAI,EAAE,eAAe,WAAW;YAC/C,cAAc,QAAQ,IAAI,CAAC,UAAU,KAAK,IAAI,aAAa;QAC7D,OAAO;YACL,2DAA2D;YAC3D,oFAAoF;YACpF,yDAAyD;YACzD,cAAc;QAChB;QAEA,kDAAkD;QAClD,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,2BAA2B,EAAE,SAAS,CAAC,CAAC,EAAE;gBAClE,OAAO;gBACP,YAAY,QAAQ,IAAI,EAAE;gBAC1B,MAAM,QAAQ,IAAI,EAAE,QAAQ,EAAE;YAChC;QACF;QAEA,MAAM,eAA4B;YAChC,QAAQ,QAAQ,MAAM,IAAI;YAC1B,SAAS;gBACP,GAAG,WAAW;gBACd,GAAI,QAAQ,OAAO,IAAI,CAAC,CAAC;YAC3B;YACA,2DAA2D;YAC3D,OAAO;YACP,0EAA0E;YAC1E,MAAM;gBACJ,qDAAqD;gBACrD,MAAM,QAAQ,IAAI,EAAE,QAAQ,EAAE;gBAC9B,wCAAwC;gBACxC,GAAI,QAAQ,IAAI,EAAE,eAAe,aAAa;oBAAE,YAAY,QAAQ,IAAI,CAAC,UAAU;gBAAC,CAAC;YACvF;QACF;QAEA,oFAAoF;QACpF,IAAI,QAAQ,IAAI,EAAE,eAAe,SAAS,aAAa,IAAI,EAAE;YAC3D,aAAa,IAAI,CAAC,UAAU,GAAG;QACjC;QAEA,sDAAsD;QACtD,IAAI,CAAC,aAAa,MAAM,KAAK,SAAS,aAAa,MAAM,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;YACvF,MAAM,cAAc,kIAAA,CAAA,UAAE,CAAC,SAAS,CAAC,QAAQ,MAAM,EAAE;gBAAE,kBAAkB;YAAK;YAC1E,IAAI,aAAa;gBACf,cAAc,GAAG,YAAY,CAAC,EAAE,aAAa;YAC/C;QAEF,OAAO,IAAI,QAAQ,MAAM,IAAI,aAAa,MAAM,KAAK,SAAS,aAAa,MAAM,KAAK,QAAQ;YAC5F,oCAAoC;YACpC,aAAa,IAAI,GAAG,KAAK,SAAS,CAAC,QAAQ,MAAM;QACnD;QAEA,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,wCAAwC,EAAE,aAAa;YACjF,IAAI,aAAa,IAAI,EAAE,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,mBAAmB,CAAC,EAAE,aAAa,IAAI,CAAC,IAAI;YACnG,IAAI,aAAa,IAAI,EAAE,eAAe,WAAW,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,qBAAqB,CAAC,EAAE,aAAa,IAAI,CAAC,UAAU;YAC/H,IAAI,QAAQ,MAAM,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,uBAAuB,CAAC,EAAE,KAAK,SAAS,CAAC,QAAQ,MAAM,EAAE,MAAM;QAC/G;QAEA,MAAM,WAAW,MAAM,MAAM,aAAa;QAE1C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI;YACJ,IAAI;gBACF,YAAY,MAAM,SAAS,IAAI;YACjC,EAAE,OAAO,GAAG;gBACV,YAAY;oBAAE,SAAS,SAAS,UAAU;oBAAE,SAAS,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;gBAAI;YAC7F;YACA,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,aAAa,EAAE,YAAY,UAAU,EAAE,SAAS,MAAM,EAAE,EAAE;YACtF,MAAM,QAAQ,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC9E,aAAa;YACb,MAAM,QAAQ,GAAG;gBAAE,QAAQ,SAAS,MAAM;gBAAE,MAAM;YAAU;YAC5D,MAAM;QACR;QAEA,MAAM,eAAe,MAAM,SAAS,IAAI;QAExC,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,6BAA6B,EAAE,aAAa;YACtE,IAAI,cAAc;gBAChB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,UAAU,qBAAqB,CAAC,EAAE,KAAK,SAAS,CAAC;oBAC/D,SAAS,CAAC,CAAC,cAAc;oBACzB,UAAU,cAAc,OAAQ,MAAM,OAAO,CAAC,aAAa,IAAI,IAAI,UAAU,WAAY;oBACzF,YAAY,cAAc,QAAQ,MAAM,OAAO,CAAC,aAAa,IAAI,IAAI,aAAa,IAAI,CAAC,MAAM,GAAG;oBAChG,SAAS,CAAC,CAAC,cAAc;gBAC3B,GAAG,MAAM;YACX;QACF;QACA,OAAO;IAET,EAAE,OAAO,OAAY;QACnB,MAAM,eAAe,oDAAyB;QAC9C,uCAAkB;;QAKlB,OAAO;YACL,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,2BAA2B,EAAE,YAAY,EAAE,CAAC,EAAE,MAAM,OAAO,IAAI;YAC3F,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,kBAAkB,CAAC,EAAE,MAAM,QAAQ,CAAC,MAAM;gBACtE,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,gBAAgB,CAAC,EAAE,MAAM,QAAQ,CAAC,IAAI;YACpE,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,4CAA4C,EAAE,aAAa;YACzF,OAAO;gBACL,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,gBAAgB,CAAC,EAAE;YACjD;QACF;QACA,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,iBAAiB;YAC3D,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,yCAAyC,EAAE,YAAY,CAAC,CAAC;YACrF,IAAI,YAAY,QAAQ,CAAC,gBAAgB,YAAY,QAAQ,CAAC,cAAc;gBAC1E,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,kIAAkI,CAAC;YACjK;QACF;QACA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,yEAAyE,CAAC;QACxG;QACA,MAAM,SAAS,GAAG;QAClB,MAAM;IACR;AACF;AAgBO,MAAM,mBAAmB;IAC9B,yBAAyB;IACzB,SAAS;QACP,QAAQ,OAAO,SAAuB,CAAC,CAAC;YACtC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,YAAY,GAAG,QAAQ,oDAAoD;YAIzL,qDAAqD;YACrD,MAAM,cAA2B;gBAC/B,GAAG,UAAU;gBACb,UAAU,WAAW,QAAQ,IAAI;gBACjC,kBAAkB;gBAClB,YAAY;oBACV,MAAM;oBACN,UAAU;gBACZ;gBACA,SAAS;oBACP,GAAI,WAAW,OAAO,IAAI,CAAC,CAAC;gBAC9B;YACF;YAEA,4BAA4B;YAC5B,MAAM,UAAwB;gBAAE,GAAI,WAAW,OAAO,IAAI,CAAC,CAAC;YAAE;YAC9D,IAAI,kBAAyB,EAAE,EAAE,wBAAwB;YAEzD,oDAAoD;YACpD,IAAI,OAAO;gBACT,gBAAgB,IAAI,CAAC;oBACnB,KAAK;wBACH;4BAAE,MAAM;gCAAE,YAAY;4BAAM;wBAAE;wBAC9B;4BAAE,UAAU;gCAAE,MAAM;oCAAE,YAAY;gCAAM;4BAAE;wBAAE;wBAC5C;4BAAE,aAAa;gCAAE,MAAM;oCAAE,YAAY;gCAAM;4BAAE;wBAAE;wBAC/C;4BAAE,YAAY;gCAAE,MAAM;oCAAE,YAAY;gCAAM;4BAAE;wBAAE;wBAC9C;4BAAE,YAAY;gCAAE,MAAM;oCAAE,YAAY;gCAAM;4BAAE;wBAAE;wBAC9C;4BAAE,aAAa;gCAAE,YAAY;4BAAM;wBAAE,EAAE,kCAAkC;qBAC1E;gBACH;YACF;YAEA,uEAAuE;YACvE,IAAI,UAAU;gBACZ,gBAAgB,IAAI,CAAC;oBACnB,SAAS;wBACP,KAAK;4BACH;gCAAE,gBAAgB;oCAAE,YAAY;gCAAS;4BAAE;4BAC3C,uEAAuE;4BACvE;gCAAE,MAAM;oCAAE,YAAY;gCAAS;4BAAE;4BACjC;gCAAE,eAAe;oCAAE,YAAY;gCAAS;4BAAE;4BAC1C;gCAAE,YAAY;oCAAE,YAAY;gCAAS;4BAAE;yBACxC;oBACH;gBACF;YACF;YAEA,mBAAmB;YACnB,IAAI,eAAe;gBAChB,gBAAgB,IAAI,CAAC;oBAAE,aAAa;wBAAE,MAAM;4BAAE,KAAK;wBAAc;oBAAE;gBAAE;YACxE;YAEA,mBAAmB;YACnB,IAAI,eAAe;gBAChB,gBAAgB,IAAI,CAAC;oBAAE,YAAY;wBAAE,MAAM;4BAAE,KAAK;wBAAc;oBAAE;gBAAE;YACvE;YAEA,kBAAkB;YAClB,IAAI,cAAc;gBACf,gBAAgB,IAAI,CAAC;oBAAE,YAAY;wBAAE,MAAM;4BAAE,KAAK;wBAAa;oBAAE;gBAAE;YACtE;YAEA,2DAA2D;YAC3D,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,QAAQ,IAAI,GAAG;YACjB,OAAO,IAAI,gBAAgB,MAAM,KAAK,GAAG;gBACvC,OAAO,MAAM,CAAC,SAAS,eAAe,CAAC,EAAE;YAC3C;YAEA,qDAAqD;YACrD,YAAY,OAAO,GAAG;YAEtB,8CAA8C;YAC9C,IAAI,eAAe;gBACjB,kDAAkD;gBAClD,MAAM,YAAY;oBAChB;oBACA;oBACA,CAAC,iBAAiB,EAAE,eAAe;oBACnC,CAAC,iBAAiB,EAAE,cAAc,QAAQ,CAAC;iBAC5C;gBAED,gCAAgC;gBAChC,UAAU,IAAI,CAAC,CAAC,iBAAiB,EAAE,cAAc,MAAM,EAAE,MAAM;gBAE/D,sCAAsC;gBACtC,IAAI,OAAO;oBACT,UAAU,IAAI,CAAC,CAAC,iBAAiB,EAAE,cAAc,OAAO,EAAE,OAAO;gBACnE;gBAEA,yCAAyC;gBACzC,IAAI,UAAU;oBACZ,UAAU,IAAI,CAAC,CAAC,iBAAiB,EAAE,cAAc,UAAU,EAAE,UAAU;gBACzE;gBAEA,gCAAgC;gBAChC,MAAM,0BAA4C;oBAAE,MAAM;gBAAU;gBACpE,IAAI,MAAM,eAAe,WAAW;oBAClC,wBAAwB,UAAU,GAAG,KAAK,UAAU;gBACtD;gBACA,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAE;oBAC1B,QAAQ;oBACR,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,6CAA6C;YAC7C,IAAI,cAAc;gBAChB,iDAAiD;gBACjD,MAAM,YAAY;oBAChB;oBACA;oBACA,CAAC,gBAAgB,EAAE,cAAc;oBACjC,CAAC,gBAAgB,EAAE,aAAa,QAAQ,CAAC;iBAC1C;gBAED,gCAAgC;gBAChC,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa,MAAM,EAAE,MAAM;gBAE7D,sCAAsC;gBACtC,IAAI,OAAO;oBACT,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa,OAAO,EAAE,OAAO;gBACjE;gBAEA,yCAAyC;gBACzC,IAAI,UAAU;oBACZ,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa,UAAU,EAAE,UAAU;gBACvE;gBAEA,gCAAgC;gBAChC,MAAM,yBAA2C;oBAAE,MAAM;gBAAU;gBACnE,IAAI,MAAM,eAAe,WAAW;oBAClC,uBAAuB,UAAU,GAAG,KAAK,UAAU;gBACrD;gBACA,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAE;oBAC1B,QAAQ;oBACR,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,4CAA4C;YAC5C,MAAM,mBAAmB;gBAAC;aAAsB;YAEhD,gCAAgC;YAChC,MAAM,aAAa,YAAY,UAAU,EAAE,QAAQ;YACnD,iBAAiB,IAAI,CAAC,CAAC,oBAAoB,EAAE,YAAY;YAEzD,sCAAsC;YACtC,IAAI,OAAO;gBACT,iBAAiB,IAAI,CAAC,CAAC,qBAAqB,EAAE,OAAO;YACvD;YAEA,yCAAyC;YACzC,IAAI,UAAU;gBACZ,iBAAiB,IAAI,CAAC,CAAC,wBAAwB,EAAE,UAAU;YAC7D;YAEA,uDAAuD;YACvD,MAAM,gBAAgB;mBAAK,MAAM,QAAQ,EAAE;mBAAM;aAAiB;YAElE,MAAM,qBAAuC;gBAAE,MAAM;YAAc;YACnE,IAAI,MAAM,eAAe,WAAW;gBAClC,mBAAmB,UAAU,GAAG,KAAK,UAAU;YACjD;YACA,gCAAgC;YAChC,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAE;gBAC1B,QAAQ;gBACR,OAAO;gBACP,MAAM;YACR;QACF;QACA,aAAa,OAAO,UAA2B,CAAC,CAAC;YAC/C,gCAAgC;YAChC,MAAM,cAAc;gBAAC;aAAuB;YAE5C,8CAA8C;YAC9C,MAAM,eAAe;mBAAI;mBAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;aAAE;YAEpE,yDAAyD;YACzD,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAE;gBAC1B,QAAQ;oBACN,QAAQ;wBAAC;qBAAO;oBAChB,YAAY;wBACV,UAAU;oBACZ;gBACF;gBACA,6CAA6C;gBAC7C,OAAO,QAAQ,KAAK,IAAI;gBACxB,MAAM;oBACJ,oBAAoB;oBACpB,MAAM;oBACN,oDAAoD;oBACpD,YAAY,QAAQ,IAAI,EAAE,cAAc;oBACxC,4BAA4B;oBAC5B,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;gBACxB;YACF;QACF;QACA,WAAW,OAAO,MAAc,UAA2B,CAAC,CAAC;YAC3D,MAAM,cAAc;gBAAC;gBAAkB,CAAC,cAAc,EAAE,MAAM;aAAC;YAE/D,8CAA8C;YAC9C,MAAM,eAAe;mBAAI;mBAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;aAAE;YAEpE,yDAAyD;YACzD,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAE;gBAC1B,QAAQ;oBACN,SAAS;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;oBAC/B,UAAU;wBACR,MAAM;wBACN,SAAS;wBACT,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,UAAU;wBACV,aAAa;wBACb,YAAY;wBACZ,eAAe;4BAAE,UAAU;gCAAE,gBAAgB;4BAAK;wBAAE;wBACpD,qBAAqB;wBACrB,iBAAiB;wBACjB,KAAK;oBACP;gBACF;gBACA,6CAA6C;gBAC7C,OAAO,QAAQ,KAAK,IAAI;gBACxB,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,oBAAoB;oBACpB,MAAM;oBACN,oDAAoD;oBACpD,YAAY,QAAQ,IAAI,EAAE,cAAc;gBAC1C;YACF;QACF;QACA,aAAa,OAAO,UAA2B,CAAC,CAAC,GAAK,SAAS,CAAC,QAAQ,CAAC,EAAE;gBACzE,QAAQ;oBACN,SAAS;wBAAE,YAAY;4BAAE,KAAK;wBAAK;oBAAE;oBACrC,UAAU;gBACZ;gBACA,GAAG,OAAO;YACZ;QACA,6EAA6E;QAC7E,mBAAmB,OAAO,EACxB,IAAI,EACJ,QAAQ,EAAE,EACV,WAAW,EAAE,EACb,OAAO,CAAC,EACR,WAAW,EAAE,EAOd;YACC,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,KAAK,QAAQ,EAAE,KAAK,SAAS,EAAE,MAAM,YAAY,EAAE,UAAU;gBAEhH,2DAA2D;gBAC3D,MAAM,UAAwB;oBAC5B,YAAY;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;gBACpC;gBAEA,mDAAmD;gBACnD,MAAM,kBAAyB,EAAE;gBAEjC,qCAAqC;gBACrC,gBAAgB,IAAI,CAAC;oBAAE,YAAY;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;gBAAE;gBAE3D,+BAA+B;gBAC/B,IAAI,OAAO;oBACT,gBAAgB,IAAI,CAAC;wBACnB,KAAK;4BACH;gCAAE,MAAM;oCAAE,YAAY;gCAAM;4BAAE;4BAC9B;gCAAE,UAAU;oCAAE,MAAM;wCAAE,YAAY;oCAAM;gCAAE;4BAAE;4BAC5C;gCAAE,aAAa;oCAAE,MAAM;wCAAE,YAAY;oCAAM;gCAAE;4BAAE;4BAC/C;gCAAE,YAAY;oCAAE,MAAM;wCAAE,YAAY;oCAAM;gCAAE;4BAAE;yBAC/C;oBACH;gBACF;gBAEA,kCAAkC;gBAClC,IAAI,UAAU;oBACZ,gBAAgB,IAAI,CAAC;wBACnB,SAAS;4BACP,KAAK;gCACH;oCAAE,gBAAgB;wCAAE,YAAY;oCAAS;gCAAE;gCAC3C;oCAAE,MAAM;wCAAE,YAAY;oCAAS;gCAAE;gCACjC;oCAAE,eAAe;wCAAE,YAAY;oCAAS;gCAAE;gCAC1C;oCAAE,YAAY;wCAAE,YAAY;oCAAS;gCAAE;6BACxC;wBACH;oBACF;gBACF;gBAEA,4CAA4C;gBAC5C,MAAM,eAA6B,gBAAgB,MAAM,GAAG,IACxD;oBAAE,MAAM;gBAAgB,IACxB;gBAEJ,6BAA6B;gBAC7B,MAAM,cAA2B;oBAC/B,SAAS;oBACT,YAAY;wBACV;wBACA;oBACF;oBACA,UAAU;wBACR,MAAM;wBACN,eAAe;wBACf,SAAS;wBACT,aAAa;wBACb,YAAY;oBACd;oBACA,kBAAkB;gBACpB;gBAEA,qEAAqE;gBACrE,MAAM,YAAY;oBAChB;oBACA;oBACA,CAAC,gBAAgB,EAAE,MAAM;oBACzB,CAAC,gBAAgB,EAAE,KAAK,QAAQ,CAAC;oBACjC,CAAC,gBAAgB,EAAE,KAAK,MAAM,EAAE,MAAM;iBACvC;gBAED,8CAA8C;gBAC9C,IAAI,OAAO;oBACT,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,OAAO,EAAE,OAAO;gBACzD;gBAEA,iDAAiD;gBACjD,IAAI,UAAU;oBACZ,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,UAAU,EAAE,UAAU;gBAC/D;gBAIA,+BAA+B;gBAC/B,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAE;oBAC1B,QAAQ;oBACR,MAAM;wBACJ,MAAM;oBACR;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC,EAAE;gBAC3E,MAAM;YACR;QACF;IACF;IAEA,+BAA+B;IAC/B,eAAe;QACb,QAAQ,OAAO,SAAuB,CAAC,CAAC;YACtC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,YAAY,GAAG,QAAQ,oDAAoD;YAIzL,qDAAqD;YACrD,MAAM,cAA2B;gBAC/B,GAAG,UAAU;gBACb,UAAU,WAAW,QAAQ,IAAI;gBACjC,kBAAkB;gBAClB,YAAY;oBACV,MAAM;oBACN,UAAU;gBACZ;gBACA,SAAS;oBACP,GAAI,WAAW,OAAO,IAAI,CAAC,CAAC;gBAC9B;YACF;YAEA,4BAA4B;YAC5B,MAAM,UAAwB;gBAAE,GAAI,WAAW,OAAO,IAAI,CAAC,CAAC;YAAE;YAC9D,IAAI,kBAAyB,EAAE,EAAE,wBAAwB;YAEzD,4CAA4C;YAC5C,IAAI,OAAO;gBACT,gBAAgB,IAAI,CAAC;oBACnB,KAAK;wBACH;4BAAE,MAAM;gCAAE,YAAY;4BAAM;wBAAE;wBAC9B;4BAAE,aAAa;gCAAE,MAAM;oCAAE,YAAY;gCAAM;4BAAE;wBAAE;wBAC/C;4BAAE,YAAY;gCAAE,MAAM;oCAAE,YAAY;gCAAM;4BAAE;wBAAE;qBAC/C;gBACH;YACF;YAEA,6FAA6F;YAC7F,oFAAoF;YACpF,2DAA2D;YAC3D,kBAAkB;YAClB,4BAA4B;YAC5B,yCAAyC;YACzC,0FAA0F;YAC1F,wDAAwD;YACxD,kFAAkF;YAClF,8CAA8C;YAC9C,uDAAuD;YACvD,oDAAoD;YACpD,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,IAAI;YAEJ,mBAAmB;YACnB,IAAI,eAAe;gBAChB,gBAAgB,IAAI,CAAC;oBAAE,aAAa;wBAAE,MAAM;4BAAE,KAAK;wBAAc;oBAAE;gBAAE;YACxE;YAEA,mBAAmB;YACnB,IAAI,eAAe;gBAChB,gBAAgB,IAAI,CAAC;oBAAE,YAAY;wBAAE,MAAM;4BAAE,KAAK;wBAAc;oBAAE;gBAAE;YACvE;YAEA,kBAAkB;YAClB,IAAI,cAAc;gBACf,gBAAgB,IAAI,CAAC;oBAAE,YAAY;wBAAE,MAAM;4BAAE,KAAK;wBAAa;oBAAE;gBAAE;YACtE;YAEA,2DAA2D;YAC3D,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,QAAQ,IAAI,GAAG;YACjB,OAAO,IAAI,gBAAgB,MAAM,KAAK,GAAG;gBACvC,OAAO,MAAM,CAAC,SAAS,eAAe,CAAC,EAAE;YAC3C;YAEA,qDAAqD;YACrD,YAAY,OAAO,GAAG;YAEtB,8CAA8C;YAC9C,IAAI,eAAe;gBACjB,kDAAkD;gBAClD,MAAM,YAAY;oBAChB;oBACA;oBACA,CAAC,iBAAiB,EAAE,eAAe;oBACnC,CAAC,iBAAiB,EAAE,cAAc,cAAc,CAAC;iBAClD;gBAED,gCAAgC;gBAChC,UAAU,IAAI,CAAC,CAAC,iBAAiB,EAAE,cAAc,MAAM,EAAE,MAAM;gBAE/D,sCAAsC;gBACtC,IAAI,OAAO;oBACT,UAAU,IAAI,CAAC,CAAC,iBAAiB,EAAE,cAAc,OAAO,EAAE,OAAO;gBACnE;gBAEA,yCAAyC;gBACzC,IAAI,UAAU;oBACZ,UAAU,IAAI,CAAC,CAAC,iBAAiB,EAAE,cAAc,UAAU,EAAE,UAAU;gBACzE;gBAEA,sCAAsC;gBACtC,MAAM,0BAA4C;oBAAE,MAAM;gBAAU;gBACpE,IAAI,MAAM,eAAe,WAAW;oBAClC,wBAAwB,UAAU,GAAG,KAAK,UAAU;gBACtD;gBACA,OAAO,SAAS,CAAC,cAAc,CAAC,EAAE;oBAChC,QAAQ;oBACR,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,6CAA6C;YAC7C,IAAI,cAAc;gBAChB,iDAAiD;gBACjD,MAAM,YAAY;oBAChB;oBACA;oBACA,CAAC,gBAAgB,EAAE,cAAc;oBACjC,CAAC,gBAAgB,EAAE,aAAa,cAAc,CAAC;iBAChD;gBAED,gCAAgC;gBAChC,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa,MAAM,EAAE,MAAM;gBAE7D,sCAAsC;gBACtC,IAAI,OAAO;oBACT,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa,OAAO,EAAE,OAAO;gBACjE;gBAEA,yCAAyC;gBACzC,IAAI,UAAU;oBACZ,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa,UAAU,EAAE,UAAU;gBACvE;gBAEA,sCAAsC;gBACtC,MAAM,yBAA2C;oBAAE,MAAM;gBAAU;gBACnE,IAAI,MAAM,eAAe,WAAW;oBAClC,uBAAuB,UAAU,GAAG,KAAK,UAAU;gBACrD;gBACA,OAAO,SAAS,CAAC,cAAc,CAAC,EAAE;oBAChC,QAAQ;oBACR,OAAO;oBACP,MAAM;gBACR;YACF;YAEA,kDAAkD;YAClD,MAAM,mBAAmB;gBAAC;aAA4B;YAEtD,gCAAgC;YAChC,MAAM,aAAa,YAAY,UAAU,EAAE,QAAQ;YACnD,iBAAiB,IAAI,CAAC,CAAC,0BAA0B,EAAE,YAAY;YAE/D,sCAAsC;YACtC,IAAI,OAAO;gBACT,iBAAiB,IAAI,CAAC,CAAC,2BAA2B,EAAE,OAAO;YAC7D;YAEA,yCAAyC;YACzC,IAAI,UAAU;gBACZ,iBAAiB,IAAI,CAAC,CAAC,8BAA8B,EAAE,UAAU;YACnE;YAEA,uDAAuD;YACvD,MAAM,gBAAgB;mBAAK,MAAM,QAAQ,EAAE;mBAAM;aAAiB;YAElE,MAAM,qBAAuC;gBAAE,MAAM;YAAc;YACnE,IAAI,MAAM,eAAe,WAAW;gBAClC,mBAAmB,UAAU,GAAG,KAAK,UAAU;YACjD;YACA,sCAAsC;YACtC,OAAO,SAAS,CAAC,cAAc,CAAC,EAAE;gBAChC,QAAQ;gBACR,OAAO;gBACP,MAAM;YACR;QACF;QACA,aAAa,OAAO,UAA2B,CAAC,CAAC;YAC/C,MAAM,WAA+B,EAAE;YACvC,IAAI,OAAO;YACX,0EAA0E;YAC1E,kFAAkF;YAClF,MAAM,oBAAoB;YAC1B,IAAI,mBAAmB;YAEvB,MAAM,cAAc;gBAAC;aAA6B;YAClD,iGAAiG;YACjG,MAAM,mBAAmB;gBACvB,OAAO,QAAQ,KAAK,IAAI;gBACxB,MAAM;oBACJ,YAAY,QAAQ,IAAI,EAAE,cAAc;oBACxC,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;oBACrD,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;gBACxB;YACF;YACA,2DAA2D;YAC3D,IAAI,iBAAiB,IAAI,CAAC,IAAI,EAAE;gBAC9B,iBAAiB,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,CAAC,IAAI;YAC5E;YAGA,MAAO,iBAAkB;gBACvB,IAAI;oBACF,MAAM,WAAW,MAAM,SAAS,CAAC,cAAc,CAAC,EAAE;wBAChD,QAAQ;4BACN,QAAQ;gCAAC;6BAAO;4BAChB,YAAY;gCACV,MAAM;gCACN,UAAU;4BACZ;wBACF;wBACA,2CAA2C;wBAC3C,OAAO,iBAAiB,KAAK;wBAC7B,MAAM,iBAAiB,IAAI;oBAC7B;oBAEA,IAAI,UAAU,QAAQ,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;wBAClD,SAAS,IAAI,CAAC,OAAO,CAAC,CAAC;4BACrB,2EAA2E;4BAC3E,mDAAmD;4BACnD,IAAI,QAAQ,KAAK,IAAI,EAAE;gCACrB,SAAS,IAAI,CAAC;oCAAE,MAAM,KAAK,IAAI;gCAAC;4BAClC;wBACF;wBAEA,6CAA6C;wBAC7C,MAAM,iBAAiB,SAAS,IAAI,EAAE;wBACtC,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,kBAAkB,QAAQ,eAAe,SAAS,EAAE;4BACnG,mBAAmB;wBACrB,OAAO;4BACL;wBACF;oBACF,OAAO;wBACL,+BAA+B;wBAC/B,mBAAmB;oBACrB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,uBAAuB,CAAC,EAAE;oBACpE,mBAAmB,OAAO,gBAAgB;gBAC5C;YACF;YACA,OAAO;gBAAE,MAAM;YAAS;QAC1B;QACA,WAAW,OAAO,MAAc,UAA2B,CAAC,CAAC;YAC3D,MAAM,cAAc;gBAAC;gBAAuB,CAAC,oBAAoB,EAAE,MAAM;aAAC,EAAE,0DAA0D;YACtI,OAAO,SAAS,CAAC,cAAc,CAAC,EAAE;gBAChC,QAAQ;oBACN,SAAS;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;oBAC/B,UAAU;wBACR,gBAAgB;wBAChB,aAAa;wBACb,aAAa;wBACb,YAAY;wBACZ,SAAS;wBACT,KAAK;oBACP;gBACF;gBACA,yDAAyD;gBACzD,OAAO,QAAQ,KAAK,IAAI;gBACxB,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;oBACrD,oDAAoD;oBACpD,YAAY,QAAQ,IAAI,EAAE,cAAc;gBAC1C;YACF;QACF;QACA,aAAa,OAAO,UAA2B,CAAC,CAAC,GAAK,SAAS,CAAC,cAAc,CAAC,EAAE;gBAC/E,QAAQ;oBACN,SAAS;wBAAE,YAAY;4BAAE,KAAK;wBAAK;oBAAE;oBACrC,UAAU;gBACZ;gBACA,GAAG,OAAO;YACZ;QACA,mFAAmF;QACnF,mBAAmB,OAAO,EACxB,IAAI,EACJ,QAAQ,EAAE,EACV,WAAW,EAAE,EACb,OAAO,CAAC,EACR,WAAW,EAAE,EAOd;YACC,IAAI;gBAEF,2DAA2D;gBAC3D,MAAM,UAAwB;oBAC5B,YAAY;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;gBACpC;gBAEA,mDAAmD;gBACnD,MAAM,kBAAyB,EAAE;gBAEjC,qCAAqC;gBACrC,gBAAgB,IAAI,CAAC;oBAAE,YAAY;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;gBAAE;gBAE3D,+BAA+B;gBAC/B,IAAI,OAAO;oBACT,gBAAgB,IAAI,CAAC;wBACnB,KAAK;4BACH;gCAAE,MAAM;oCAAE,YAAY;gCAAM;4BAAE;4BAC9B;gCAAE,OAAO;oCAAE,YAAY;gCAAM;4BAAE;4BAC/B;gCAAE,gBAAgB;oCAAE,YAAY;gCAAM;4BAAE;4BACxC;gCAAE,aAAa;oCAAE,MAAM;wCAAE,YAAY;oCAAM;gCAAE;4BAAE;4BAC/C;gCAAE,YAAY;oCAAE,MAAM;wCAAE,YAAY;oCAAM;gCAAE;4BAAE;yBAC/C;oBACH;gBACF;gBAEA,kCAAkC;gBAClC,IAAI,UAAU;oBACZ,gBAAgB,IAAI,CAAC;wBACnB,SAAS;4BACP,KAAK;gCACH;oCAAE,gBAAgB;wCAAE,YAAY;oCAAS;gCAAE;gCAC3C;oCAAE,MAAM;wCAAE,YAAY;oCAAS;gCAAE;gCACjC;oCAAE,eAAe;wCAAE,YAAY;oCAAS;gCAAE;gCAC1C;oCAAE,YAAY;wCAAE,YAAY;oCAAS;gCAAE;6BACxC;wBACH;oBACF;gBACF;gBAEA,4CAA4C;gBAC5C,MAAM,eAA6B,gBAAgB,MAAM,GAAG,IACxD;oBAAE,MAAM;gBAAgB,IACxB;gBAEJ,6BAA6B;gBAC7B,MAAM,cAA2B;oBAC/B,SAAS;oBACT,YAAY;wBACV;wBACA;oBACF;oBACA,UAAU;wBACR,gBAAgB;wBAChB,aAAa;wBACb,aAAa;wBACb,YAAY;wBACZ,YAAY;oBACd;oBACA,kBAAkB;gBACpB;gBAEA,qEAAqE;gBACrE,MAAM,YAAY;oBAChB;oBACA;oBACA,CAAC,gBAAgB,EAAE,MAAM;oBACzB,CAAC,gBAAgB,EAAE,KAAK,cAAc,CAAC;oBACvC,CAAC,gBAAgB,EAAE,KAAK,MAAM,EAAE,MAAM;iBACvC;gBAED,8CAA8C;gBAC9C,IAAI,OAAO;oBACT,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,OAAO,EAAE,OAAO;gBACzD;gBAEA,iDAAiD;gBACjD,IAAI,UAAU;oBACZ,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,UAAU,EAAE,UAAU;gBAC/D;gBAIA,+BAA+B;gBAC/B,OAAO,SAAS,CAAC,cAAc,CAAC,EAAE;oBAChC,QAAQ;oBACR,MAAM;wBACJ,MAAM;oBACR;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC,EAAE;gBACjF,MAAM;YACR;QACF;IACF;IAEA,2BAA2B;IAC3B,YAAY;QACV,QAAQ,OAAO,SAAuB,CAAC,CAAC;YACrC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,YAAY,GAAG,QAAQ,iCAAiC;YAE3H,qDAAqD;YACrD,MAAM,cAA2B;gBAC/B,GAAG,UAAU;gBACb,UAAU,WAAW,QAAQ,IAAI;oBAC/B,8DAA8D;oBAC9D,MAAM;oBACN,eAAe;gBAEjB;gBACA,kBAAkB;gBAClB,YAAY;oBACV,MAAM;oBACN,UAAU;gBACZ;gBACA,SAAS;oBACP,GAAI,WAAW,OAAO,IAAI,CAAC,CAAC;gBAC9B;YACF;YAEA,uCAAuC;YACvC,IAAI,SAAS,YAAY,OAAO,EAAE;gBAChC,YAAY,OAAO,CAAC,IAAI,GAAG;oBAAE,YAAY;gBAAM;YACjD;YAEA,qEAAqE;YACrE,MAAM,mBAAmB;gBAAC;gBAAqB;aAA0B;YAEzE,gCAAgC;YAChC,IAAI,YAAY,UAAU,EAAE,MAAM,iBAAiB,IAAI,CAAC,CAAC,uBAAuB,EAAE,YAAY,UAAU,CAAC,IAAI,EAAE;YAE/G,sCAAsC;YACtC,IAAI,OAAO,iBAAiB,IAAI,CAAC,CAAC,wBAAwB,EAAE,OAAO;YAEnE,uDAAuD;YACvD,MAAM,gBAAgB;mBAAK,MAAM,QAAQ,EAAE;mBAAM;aAAiB;YAElE,MAAM,cAAgC;gBAAE,MAAM;YAAc;YAC5D,IAAI,MAAM,eAAe,WAAW;gBAClC,YAAY,UAAU,GAAG,KAAK,UAAU;YAC1C;YAEA,OAAO,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC7B,QAAQ;gBACR,OAAO;gBACP,MAAM;YACR;QACF;QACA,WAAW,OAAO,MAAc,UAA2B,CAAC,CAAC;YAC3D,MAAM,mBAAmB;gBACvB;gBACA,CAAC,gBAAgB,EAAE,MAAM;gBACzB,CAAC,gBAAgB,EAAE,KAAK,QAAQ,CAAC;gBACjC,CAAC,gBAAgB,EAAE,KAAK,cAAc,CAAC;aACxC;YACD,OAAO,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC7B,QAAQ;oBACN,SAAS;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;oBAC/B,UAAU;wBACR,KAAK;4BAAE,UAAU;gCAAE,WAAW;oCAAE,UAAU;wCAAE,SAAS;oCAAK;gCAAE;gCAAG,WAAW;4BAAK;wBAAE;wBACjF,MAAM;wBACN,eAAe;wBACf,SAAS;4BAAE,UAAU;gCAAE,MAAM;gCAAM,eAAe;gCAAM,SAAS;gCAAM,aAAa;4BAAK;wBAAE;wBAC3F,eAAe;4BAAE,UAAU;gCAAE,gBAAgB;gCAAM,aAAa;4BAAK;wBAAE;oBACzE;oBACA,kBAAkB;gBACpB;gBACA,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAsB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;gBAC5D;YACF;QACF;QACA,aAAa,OAAO,UAA2B,CAAC,CAAC;YAC/C,MAAM,cAAc;gBAAC;aAA0B;YAC/C,OAAO,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC7B,QAAQ;oBACN,QAAQ;wBAAC;qBAAO;oBAChB,YAAY;wBAAE,UAAU;oBAAK;gBAC/B;gBACA,OAAO,QAAQ,KAAK,IAAI;gBACxB,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;oBACrD,YAAY,QAAQ,IAAI,EAAE,cAAc;gBAC1C;YACF;QACF;QACA,yDAAyD;QACzD,qBAAqB,OAAO,UAA2B,CAAC,CAAC;YACvD,MAAM,cAAc;gBAAC;gBAAqB;aAA2B;YACrE,OAAO,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC7B,QAAQ;oBACN,SAAS;wBAAE,cAAc;4BAAE,KAAK;wBAAK;oBAAE;oBACvC,UAAU;oBACV,kBAAkB;gBACpB;gBACA,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;gBACvD;YACF;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM;QACJ,UAAU,OAAO,SAAsB,CAAC,CAAC;YACvC,IAAI;gBACF,yEAAyE;gBACzE,MAAM,cAAmC;oBACvC,kBAAkB;gBACpB;gBAEA,gCAAgC;gBAChC,IAAI,OAAO;gBACX,IAAI,WAAW;gBAEf,2DAA2D;gBAC3D,IAAI,OAAO,UAAU,EAAE;oBACrB,YAAY,UAAU,GAAG,OAAO,UAAU;oBAC1C,OAAO,OAAO,UAAU,CAAC,IAAI,IAAI;oBACjC,WAAW,OAAO,UAAU,CAAC,QAAQ,IAAI;gBAC3C,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,QAAQ,EAAE;oBACzC,2CAA2C;oBAC3C,OAAO,OAAO,IAAI,IAAI;oBACtB,WAAW,OAAO,QAAQ,IAAI;oBAC9B,YAAY,UAAU,GAAG;wBACvB;wBACA;oBACF;gBACF;gBAEA,wBAAwB;gBACxB,IAAI,OAAO,IAAI,EAAE;oBACf,YAAY,IAAI,GAAG,OAAO,IAAI;gBAChC,OAAO;oBACL,2DAA2D;oBAC3D,YAAY,IAAI,GAAG;wBAAC;qBAAmB;gBACzC;gBAEA,iBAAiB;gBACjB,IAAI,OAAO,OAAO,EAAE;oBAClB,YAAY,OAAO,GAAG,OAAO,OAAO;gBACtC,OAAO,IAAI,OAAO,YAAY,EAAE;oBAC9B,4CAA4C;oBAC5C,YAAY,OAAO,GAAG;wBACpB,GAAI,YAAY,OAAO,IAAI,CAAC,CAAC;wBAC7B,iBAAiB;4BAAE,MAAM;gCAAE,KAAK,OAAO,YAAY;4BAAC;wBAAE;oBACxD;gBACF,OAAO,IAAI,OAAO,OAAO,EAAE;oBACzB,uCAAuC;oBACvC,YAAY,OAAO,GAAG;wBACpB,GAAI,YAAY,OAAO,IAAI,CAAC,CAAC;wBAC7B,WAAW;4BAAE,MAAM;gCAAE,KAAK,OAAO,OAAO;4BAAC;wBAAE;oBAC7C;gBACF,OAAO,IAAI,OAAO,KAAK,EAAE;oBACvB,qCAAqC;oBACrC,YAAY,OAAO,GAAG;wBACpB,GAAI,YAAY,OAAO,IAAI,CAAC,CAAC;wBAC7B,KAAK;4BACH;gCAAE,OAAO;oCAAE,YAAY,OAAO,KAAK;gCAAC;4BAAE;4BACtC;gCAAE,SAAS;oCAAE,YAAY,OAAO,KAAK;gCAAC;4BAAE;yBACzC;oBACH;gBACF;gBAEA,0EAA0E;gBAC1E,IAAI,OAAO,QAAQ,EAAE;oBACnB,YAAY,QAAQ,GAAG,OAAO,QAAQ;gBACxC,OAAO;oBACL,YAAY,QAAQ,GAAG;wBACrB,eAAe;wBACf,cAAc;4BACZ,UAAU;gCACR,gBAAgB;4BAClB;wBACF;wBACA,iBAAiB;wBACjB,WAAW;oBACb;gBACF;gBAEA,2CAA2C;gBAE3C,qEAAqE;gBACrE,MAAM,YAAY;oBAAC;iBAAoB;gBAEvC,2DAA2D;gBAC3D,IAAI,OAAO,YAAY,EAAE;oBACvB,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,OAAO,YAAY,EAAE;oBACvD,UAAU,IAAI,CAAC,CAAC,gBAAgB,EAAE,OAAO,YAAY,CAAC,MAAM,EAAE,MAAM;gBACtE;gBAEA,iDAAiD;gBACjD,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,IAAI,CAAC,CAAC,WAAW,EAAE,OAAO,OAAO,EAAE;oBAC7C,UAAU,IAAI,CAAC,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,MAAM,EAAE,MAAM;gBAC5D;gBAEA,qDAAqD;gBACrD,IAAI,OAAO,KAAK,EAAE;oBAChB,UAAU,IAAI,CAAC,CAAC,kBAAkB,EAAE,OAAO,KAAK,EAAE;gBACpD;gBAEA,wEAAwE;gBACxE,MAAM,WAAW,MAAM,SAAS,CAAC,WAAW,CAAC,EAAE;oBAC7C,QAAQ;oBACR,MAAM;wBACJ,MAAM;oBACR;gBACF;gBAEA,2CAA2C;gBAC3C,wCAA4C;oBAC1C,QAAQ,GAAG,CAAC,qCAAqC,KAAK,SAAS,CAAC;wBAC9D,SAAS,CAAC,CAAC,UAAU;wBACrB,aAAa,MAAM,OAAO,CAAC,UAAU;wBACrC,YAAY,MAAM,OAAO,CAAC,UAAU,QAAQ,SAAS,IAAI,CAAC,MAAM,GAAG;wBACnE,SAAS,CAAC,CAAC,UAAU;wBACrB,eAAe,CAAC,CAAC,UAAU,MAAM;wBACjC,YAAY,UAAU,MAAM,YAAY,SAAS;oBACnD;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,kCAAkC;gBAClC,IAAI,MAAM,QAAQ,EAAE;oBAClB,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;oBACvD,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;oBACnD,QAAQ,KAAK,CAAC,qBAAqB,MAAM,QAAQ,CAAC,OAAO;gBAC3D;gBACA,MAAM;YACR;QACF;QACA,aAAa,OAAO,UAA2B,CAAC,CAAC;YAC/C,MAAM,cAAc;gBAAC;aAA0B;YAC/C,OAAO,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC7B,QAAQ;oBACN,QAAQ;wBAAC;qBAAO;oBAChB,YAAY;wBAAE,UAAU;oBAAK;gBAC/B;gBACA,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;gBACvD;YACF;QACF;QACA,eAAe,OAAO,MAAc,UAA2B,CAAC,CAAC;YAC/D,MAAM,mBAAmB;gBAAC;gBAAqB,CAAC,iBAAiB,EAAE,MAAM;aAAC;YAC1E,OAAO,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC7B,QAAQ;oBACN,SAAS;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;oBAC/B,UAAU;wBACR,KAAK;4BAAE,UAAU;gCAAE,WAAW;gCAAM,WAAW;4BAAK;wBAAE;wBACtD,eAAe;wBACf,cAAc;4BAAE,QAAQ;gCAAC;gCAAM;gCAAQ;gCAAQ;6BAAM;4BAAE,UAAU;gCAAE,gBAAgB;4BAAK;wBAAE;wBAC1F,iBAAiB;4BAAE,QAAQ;gCAAC;gCAAM;gCAAQ;6BAAO;wBAAC;wBAClD,WAAW;4BAAE,QAAQ;gCAAC;gCAAM;gCAAQ;6BAAO;wBAAC;oBAC9C;gBACF;gBACA,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAsB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;gBAC5D;YACF;QACF;QACA,eAAe,OAAO,SAAsB,CAAC,CAAC,EAAE,eAAgC,CAAC,CAAC;YAChF,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,cAAc,GAAG,QAAQ,yCAAyC;YAC/F,MAAM,cAAc;gBAClB,GAAG,YAAY;gBACf,kBAAkB;gBAClB,UAAU,aAAa,QAAQ,IAAI;YACrC;YACA,MAAM,mBAAmB;gBAAC;gBAAqB;aAAyB;YACxE,OAAO,SAAS,CAAC,gBAAgB,CAAC,EAAE;gBAClC,QAAQ;gBACR,MAAM;oBACJ,GAAI,aAAa,IAAI,IAAI,CAAC,CAAC;oBAC3B,GAAI,cAAc,CAAC,CAAC;oBACpB,MAAM;2BAAI;2BAAsB,aAAa,IAAI,EAAE,QAAQ,EAAE;2BAAO,YAAY,QAAQ,EAAE;qBAAE;gBAC9F;YACF;QACF;QACA,mBAAmB,OAAO,MAAc,SAAsB,CAAC,CAAC,EAAE,eAAgC,CAAC,CAAC;YAClG,IAAI;gBACF,oDAAoD;gBACpD,MAAM,OAAO,OAAO,UAAU,EAAE,QAAQ;gBACxC,MAAM,WAAW,OAAO,UAAU,EAAE,YAAY;gBAEhD,mDAAmD;gBACnD,MAAM,OAAO,OAAO,IAAI,IAAI;gBAE5B,+CAA+C;gBAE/C,qEAAqE;gBACrE,MAAM,YAAY;oBAChB;oBACA;oBACA,CAAC,gBAAgB,EAAE,MAAM;oBACzB,CAAC,gBAAgB,EAAE,KAAK,MAAM,EAAE,MAAM;iBACvC;gBAED,yCAAyC;gBACzC,0EAA0E;gBAC1E,qEAAqE;gBACrE,MAAM,cAAc;oBAClB,SAAS;wBACP,MAAM;4BACJ,KAAK;wBACP;oBACF;oBACA,wDAAwD;oBACxD,YAAY;wBACV,MAAM;wBACN,UAAU,EAAE,4BAA4B;oBAC1C;oBACA,MAAM;wBAAC;qBAAK;oBACZ,UAAU;wBACR,YAAY;4BACV,QAAQ;gCAAC;gCAAS;gCAAQ;gCAAW;gCAAe;6BAAc;4BAClE,UAAU;gCACR,eAAe;gCACf,cAAc;oCACZ,UAAU;wCACR,gBAAgB;oCAClB;gCACF;4BACF;4BACA,4DAA4D;4BAC5D,YAAY;gCACV;gCACA;4BACF;4BACA,MAAM;gCAAC;6BAAK;wBACd;wBACA,KAAK;oBACP;gBACF;gBAEA,yCAAyC;gBAEzC,6CAA6C;gBAC7C,MAAM,WAAW,MAAM,SAAS,CAAC,gBAAgB,CAAC,EAAE;oBAClD,QAAQ;oBACR,MAAM;wBACJ,MAAM;oBACR;gBACF;gBAEA,mDAAmD;gBAEnD,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC,EAAE;gBAE9D,qCAAqC;gBACrC,IAAI;oBAEF,gDAAgD;oBAChD,MAAM,EAAE,MAAM,kBAAkB,EAAE,GAAG,sBAAsB,GAAG;oBAC9D,OAAO,MAAM,SAAS,CAAC,gBAAgB,CAAC,EAAE;wBACxC,QAAQ;4BACN,GAAG,oBAAoB;4BACvB,SAAS;gCAAE,MAAM;oCAAE,KAAK;gCAAK;4BAAE;4BAC/B,UAAU;gCAAE,YAAY;oCAAE,UAAU;wCAAC;wCAAiB;qCAA8B;gCAAC;gCAAG,KAAK;4BAAK;wBACpG;wBACA,MAAM;4BAAE,GAAI,aAAa,IAAI,IAAI,CAAC,CAAC;4BAAG,GAAI,sBAAsB,CAAC,CAAC;wBAAE;oBACtE;gBACF,EAAE,OAAO,eAAe;oBACtB,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC,EAAE;oBACxD,MAAM;gBACR;YACF;QACF;QACA,SAAS,OAAO,UAA2B,CAAC,CAAC;YAC3C,MAAM,cAAc;gBAAC;gBAAe;aAAmB;YACvD,OAAO,SAAS,cAAc;gBAC5B,QAAQ;oBAAE,UAAU;gBAAI;gBACxB,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;gBACvD;YACF;QACF;QACA,cAAc,OAAO,MAAc,UAA2B,CAAC,CAAC;YAC9D,MAAM,cAAc;gBAAC;gBAAe;gBAAoB,CAAC,WAAW,EAAE,MAAM;aAAC;YAC7E,OAAO,SAAS,CAAC,UAAU,CAAC,EAAE;gBAC5B,QAAQ;oBACN,SAAS;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;oBAC/B,UAAU;wBACR,YAAY;4BACV,UAAU;gCACR,eAAe;oCACb,QAAQ;wCAAC;wCAAO;wCAAmB;wCAAS;wCAAU;qCAAU,CAAC,cAAc;gCACjF;gCACA,cAAc;oCACZ,UAAU;wCACR,gBAAgB;4CACd,QAAQ;gDAAC;gDAAO;gDAAmB;gDAAS;gDAAU;6CAAU,CAAC,cAAc;wCACjF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;gBACA,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;gBACvD;YACF;QACF;QACA,YAAY,OAAO,UAA2B,CAAC,CAAC;YAC9C,4EAA4E;YAC5E,MAAM,eAAe,QAAQ,KAAK,IAAI;YACtC,MAAM,oBAAoB,QAAQ,IAAI,EAAE,cAAc;YACtD,MAAM,cAAc;gBAAC;gBAAkB;aAAsB;YAC7D,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAE;gBAC1B,QAAQ;oBAAE,UAAU;gBAAI;gBACxB,OAAO;gBACP,MAAM;oBACJ,GAAG,QAAQ,IAAI;oBACf,YAAY;oBACZ,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;gBACvD;YACF;QACF;QACA,iBAAiB,OAAO,MAAc,UAA2B,CAAC,CAAC;YACjE,4EAA4E;YAC5E,MAAM,eAAe,QAAQ,KAAK,IAAI;YACtC,MAAM,oBAAoB,QAAQ,IAAI,EAAE,cAAc;YACtD,MAAM,cAAc;gBAAC;gBAAkB;gBAAuB,CAAC,cAAc,EAAE,MAAM;aAAC;YACtF,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAE;gBAC1B,QAAQ;oBAAE,SAAS;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;oBAAG,UAAU;gBAAI;gBAC1D,OAAO;gBACP,MAAM;oBACJ,GAAG,QAAQ,IAAI;oBACf,YAAY;oBACZ,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;gBACvD;YACF;QACF;IACF;IAEA,4BAA4B;IAC5B,YAAY;QACV,QAAQ,OAAO,SAAuB,CAAC,CAAC;YACtC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,YAAY,GAAG;YACjF,MAAM,cAA2B;gBAC/B,GAAG,UAAU;gBACb,UAAU,WAAW,QAAQ,IAAI;gBACjC,kBAAkB;gBAClB,YAAY;oBAAE,MAAM;oBAAM,UAAU;gBAAS;gBAC7C,SAAS;oBAAE,GAAI,WAAW,OAAO,IAAI,CAAC,CAAC;gBAAE;YAC3C;YACA,IAAI,SAAS,YAAY,OAAO,EAAE,YAAY,OAAO,CAAC,IAAI,GAAG;gBAAE,YAAY;YAAM;YAEjF,qCAAqC;YACrC,MAAM,mBAAmB;gBAAC;aAAoB;YAC9C,IAAI,YAAY,UAAU,EAAE,MAAM,iBAAiB,IAAI,CAAC,CAAC,uBAAuB,EAAE,YAAY,UAAU,CAAC,IAAI,EAAE;YAC/G,IAAI,OAAO,iBAAiB,IAAI,CAAC,CAAC,wBAAwB,EAAE,OAAO;YAEnE,uDAAuD;YACvD,MAAM,gBAAgB;mBAAK,MAAM,QAAQ,EAAE;mBAAM;aAAiB;YAElE,MAAM,cAAgC;gBAAE,MAAM;YAAc;YAC5D,IAAI,MAAM,eAAe,WAAW;gBAClC,YAAY,UAAU,GAAG,KAAK,UAAU;YAC1C;YAEA,OAAO,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC7B,QAAQ;gBACR,OAAO;gBACP,MAAM;YACR;QACF;QACA,WAAW,OAAO,MAAc,UAA2B,CAAC,CAAC;YAC3D,MAAM,cAAc;gBAAC;gBAAqB,CAAC,iBAAiB,EAAE,MAAM;aAAC;YACrE,OAAO,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC7B,QAAQ;oBAAE,SAAS;wBAAE,MAAM;4BAAE,KAAK;wBAAK;oBAAE;oBAAG,UAAU;wBAAE,KAAK;oBAAK;gBAAE;gBACpE,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;gBACvD;YACF;QACF;QACA,aAAa,OAAO,UAA2B,CAAC,CAAC;YAC/C,MAAM,cAAc;gBAAC;aAA0B;YAC/C,OAAO,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC7B,QAAQ;oBACN,QAAQ;wBAAC;qBAAO;oBAChB,YAAY;wBAAE,UAAU;oBAAK;gBAC/B;gBACA,OAAO,QAAQ,KAAK,IAAI;gBACxB,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;oBACrD,YAAY,QAAQ,IAAI,EAAE,cAAc;gBAC1C;YACF;QACF;IACF;IAEA,yBAAyB;IACzB,SAAS;QACP,KAAK,OAAO,UAA2B,CAAC,CAAC,GAAK,SAAS,CAAC,SAAS,CAAC,EAAE;gBAAE,QAAQ;oBAAE,UAAU;oBAAK,kBAAkB;gBAAO;gBAAG,GAAG,OAAO;YAAC;IACxI;IAEA,+BAA+B;IAC/B,eAAe;QACb,KAAK,OAAO,UAA2B,CAAC,CAAC,GAAK,SAAS,CAAC,eAAe,CAAC,EAAE;gBAAE,QAAQ;oBAAE,UAAU;oBAAK,kBAAkB;gBAAO;gBAAG,GAAG,OAAO;YAAC;IAC9I;IAEA,iCAAiC;IACjC,gBAAgB;QACd,KAAK,OAAO,UAA2B,CAAC,CAAC,GAAK,SAAS,CAAC,iBAAiB,CAAC,EAAE;gBAAE,QAAQ;oBAAE,UAAU;oBAAK,kBAAkB;gBAAO;gBAAG,GAAG,OAAO;YAAC;IAChJ;IAEA,qCAAqC;IACrC,qBAAqB;QACnB,KAAK,OAAO,UAA2B,CAAC,CAAC,GAAK,SAAS,CAAC,qBAAqB,CAAC,EAAE;gBAAE,QAAQ;oBAAE,UAAU;oBAAK,kBAAkB;gBAAO;gBAAG,GAAG,OAAO;YAAC;IACpJ;IAEA,kBAAkB;IAClB,QAAQ;QACN,aAAa,OAAO,UAA2B,CAAC,CAAC,GAAK,SAAS,CAAC,eAAe,CAAC,EAAE;gBAAE,QAAQ;oBAAE,UAAU;oBAAK,kBAAkB;gBAAO;gBAAG,GAAG,OAAO;YAAC;QACpJ,aAAa,OAAO,UAA2B,CAAC,CAAC,GAAK,SAAS,CAAC,SAAS,CAAC,EAAE;gBAAE,QAAQ;oBAAE,UAAU;oBAAK,kBAAkB;gBAAO;gBAAG,GAAG,OAAO;YAAC;QAC9I,iBAAiB,OAAO,UAA2B,CAAC,CAAC,GAAK,SAAS,CAAC,cAAc,CAAC,EAAE;gBAAE,QAAQ;oBAAE,UAAU;oBAAK,kBAAkB;gBAAO;gBAAG,GAAG,OAAO;YAAC;IACzJ;IAEA,4BAA4B;IAC5B,aAAa;QACX,QAAQ,OAAO,SAAuB,CAAC,CAAC;YACtC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,YAAY,GAAG;YACjF,MAAM,cAA2B;gBAC/B,GAAG,UAAU;gBACb,UAAU,WAAW,QAAQ,IAAI;gBACjC,kBAAkB;gBAClB,YAAY;oBAAE,MAAM;oBAAM,UAAU;gBAAS;gBAC7C,SAAS;oBAAE,GAAI,WAAW,OAAO,IAAI,CAAC,CAAC;gBAAE;YAC3C;YACA,IAAI,SAAS,YAAY,OAAO,EAAE,YAAY,OAAO,CAAC,IAAI,GAAG;gBAAE,YAAY;YAAM;YAEjF,qCAAqC;YACrC,MAAM,mBAAmB;gBAAC;aAAqB;YAC/C,IAAI,YAAY,UAAU,EAAE,MAAM,iBAAiB,IAAI,CAAC,CAAC,wBAAwB,EAAE,YAAY,UAAU,CAAC,IAAI,EAAE;YAChH,IAAI,OAAO,iBAAiB,IAAI,CAAC,CAAC,yBAAyB,EAAE,OAAO;YAEpE,uDAAuD;YACvD,MAAM,gBAAgB;mBAAK,MAAM,QAAQ,EAAE;mBAAM;aAAiB;YAElE,MAAM,cAAgC;gBAAE,MAAM;YAAc;YAC5D,IAAI,MAAM,eAAe,WAAW;gBAClC,YAAY,UAAU,GAAG,KAAK,UAAU;YAC1C;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,SAAS,CAAC,YAAY,CAAC,EAAE;oBAC9C,QAAQ;oBACR,OAAO;oBACP,MAAM;gBACR;gBAEA,8DAA8D;gBAC9D,IAAI,UAAU,QAAQ,MAAM,OAAO,CAAC,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG,GAAG;oBAC9E,MAAM,YAAY,SAAS,IAAI,CAAC,EAAE;oBAElC,iEAAiE;oBACjE,+CAA+C;oBAC/C,IAAI,CAAC,UAAU,UAAU,IAAI,UAAU,EAAE,EAAE;wBACzC,4CAA4C;wBAC5C,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC;4BACjC,+CAA+C;4BAC/C,IAAI,KAAK,UAAU,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO;4BAExC,4EAA4E;4BAC5E,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,GAAG;4BAExB,kDAAkD;4BAClD,OAAO;gCACL;gCACA,YAAY;oCAAE,GAAG,IAAI;gCAAC;4BACxB;wBACF;oBACF;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM;YACR;QACF;QACA,aAAa,OAAO,UAA2B,CAAC,CAAC;YAC/C,MAAM,cAAc;gBAAC;aAA2B;YAChD,OAAO,SAAS,CAAC,YAAY,CAAC,EAAE;gBAC9B,QAAQ;oBACN,QAAQ;wBAAC;qBAAO;oBAChB,YAAY;wBAAE,UAAU;oBAAK;gBAC/B;gBACA,OAAO,QAAQ,KAAK,IAAI;gBACxB,MAAM;oBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;oBACtB,MAAM;2BAAI;2BAAiB,QAAQ,IAAI,EAAE,QAAQ,EAAE;qBAAE;oBACrD,YAAY,QAAQ,IAAI,EAAE,cAAc;gBAC1C;YACF;QACF;QACA,WAAW,OAAO,MAAc,UAA2B,CAAC,CAAC;YAC3D,MAAM,mBAAmB;gBAAC;gBAAsB,CAAC,iBAAiB,EAAE,MAAM;aAAC;YAC3E,IAAI;gBACF,MAAM,WAAW,MAAM,SAAS,CAAC,YAAY,CAAC,EAAE;oBAC9C,QAAQ;wBACN,SAAS;4BAAE,MAAM;gCAAE,KAAK;4BAAK;wBAAE;wBAC/B,UAAU;4BAAE,KAAK;4BAAM,eAAe;4BAAM,SAAS;4BAAM,eAAe;wBAAK;oBACjF;oBACA,MAAM;wBACJ,GAAI,QAAQ,IAAI,IAAI,CAAC,CAAC;wBACtB,MAAM;+BAAI;+BAAsB,QAAQ,IAAI,EAAE,QAAQ,EAAE;yBAAE;oBAC5D;gBACF;gBAEA,8DAA8D;gBAC9D,IAAI,UAAU,QAAQ,MAAM,OAAO,CAAC,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG,GAAG;oBAC9E,MAAM,YAAY,SAAS,IAAI,CAAC,EAAE;oBAElC,iEAAiE;oBACjE,+CAA+C;oBAC/C,IAAI,CAAC,UAAU,UAAU,IAAI,UAAU,EAAE,EAAE;wBACzC,4CAA4C;wBAC5C,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC;4BACjC,+CAA+C;4BAC/C,IAAI,KAAK,UAAU,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO;4BAExC,4EAA4E;4BAC5E,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,GAAG;4BAExB,kDAAkD;4BAClD,OAAO;gCACL;gCACA,YAAY;oCAAE,GAAG,IAAI;gCAAC;4BACxB;wBACF;oBACF;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC,EAAE;gBAC3D,MAAM;YACR;QACF;IACF;IAEA,cAAc;IACd,KAAK;QACH,aAAa,CAAC;YACZ,IAAI,CAAC,QAAQ,OAAO;YAEpB,qEAAqE;YACrE,MAAM,MAAM,OAAO,UAAU,EAAE,KAAK,MAAM,cAAc;YACxD,MAAM,aAAa,OAAO,UAAU,EAAE,cAAc,EAAE;YAEtD,OAAO;gBACL,OAAO,KAAK,aAAa,OAAO,UAAU,EAAE,SAAS,OAAO,UAAU,EAAE;gBACxE,aAAa,KAAK,mBAAmB,OAAO,UAAU,EAAE,eAAe,OAAO,UAAU,EAAE;gBAC1F,WAAW,YAAY,KAAK,CAAC,OAAc,KAAK,aAAa,KAAK,eAAe;gBACjF,SAAS,YAAY,KAAK,CAAC,OAAc,KAAK,aAAa,KAAK,cAAc;gBAC9E,gBAAgB,KAAK,kBAAkB;gBACvC,cAAc,KAAK,gBAAgB;gBACnC,YAAY,KAAK,cAAc;YACjC;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/serverCache.ts"], "sourcesContent": ["/**\r\n * Advanced server-side caching system for Next.js 15 and Strapi 5\r\n * \r\n * Key improvements:\r\n * - Adaptive caching based on content type and environment\r\n * - Stale-while-revalidate strategy using tags for optimal performance\r\n * - Tiered cache hierarchy with contextual invalidation\r\n * - Performance monitoring for cache operations\r\n * - Optimized for Next.js 15's improved cache API\r\n */\r\nimport { cache } from 'react';\r\nimport { revalidateTag, unstable_cache } from 'next/cache';\r\nimport { getStrapiContent } from './strapi';\r\n\r\n// Performance monitoring\r\nconst ENABLE_CACHE_METRICS = process.env.NEXT_PUBLIC_CACHE_METRICS === 'true';\r\nconst cacheMetrics = {\r\n  hits: 0,\r\n  misses: 0,\r\n  revalidations: 0,\r\n  requestTimes: [] as number[],\r\n};\r\n\r\n// Content categories with their respective cache durations based on environment\r\n// Tiered caching approach with shorter times in development\r\nconst CONTENT_CACHE_CONFIG = {\r\n  // Static/rarely changing content \r\n  'global-setting': process.env.NODE_ENV === 'production' ? 604800 : 60, // 7 days in prod, 1 min in dev\r\n  'categories': process.env.NODE_ENV === 'production' ? 604800 : 300,    // 7 days in prod, 5 min in dev\r\n  'specialties': process.env.NODE_ENV === 'production' ? 604800 : 300,   // 7 days in prod, 5 min in dev\r\n  'conditions': process.env.NODE_ENV === 'production' ? 604800 : 300,    // 7 days in prod, 5 min in dev\r\n  \r\n  // Semi-dynamic content\r\n  'clinics': process.env.NODE_ENV === 'production' ? 86400 : 180,        // 24 hours in prod, 3 min in dev\r\n  'practitioners': process.env.NODE_ENV === 'production' ? 86400 : 180,  // 24 hours in prod, 3 min in dev\r\n  \r\n  // Frequently changing content\r\n  'blog-posts': process.env.NODE_ENV === 'production' ? 21600 : 120,     // 6 hours in prod, 2 min in dev\r\n  \r\n  // Default fallback\r\n  'default': process.env.NODE_ENV === 'production' ? 3600 : 60           // 1 hour in prod, 1 min in dev\r\n};\r\n\r\n// Enhanced related content mapping for intelligent revalidation cascades\r\n// This defines which content types should be revalidated together\r\nconst RELATED_CONTENT = {\r\n  'blog-posts': ['blog-categories', 'blog-tags', 'blog-homepage', 'homepage'],\r\n  'clinics': ['categories', 'specialties', 'homepage'],\r\n  'practitioners': ['specialties', 'homepage'],\r\n  'categories': ['clinics'],\r\n  'specialties': ['clinics', 'practitioners'],\r\n  'conditions': ['clinics', 'practitioners'],\r\n};\r\n\r\n// Stale-while-revalidate configuration (in seconds)\r\n// This extends cache lifetime while fetching fresh data in background\r\nconst SWR_EXTENSION = {\r\n  'global-setting': 86400,  // 24 hour stale extension\r\n  'categories': 86400,      // 24 hour stale extension\r\n  'specialties': 86400,     // 24 hour stale extension\r\n  'conditions': 86400,      // 24 hour stale extension\r\n  'clinics': 43200,         // 12 hour stale extension\r\n  'practitioners': 43200,   // 12 hour stale extension\r\n  'blog-posts': 21600,      // 6 hour stale extension\r\n  'default': 3600           // 1 hour stale extension\r\n};\r\n\r\n/**\r\n * Enhanced cache tag generation for more precise invalidation\r\n * Creates a hierarchical tag structure for granular cache control\r\n * \r\n * @param contentType - The content type to generate tags for\r\n * @param id - Optional ID for entity-specific tags\r\n * @param additionalTags - Any extra tags to include\r\n * @returns Array of cache tags\r\n */\r\nfunction generateCacheTags(\r\n  contentType: string, \r\n  id?: string | number, \r\n  additionalTags: string[] = []\r\n): string[] {\r\n  // Start with base content type tag\r\n  const tags = [`strapi-${contentType}`];\r\n  \r\n  // Add hierarchy tags for better organization and targeted invalidation\r\n  if (contentType.includes('-')) {\r\n    const [parent, child] = contentType.split('-');\r\n    tags.push(`strapi-${parent}`);\r\n    \r\n    // For collection types, add a collection tag\r\n    if (child === 'posts' || child === 'categories' || child === 'tags') {\r\n      tags.push(`strapi-collection-${parent}`);\r\n    }\r\n  }\r\n  \r\n  // Add page-specific tag for paginated content\r\n  if (id && !isNaN(Number(id)) && Number(id) > 0) {\r\n    tags.push(`strapi-${contentType}-page-${id}`);\r\n  }\r\n  // Add slug/ID-specific tag for single entities\r\n  else if (id) {\r\n    tags.push(`strapi-${contentType}-${id}`);\r\n  }\r\n  \r\n  // Add additional custom tags\r\n  if (additionalTags.length > 0) {\r\n    tags.push(...additionalTags);\r\n  }\r\n  \r\n  return tags;\r\n}\r\n\r\n/**\r\n * Advanced revalidation with performance monitoring and logging\r\n * \r\n * @param contentType - The content type to revalidate\r\n * @param id - Optional specific ID to revalidate\r\n * @param options - Additional options for revalidation behavior\r\n */\r\nexport function revalidateContent(\r\n  contentType: string,\r\n  id?: string | number,\r\n  options: {\r\n    revalidateRelated?: boolean;\r\n    reason?: string;\r\n    priority?: 'high' | 'normal' | 'low';\r\n  } = {}\r\n): void {\r\n  const startTime = performance.now();\r\n  const { revalidateRelated = true, reason = 'manual', priority = 'normal' } = options;\r\n  \r\n  // Revalidate the primary content\r\n  const tags = generateCacheTags(contentType, id);\r\n  \r\n  // Log operation start for debugging\r\n  if (process.env.NODE_ENV === 'development' || ENABLE_CACHE_METRICS) {\r\n    console.log(`🔄 Revalidating ${contentType}${id ? ` (ID: ${id})` : ''} - Reason: ${reason}`);\r\n  }\r\n  \r\n  // Execute revalidation\r\n  tags.forEach(tag => {\r\n    try {\r\n      revalidateTag(tag);\r\n      if (ENABLE_CACHE_METRICS) {\r\n        cacheMetrics.revalidations++;\r\n      }\r\n    } catch (error) {\r\n      console.error(`Error revalidating tag ${tag}:`, error);\r\n    }\r\n  });\r\n  \r\n  // Revalidate related content types if specified\r\n  if (revalidateRelated && contentType in RELATED_CONTENT) {\r\n    const relatedTypes = RELATED_CONTENT[contentType as keyof typeof RELATED_CONTENT];\r\n    relatedTypes.forEach(relatedType => {\r\n      // Skip high-volume related invalidations for low priority updates\r\n      if (priority === 'low' && (relatedType === 'homepage' || relatedType === 'blog-homepage')) {\r\n        return;\r\n      }\r\n      \r\n      const relatedTags = generateCacheTags(relatedType);\r\n      relatedTags.forEach(tag => {\r\n        try {\r\n          revalidateTag(tag);\r\n          if (ENABLE_CACHE_METRICS) {\r\n            cacheMetrics.revalidations++;\r\n          }\r\n        } catch (error) {\r\n          console.error(`Error revalidating related tag ${tag}:`, error);\r\n        }\r\n      });\r\n    });\r\n  }\r\n  \r\n  // Performance tracking\r\n  if (ENABLE_CACHE_METRICS) {\r\n    const duration = performance.now() - startTime;\r\n    cacheMetrics.requestTimes.push(duration);\r\n    \r\n    // Log performance metrics periodically\r\n    if (cacheMetrics.revalidations % 10 === 0) {\r\n      const avgTime = cacheMetrics.requestTimes.reduce((sum, time) => sum + time, 0) / \r\n                     cacheMetrics.requestTimes.length;\r\n      console.log(`Cache metrics: ${cacheMetrics.revalidations} revalidations, avg time: ${avgTime.toFixed(2)}ms`);\r\n    }\r\n  }\r\n  \r\n  // Log completion\r\n  if (process.env.NODE_ENV === 'development') {\r\n    console.log(`✅ Revalidated cache for ${contentType}${id ? ` (ID: ${id})` : ''} in ${\r\n      (performance.now() - startTime).toFixed(2)\r\n    }ms`);\r\n  }\r\n}\r\n\r\n/**\r\n * Smart batch revalidation with prioritization\r\n * Efficiently revalidates multiple content types while minimizing redundant operations\r\n */\r\nexport function revalidateAllContent(options: { \r\n  reason?: string; \r\n  excludeTypes?: string[];\r\n  highPriorityOnly?: boolean;\r\n} = {}): void {\r\n  const startTime = performance.now();\r\n  const { reason = 'manual', excludeTypes = [], highPriorityOnly = false } = options;\r\n  \r\n  // Content types organized by priority\r\n  const highPriorityTypes = ['global-setting', 'homepage', 'blog-homepage'];\r\n  const mediumPriorityTypes = ['blog-posts', 'clinics', 'practitioners'];\r\n  const lowPriorityTypes = ['categories', 'specialties', 'conditions', 'blog-categories', 'blog-tags'];\r\n  \r\n  // Determine which types to revalidate\r\n  const typesToRevalidate = [\r\n    ...highPriorityTypes,\r\n    ...(highPriorityOnly ? [] : [...mediumPriorityTypes, ...lowPriorityTypes])\r\n  ].filter(type => !excludeTypes.includes(type));\r\n  \r\n  // Track already revalidated tags to avoid duplicates\r\n  const revalidatedTags = new Set<string>();\r\n  \r\n  // Log operation start\r\n  console.log(`🔄 Starting batch revalidation (${reason}): ${typesToRevalidate.length} content types`);\r\n  \r\n  // Process each content type\r\n  typesToRevalidate.forEach(contentType => {\r\n    const tags = generateCacheTags(contentType);\r\n    \r\n    tags.forEach(tag => {\r\n      // Skip if already revalidated in this batch\r\n      if (!revalidatedTags.has(tag)) {\r\n        try {\r\n          revalidateTag(tag);\r\n          revalidatedTags.add(tag);\r\n          \r\n          if (ENABLE_CACHE_METRICS) {\r\n            cacheMetrics.revalidations++;\r\n          }\r\n        } catch (error) {\r\n          console.error(`Error in batch revalidation for tag ${tag}:`, error);\r\n        }\r\n      }\r\n    });\r\n  });\r\n  \r\n  // Log completion with performance metrics\r\n  const duration = performance.now() - startTime;\r\n  console.log(`✅ Batch revalidation complete. ${revalidatedTags.size} unique tags processed in ${duration.toFixed(2)}ms`);\r\n}\r\n\r\n/**\r\n * Enhanced cache configuration generator with stale-while-revalidate support\r\n * \r\n * @param contentType - The content type to get cache config for\r\n * @param options - Optional configuration overrides\r\n * @returns Next.js cache configuration object\r\n */\r\nfunction getCacheConfig(\r\n  contentType: string, \r\n  options?: { \r\n    revalidate?: number | false; \r\n    tags?: string[];\r\n    staleWhileRevalidate?: boolean | number;\r\n  }\r\n) {\r\n  // Determine revalidation time from options, type-specific config, or default\r\n  const fallbackRevalidate = \r\n    options?.revalidate !== undefined ? options.revalidate :\r\n    contentType in CONTENT_CACHE_CONFIG \r\n      ? CONTENT_CACHE_CONFIG[contentType as keyof typeof CONTENT_CACHE_CONFIG]\r\n      : CONTENT_CACHE_CONFIG.default;\r\n  \r\n  // Generate appropriate cache tags\r\n  const defaultTags = generateCacheTags(contentType);\r\n  const cacheTags = options?.tags || defaultTags;\r\n  \r\n  // Determine if we should use stale-while-revalidate\r\n  const enableSWR = options?.staleWhileRevalidate !== false;\r\n  \r\n  // Configure stale-while-revalidate if enabled\r\n  if (enableSWR && fallbackRevalidate !== false) {\r\n    // Get SWR extension value (either from options, type-specific config, or default)\r\n    const swrValue = typeof options?.staleWhileRevalidate === 'number' \r\n      ? options.staleWhileRevalidate\r\n      : contentType in SWR_EXTENSION\r\n        ? SWR_EXTENSION[contentType as keyof typeof SWR_EXTENSION]\r\n        : SWR_EXTENSION.default;\r\n    \r\n    // Add Cache-Control header with stale-while-revalidate\r\n    return {\r\n      revalidate: fallbackRevalidate,\r\n      tags: cacheTags,\r\n      extraHeaders: {\r\n        'Cache-Control': `public, max-age=${fallbackRevalidate}, stale-while-revalidate=${swrValue}`\r\n      }\r\n    };\r\n  }\r\n  \r\n  // Basic config without SWR\r\n  return {\r\n    revalidate: fallbackRevalidate,\r\n    tags: cacheTags\r\n  };\r\n}\r\n\r\n/**\r\n * createOptimizedFetch: Higher-order function that wraps data fetching with advanced caching\r\n * \r\n * @param fetcher - The actual data fetching function\r\n * @param options - Cache configuration options\r\n * @returns Cached function with the same signature as the original fetcher\r\n */\r\nfunction createOptimizedFetch<T extends (...args: any[]) => Promise<any>>(\r\n  fetcher: T,\r\n  options: {\r\n    contentType: string;\r\n    revalidate?: number;\r\n    tags?: string[];\r\n    enableSWR?: boolean;\r\n  }\r\n) {\r\n  // Use simplified version with fixed tags for better TypeScript compatibility\r\n  const cacheTags = options.tags || [`strapi-${options.contentType}`];\r\n  \r\n  // Determine revalidation time from options or config\r\n  const revalidateTime = options.revalidate ?? \r\n                       CONTENT_CACHE_CONFIG[options.contentType as keyof typeof CONTENT_CACHE_CONFIG] ?? \r\n                       CONTENT_CACHE_CONFIG.default;\r\n  \r\n  // Return a cached version of the fetcher\r\n  return unstable_cache(\r\n    async (...args: Parameters<T>) => {\r\n      const startTime = ENABLE_CACHE_METRICS ? performance.now() : 0;\r\n      try {\r\n        const result = await fetcher(...args);\r\n        \r\n        // Track performance if enabled\r\n        if (ENABLE_CACHE_METRICS && startTime) {\r\n          const duration = performance.now() - startTime;\r\n          cacheMetrics.requestTimes.push(duration);\r\n          cacheMetrics.hits++;\r\n        }\r\n        \r\n        return result;\r\n      } catch (error) {\r\n        if (ENABLE_CACHE_METRICS) {\r\n          cacheMetrics.misses++;\r\n        }\r\n        throw error;\r\n      }\r\n    },\r\n    // Use simpler string keys to avoid type issues\r\n    [`${options.contentType}`],\r\n    // Cache configuration with fixed tags\r\n    {\r\n      revalidate: revalidateTime,\r\n      tags: cacheTags\r\n    }\r\n  );\r\n}\r\n\r\n/**\r\n * Cached function to fetch global settings\r\n * Using a longer cache time (24 hours) since global settings rarely change\r\n * Still using tags for on-demand revalidation when needed\r\n */\r\nexport const getGlobalSettings = cache(async () => {\r\n  return getStrapiContent.global.getSettings({\r\n    cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15\r\n    next: {\r\n      revalidate: 3600, // Changed to 1 hour cache\r\n      tags: ['strapi-global-setting']\r\n    }\r\n  });\r\n});\r\n\r\n/**\r\n * Cached function to fetch categories\r\n * Using a longer cache time (1 week) since categories rarely change\r\n * Still using tags for on-demand revalidation when needed\r\n */\r\nexport const getCategories = cache(async (options?: {\r\n  page?: number;\r\n  pageSize?: number;\r\n  query?: string;\r\n  next?: { revalidate?: number | false; tags?: string[] };\r\n}) => {\r\n  // Default to 1 week revalidation and specific tags if not provided\r\n  const nextOptions = options?.next || {\r\n    revalidate: 604800, // 1 week (7 days)\r\n    tags: ['strapi-categories', 'strapi-categories-all'],\r\n  };\r\n  return getStrapiContent.categories.getAll({\r\n    ...options,\r\n    cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15\r\n    next: nextOptions\r\n  });\r\n});\r\n\r\n/**\r\n * Cached function to fetch footer categories\r\n * Using a longer cache time (1 week) since footer categories rarely change\r\n * Still using tags for on-demand revalidation when needed\r\n */\r\nexport const getFooterCategories = cache(async (options?: {\r\n  next?: { revalidate?: number | false; tags?: string[] };\r\n}) => {\r\n  // Default to 1 week revalidation and specific tags if not provided\r\n  const nextOptions = options?.next || {\r\n    revalidate: 604800, // 1 week (7 days)\r\n    tags: ['strapi-categories', 'strapi-categories-footer'],\r\n  };\r\n  return getStrapiContent.categories.getFooterCategories({\r\n    ...options,\r\n    cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15\r\n    next: nextOptions\r\n  });\r\n});\r\n\r\n/**\r\n * Cached function to fetch specialties\r\n * Using a longer cache time (1 week) since specialties rarely change\r\n * Still using tags for on-demand revalidation when needed\r\n */\r\nexport const getSpecialties = cache(async (options?: {\r\n  page?: number;\r\n  pageSize?: number;\r\n  query?: string;\r\n  next?: { revalidate?: number | false; tags?: string[] }; // Add next options\r\n}) => {\r\n  // Default to 1 week revalidation and specific tags if not provided\r\n  const nextOptions = options?.next || {\r\n    revalidate: 604800, // 1 week (7 days)\r\n    tags: ['strapi-specialties', 'strapi-specialties-all'],\r\n  };\r\n  return getStrapiContent.specialties.getAll({\r\n    ...options,\r\n    cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15\r\n    next: nextOptions\r\n  });\r\n});\r\n\r\n/**\r\n * Cached function to fetch clinics\r\n */\r\nexport const getClinics = cache(async (options: {\r\n  page?: number;\r\n  pageSize?: number;\r\n  query?: string;\r\n  location?: string;\r\n  specialtySlug?: string;\r\n  conditionSlug?: string;\r\n  next?: { revalidate?: number | false; tags?: string[] };\r\n} = {}) => {\r\n  const nextOptions = options.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-clinics', `page-${options.page || 1}`], // Add more specific tags as needed\r\n  };\r\n  return getStrapiContent.clinics.getAll({ ...options, next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch a single clinic by slug\r\n */\r\nexport const getClinicBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-clinics', `strapi-clinic-${slug}`],\r\n  };\r\n  return getStrapiContent.clinics.getBySlug(slug, { next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch featured clinics\r\n */\r\nexport const getFeaturedClinics = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-clinics', 'strapi-clinics-featured'],\r\n  };\r\n  return getStrapiContent.clinics.getFeatured({ next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch practitioners\r\n */\r\nexport const getPractitioners = cache(async (options: {\r\n  page?: number;\r\n  pageSize?: number;\r\n  query?: string;\r\n  location?: string;\r\n  specialtySlug?: string;\r\n  conditionSlug?: string;\r\n  next?: { revalidate?: number | false; tags?: string[] };\r\n} = {}) => {\r\n  const nextOptions = options.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-practitioners', `page-${options.page || 1}`], // Add more specific tags as needed\r\n  };\r\n  return getStrapiContent.practitioners.getAll({ ...options, next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch a single practitioner by slug\r\n */\r\nexport const getPractitionerBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-practitioner', `strapi-practitioner-${slug}`], // Changed 'strapi-practitioners' to 'strapi-practitioner'\r\n  };\r\n  return getStrapiContent.practitioners.getBySlug(slug, { next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch featured practitioners\r\n */\r\nexport const getFeaturedPractitioners = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-practitioners', 'strapi-practitioners-featured'],\r\n  };\r\n  return getStrapiContent.practitioners.getFeatured({ next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch blog posts\r\n */\r\nexport const getBlogPosts = cache(async (options: {\r\n  page?: number;\r\n  pageSize?: number;\r\n  query?: string;\r\n  categorySlug?: string;\r\n  tagSlug?: string;\r\n  sort?: string | string[];\r\n  populate?: string | string[] | Record<string, any>;\r\n  next?: { revalidate?: number | false; tags?: string[] };\r\n} = {}) => {\r\n  const nextOptions = options.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-blog-posts', `page-${options.page || 1}`], // Add more specific tags as needed\r\n  };\r\n  return getStrapiContent.blog.getPosts({\r\n    ...options,\r\n    sort: options.sort || ['publishDate:desc'],\r\n    next: nextOptions,\r\n  });\r\n});\r\n\r\n/**\r\n * Cached function to fetch a single blog post by slug\r\n */\r\nexport const getBlogPostBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-blog-posts', `strapi-blog-post-${slug}`],\r\n  };\r\n  return getStrapiContent.blog.getPostBySlug(slug, { next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch featured blog posts\r\n */\r\nexport const getFeaturedBlogPosts = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-blog-posts', 'strapi-blog-posts-featured'],\r\n  };\r\n  return getStrapiContent.blog.getPosts({\r\n    filters: { isFeatured: { $eq: true } },\r\n    sort: ['publishDate:desc'],\r\n    pagination: { page: 1, pageSize: 10 },\r\n    next: nextOptions,\r\n  });\r\n});\r\n\r\n/**\r\n * Cached function to fetch blog categories\r\n */\r\nexport const getBlogCategories = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-blog-categories', 'strapi-categories'],\r\n  };\r\n  return getStrapiContent.blog.getCategories({ next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch blog tags\r\n */\r\nexport const getBlogTags = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-blog-tags', 'strapi-tags'],\r\n  };\r\n  return getStrapiContent.blog.getTags({ next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch blog homepage\r\n */\r\nexport const getBlogHomepage = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-global-setting', 'strapi-blog-homepage'],\r\n  };\r\n  return getStrapiContent.global.getBlogHomepage({ next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch a specialty by slug\r\n */\r\nexport const getSpecialtyBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-specialties', `strapi-specialty-${slug}`],\r\n  };\r\n  return getStrapiContent.specialties.getBySlug(slug, { next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch a condition by slug\r\n */\r\nexport const getConditionBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-conditions', `strapi-condition-${slug}`],\r\n  };\r\n  return getStrapiContent.conditions.getBySlug(slug, { next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch conditions\r\n */\r\nexport const getConditions = cache(async (options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-conditions', 'strapi-conditions-all'],\r\n  };\r\n  return getStrapiContent.conditions.getAll({ next: nextOptions });\r\n});\r\n\r\n/**\r\n * Cached function to fetch a category by slug\r\n */\r\nexport const getCategoryBySlug = cache(async (slug: string, options?: { next?: { revalidate?: number | false; tags?: string[] }}) => {\r\n  const nextOptions = options?.next || {\r\n    revalidate: 3600,\r\n    tags: ['strapi-categories', `strapi-category-${slug}`],\r\n  };\r\n  return getStrapiContent.categories.getBySlug(slug, { next: nextOptions });\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;;;;;;;;;;;;;;;;;;;;;AACD;AACA;AACA;;;;AAEA,yBAAyB;AACzB,MAAM,uBAAuB,QAAQ,GAAG,CAAC,yBAAyB,KAAK;AACvE,MAAM,eAAe;IACnB,MAAM;IACN,QAAQ;IACR,eAAe;IACf,cAAc,EAAE;AAClB;AAEA,gFAAgF;AAChF,4DAA4D;AAC5D,MAAM,uBAAuB;IAC3B,kCAAkC;IAClC,kBAAkB,6EAAiD;IACnE,cAAc,6EAAiD;IAC/D,eAAe,6EAAiD;IAChE,cAAc,6EAAiD;IAE/D,uBAAuB;IACvB,WAAW,6EAAgD;IAC3D,iBAAiB,6EAAgD;IAEjE,8BAA8B;IAC9B,cAAc,6EAAgD;IAE9D,mBAAmB;IACnB,WAAW,6EAA+C,GAAa,+BAA+B;AACxG;AAEA,yEAAyE;AACzE,kEAAkE;AAClE,MAAM,kBAAkB;IACtB,cAAc;QAAC;QAAmB;QAAa;QAAiB;KAAW;IAC3E,WAAW;QAAC;QAAc;QAAe;KAAW;IACpD,iBAAiB;QAAC;QAAe;KAAW;IAC5C,cAAc;QAAC;KAAU;IACzB,eAAe;QAAC;QAAW;KAAgB;IAC3C,cAAc;QAAC;QAAW;KAAgB;AAC5C;AAEA,oDAAoD;AACpD,sEAAsE;AACtE,MAAM,gBAAgB;IACpB,kBAAkB;IAClB,cAAc;IACd,eAAe;IACf,cAAc;IACd,WAAW;IACX,iBAAiB;IACjB,cAAc;IACd,WAAW,KAAe,yBAAyB;AACrD;AAEA;;;;;;;;CAQC,GACD,SAAS,kBACP,WAAmB,EACnB,EAAoB,EACpB,iBAA2B,EAAE;IAE7B,mCAAmC;IACnC,MAAM,OAAO;QAAC,CAAC,OAAO,EAAE,aAAa;KAAC;IAEtC,uEAAuE;IACvE,IAAI,YAAY,QAAQ,CAAC,MAAM;QAC7B,MAAM,CAAC,QAAQ,MAAM,GAAG,YAAY,KAAK,CAAC;QAC1C,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ;QAE5B,6CAA6C;QAC7C,IAAI,UAAU,WAAW,UAAU,gBAAgB,UAAU,QAAQ;YACnE,KAAK,IAAI,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QACzC;IACF;IAEA,8CAA8C;IAC9C,IAAI,MAAM,CAAC,MAAM,OAAO,QAAQ,OAAO,MAAM,GAAG;QAC9C,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,YAAY,MAAM,EAAE,IAAI;IAC9C,OAEK,IAAI,IAAI;QACX,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE,IAAI;IACzC;IAEA,6BAA6B;IAC7B,IAAI,eAAe,MAAM,GAAG,GAAG;QAC7B,KAAK,IAAI,IAAI;IACf;IAEA,OAAO;AACT;AASO,SAAS,kBACd,WAAmB,EACnB,EAAoB,EACpB,UAII,CAAC,CAAC;IAEN,MAAM,YAAY,YAAY,GAAG;IACjC,MAAM,EAAE,oBAAoB,IAAI,EAAE,SAAS,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG;IAE7E,iCAAiC;IACjC,MAAM,OAAO,kBAAkB,aAAa;IAE5C,oCAAoC;IACpC,wCAAoE;QAClE,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,cAAc,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,WAAW,EAAE,QAAQ;IAC7F;IAEA,uBAAuB;IACvB,KAAK,OAAO,CAAC,CAAA;QACX,IAAI;YACF,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;YACd,IAAI,sBAAsB;gBACxB,aAAa,aAAa;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC,EAAE;QAClD;IACF;IAEA,gDAAgD;IAChD,IAAI,qBAAqB,eAAe,iBAAiB;QACvD,MAAM,eAAe,eAAe,CAAC,YAA4C;QACjF,aAAa,OAAO,CAAC,CAAA;YACnB,kEAAkE;YAClE,IAAI,aAAa,SAAS,CAAC,gBAAgB,cAAc,gBAAgB,eAAe,GAAG;gBACzF;YACF;YAEA,MAAM,cAAc,kBAAkB;YACtC,YAAY,OAAO,CAAC,CAAA;gBAClB,IAAI;oBACF,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;oBACd,IAAI,sBAAsB;wBACxB,aAAa,aAAa;oBAC5B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC,EAAE;gBAC1D;YACF;QACF;IACF;IAEA,uBAAuB;IACvB,IAAI,sBAAsB;QACxB,MAAM,WAAW,YAAY,GAAG,KAAK;QACrC,aAAa,YAAY,CAAC,IAAI,CAAC;QAE/B,uCAAuC;QACvC,IAAI,aAAa,aAAa,GAAG,OAAO,GAAG;YACzC,MAAM,UAAU,aAAa,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,MAAM,KAC7D,aAAa,YAAY,CAAC,MAAM;YAC/C,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,aAAa,aAAa,CAAC,0BAA0B,EAAE,QAAQ,OAAO,CAAC,GAAG,EAAE,CAAC;QAC7G;IACF;IAEA,iBAAiB;IACjB,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,cAAc,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,EAChF,CAAC,YAAY,GAAG,KAAK,SAAS,EAAE,OAAO,CAAC,GACzC,EAAE,CAAC;IACN;AACF;AAMO,SAAS,qBAAqB,UAIjC,CAAC,CAAC;IACJ,MAAM,YAAY,YAAY,GAAG;IACjC,MAAM,EAAE,SAAS,QAAQ,EAAE,eAAe,EAAE,EAAE,mBAAmB,KAAK,EAAE,GAAG;IAE3E,sCAAsC;IACtC,MAAM,oBAAoB;QAAC;QAAkB;QAAY;KAAgB;IACzE,MAAM,sBAAsB;QAAC;QAAc;QAAW;KAAgB;IACtE,MAAM,mBAAmB;QAAC;QAAc;QAAe;QAAc;QAAmB;KAAY;IAEpG,sCAAsC;IACtC,MAAM,oBAAoB;WACrB;WACC,mBAAmB,EAAE,GAAG;eAAI;eAAwB;SAAiB;KAC1E,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,aAAa,QAAQ,CAAC;IAExC,qDAAqD;IACrD,MAAM,kBAAkB,IAAI;IAE5B,sBAAsB;IACtB,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,OAAO,GAAG,EAAE,kBAAkB,MAAM,CAAC,cAAc,CAAC;IAEnG,4BAA4B;IAC5B,kBAAkB,OAAO,CAAC,CAAA;QACxB,MAAM,OAAO,kBAAkB;QAE/B,KAAK,OAAO,CAAC,CAAA;YACX,4CAA4C;YAC5C,IAAI,CAAC,gBAAgB,GAAG,CAAC,MAAM;gBAC7B,IAAI;oBACF,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;oBACd,gBAAgB,GAAG,CAAC;oBAEpB,IAAI,sBAAsB;wBACxB,aAAa,aAAa;oBAC5B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,IAAI,CAAC,CAAC,EAAE;gBAC/D;YACF;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,WAAW,YAAY,GAAG,KAAK;IACrC,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,gBAAgB,IAAI,CAAC,0BAA0B,EAAE,SAAS,OAAO,CAAC,GAAG,EAAE,CAAC;AACxH;AAEA;;;;;;CAMC,GACD,SAAS,eACP,WAAmB,EACnB,OAIC;IAED,6EAA6E;IAC7E,MAAM,qBACJ,SAAS,eAAe,YAAY,QAAQ,UAAU,GACtD,eAAe,uBACX,oBAAoB,CAAC,YAAiD,GACtE,qBAAqB,OAAO;IAElC,kCAAkC;IAClC,MAAM,cAAc,kBAAkB;IACtC,MAAM,YAAY,SAAS,QAAQ;IAEnC,oDAAoD;IACpD,MAAM,YAAY,SAAS,yBAAyB;IAEpD,8CAA8C;IAC9C,IAAI,aAAa,uBAAuB,OAAO;QAC7C,kFAAkF;QAClF,MAAM,WAAW,OAAO,SAAS,yBAAyB,WACtD,QAAQ,oBAAoB,GAC5B,eAAe,gBACb,aAAa,CAAC,YAA0C,GACxD,cAAc,OAAO;QAE3B,uDAAuD;QACvD,OAAO;YACL,YAAY;YACZ,MAAM;YACN,cAAc;gBACZ,iBAAiB,CAAC,gBAAgB,EAAE,mBAAmB,yBAAyB,EAAE,UAAU;YAC9F;QACF;IACF;IAEA,2BAA2B;IAC3B,OAAO;QACL,YAAY;QACZ,MAAM;IACR;AACF;AAEA;;;;;;CAMC,GACD,SAAS,qBACP,OAAU,EACV,OAKC;IAED,6EAA6E;IAC7E,MAAM,YAAY,QAAQ,IAAI,IAAI;QAAC,CAAC,OAAO,EAAE,QAAQ,WAAW,EAAE;KAAC;IAEnE,qDAAqD;IACrD,MAAM,iBAAiB,QAAQ,UAAU,IACpB,oBAAoB,CAAC,QAAQ,WAAW,CAAsC,IAC9E,qBAAqB,OAAO;IAEjD,yCAAyC;IACzC,OAAO,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAClB,OAAO,GAAG;QACR,MAAM,YAAY,uBAAuB,YAAY,GAAG,KAAK;QAC7D,IAAI;YACF,MAAM,SAAS,MAAM,WAAW;YAEhC,+BAA+B;YAC/B,IAAI,wBAAwB,WAAW;gBACrC,MAAM,WAAW,YAAY,GAAG,KAAK;gBACrC,aAAa,YAAY,CAAC,IAAI,CAAC;gBAC/B,aAAa,IAAI;YACnB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,sBAAsB;gBACxB,aAAa,MAAM;YACrB;YACA,MAAM;QACR;IACF,GACA,+CAA+C;IAC/C;QAAC,GAAG,QAAQ,WAAW,EAAE;KAAC,EAC1B,sCAAsC;IACtC;QACE,YAAY;QACZ,MAAM;IACR;AAEJ;AAOO,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE;IACrC,OAAO,oHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC;QACzC,OAAO;QACP,MAAM;YACJ,YAAY;YACZ,MAAM;gBAAC;aAAwB;QACjC;IACF;AACF;AAOO,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IAMxC,mEAAmE;IACnE,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAqB;SAAwB;IACtD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC;QACxC,GAAG,OAAO;QACV,OAAO;QACP,MAAM;IACR;AACF;AAOO,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IAG9C,mEAAmE;IACnE,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAqB;SAA2B;IACzD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,mBAAmB,CAAC;QACrD,GAAG,OAAO;QACV,OAAO;QACP,MAAM;IACR;AACF;AAOO,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IAMzC,mEAAmE;IACnE,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAsB;SAAyB;IACxD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC;QACzC,GAAG,OAAO;QACV,OAAO;QACP,MAAM;IACR;AACF;AAKO,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO,UAQnC,CAAC,CAAC;IACJ,MAAM,cAAc,QAAQ,IAAI,IAAI;QAClC,YAAY;QACZ,MAAM;YAAC;YAAkB,CAAC,KAAK,EAAE,QAAQ,IAAI,IAAI,GAAG;SAAC;IACvD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC;QAAE,GAAG,OAAO;QAAE,MAAM;IAAY;AACzE;AAKO,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO,MAAc;IACxD,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAkB,CAAC,cAAc,EAAE,MAAM;SAAC;IACnD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM;QAAE,MAAM;IAAY;AACtE;AAKO,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IAC7C,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAkB;SAA0B;IACrD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC;QAAE,MAAM;IAAY;AAClE;AAKO,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO,UAQzC,CAAC,CAAC;IACJ,MAAM,cAAc,QAAQ,IAAI,IAAI;QAClC,YAAY;QACZ,MAAM;YAAC;YAAwB,CAAC,KAAK,EAAE,QAAQ,IAAI,IAAI,GAAG;SAAC;IAC7D;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,aAAa,CAAC,MAAM,CAAC;QAAE,GAAG,OAAO;QAAE,MAAM;IAAY;AAC/E;AAKO,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO,MAAc;IAC9D,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAuB,CAAC,oBAAoB,EAAE,MAAM;SAAC;IAC9D;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM;QAAE,MAAM;IAAY;AAC5E;AAKO,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IACnD,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAwB;SAAgC;IACjE;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,aAAa,CAAC,WAAW,CAAC;QAAE,MAAM;IAAY;AACxE;AAKO,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO,UASrC,CAAC,CAAC;IACJ,MAAM,cAAc,QAAQ,IAAI,IAAI;QAClC,YAAY;QACZ,MAAM;YAAC;YAAqB,CAAC,KAAK,EAAE,QAAQ,IAAI,IAAI,GAAG;SAAC;IAC1D;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC;QACpC,GAAG,OAAO;QACV,MAAM,QAAQ,IAAI,IAAI;YAAC;SAAmB;QAC1C,MAAM;IACR;AACF;AAKO,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO,MAAc;IAC1D,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAqB,CAAC,iBAAiB,EAAE,MAAM;SAAC;IACzD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM;QAAE,MAAM;IAAY;AACvE;AAKO,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IAC/C,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAqB;SAA6B;IAC3D;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC;QACpC,SAAS;YAAE,YAAY;gBAAE,KAAK;YAAK;QAAE;QACrC,MAAM;YAAC;SAAmB;QAC1B,YAAY;YAAE,MAAM;YAAG,UAAU;QAAG;QACpC,MAAM;IACR;AACF;AAKO,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IAC5C,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAA0B;SAAoB;IACvD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC;QAAE,MAAM;IAAY;AACjE;AAKO,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IACtC,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAoB;SAAc;IAC3C;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC;QAAE,MAAM;IAAY;AAC3D;AAKO,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IAC1C,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAyB;SAAuB;IACzD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,eAAe,CAAC;QAAE,MAAM;IAAY;AACrE;AAKO,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO,MAAc;IAC3D,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAsB,CAAC,iBAAiB,EAAE,MAAM;SAAC;IAC1D;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM;QAAE,MAAM;IAAY;AAC1E;AAKO,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO,MAAc;IAC3D,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAqB,CAAC,iBAAiB,EAAE,MAAM;SAAC;IACzD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM;QAAE,MAAM;IAAY;AACzE;AAKO,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO;IACxC,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAqB;SAAwB;IACtD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC;QAAE,MAAM;IAAY;AAChE;AAKO,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE,OAAO,MAAc;IAC1D,MAAM,cAAc,SAAS,QAAQ;QACnC,YAAY;QACZ,MAAM;YAAC;YAAqB,CAAC,gBAAgB,EAAE,MAAM;SAAC;IACxD;IACA,OAAO,oHAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM;QAAE,MAAM;IAAY;AACzE", "debugId": null}}, {"offset": {"line": 2844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from 'next';\r\nimport { Inter } from 'next/font/google';\r\nimport './globals.css';\r\nimport { Analytics } from \"@vercel/analytics/next\";\r\nimport { AuthProvider } from '@/contexts/AuthContext';\r\nimport { ErrorProvider } from '@/contexts/ErrorContext';\r\n// Remove direct fetchFromApiServer import, use cached functions instead\r\nimport Layout from '@/components/layout/Layout'; // Import Layout component\r\nimport ReactQueryProvider from '@/providers/QueryProvider'; // Updated import path for React Query Provider\r\nimport { getGlobalSettings, getFooterCategories } from '@/lib/serverCache'; // Import cached functions\r\nimport { GoogleTagManager } from '@next/third-parties/google';\r\n\r\nconst inter = Inter({ subsets: ['latin'] });\r\n\r\n// Define interfaces for Strapi data structures\r\ninterface StrapiMediaAttributes {\r\n  id?: number | string;\r\n  name?: string;\r\n  alternativeText?: string | null;\r\n  caption?: string | null;\r\n  width?: number;\r\n  height?: number;\r\n  formats?: any; // Can be more specific if needed\r\n  hash?: string;\r\n  ext?: string;\r\n  mime?: string;\r\n  size?: number;\r\n  url: string;\r\n  previewUrl?: string | null;\r\n  provider?: string;\r\n  provider_metadata?: any;\r\n  createdAt?: string;\r\n  updatedAt?: string;\r\n  publishedAt?: string | null;\r\n}\r\n\r\ninterface StrapiMedia {\r\n  data?: {\r\n    id: number | string;\r\n    attributes: StrapiMediaAttributes;\r\n  };\r\n  // For v5 flattened media directly in the field\r\n  id?: number | string;\r\n  attributes?: StrapiMediaAttributes; // if it's a component still\r\n  url?: string; // if it's truly flat\r\n  // Add other direct media fields if Strapi v5 flattens them this way\r\n  name?: string;\r\n  alternativeText?: string | null;\r\n  caption?: string | null;\r\n  width?: number;\r\n  height?: number;\r\n  // ... other StrapiMediaAttributes\r\n}\r\n\r\n\r\ninterface GlobalSettingsAttributes {\r\n  siteName?: string;\r\n  defaultSeoDescription?: string;\r\n  favicon?: StrapiMedia; // Can be StrapiMedia or StrapiMediaAttributes if fully flat\r\n  logoLight?: StrapiMedia; // Can be StrapiMedia or StrapiMediaAttributes if fully flat\r\n  // other attributes...\r\n}\r\n\r\ninterface CategoryAttributes {\r\n  name: string;\r\n  slug: string;\r\n  // other attributes...\r\n}\r\n\r\n// Strapi API response types\r\n// T is the type of the attributes object for a single item, or the flat item itself for v5\r\ninterface StrapiSingleResponse<T> {\r\n  data: { id: number | string; attributes: T } | T | null; // Allow null for error cases or empty single types\r\n  meta?: Record<string, any>;\r\n}\r\n\r\ninterface StrapiCollectionResponse<T> {\r\n  data: Array<{ id: number | string; attributes: T } | T> | null; // Allow null for error cases\r\n  meta?: {\r\n    pagination?: {\r\n      page: number;\r\n      pageSize: number;\r\n      pageCount: number;\r\n      total: number;\r\n    };\r\n  };\r\n}\r\n\r\n// Function to generate dynamic metadata\r\nexport async function generateMetadata(): Promise<Metadata> {\r\n  let faviconUrl = '/favicon.ico';\r\n  let siteTitle = 'Natural Healing Now - Holistic Health Directory';\r\n  let siteDescription = 'Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.';\r\n  const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL;\r\n\r\n  try {\r\n    // Fetch global settings using the cached function with longer cache time\r\n    const globalSettingsResponse = await getGlobalSettings();\r\n\r\n    if (globalSettingsResponse?.data) {\r\n      let attributesData: GlobalSettingsAttributes | null = null;\r\n\r\n      // Handle both Strapi v4 and v5 response formats\r\n      if ('attributes' in globalSettingsResponse.data && globalSettingsResponse.data.attributes) {\r\n        attributesData = globalSettingsResponse.data.attributes as GlobalSettingsAttributes;\r\n      } else {\r\n        attributesData = globalSettingsResponse.data as GlobalSettingsAttributes;\r\n      }\r\n\r\n      if (attributesData) {\r\n        // Process favicon data\r\n        const faviconField = attributesData.favicon;\r\n        const faviconActualMedia = faviconField?.data?.attributes ?? faviconField?.attributes ?? faviconField;\r\n\r\n        if (faviconActualMedia?.url) {\r\n          const faviconPath = faviconActualMedia.url;\r\n          if (faviconPath && strapiBaseUrl) {\r\n            faviconUrl = faviconPath.startsWith('/') ? `${strapiBaseUrl}${faviconPath}` : faviconPath;\r\n          }\r\n        }\r\n\r\n        // Set title and description from global settings\r\n        siteTitle = attributesData.siteName || siteTitle;\r\n        siteDescription = attributesData.defaultSeoDescription || siteDescription;\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch global settings for metadata:\", error);\r\n    // Use default values if the fetch fails\r\n  }\r\n\r\n  return {\r\n    title: siteTitle,\r\n    description: siteDescription,\r\n    icons: { icon: faviconUrl, shortcut: faviconUrl, apple: faviconUrl },\r\n  };\r\n}\r\n\r\n// Make the component async to fetch data\r\nexport default async function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  let siteName = 'Natural Healing Now';\r\n  let logoLight = null;\r\n  let footerCategories: any[] = [];\r\n\r\n  try {\r\n    // Fetch global settings and footer categories in parallel using cached functions\r\n    // These functions now use longer cache times (24 hours for global settings, 1 week for categories)\r\n    // and explicit 'force-cache' option for Next.js 15 compatibility\r\n    const [globalSettingsResponse, categoriesResponse] = await Promise.all([\r\n      getGlobalSettings(),\r\n      getFooterCategories()\r\n    ]);\r\n\r\n    // Process global settings\r\n    if (globalSettingsResponse?.data) {\r\n      let attributesData: GlobalSettingsAttributes | null = null;\r\n\r\n      // Handle both Strapi v4 and v5 response formats\r\n      if ('attributes' in globalSettingsResponse.data && globalSettingsResponse.data.attributes) {\r\n        attributesData = globalSettingsResponse.data.attributes as GlobalSettingsAttributes;\r\n      } else {\r\n        attributesData = globalSettingsResponse.data as GlobalSettingsAttributes;\r\n      }\r\n\r\n      if (attributesData) {\r\n        // Set site name from global settings\r\n        siteName = attributesData.siteName || siteName;\r\n\r\n        // Process logo data\r\n        const logoField = attributesData.logoLight;\r\n        const logoActualMedia = logoField?.data?.attributes ?? logoField?.attributes ?? logoField;\r\n\r\n        if (logoActualMedia?.url) {\r\n          const mediaAttrs = logoActualMedia as StrapiMediaAttributes;\r\n\r\n          // Parse ID safely\r\n          let parsedId: number | undefined =\r\n            typeof mediaAttrs.id === 'string' ? parseInt(mediaAttrs.id, 10) :\r\n            typeof mediaAttrs.id === 'number' ? mediaAttrs.id :\r\n            undefined;\r\n\r\n          if (parsedId !== undefined && isNaN(parsedId)) parsedId = undefined;\r\n\r\n          // Create logo object with all required properties\r\n          logoLight = {\r\n            id: parsedId || 0,\r\n            name: mediaAttrs.name || '',\r\n            alternativeText: mediaAttrs.alternativeText || siteName,\r\n            caption: mediaAttrs.caption,\r\n            width: mediaAttrs.width,\r\n            height: mediaAttrs.height,\r\n            formats: mediaAttrs.formats,\r\n            hash: mediaAttrs.hash || '',\r\n            ext: mediaAttrs.ext || '',\r\n            mime: mediaAttrs.mime || '',\r\n            size: mediaAttrs.size || 0,\r\n            url: mediaAttrs.url,\r\n            previewUrl: mediaAttrs.previewUrl,\r\n            provider: mediaAttrs.provider || 'local',\r\n            provider_metadata: mediaAttrs.provider_metadata,\r\n            createdAt: mediaAttrs.createdAt || '',\r\n            updatedAt: mediaAttrs.updatedAt || '',\r\n            publishedAt: mediaAttrs.publishedAt === null ? undefined : mediaAttrs.publishedAt,\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process categories for footer\r\n    if (categoriesResponse?.data && Array.isArray(categoriesResponse.data)) {\r\n      footerCategories = categoriesResponse.data\r\n        .map((catEntry: { id: string | number; attributes: CategoryAttributes } | CategoryAttributes) => {\r\n          // Handle both Strapi v4 and v5 response formats\r\n          let catAttributes: CategoryAttributes;\r\n          let catId: string | number;\r\n\r\n          if ('attributes' in catEntry && catEntry.attributes) {\r\n            catAttributes = catEntry.attributes as CategoryAttributes;\r\n            catId = catEntry.id;\r\n          } else {\r\n            catAttributes = catEntry as CategoryAttributes;\r\n            catId = (catEntry as any).id;\r\n          }\r\n\r\n          return {\r\n            id: catId,\r\n            attributes: {\r\n              name: catAttributes.name || 'Unknown Category',\r\n              slug: catAttributes.slug || 'unknown-category',\r\n            },\r\n          };\r\n        })\r\n        .filter(Boolean);\r\n    } else if (categoriesResponse === null) {\r\n      console.warn(\"Received null for categoriesResponse in RootLayout, likely due to fetch error.\");\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch initial data in RootLayout:\", error);\r\n    // Use default values if the fetch fails\r\n  }\r\n\r\n  return (\r\n    <html lang=\"en\">\r\n      {process.env.NEXT_PUBLIC_GTM_ID && (\r\n        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID} />\r\n      )}\r\n      <body className={inter.className}>\r\n        <ErrorProvider>\r\n          <AuthProvider>\r\n            <ReactQueryProvider>\r\n              {/* Wrap children with Layout and pass fetched data, including footerCategories */}\r\n              <Layout siteName={siteName} logoLight={logoLight} footerCategories={footerCategories}>\r\n                {children}\r\n              </Layout>\r\n            </ReactQueryProvider>\r\n          </AuthProvider>\r\n        </ErrorProvider>\r\n        <Analytics />\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AACA;AACA,wEAAwE;AACxE,2OAAiD,0BAA0B;AAC3E,sOAA4D,+CAA+C;AAC3G,oNAA4E,0BAA0B;AACtG;;;;;;;;;;;AA+EO,eAAe;IACpB,IAAI,aAAa;IACjB,IAAI,YAAY;IAChB,IAAI,kBAAkB;IACtB,MAAM;IAEN,IAAI;QACF,yEAAyE;QACzE,MAAM,yBAAyB,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;QAErD,IAAI,wBAAwB,MAAM;YAChC,IAAI,iBAAkD;YAEtD,gDAAgD;YAChD,IAAI,gBAAgB,uBAAuB,IAAI,IAAI,uBAAuB,IAAI,CAAC,UAAU,EAAE;gBACzF,iBAAiB,uBAAuB,IAAI,CAAC,UAAU;YACzD,OAAO;gBACL,iBAAiB,uBAAuB,IAAI;YAC9C;YAEA,IAAI,gBAAgB;gBAClB,uBAAuB;gBACvB,MAAM,eAAe,eAAe,OAAO;gBAC3C,MAAM,qBAAqB,cAAc,MAAM,cAAc,cAAc,cAAc;gBAEzF,IAAI,oBAAoB,KAAK;oBAC3B,MAAM,cAAc,mBAAmB,GAAG;oBAC1C,IAAI,eAAe,eAAe;wBAChC,aAAa,YAAY,UAAU,CAAC,OAAO,GAAG,gBAAgB,aAAa,GAAG;oBAChF;gBACF;gBAEA,iDAAiD;gBACjD,YAAY,eAAe,QAAQ,IAAI;gBACvC,kBAAkB,eAAe,qBAAqB,IAAI;YAC5D;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;IAC/D,wCAAwC;IAC1C;IAEA,OAAO;QACL,OAAO;QACP,aAAa;QACb,OAAO;YAAE,MAAM;YAAY,UAAU;YAAY,OAAO;QAAW;IACrE;AACF;AAGe,eAAe,WAAW,EACvC,QAAQ,EAGR;IACA,IAAI,WAAW;IACf,IAAI,YAAY;IAChB,IAAI,mBAA0B,EAAE;IAEhC,IAAI;QACF,iFAAiF;QACjF,mGAAmG;QACnG,iEAAiE;QACjE,MAAM,CAAC,wBAAwB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACrE,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;YAChB,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD;SACnB;QAED,0BAA0B;QAC1B,IAAI,wBAAwB,MAAM;YAChC,IAAI,iBAAkD;YAEtD,gDAAgD;YAChD,IAAI,gBAAgB,uBAAuB,IAAI,IAAI,uBAAuB,IAAI,CAAC,UAAU,EAAE;gBACzF,iBAAiB,uBAAuB,IAAI,CAAC,UAAU;YACzD,OAAO;gBACL,iBAAiB,uBAAuB,IAAI;YAC9C;YAEA,IAAI,gBAAgB;gBAClB,qCAAqC;gBACrC,WAAW,eAAe,QAAQ,IAAI;gBAEtC,oBAAoB;gBACpB,MAAM,YAAY,eAAe,SAAS;gBAC1C,MAAM,kBAAkB,WAAW,MAAM,cAAc,WAAW,cAAc;gBAEhF,IAAI,iBAAiB,KAAK;oBACxB,MAAM,aAAa;oBAEnB,kBAAkB;oBAClB,IAAI,WACF,OAAO,WAAW,EAAE,KAAK,WAAW,SAAS,WAAW,EAAE,EAAE,MAC5D,OAAO,WAAW,EAAE,KAAK,WAAW,WAAW,EAAE,GACjD;oBAEF,IAAI,aAAa,aAAa,MAAM,WAAW,WAAW;oBAE1D,kDAAkD;oBAClD,YAAY;wBACV,IAAI,YAAY;wBAChB,MAAM,WAAW,IAAI,IAAI;wBACzB,iBAAiB,WAAW,eAAe,IAAI;wBAC/C,SAAS,WAAW,OAAO;wBAC3B,OAAO,WAAW,KAAK;wBACvB,QAAQ,WAAW,MAAM;wBACzB,SAAS,WAAW,OAAO;wBAC3B,MAAM,WAAW,IAAI,IAAI;wBACzB,KAAK,WAAW,GAAG,IAAI;wBACvB,MAAM,WAAW,IAAI,IAAI;wBACzB,MAAM,WAAW,IAAI,IAAI;wBACzB,KAAK,WAAW,GAAG;wBACnB,YAAY,WAAW,UAAU;wBACjC,UAAU,WAAW,QAAQ,IAAI;wBACjC,mBAAmB,WAAW,iBAAiB;wBAC/C,WAAW,WAAW,SAAS,IAAI;wBACnC,WAAW,WAAW,SAAS,IAAI;wBACnC,aAAa,WAAW,WAAW,KAAK,OAAO,YAAY,WAAW,WAAW;oBACnF;gBACF;YACF;QACF;QAEA,gCAAgC;QAChC,IAAI,oBAAoB,QAAQ,MAAM,OAAO,CAAC,mBAAmB,IAAI,GAAG;YACtE,mBAAmB,mBAAmB,IAAI,CACvC,GAAG,CAAC,CAAC;gBACJ,gDAAgD;gBAChD,IAAI;gBACJ,IAAI;gBAEJ,IAAI,gBAAgB,YAAY,SAAS,UAAU,EAAE;oBACnD,gBAAgB,SAAS,UAAU;oBACnC,QAAQ,SAAS,EAAE;gBACrB,OAAO;oBACL,gBAAgB;oBAChB,QAAQ,AAAC,SAAiB,EAAE;gBAC9B;gBAEA,OAAO;oBACL,IAAI;oBACJ,YAAY;wBACV,MAAM,cAAc,IAAI,IAAI;wBAC5B,MAAM,cAAc,IAAI,IAAI;oBAC9B;gBACF;YACF,GACC,MAAM,CAAC;QACZ,OAAO,IAAI,uBAAuB,MAAM;YACtC,QAAQ,IAAI,CAAC;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;IAC7D,wCAAwC;IAC1C;IAEA,qBACE,8OAAC;QAAK,MAAK;;YACR,QAAQ,GAAG,CAAC,kBAAkB,kBAC7B,8OAAC,qKAAA,CAAA,mBAAgB;gBAAC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;;;;;;0BAEzD,8OAAC;gBAAK,WAAW,yIAAA,CAAA,UAAK,CAAC,SAAS;;kCAC9B,8OAAC,gIAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,+HAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,UAAkB;0CAEjB,cAAA,8OAAC,sIAAA,CAAA,UAAM;oCAAC,UAAU;oCAAU,WAAW;oCAAW,kBAAkB;8CACjE;;;;;;;;;;;;;;;;;;;;;kCAKT,8OAAC,+JAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;AAIlB", "debugId": null}}]}