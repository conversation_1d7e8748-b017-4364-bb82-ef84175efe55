# Node.js
node_modules/
npm-debug.log*
yarn-error.log*
package-lock.json # Often committed, but can be ignored if preferred
yarn.lock # Often committed, but can be ignored if preferred

# Build output
build/
dist/
out/

# Test coverage and results
coverage/
test-results/
playwright-report/

# Log files
logs/
*.log

# Environment variables
.env*
!.env.example
!.env.sample

# Strapi specific
strapi-cms/node_modules/
strapi-cms/.cache/
strapi-cms/build/
strapi-cms/dist/
strapi-cms/.tmp/
strapi-cms/database/data.db* # Ignore SQLite database file if using default
strapi-cms/.env # Ignore environment variables file

# OS specific
.DS_Store
Thumbs.db

# IDE specific
.vscode/
.idea/
