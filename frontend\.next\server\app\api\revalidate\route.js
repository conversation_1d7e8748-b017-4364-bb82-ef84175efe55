(()=>{var e={};e.id=6931,e.ids=[6931],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66936:(e,s,r)=>{"use strict";r.r(s),r.d(s,{patchFetch:()=>v,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var t={};r.r(t),r.d(t,{POST:()=>c});var a=r(96559),i=r(48088),o=r(37719),n=r(32190),l=r(62351);let p=process.env.REVALIDATION_SECRET;async function c(e){let s,r=e.nextUrl.searchParams.get("secret");if(!p)return console.error("REVALIDATION_SECRET is not set. Skipping revalidation."),n.NextResponse.json({error:"Revalidation secret not configured on server"},{status:500});if(r!==p)return console.warn("Invalid revalidation secret received."),n.NextResponse.json({error:"Invalid revalidation secret"},{status:401});try{s=await e.json()}catch(e){return console.error("Failed to parse webhook payload:",e),n.NextResponse.json({error:"Invalid request body"},{status:400})}let{event:t,model:a,entry:i}=s;if(!a||!i)return console.warn("Webhook payload missing model or entry:",s),n.NextResponse.json({error:"Missing model or entry in payload"},{status:400});console.log(`Received webhook: event='${t}', model='${a}', id='${i.id}', slug='${i.slug||"N/A"}'`);try{let e=[],s=[`strapi-${a}`];switch(a){case"clinic":i.slug&&(e.push(`/clinics/${i.slug}`),s.push(`strapi-clinic-${i.slug}`)),s.push("strapi-clinics"),s.push("strapi-featured-clinics"),i.categories&&Array.isArray(i.categories)&&i.categories.forEach(r=>{r.slug&&(s.push(`strapi-clinics-category-${r.slug}`),e.push(`/categories/${r.slug}`))});break;case"practitioner":i.slug&&(e.push(`/practitioners/${i.slug}`),s.push(`strapi-practitioner-${i.slug}`)),s.push("strapi-practitioners"),i.clinic?.slug&&(e.push(`/clinics/${i.clinic.slug}`),s.push(`strapi-clinic-${i.clinic.slug}`));break;case"category":i.slug&&(e.push(`/categories/${i.slug}`),s.push(`strapi-category-${i.slug}`),s.push(`strapi-clinics-category-${i.slug}`)),s.push("strapi-categories"),s.push("strapi-global");break;case"specialty":i.slug&&(e.push(`/specialties/${i.slug}`),s.push(`strapi-specialty-${i.slug}`),s.push(`strapi-clinics-specialty-${i.slug}`)),s.push("strapi-specialties"),s.push("strapi-global");break;case"condition":i.slug&&(e.push(`/conditions/${i.slug}`),s.push(`strapi-condition-${i.slug}`),s.push(`strapi-clinics-condition-${i.slug}`)),s.push("strapi-conditions"),s.push("strapi-global");break;case"service":s.push("strapi-services");break;case"setting":s.push("strapi-settings"),s.push("strapi-global"),e.push("/");break;default:console.warn(`No specific revalidation logic for model: ${a}. Revalidating general tags.`),s.push("strapi-global"),e.push("/")}let r=0,o=0,p=[...new Set(e)],c=[...new Set(s)];for(let e of p)console.log(`Revalidating path: ${e}`),await (0,l.revalidatePath)(e),r++;for(let e of c)console.log(`Revalidating tag: ${e}`),await (0,l.revalidateTag)(e),o++;return console.log(`Revalidation complete. Paths: ${r}, Tags: ${o}.`),n.NextResponse.json({revalidated:!0,timestamp:new Date().toISOString(),model:a,event:t,entryId:i.id,revalidatedPaths:p,revalidatedTags:c})}catch(s){let e=s instanceof Error?s.message:"Unknown revalidation error";return console.error("Error during revalidation process:",e,s),n.NextResponse.json({error:"Revalidation failed",details:e},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/revalidate/route",pathname:"/api/revalidate",filename:"route",bundlePath:"app/api/revalidate/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\revalidate\\route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:h}=u;function v(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},78335:()=>{},96487:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[7719,6391,580],()=>r(66936));module.exports=t})();