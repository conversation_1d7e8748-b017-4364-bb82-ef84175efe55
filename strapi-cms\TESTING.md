# Strapi Testing Guide

This guide explains how to run tests for the Strapi backend using real data.

## Prerequisites

Before running tests, make sure you have:

1. A running Strapi instance (local or remote)
2. Installed all dependencies with `npm install`

## Configuration

The tests are configured to use your real Strapi data. You can customize the test environment by editing the `.env.test` file:

```
# Test environment configuration
TEST_API_URL=http://localhost:1337
TEST_WITH_REAL_DATA=true

# Credentials for authenticated tests (replace with your test user credentials)
STRAPI_TEST_USERNAME=your-test-username
STRAPI_TEST_PASSWORD=your-test-password
```

## Running Tests

### Running Tests with Real Data

To run tests that use your real Strapi data:

```bash
npm run test:real
```

This command will run the tests in `tests/integration/real-data.test.js` which connect to your actual Strapi instance and fetch real data.

### Running All Tests

To run all tests:

```bash
npm test
```

## Test Structure

The tests are organized as follows:

- `tests/helpers/`: Helper functions and setup files
- `tests/integration/`: Integration tests for API endpoints

## Writing New Tests

When writing new tests that use real data:

1. Use the `strapiHelpers` module to interact with the API
2. Use the `(runTests ? it : it.skip)` pattern to conditionally run tests based on the environment
3. Be careful with tests that modify data - prefer read-only tests when possible

Example:

```javascript
const strapiHelpers = require('../helpers/strapi');

describe('My Test Suite', () => {
  const runTests = process.env.TEST_WITH_REAL_DATA === 'true';
  
  (runTests ? it : it.skip)('should fetch data from the API', async () => {
    const result = await strapiHelpers.getEntries('my-collection');
    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
  });
});
```

## Troubleshooting

If you encounter issues:

1. Make sure your Strapi instance is running and accessible
2. Check that the API URL in `.env.test` is correct
3. Verify that your test credentials have the necessary permissions
4. Look for error messages in the test output for more details
