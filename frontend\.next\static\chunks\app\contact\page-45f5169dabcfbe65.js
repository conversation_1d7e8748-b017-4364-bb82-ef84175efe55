(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{2528:(e,r,t)=>{Promise.resolve().then(t.bind(t,7032))},4436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>d});var s=t(2115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=s.createContext&&s.createContext(a),n=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e}).apply(this,arguments)}function i(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,s)}return t}function o(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?i(Object(t),!0).forEach(function(r){var s,a,l;s=e,a=r,l=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var s=t.call(e,r||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in s?Object.defineProperty(s,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):s[a]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(e){return r=>s.createElement(m,c({attr:o({},e.attr)},r),function e(r){return r&&r.map((r,t)=>s.createElement(r.tag,o({key:t},r.attr),e(r.child)))}(e.child))}function m(e){var r=r=>{var t,{attr:a,size:l,title:i}=e,d=function(e,r){if(null==e)return{};var t,s,a=function(e,r){if(null==e)return{};var t={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(r.indexOf(s)>=0)continue;t[s]=e[s]}return t}(e,r);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(s=0;s<l.length;s++)t=l[s],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,n),m=l||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),s.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,d,{className:t,style:o(o({color:e.color||r.color},r.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),i&&s.createElement("title",null,i),e.children)};return void 0!==l?s.createElement(l.Consumer,null,e=>r(e)):r(a)}},4477:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{callServer:function(){return s.callServer},createServerReference:function(){return l},findSourceMapURL:function(){return a.findSourceMapURL}});let s=t(3806),a=t(1818),l=t(4979).createServerReference},7032:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(5155),a=t(351),l=t(7650),n=t(2115),c=t(4477);let i=(0,c.createServerReference)("60044a3effd7f316be8be02ac22417c09ce9a8362d",c.callServer,void 0,c.findSourceMapURL,"sendContactMessage");var o=t(1890);let d={message:"",success:!1,errors:void 0};function m(){let{pending:e}=(0,l.useFormStatus)();return(0,s.jsxs)("button",{type:"submit","aria-disabled":e,disabled:e,className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold flex items-center disabled:opacity-70 disabled:cursor-not-allowed",children:[(0,s.jsx)(a.kGk,{className:"mr-2"}),e?"Sending...":"Send Message"]})}function u(){var e,r,t,c;let[u,x]=(0,l.useFormState)(i,d),f=(0,n.useRef)(null),b={email:"<EMAIL>"};return(0,n.useEffect)(()=>{if(u.success){var e;null==(e=f.current)||e.reset()}},[u.success]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Contact Us"}),(0,s.jsx)("p",{className:"text-lg max-w-3xl mx-auto",children:"Have questions about natural healing options? We're here to help you find the right resources for your wellness journey."})]})}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-12",children:[(0,s.jsxs)("div",{className:"w-full max-w-xl",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:"Send Us a Message"}),u.message&&!u.success&&(0,s.jsx)("div",{className:"mb-4 p-3 rounded-md bg-red-100 text-red-700 text-sm",children:u.message}),u.message&&u.success&&(0,s.jsx)("div",{className:"mb-4 p-3 rounded-md bg-green-100 text-green-700 text-sm",children:u.message}),(0,s.jsxs)("form",{ref:f,action:x,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Your Name"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"John Doe"}),(null==(e=u.errors)?void 0:e.name)&&(0,s.jsx)("p",{className:"mt-1 text-xs text-red-600",children:u.errors.name.join(", ")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"<EMAIL>"}),(null==(r=u.errors)?void 0:r.email)&&(0,s.jsx)("p",{className:"mt-1 text-xs text-red-600",children:u.errors.email.join(", ")})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-1",children:"Subject"}),(0,s.jsx)("input",{type:"text",id:"subject",name:"subject",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"How can we help you?"}),(null==(t=u.errors)?void 0:t.subject)&&(0,s.jsx)("p",{className:"mt-1 text-xs text-red-600",children:u.errors.subject.join(", ")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,s.jsx)("textarea",{id:"message",name:"message",rows:6,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"Your message here..."}),(null==(c=u.errors)?void 0:c.message)&&(0,s.jsx)("p",{className:"mt-1 text-xs text-red-600",children:u.errors.message.join(", ")})]}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(m,{})})]})]}),(0,s.jsx)("div",{className:"w-full max-w-xl text-center",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-8",children:[(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:"For direct inquiries, you can also reach us at:"}),(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"h-12 w-12 bg-emerald-100 rounded-lg flex items-center justify-center",children:(0,s.jsx)(a.pHD,{className:"h-6 w-6 text-emerald-600"})})}),(0,s.jsx)("div",{className:"ml-4",children:(0,s.jsx)("p",{className:"mt-0 text-gray-600 text-lg",children:(0,s.jsx)("a",{href:"mailto:".concat(o.env.NEXT_PUBLIC_CONTACT_EMAIL||b.email),className:"hover:text-emerald-600 font-medium",children:o.env.NEXT_PUBLIC_CONTACT_EMAIL||b.email})})})]})})]})})]})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[844,8441,1684,7358],()=>r(2528)),_N_E=e.O()}]);