/**
 * Test script for the revalidation API
 *
 * This script sends a test request to the revalidation API to verify that it's working correctly.
 *
 * Usage:
 * node scripts/test-revalidation.js
 */

// Use native fetch in Node.js 18+
// If running on older Node.js versions, uncomment the line below:
// const fetch = require('node-fetch');
require('dotenv').config();

// Get the revalidation token from environment variables
const token = process.env.PREVIEW_SECRET || process.env.REVALIDATE_TOKEN;
if (!token) {
  console.error('Error: No revalidation token found in environment variables.');
  console.error('Please set PREVIEW_SECRET or REVALIDATE_TOKEN in your .env file.');
  process.exit(1);
}

// Get the site URL from environment variables or use a default
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Construct the revalidation API URL
const revalidateUrl = `${siteUrl}/api/revalidate`;

// Log the URL being used
console.log(`Using site URL: ${siteUrl}`);
console.log(`Revalidation API URL: ${revalidateUrl}`);
console.log(`Revalidation token: ${token ? token.substring(0, 5) + '...' : 'Not found'}`);

// Check if we're using the correct token
if (token !== '3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=') {
  console.warn('Warning: The token in your environment variables does not match the expected token.');
  console.warn('Expected: 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=');
  console.warn('Actual: ' + token);
  console.warn('This may cause authentication failures when testing the revalidation API.');
}

// Create test payloads for different scenarios
const testPayloads = [
  // Test 1: Basic revalidation with token in body
  {
    name: 'Basic revalidation (token in body)',
    payload: {
      token,
      contentType: 'test',
      paths: ['/']
    },
    headers: {
      'Content-Type': 'application/json'
    }
  },

  // Test 2: Revalidation with token in x-revalidate-token header (legacy)
  {
    name: 'Revalidation with token in x-revalidate-token header',
    payload: {
      contentType: 'test',
      paths: ['/']
    },
    headers: {
      'Content-Type': 'application/json',
      'x-revalidate-token': token
    }
  },

  // Test 3: Revalidation with token in Authorization header (Strapi standard)
  {
    name: 'Revalidation with token in Authorization header',
    payload: {
      contentType: 'test',
      paths: ['/']
    },
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  },

  // Test 4: Revalidate blog posts
  {
    name: 'Revalidate blog posts',
    payload: {
      token,
      contentType: 'blog-post'
    },
    headers: {
      'Content-Type': 'application/json'
    }
  },

  // Test 5: Revalidate practitioners
  {
    name: 'Revalidate practitioners',
    payload: {
      token,
      contentType: 'practitioner'
    },
    headers: {
      'Content-Type': 'application/json'
    }
  },

  // Test 6: Strapi webhook payload format for blog post
  {
    name: 'Strapi webhook payload format (blog post)',
    payload: {
      event: 'entry.update',
      model: 'api::blog-post.blog-post',
      entry: {
        id: 123,
        slug: 'test-blog-post'
      }
    },
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  },

  // Test 7: Strapi webhook payload format for practitioner
  {
    name: 'Strapi webhook payload format (practitioner)',
    payload: {
      event: 'entry.update',
      model: 'api::practitioner.practitioner',
      entry: {
        id: 456,
        slug: 'test-practitioner'
      }
    },
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  },

  // Test 8: Strapi webhook payload format for clinic
  {
    name: 'Strapi webhook payload format (clinic)',
    payload: {
      event: 'entry.update',
      model: 'api::clinic.clinic',
      entry: {
        id: 789,
        slug: 'test-clinic'
      }
    },
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  },

  // Test 9: Strapi webhook payload format for category
  {
    name: 'Strapi webhook payload format (category)',
    payload: {
      event: 'entry.update',
      model: 'api::category.category',
      entry: {
        id: 101,
        slug: 'test-category'
      }
    },
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  },

  // Test 10: Strapi webhook payload format for specialty
  {
    name: 'Strapi webhook payload format (specialty)',
    payload: {
      event: 'entry.update',
      model: 'api::specialty.specialty',
      entry: {
        id: 202,
        slug: 'test-specialty'
      }
    },
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  }
];

// Function to test a single payload
async function testPayload(test) {
  console.log(`\n🧪 Running test: ${test.name}`);
  console.log(`URL: ${revalidateUrl}`);
  console.log('Headers:', JSON.stringify(test.headers, null, 2));
  console.log('Payload:', JSON.stringify(test.payload, null, 2));

  try {
    const response = await fetch(revalidateUrl, {
      method: 'POST',
      headers: test.headers,
      body: JSON.stringify(test.payload),
    });

    const data = await response.json();

    if (response.ok) {
      console.log('✅ Test passed!');
      console.log('Status:', response.status);
      console.log('Response:', JSON.stringify(data, null, 2));
    } else {
      console.error('❌ Test failed!');
      console.error('Status:', response.status);
      console.error('Response:', JSON.stringify(data, null, 2));
    }

    return response.ok;
  } catch (error) {
    console.error('❌ Error testing revalidation API:', error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Testing revalidation API...');

  let passedTests = 0;

  for (const test of testPayloads) {
    const passed = await testPayload(test);
    if (passed) passedTests++;
  }

  console.log(`\n📊 Test results: ${passedTests}/${testPayloads.length} tests passed`);
}

runTests();
