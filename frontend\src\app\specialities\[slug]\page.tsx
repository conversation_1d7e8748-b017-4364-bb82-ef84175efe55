import Link from 'next/link';
import { Fi<PERSON>apPin, FiFilter } from 'react-icons/fi';
import { getStrapiContent } from '@/lib/strapi';
import { notFound } from 'next/navigation';
import ExploreFurther from '@/components/shared/ExploreFurther';
import SearchInput from '@/components/shared/SearchInput';
import { Metadata } from 'next';
import { getOgImageUrl } from '@/lib/mediaUtils';
import TabSwitcher from '@/components/shared/TabSwitcher';
import TabContent from '@/components/shared/TabContent';

// Force static rendering for this route segment
export const dynamic = 'force-static';

// Enable ISR with both time-based and on-demand revalidation
// Use a 12-hour revalidation period
export const revalidate = 43200; // 12 hours in seconds

// Allow dynamic params to be generated on-demand for any new specialties
// created between builds
export const dynamicParams = true;

// Define a simple type for items with a slug
interface SlugItem {
  slug: string;
  // Add other properties if they exist and are relevant
}

// Generate static paths for all specialties at build time
export async function generateStaticParams() {
  try {
    // In Next.js 15, explicitly opt-in to caching
    const response = await getStrapiContent.specialties.getAllSlugs({
      cache: 'force-cache',
      next: {
        revalidate: 43200, // Revalidate slugs every 12 hours
        tags: ['strapi-specialties-slugs']
      }
    });

    if (response && response.data && Array.isArray(response.data)) {
      console.log(`Pre-rendering ${response.data.length} specialty detail pages`);

      return response.data
        .filter((item: SlugItem | null): item is SlugItem => item !== null && typeof item.slug === 'string')
        .map((item: SlugItem) => ({
          slug: item.slug,
        }));
    }

    return [];
  } catch (error) {
    console.error('Error fetching specialty slugs for generateStaticParams:', error);
    return []; // Return empty array on error to prevent build failure
  }
}

// Get Strapi URL from environment variables with fallback
const STRAPI_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_STRAPI_API_URL;

// Define the site URL from environment variable with proper fallback to your actual domain
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log the URLs being used for debugging
if (process.env.NODE_ENV === 'production') {
  console.log("Using STRAPI_URL:", STRAPI_URL || "Not set");
  console.log("Using site URL for canonical URLs:", SITE_URL);
}

// Helper to create absolute URL for Strapi assets - enhanced for Strapi v5 and better debugging
const getStrapiMediaUrl = (urlOrObject: any): string | null => {
  console.log("getStrapiMediaUrl input:", JSON.stringify({
    type: typeof urlOrObject,
    isNull: urlOrObject === null,
    isUndefined: urlOrObject === undefined,
    value: urlOrObject
  }, null, 2));

  // If null/undefined, return null
  if (!urlOrObject) return null;

  // If it's a string URL
  if (typeof urlOrObject === 'string') {
    console.log("URL is a string:", urlOrObject);
    // If already absolute, return as is
    if (urlOrObject.startsWith('http://') || urlOrObject.startsWith('https://')) {
      return urlOrObject;
    }
    // Otherwise, prepend Strapi URL
    return `${STRAPI_URL}${urlOrObject}`;
  }

  // Handle Strapi v5 media structure with nested data
  if (urlOrObject && typeof urlOrObject === 'object') {
    console.log("URL is an object with keys:", Object.keys(urlOrObject));

    // Different ways the URL might be stored in Strapi v5
    const url =
      urlOrObject.url || // Direct url property
      urlOrObject.data?.attributes?.url || // Nested in data.attributes (v4 style)
      urlOrObject.data?.url || // Nested in data
      null;

    if (url) {
      console.log("Extracted URL from object:", url);
      // Check if it's already absolute
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      // Otherwise, prepend Strapi URL
      return `${STRAPI_URL}${url}`;
    }
  }

  console.warn("Could not extract URL from:", urlOrObject);
  return null;
};

// --- Type Definitions ---

// Raw Strapi types (adjust based on actual API response)
interface RawStrapiMedia {
  url?: string;
  // Add other potential media fields if needed (e.g., alternativeText)
}

// Reusable Address type
interface RawStrapiAddress {
  city: string;
  stateProvince: string;
  // Add other address fields if needed
}

interface RawStrapiContactInfo {
  phoneNumber?: string;
  websiteUrl?: string;
}

// Clinic and Practitioner types remain the same
interface RawStrapiClinic {
  id: string;
  documentId: string; // Assuming this might exist from Strapi
  name: string;
  slug: string;
  description?: string | null;
  logo?: RawStrapiMedia | null;
  featuredImage?: RawStrapiMedia | null;
  address?: RawStrapiAddress | null;
  contactInfo?: RawStrapiContactInfo | null;
  isVerified?: boolean; // Add isVerified
}

interface RawStrapiPractitioner {
  id: string;
  documentId: string; // Assuming this might exist from Strapi
  name: string;
  slug: string;
  title?: string | null;
  qualifications?: string | null;
  profilePicture?: RawStrapiMedia | null; // Assuming a profile picture field
  address?: RawStrapiAddress | null; // Add address field
  isVerified?: boolean; // Add isVerified
  bio?: string | null; // Add bio field
  // Add other practitioner fields if needed
}

// Define the structure for a Specialty based on Strapi API
interface RawStrapiSpecialty {
  id: string;
  documentId: string; // Assuming this might exist from Strapi
  name: string;
  slug: string;
  description?: string | null;
  featuredImage?: RawStrapiMedia | null;
  clinics?: RawStrapiClinic[]; // Assuming specialties can be linked to clinics
  practitioners?: RawStrapiPractitioner[]; // Assuming specialties can be linked to practitioners
  // Add other specialty-specific fields if they exist in Strapi
}

// Transformed types for components
interface TransformedClinic {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  logo: string | null;
  featuredImage: string | null;
  address: {
    city: string;
    stateProvince: string;
  };
  contactInfo?: {
    phoneNumber?: string;
    websiteUrl?: string;
  } | null;
  isVerified?: boolean; // Add isVerified
}

interface TransformedPractitioner {
  id: string;
  name: string;
  slug: string;
  title?: string | null;
  qualifications?: string | null;
  profilePicture: string | null;
  isVerified?: boolean; // Add isVerified
  bio?: string | null; // Add bio field
}

// Define the possible tab types to avoid TypeScript comparison errors
type TabType = 'clinics' | 'practitioners';

// --- Data Fetching & Transformation ---

// Fetch specialty data with enhanced error handling and logging
async function getSpecialtyData(slug: string): Promise<RawStrapiSpecialty | null> {
  try {
    console.log(`Fetching specialty data for slug: ${slug}`);

    // Use a cache-first approach with the same revalidation period as the page
    // Specify minimal population to avoid overly complex queries causing 400 errors.
    // Clinics and Practitioners for this specialty will be fetched separately later in the component.
    const response = await getStrapiContent.specialties.getBySlug(slug, {
      populate: { // Specify what to populate for the specialty itself
        featuredImage: true,
        seo: { // If SEO is a component, you might need to populate its fields
          populate: '*' // Or specify fields: ['metaTitle', 'metaDescription', 'metaImage', etc.]
        }
        // IMPORTANT: Explicitly DO NOT populate 'clinics' or 'practitioners' here
      },
      next: {
        tags: ['strapi-specialties-list', `strapi-specialty-${slug}`],
        revalidate: 43200 // 12 hours to match the page-level setting
      },
      cache: 'force-cache' // Explicitly opt-in to caching for Next.js 15
    });

    console.log("Specialty API response structure:", JSON.stringify({
      hasData: !!response,
      hasDataProperty: !!response?.data,
      isDataArray: Array.isArray(response?.data),
      dataLength: Array.isArray(response?.data) ? response.data.length : 'not an array',
      firstItemHasId: Array.isArray(response?.data) && response.data.length > 0 ? !!response.data[0].id : 'no items'
    }, null, 2));

    // Log the first item to understand its structure
    if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
      const firstItem = response.data[0];
      console.log("First specialty item structure:", JSON.stringify({
        id: firstItem.id,
        hasAttributes: !!firstItem.attributes,
        attributesKeys: firstItem.attributes ? Object.keys(firstItem.attributes) : [],
        hasSlug: !!firstItem.attributes?.slug,
        slug: firstItem.attributes?.slug || 'no slug',
        // Check for direct properties too
        hasDirectName: !!firstItem.name,
        hasDirectSlug: !!firstItem.slug,
        directName: firstItem.name || 'no direct name',
        directSlug: firstItem.slug || 'no direct slug'
      }, null, 2));

      console.log("Complete first specialty item:", JSON.stringify(firstItem, null, 2));
    }

    // Handle different response structures
    if (response?.data) {
      if (Array.isArray(response.data) && response.data.length > 0) {
        // Strapi v5 returns data directly in an array when filtering
        const specialtyData = response.data[0];

        // Check if we have a valid specialty with required fields
        if (specialtyData && specialtyData.id) {
          // Create a normalized specialty object that combines direct properties and attributes
          const attributes = specialtyData.attributes || {};

          // Extract name with fallbacks
          const name = attributes.name || specialtyData.name || 'Unnamed Specialty';

          // Use the URL slug as the default
          let finalSlug = slug;

          // Try to get the slug from the data if available
          if (attributes.slug) {
            finalSlug = attributes.slug;
          } else if (specialtyData.slug) {
            finalSlug = specialtyData.slug;
          }

          // If we still don't have a slug, generate one from the name
          if (!finalSlug || finalSlug === 'no slug') {
            if (name && name !== 'Unnamed Specialty') {
              finalSlug = name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
            } else {
              // Last resort: use the ID
              finalSlug = `specialty-${specialtyData.id}`;
            }
          }

          const normalizedSpecialty: RawStrapiSpecialty = {
            id: specialtyData.id.toString(),
            documentId: specialtyData.documentId || attributes.documentId || '',
            name: name,
            slug: finalSlug,
            description: attributes.description || specialtyData.description || '',
            featuredImage: attributes.featuredImage || specialtyData.featuredImage || null,
            clinics: [], // Will be populated separately
            practitioners: [] // Will be populated separately
          };

          console.log(`Successfully processed specialty: ${normalizedSpecialty.name} with slug: ${normalizedSpecialty.slug}`);
          return normalizedSpecialty;
        }
      } else if (!Array.isArray(response.data) && response.data.id) {
        // Handle case where data is not an array but a direct object
        const specialtyData = response.data;
        const attributes = specialtyData.attributes || {};

        // Extract name with fallbacks
        const name = attributes.name || specialtyData.name || 'Unnamed Specialty';

        // Use the URL slug as the default
        let finalSlug = slug;

        // Try to get the slug from the data if available
        if (attributes.slug) {
          finalSlug = attributes.slug;
        } else if (specialtyData.slug) {
          finalSlug = specialtyData.slug;
        }

        // If we still don't have a slug, generate one from the name
        if (!finalSlug || finalSlug === 'no slug') {
          if (name && name !== 'Unnamed Specialty') {
            finalSlug = name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
          } else {
            // Last resort: use the ID
            finalSlug = `specialty-${specialtyData.id}`;
          }
        }

        const normalizedSpecialty: RawStrapiSpecialty = {
          id: specialtyData.id.toString(),
          documentId: specialtyData.documentId || attributes.documentId || '',
          name: name,
          slug: finalSlug,
          description: attributes.description || specialtyData.description || '',
          featuredImage: attributes.featuredImage || specialtyData.featuredImage || null,
          clinics: [],
          practitioners: []
        };

        console.log(`Successfully processed specialty: ${normalizedSpecialty.name} with slug: ${normalizedSpecialty.slug}`);
        return normalizedSpecialty;
      }
    }

    // If we couldn't find a specialty with the given slug, create a fallback one
    // This is a last resort to prevent the page from crashing
    console.warn(`No valid specialty data found for slug: ${slug}, creating fallback`);
    return {
      id: `fallback-${Date.now()}`,
      documentId: '',
      name: slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), // Convert slug to title case
      slug: slug,
      featuredImage: null,
      clinics: [],
      practitioners: []
    };
  } catch (error) {
    console.error(`Error fetching specialty with slug ${slug}:`, error);

    // Create a fallback specialty to prevent the page from crashing
    return {
      id: `error-${Date.now()}`,
      documentId: '',
      name: slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), // Convert slug to title case
      slug: slug,
      featuredImage: null,
      clinics: [],
      practitioners: []
    };
  }
}

// transformClinicData and transformPractitionerData remain the same as they process clinic/practitioner data
function transformClinicData(rawClinic: RawStrapiClinic): TransformedClinic | null {
  // More detailed logging to understand the structure of rawClinic
  console.log("Raw clinic data structure:", JSON.stringify({
    hasData: !!rawClinic,
    id: rawClinic?.id,
    name: rawClinic?.name,
    keys: rawClinic ? Object.keys(rawClinic) : []
  }, null, 2));

  if (!rawClinic) {
    console.warn(`Skipping clinic due to missing data.`);
    return null;
  }

  // Check for essential fields like id and name
  if (!rawClinic.id || !rawClinic.name) {
    console.warn(`Skipping invalid clinic data: ID ${rawClinic?.id}`);
    return null;
  }

  // Process image URLs - Pass the whole object to the helper
  const logoUrl = getStrapiMediaUrl(rawClinic.logo);
  const featuredImageUrl = getStrapiMediaUrl(rawClinic.featuredImage);

  return {
    id: String(rawClinic.id),
    name: rawClinic.name,
    slug: rawClinic.slug || `clinic-${rawClinic.id}`,
    description: rawClinic.description,
    logo: logoUrl,
    featuredImage: featuredImageUrl,
    address: rawClinic.address || { city: 'Unknown', stateProvince: 'N/A' },
    contactInfo: rawClinic.contactInfo,
    isVerified: rawClinic.isVerified || false, // Pass through isVerified
  };
}

function transformPractitionerData(rawPractitioner: RawStrapiPractitioner): TransformedPractitioner | null {
  // More detailed logging to understand the structure of rawPractitioner
  console.log("Raw practitioner data structure:", JSON.stringify({
    hasData: !!rawPractitioner,
    id: rawPractitioner?.id,
    name: rawPractitioner?.name,
    keys: rawPractitioner ? Object.keys(rawPractitioner) : []
  }, null, 2));

  if (!rawPractitioner) {
    console.warn(`Skipping practitioner due to missing data.`);
    return null;
  }

  // Check for essential fields like id and name
  if (!rawPractitioner.id || !rawPractitioner.name) {
    console.warn(`Skipping invalid practitioner data: ID ${rawPractitioner?.id}`);
    return null;
  }

  return {
    id: String(rawPractitioner.id),
    name: rawPractitioner.name,
    slug: rawPractitioner.slug || `practitioner-${rawPractitioner.id}`,
    title: rawPractitioner.title,
    qualifications: rawPractitioner.qualifications,
    profilePicture: getStrapiMediaUrl(rawPractitioner.profilePicture),
    isVerified: rawPractitioner.isVerified || false,
    bio: rawPractitioner.bio, // Add bio field
  };
}

// --- Page Component ---

interface PageParams {
  slug: string;
}

// Generate Metadata
export async function generateMetadata({ params }: { params: PageParams }): Promise<Metadata> {
  const awaitedParams = await params;
  const specialtyData = await getSpecialtyData(awaitedParams.slug); // Renamed for clarity

  if (!specialtyData) {
    return {
      title: 'Specialty Not Found | Natural Healing Now',
      description: 'The requested specialty could not be found.',
      // No canonical for not found pages
    };
  }

  // Extract SEO data from the fetched specialty data
  // The type RawStrapiSpecialty suggests fields are direct properties, but API might nest them.
  // Let's try accessing attributes safely, assuming the API returns { id: ..., attributes: { ... } } structure despite the type assertion.
  const attributes = (specialtyData as any)?.attributes || specialtyData || {}; // Try accessing attributes, fallback to specialtyData itself
  const seo = attributes.seo; // Access seo from attributes or the main object

  const defaultTitle = `${attributes.name || specialtyData.name || 'Specialty'} | Natural Healing Now`; // Check both levels
  const defaultDescription = attributes.description || specialtyData.description || `Learn about ${attributes.name || specialtyData.name || 'this specialty'} and find related clinics and practitioners.`; // Check both levels

  const metaTitle = seo?.metaTitle || defaultTitle;
  const metaDescription = seo?.metaDescription || defaultDescription;
  const metaImageUrl = getStrapiMediaUrl(seo?.metaImage); // Use helper for image URL

  const canonicalPath = `/specialities/${specialtyData.slug}`;
  const canonicalUrl = seo?.canonicalURL || (SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath);

  // Prepare Open Graph data
  let ogImageUrl: string | undefined = undefined;

  // First try to get image from SEO plugin's openGraph data
  if (seo?.openGraph?.image) {
    ogImageUrl = getOgImageUrl(getStrapiMediaUrl(seo.openGraph.image));
  } else if (seo?.metaImage) {
    ogImageUrl = getOgImageUrl(metaImageUrl); // Fallback to metaImage
  }

  // Log the image URL in production for debugging
  if (process.env.NODE_ENV === 'production') {
    console.log('Specialty og:image:', {
      openGraphImage: seo?.openGraph?.image,
      metaImage: seo?.metaImage,
      ogImageUrl
    });
  }

  // Use openGraph data from SEO plugin if available
  const openGraphData: any = seo?.openGraph ? {
    title: seo.openGraph.title || metaTitle,
    description: seo.openGraph.description || metaDescription,
    url: seo.openGraph.url || canonicalUrl,
    type: seo.openGraph.type || 'website',
    siteName: seo.openGraph.siteName || 'Natural Healing Now',
    ...(ogImageUrl && { images: [{ url: ogImageUrl }] }),
  } : {
    // Fallback if no openGraph data in SEO plugin
    title: metaTitle,
    description: metaDescription,
    url: canonicalUrl,
    type: 'website',
    siteName: 'Natural Healing Now',
    ...(ogImageUrl && { images: [{ url: ogImageUrl }] }),
  };

  return {
    title: metaTitle,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: openGraphData,
    twitter: {
      card: 'summary_large_image',
      title: seo?.openGraph?.title || metaTitle,
      description: seo?.openGraph?.description || metaDescription,
      images: ogImageUrl ? [ogImageUrl] : [],
    },
  };
}

// Define the interface for the searchParams object, adding query, location, and page
interface SpecialtyPageSearchParams {
  tab?: string;
  query?: string;
  location?: string; // Add location param
  page?: string;
}

// Main Page Component - Renamed to SpecialtyDetailPage
export default async function SpecialtyDetailPage({
  params,
  searchParams
}: {
  params: PageParams;
  searchParams: SpecialtyPageSearchParams; // Use updated interface
}) {
  // Await searchParams before destructuring
  const awaitedSearchParams = await searchParams;

  // Destructure the awaited searchParams
  const {
    query = '',
    location = '',
    page: pageParam, // Rename to avoid conflict with page variable
    tab: rawTabValue = 'clinics'
  } = awaitedSearchParams;

  const awaitedParams = await params; // Keep this if needed for Next.js version
  const slug = awaitedParams.slug;
  const currentPage = Number(pageParam) || 1; // Use the destructured pageParam
  const activeTab: TabType = (rawTabValue === 'practitioners') ? 'practitioners' : 'clinics';

  // 1. Fetch only basic specialty details first
  const specialty = await getSpecialtyData(slug);

  if (!specialty) {
    notFound(); // Show 404 if specialty data is not found
  }

  // Destructure specialty data
  const {
    name = 'Unnamed Specialty',
    description = null, // Changed default to null
    featuredImage = null,
    // clinics and practitioners will be fetched based on active tab and filters
  } = specialty;

  const specialtyImageUrl = getStrapiMediaUrl(featuredImage);

  // 2. Fetch counts for both clinics and practitioners, and detailed data for both tabs
  let clinicCount = 0;
  let practitionerCount = 0;
  let totalPages = 1;

  // Determine caching strategy based on search parameters
  const hasFilters = query || location;
  const fetchOptions = hasFilters
    ? { cache: 'no-store' } // Don't cache filtered results
    : {
        next: {
          tags: ['strapi-specialties-list', `strapi-specialty-${slug}`],
          revalidate: 43200 // 12 hours
        },
        cache: 'force-cache'
      };

  // Fetch counts first
  try {
    const clinicCountResponse = await getStrapiContent.clinics.getAll({
      specialtySlug: slug,
      pagination: { page: 1, pageSize: 1 }, // Just need count
    });
    clinicCount = clinicCountResponse?.meta?.pagination?.total || 0;

    const practitionerCountResponse = await getStrapiContent.practitioners.getAll({
      specialtySlug: slug,
      pagination: { page: 1, pageSize: 1 }, // Just need count
    });
    practitionerCount = practitionerCountResponse?.meta?.pagination?.total || 0;
    console.log(`Counts for specialty ${slug}: Clinics=${clinicCount}, Practitioners=${practitionerCount}`);
  } catch (error) {
    console.error("Error fetching counts for specialty page:", error);
  }
  
  // Fetch clinics data
  let clinics: TransformedClinic[] = [];
  let clinicPages = 1;
  try {
    console.log(`Fetching clinics for specialty ${slug}, activeTab: ${activeTab}, query: ${query}, location: ${location}, currentPage: ${currentPage}`);
    const clinicResponse = await getStrapiContent.clinics.getAll({
      specialtySlug: slug,
      query: activeTab === 'clinics' ? query : '',
      location: activeTab === 'clinics' ? location : '',
      page: activeTab === 'clinics' ? currentPage : 1,
      ...fetchOptions
    });
    console.log(`Clinic response data length for specialty ${slug}: ${clinicResponse?.data?.length || 0}`);
    clinics = (clinicResponse?.data || []).map((item: any) => {
      const clinicDataToTransform = item.attributes || item;
      if (item?.id && !clinicDataToTransform.id) clinicDataToTransform.id = item.id;
      return transformClinicData(clinicDataToTransform);
    }).filter(Boolean as unknown as (value: TransformedClinic | null) => value is TransformedClinic);
    console.log(`Transformed clinics array length for specialty ${slug}: ${clinics.length}`);
    clinicPages = clinicResponse?.meta?.pagination?.pageCount || 1;
  } catch (error) {
    console.error(`Error fetching clinics for specialty ${slug}:`, error);
  }

  // Fetch practitioners data
  let practitioners: TransformedPractitioner[] = [];
  let practitionerPages = 1;
  try {
    console.log(`Fetching practitioners for specialty ${slug}, activeTab: ${activeTab}, query: ${query}, location: ${location}, currentPage: ${currentPage}`);
    const practitionerResponse = await getStrapiContent.practitioners.getAll({
      specialtySlug: slug,
      query: activeTab === 'practitioners' ? query : '',
      location: activeTab === 'practitioners' ? location : '',
      page: activeTab === 'practitioners' ? currentPage : 1,
      ...fetchOptions
    });
    console.log(`Practitioner raw response data length for specialty ${slug}: ${practitionerResponse?.data?.length || 0}`);
    if (practitionerResponse?.data && practitionerResponse.data.length > 0) {
      const firstRawItem = practitionerResponse.data[0];
      console.log(`First raw practitioner item structure for specialty ${slug}:`, JSON.stringify({
        id: firstRawItem.id,
        hasAttributes: !!firstRawItem.attributes,
        attributesType: firstRawItem.attributes ? typeof firstRawItem.attributes : 'N/A',
        keys: Object.keys(firstRawItem),
        attributeKeys: firstRawItem.attributes ? Object.keys(firstRawItem.attributes) : []
      }, null, 2));
    }
    practitioners = (practitionerResponse?.data || []).map((item: any) => {
      const practitionerRawData = item.attributes || item;
      if (item?.id && !practitionerRawData.id) practitionerRawData.id = item.id;
      // console.log(`Processing practitioner for specialty ${slug}: ${practitionerRawData.name || 'unnamed'}, ID: ${practitionerRawData.id || 'no-id'}`);
      if (!practitionerRawData.id || !practitionerRawData.name) {
        console.warn(`Practitioner data missing id or name for specialty ${slug}. ID: ${practitionerRawData.id}, Name: ${practitionerRawData.name}. Full item:`, JSON.stringify(item));
      }
      return transformPractitionerData(practitionerRawData);
    }).filter((p: TransformedPractitioner | null): p is TransformedPractitioner => {
      if (p === null) {
        // console.warn(`A practitioner was filtered out by transformPractitionerData for specialty ${slug}.`);
      }
      return p !== null;
    });
    console.log(`Transformed practitioners array length for specialty ${slug}: ${practitioners.length}`);
    practitionerPages = practitionerResponse?.meta?.pagination?.pageCount || 1;
  } catch (error) {
    console.error(`Error fetching practitioners for specialty ${slug}:`, error);
  }

  totalPages = activeTab === 'clinics' ? clinicPages : practitionerPages;

  // No longer need the isTabActive function as it's handled by the client components

  // Extract SEO data from specialty
  const attributes = (specialty as any)?.attributes || specialty || {};
  const seo = attributes.seo;

  // Ensure structuredData is a string before rendering
  let structuredDataString: string | null = null;
  if (seo?.structuredData) {
    if (typeof seo.structuredData === 'string') {
      structuredDataString = seo.structuredData;
    } else if (typeof seo.structuredData === 'object') {
      try {
        structuredDataString = JSON.stringify(seo.structuredData);
      } catch (e) {
        console.error("Failed to stringify structuredData object:", e);
      }
    }
  }

  // If no structured data from SEO plugin, create a default one
  if (!structuredDataString) {
    // Create default structured data for specialty
    const defaultStructuredData = {
      "@context": "https://schema.org",
      "@type": "MedicalSpecialty",
      "name": name,
      "description": description,
      "url": `${process.env.NEXT_PUBLIC_SITE_URL || ''}/specialities/${slug}`,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `${process.env.NEXT_PUBLIC_SITE_URL || ''}/specialities/${slug}`
      }
    };

    structuredDataString = JSON.stringify(defaultStructuredData);
  }

  return (
    <>
      {/* Structured Data (JSON-LD) */}
      {structuredDataString && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: structuredDataString }}
        />
      )}
      {/* Breadcrumb - Updated for Specialties */}
      <div className="bg-gray-100 py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-emerald-600">Home</Link>
            {/* Optional: Add a link to a general specialties listing page if it exists */}
            <span className="mx-2">/</span>
            <Link href="/specialities" className="hover:text-emerald-600">Specialties</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-800">{name}</span> {/* Display specialty name */}
          </div>
        </div>
      </div>

      {/* Specialty Header - Updated for Specialties */}
      <div className="bg-emerald-600 text-white py-12 relative overflow-hidden">
        {specialtyImageUrl && (
          <div
            className="absolute inset-0 opacity-20 bg-cover bg-center"
            style={{ backgroundImage: `url(${specialtyImageUrl})` }}
          ></div>
        )}
        <div className="container mx-auto px-4 relative z-10">
          {/* Removed icon logic as specialties might not have icons */}
          <h1 className="text-3xl md:text-4xl font-bold mb-4">{name}</h1>
          {description && <p className="text-lg max-w-3xl mb-4">{description}</p>}
          <div className="flex gap-4 text-sm">
            {/* Show counts for related clinics/practitioners */}
            <span>{clinicCount} Related Clinics</span>
            <span>&bull;</span>
            <span>{practitionerCount} Related Practitioners</span>
          </div>
        </div>
      </div>

      {/* Search and Filter Section - Kept generic */}
      {/* Search and Filter Section - Updated */}
      <div className="bg-white shadow-sm sticky top-0 z-20">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
               <SearchInput placeholder={`Search clinics/practitioners for ${name}...`} paramName="query" />
            </div>
             {/* Location Input - Replaced with SearchInput */}
            <div className="flex-1">
               <SearchInput placeholder="City, state, or zip code" paramName="location" icon={<FiMapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />} />
            </div>
            <div>
              <button className="w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200">
                <FiFilter />
                <span>Filters</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs & Results Section - Using client-side components */}
      <div className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          {/* Use the client-side TabSwitcher component */}
          <TabSwitcher
            slug={awaitedParams.slug}
            pageType="specialities"
            clinicCount={clinicCount}
            practitionerCount={practitionerCount}
            initialTab={activeTab}
          />

          {/* Use the client-side TabContent component */}
          <TabContent
            clinics={clinics}
            practitioners={practitioners}
            totalPages={totalPages}
            initialTab={activeTab}
          />
        </div>
      </div>

      {/* Removed Related Conditions Section */}

      {/* Replace Call to Action with ExploreFurther component */}
      <ExploreFurther />
    </>
  );
}
