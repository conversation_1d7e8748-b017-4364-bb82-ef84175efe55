'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

/**
 * Navigation component for optimization examples
 */
export default function OptimizationNav() {
  const pathname = usePathname();
  
  const links = [
    { href: '/optimized-example', label: 'Server-Side Optimization' },
    { href: '/optimized-client-example', label: 'Client-Side Optimization' },
  ];
  
  return (
    <div className="bg-blue-50 p-4 rounded-lg mb-8">
      <h2 className="text-xl font-semibold mb-2">Optimization Examples</h2>
      <p className="mb-4">
        These examples demonstrate different approaches to reducing API calls to Strapi.
      </p>
      
      <div className="flex flex-wrap gap-2">
        {links.map(link => (
          <Link
            key={link.href}
            href={link.href}
            className={`px-4 py-2 rounded ${
              pathname === link.href
                ? 'bg-blue-500 text-white'
                : 'bg-white text-blue-500 hover:bg-blue-100'
            }`}
          >
            {link.label}
          </Link>
        ))}
      </div>
    </div>
  );
}
