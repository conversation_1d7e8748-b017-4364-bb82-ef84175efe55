import { useQuery, useInfiniteQuery, InfiniteData, keepPreviousData } from '@tanstack/react-query'; // Import InfiniteData and keepPreviousData
import { Clinic } from '@/types/strapi-types';
import { StrapiResponse } from '@/lib/api/strapiClient';

// --- API Client-Side Fetching Functions ---
// These functions will be called by React Query to fetch data from your Next.js API routes.

async function fetchClinicBySlugAPI(slug: string): Promise<StrapiResponse<Clinic | null>> {
  const response = await fetch(`/api/clinics/${slug}`);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: `Failed to fetch clinic ${slug}: ${response.status} ${response.statusText}` }));
    console.error(`API error fetching clinic ${slug}:`, errorData);
    throw new Error(errorData.error?.message || errorData.message || `Request failed for clinic ${slug} with status ${response.status}`);
  }
  return response.json();
}

async function fetchClinicsListAPI({
  pageParam = 1,
  categorySlug,
  specialtySlug,
  conditionSlug,
  searchTerm,
  pageSize = 10,
}: {
  pageParam?: number;
  categorySlug?: string;
  specialtySlug?: string;
  conditionSlug?: string;
  searchTerm?: string;
  pageSize?: number;
}): Promise<StrapiResponse<Clinic[]>> {
  const params = new URLSearchParams();
  params.append('page', pageParam.toString());
  params.append('pageSize', pageSize.toString());
  if (categorySlug) params.append('category', categorySlug);
  if (specialtySlug) params.append('specialty', specialtySlug);
  if (conditionSlug) params.append('condition', conditionSlug);
  if (searchTerm) params.append('search', searchTerm);

  const response = await fetch(`/api/clinics?${params.toString()}`);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: `Failed to fetch clinics list: ${response.status} ${response.statusText}` }));
    console.error('API error fetching clinics list:', errorData);
    throw new Error(errorData.error?.message || errorData.message || `Request failed for clinics list with status ${response.status}`);
  }
  return response.json();
}

// --- React Query Hooks ---

/**
 * Hook to fetch a single clinic by its slug.
 * @param slug - The slug of the clinic to fetch.
 * @param options - Optional React Query options.
 */
export function useClinic(slug: string | undefined, options?: { enabled?: boolean }) {
  return useQuery<StrapiResponse<Clinic | null>, Error>({
    queryKey: ['clinic', slug],
    queryFn: () => {
      if (!slug) return Promise.resolve({ data: null, meta: undefined }); // Ensure StrapiResponse structure
      return fetchClinicBySlugAPI(slug);
    },
    enabled: !!slug && (options?.enabled !== undefined ? options.enabled : true), // Only run query if slug is provided and enabled
    staleTime: 1000 * 60 * 5, // 5 minutes
    // gcTime is inherited from QueryProvider defaults (30 minutes)
  });
}

/**
 * Hook to fetch a paginated list of clinics, with optional filters.
 * Supports infinite scrolling.
 * @param filters - Optional filters for category, specialty, condition, search term.
 * @param pageSize - Number of items per page.
 */
export function useClinicsList(
  filters: {
    categorySlug?: string;
    specialtySlug?: string;
    conditionSlug?: string;
    searchTerm?: string;
  } = {},
  pageSize: number = 10
) {
  // TQueryFnData: StrapiResponse<Clinic[]> (data type per page)
  // TError: Error
  // TData: InfiniteData<StrapiResponse<Clinic[]>, number> (shape of the entire data object)
  // TQueryKey: (string | typeof filters)[] (type of the queryKey)
  // TPageParam: number (type of pageParam)
  return useInfiniteQuery<
    StrapiResponse<Clinic[]>,
    Error,
    InfiniteData<StrapiResponse<Clinic[]>, number>,
    (string | typeof filters)[],
    number
  >({
    queryKey: ['clinicsList', filters],
    queryFn: ({ pageParam }) => fetchClinicsListAPI({ ...filters, pageParam, pageSize }),
    getNextPageParam: (lastPage: StrapiResponse<Clinic[]>) => {
      if (!lastPage.meta?.pagination) return undefined;
      const { page, pageCount } = lastPage.meta.pagination;
      return page < pageCount ? page + 1 : undefined;
    },
    initialPageParam: 1, // Required for v5
    staleTime: 1000 * 60 * 2, // 2 minutes for list data
    // gcTime is inherited
    placeholderData: keepPreviousData, // Correct usage for v5
  });
}

// Example hook for featured clinics (if you have a separate API endpoint or different client-side needs)
// async function fetchFeaturedClinicsAPI(limit: number = 5): Promise<StrapiResponse<Clinic[]>> {
//   const response = await fetch(`/api/clinics/featured?limit=${limit}`);
//   if (!response.ok) {
//     const errorData = await response.json().catch(() => ({ message: 'Failed to fetch featured clinics' }));
//     throw new Error(errorData.error?.message || errorData.message || 'Network response was not ok for featured clinics');
//   }
//   return response.json();
// }

// export function useFeaturedClinics(limit: number = 5) {
//   return useQuery<StrapiResponse<Clinic[]>, Error>(
//     ['featuredClinics', limit],
//     () => fetchFeaturedClinicsAPI(limit),
//     {
//       staleTime: 1000 * 60 * 10, // 10 minutes for featured content
//     }
//   );
// }
