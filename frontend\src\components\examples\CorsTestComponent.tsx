'use client';

import { useState, useEffect } from 'react';
import { useStrapiWithFallback } from '@/hooks/useStrapiWithFallback';

/**
 * A component that tests different methods of fetching data from Strapi
 * to diagnose and fix CORS issues
 */
export default function CorsTestComponent() {
  // State for direct fetch test
  const [directData, setDirectData] = useState<any>(null);
  const [directError, setDirectError] = useState<string | null>(null);
  const [directLoading, setDirectLoading] = useState(false);
  
  // State for proxy fetch test
  const [proxyData, setProxyData] = useState<any>(null);
  const [proxyError, setProxyError] = useState<string | null>(null);
  const [proxyLoading, setProxyLoading] = useState(false);
  
  // Use the custom hook that tries direct fetch with proxy fallback
  const { 
    data: fallbackData, 
    isLoading: fallbackLoading, 
    error: fallbackError,
    usingProxy 
  } = useStrapiWithFallback('/clinics', {
    params: {
      filters: { isFeatured: { $eq: true } },
      populate: '*'
    }
  });
  
  // Test direct fetch to Strapi
  const testDirectFetch = async () => {
    setDirectLoading(true);
    setDirectError(null);
    setDirectData(null);
    
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://nice-badge-2130241d6c.strapiapp.com';
      const response = await fetch(`${apiUrl}/api/clinics?filters[isFeatured][$eq]=true&populate=*`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      setDirectData(data);
    } catch (error: any) {
      console.error('Direct fetch error:', error);
      setDirectError(error.message || 'An error occurred');
    } finally {
      setDirectLoading(false);
    }
  };
  
  // Test fetch through proxy
  const testProxyFetch = async () => {
    setProxyLoading(true);
    setProxyError(null);
    setProxyData(null);
    
    try {
      const response = await fetch('/api/strapi-proxy?endpoint=/clinics&filters[isFeatured][$eq]=true&populate=*');
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      setProxyData(data);
    } catch (error: any) {
      console.error('Proxy fetch error:', error);
      setProxyError(error.message || 'An error occurred');
    } finally {
      setProxyLoading(false);
    }
  };
  
  // Log environment variables for debugging
  useEffect(() => {
    console.log('NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
  }, []);
  
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">CORS Test Component</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Direct Fetch Test */}
        <div className="border p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Direct Fetch Test</h2>
          <button
            onClick={testDirectFetch}
            disabled={directLoading}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mb-4 disabled:opacity-50"
          >
            {directLoading ? 'Loading...' : 'Test Direct Fetch'}
          </button>
          
          {directError && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <p className="font-bold">Error:</p>
              <p>{directError}</p>
            </div>
          )}
          
          {directData && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <p className="font-bold">Success!</p>
              <p>Found {directData.data?.length || 0} clinics</p>
            </div>
          )}
        </div>
        
        {/* Proxy Fetch Test */}
        <div className="border p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Proxy Fetch Test</h2>
          <button
            onClick={testProxyFetch}
            disabled={proxyLoading}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mb-4 disabled:opacity-50"
          >
            {proxyLoading ? 'Loading...' : 'Test Proxy Fetch'}
          </button>
          
          {proxyError && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <p className="font-bold">Error:</p>
              <p>{proxyError}</p>
            </div>
          )}
          
          {proxyData && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <p className="font-bold">Success!</p>
              <p>Found {proxyData.data?.length || 0} clinics</p>
            </div>
          )}
        </div>
        
        {/* Fallback Hook Test */}
        <div className="border p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Fallback Hook Test</h2>
          <div className="mb-4">
            <p className="font-semibold">Status:</p>
            <p>{fallbackLoading ? 'Loading...' : usingProxy ? 'Using Proxy' : 'Direct Success'}</p>
          </div>
          
          {fallbackError && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <p className="font-bold">Error:</p>
              <p>{fallbackError.message}</p>
            </div>
          )}
          
          {fallbackData && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <p className="font-bold">Success!</p>
              <p>Found {fallbackData.data?.length || 0} clinics</p>
              <p className="text-sm mt-2">Method: {usingProxy ? 'Proxy' : 'Direct'}</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Environment Info</h2>
        <p><strong>NEXT_PUBLIC_API_URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'Not set'}</p>
        <p><strong>Browser Origin:</strong> {typeof window !== 'undefined' ? window.location.origin : 'Server-side rendering'}</p>
      </div>
    </div>
  );
}
