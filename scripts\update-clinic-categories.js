/**
 * Update clinic categories in Strapi 5
 * 
 * This script updates the categories for specific clinics in the Strapi database.
 * 
 * Usage: node update-clinic-categories.js
 */

const axios = require('axios');
require('dotenv').config();

// Configuration
const STRAPI_URL = process.env.STRAPI_URL || 'http://localhost:1337';
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;

// Axios instance with authorization header
const strapiAPI = axios.create({
  baseURL: STRAPI_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${STRAPI_API_TOKEN}`
  }
});

// Define the categories we want to use
const CATEGORIES = {
  'Acupuncture Clinics': 'acupuncture-clinics',
  'Chiropractors': 'chiropractors',
  'Functional Medicine Doctors': 'functional-medicine-doctors',
  'Homeopaths': 'homeopaths',
  'Integrative Medicine Doctors': 'integrative-medicine-doctors',
  'Osteopaths': 'osteopaths',
  'Physical Therapists': 'physical-therapists',
  'Wellness Centers': 'wellness-centers'
};

// Define the clinics and their categories
const CLINICS_TO_UPDATE = [
  { name: 'Fluid Water Therapy', category: 'Wellness Centers' },
  { name: 'Costigan Theresa', category: 'Wellness Centers' },
  { name: 'Holistic Health And Fitness Coaching', category: 'Wellness Centers' },
  { name: 'Holistic Natural Medicine', category: 'Wellness Centers' },
  { name: 'Clutch Physical Therapy', category: 'Physical Therapists' },
  { name: 'Integrative Physical Therapy Of Nyc', category: 'Physical Therapists' },
  { name: 'Thrive Integrated Physical Therapy', category: 'Physical Therapists' },
  { name: 'Grand Central Physical Medicine And Rehabilitation', category: 'Physical Therapists' },
  { name: 'Tomorrow Health', category: 'Osteopaths' },
  { name: 'Natural Integrated Medicine', category: 'Integrative Medicine Doctors' },
  { name: 'Virginia University Of Integrative Medicine - New Jersey Campus', category: 'Integrative Medicine Doctors' },
  { name: 'Homeopathic Healing', category: 'Homeopaths' },
  { name: 'Homeopathy Studio', category: 'Homeopaths' },
  { name: 'New York Center For Homeopathy', category: 'Homeopaths' },
  { name: 'Anderson Peak Performance', category: 'Functional Medicine Doctors' },
  { name: 'Rahav Wellness The Center For Collaborative Healing', category: 'Functional Medicine Doctors' },
  { name: 'Tarrytown Functional Medicine', category: 'Functional Medicine Doctors' },
  { name: 'Union Square Spinal Care', category: 'Chiropractors' },
  { name: 'Dr. David Kulla, Dc', category: 'Chiropractors' },
  { name: 'Amayu Institute', category: 'Chiropractors' },
  { name: 'Dr. Robert Shire, Dc', category: 'Chiropractors' },
  { name: 'Russell, George, Dc', category: 'Chiropractors' },
  { name: 'Willen Wellness', category: 'Chiropractors' },
  { name: 'Nachmias Chiropractic: Nachmias Adam L DC', category: 'Chiropractors' },
  { name: 'Angrist Chiropractic & Wellness Care', category: 'Chiropractors' },
  { name: 'Bajaj Chiropractic, Pc', category: 'Chiropractors' },
  { name: 'Brooklyn Chiropractic Studio: Megan Hondru, Dc', category: 'Chiropractors' },
  { name: 'Get Adjusted Chiropractic P.C.', category: 'Chiropractors' },
  { name: 'Hartman Center For Functional Neurology Chiropractic', category: 'Chiropractors' },
  { name: 'Midtown Chiropractor, Dr. Gregg Rubinstein', category: 'Chiropractors' },
  { name: 'NYC Back Chiropractic - Isaac Lichy DC', category: 'Chiropractors' },
  { name: 'Tribeca Chiropractic, Pllc', category: 'Chiropractors' },
  { name: 'Sidh Ayurveda', category: 'Wellness Centers' },
  { name: 'Eastwest Integrated Wellness', category: 'Acupuncture Clinics' },
  { name: 'Brooklyn Osteopathy', category: 'Acupuncture Clinics' },
  { name: 'Lab Of Life Wellness Center', category: 'Acupuncture Clinics' },
  { name: 'Les Acupuncture & Bodywork', category: 'Acupuncture Clinics' },
  { name: 'Tee Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Dr. Christy Chiang, L.Ac', category: 'Acupuncture Clinics' },
  { name: 'De\'qi Health', category: 'Acupuncture Clinics' },
  { name: 'Blue Phoenix Wellness', category: 'Acupuncture Clinics' },
  { name: 'Gotham Holistic', category: 'Acupuncture Clinics' },
  { name: 'Hudson River Wellness Heather Spillane', category: 'Acupuncture Clinics' },
  { name: 'Integrative Healing Arts', category: 'Acupuncture Clinics' },
  { name: 'Lotus Heals', category: 'Acupuncture Clinics' },
  { name: 'Medina Wellness', category: 'Acupuncture Clinics' },
  { name: 'Sean V Cotter, Dc', category: 'Acupuncture Clinics' },
  { name: 'Dr. Naika Apeakorang, Nd, Lac.', category: 'Acupuncture Clinics' },
  { name: 'Elizabeth Carpenter, Ms, Lac', category: 'Acupuncture Clinics' },
  { name: 'Meg Richichi, MS, LAc', category: 'Acupuncture Clinics' },
  { name: 'People Tree Wellness - Mona Chopra, Lac', category: 'Acupuncture Clinics' },
  { name: 'Shannon Russo-Pollack', category: 'Acupuncture Clinics' },
  { name: 'Douglas S Freeman, Lac', category: 'Acupuncture Clinics' },
  { name: 'Dr. Richard Hazel, DAc - Range Of Motion Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'New York Pain Medicine', category: 'Acupuncture Clinics' },
  { name: 'Magnus Wellness Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'U.N. Acupuncture Center', category: 'Acupuncture Clinics' },
  { name: 'Diamond Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Ab Acupuncture- Annalisa Brown, L.Ac', category: 'Acupuncture Clinics' },
  { name: 'Aca Acupuncture & Wellness - Union Square', category: 'Acupuncture Clinics' },
  { name: 'Acusophy Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Anna Panettiere Acupuncture & Herbs', category: 'Acupuncture Clinics' },
  { name: 'Arteva Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Awaken Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Bdyi Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Beth Conroy Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Bloom Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Clear Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Acupuncture by Bae', category: 'Acupuncture Clinics' },
  { name: 'Jaesun Yoo Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Soho Acupuncture By Jen Becker', category: 'Acupuncture Clinics' },
  { name: 'Klara Brown Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Mauro Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Mila Mintsis Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Morningside Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Nurturing Life Acupuncture & Wellness', category: 'Acupuncture Clinics' },
  { name: 'Sher Acupuncture Center Nyc', category: 'Acupuncture Clinics' },
  { name: 'Susy Qi Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Tranquility Base Acupuncture', category: 'Acupuncture Clinics' },
  { name: 'Acupuncture Bodywork Pc', category: 'Acupuncture Clinics' }
];

// Helper function to clean slug
function cleanSlug(slug) {
  if (!slug) return '';
  // Replace any characters that aren't allowed in Strapi slugs
  return slug.replace(/[^A-Za-z0-9-_.~]/g, '-').toLowerCase();
}

// Helper function to find a clinic by name
async function findClinicByName(name) {
  try {
    // First try exact match
    const exactResponse = await strapiAPI.get(`/api/clinics?filters[name][$eq]=${encodeURIComponent(name)}`);
    if (exactResponse.data.data && exactResponse.data.data.length > 0) {
      return exactResponse.data.data[0];
    }
    
    // If no exact match, try case-insensitive match
    const caseInsensitiveResponse = await strapiAPI.get(`/api/clinics?filters[name][$containsi]=${encodeURIComponent(name)}`);
    if (caseInsensitiveResponse.data.data && caseInsensitiveResponse.data.data.length > 0) {
      // Find the closest match
      const clinics = caseInsensitiveResponse.data.data;
      const closestMatch = clinics.find(clinic => 
        clinic.name.toLowerCase() === name.toLowerCase()
      ) || clinics[0];
      
      return closestMatch;
    }
    
    return null;
  } catch (error) {
    console.error(`Error finding clinic by name ${name}:`, error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    return null;
  }
}

// Helper function to find a category by name or slug
async function findCategoryByNameOrSlug(nameOrSlug) {
  try {
    // Try to find by name first
    const nameResponse = await strapiAPI.get(`/api/categories?filters[name][$eq]=${encodeURIComponent(nameOrSlug)}`);
    if (nameResponse.data.data && nameResponse.data.data.length > 0) {
      return nameResponse.data.data[0];
    }
    
    // If not found by name, try by slug
    const slug = CATEGORIES[nameOrSlug] || cleanSlug(nameOrSlug);
    const slugResponse = await strapiAPI.get(`/api/categories?filters[slug][$eq]=${encodeURIComponent(slug)}`);
    if (slugResponse.data.data && slugResponse.data.data.length > 0) {
      return slugResponse.data.data[0];
    }
    
    return null;
  } catch (error) {
    console.error(`Error finding category by name or slug ${nameOrSlug}:`, error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    return null;
  }
}

// Helper function to create a category if it doesn't exist
async function createCategoryIfNotExists(name) {
  try {
    // Check if category exists
    const existingCategory = await findCategoryByNameOrSlug(name);
    if (existingCategory) {
      console.log(`Category ${name} already exists with ID ${existingCategory.id}`);
      return existingCategory;
    }
    
    // Create new category
    const slug = CATEGORIES[name] || cleanSlug(name);
    const createResponse = await strapiAPI.post('/api/categories', {
      data: {
        name,
        slug,
        description: `${name} directory category`,
        publishedAt: new Date().toISOString()
      }
    });
    
    console.log(`Created new category: ${name} with ID ${createResponse.data.data.id}`);
    return createResponse.data.data;
  } catch (error) {
    console.error(`Error creating category ${name}:`, error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    return null;
  }
}

// Main function to update clinic categories
async function updateClinicCategories() {
  try {
    console.log('Starting clinic category update process...');
    
    // Ensure all categories exist
    const uniqueCategories = [...new Set(CLINICS_TO_UPDATE.map(clinic => clinic.category))];
    console.log(`Found ${uniqueCategories.length} unique categories to ensure exist`);
    
    const categoryMap = {};
    for (const categoryName of uniqueCategories) {
      const category = await createCategoryIfNotExists(categoryName);
      if (category) {
        categoryMap[categoryName] = category;
      }
    }
    
    console.log('Category map created:', Object.keys(categoryMap).length);
    
    // Update each clinic
    let updated = 0;
    let skipped = 0;
    let errors = 0;
    
    for (const clinicData of CLINICS_TO_UPDATE) {
      try {
        const { name, category: categoryName } = clinicData;
        console.log(`Processing clinic: ${name}`);
        
        // Find the clinic
        const clinic = await findClinicByName(name);
        if (!clinic) {
          console.log(`Clinic not found: ${name}`);
          skipped++;
          continue;
        }
        
        // Find the category
        const category = categoryMap[categoryName];
        if (!category) {
          console.log(`Category not found: ${categoryName}`);
          skipped++;
          continue;
        }
        
        console.log(`Updating clinic ${name} (ID: ${clinic.id}) with category ${categoryName} (ID: ${category.id})`);
        
        // Update the clinic with the category
        const updateResponse = await strapiAPI.put(`/api/clinics/${clinic.documentId}`, {
          data: {
            categories: {
              connect: [{ documentId: category.documentId }]
            }
          }
        });
        
        console.log(`Updated clinic: ${name} with category: ${categoryName}`);
        updated++;
      } catch (error) {
        console.error(`Error updating clinic ${clinicData.name}:`, error.message);
        if (error.response) {
          console.error('Response status:', error.response.status);
          console.error('Response data:', JSON.stringify(error.response.data, null, 2));
        }
        errors++;
      }
    }
    
    console.log('\nUpdate Summary:');
    console.log(`Updated: ${updated}`);
    console.log(`Skipped: ${skipped}`);
    console.log(`Errors: ${errors}`);
    console.log(`Total processed: ${CLINICS_TO_UPDATE.length}`);
    
    return { updated, skipped, errors };
  } catch (error) {
    console.error('Update failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    throw error;
  }
}

// Run the update
console.log('Starting update process...');
updateClinicCategories()
  .then(result => {
    console.log('Update completed successfully:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('Update failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  });
