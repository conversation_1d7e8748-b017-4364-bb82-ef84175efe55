import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag, revalidatePath } from 'next/cache';
import logger from '@/lib/logger';

// Get the revalidation token from environment variables
const REVALIDATE_SECRET = process.env.PREVIEW_SECRET || process.env.REVALIDATE_TOKEN;

/**
 * API route for on-demand revalidation of category pages
 * This endpoint is called by Strapi webhooks when category content is updated
 *
 * POST /api/revalidate/categories
 */
export async function POST(request: NextRequest) {
  if (!REVALIDATE_SECRET) {
    logger.error('CRITICAL: REVALIDATE_SECRET is not set. Categories revalidation endpoint is disabled.');
    return NextResponse.json({ message: 'Revalidation secret not configured.' }, { status: 500 });
  }

  // Check for the secret token in a custom header
  const secretFromHeader = request.headers.get('X-Revalidate-Secret');

  if (secretFromHeader !== REVALIDATE_SECRET) {
    logger.warn('Invalid categories revalidation attempt: Incorrect or missing X-Revalidate-Secret header.');
    return NextResponse.json({ message: 'Invalid token' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { event, model, entry } = body;

    // Ensure this webhook is for the 'category' model
    if (model !== 'category') {
      logger.info(`Revalidation skipped: Not a category model. Model received: ${model}`);
      return NextResponse.json({ message: 'Revalidation skipped: Not a category model.' }, { status: 200 });
    }

    const tagsToRevalidate: string[] = [];

    // Always revalidate the general categories list tag
    tagsToRevalidate.push('strapi-categories');
    tagsToRevalidate.push('strapi-categories-slugs');

    // Revalidate paginated category pages (assuming max 10 pages)
    for (let i = 1; i <= 10; i++) {
      tagsToRevalidate.push(`strapi-categories-page-${i}`);
    }

    // Also revalidate the path for the categories index page
    try {
      revalidatePath('/categories');
      logger.info('Successfully revalidated path: /categories');
    } catch (error) {
      logger.error('Error revalidating path /categories:', error);
    }

    // If we have a specific category entry with a slug, revalidate its specific tag
    let revalidatedPaths = ['/categories']; // Initialize with the categories index path

    if (entry && entry.slug) {
      const categorySlug = entry.slug;

      // Revalidate the specific category tag
      tagsToRevalidate.push(`strapi-category-${categorySlug}`);

      // Revalidate tags for clinics and practitioners in this category
      tagsToRevalidate.push(`strapi-category-${categorySlug}-clinics`);
      tagsToRevalidate.push(`strapi-category-${categorySlug}-practitioners`);

      // Also revalidate the specific category detail page
      try {
        const categoryDetailPath = `/categories/${categorySlug}`;
        revalidatePath(categoryDetailPath);
        revalidatedPaths.push(categoryDetailPath);
        logger.info(`Successfully revalidated path: ${categoryDetailPath}`);
      } catch (error) {
        logger.error(`Error revalidating path /categories/${categorySlug}:`, error);
      }

      logger.info(`Attempting to revalidate tags for category slug: ${categorySlug}`);
    }

    // Revalidate all tags
    if (tagsToRevalidate.length > 0) {
      for (const tag of tagsToRevalidate) {
        revalidateTag(tag);
      }
      logger.info('Category revalidation successful for tags:', tagsToRevalidate.join(', '));
      return NextResponse.json({
        revalidated: true,
        revalidatedTags: tagsToRevalidate,
        revalidatedPaths: revalidatedPaths,
        timestamp: new Date().toISOString()
      });
    } else {
      logger.info('No specific category tags to revalidate.');
      return NextResponse.json({
        revalidated: false,
        message: 'No specific category tags to revalidate.'
      });
    }

  } catch (error: any) {
    logger.error('Error during category revalidation:', error);
    return NextResponse.json({
      message: 'Error revalidating categories',
      error: error.message
    }, { status: 500 });
  }
}

// Optional: GET handler for testing or manual trigger
export async function GET(request: NextRequest) {
  if (!REVALIDATE_SECRET) {
    return NextResponse.json({ message: 'Revalidation secret not configured.' }, { status: 500 });
  }

  const secretFromHeader = request.headers.get('X-Revalidate-Secret');
  const requestUrl = new URL(request.url);
  const tag = requestUrl.searchParams.get('tag');
  const slug = requestUrl.searchParams.get('slug');

  if (secretFromHeader !== REVALIDATE_SECRET) {
    const secretFromQuery = requestUrl.searchParams.get('secret');
    if (secretFromQuery !== REVALIDATE_SECRET) {
      logger.warn('Invalid GET category revalidation attempt: Incorrect or missing secret.');
      return NextResponse.json({ message: 'Invalid token' }, { status: 401 });
    }
  }

  try {
    const tagsToRevalidate: string[] = [];

    // If a specific tag is provided, revalidate only that tag
    if (tag) {
      tagsToRevalidate.push(tag);
    }
    // If a slug is provided, revalidate tags for that category
    else if (slug) {
      tagsToRevalidate.push(`strapi-category-${slug}`);
      tagsToRevalidate.push(`strapi-category-${slug}-clinics`);
      tagsToRevalidate.push(`strapi-category-${slug}-practitioners`);
    }
    // Otherwise, revalidate all category tags
    else {
      tagsToRevalidate.push('strapi-categories');
      tagsToRevalidate.push('strapi-categories-slugs');

      // Revalidate paginated category pages (assuming max 10 pages)
      for (let i = 1; i <= 10; i++) {
        tagsToRevalidate.push(`strapi-categories-page-${i}`);
      }

      // Also revalidate the path for the categories index page
      try {
        revalidatePath('/categories');
        logger.info('Successfully revalidated path: /categories');
      } catch (error) {
        logger.error('Error revalidating path /categories:', error);
      }
    }

    // Revalidate all tags
    for (const tagToRevalidate of tagsToRevalidate) {
      revalidateTag(tagToRevalidate);
    }

    // Prepare the list of revalidated paths
    const revalidatedPaths = ['/categories'];

    // If a specific slug was provided, add its path
    if (slug) {
      revalidatedPaths.push(`/categories/${slug}`);
    }

    logger.info('Manual category revalidation successful for tags:', tagsToRevalidate.join(', '));
    logger.info('Manual category revalidation successful for paths:', revalidatedPaths.join(', '));

    return NextResponse.json({
      revalidated: true,
      revalidatedTags: tagsToRevalidate,
      revalidatedPaths: revalidatedPaths,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    logger.error('Error during manual category revalidation:', error);
    return NextResponse.json({
      message: 'Error revalidating categories',
      error: error.message
    }, { status: 500 });
  }
}
