"use client";

import Link from 'next/link';
import { <PERSON>U<PERSON>, FiAward } from 'react-icons/fi';
import { MdVerifiedUser } from "react-icons/md";
import { useEffect, useState, useRef } from 'react';
import { getCachedPractitioner, cachePractitioner } from '@/lib/clientCache';

interface PractitionerCardProps {
  practitioner: {
    id: string;
    name: string;
    slug: string;
    isVerified?: boolean;
    bio?: string | null;
    profilePicture?: string | null;
    qualifications?: string | null;
    contactInfo?: { 
      phoneNumber?: string;
      emailAddress?: string;
    } | null;
    // Add all other fields that might be needed for the detail page
    title?: string | null;
    videoEmbed?: any;
    specialties?: any;
    conditions?: any;
    education?: any;
    clinics?: any;
    seo?: any;
    _hasDetailedData?: boolean; // Flag from server prefetching
  };
  prefetchedData?: boolean; // Flag to indicate if this card has complete data
}

const PractitionerCard = ({ practitioner, prefetchedData = false }: PractitionerCardProps) => {
  // Check if we have this practitioner in the client-side cache
  const [isCached, setIsCached] = useState(false);
  const hasCached = useRef(false);
  
  useEffect(() => {
    // Only run this effect once per component instance
    if (hasCached.current) return;
    
    // Check if this practitioner is in the client-side cache
    const cachedData = getCachedPractitioner(practitioner.slug);
    if (cachedData) {
      setIsCached(true);
    }
    
    // If we have server-prefetched data, store it in the client cache
    if (practitioner._hasDetailedData && !cachedData) {
      cachePractitioner(practitioner);
      setIsCached(true);
    }
    
    hasCached.current = true;
  }, [practitioner]);
  
  // Determine if we should use the prefetched flag
  // We'll use it if we have server-prefetched data or client-cached data
  const hasPrefetchedData = prefetchedData || practitioner._hasDetailedData || isCached;
  
  // Construct the link with state if we have prefetched data
  const linkHref = hasPrefetchedData 
    ? { 
        pathname: `/practitioners/${practitioner.slug}`,
        // Pass the practitioner data as state to avoid refetching
        query: { prefetched: 'true' } 
      }
    : `/practitioners/${practitioner.slug}`;
  
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col"> 
      <div className="p-4 flex-grow">
        <h3 className="text-xl font-semibold text-gray-800 mb-1">
          <Link href={linkHref} className="hover:text-emerald-600">
            {practitioner.name}
          </Link>
        </h3>
        {/* Verified Badge */}
        {practitioner.isVerified && (
          <div className="flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium">
            <MdVerifiedUser color="#009967" size={14} />
            <span>VERIFIED</span>
          </div>
        )}
        
        {practitioner.bio && (
          <p className="text-gray-600 mb-4 line-clamp-3">{practitioner.bio}</p>
        )}
      </div>
      
      {/* Ensure footer stays at the bottom */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto"> 
        <Link 
          href={linkHref}
          className="text-emerald-600 hover:text-emerald-700 font-medium text-sm"
        >
          View Profile →
        </Link>
      </div>
    </div>
  );
};

export default PractitionerCard;
