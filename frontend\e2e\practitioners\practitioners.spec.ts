import { test, expect } from '@playwright/test';

test.describe('Practitioners Page', () => {
  test('should load the practitioners page successfully', async ({ page }) => {
    await page.goto('/practitioners');
    
    // Check that the page title is correct
    await expect(page).toHaveTitle(/Practitioners/);
    
    // Check that main sections are visible
    await expect(page.getByRole('heading', { name: /Practitioners/i })).toBeVisible();
  });

  test('should filter practitioners by specialty', async ({ page }) => {
    await page.goto('/practitioners');
    
    // Check if there's a specialty filter
    const specialtyFilter = page.getByText(/Filter by Specialty/i);
    if (await specialtyFilter.count() > 0) {
      // Click on the specialty filter dropdown
      await specialtyFilter.click();
      
      // Select the first specialty option
      const specialtyOptions = page.locator('li, option').filter({ hasText: /./ });
      if (await specialtyOptions.count() > 0) {
        await specialtyOptions.first().click();
        
        // Check that the filter has been applied
        await expect(page.getByText(/Filtered by/i)).toBeVisible();
      } else {
        test.skip('No specialty options found to test');
      }
    } else {
      test.skip('No specialty filter found to test');
    }
  });

  test('should navigate to a practitioner detail page', async ({ page }) => {
    await page.goto('/practitioners');
    
    // Click on the first practitioner card
    const practitionerCards = page.locator('.practitioner-card, [data-testid="practitioner-card"]').first();
    if (await practitionerCards.count() > 0) {
      await practitionerCards.click();
      
      // Check that we've navigated to the practitioner detail page
      await expect(page).toHaveURL(/\/practitioners\/.+/);
      
      // Check that the practitioner details are displayed
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
    } else {
      test.skip('No practitioner cards found to test');
    }
  });

  test('should search for practitioners', async ({ page }) => {
    await page.goto('/practitioners');
    
    // Fill the search input
    await page.getByPlaceholder(/Search/i).fill('acupuncture');
    
    // Press Enter to submit the search
    await page.keyboard.press('Enter');
    
    // Check that search results are displayed
    await expect(page.getByText(/Search Results/i)).toBeVisible();
  });
});
