'use client';

import { useState, useEffect, useCallback, useRef, memo } from 'react';
import Image, { ImageProps } from 'next/image';
import { FiImage } from 'react-icons/fi';
import { imageUtils } from '@/lib/imageLoader';

interface LazyImageProps extends Omit<ImageProps, 'onLoad' | 'onError' | 'blurDataURL'> {
  /**
   * CSS class for the fallback container when image is not loaded
   */
  fallbackClassName?: string;

  /**
   * Whether to show a blur placeholder while loading
   */
  showPlaceholder?: boolean;
  
  /**
   * Quality level for the image (will override default quality settings)
   * Higher values = better quality but larger file size
   */
  qualityOverride?: number;
  
  /**
   * Enable advanced blur-up placeholder technique
   */
  advancedBlur?: boolean;
  
  /**
   * Whether to preload the image before it enters the viewport
   * Good for images that are just below the fold
   */
  preload?: boolean;
  
  /**
   * Whether to add fade-in animation when image loads
   */
  fadeIn?: boolean;
  
  /**
   * Element to use as a wrapper (div or picture)
   */
  wrapperAs?: 'div' | 'picture';
  
  /**
   * If true, the image will fill its parent container.
   * The parent container must have `position: relative`.
   * `width` and `height` props will be used for aspect ratio and `srcset` but not for fixed sizing.
   */
  fillContainer?: boolean;
}

/**
 * High-Performance LazyImage component with advanced optimizations
 * - Uses next/image with additional performance enhancements
 * - Progressive loading with intelligent blur-up technique
 * - Smart preloading for critical images
 * - Avoids layout shifts with aspect ratio preservation
 * - Efficient resource loading with modern browser hints
 * - Advanced error handling with graceful degradation
 * - Zero overhead when fully loaded
 */
const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  fallbackClassName = '',
  showPlaceholder = true,
  advancedBlur = false,
  preload = false,
  fadeIn = true,
  wrapperAs = 'div',
  fillContainer = false,
  sizes = '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw', // More optimized responsive sizes
  style,
  priority = false,
  qualityOverride,
  ...rest
}) => {
  // Track component mounted state to prevent state updates after unmount
  const isMounted = useRef(true);
  
  // Single state to track loading status: "loading" | "loaded" | "error"
  const [loadState, setLoadState] = useState<'loading' | 'loaded' | 'error'>(
    priority ? 'loaded' : 'loading' // Assume priority images are already loaded for better UX
  );

  // Keep track of actual dimensions for aspect ratio calculations
  // If fillContainer is true, width/height props are for aspect ratio, not fixed size.
  const [dimensions, setDimensions] = useState({ 
    width: fillContainer ? undefined : width, 
    height: fillContainer ? undefined : height 
  });
  
  // Process source with enhanced handling for various formats
  const imageSrc = typeof src === 'string' 
    ? src 
    : (src as any)?.src || (src as any)?.url || (src as any)?.default?.src || null;

  // Advanced blur data URL generation using our optimized loader
  const blurDataURL = advancedBlur && showPlaceholder && imageSrc
    ? imageUtils.getBlurDataUrl(imageSrc, 20) // Generate an optimized tiny preview image
    : showPlaceholder 
      ? 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+'
      : undefined;

  // Handle preloading for important images that are just outside the viewport
  useEffect(() => {
    if (preload && imageSrc && !priority && typeof window !== 'undefined') {
      const img = imageUtils.preloadImage(imageSrc, typeof width === 'number' ? width : 500);
      
      // When preloaded, update state if still mounted
      if (img) {
        img.onload = () => {
          if (isMounted.current) {
            setLoadState('loaded');
          }
        };
        
        img.onerror = () => {
          if (isMounted.current) {
            setLoadState('error');
          }
        };
      }
    }
    
    // Clean up on unmount
    return () => {
      isMounted.current = false;
    };
  }, [imageSrc, preload, priority, width]);
  
  // Calculate aspect ratio for responsive images
  const aspectRatio = 
    typeof width === 'number' && typeof height === 'number' && width > 0 && height > 0
      ? width / height
      : undefined;

  // Memoized callbacks to prevent recreating functions on each render
  const handleLoad = useCallback((event: any) => {
    // Update with actual loaded dimensions to prevent layout shift, only if not in fillContainer mode
    if (!fillContainer && event?.target) {
      const { naturalWidth, naturalHeight } = event.target;
      if (naturalWidth && naturalHeight) {
        if (isMounted.current) {
          setDimensions({ width: naturalWidth, height: naturalHeight });
        }
      }
    }
    if (isMounted.current) {
      setLoadState('loaded');
    }
  }, [fillContainer]);

  const handleError = useCallback(() => {
    setLoadState('error');
    // Only log in development to avoid excessive logs in production
    if (process.env.NODE_ENV === 'development') {
      console.error('Image failed to load:', { src: imageSrc, alt });
    }
  }, [imageSrc, alt]);

  // Calculate loading strategy based on priority
  const loadingStrategy = priority ? 'eager' : 'lazy';

  // Calculate placeholder strategy
  const placeholderStrategy = showPlaceholder ? 'blur' : 'empty';

  // Render fallback for errors or missing source
  if (loadState === 'error' || !imageSrc) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 ${fallbackClassName || (fillContainer ? '' : className)}`}
        style={{ 
          width: fillContainer ? '100%' : width, 
          height: fillContainer ? '100%' : height, 
          aspectRatio: aspectRatio ? `${aspectRatio}` : undefined,
          ...style 
        }}
        role="img"
        aria-label={alt || 'Image failed to load'}
      >
        <FiImage className="text-gray-400 w-1/5 h-1/5" />
      </div>
    );
  }

  // Combine transition classes for smoother loading experience
  const imageClasses = [
    className,
    loadState === 'loaded' ? 'opacity-100' : 'opacity-0',
    fadeIn ? 'transition-opacity duration-300' : ''
  ].filter(Boolean).join(' ');

  // Create responsive style object with aspect ratio preservation
  const responsiveStyle = {
    objectFit: (style?.objectFit || 'cover') as 'cover' | 'contain' | 'fill' | 'none' | 'scale-down',
    aspectRatio: fillContainer ? undefined : (aspectRatio ? `${aspectRatio}` : undefined), // Aspect ratio handled by parent in fill mode
    ...style,
    width: fillContainer ? undefined : style?.width, // Let fill handle width/height
    height: fillContainer ? undefined : style?.height,
  };

  // Image render with optimized props
  const imageElement = (
    <Image
      src={imageSrc}
      alt={alt || ''}
      width={fillContainer ? undefined : dimensions.width} // next/image requires width/height unless fill is true
      height={fillContainer ? undefined : dimensions.height}
      fill={fillContainer}
      className={imageClasses} // className from props is applied here
      loading={loadingStrategy}
      fetchPriority={priority ? "high" : preload ? "low" : "auto"}
      priority={priority}
      sizes={sizes}
      style={responsiveStyle}
      placeholder={placeholderStrategy}
      blurDataURL={blurDataURL}
      onLoad={handleLoad}
      onError={handleError}
      quality={qualityOverride}
      {...rest}
    />
  );

  // Wrapper element (div or picture) with appropriate ARIA attributes
  const WrapperElement = wrapperAs as any;
  
  const wrapperStyle = fillContainer 
    ? { width: '100%', height: '100%', position: 'relative' as const, ...style } // Ensure parent has dimensions
    : { width: dimensions.width, height: dimensions.height, aspectRatio: aspectRatio ? `${aspectRatio}` : undefined, position: 'relative' as const, ...style };

  // If fillContainer, className from props should apply to the wrapper for sizing, not the image.
  // However, className often contains object-fit, which should apply to the image.
  // This is tricky. For now, className from props is passed to Image, assuming it contains object-fit.
  // If fillContainer, the wrapper takes full size of its parent.

  return (
    <WrapperElement 
      className={`relative ${fillContainer ? 'w-full h-full' : ''}`} 
      style={wrapperStyle}
    >
      {imageElement}
      
      {/* Enhanced loading placeholder with ARIA attributes for accessibility */}
      {loadState === 'loading' && showPlaceholder && (
        <div
          className={`absolute inset-0 bg-gray-100 animate-pulse ${fallbackClassName || ''}`}
          style={{ width: '100%', height: '100%' }} // Placeholder should always fill the wrapper
          aria-hidden="true"
        />
      )}
    </WrapperElement>
  );
};

// Use displayName for better debugging
LazyImage.displayName = 'LazyImage';

// Memoize the component to prevent unnecessary re-renders
export default memo(LazyImage);
