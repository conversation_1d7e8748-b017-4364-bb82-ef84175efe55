(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162],{3865:(e,s,r)=>{"use strict";r.d(s,{default:()=>i});var t=r(5155),a=r(2115),d=r(3310),l=r(6874),n=r.n(l);function i(){let[e,s]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[i,o]=(0,a.useState)(""),[m,c]=(0,a.useState)(!1),{resetPassword:u}=(0,d.A)(),x=async s=>{s.preventDefault(),l(""),o(""),c(!0);try{let{error:s}=await u(e);s?l(s.message||"Failed to send password reset email"):o("Password reset instructions have been sent to your email")}catch(e){console.error("Error during password reset:",e),l("An unexpected error occurred")}finally{c(!1)}};return(0,t.jsxs)("div",{className:"max-w-md w-full mx-auto p-6 bg-white rounded-lg shadow-md",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-gray-800",children:"Reset Password"}),r&&(0,t.jsx)("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md",children:r}),i&&(0,t.jsx)("div",{className:"mb-4 p-3 bg-green-100 text-green-700 rounded-md",children:i}),(0,t.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,t.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>s(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500",placeholder:"Enter your registered email"})]}),(0,t.jsx)("button",{type:"submit",disabled:m||!!i,className:"w-full bg-emerald-600 text-white py-2 px-4 rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:m?"Sending...":"Send Reset Link"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Remember your password?"," ",(0,t.jsx)(n(),{href:"/signin",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"Sign in"})]})})]})}},6923:(e,s,r)=>{Promise.resolve().then(r.bind(r,3865)),Promise.resolve().then(r.bind(r,3254))}},e=>{var s=s=>e(e.s=s);e.O(0,[844,6874,3063,3464,3120,2112,3254,8441,1684,7358],()=>s(6923)),_N_E=e.O()}]);