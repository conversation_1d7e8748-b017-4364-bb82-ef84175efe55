'use client';

import React, { useEffect } from 'react';
import ErrorBoundary from './ErrorBoundary';
import { useError } from '@/contexts/ErrorContext';

interface GlobalErrorBoundaryProps {
  children: React.ReactNode;
}

/**
 * A global error boundary component that integrates with the ErrorContext
 * to provide consistent error handling across the application.
 */
const GlobalErrorBoundary: React.FC<GlobalErrorBoundaryProps> = ({ children }) => {
  const { addErrorLog } = useError();

  // Handle unhandled promise rejections
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      addErrorLog(
        event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
        'unhandled-promise-rejection'
      );
    };

    // Add event listener for unhandled promise rejections
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Clean up event listener
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [addErrorLog]);

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        addErrorLog(error, 'react-error-boundary');
        
        // You could also send the error to an error reporting service here
        // Example: sendToErrorReportingService(error, errorInfo);
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default GlobalErrorBoundary;
