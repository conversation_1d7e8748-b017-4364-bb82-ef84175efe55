import { Suspense } from 'react';
import {
  getFeaturedClinics,
  getFeaturedPractitioners,
  getCategories,
  getBlogPosts
} from '@/lib/serverCache';
import ClientHomePage from './page.client';
import { mapBlogPostData } from '@/lib/dataMappers';
import { getStrapiMediaUrl } from '@/lib/mediaUtils'; // Import for processing image URLs

// Enable ISR with on-demand revalidation
export const revalidate = false;

// Add metadata through generateMetadata function
export const generateMetadata = async () => {
  // Fetch minimal data for the featured post's image for preloading
  // This is a simplified fetch; in a real app, you might have a more specific query
  // or derive this from a shared data fetching layer.
  let preloadLinks: Array<{ rel: string; as: string; href: string; type?: string; crossOrigin?: string }> = [];
  try {
    const blogPostsResponse = await getBlogPosts({
      pageSize: 1, // Only need the top featured one
      sort: ['isFeatured:desc', 'publishDate:desc'], // Prioritize featured, then latest
      populate: { featuredImage: true },
    });

    const firstPost = blogPostsResponse?.data?.[0]?.attributes;
    if (firstPost && firstPost.featuredImage?.data?.attributes?.url) {
      const imageUrl = getStrapiMediaUrl(firstPost.featuredImage.data.attributes.url);
      if (imageUrl) {
        preloadLinks.push({
          rel: 'preload',
          as: 'image',
          href: imageUrl,
          // Optionally, if you know the image type and it's consistent:
          // type: 'image/jpeg', // or 'image/webp' etc.
          // crossOrigin: 'anonymous', // If served from a different domain and CORS is set up
        });
      }
    }
  } catch (error) {
    console.error("Error fetching featured post for metadata preloading:", error);
    // Proceed without preloading if this fails
  }

  return {
    title: 'Natural Healing Now - Find Holistic Health Practitioners & Clinics',
    description: 'Connect with holistic health practitioners and natural healing clinics to support your wellness journey.',
    alternates: {
      canonical: '/',
    },
    other: {
      'cache-control': 'public, max-age=3600, stale-while-revalidate=86400',
    },
    links: preloadLinks, // Add preload links here
  };
};

export default async function HomePage() {
  // Fetch all data in parallel
  const [clinicsResponse, practitionersResponse, categoriesResponse, blogPostsResponse] =
    await Promise.all([
      getFeaturedClinics(),
      getFeaturedPractitioners(),
      getCategories(),
      // Fetch all blog posts we need in a single request with a larger pageSize and comprehensive populate
      getBlogPosts({
        pageSize: 8, // Enough to cover featured, latest, and popular posts
        sort: ['publishDate:desc'],
        populate: {
          featuredImage: true,
          seo: { // Populate the seo component
            populate: { // Populate fields within seo
              metaImage: true, // Populate the media field
              openGraph: true // Populate the nested openGraph component if it exists
            }
          },
          author_blogs: { // Populate the author relation
            fields: ['id', 'name', 'slug', 'bio'], // Added id field for author comparison
            populate: { // Populate nested relations within the author
              profilePicture: true // Populate the profile picture media relation
            }
          },
          blog_categories: { // Detailed population for categories
            fields: ['id', 'name', 'slug'] // Explicitly include fields needed for comparison
          },
          blog_tags: { // Detailed population for tags
            fields: ['id', 'name', 'slug'] // Explicitly include fields needed for comparison
          }
        }
      })
    ]);

  // Process the blog posts data
  // Ensure attributes are accessed correctly if the data structure is { id, attributes }
  const allBlogPosts = blogPostsResponse?.data?.map((post: any) => post.attributes ? { id: post.id, ...post.attributes } : post) || [];


  // Extract featured posts (if any have isFeatured flag)
  const featuredPosts = allBlogPosts
    .filter((post: any) => post.isFeatured)
    .slice(0, 1)
    .map(mapBlogPostData);

  // Extract latest posts (already sorted by publishDate:desc)
  const latestPosts = allBlogPosts
    .slice(0, 4) // Get first 4 posts for latest
    .map(mapBlogPostData);

  // For popular posts, we'll just use the latest posts as a fallback
  // In a real implementation, you might want to sort by view_count or another metric
  const popularPosts = allBlogPosts
    .slice(0, 4) // Get first 4 posts for popular
    .map(mapBlogPostData);

  // Prepare the data for the client component
  const pageData = {
    clinics: clinicsResponse?.data || [],
    practitioners: practitionersResponse?.data || [],
    categories: categoriesResponse?.data || [],
    featuredPosts: featuredPosts.length > 0 ? featuredPosts : [latestPosts[0]],
    latestPosts,
    popularPosts
  };

  // Render the client component with the pre-fetched data
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ClientHomePage initialData={pageData} />
    </Suspense>
  );
}
