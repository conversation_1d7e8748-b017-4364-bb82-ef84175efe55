{"kind": "collectionType", "collectionName": "payment_methods", "info": {"singularName": "payment-method", "pluralName": "payment-methods", "displayName": "Payment Method", "description": "Payment methods accepted by clinics"}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "clinics": {"type": "relation", "relation": "manyToMany", "target": "api::clinic.clinic", "mappedBy": "payment_methods"}}}