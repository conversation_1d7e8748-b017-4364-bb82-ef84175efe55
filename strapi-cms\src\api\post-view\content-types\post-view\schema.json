{"kind": "collectionType", "collectionName": "post_views", "info": {"singularName": "post-view", "pluralName": "post-views", "displayName": "Post View"}, "options": {"draftAndPublish": true}, "attributes": {"post": {"type": "relation", "relation": "oneToOne", "target": "api::blog-post.blog-post"}, "visitorId": {"type": "string", "required": true, "maxLength": 50}, "timestamp": {"type": "datetime"}, "referrer": {"type": "string", "maxLength": 255}}}