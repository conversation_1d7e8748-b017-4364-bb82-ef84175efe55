import Layout from '@/components/layout/Layout';
import CategoryCard from '@/components/categories/CategoryCard';
import Link from 'next/link';
// Removed FiSearch import
import { getStrapiContent } from '@/lib/strapi';
import ExploreFurther from '@/components/shared/ExploreFurther';
import SearchInput from '@/components/shared/SearchInput'; // Import SearchInput
import Pagination from '@/components/shared/Pagination'; // Import Pagination
import { Metadata } from 'next'; // Import Metadata type

// Use ISR with a long revalidation period (1 week = 604800 seconds)
// This will be the fallback revalidation time if on-demand revalidation fails
export const revalidate = 604800; // 1 week

// Force static rendering for this route segment
export const dynamic = 'force-static';

const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL; // Get base site URL

// Define types for category data based on the actual API response structure
interface StrapiCategory {
  id: string;
  documentId: string;
  name: string;
  slug: string;
  description?: string | null;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  icon: any; // Can be string or object with URL
  featuredImage: any; // Can be string or object with URL
}

// Define the transformed category data structure that matches CategoryCard props
interface TransformedCategory {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  icon: string | null;
  featured_image: string | null; // Ensure this matches CategoryCard prop name
}

// Transform Strapi data to match CategoryCard component props, adding fallbacks
function transformCategoryData(strapiCategory: StrapiCategory): TransformedCategory | null {
  // Basic validation
  if (!strapiCategory || !strapiCategory.name) {
    console.warn(`Skipping invalid category: ID ${strapiCategory?.id}`);
    return null;
  }

  // Debug logging for image data
  console.log(`Processing category ${strapiCategory.name}: Raw image data:`, {
    icon: strapiCategory.icon,
    featuredImage: strapiCategory.featuredImage
  });

  return {
    id: strapiCategory.id,
    name: strapiCategory.name || 'Unnamed Category', // Fallback name
    slug: strapiCategory.slug || `category-${strapiCategory.id}`, // Fallback slug
    description: strapiCategory.description,
    icon: strapiCategory.icon || null,
    featured_image: strapiCategory.featuredImage || null // Ensure this matches CategoryCard prop name
  };
}

// Define types for condition data
interface StrapiCondition {
  id: string;
  name: string;
  slug: string;
  // Add other fields if needed from Strapi
}

interface TransformedCondition {
  id: string;
  name: string;
  slug: string;
}

// Transform Strapi condition data
function transformConditionData(strapiCondition: StrapiCondition): TransformedCondition | null {
  if (!strapiCondition || !strapiCondition.name || !strapiCondition.slug) {
    console.warn(`Skipping invalid condition: ID ${strapiCondition?.id}`);
    return null;
  }
  return {
    id: strapiCondition.id,
    name: strapiCondition.name,
    slug: strapiCondition.slug,
  };
}

// Define the props for the page component to accept searchParams
interface CategoriesPageProps {
  searchParams?: {
    query?: string;
    page?: string;
  };
}

// Define a type for the raw Strapi data item
interface StrapiDataItem<T> {
  id: string;
  attributes: T;
}

// Define a type for the Strapi API response
interface StrapiApiResponse<T> {
  data: StrapiDataItem<T>[];
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// Note: We don't need generateStaticParams for the categories index page
// since it's a single page that uses searchParams for pagination and filtering.
// The page will be statically generated at build time and then use ISR for updates.

// Generate metadata for the categories listing page
export async function generateMetadata(): Promise<Metadata> {
  const title = "Explore Holistic Health Categories | Natural Healing Now";
  const description = "Discover different approaches to natural healing and find the right holistic health modality for your needs. Browse categories like Acupuncture, Naturopathy, and more.";
  const canonicalPath = "/categories";
  const canonicalUrl = SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath;

  return {
    title: title,
    description: description,
    alternates: {
      canonical: canonicalUrl,
    },
    // Add Open Graph and Twitter card data if desired
    // openGraph: {
    //   title: title,
    //   description: description,
    //   url: canonicalUrl,
    //   // images: [ ... ]
    // },
    // twitter: {
    //   card: 'summary_large_image',
    //   title: title,
    //   description: description,
    //   // images: [ ... ]
    // },
  };
}

export default async function CategoriesPage({ searchParams }: CategoriesPageProps) { // Destructure searchParams
  const query = searchParams?.query || ''; // Get query from searchParams
  const currentPage = Number(searchParams?.page) || 1; // Get page from searchParams

  // Define cache tags for ISR
  const cacheTags = ['strapi-categories'];

  // Add page-specific cache tag for pagination
  if (currentPage > 1) {
    cacheTags.push(`strapi-categories-page-${currentPage}`);
  }

  // Fetch categories from Strapi with proper cache configuration for ISR
  const categoryResponse = await getStrapiContent.categories.getAll({
    query,
    page: currentPage,
    next: {
      tags: cacheTags,
      revalidate: 604800 // 1 week - matches page-level revalidate
    }
  });

  const strapiCategories = categoryResponse?.data || [];
  const totalPages = categoryResponse?.meta?.pagination?.pageCount || 1; // Get total pages

  // Transform the data for rendering
  const transformedCategories = strapiCategories.map((category: any) => transformCategoryData(category as StrapiCategory));
  const categories = transformedCategories.filter((c: any): c is TransformedCategory => c !== null);

  // Fetch conditions from Strapi with proper cache configuration for ISR
  const conditionResponse = await getStrapiContent.conditions.getAll({
    next: {
      tags: ['strapi-conditions'],
      revalidate: 604800 // 1 week - matches page-level revalidate
    }
  });

  const strapiConditions = conditionResponse?.data || [];

  // Transform the data for rendering
  const transformedConditions = strapiConditions.map((condition: any) => transformConditionData(condition as StrapiCondition));
  const conditions = transformedConditions.filter((cond: any): cond is TransformedCondition => cond !== null);

  return (
    <>
      {/* Page Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Explore Holistic Health Categories</h1>
          <p className="text-lg max-w-3xl">
            Discover different approaches to natural healing and find the right holistic health modality for your needs.
          </p>
        </div>
      </div>

      {/* Search Section */}
      {/* Search Section - Replaced with SearchInput */}
      <div className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-md mx-auto">
             <SearchInput placeholder="Search categories" paramName="query" />
          </div>
        </div>
      </div>

      {/* Categories Grid */}
      <div className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            All Categories
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {categories.length > 0 ? (
              categories.map((category: TransformedCategory) => (
                <CategoryCard key={category.id} category={category} />
              ))
            ) : (
              <div className="col-span-4 text-center py-8">
                <p className="text-gray-500">No categories found. Please check back later.</p>
              </div>
            )}
          </div>

          {/* Pagination */}
          <div className="mt-12 flex justify-center">
             <Pagination totalPages={totalPages} />
          </div>
        </div>
      </div>

      {/* Conditions Section */}
      <div className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            Specialists by Health Conditions
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {conditions.length > 0 ? (
              conditions.map((condition: TransformedCondition) => ( // Add type TransformedCondition
                <Link
                  key={condition.id}
                  href={`/conditions/${condition.slug}`} // Use slug directly
                  className="bg-gray-50 hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors"
                >
                  <span className="text-gray-800 font-medium">{condition.name}</span>
                </Link>
              ))
            ) : (
              <div className="col-span-full text-center py-8"> {/* Use col-span-full */}
                <p className="text-gray-500">No health conditions found. Please check back later.</p>
              </div> // Correct closing tag to </div>
            )} {/* Correct placement for closing parenthesis */}
          </div>
        </div>
      </div>

      {/* Replace Call to Action with ExploreFurther component */}
      <ExploreFurther />
    </>
  );
}
