(()=>{var e={};e.id=8421,e.ids=[8421],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return u},throwForSearchParamsAccessInUseCache:function(){return i},throwWithStaticGenerationBailoutError:function(){return s},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return o}});let n=r(80023),a=r(3295);function s(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function o(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function i(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function u(){let e=a.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43763:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54086:(e,t,r)=>{"use strict";r.d(t,{M4:()=>u,lX:()=>c});var n=r(44999),a=r(33376),s=r.n(a);let o="https://nice-badge-2130241d6c.strapiapp.com";console.log("Server API Client using URL:",o||"No API URL found in environment variables"),o||console.error("CRITICAL ERROR: No Strapi API URL found in environment variables. Please set NEXT_PUBLIC_API_URL in your Vercel environment variables.");let i=()=>{if(!o)throw Error("No Strapi API URL found in environment variables. Please set NEXT_PUBLIC_API_URL.");return`${o}/api`},u=async(e,t={})=>{let r=process.env.STRAPI_API_TOKEN,n=i(),a=`${n}${e.startsWith("/")?e:`/${e}`}`,o={"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`},...t.headers||{}};if(t.params&&("GET"===t.method||!t.method)){let e=s().stringify(t.params,{encodeValuesOnly:!0});e&&(a=`${a}?${e}`)}let{params:u,...c}=t;try{let t=c.next||{};if(void 0===t.revalidate&&!1!==t.revalidate&&(t.revalidate=3600),!t.tags||0===t.tags.length){let r=e.split("/")[0].replace(/^\/+/,"");r&&(t.tags=[`strapi-${r}`])}let r=await fetch(a,{...c,headers:o,next:t});if(!r.ok){let e;try{e=await r.json()}catch(t){e={message:r.statusText,details:await r.text().catch(()=>"")}}console.error(`Server: API Error (${a}): Status ${r.status}`,e);let t=Error(`API Error: ${r.status} ${r.statusText}`);throw t.response={status:r.status,data:e},t}return r.json()}catch(e){throw console.error(`Server: Error fetching from API (${a}):`,e.message||e),e.response&&(console.error("Server: Response status:",e.response.status),console.error("Server: Response data:",e.response.data)),e}},c=async(e,t={})=>{let r=(0,n.UL)(),a=r.get("jwt")?.value,o=i(),u=`${o}${e.startsWith("/")?e:`/${e}`}`,c={"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`},...t.headers||{}};if(t.params&&("GET"===t.method||!t.method)){let e=s().stringify(t.params,{encodeValuesOnly:!0});e&&(u=`${u}?${e}`)}let{params:l,...d}=t;try{let e=await fetch(u,{...d,headers:c});if(!e.ok){let t;try{t=await e.json()}catch(r){t={message:e.statusText,details:await e.text().catch(()=>"")}}console.error(`Server: API Error with user auth (${u}): Status ${e.status}`,t);let r=Error(`API Error with user auth: ${e.status} ${e.statusText}`);throw r.response={status:e.status,data:t},r}return e.json()}catch(e){throw console.error(`Server: Error fetching from API with user auth (${u}):`,e.message||e),e.response&&(console.error("Server: Response status:",e.response.status),console.error("Server: Response data:",e.response.data)),e}}},58276:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var n={};r.r(n),r.d(n,{GET:()=>l});var a=r(96559),s=r(48088),o=r(37719),i=r(32190),u=r(44999),c=r(54086);async function l(e){let t=(0,u.UL)();if(!t.get("jwt")?.value)return i.NextResponse.json({error:"Unauthorized - No token provided"},{status:401});try{let e=await (0,c.lX)("/users/me");return i.NextResponse.json({message:"Authentication successful",user:{id:e.id,username:e.username,email:e.email}})}catch(e){if(e.response?.status===401)return i.NextResponse.json({error:"Unauthorized - Invalid or expired token"},{status:401});return console.error("Error in protected API route:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/protected-data/route",pathname:"/api/protected-data",filename:"route",bundlePath:"app/api/protected-data/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\protected-data\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:h}=d;function y(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return s}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function a(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let s=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return u}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=s?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(61120));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let s={current:null},o="function"==typeof n.cache?n.cache:e=>e,i=console.warn;function u(e){return function(...t){i(e(...t))}}o(e=>{try{i(s.current)}finally{s.current=null}})},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,1330,3376,580,4999],()=>r(58276));module.exports=n})();