import Link from 'next/link';
import { Suspense } from 'react'; // Import Suspense
import { getStrapiContent } from '@/lib/strapi'; // Import the API functions
import { notFound } from 'next/navigation'; // Import notFound for handling missing data
import { Metadata } from 'next'; // Import Metadata type
import MarkdownContent from '@/components/blog/MarkdownContent'; // Import MarkdownContent
import { getStrapiMediaUrl, getProfilePictureUrl, getOgImageUrl } from '@/lib/mediaUtils'; // Import media utility functions
import PractitionerProfileImage from '@/components/practitioners/PractitionerProfileImage'; // Import our new Client Component
import VerifiedBadge from '@/components/practitioners/VerifiedBadge'; // Import our new VerifiedBadge component
import EmailContact from '@/components/practitioners/EmailContact'; // Import our new EmailContact component
import EmailContactDetail from '@/components/practitioners/EmailContactDetail'; // Import our new EmailContactDetail component
import VideoEmbed from '@/components/practitioners/VideoEmbed'; // Import our new VideoEmbed component
import StructuredData from '@/components/shared/StructuredData'; // Import our new StructuredData component
import { getPractitionerBySlug } from '@/lib/serverCache'; // Import cached function
import PractitionerPrefetcher from '@/components/practitioners/PractitionerPrefetcher'; // Import our new prefetcher component

// Generate static paths for all practitioners at build time
export async function generateStaticParams() {
  try {
    // In Next.js 15, we need to explicitly opt-in to caching
    const response = await getStrapiContent.practitioners.getAllSlugs({
      cache: 'force-cache', // Explicitly cache for generateStaticParams
      next: {
        revalidate: 43200, // Revalidate slugs every 12 hours
        tags: ['strapi-practitioners-slugs'] // Tag for revalidation
      }
    });

    // Ensure response and response.data are not null and response.data is an array
    if (response && response.data && Array.isArray(response.data)) {
      // Filter out any items that might be null or don't have a slug property
      return response.data
        .filter(item => item && typeof item.slug === 'string')
        .map(item => ({
          slug: item.slug,
        }));
    }

    // Log an error or return an empty array if data is not as expected
    console.error('Failed to fetch practitioner slugs or data is not in expected format:', response);
    return [];
  } catch (error) {
    console.error('Error fetching practitioner slugs for generateStaticParams:', error);
    return []; // Return empty array on error to prevent build failure
  }
}

// Enable ISR with time-based revalidation (12 hours)
// This will also allow on-demand revalidation via webhooks
export const revalidate = 43200; // 12 hours in seconds
// Only allow static paths from generateStaticParams
export const dynamicParams = false;

// Define the site URL from environment variable with proper fallback to your actual domain
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log the URL being used for debugging
if (process.env.NODE_ENV === 'production') {
  console.log(`Using site URL for canonical URLs: ${SITE_URL}`);
}

// Define the expected structure for nested data
type ClinicStub = { // Direct structure from API
  id: string;
  name: string;
  slug: string;
};

type EducationItem = {
  id?: string;
  degree: string;
  institution: string;
  year: string;
};

// Define the direct data structure expected from getBySlug for the practitioner itself
type PractitionerData = {
  id: string;
  name: string;
  slug: string;
  isVerified?: boolean; // Add isVerified field
  title?: string | null;
  qualifications?: string | null;
  bio?: string | null;
  profilePicture?: {
    data?: { attributes?: { url?: string } } | Array<{ attributes?: { url?: string } }>;
    url?: string;
    attributes?: { url?: string };
  } | string | null; // Handle all possible Strapi structures
  videoEmbed?: { url?: string; oembed?: { html?: string }; } | null; // Keep nested
  specialties?: Array<{ id: string; name: string; slug: string }> | null; // Added slug
  conditions?: Array<{ id: string; name: string; slug: string }> | null; // Added slug
  education?: EducationItem[] | null; // Keep direct array of objects
  clinics?: Array<ClinicStub> | null; // Direct array based on API response
  contactInfo?: { emailAddress?: string; } | null; // Keep nested
  seo?: {
    metaTitle?: string | null;
    metaDescription?: string | null;
    metaImage?: { url?: string } | null;
    canonicalURL?: string | null;
    openGraph?: {
      ogTitle?: string | null;
      ogDescription?: string | null;
      ogImage?: { url?: string } | null;
      ogUrl?: string | null;
      ogType?: string | null;
      title?: string | null;
      description?: string | null;
      url?: string | null;
      type?: string | null;
      siteName?: string | null;
      image?: { url?: string } | null;
    } | null;
    structuredData?: string | object | null; // Allow object too
  } | null;
};

// This function fetches the actual data using the cached function with proper ISR support
async function getPractitionerData(slug: string, prefetchedData?: any): Promise<PractitionerData | null> {
  // If we have prefetched data, use it directly
  if (prefetchedData && prefetchedData.id && prefetchedData.name && prefetchedData.slug) {
    console.log(`Using prefetched data for practitioner ${slug}`);
    return prefetchedData as PractitionerData;
  }

  try {
    // Use direct API call with explicit ISR options for Next.js 15
    // In Next.js 15, fetch is uncached by default, so we need to explicitly opt-in to caching
    const response = await getStrapiContent.practitioners.getBySlug(slug, {
      cache: 'force-cache', // Explicitly opt-in to caching for Next.js 15
      next: {
        revalidate: 43200, // Match the page's revalidation period (12 hours)
        tags: ['strapi-practitioner', `strapi-practitioner-${slug}`] // Updated tag to singular 'strapi-practitioner'
      }
    });

    // Only log errors, not regular responses to avoid dynamic rendering
    // Debug logging removed to ensure static rendering

    // Assuming getBySlug returns { data: [ { id: ..., name: ..., slug: ... } ] }
    if (response?.data && response.data.length > 0) {
      // Get the first item
      const practitioner = response.data[0];

      // Validate that we have the minimum required fields
      if (!practitioner.id || !practitioner.name || !practitioner.slug) {
        console.error(`Practitioner data for slug ${slug} is missing required fields:`, {
          hasId: !!practitioner.id,
          hasName: !!practitioner.name,
          hasSlug: !!practitioner.slug
        });
        return null;
      }

      // Return the practitioner data
      return practitioner as PractitionerData;
    }

    // No data found
    console.warn(`No practitioner found with slug ${slug}`);
    return null;
  } catch (error) {
    console.error(`Error fetching practitioner with slug ${slug}:`, error);

    // Log additional information about the error
    if (error instanceof Error) {
      console.error(`Error name: ${error.name}, message: ${error.message}`);
      console.error(`Error stack: ${error.stack}`);
    }

    return null;
  }
}

// Generate Metadata
export async function generateMetadata({ params, searchParams }: { params: { slug: string }, searchParams: { prefetched?: string } }): Promise<Metadata> {
  // In Next.js 15, params is a Promise that needs to be awaited
  const awaitedParams = await params;
  const { slug } = awaitedParams;
  // Accessing searchParams here is generally safe for metadata as it runs on the server.
  // However, for ISR, the page component's usage is more critical.
  // const isPrefetched = searchParams?.prefetched === 'true'; 

  // We'll pass null for prefetchedData here since we don't have access to it in generateMetadata
  // The actual page component will handle prefetched data
  const practitioner = await getPractitionerData(slug);

  if (!practitioner) {
    return {
      title: 'Practitioner Not Found | Natural Healing Now',
      description: 'The requested practitioner could not be found.',
      // No canonical for not found pages
    };
  }

  // No need for strapiBaseUrl as we're using the utility functions

  // --- SEO Data Extraction ---
  const seoData = practitioner.seo;
  const fallbackTitle = `${practitioner.name} - ${practitioner.title || 'Holistic Health Practitioner'} | Natural Healing Now`;
  const fallbackDescription = practitioner.bio?.substring(0, 160) || `Learn more about ${practitioner.name}, a ${practitioner.title || 'holistic health practitioner'}.`;

  const metaTitle = seoData?.metaTitle || fallbackTitle;
  const metaDescription = seoData?.metaDescription || fallbackDescription;

  const canonicalPath = `/practitioners/${practitioner.slug}`;
  const canonicalUrl = seoData?.canonicalURL || (SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath);

  // Determine OG/Twitter Image URL (prioritize OG from SEO plugin, then meta, then profile picture)
  let imageUrl: string | undefined = undefined;
  const ogImageFromSeo = seoData?.openGraph?.ogImage?.url; // Corrected: ogImage
  const metaImageUrl = seoData?.metaImage?.url;

  // Get profile picture URL using the utility function
  const profilePictureUrl = getProfilePictureUrl(practitioner);

  // Use the new getOgImageUrl function to get proper Strapi media URLs
  if (ogImageFromSeo) { // Corrected: ogImageFromSeo
    imageUrl = getOgImageUrl(ogImageFromSeo);
  } else if (metaImageUrl) {
    imageUrl = getOgImageUrl(metaImageUrl);
  } else if (profilePictureUrl) {
    imageUrl = getOgImageUrl(profilePictureUrl);
  }

  // Debug logging removed to ensure static rendering

  const ogImages = imageUrl ? [{ url: imageUrl }] : [];

  // Define allowed Open Graph types
  type OgType = "article" | "website" | "book" | "profile" | "music.song" | "music.album" | "music.playlist" | "music.radio_station" | "video.movie" | "video.episode" | "video.tv_show" | "video.other";
  const allowedOgTypes: OgType[] = ["article", "website", "book", "profile", "music.song", "music.album", "music.playlist", "music.radio_station", "video.movie", "video.episode", "video.tv_show", "video.other"];

  // Validate or fallback ogType
  let ogType: OgType = 'profile'; // Default to 'profile' for practitioners
  const strapiOgType = seoData?.openGraph?.ogType; // Corrected: ogType
  if (strapiOgType && (allowedOgTypes as string[]).includes(strapiOgType)) {
    ogType = strapiOgType as OgType;
  }

  // --- Metadata Object ---
  return {
    title: metaTitle,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    // Use openGraph data from SEO plugin if available
    openGraph: seoData?.openGraph ? {
      title: seoData.openGraph.ogTitle || metaTitle, // Corrected: ogTitle
      description: seoData.openGraph.ogDescription || metaDescription, // Corrected: ogDescription
      url: seoData.openGraph.ogUrl || canonicalUrl, // Corrected: ogUrl
      type: ogType, // Use the validated type
      // siteName: 'Natural Healing Now', // siteName is not standard and not in PractitionerData.seo.openGraph with 'og' prefix
      images: ogImages,
    } : {
      // Fallback if no openGraph data in SEO plugin
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      type: ogType,
      images: ogImages,
      // siteName: 'Natural Healing Now',
    },
    twitter: { // Add Twitter card data
      card: 'summary_large_image',
      title: seoData?.openGraph?.ogTitle || metaTitle, // Corrected: ogTitle
      description: seoData?.openGraph?.ogDescription || metaDescription, // Corrected: ogDescription
      images: ogImages, // Use the same image array
      // site: '@YourTwitterHandle', // Optional: Add Twitter handle
      // creator: '@CreatorHandle', // Optional: Add creator handle if applicable
    },
  };
}

export default async function PractitionerDetailPage({ params, searchParams }: { params: { slug: string }, searchParams: { prefetched?: string } }) {
  // In Next.js 15, params is a Promise that needs to be awaited
  const awaitedParams = await params;
  const { slug } = awaitedParams;
  // We will pass searchParams to the PractitionerPrefetcher which is a client component
  // and will use useSearchParams() hook.

  // If this is a prefetched navigation, we'll get the data from the client-side cache
  // The actual data fetching will happen in the client component (PractitionerPrefetcher)
  // For server-side rendering, we'll fetch the data from the server
  const practitioner = await getPractitionerData(slug);

  // If practitioner not found, use Next.js notFound function
  if (!practitioner) {
    notFound();
  }

  // Access properties directly from the fetched practitioner object
  const {
    name = 'Unnamed Practitioner',
    title = null,
    qualifications = null,
    bio = 'No biography available.',
    // We're handling profilePicture separately with getProfilePictureUrl
    videoEmbed = null, // Access nested below
    specialties = null, // Access nested below
    conditions = null, // Access nested below
    education = null, // Access nested below
    clinics = null, // Access nested below
    contactInfo = null, // Access nested below
    isVerified = false, // Destructure isVerified, default to false
    seo = null // Destructure seo
  } = practitioner; // Destructure directly from practitioner

  // Use the getProfilePictureUrl utility function to handle all the different Strapi structures
  let profilePictureUrl = null;
  try {
    // Debug logging removed to ensure static rendering

    // Use the utility function that handles all possible Strapi structures
    if (practitioner.profilePicture) {
      // First try using the direct object
      profilePictureUrl = getProfilePictureUrl(practitioner);

      // If that fails, try passing just the profilePicture field
      if (!profilePictureUrl && typeof practitioner.profilePicture === 'object') {
        profilePictureUrl = getStrapiMediaUrl(practitioner.profilePicture, { debug: false });
      }
    }

    // Debug logging removed to ensure static rendering
  } catch (error) {
    console.error('Error processing profile picture URL:', error);
    profilePictureUrl = null;
  }

  // Prioritize constructing iframe from URL for consistent responsive styling
  const videoHtml = videoEmbed?.url ? `<iframe src="${videoEmbed.url.replace('watch?v=', 'embed/')}" title="${name} Video" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: 0;"></iframe>` : videoEmbed?.oembed?.html || null;

  // Ensure structuredData is a string before rendering
  let structuredDataString: string | null = null;
  if (seo?.structuredData) {
    if (typeof seo.structuredData === 'string') {
      structuredDataString = seo.structuredData;
    } else if (typeof seo.structuredData === 'object') {
      try {
        structuredDataString = JSON.stringify(seo.structuredData);
      } catch (e) {
        console.error("Failed to stringify structuredData object:", e);
      }
    }
  }

  return (
    <> {/* Use Fragment instead of Layout */}
      {/* Wrap PractitionerPrefetcher in Suspense */}
      <Suspense fallback={null}> {/* Fallback can be null or a simple loading indicator */}
        <PractitionerPrefetcher practitioner={practitioner} />
      </Suspense>

      {/* Breadcrumb */}
      <div className="bg-gray-100 py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-emerald-600">Home</Link>
            <span className="mx-2">/</span>
            <Link href="/practitioners" className="hover:text-emerald-600">Practitioners</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-800">{name}</span>
          </div>
        </div>
      </div>

      {/* Practitioner Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row md:items-center">
            {/* Profile Picture - Using Client Component */}
            <div className="mb-4 md:mb-0 md:mr-6 flex-shrink-0">
              <PractitionerProfileImage
                imageUrl={profilePictureUrl}
                name={name}
              />
            </div>

            <div>
              <div className="flex items-center gap-x-2 mb-1"> {/* Wrapper for name and verified status */}
                <h1 className="text-3xl font-bold text-gray-800">{name}</h1>
                <VerifiedBadge isVerified={isVerified} />
              </div>
              {title && <p className="text-xl text-gray-600 mb-2">{title}</p>}
              {qualifications && <p className="text-gray-500 mb-3">{qualifications}</p>}
              <EmailContact emailAddress={contactInfo?.emailAddress} />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Column */}
          <div className="lg:col-span-2">
            {/* Bio Section */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">About</h2>
              <div className="bg-white rounded-lg shadow-sm p-6">
                {/* Use MarkdownContent for bio, disable nofollow */}
                <MarkdownContent content={bio || ''} applyNoFollow={false} />

                {/* Video embedded */}
                <VideoEmbed videoHtml={videoHtml} />
              </div>
            </section>

            {/* Specialties Section */}
            {specialties && specialties.length > 0 && (
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Specialties</h2>
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {specialties.map((specialty) => (
                      <li key={specialty.id} className="flex items-center">
                        <span className="h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"></span>
                        <Link href={`/specialities/${specialty.slug}`} className="no-underline hover:text-emerald-600">
                          {specialty.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            )}

            {/* Conditions Treated Section */}
            {conditions && conditions.length > 0 && (
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Conditions Treated</h2>
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {conditions.map((condition) => (
                      <li key={condition.id} className="flex items-center">
                        <span className="h-2 w-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"></span>
                        <Link href={`/conditions/${condition.slug}`} className="no-underline hover:text-emerald-600">
                          {condition.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            )}

            {/* Education Section */}
            {education && education.length > 0 && (
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Education & Training</h2>
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <ul className="space-y-4">
                    {education.map((edu, index) => (
                      <li key={edu.id || index}> {/* Use edu.id if available */}
                        <p className="font-medium text-gray-800">{edu.degree}</p>
                        <p className="text-gray-600">{edu.institution}, {edu.year}</p>
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            )}
          </div>

          {/* Sidebar */}
          <div>
            {/* Clinics Section */}
            {clinics && clinics.length > 0 && (
              <section className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 className="font-bold text-gray-800 mb-4">Practices At</h3>
                <ul className="space-y-4">
                  {clinics.map((clinic) => (
                    <li key={clinic.id}>
                      <Link
                        href={`/clinics/${clinic.slug}`}
                        className="text-emerald-600 hover:text-emerald-700 font-medium"
                      >
                        {clinic.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </section>
            )}

            {/* Contact Section */}
            {contactInfo?.emailAddress && (
              <section className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 className="font-bold text-gray-800 mb-4">Contact</h3>
                <div className="space-y-3">
                  <EmailContactDetail emailAddress={contactInfo.emailAddress} />
                  {/* Add phone number if available in contactInfo */}
                </div>
              </section>
            )}

            {/* Book Appointment CTA */}
            {clinics && clinics.length > 0 && (
              <section className="bg-emerald-50 rounded-lg p-6 border border-emerald-100">
                <h3 className="font-bold text-gray-800 mb-3">Interested in an Appointment?</h3>
                <p className="text-gray-600 mb-4">Contact one of the clinics where {name} practices to schedule a consultation.</p>
                <Link
                  href={`/clinics/${clinics[0].slug}`}
                  className="block w-full bg-emerald-600 hover:bg-emerald-700 text-white text-center py-3 rounded-lg font-medium"
                >
                  View Clinic Details
                </Link>
              </section>
            )}
          </div>
        </div>
      </div>
      {/* Removed ExploreFurther component */}

      {/* Structured Data (JSON-LD) */}
      <StructuredData data={structuredDataString} />
    </> // Close Fragment
  );
}
