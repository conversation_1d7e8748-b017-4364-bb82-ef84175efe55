(()=>{var e={};e.id=879,e.ids=[879],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5059:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\auth\\\\SignUpForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\auth\\SignUpForm.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17567:(e,r,t)=>{Promise.resolve().then(t.bind(t,5059))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38255:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>a});var s=t(37413),n=t(5059);let a={title:"Sign Up - Natural Healing Now",description:"Create a new account on Natural Healing Now"};function o(){return(0,s.jsx)("div",{className:"container mx-auto py-12 px-4",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)(n.default,{})})})}},54519:(e,r,t)=>{Promise.resolve().then(t.bind(t,67325))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67325:(e,r,t)=>{"use strict";t.d(r,{default:()=>d});var s=t(60687),n=t(43210),a=t(6363),o=t(16189),i=t(85814),l=t.n(i);function d(){let[e,r]=(0,n.useState)(""),[t,i]=(0,n.useState)(""),[d,u]=(0,n.useState)(""),[c,p]=(0,n.useState)(""),[m,x]=(0,n.useState)(""),[g,h]=(0,n.useState)(!1),{signUp:f}=(0,a.A)();(0,o.useRouter)();let b=async r=>{if(r.preventDefault(),p(""),x(""),h(!0),t!==d){p("Passwords do not match"),h(!1);return}if(t.length<8){p("Password must be at least 8 characters long"),h(!1);return}try{let r=e.split("@")[0],{error:s}=await f(r,e,t);s?p(s.message||"Failed to sign up"):x("Sign up successful! Please check your email to confirm your account.")}catch(e){console.error("Error during sign up:",e),p("An unexpected error occurred")}finally{h(!1)}};return(0,s.jsxs)("div",{className:"max-w-md w-full mx-auto p-6 bg-white rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-gray-800",children:"Create an Account"}),c&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md",children:c}),m&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-green-100 text-green-700 rounded-md",children:m}),(0,s.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,s.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>r(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,s.jsx)("input",{id:"password",type:"password",value:t,onChange:e=>i(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Must be at least 8 characters long"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),(0,s.jsx)("input",{id:"confirmPassword",type:"password",value:d,onChange:e=>u(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"})]}),(0,s.jsx)("button",{type:"submit",disabled:g||!!m,className:"w-full bg-emerald-600 text-white py-2 px-4 rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:g?"Signing up...":"Sign Up"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/signin",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"Sign in"})]})})]})}},72698:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.default,__next_app__:()=>u,pages:()=>d,routeModule:()=>c,tree:()=>l});var s=t(65239),n=t(48088),a=t(31369),o=t(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(r,i);let l={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38255)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\signup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\signup\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,1330,3376,6391,2975,8446,270],()=>t(72698));module.exports=s})();