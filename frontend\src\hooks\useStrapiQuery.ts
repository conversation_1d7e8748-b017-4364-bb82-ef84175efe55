'use client';

import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { fetchAPI } from '@/lib/strapi';
import { AxiosRequestConfig } from 'axios';

/**
 * Custom hook for fetching data from Strapi with React Query
 * 
 * @param endpoint - The Strapi API endpoint to fetch from
 * @param params - Query parameters to send with the request
 * @param options - Additional React Query options
 * @returns The React Query result object
 */
export function useStrapiQuery<TData = any, TError = Error>(
  endpoint: string,
  params?: Record<string, any>,
  options?: Omit<UseQueryOptions<TData, TError, TData>, 'queryKey' | 'queryFn'>
) {
  // Create a stable query key based on the endpoint and params
  const queryKey = params ? [endpoint, params] : [endpoint];

  return useQuery<TData, TError>({
    queryKey,
    queryFn: async () => {
      const axiosOptions: AxiosRequestConfig = {};
      if (params) {
        axiosOptions.params = params;
      }
      
      const data = await fetchAPI(endpoint, axiosOptions);
      return data as TData;
    },
    ...options,
  });
}

/**
 * Custom hook for fetching featured clinics from Strapi
 */
export function useFeaturedClinics(options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiQuery(
    '/clinics',
    {
      filters: { isFeatured: { $eq: true } },
      populate: '*'
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
}

/**
 * Custom hook for fetching featured practitioners from Strapi
 */
export function useFeaturedPractitioners(options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiQuery(
    '/practitioners',
    {
      filters: { isFeatured: { $eq: true } },
      populate: '*'
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
}

/**
 * Custom hook for fetching categories from Strapi
 */
export function useCategories(pageSize = 4, options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiQuery(
    '/categories',
    {
      pagination: { pageSize },
      populate: '*'
    },
    {
      staleTime: 10 * 60 * 1000, // 10 minutes
      ...options,
    }
  );
}

/**
 * Custom hook for fetching featured blog posts from Strapi
 */
export function useFeaturedBlogPosts(options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiQuery(
    '/blog-posts',
    {
      filters: { isFeatured: { $eq: true } },
      sort: 'publishDate:desc',
      pagination: { page: 1, pageSize: 1 },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        }
      }
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
}

/**
 * Custom hook for fetching latest blog posts from Strapi
 */
export function useLatestBlogPosts(pageSize = 3, options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiQuery(
    '/blog-posts',
    {
      sort: 'publishDate:desc',
      pagination: { page: 1, pageSize },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        }
      }
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
}

/**
 * Custom hook for fetching popular blog posts from Strapi
 */
export function usePopularBlogPosts(pageSize = 4, options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiQuery(
    '/blog-posts',
    {
      sort: 'publishDate:desc', // Use publishDate as a fallback
      pagination: { page: 1, pageSize },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        }
      }
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      ...options,
    }
  );
}
