'use client';

import React from 'react';
import { useEffect } from 'react';
import ErrorFallback from '@/components/shared/ErrorFallback';
import { useError } from '@/contexts/ErrorContext';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Global error page for the Next.js application
 * This is used by Next.js when an error occurs in a route segment
 */
export default function ErrorPage({ error, reset }: ErrorPageProps) {
  const { addErrorLog } = useError();
  
  useEffect(() => {
    // Log the error to our error context
    addErrorLog(error, 'next-error-page');
  }, [error, addErrorLog]);

  return (
    <div className="container mx-auto px-4 py-8">
      <ErrorFallback
        error={error}
        resetErrorBoundary={reset}
        message="Sorry, something went wrong while loading this page."
        showHomeLink={true}
        showRefreshButton={true}
      />
    </div>
  );
}
