# Blog Webhook Setup for Strapi

This guide will help you manually set up webhooks in Strapi to trigger on-demand revalidation for blog-related content in your Next.js application.

## Prerequisites

- Access to the Strapi admin panel
- The revalidation token: `3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
- The Next.js site URL: `https://www.naturalhealingnow.com`

## Step 1: Access the Webhooks Section

1. Log in to your Strapi admin panel at `https://nice-badge-2130241d6c.strapiapp.com/admin`
2. Navigate to Settings > Webhooks

## Step 2: Configure Webhooks for Blog-Related Content Types

You need to create a webhook for each blog-related content type that should trigger revalidation. Here are the configurations for each:

### Blog Posts Webhook

1. **Name**: `Revalidate Blog Posts`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
   - `Authorization`: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
4. **Events**:
   - Select all events under "Entry" for the "blog-post" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`

### Blog Categories Webhook

1. **Name**: `Revalidate Blog Categories`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
   - `Authorization`: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
4. **Events**:
   - Select all events under "Entry" for the "blog-category" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`

### Blog Tags Webhook

1. **Name**: `Revalidate Blog Tags`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
   - `Authorization`: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
4. **Events**:
   - Select all events under "Entry" for the "blog-tag" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`

### Blog Authors Webhook

1. **Name**: `Revalidate Blog Authors`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
   - `Authorization`: `Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=`
4. **Events**:
   - Select all events under "Entry" for the "author" or "author-blog" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`

## Step 3: Test the Webhooks

After configuring the webhooks, you should test them to ensure they're working correctly:

1. Make a change to a blog post, category, tag, or author in Strapi
2. Publish the change
3. Check the Strapi webhook logs to ensure the webhook was triggered
4. Check the Vercel logs to ensure the revalidation API was called
5. Visit the page on your site to ensure the change is reflected

## Troubleshooting

If the webhooks aren't working as expected, check the following:

1. **Webhook URL**: Make sure the webhook URL is correct and accessible from the internet.
2. **Authorization Header**: Verify that the Authorization header is correctly formatted with the Bearer token.
3. **Content Type**: Ensure the content type in the request body matches the content type in Strapi.
4. **Events**: Make sure the correct events are selected for each webhook.

## Testing with cURL

You can test the revalidation API directly using cURL:

```bash
# Test blog post revalidation
curl -X POST https://www.naturalhealingnow.com/api/revalidate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=" \
  -d '{"event": "entry.update", "model": "api::blog-post.blog-post", "entry": {"id": 123, "slug": "test-blog-post"}}'

# Test blog category revalidation
curl -X POST https://www.naturalhealingnow.com/api/revalidate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=" \
  -d '{"event": "entry.update", "model": "api::blog-category.blog-category", "entry": {"id": 456, "slug": "test-category"}}'

# Test blog tag revalidation
curl -X POST https://www.naturalhealingnow.com/api/revalidate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=" \
  -d '{"event": "entry.update", "model": "api::blog-tag.blog-tag", "entry": {"id": 789, "slug": "test-tag"}}'

# Test blog author revalidation
curl -X POST https://www.naturalhealingnow.com/api/revalidate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=" \
  -d '{"event": "entry.update", "model": "api::author.author", "entry": {"id": 101, "slug": "test-author"}}'
```

## Using the Node.js Test Script

You can also use the provided Node.js test script to test the revalidation API:

```bash
node scripts/test-blog-revalidation.js
```

This script will test the revalidation API for blog posts, categories, tags, and authors.
