import { cache } from 'react';
import { fetchFromStrapi, StrapiResponse, createErrorResponse } from '../strapiClient'; // Import createErrorResponse
import { Category, Clinic, GlobalSettings } from '@/types/strapi-types'; // Adjust path as needed

export interface AggregatedGlobalData {
  categories?: Category[];
  featuredClinics?: Clinic[];
  settings?: GlobalSettings | null;
  // Add other global data types as needed
}

// This function fetches multiple common data sources in parallel,
// leveraging React.cache for deduplication if called multiple times in a render pass.
export const getAggregatedGlobalData = cache(
  async (): Promise<AggregatedGlobalData> => {
    // Define all fetches as promises
    const categoriesPromise = fetchFromStrapi<Category[]>('categories', {
      fields: ['name', 'slug', 'description'], // Select only necessary fields
      sort: ['name:asc'],
      pagination: { pageSize: 50 } // Adjust as needed, or remove if you want all
    }, {
      next: {
        tags: ['strapi-categories', 'strapi-global'],
        revalidate: 3600, // 1 hour
      },
    });

    const featuredClinicsPromise = fetchFromStrapi<Clinic[]>('clinics', {
      filters: { isFeatured: { $eq: true } },
      fields: ['name', 'slug', 'description'],
      populate: {
        logo: { fields: ['url', 'alternativeText'] },
        categories: { fields: ['name', 'slug'] }, // Only names and slugs for categories
      },
      pagination: { pageSize: 6 }, // Limit to a small number for featured items
    }, {
      next: {
        tags: ['strapi-featured-clinics', 'strapi-clinics', 'strapi-global'],
        revalidate: 1800, // 30 minutes
      },
    });

    // Assuming 'setting' is a single type in Strapi for global site settings
    // If it's a collection type, adjust accordingly or fetch by a specific ID/slug
    const settingsPromise = fetchFromStrapi<GlobalSettings | GlobalSettings[]>('setting', { // Endpoint might be 'global-settings' or similar
      populate: {
        logo: { fields: ['url', 'alternativeText'] },
        favicon: { fields: ['url', 'alternativeText'] },
      },
      // For single types, Strapi often returns the object directly, not in an array.
      // The StrapiResponse<T> should handle if T is an object or T[].
      // If 'setting' is a single type, the response.data will be GlobalSettings, not GlobalSettings[]
    }, {
      next: {
        tags: ['strapi-settings', 'strapi-global'],
        revalidate: 7200, // 2 hours
      },
    });

    // Await all promises in parallel
    const [
      categoriesResponse,
      featuredClinicsResponse,
      settingsDataResponse, // Renamed to avoid conflict if settings is an array
    ] = await Promise.all([
      categoriesPromise,
      featuredClinicsPromise,
      settingsPromise,
    ]);

    // Process settings: if it's a single type, data is the object itself.
    // If it's a collection (even with one item), data is an array.
    // We assume 'setting' is a single type for this example.
    // The fetchFromStrapi should ideally return StrapiResponse<GlobalSettings> for single types.
    // Let's adjust how we access settings data based on typical single type responses.
    // Strapi v4/v5 single type response is usually { data: YourType, meta: {} }
    const settings = settingsDataResponse.data as GlobalSettings | null;


    return {
      categories: categoriesResponse.data || [],
      featuredClinics: featuredClinicsResponse.data || [],
      settings: settings, // settingsDataResponse.data should be the GlobalSettings object or null
    };
  }
);

// You might also want individual cached getters for these if they are needed separately
export const getCategories = cache(async (): Promise<StrapiResponse<Category[]>> => {
  return fetchFromStrapi<Category[]>('categories', {
    fields: ['name', 'slug', 'description'],
    sort: ['name:asc'],
    pagination: { pageSize: 100 } // Get all categories, adjust if too many
  }, {
    next: { tags: ['strapi-categories', 'strapi-global'], revalidate: 3600 }
  });
});

export const getGlobalSettings = cache(async (): Promise<StrapiResponse<GlobalSettings | null>> => {
    // Assuming 'setting' is the API ID of your global settings single type
    const response = await fetchFromStrapi<GlobalSettings>('setting', {
        populate: {
            logo: { fields: ['url', 'alternativeText'] },
            favicon: { fields: ['url', 'alternativeText'] },
            // Add other relations for global settings if needed
        },
        fields: ['siteName', 'siteDescription'] // Specify top-level fields
    }, {
        next: {
            tags: ['strapi-settings', 'strapi-global'],
            revalidate: 7200, // 2 hours
        },
    });
     // For single types, Strapi typically returns the object directly in `data`.
    // If it could be an array (e.g. if it was a collection type by mistake), handle that.
    if (response.error || !response.data) {
        return response.error
            ? { data: null, error: response.error }
            : createErrorResponse<GlobalSettings | null>(404, 'NotFound', 'Settings not found');
    }
    return { data: response.data, meta: response.meta };
});
