{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/ExploreFurther.tsx"], "sourcesContent": ["import Link from 'next/link';\r\n\r\ninterface ExploreFurtherProps {\r\n  currentPath?: string;\r\n}\r\n\r\nconst ExploreFurther: React.FC<ExploreFurtherProps> = ({ currentPath }) => {\r\n  return (\r\n    <div className=\"py-16 bg-emerald-50\">\r\n      <div className=\"container mx-auto px-4 text-center\">\r\n        <h2 className=\"text-3xl font-bold mb-6 text-gray-800\">Explore Further</h2>\r\n        <p className=\"text-lg mb-8 max-w-3xl mx-auto text-gray-600\">\r\n          Didn't find what you were looking for?<br></br>Explore our complete listings of clinics, practitioners, and categories.\r\n        </p>\r\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n          {currentPath !== '/clinics' && (\r\n            <Link\r\n              href=\"/clinics\"\r\n              className=\"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold\"\r\n            >\r\n              Find All Clinics\r\n            </Link>\r\n          )}\r\n          {currentPath !== '/practitioners' && (\r\n            <Link\r\n              href=\"/practitioners\"\r\n              className=\"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-semibold\"\r\n            >\r\n              Find All Practitioners\r\n            </Link>\r\n          )}\r\n          <Link\r\n            href=\"/categories\"\r\n            className=\"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-semibold\"\r\n          >\r\n            View All Categories\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExploreFurther;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAMA,MAAM,iBAAgD,CAAC,EAAE,WAAW,EAAE;IACpE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,8OAAC;oBAAE,WAAU;;wBAA+C;sCACpB,8OAAC;;;;;wBAAQ;;;;;;;8BAEjD,8OAAC;oBAAI,WAAU;;wBACZ,gBAAgB,4BACf,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;wBAIF,gBAAgB,kCACf,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAIH,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/SearchInput.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/shared/SearchInput.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/shared/SearchInput.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/SearchInput.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/shared/SearchInput.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/shared/SearchInput.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/blog/MarkdownContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/blog/MarkdownContent.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/MarkdownContent.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/blog/MarkdownContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/blog/MarkdownContent.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/MarkdownContent.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/TabSwitcher.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/shared/TabSwitcher.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/shared/TabSwitcher.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/TabSwitcher.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/shared/TabSwitcher.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/shared/TabSwitcher.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/TabContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/shared/TabContent.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/shared/TabContent.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/TabContent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/shared/TabContent.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/shared/TabContent.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/app/categories/%5Bslug%5D/page.tsx"], "sourcesContent": ["import Link from 'next/link';\r\nimport { Fi<PERSON>apPin, FiFilter } from 'react-icons/fi';\r\nimport { getStrapiContent } from '@/lib/strapi';\r\nimport { notFound } from 'next/navigation';\r\nimport ExploreFurther from '@/components/shared/ExploreFurther';\r\nimport SearchInput from '@/components/shared/SearchInput';\r\nimport MarkdownContent from '@/components/blog/MarkdownContent';\r\nimport { Metadata } from 'next';\r\nimport TabSwitcher from '@/components/shared/TabSwitcher';\r\nimport TabContent from '@/components/shared/TabContent';\r\n\r\n// Force static rendering for this route segment\r\nexport const dynamic = 'force-static';\r\n\r\n// Enable ISR with both time-based and on-demand revalidation\r\n// Use a 12-hour revalidation period\r\nexport const revalidate = 43200; // 12 hours in seconds\r\n\r\n// Allow dynamic params for categories not generated at build time\r\nexport const dynamicParams = true;\r\n\r\n// Define a simple type for items with a slug (can be moved to a shared types file)\r\ninterface SlugItem {\r\n  slug: string;\r\n}\r\n\r\n// Generate static paths for all categories at build time\r\nexport async function generateStaticParams() {\r\n  try {\r\n    const response = await getStrapiContent.categories.getAllSlugs({\r\n      cache: 'force-cache',\r\n      next: {\r\n        revalidate: 43200, // Revalidate slugs every 12 hours\r\n        tags: ['strapi-categories-slugs']\r\n      }\r\n    });\r\n\r\n    if (response && response.data && Array.isArray(response.data)) {\r\n      console.log(`Pre-rendering ${response.data.length} category detail pages`);\r\n\r\n      return response.data\r\n        .filter((item: SlugItem | null): item is SlugItem => item !== null && typeof item.slug === 'string')\r\n        .map((item: SlugItem) => ({\r\n          slug: item.slug,\r\n        }));\r\n    }\r\n\r\n    return [];\r\n  } catch (error) {\r\n    console.error('Error fetching category slugs for generateStaticParams:', error);\r\n    return []; // Return empty array on error to prevent build failure\r\n  }\r\n}\r\n\r\n\r\nconst STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL;\r\n\r\n// Helper to create absolute URL for Strapi assets - enhanced for Strapi v5 and better debugging\r\nconst getStrapiMediaUrl = (mediaObject: any): string | null => {\r\n  // console.log(\"getStrapiMediaUrl input:\", JSON.stringify(mediaObject, null, 2)); // Keep commented unless deep debugging needed\r\n\r\n  // If null/undefined, return null\r\n  if (!mediaObject) return null;\r\n\r\n  // If it's already a string URL (less likely for populated media, but check)\r\n  if (typeof mediaObject === 'string') {\r\n    // console.log(\"Input is a string:\", mediaObject);\r\n    if (mediaObject.startsWith('http://') || mediaObject.startsWith('https://')) {\r\n      return mediaObject;\r\n    }\r\n    // Assume it's a relative path if it's a string but not absolute\r\n    return STRAPI_URL ? `${STRAPI_URL}${mediaObject}` : null;\r\n  }\r\n\r\n  // Handle Strapi v4/v5 media object structures\r\n  if (mediaObject && typeof mediaObject === 'object') {\r\n    // console.log(\"Input is an object:\", mediaObject);\r\n\r\n    // Most common pattern for populated media (v4/v5): object.data.attributes.url\r\n    let url = mediaObject.data?.attributes?.url;\r\n\r\n    // Fallback: Direct url property on the object itself (less common for populated)\r\n    if (!url) {\r\n      url = mediaObject.url;\r\n    }\r\n\r\n    // Fallback: Direct url property within a 'data' object (less common)\r\n    if (!url) {\r\n      url = mediaObject.data?.url;\r\n    }\r\n\r\n    // If we found a URL string\r\n    if (typeof url === 'string') {\r\n      // console.log(\"Extracted URL:\", url);\r\n      // Check if it's already absolute\r\n      if (url.startsWith('http://') || url.startsWith('https://')) {\r\n        return url;\r\n      }\r\n      // Otherwise, prepend Strapi URL\r\n      return `${STRAPI_URL}${url}`;\r\n    }\r\n  }\r\n\r\n  console.warn(\"Could not extract URL from media object:\", mediaObject);\r\n  return null;\r\n};\r\n\r\n// --- Type Definitions ---\r\n\r\n// Raw Strapi types (adjust based on actual API response)\r\ninterface RawStrapiMedia {\r\n  url?: string;\r\n  // Add other potential media fields if needed (e.g., alternativeText)\r\n}\r\n\r\n// Reusable Address type\r\ninterface RawStrapiAddress {\r\n  city: string;\r\n  stateProvince: string;\r\n  // Add other address fields if needed\r\n}\r\n\r\ninterface RawStrapiContactInfo {\r\n  phoneNumber?: string;\r\n  websiteUrl?: string;\r\n}\r\n\r\ninterface RawStrapiClinic {\r\n  id: string;\r\n  documentId: string;\r\n  name: string;\r\n  slug: string;\r\n  description?: string | null;\r\n  logo?: RawStrapiMedia | null;\r\n  featuredImage?: RawStrapiMedia | null;\r\n  address?: RawStrapiAddress | null;\r\n  contactInfo?: RawStrapiContactInfo | null;\r\n  isVerified?: boolean; // Add isVerified\r\n}\r\n\r\ninterface RawStrapiPractitioner {\r\n  id: string;\r\n  documentId: string;\r\n  name: string;\r\n  slug: string;\r\n  title?: string | null;\r\n  qualifications?: string | null;\r\n  profilePicture?: RawStrapiMedia | null; // Assuming a profile picture field\r\n  address?: RawStrapiAddress | null; // Add address field\r\n  isVerified?: boolean; // Add isVerified\r\n  bio?: string | null; // Add bio field\r\n  // Add other practitioner fields if needed\r\n}\r\n\r\n// Define the structure for the nested Open Graph component\r\ninterface RawStrapiOpenGraph {\r\n  ogTitle?: string;\r\n  ogDescription?: string;\r\n  ogImage?: RawStrapiMedia | null;\r\n  ogUrl?: string;\r\n  ogType?: string;\r\n}\r\n\r\n// Define a basic structure for the SEO component data, including the nested openGraph component\r\ninterface RawStrapiSeo {\r\n  metaTitle?: string;\r\n  metaDescription?: string;\r\n  metaImage?: RawStrapiMedia | null;\r\n  openGraph?: RawStrapiOpenGraph | null; // Nested Open Graph component\r\n  // Add other SEO fields if they exist (e.g., keywords, structuredData)\r\n}\r\n\r\n\r\ninterface RawStrapiCategory {\r\n  id: string;\r\n  documentId: string;\r\n  name: string;\r\n  slug: string;\r\n  description?: string | null;\r\n  icon?: RawStrapiMedia | null;\r\n  featuredImage?: RawStrapiMedia | null;\r\n  clinics?: RawStrapiClinic[]; // Direct array in v5\r\n  practitioners?: RawStrapiPractitioner[]; // Direct array in v5\r\n  relatedConditions?: string[] | null; // Assuming this remains simple\r\n  contentBottomCategory?: string | null; // <-- ADD THIS LINE\r\n  seo?: RawStrapiSeo | null; // Add the SEO field\r\n}\r\n\r\n// Transformed types for components\r\ninterface TransformedClinic {\r\n  id: string;\r\n  name: string;\r\n  slug: string;\r\n  description?: string | null;\r\n  logo: string | null;\r\n  featuredImage: string | null;\r\n  address: {\r\n    city: string;\r\n    stateProvince: string;\r\n  };\r\n  contactInfo?: {\r\n    phoneNumber?: string;\r\n    websiteUrl?: string;\r\n  } | null;\r\n  isVerified?: boolean; // Add isVerified\r\n}\r\n\r\ninterface TransformedPractitioner {\r\n  id: string;\r\n  name: string;\r\n  slug: string;\r\n  title?: string | null;\r\n  qualifications?: string | null;\r\n  profilePicture: string | null;\r\n  isVerified?: boolean; // Add isVerified\r\n  bio?: string | null; // Add bio field\r\n  // Removed address field\r\n}\r\n\r\n// Define the possible tab types to avoid TypeScript comparison errors\r\ntype TabType = 'clinics' | 'practitioners';\r\n\r\n// --- Data Fetching & Transformation ---\r\n\r\n// Get category data with optimized caching\r\nasync function getCategoryData(slug: string): Promise<RawStrapiCategory | null> {\r\n  try {\r\n    // Use the optimized getBySlug function with proper cache tags\r\n    // The getBySlug in strapi.ts should handle appropriate next options or rely on page-level revalidate\r\n    const response = await getStrapiContent.categories.getBySlug(slug, {\r\n      next: {\r\n        tags: ['strapi-categories-list', `strapi-category-${slug}`], // Ensure these tags are used\r\n        // revalidate: 43200 // This will be inherited from page-level revalidate\r\n      },\r\n      cache: 'force-cache' // Opt-in to caching\r\n    });\r\n\r\n    // Strapi v5 returns data directly in an array when filtering\r\n    if (response?.data && Array.isArray(response.data) && response.data.length > 0) {\r\n      return response.data[0] as RawStrapiCategory;\r\n    }\r\n    return null;\r\n  } catch (error) {\r\n    console.error(`Error fetching category with slug ${slug}:`, error);\r\n    return null;\r\n  }\r\n}\r\n\r\nfunction transformClinicData(rawClinic: RawStrapiClinic): TransformedClinic | null {\r\n  if (!rawClinic || !rawClinic.id || !rawClinic.name) {\r\n    return null;\r\n  }\r\n\r\n  // Process image URLs - Pass the whole object to the helper\r\n  const logoUrl = getStrapiMediaUrl(rawClinic.logo);\r\n  const featuredImageUrl = getStrapiMediaUrl(rawClinic.featuredImage);\r\n\r\n  return {\r\n    id: String(rawClinic.id),\r\n    name: rawClinic.name,\r\n    slug: rawClinic.slug || `clinic-${rawClinic.id}`,\r\n    description: rawClinic.description,\r\n    logo: logoUrl,\r\n    featuredImage: featuredImageUrl,\r\n    address: rawClinic.address || { city: 'Unknown', stateProvince: 'N/A' },\r\n    contactInfo: rawClinic.contactInfo,\r\n    isVerified: rawClinic.isVerified || false, // Pass through isVerified\r\n  };\r\n}\r\n\r\nfunction transformPractitionerData(rawPractitioner: RawStrapiPractitioner): TransformedPractitioner | null {\r\n  if (!rawPractitioner || !rawPractitioner.id || !rawPractitioner.name) {\r\n    return null;\r\n  }\r\n\r\n  return {\r\n    id: String(rawPractitioner.id),\r\n    name: rawPractitioner.name,\r\n    slug: rawPractitioner.slug || `practitioner-${rawPractitioner.id}`,\r\n    title: rawPractitioner.title,\r\n    qualifications: rawPractitioner.qualifications,\r\n    profilePicture: getStrapiMediaUrl(rawPractitioner.profilePicture),\r\n    isVerified: rawPractitioner.isVerified || false, // Pass through isVerified\r\n    bio: rawPractitioner.bio, // Add bio field\r\n  };\r\n}\r\n\r\n// --- Page Component ---\r\n\r\ninterface PageParams {\r\n  slug: string;\r\n}\r\n\r\n// Define the site URL from environment variable with proper fallback to your actual domain\r\nconst SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';\r\n\r\n// Site URL for canonical URLs: ${SITE_URL}\r\n\r\n// Generate Metadata\r\nexport async function generateMetadata({ params }: { params: PageParams }): Promise<Metadata> {\r\n  const awaitedParams = await params;\r\n  const categoryData = await getCategoryData(awaitedParams.slug); // Renamed for clarity\r\n\r\n  if (!categoryData) {\r\n    return {\r\n      title: 'Category Not Found | Natural Healing Now',\r\n      description: 'The requested category could not be found.',\r\n      // No canonical for not found pages\r\n    };\r\n  }\r\n\r\n  // Extract SEO data directly from categoryData, now that the type includes it\r\n  const seo = categoryData.seo;\r\n  // Use direct properties from categoryData for fallbacks as per RawStrapiCategory type\r\n  const defaultTitle = `${categoryData.name || 'Category'} | Natural Healing Now`;\r\n  const defaultDescription = categoryData.description || `Learn about ${categoryData.name || 'this category'} and find related clinics and practitioners.`;\r\n\r\n  const metaTitle = seo?.metaTitle || defaultTitle;\r\n  const metaDescription = seo?.metaDescription || defaultDescription;\r\n  const metaImageUrl = getStrapiMediaUrl(seo?.metaImage); // Use helper for image URL\r\n\r\n  const canonicalPath = `/categories/${categoryData.slug}`; // Use slug from original data object\r\n  const canonicalUrl = SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath;\r\n\r\n  // Prepare Open Graph data - Access fields from the nested seo.openGraph component\r\n  const ogTitle = seo?.openGraph?.ogTitle || metaTitle; // Access via seo.openGraph\r\n  const ogDescription = seo?.openGraph?.ogDescription || metaDescription; // Access via seo.openGraph\r\n  const ogImageUrl = getStrapiMediaUrl(seo?.openGraph?.ogImage) || metaImageUrl; // Access via seo.openGraph\r\n  const ogUrl = seo?.openGraph?.ogUrl || canonicalUrl; // Access via seo.openGraph\r\n  const ogTypeInput = seo?.openGraph?.ogType || 'website'; // Access via seo.openGraph\r\n  const allowedOgTypes = ['website', 'article', 'book', 'profile'];\r\n  // Validate the input type and default to 'website' if it's not one of the allowed literals\r\n  const finalOgType = allowedOgTypes.includes(ogTypeInput) ? ogTypeInput as 'website' | 'article' | 'book' | 'profile' : 'website';\r\n\r\n\r\n  // Construct the openGraph object carefully, adhering to the Metadata['openGraph'] type\r\n  const openGraphData: Metadata['openGraph'] = {\r\n    title: ogTitle,\r\n    description: ogDescription,\r\n    url: ogUrl,\r\n    type: finalOgType, // Use the validated and typed value\r\n    siteName: 'Natural Healing Now', // Add site name\r\n    images: ogImageUrl ? [{ url: ogImageUrl }] : undefined, // Set images only if URL exists\r\n  };\r\n\r\n  return {\r\n    title: metaTitle,\r\n    description: metaDescription,\r\n    alternates: {\r\n      canonical: canonicalUrl,\r\n    },\r\n    openGraph: openGraphData,\r\n    // Add Twitter card data if desired\r\n    // twitter: { ... },\r\n  };\r\n}\r\n\r\n// Define the interface for the searchParams object, adding query and page\r\ninterface CategoryPageSearchParams {\r\n  tab?: string;\r\n  query?: string;\r\n  page?: string;\r\n}\r\n\r\n// Main Page Component\r\nexport default async function CategoryDetailPage({\r\n  params,\r\n  searchParams\r\n}: {\r\n  params: PageParams;\r\n  searchParams: CategoryPageSearchParams; // Use updated interface\r\n}) {\r\n  const awaitedParams = await params;\r\n  const slug = awaitedParams.slug;\r\n\r\n  const awaitedSearchParams = await searchParams; // Await searchParams\r\n  const query = awaitedSearchParams?.query || '';\r\n  const location = awaitedSearchParams?.location || ''; // Get location from search params\r\n  const currentPage = Number(awaitedSearchParams?.page) || 1;\r\n  const rawTabValue = awaitedSearchParams?.tab || 'clinics';\r\n  const activeTab: TabType = (rawTabValue === 'practitioners') ? 'practitioners' : 'clinics';\r\n\r\n\r\n\r\n  const category = await getCategoryData(slug);\r\n\r\n  if (!category) {\r\n    notFound();\r\n  }\r\n\r\n  const {\r\n    name = 'Unnamed Category',\r\n    description = 'No description available.',\r\n    icon = null,\r\n    featuredImage = null,\r\n    relatedConditions = null,\r\n    contentBottomCategory\r\n  } = category;\r\n\r\n  const categoryIconUrl = getStrapiMediaUrl(icon); // Pass the whole icon object\r\n  const categoryImageUrl = getStrapiMediaUrl(featuredImage); // Pass the whole featuredImage object\r\n\r\n  let clinicCount = 0;\r\n  let practitionerCount = 0;\r\n  let totalPages = 1;\r\n\r\n  // Determine caching strategy based on search parameters\r\n  const hasFilters = query || location; // Include location in filter detection\r\n  const fetchOptions = hasFilters\r\n    ? { cache: 'no-store' } // Don't cache filtered results\r\n    : {\r\n        next: {\r\n          tags: ['strapi-categories-list', `strapi-category-${slug}`], // General tags for category data\r\n          revalidate: 43200 // Inherits from page\r\n        },\r\n        cache: 'force-cache'\r\n      };\r\n\r\n  // Fetch counts first - include filters if they exist\r\n  try {\r\n    const clinicCountResponse = await getStrapiContent.clinics.getAll({\r\n        categorySlug: slug,\r\n        query: hasFilters ? query : '', // Include query filter for count if filters are applied\r\n        location: hasFilters ? location : '', // Include location filter for count if filters are applied\r\n        page: 1, // For count, page 1 is enough\r\n        pageSize: 1, // We only need the meta for total count\r\n        ...fetchOptions // Apply the same caching strategy\r\n    });\r\n    clinicCount = clinicCountResponse?.meta?.pagination?.total || 0;\r\n\r\n    const practitionerCountResponse = await getStrapiContent.practitioners.getAll({\r\n        categorySlug: slug,\r\n        query: hasFilters ? query : '', // Include query filter for count if filters are applied\r\n        location: hasFilters ? location : '', // Include location filter for count if filters are applied\r\n        page: 1,\r\n        pageSize: 1,\r\n        ...fetchOptions // Apply the same caching strategy\r\n    });\r\n    practitionerCount = practitionerCountResponse?.meta?.pagination?.total || 0;\r\n\r\n  } catch (error) {\r\n    console.error(\"Error fetching counts for category page:\", error);\r\n  }\r\n\r\n  // Fetch clinics data\r\n  let clinics: TransformedClinic[] = [];\r\n  let clinicPages = 1;\r\n  try {\r\n    const clinicResponse = await getStrapiContent.clinics.getAll({\r\n      categorySlug: slug,\r\n      query: activeTab === 'clinics' ? query : '', // Only apply query for active tab\r\n      location: activeTab === 'clinics' ? location : '', // Only apply location for active tab\r\n      page: activeTab === 'clinics' ? currentPage : 1, // Use page 1 for inactive tab\r\n      pageSize: 12,\r\n      ...fetchOptions // Use the same approach as specialities page\r\n    });\r\n\r\n    console.log(`Clinic response data length: ${clinicResponse?.data?.length || 0}`);\r\n    clinics = (clinicResponse?.data || [])\r\n      .map((item: any) => transformClinicData(item.attributes || item))\r\n      .filter(Boolean as unknown as (value: TransformedClinic | null) => value is TransformedClinic);\r\n    console.log(`Transformed clinics array length: ${clinics.length}`);\r\n\r\n    clinicPages = clinicResponse?.meta?.pagination?.pageCount || 1;\r\n  } catch (error) {\r\n    console.error(`Error fetching clinics for category ${slug}:`, error);\r\n  }\r\n\r\n  // Fetch practitioners data\r\n  let practitioners: TransformedPractitioner[] = [];\r\n  let practitionerPages = 1;\r\n  try {\r\n    const practitionerResponse = await getStrapiContent.practitioners.getAll({\r\n      categorySlug: slug,\r\n      query: activeTab === 'practitioners' ? query : '', // Only apply query for active tab\r\n      location: activeTab === 'practitioners' ? location : '', // Only apply location for active tab\r\n      page: activeTab === 'practitioners' ? currentPage : 1, // Use page 1 for inactive tab\r\n      pageSize: 12,\r\n      ...fetchOptions // Use the same approach as specialities page\r\n    });\r\n\r\n    console.log(`Practitioner raw response data length: ${practitionerResponse?.data?.length || 0}`);\r\n    if (practitionerResponse?.data && practitionerResponse.data.length > 0) {\r\n      console.log(`First raw practitioner item structure:`, JSON.stringify({\r\n        id: practitionerResponse.data[0].id,\r\n        hasAttributes: !!practitionerResponse.data[0].attributes,\r\n        attributesType: practitionerResponse.data[0].attributes ? typeof practitionerResponse.data[0].attributes : 'N/A',\r\n        keys: Object.keys(practitionerResponse.data[0]),\r\n        attributeKeys: practitionerResponse.data[0].attributes ? Object.keys(practitionerResponse.data[0].attributes) : []\r\n      }, null, 2));\r\n    }\r\n\r\n    practitioners = (practitionerResponse?.data || []).map((item: any) => {\r\n      const practitionerRawData = item.attributes || item;\r\n      // console.log(`Processing practitioner: ${practitionerRawData.name || 'unnamed'}, ID: ${practitionerRawData.id || 'no-id'}`);\r\n      if (!practitionerRawData.id || !practitionerRawData.name) {\r\n        console.warn(`Practitioner data missing id or name. ID: ${practitionerRawData.id}, Name: ${practitionerRawData.name}. Full item:`, JSON.stringify(item));\r\n      }\r\n      return transformPractitionerData(practitionerRawData);\r\n    }).filter((p: TransformedPractitioner | null): p is TransformedPractitioner => {\r\n      if (p === null) {\r\n        // console.warn(\"A practitioner was filtered out by transformPractitionerData returning null.\");\r\n      }\r\n      return p !== null;\r\n    });\r\n\r\n    console.log(`Transformed practitioners array length: ${practitioners.length}`);\r\n    practitionerPages = practitionerResponse?.meta?.pagination?.pageCount || 1;\r\n  } catch (error) {\r\n    console.error(`Error fetching practitioners for category ${slug}:`, error);\r\n  }\r\n\r\n  totalPages = activeTab === 'clinics' ? clinicPages : practitionerPages;\r\n\r\n  const conditions = relatedConditions ? [...relatedConditions] : [];\r\n\r\n  // No longer need the isTabActive function as it's handled by the client components\r\n\r\n  return (\r\n    <>\r\n      {/* Breadcrumb */}\r\n      <div className=\"bg-gray-100 py-3\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"flex items-center text-sm text-gray-600\">\r\n            <Link href=\"/\" className=\"hover:text-emerald-600\">Home</Link>\r\n            <span className=\"mx-2\">/</span>\r\n            <Link href=\"/categories\" className=\"hover:text-emerald-600\">Categories</Link>\r\n            <span className=\"mx-2\">/</span>\r\n            <span className=\"text-gray-800\">{name}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Category Header */}\r\n      <div className=\"bg-emerald-600 text-white py-12 relative overflow-hidden\">\r\n        {categoryImageUrl && (\r\n          <div\r\n            className=\"absolute inset-0 opacity-20 bg-cover bg-center\"\r\n            style={{ backgroundImage: `url(${categoryImageUrl})` }}\r\n          >\r\n            {/* Fallback div with background image instead of Next.js Image component */}\r\n          </div>\r\n        )}\r\n        <div className=\"container mx-auto px-4 relative z-10\">\r\n          <div className=\"flex items-center mb-4\">\r\n            {categoryIconUrl && (\r\n              <div className=\"relative h-12 w-12 mr-4 bg-white rounded-full p-1 flex-shrink-0\">\r\n                <div\r\n                  className=\"absolute inset-0 rounded-full bg-contain bg-center bg-no-repeat\"\r\n                  style={{ backgroundImage: `url(${categoryIconUrl})` }}\r\n                >\r\n                  {/* Fallback div with background image */}\r\n                </div>\r\n              </div>\r\n            )}\r\n            <h1 className=\"text-3xl md:text-4xl font-bold\">{name}</h1>\r\n          </div>\r\n          {description && <p className=\"text-lg max-w-3xl mb-4\">{description}</p>}\r\n          <div className=\"flex gap-4 text-sm\">\r\n            <span>{clinicCount} Clinics</span>\r\n            <span>&bull;</span>\r\n            <span>{practitionerCount} Practitioners</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Search and Filter Section */}\r\n      <div className=\"bg-white shadow-sm sticky top-0 z-20\"> {/* Make filters sticky */}\r\n        <div className=\"container mx-auto px-4 py-4\">\r\n          <div className=\"flex flex-col md:flex-row gap-4\">\r\n            {/* Search Input */}\r\n            <div className=\"flex-1\">\r\n               <SearchInput placeholder={`Search within ${name}...`} paramName=\"query\" />\r\n            </div>\r\n             {/* Location Input - Replaced with SearchInput */}\r\n            <div className=\"flex-1\">\r\n               <SearchInput placeholder=\"City, state, or zip code\" paramName=\"location\" icon={<FiMapPin className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />} />\r\n            </div>\r\n            <div>\r\n              <button className=\"w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200\">\r\n                <FiFilter />\r\n                <span>Filters</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n          {/* Placeholder for active filters */}\r\n          {/* <div className=\"flex flex-wrap gap-2 mt-3\"> ... </div> */}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tabs & Results Section - Using client-side components */}\r\n      <div className=\"py-8 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4\">\r\n          {/* Use the client-side TabSwitcher component */}\r\n          <TabSwitcher\r\n            slug={awaitedParams.slug}\r\n            pageType=\"categories\"\r\n            clinicCount={clinicCount}\r\n            practitionerCount={practitionerCount}\r\n            initialTab={activeTab}\r\n          />\r\n\r\n          {/* Use the client-side TabContent component */}\r\n          <TabContent\r\n            clinics={clinics}\r\n            practitioners={practitioners}\r\n            totalPages={totalPages}\r\n            initialTab={activeTab}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Related Conditions Section */}\r\n      {conditions.length > 0 && (\r\n        <div className=\"py-12 bg-white\">\r\n          <div className=\"container mx-auto px-4\">\r\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">\r\n              Common Conditions Addressed by {name}\r\n            </h2>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {conditions.map((condition, index) => (\r\n                <span\r\n                  key={index}\r\n                  className=\"bg-emerald-50 text-emerald-700 px-3 py-1 rounded-full text-sm\"\r\n                >\r\n                  {condition}\r\n                </span>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Add Content Bottom Section */}\r\n      {contentBottomCategory && (\r\n        <div className=\"py-12 bg-white border-t border-gray-200 flex justify-center\"> {/* Added flex justify-center back */}\r\n          <div className=\"w-full max-w-4xl px-4 prose lg:prose-lg\"> {/* Removed container, mx-auto, max-w-none; Added w-full, max-w-4xl */}\r\n            <MarkdownContent content={contentBottomCategory} />\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Replace Call to Action with ExploreFurther component */}\r\n      <ExploreFurther />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;AAGO,MAAM,UAAU;AAIhB,MAAM,aAAa,OAAO,sBAAsB;AAGhD,MAAM,gBAAgB;AAQtB,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,oHAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,WAAW,CAAC;YAC7D,OAAO;YACP,MAAM;gBACJ,YAAY;gBACZ,MAAM;oBAAC;iBAA0B;YACnC;QACF;QAEA,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;YAC7D,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;YAEzE,OAAO,SAAS,IAAI,CACjB,MAAM,CAAC,CAAC,OAA4C,SAAS,QAAQ,OAAO,KAAK,IAAI,KAAK,UAC1F,GAAG,CAAC,CAAC,OAAmB,CAAC;oBACxB,MAAM,KAAK,IAAI;gBACjB,CAAC;QACL;QAEA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2DAA2D;QACzE,OAAO,EAAE,EAAE,uDAAuD;IACpE;AACF;AAGA,MAAM;AAEN,gGAAgG;AAChG,MAAM,oBAAoB,CAAC;IACzB,gIAAgI;IAEhI,iCAAiC;IACjC,IAAI,CAAC,aAAa,OAAO;IAEzB,4EAA4E;IAC5E,IAAI,OAAO,gBAAgB,UAAU;QACnC,kDAAkD;QAClD,IAAI,YAAY,UAAU,CAAC,cAAc,YAAY,UAAU,CAAC,aAAa;YAC3E,OAAO;QACT;QACA,gEAAgE;QAChE,OAAO,uCAAa,GAAG,aAAa,aAAa;IACnD;IAEA,8CAA8C;IAC9C,IAAI,eAAe,OAAO,gBAAgB,UAAU;QAClD,mDAAmD;QAEnD,8EAA8E;QAC9E,IAAI,MAAM,YAAY,IAAI,EAAE,YAAY;QAExC,iFAAiF;QACjF,IAAI,CAAC,KAAK;YACR,MAAM,YAAY,GAAG;QACvB;QAEA,qEAAqE;QACrE,IAAI,CAAC,KAAK;YACR,MAAM,YAAY,IAAI,EAAE;QAC1B;QAEA,2BAA2B;QAC3B,IAAI,OAAO,QAAQ,UAAU;YAC3B,sCAAsC;YACtC,iCAAiC;YACjC,IAAI,IAAI,UAAU,CAAC,cAAc,IAAI,UAAU,CAAC,aAAa;gBAC3D,OAAO;YACT;YACA,gCAAgC;YAChC,OAAO,GAAG,aAAa,KAAK;QAC9B;IACF;IAEA,QAAQ,IAAI,CAAC,4CAA4C;IACzD,OAAO;AACT;AAqHA,yCAAyC;AAEzC,2CAA2C;AAC3C,eAAe,gBAAgB,IAAY;IACzC,IAAI;QACF,8DAA8D;QAC9D,qGAAqG;QACrG,MAAM,WAAW,MAAM,oHAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM;YACjE,MAAM;gBACJ,MAAM;oBAAC;oBAA0B,CAAC,gBAAgB,EAAE,MAAM;iBAAC;YAE7D;YACA,OAAO,cAAc,oBAAoB;QAC3C;QAEA,6DAA6D;QAC7D,IAAI,UAAU,QAAQ,MAAM,OAAO,CAAC,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG,GAAG;YAC9E,OAAO,SAAS,IAAI,CAAC,EAAE;QACzB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC,EAAE;QAC5D,OAAO;IACT;AACF;AAEA,SAAS,oBAAoB,SAA0B;IACrD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,EAAE;QAClD,OAAO;IACT;IAEA,2DAA2D;IAC3D,MAAM,UAAU,kBAAkB,UAAU,IAAI;IAChD,MAAM,mBAAmB,kBAAkB,UAAU,aAAa;IAElE,OAAO;QACL,IAAI,OAAO,UAAU,EAAE;QACvB,MAAM,UAAU,IAAI;QACpB,MAAM,UAAU,IAAI,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE;QAChD,aAAa,UAAU,WAAW;QAClC,MAAM;QACN,eAAe;QACf,SAAS,UAAU,OAAO,IAAI;YAAE,MAAM;YAAW,eAAe;QAAM;QACtE,aAAa,UAAU,WAAW;QAClC,YAAY,UAAU,UAAU,IAAI;IACtC;AACF;AAEA,SAAS,0BAA0B,eAAsC;IACvE,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,IAAI,EAAE;QACpE,OAAO;IACT;IAEA,OAAO;QACL,IAAI,OAAO,gBAAgB,EAAE;QAC7B,MAAM,gBAAgB,IAAI;QAC1B,MAAM,gBAAgB,IAAI,IAAI,CAAC,aAAa,EAAE,gBAAgB,EAAE,EAAE;QAClE,OAAO,gBAAgB,KAAK;QAC5B,gBAAgB,gBAAgB,cAAc;QAC9C,gBAAgB,kBAAkB,gBAAgB,cAAc;QAChE,YAAY,gBAAgB,UAAU,IAAI;QAC1C,KAAK,gBAAgB,GAAG;IAC1B;AACF;AAQA,2FAA2F;AAC3F,MAAM,WAAW,6DAAoC;AAK9C,eAAe,iBAAiB,EAAE,MAAM,EAA0B;IACvE,MAAM,gBAAgB,MAAM;IAC5B,MAAM,eAAe,MAAM,gBAAgB,cAAc,IAAI,GAAG,sBAAsB;IAEtF,IAAI,CAAC,cAAc;QACjB,OAAO;YACL,OAAO;YACP,aAAa;QAEf;IACF;IAEA,6EAA6E;IAC7E,MAAM,MAAM,aAAa,GAAG;IAC5B,sFAAsF;IACtF,MAAM,eAAe,GAAG,aAAa,IAAI,IAAI,WAAW,sBAAsB,CAAC;IAC/E,MAAM,qBAAqB,aAAa,WAAW,IAAI,CAAC,YAAY,EAAE,aAAa,IAAI,IAAI,gBAAgB,4CAA4C,CAAC;IAExJ,MAAM,YAAY,KAAK,aAAa;IACpC,MAAM,kBAAkB,KAAK,mBAAmB;IAChD,MAAM,eAAe,kBAAkB,KAAK,YAAY,2BAA2B;IAEnF,MAAM,gBAAgB,CAAC,YAAY,EAAE,aAAa,IAAI,EAAE,EAAE,qCAAqC;IAC/F,MAAM,eAAe,uCAAW,GAAG,WAAW,eAAe;IAE7D,kFAAkF;IAClF,MAAM,UAAU,KAAK,WAAW,WAAW,WAAW,2BAA2B;IACjF,MAAM,gBAAgB,KAAK,WAAW,iBAAiB,iBAAiB,2BAA2B;IACnG,MAAM,aAAa,kBAAkB,KAAK,WAAW,YAAY,cAAc,2BAA2B;IAC1G,MAAM,QAAQ,KAAK,WAAW,SAAS,cAAc,2BAA2B;IAChF,MAAM,cAAc,KAAK,WAAW,UAAU,WAAW,2BAA2B;IACpF,MAAM,iBAAiB;QAAC;QAAW;QAAW;QAAQ;KAAU;IAChE,2FAA2F;IAC3F,MAAM,cAAc,eAAe,QAAQ,CAAC,eAAe,cAA4D;IAGvH,uFAAuF;IACvF,MAAM,gBAAuC;QAC3C,OAAO;QACP,aAAa;QACb,KAAK;QACL,MAAM;QACN,UAAU;QACV,QAAQ,aAAa;YAAC;gBAAE,KAAK;YAAW;SAAE,GAAG;IAC/C;IAEA,OAAO;QACL,OAAO;QACP,aAAa;QACb,YAAY;YACV,WAAW;QACb;QACA,WAAW;IAGb;AACF;AAUe,eAAe,mBAAmB,EAC/C,MAAM,EACN,YAAY,EAIb;IACC,MAAM,gBAAgB,MAAM;IAC5B,MAAM,OAAO,cAAc,IAAI;IAE/B,MAAM,sBAAsB,MAAM,cAAc,qBAAqB;IACrE,MAAM,QAAQ,qBAAqB,SAAS;IAC5C,MAAM,WAAW,qBAAqB,YAAY,IAAI,kCAAkC;IACxF,MAAM,cAAc,OAAO,qBAAqB,SAAS;IACzD,MAAM,cAAc,qBAAqB,OAAO;IAChD,MAAM,YAAqB,AAAC,gBAAgB,kBAAmB,kBAAkB;IAIjF,MAAM,WAAW,MAAM,gBAAgB;IAEvC,IAAI,CAAC,UAAU;QACb,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,EACJ,OAAO,kBAAkB,EACzB,cAAc,2BAA2B,EACzC,OAAO,IAAI,EACX,gBAAgB,IAAI,EACpB,oBAAoB,IAAI,EACxB,qBAAqB,EACtB,GAAG;IAEJ,MAAM,kBAAkB,kBAAkB,OAAO,6BAA6B;IAC9E,MAAM,mBAAmB,kBAAkB,gBAAgB,sCAAsC;IAEjG,IAAI,cAAc;IAClB,IAAI,oBAAoB;IACxB,IAAI,aAAa;IAEjB,wDAAwD;IACxD,MAAM,aAAa,SAAS,UAAU,uCAAuC;IAC7E,MAAM,eAAe,aACjB;QAAE,OAAO;IAAW,EAAE,+BAA+B;OACrD;QACE,MAAM;YACJ,MAAM;gBAAC;gBAA0B,CAAC,gBAAgB,EAAE,MAAM;aAAC;YAC3D,YAAY,MAAM,qBAAqB;QACzC;QACA,OAAO;IACT;IAEJ,qDAAqD;IACrD,IAAI;QACF,MAAM,sBAAsB,MAAM,oHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC;YAC9D,cAAc;YACd,OAAO,aAAa,QAAQ;YAC5B,UAAU,aAAa,WAAW;YAClC,MAAM;YACN,UAAU;YACV,GAAG,aAAa,kCAAkC;QACtD;QACA,cAAc,qBAAqB,MAAM,YAAY,SAAS;QAE9D,MAAM,4BAA4B,MAAM,oHAAA,CAAA,mBAAgB,CAAC,aAAa,CAAC,MAAM,CAAC;YAC1E,cAAc;YACd,OAAO,aAAa,QAAQ;YAC5B,UAAU,aAAa,WAAW;YAClC,MAAM;YACN,UAAU;YACV,GAAG,aAAa,kCAAkC;QACtD;QACA,oBAAoB,2BAA2B,MAAM,YAAY,SAAS;IAE5E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;IAC5D;IAEA,qBAAqB;IACrB,IAAI,UAA+B,EAAE;IACrC,IAAI,cAAc;IAClB,IAAI;QACF,MAAM,iBAAiB,MAAM,oHAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC;YAC3D,cAAc;YACd,OAAO,cAAc,YAAY,QAAQ;YACzC,UAAU,cAAc,YAAY,WAAW;YAC/C,MAAM,cAAc,YAAY,cAAc;YAC9C,UAAU;YACV,GAAG,aAAa,6CAA6C;QAC/D;QAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,gBAAgB,MAAM,UAAU,GAAG;QAC/E,UAAU,CAAC,gBAAgB,QAAQ,EAAE,EAClC,GAAG,CAAC,CAAC,OAAc,oBAAoB,KAAK,UAAU,IAAI,OAC1D,MAAM,CAAC;QACV,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,QAAQ,MAAM,EAAE;QAEjE,cAAc,gBAAgB,MAAM,YAAY,aAAa;IAC/D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC,EAAE;IAChE;IAEA,2BAA2B;IAC3B,IAAI,gBAA2C,EAAE;IACjD,IAAI,oBAAoB;IACxB,IAAI;QACF,MAAM,uBAAuB,MAAM,oHAAA,CAAA,mBAAgB,CAAC,aAAa,CAAC,MAAM,CAAC;YACvE,cAAc;YACd,OAAO,cAAc,kBAAkB,QAAQ;YAC/C,UAAU,cAAc,kBAAkB,WAAW;YACrD,MAAM,cAAc,kBAAkB,cAAc;YACpD,UAAU;YACV,GAAG,aAAa,6CAA6C;QAC/D;QAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,sBAAsB,MAAM,UAAU,GAAG;QAC/F,IAAI,sBAAsB,QAAQ,qBAAqB,IAAI,CAAC,MAAM,GAAG,GAAG;YACtE,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC,EAAE,KAAK,SAAS,CAAC;gBACnE,IAAI,qBAAqB,IAAI,CAAC,EAAE,CAAC,EAAE;gBACnC,eAAe,CAAC,CAAC,qBAAqB,IAAI,CAAC,EAAE,CAAC,UAAU;gBACxD,gBAAgB,qBAAqB,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,OAAO,qBAAqB,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG;gBAC3G,MAAM,OAAO,IAAI,CAAC,qBAAqB,IAAI,CAAC,EAAE;gBAC9C,eAAe,qBAAqB,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,OAAO,IAAI,CAAC,qBAAqB,IAAI,CAAC,EAAE,CAAC,UAAU,IAAI,EAAE;YACpH,GAAG,MAAM;QACX;QAEA,gBAAgB,CAAC,sBAAsB,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC;YACtD,MAAM,sBAAsB,KAAK,UAAU,IAAI;YAC/C,8HAA8H;YAC9H,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,IAAI,EAAE;gBACxD,QAAQ,IAAI,CAAC,CAAC,0CAA0C,EAAE,oBAAoB,EAAE,CAAC,QAAQ,EAAE,oBAAoB,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,SAAS,CAAC;YACpJ;YACA,OAAO,0BAA0B;QACnC,GAAG,MAAM,CAAC,CAAC;YACT,IAAI,MAAM,MAAM;YACd,gGAAgG;YAClG;YACA,OAAO,MAAM;QACf;QAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,cAAc,MAAM,EAAE;QAC7E,oBAAoB,sBAAsB,MAAM,YAAY,aAAa;IAC3E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC,EAAE;IACtE;IAEA,aAAa,cAAc,YAAY,cAAc;IAErD,MAAM,aAAa,oBAAoB;WAAI;KAAkB,GAAG,EAAE;IAElE,mFAAmF;IAEnF,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAyB;;;;;;0CAClD,8OAAC;gCAAK,WAAU;0CAAO;;;;;;0CACvB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAc,WAAU;0CAAyB;;;;;;0CAC5D,8OAAC;gCAAK,WAAU;0CAAO;;;;;;0CACvB,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;;;;;;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;oBACZ,kCACC,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;wBAAC;;;;;;kCAKzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,iCACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAM1D,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;;4BAEjD,6BAAe,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAM;4CAAY;;;;;;;kDACnB,8OAAC;kDAAK;;;;;;kDACN,8OAAC;;4CAAM;4CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;;oBAAuC;kCACpD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,cAAA,8OAAC,2IAAA,CAAA,UAAW;wCAAC,aAAa,CAAC,cAAc,EAAE,KAAK,GAAG,CAAC;wCAAE,WAAU;;;;;;;;;;;8CAGnE,8OAAC;oCAAI,WAAU;8CACZ,cAAA,8OAAC,2IAAA,CAAA,UAAW;wCAAC,aAAY;wCAA2B,WAAU;wCAAW,oBAAM,8OAAC,8IAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAEtG,8OAAC;8CACC,cAAA,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,8IAAA,CAAA,WAAQ;;;;;0DACT,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,2IAAA,CAAA,UAAW;4BACV,MAAM,cAAc,IAAI;4BACxB,UAAS;4BACT,aAAa;4BACb,mBAAmB;4BACnB,YAAY;;;;;;sCAId,8OAAC,0IAAA,CAAA,UAAU;4BACT,SAAS;4BACT,eAAe;4BACf,YAAY;4BACZ,YAAY;;;;;;;;;;;;;;;;;YAMjB,WAAW,MAAM,GAAG,mBACnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAwC;gCACpB;;;;;;;sCAElC,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;;;;;;;;;;;;YAYhB,uCACC,8OAAC;gBAAI,WAAU;;oBAA8D;kCAC3E,8OAAC;wBAAI,WAAU;;4BAA0C;0CACvD,8OAAC,6IAAA,CAAA,UAAe;gCAAC,SAAS;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC,8IAAA,CAAA,UAAc;;;;;;;AAGrB", "debugId": null}}]}