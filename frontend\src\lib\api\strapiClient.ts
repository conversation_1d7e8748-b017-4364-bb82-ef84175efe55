import { buildQueryParams, StrapiQueryParams } from './queryBuilder';

export type FetchOptions = {
  cache?: RequestCache;
  next?: {
    revalidate?: number | false;
    tags?: string[];
  };
  headers?: HeadersInit;
  method?: string;
  body?: BodyInit | null;
};

// Ensure NEXT_PUBLIC_STRAPI_API_URL is set in your .env.local or environment variables
const BASE_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL;
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN; // Server-side or build-time token

if (!BASE_URL) {
  console.error(
    'CRITICAL: NEXT_PUBLIC_STRAPI_API_URL is not defined. Please set it in your environment variables.'
  );
  // Depending on the desired behavior, you might throw an error here during build/startup in non-production,
  // or use a non-functional fallback for production if absolutely necessary (though not recommended).
  // For now, we'll log and let it proceed, which might lead to runtime errors if not caught.
}
// STRAPI_API_TOKEN is optional for public GET requests but crucial for mutations or draft content.
// A check for it could be added here if it's always required for your setup.

// Default revalidation time (e.g., 12 hours)
const DEFAULT_REVALIDATION_SECONDS = 60 * 60 * 12;

export interface StrapiResponse<T> {
  data: T;
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
    // Add other meta fields if necessary
  };
  error?: {
    status: number;
    name: string;
    message: string;
    details: any;
  };
}

// Utility function to create a standardized error response object
export function createErrorResponse<T>(
  status: number,
  name: string,
  message: string,
  details: any = {}
): StrapiResponse<T> {
  return {
    data: null as T, // Ensures the data field is present and typed correctly as null
    error: {
      status,
      name,
      message,
      details,
    },
  };
}

export async function fetchFromStrapi<T = any>(
  endpoint: string,
  queryParams: StrapiQueryParams = {},
  options: FetchOptions = {}
): Promise<StrapiResponse<T>> {
  const params = buildQueryParams(queryParams);
  const url = `${BASE_URL}/api/${endpoint}${params.toString() ? `?${params.toString()}` : ''}`;

  const mergedHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    ...(STRAPI_API_TOKEN && { Authorization: `Bearer ${STRAPI_API_TOKEN}` }),
    ...options.headers,
  };

  const fetchOptions: RequestInit = {
    method: options.method || 'GET',
    headers: mergedHeaders,
    // Explicitly opt-in to caching for Next.js 15 as per new defaults
    // 'force-cache' is the default for `fetch` if `revalidate` is set in `next` object.
    // If `next.revalidate` is not set, it defaults to `cache: 'no-store'` in Next.js 15.
    // We want to control this explicitly.
    cache: options.cache || (options.next?.revalidate !== undefined ? 'force-cache' : 'no-store'),
    next: {
      revalidate: options.next?.revalidate ?? DEFAULT_REVALIDATION_SECONDS,
      ...(options.next?.tags && { tags: options.next.tags }),
    },
    ...(options.body && { body: options.body }),
  };

  // If revalidate is false, it means we want to fetch fresh data every time (dynamic rendering)
  if (options.next?.revalidate === false) {
    fetchOptions.cache = 'no-store';
    // @ts-ignore // revalidate might not be expected if cache is 'no-store' by some typings, but Next.js handles it.
    fetchOptions.next = { ...fetchOptions.next, revalidate: 0 };
  }


  try {
    const response = await fetch(url, fetchOptions);

    if (!response.ok) {
      const errorBody = await response.json().catch(() => ({ message: response.statusText }));
      console.error(`Strapi API Error (${response.status}) for ${url}:`, errorBody);
      // Construct a StrapiResponse compatible error
      return createErrorResponse<T>(
        response.status,
        'StrapiApiError',
        errorBody.error?.message || errorBody.message || response.statusText,
        errorBody.error?.details || errorBody.details || errorBody
      );
    }

    const data = await response.json();
    // Ensure the response has a .data property, even if Strapi returns raw object for single types sometimes
    // However, our StrapiResponse type expects `data` to be the primary container.
    // If Strapi returns { id: 1, attributes: {...} } directly for a single type without a `data` wrapper,
    // we might need to adjust this or ensure Strapi always wraps.
    // For now, assuming Strapi's response structure matches `StrapiResponse<T>` or `data` is the direct payload.
    // If `data.data` exists, it's likely a Strapi v4/v5 collection response.
    // If `data` itself is the payload (e.g. for single types), it should be assigned to `response.data`.
    // The current `StrapiResponse<T>` expects `data: T`.
    // If `response.json()` returns `{ data: ActualData, meta: ... }`, it's fine.
    // If `response.json()` returns `ActualData` (for single types), we need to wrap it.
    // Let's assume `response.json()` returns the full StrapiResponse structure including the top-level `data` field.
    return data as StrapiResponse<T>;
  } catch (error) {
    console.error(`Network or other error fetching from Strapi for ${url}:`, error);
    return createErrorResponse<T>(
      500,
      'NetworkError',
      error instanceof Error ? error.message : 'An unknown network error occurred',
      error
    );
  }
}
