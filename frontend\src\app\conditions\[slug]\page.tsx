import Link from 'next/link';
import { Fi<PERSON>apPin, FiFilter } from 'react-icons/fi';
import ExploreFurther from '@/components/shared/ExploreFurther';
import { getStrapiContent } from '@/lib/strapi';
import { notFound } from 'next/navigation';
import SearchInput from '@/components/shared/SearchInput';
import { Metadata } from 'next';
import { getOgImageUrl } from '@/lib/mediaUtils';
import TabSwitcher from '@/components/shared/TabSwitcher';
import TabContent from '@/components/shared/TabContent';

// Force static rendering for this route segment
export const dynamic = 'force-static';

// Enable ISR with both time-based and on-demand revalidation
// Use a 12-hour revalidation period
export const revalidate = 43200; // 12 hours in seconds

// Allow dynamic params to be generated on-demand for any new conditions
// created between builds
export const dynamicParams = true;

// Define a simple type for items with a slug (can be moved to a shared types file)
interface SlugItem {
  slug: string;
}

// Generate static paths for all conditions at build time
export async function generateStaticParams() {
  try {
    // In Next.js 15, explicitly opt-in to caching
    const response = await getStrapiContent.conditions.getAllSlugs({
      cache: 'force-cache',
      next: {
        revalidate: 43200, // Revalidate slugs every 12 hours
        tags: ['strapi-conditions-slugs']
      }
    });

    if (response && response.data && Array.isArray(response.data)) {
      console.log(`Pre-rendering ${response.data.length} condition detail pages`);

      return response.data
        .filter((item: SlugItem | null): item is SlugItem => item !== null && typeof item.slug === 'string')
        .map((item: SlugItem) => ({
          slug: item.slug,
        }));
    }

    return [];
  } catch (error) {
    console.error('Error fetching condition slugs for generateStaticParams:', error);
    return []; // Return empty array on error to prevent build failure
  }
}

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL;

// Define the site URL from environment variable with proper fallback to your actual domain
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log the URL being used for debugging
if (process.env.NODE_ENV === 'production') {
  console.log(`Using site URL for canonical URLs: ${SITE_URL}`);
}

// Helper to create absolute URL for Strapi assets - enhanced for Strapi v5 and better debugging
const getStrapiMediaUrl = (urlOrObject: any): string | null => {
  console.log("getStrapiMediaUrl input:", JSON.stringify({
    type: typeof urlOrObject,
    isNull: urlOrObject === null,
    isUndefined: urlOrObject === undefined,
    value: urlOrObject
  }, null, 2));

  // If null/undefined, return null
  if (!urlOrObject) return null;

  // If it's a string URL
  if (typeof urlOrObject === 'string') {
    console.log("URL is a string:", urlOrObject);
    // If already absolute, return as is
    if (urlOrObject.startsWith('http://') || urlOrObject.startsWith('https://')) {
      return urlOrObject;
    }
    // Otherwise, prepend Strapi URL
    return `${STRAPI_URL}${urlOrObject}`;
  }

  // Handle Strapi v5 media structure with nested data
  if (urlOrObject && typeof urlOrObject === 'object') {
    console.log("URL is an object with keys:", Object.keys(urlOrObject));

    // Different ways the URL might be stored in Strapi v5
    const url =
      urlOrObject.url || // Direct url property
      urlOrObject.data?.attributes?.url || // Nested in data.attributes (v4 style)
      urlOrObject.data?.url || // Nested in data
      null;

    if (url) {
      console.log("Extracted URL from object:", url);
      // Check if it's already absolute
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      // Otherwise, prepend Strapi URL
      return `${STRAPI_URL}${url}`;
    }
  }

  console.warn("Could not extract URL from:", urlOrObject);
  return null;
};

// --- Type Definitions ---

// Raw Strapi types (adjust based on actual API response)
interface RawStrapiMedia {
  url?: string;
  // Add other potential media fields if needed (e.g., alternativeText)
}

// Reusable Address type
interface RawStrapiAddress {
  city: string;
  stateProvince: string;
  // Add other address fields if needed
}

interface RawStrapiContactInfo {
  phoneNumber?: string;
  websiteUrl?: string;
}

interface RawStrapiClinic {
  id: string;
  documentId: string;
  name: string;
  slug: string;
  description?: string | null;
  logo?: RawStrapiMedia | null;
  featuredImage?: RawStrapiMedia | null;
  address?: RawStrapiAddress | null;
  contactInfo?: RawStrapiContactInfo | null;
  isVerified?: boolean; // Add isVerified
}

interface RawStrapiPractitioner {
  id: string;
  documentId: string;
  name: string;
  slug: string;
  title?: string | null;
  qualifications?: string | null;
  profilePicture?: RawStrapiMedia | null; // Assuming a profile picture field
  address?: RawStrapiAddress | null; // Add address field
  isVerified?: boolean; // Add isVerified
  bio?: string | null; // Add bio field
  // Add other practitioner fields if needed
}

// Define the structure for a Condition based on Strapi API
interface RawStrapiCondition {
  id: string;
  documentId: string;
  name: string;
  slug: string;
  description?: string | null;
  featuredImage?: RawStrapiMedia | null;
  clinics?: RawStrapiClinic[]; // Assuming conditions can be linked to clinics
  practitioners?: RawStrapiPractitioner[]; // Assuming conditions can be linked to practitioners
  // Add other condition-specific fields if they exist in Strapi
}

// Transformed types for components
interface TransformedClinic {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  logo: string | null;
  featuredImage: string | null;
  address: {
    city: string;
    stateProvince: string;
  };
  contactInfo?: {
    phoneNumber?: string;
    websiteUrl?: string;
  } | null;
  isVerified?: boolean; // Add isVerified
}

interface TransformedPractitioner {
  id: string;
  name: string;
  slug: string;
  title?: string | null;
  qualifications?: string | null;
  profilePicture: string | null;
  isVerified?: boolean; // Add isVerified
  bio?: string | null; // Add bio field
}

// Define the possible tab types to avoid TypeScript comparison errors
type TabType = 'clinics' | 'practitioners';

// --- Data Fetching & Transformation ---

// TODO: Ensure Strapi API populates clinics & practitioners deeply, including isVerified
async function getConditionData(slug: string): Promise<RawStrapiCondition | null> {
  try {
    // Use a cache-first approach with the same revalidation period as the page
    const response = await getStrapiContent.conditions.getBySlug(slug, {
      next: {
        tags: ['strapi-conditions-list', `strapi-condition-${slug}`],
        revalidate: 43200 // 12 hours to match the page-level setting
      },
      cache: 'force-cache' // Explicitly opt-in to caching for Next.js 15
    });
    // console.log("Strapi Condition Response:", JSON.stringify(response, null, 2)); // Keep commented unless debugging

    // Additional logging for the first clinic if present
    // if (response?.data?.[0]?.clinics?.[0]) {
    //   console.log("DETAILED CLINIC STRUCTURE FROM STRAPI V5 (via Condition):",
    //     JSON.stringify(response.data[0].clinics[0], null, 2)
    //   );
    // }

    // Strapi v5 returns data directly in an array when filtering
    if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
      // Assuming the first item is the correct condition
      return response.data[0] as RawStrapiCondition;
    }
    return null;
  } catch (error) {
    console.error(`Error fetching condition with slug ${slug}:`, error);
    return null;
  }
}

// transformClinicData and transformPractitionerData remain the same as they process clinic/practitioner data
function transformClinicData(rawClinic: RawStrapiClinic): TransformedClinic | null {
  // More detailed logging to understand the structure of rawClinic
  console.log("Raw clinic data structure:", JSON.stringify({
    hasData: !!rawClinic,
    id: rawClinic?.id,
    name: rawClinic?.name,
    keys: rawClinic ? Object.keys(rawClinic) : []
  }, null, 2));

  if (!rawClinic) {
    console.warn(`Skipping clinic due to missing data.`);
    return null;
  }

  // Check for essential fields like id and name
  if (!rawClinic.id || !rawClinic.name) {
    console.warn(`Skipping invalid clinic data: ID ${rawClinic?.id}`);
    return null;
  }

  // Process image URLs - Pass the whole object to the helper
  const logoUrl = getStrapiMediaUrl(rawClinic.logo);
  const featuredImageUrl = getStrapiMediaUrl(rawClinic.featuredImage);

  return {
    id: String(rawClinic.id),
    name: rawClinic.name,
    slug: rawClinic.slug || `clinic-${rawClinic.id}`,
    description: rawClinic.description,
    logo: logoUrl,
    featuredImage: featuredImageUrl,
    address: rawClinic.address || { city: 'Unknown', stateProvince: 'N/A' },
    contactInfo: rawClinic.contactInfo,
    isVerified: rawClinic.isVerified || false, // Pass through isVerified
  };
}

function transformPractitionerData(rawPractitioner: RawStrapiPractitioner): TransformedPractitioner | null {
  // More detailed logging to understand the structure of rawPractitioner
  console.log("Raw practitioner data structure:", JSON.stringify({
    hasData: !!rawPractitioner,
    id: rawPractitioner?.id,
    name: rawPractitioner?.name,
    keys: rawPractitioner ? Object.keys(rawPractitioner) : []
  }, null, 2));

  if (!rawPractitioner) {
    console.warn(`Skipping practitioner due to missing data.`);
    return null;
  }

  // Check for essential fields like id and name
  if (!rawPractitioner.id || !rawPractitioner.name) {
    console.warn(`Skipping invalid practitioner data: ID ${rawPractitioner?.id}`);
    return null;
  }

  return {
    id: String(rawPractitioner.id),
    name: rawPractitioner.name,
    slug: rawPractitioner.slug || `practitioner-${rawPractitioner.id}`,
    title: rawPractitioner.title,
    qualifications: rawPractitioner.qualifications,
    profilePicture: getStrapiMediaUrl(rawPractitioner.profilePicture),
    isVerified: rawPractitioner.isVerified || false,
    bio: rawPractitioner.bio, // Add bio field
  };
}

// --- Page Component ---

interface PageParams {
  slug: string;
}

// Generate Metadata
export async function generateMetadata({ params }: { params: PageParams }): Promise<Metadata> {
  const awaitedParams = await params;
  const conditionData = await getConditionData(awaitedParams.slug); // Renamed for clarity

  if (!conditionData) {
    return {
      title: 'Condition Not Found | Natural Healing Now',
      description: 'The requested condition could not be found.',
      // No canonical for not found pages
    };
  }

  // Extract SEO data from the fetched condition data
  // Assuming conditionData is the object { id: ..., attributes: { ... } }
  const attributes = (conditionData as any)?.attributes || conditionData || {}; // Try accessing attributes, fallback to conditionData itself
  const seo = attributes.seo; // Access seo from attributes or the main object

  const defaultTitle = `${attributes.name || conditionData.name || 'Condition'} | Natural Healing Now`; // Check both levels
  const defaultDescription = attributes.description || conditionData.description || `Learn about ${attributes.name || conditionData.name || 'this condition'} and find related clinics and practitioners.`; // Check both levels

  const metaTitle = seo?.metaTitle || defaultTitle;
  const metaDescription = seo?.metaDescription || defaultDescription;
  const metaImageUrl = getStrapiMediaUrl(seo?.metaImage); // Use helper for image URL

  const canonicalPath = `/conditions/${conditionData.slug}`; // Use slug from original data object
  const canonicalUrl = seo?.canonicalURL || (SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath);

  // Prepare Open Graph data
  let ogImageUrl: string | undefined = undefined;

  // First try to get image from SEO plugin's openGraph data
  if (seo?.openGraph?.image) {
    ogImageUrl = getOgImageUrl(getStrapiMediaUrl(seo.openGraph.image));
  } else if (seo?.metaImage) {
    ogImageUrl = getOgImageUrl(metaImageUrl); // Fallback to metaImage
  }

  // Log the image URL in production for debugging
  if (process.env.NODE_ENV === 'production') {
    console.log('Condition og:image:', {
      openGraphImage: seo?.openGraph?.image,
      metaImage: seo?.metaImage,
      ogImageUrl
    });
  }

  // Use openGraph data from SEO plugin if available
  const openGraphData: any = seo?.openGraph ? {
    title: seo.openGraph.title || metaTitle,
    description: seo.openGraph.description || metaDescription,
    url: seo.openGraph.url || canonicalUrl,
    type: seo.openGraph.type || 'website',
    siteName: seo.openGraph.siteName || 'Natural Healing Now',
    ...(ogImageUrl && { images: [{ url: ogImageUrl }] }),
  } : {
    // Fallback if no openGraph data in SEO plugin
    title: metaTitle,
    description: metaDescription,
    url: canonicalUrl,
    type: 'website',
    siteName: 'Natural Healing Now',
    ...(ogImageUrl && { images: [{ url: ogImageUrl }] }),
  };

  return {
    title: metaTitle,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: openGraphData,
    twitter: {
      card: 'summary_large_image',
      title: seo?.openGraph?.title || metaTitle,
      description: seo?.openGraph?.description || metaDescription,
      images: ogImageUrl ? [ogImageUrl] : [],
    },
  };
}

// Define the interface for the searchParams object, adding query, location, and page
interface ConditionPageSearchParams {
  tab?: string;
  query?: string;
  location?: string; // Add location param
  page?: string;
}

// Main Page Component - Renamed to ConditionDetailPage
export default async function ConditionDetailPage({
  params,
  searchParams
}: {
  params: PageParams;
  searchParams: ConditionPageSearchParams; // Use updated interface
}) {
  const awaitedParams = await params; // Keep this if needed for Next.js version
  const slug = awaitedParams.slug;

  // Await searchParams before accessing its properties
  const awaitedSearchParams = await searchParams;
  const query = awaitedSearchParams?.query || '';
  const location = awaitedSearchParams?.location || ''; // Get location param
  const currentPage = Number(awaitedSearchParams?.page) || 1;
  const rawTabValue = awaitedSearchParams?.tab || 'clinics';
  const activeTab: TabType = (rawTabValue === 'practitioners') ? 'practitioners' : 'clinics';

  // 1. Fetch only basic condition details first
  const condition = await getConditionData(slug);

  if (!condition) {
    notFound(); // Show 404 if condition data is not found
  }

  // Destructure condition data
  const {
    name = 'Unnamed Condition',
    description = 'No description available.',
    featuredImage = null,
    // clinics and practitioners will be fetched based on active tab and filters
  } = condition;

  const conditionImageUrl = getStrapiMediaUrl(featuredImage);

  // 2. Fetch counts for both clinics and practitioners, and detailed data for both tabs
  let clinicCount = 0;
  let practitionerCount = 0;
  let totalPages = 1;

  // Determine caching strategy based on search parameters
  const hasFilters = query || location;
  const fetchOptions = hasFilters
    ? { cache: 'no-store' } // Don't cache filtered results
    : {
        next: {
          tags: ['strapi-conditions-list', `strapi-condition-${slug}`],
          revalidate: 43200 // 12 hours
        },
        cache: 'force-cache'
      };

  // Fetch counts first
  try {
    const clinicCountResponse = await getStrapiContent.clinics.getAll({
      conditionSlug: slug,
      pagination: { page: 1, pageSize: 1 },
    });
    clinicCount = clinicCountResponse?.meta?.pagination?.total || 0;

    const practitionerCountResponse = await getStrapiContent.practitioners.getAll({
      conditionSlug: slug,
      pagination: { page: 1, pageSize: 1 },
    });
    practitionerCount = practitionerCountResponse?.meta?.pagination?.total || 0;
    console.log(`Counts for condition ${slug}: Clinics=${clinicCount}, Practitioners=${practitionerCount}`);
  } catch (error) {
    console.error("Error fetching counts for condition page:", error);
  }

  // Fetch clinics data
  let clinics: TransformedClinic[] = [];
  let clinicPages = 1;
  try {
    console.log(`Fetching clinics for condition ${slug}, activeTab: ${activeTab}, query: ${query}, location: ${location}, currentPage: ${currentPage}`);
    const clinicResponse = await getStrapiContent.clinics.getAll({
      conditionSlug: slug,
      query: activeTab === 'clinics' ? query : '',
      location: activeTab === 'clinics' ? location : '',
      page: activeTab === 'clinics' ? currentPage : 1,
      ...fetchOptions
    });
    console.log(`Clinic response data length for condition ${slug}: ${clinicResponse?.data?.length || 0}`);
    clinics = (clinicResponse?.data || []).map((item: any) => {
      const clinicDataToTransform = item.attributes || item;
      if (item?.id && !clinicDataToTransform.id) clinicDataToTransform.id = item.id;
      return transformClinicData(clinicDataToTransform);
    }).filter(Boolean as unknown as (value: TransformedClinic | null) => value is TransformedClinic);
    console.log(`Transformed clinics array length for condition ${slug}: ${clinics.length}`);
    clinicPages = clinicResponse?.meta?.pagination?.pageCount || 1;
  } catch (error) {
    console.error(`Error fetching clinics for condition ${slug}:`, error);
  }

  // Fetch practitioners data
  let practitioners: TransformedPractitioner[] = [];
  let practitionerPages = 1;
  try {
    console.log(`Fetching practitioners for condition ${slug}, activeTab: ${activeTab}, query: ${query}, location: ${location}, currentPage: ${currentPage}`);
    const practitionerResponse = await getStrapiContent.practitioners.getAll({
      conditionSlug: slug,
      query: activeTab === 'practitioners' ? query : '',
      location: activeTab === 'practitioners' ? location : '',
      page: activeTab === 'practitioners' ? currentPage : 1,
      ...fetchOptions
    });
    console.log(`Practitioner raw response data length for condition ${slug}: ${practitionerResponse?.data?.length || 0}`);
    if (practitionerResponse?.data && practitionerResponse.data.length > 0) {
      const firstRawItem = practitionerResponse.data[0];
      console.log(`First raw practitioner item structure for condition ${slug}:`, JSON.stringify({
        id: firstRawItem.id,
        hasAttributes: !!firstRawItem.attributes,
        attributesType: firstRawItem.attributes ? typeof firstRawItem.attributes : 'N/A',
        keys: Object.keys(firstRawItem),
        attributeKeys: firstRawItem.attributes ? Object.keys(firstRawItem.attributes) : []
      }, null, 2));
    }
    practitioners = (practitionerResponse?.data || []).map((item: any) => {
      const practitionerRawData = item.attributes || item;
      if (item?.id && !practitionerRawData.id) practitionerRawData.id = item.id;
      // console.log(`Processing practitioner for condition ${slug}: ${practitionerRawData.name || 'unnamed'}, ID: ${practitionerRawData.id || 'no-id'}`);
      if (!practitionerRawData.id || !practitionerRawData.name) {
        console.warn(`Practitioner data missing id or name for condition ${slug}. ID: ${practitionerRawData.id}, Name: ${practitionerRawData.name}. Full item:`, JSON.stringify(item));
      }
      return transformPractitionerData(practitionerRawData);
    }).filter((p: TransformedPractitioner | null): p is TransformedPractitioner => {
      if (p === null) {
        // console.warn(`A practitioner was filtered out by transformPractitionerData for condition ${slug}.`);
      }
      return p !== null;
    });
    console.log(`Transformed practitioners array length for condition ${slug}: ${practitioners.length}`);
    practitionerPages = practitionerResponse?.meta?.pagination?.pageCount || 1;
  } catch (error) {
    console.error(`Error fetching practitioners for condition ${slug}:`, error);
  }

  totalPages = activeTab === 'clinics' ? clinicPages : practitionerPages;

  // No longer need the isTabActive function as it's handled by the client components

  // Extract SEO data from condition
  const attributes = (condition as any)?.attributes || condition || {};
  const seo = attributes.seo;

  // Ensure structuredData is a string before rendering
  let structuredDataString: string | null = null;
  if (seo?.structuredData) {
    if (typeof seo.structuredData === 'string') {
      structuredDataString = seo.structuredData;
    } else if (typeof seo.structuredData === 'object') {
      try {
        structuredDataString = JSON.stringify(seo.structuredData);
      } catch (e) {
        console.error("Failed to stringify structuredData object:", e);
      }
    }
  }

  // If no structured data from SEO plugin, create a default one
  if (!structuredDataString) {
    // Create default structured data for condition
    const defaultStructuredData = {
      "@context": "https://schema.org",
      "@type": "MedicalCondition",
      "name": name,
      "description": description,
      "url": `${process.env.NEXT_PUBLIC_SITE_URL || ''}/conditions/${slug}`,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `${process.env.NEXT_PUBLIC_SITE_URL || ''}/conditions/${slug}`
      }
    };

    structuredDataString = JSON.stringify(defaultStructuredData);
  }

  return (
    <>
      {/* Structured Data (JSON-LD) */}
      {structuredDataString && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: structuredDataString }}
        />
      )}
      {/* Breadcrumb - Updated for Conditions */}
      <div className="bg-gray-100 py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-emerald-600">Home</Link>
            {/* Optional: Add a link to a general conditions listing page if it exists */}
            {/* <span className="mx-2">/</span> */}
            {/* <Link href="/conditions" className="hover:text-emerald-600">Conditions</Link> */}
            <span className="mx-2">/</span>
            <span className="text-gray-800">{name}</span> {/* Display condition name */}
          </div>
        </div>
      </div>

      {/* Condition Header - Updated for Conditions */}
      <div className="bg-emerald-600 text-white py-12 relative overflow-hidden">
        {conditionImageUrl && (
          <div
            className="absolute inset-0 opacity-20 bg-cover bg-center"
            style={{ backgroundImage: `url(${conditionImageUrl})` }}
          ></div>
        )}
        <div className="container mx-auto px-4 relative z-10">
          {/* Removed icon logic as conditions might not have icons */}
          <h1 className="text-3xl md:text-4xl font-bold mb-4">{name}</h1>
          {description && <p className="text-lg max-w-3xl mb-4">{description}</p>}
          <div className="flex gap-4 text-sm">
            {/* Show counts for related clinics/practitioners */}
            <span>{clinicCount} Related Clinics</span>
            <span>&bull;</span>
            <span>{practitionerCount} Related Practitioners</span>
          </div>
        </div>
      </div>

      {/* Search and Filter Section - Kept generic */}
      {/* Search and Filter Section - Updated */}
      <div className="bg-white shadow-sm sticky top-0 z-20">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
               <SearchInput placeholder={`Search clinics/practitioners for ${name}...`} paramName="query" />
            </div>
             {/* Location Input - Replaced with SearchInput */}
            <div className="flex-1">
               <SearchInput placeholder="City, state, or zip code" paramName="location" icon={<FiMapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />} />
            </div>
            <div>
              <button className="w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200">
                <FiFilter />
                <span>Filters</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs & Results Section - Using client-side components */}
      <div className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          {/* Use the client-side TabSwitcher component */}
          <TabSwitcher
            slug={awaitedParams.slug}
            pageType="conditions"
            clinicCount={clinicCount}
            practitionerCount={practitionerCount}
            initialTab={activeTab}
          />

          {/* Use the client-side TabContent component */}
          <TabContent
            clinics={clinics}
            practitioners={practitioners}
            totalPages={totalPages}
            initialTab={activeTab}
          />
        </div>
      </div>

      {/* Removed Related Conditions Section */}

      {/* Use the ExploreFurther component */}
      <ExploreFurther />
    </>
  );
}
