"use strict";exports.id=8319,exports.ids=[8319],exports.modules={26800:(e,t,s)=>{s.d(t,{default:()=>d});var a=s(60687),r=s(85814),l=s.n(r),i=s(17019),n=s(20255);let d=({clinic:e,showContactInfo:t=!0,prefetchedData:s=!1})=>{let r=s?{pathname:`/clinics/${e.slug}`,query:{prefetched:"true"}}:`/clinics/${e.slug}`;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,a.jsxs)("div",{className:"p-4 flex-grow",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,a.jsx)(l(),{href:r,className:"hover:text-emerald-600",children:e.name})}),e.isVerified&&(0,a.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,a.jsx)(n.AI8,{color:"#009967",size:14}),(0,a.jsx)("span",{children:"VERIFIED"})]}),e.description&&(0,a.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[e.address&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.HzC,{className:"mr-2 text-emerald-500"}),(0,a.jsxs)("span",{children:[e.address.city,", ",e.address.stateProvince]})]}),t&&e.contactInfo?.phoneNumber&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.QFc,{className:"mr-2 text-emerald-500"}),(0,a.jsx)("span",{children:e.contactInfo.phoneNumber})]}),t&&e.contactInfo?.websiteUrl&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.VeH,{className:"mr-2 text-emerald-500"}),(0,a.jsx)("a",{href:e.contactInfo.websiteUrl,target:"_blank",rel:"nofollow noopener noreferrer",className:"hover:text-emerald-600",children:"Visit Website"})]})]})]}),(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,a.jsx)(l(),{href:r,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Details →"})})]})}},28831:(e,t,s)=>{s.d(t,{A:()=>i});var a=s(60687),r=s(43210),l=s(17019);let i=({placeholder:e="Search...",onSearch:t,buttonText:s="Search",className:i="",buttonClassName:n="",initialValue:d=""})=>{let[o,c]=(0,r.useState)(d);return(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(o)},className:`flex w-full ${i}`,children:[(0,a.jsxs)("div",{className:"relative flex-grow",children:[(0,a.jsx)("input",{type:"text",value:o,onChange:e=>c(e.target.value),placeholder:e,className:"w-full px-10 py-2 text-gray-700 bg-white border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-emerald-500","aria-label":"Search"}),(0,a.jsx)(l.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400"})]}),(0,a.jsx)("button",{type:"submit",className:`px-6 py-2 font-medium text-white bg-emerald-600 rounded-r-lg hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 ${n}`,children:s})]})}},30104:(e,t,s)=>{s.d(t,{default:()=>o});var a=s(60687),r=s(85814),l=s.n(r),i=s(20255),n=s(43210),d=s(68326);let o=({practitioner:e,prefetchedData:t=!1})=>{let[s,r]=(0,n.useState)(!1),o=(0,n.useRef)(!1);(0,n.useEffect)(()=>{if(o.current)return;let t=(0,d.b3)(e.slug);t&&r(!0),e._hasDetailedData&&!t&&((0,d.tq)(e),r(!0)),o.current=!0},[e]);let c=t||e._hasDetailedData||s?{pathname:`/practitioners/${e.slug}`,query:{prefetched:"true"}}:`/practitioners/${e.slug}`;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,a.jsxs)("div",{className:"p-4 flex-grow",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,a.jsx)(l(),{href:c,className:"hover:text-emerald-600",children:e.name})}),e.isVerified&&(0,a.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,a.jsx)(i.AI8,{color:"#009967",size:14}),(0,a.jsx)("span",{children:"VERIFIED"})]}),e.bio&&(0,a.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:e.bio})]}),(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,a.jsx)(l(),{href:c,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Profile →"})})]})}},68326:(e,t,s)=>{s.d(t,{b3:()=>r,tq:()=>a});function a(e){var t;if(!e||!e.id||!e.slug)return;let s=(e.slug,null);s&&(!e._hasDetailedData||s._hasDetailedData)||function(e,t,s=3e5){}(`practitioner_${e.slug}`,0,18e5)}function r(e){var t;return null}},96647:(e,t,s)=>{s.d(t,{default:()=>d});var a=s(60687),r=s(24587),l=s(85814),i=s.n(l),n=s(28136);let d=({category:e})=>{let t=t=>t?"object"==typeof t?t.url?t.url:t.data?.attributes?.url?t.data.attributes.url:(console.log(`Could not extract URL from image object for ${e.name}:`,t),""):t:"",s=t(e.featured_image),l=t(e.icon),d=s?(0,n.Jf)(s):"",o=l?(0,n.Jf)(l):"",c=!!d;return console.log(`CategoryCard for ${e.name}:`,{originalFeaturedImage:e.featured_image,extractedFeaturedImage:s,sanitizedFeaturedImage:d,originalIcon:e.icon,extractedIcon:l,sanitizedIcon:o,hasImage:c}),(0,a.jsx)(i(),{href:`/categories/${e.slug}`,className:"block group",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform group-hover:shadow-lg group-hover:-translate-y-1",children:[(0,a.jsxs)("div",{className:"relative h-40 w-full",children:[c&&d?(0,a.jsx)(r.default,{src:d,alt:e.name,width:400,height:300,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1,showPlaceholder:!0}):(0,a.jsx)("div",{className:"absolute inset-0 bg-purple-200 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-purple-700 font-semibold text-xl",children:e.name.charAt(0)})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"}),e.icon&&o&&(0,a.jsx)("div",{className:"absolute top-4 left-4 bg-white p-2 rounded-full shadow-md",children:(0,a.jsxs)("div",{className:"relative h-8 w-8",children:[" ",(0,a.jsx)(r.default,{src:o,alt:`${e.name} icon`,width:32,height:32,fillContainer:!0,className:"object-cover rounded-full",sizes:"32px",showPlaceholder:!1})]})}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:e.name})})]}),e.description&&(0,a.jsx)("div",{className:"p-4",children:(0,a.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:e.description})}),(0,a.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100",children:(0,a.jsxs)("span",{className:"text-emerald-600 group-hover:text-emerald-700 font-medium text-sm flex items-center",children:["Browse Clinics",(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})})}}};