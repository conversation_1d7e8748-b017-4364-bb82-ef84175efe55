'use server';

import React from 'react';
import Link from 'next/link';
import { FiAlertTriangle, FiHome } from 'react-icons/fi';

interface ServerErrorProps {
  message?: string;
  statusCode?: number;
  showHomeLink?: boolean;
}

/**
 * A server component for displaying errors in server components
 */
export default function ServerError({
  message = 'Something went wrong on the server',
  statusCode = 500,
  showHomeLink = true,
}: ServerErrorProps) {
  // Determine if we should show technical error details
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6 my-4 max-w-2xl mx-auto">
      <div className="flex items-center mb-4 text-red-600">
        <FiAlertTriangle className="w-6 h-6 mr-2" />
        <h2 className="text-xl font-semibold">Server Error</h2>
      </div>
      
      <p className="mb-2 text-gray-700">{message}</p>
      
      {isDevelopment && (
        <div className="mb-4 p-3 bg-gray-100 rounded text-sm">
          <p className="font-mono text-red-600">Status Code: {statusCode}</p>
        </div>
      )}
      
      <div className="flex flex-wrap gap-3 mt-4">
        {showHomeLink && (
          <Link href="/" className="flex items-center px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors">
            <FiHome className="mr-2" />
            Go to Homepage
          </Link>
        )}
      </div>
    </div>
  );
}
