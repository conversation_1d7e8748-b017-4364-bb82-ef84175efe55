# Security Best Practices

This document outlines security best practices for the Natural Healing Now project, which uses Strapi 5 for the backend and Next.js 15 for the frontend.

## API Tokens

### Strapi API Tokens

1. **Create Specific Tokens**: Create separate API tokens for different purposes (e.g., one for the frontend, one for import scripts).
2. **Limit Permissions**: Assign only the necessary permissions to each token.
3. **Token Rotation**: Regularly rotate API tokens, especially in production.
4. **Environment Variables**: Store API tokens in environment variables, never in code.
5. **Server-Side Only**: Only use API tokens in server-side code, never in client-side code.

### Creating a Strapi API Token

1. Log in to the Strapi admin panel
2. Go to Settings > API Tokens
3. Click "Create new API Token"
4. Set a name, description, and expiration date
5. Select the appropriate permissions
6. Save the token and add it to your environment variables

## CORS Settings

The CORS settings have been configured in `strapi-cms/config/middlewares.ts` to only allow requests from trusted origins. If you need to add additional origins:

1. Update the `origin` array in the CORS configuration
2. Only add trusted domains
3. Avoid using wildcards (`*`) in production

## Rate Limiting

Rate limiting has been implemented to protect against abuse:

1. General API endpoints: 100 requests per minute per IP
2. Authentication endpoints: 10 requests per minute per IP

To adjust these limits, modify the `rate-limiter.ts` middleware.

## Content Security Policy (CSP)

A strict Content Security Policy has been implemented:

1. In development: A more permissive policy to avoid issues
2. In production: A strict policy that only allows resources from trusted sources

To add additional trusted sources, update the CSP configuration in:
- `frontend/src/middleware.ts` for the frontend
- `strapi-cms/config/middlewares.ts` for the backend

## Authentication Security

1. **HTTP-Only Cookies**: JWT tokens are stored in HTTP-only cookies
2. **Secure Cookies**: In production, cookies are only sent over HTTPS
3. **SameSite Policy**: Cookies use 'strict' or 'lax' SameSite policy
4. **Token Verification**: All protected routes verify the token before proceeding
5. **Server-Side Authentication**: Authentication is handled at the data access layer

## Security Headers

The following security headers have been implemented:

1. `X-Frame-Options: DENY` - Prevents clickjacking
2. `X-Content-Type-Options: nosniff` - Prevents MIME type sniffing
3. `Referrer-Policy: strict-origin-when-cross-origin` - Limits referrer information
4. `Permissions-Policy` - Restricts browser features
5. `Strict-Transport-Security` - Enforces HTTPS (in production)

## Error Handling

Proper error handling has been implemented to avoid leaking sensitive information:

1. Log detailed errors server-side
2. Return generic error messages to clients
3. Use appropriate HTTP status codes
4. Implement global error boundaries in React components

## Environment Variables

Sensitive information is stored in environment variables:

1. Create `.env` files based on the provided `.env.example` files
2. Generate strong, unique secrets for production
3. Keep different environment variables for development and production
4. Never commit `.env` files to version control

## Regular Updates

Keep dependencies up to date:

1. Regularly run `npm audit` to check for vulnerabilities
2. Update dependencies to fix security issues
3. Subscribe to security announcements for Strapi and Next.js

## Additional Resources

- [Strapi Security Documentation](https://docs.strapi.io/dev-docs/configurations/server)
- [Next.js Security Documentation](https://nextjs.org/docs/app/building-your-application/security)
- [OWASP Top Ten](https://owasp.org/www-project-top-ten/)
