/**
 * `import-clinics` command
 *
 * This command imports clinics from a CSV file into the Strapi database.
 */

module.exports = {
  command: 'import-clinics',
  description: 'Import clinics from CSV file',
  run: async ({ strapi }) => {
    try {
      const importClinics = require('../../scripts/import-clinics');
      await importClinics(strapi);
      process.exit(0);
    } catch (error) {
      console.error('Import failed:', error);
      process.exit(1);
    }
  }
};
