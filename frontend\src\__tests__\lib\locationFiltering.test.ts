/**
 * Test for location filtering logic in categories pages
 */

import { getStrapiContent } from '@/lib/strapi';

// Mock the fetchAPI function
jest.mock('@/lib/strapi', () => ({
  getStrapiContent: {
    clinics: {
      getAll: jest.fn(),
      getByCategorySlug: jest.fn(),
    },
    practitioners: {
      getAll: jest.fn(),
      getByCategorySlug: jest.fn(),
    },
  },
}));

describe('Location Filtering in Categories', () => {
  const mockClinicResponse = {
    data: [
      {
        id: 1,
        name: 'Test Clinic',
        slug: 'test-clinic',
        address: {
          city: 'New York',
          stateProvince: 'NY',
          postalCode: '10001',
        },
      },
    ],
    meta: {
      pagination: {
        total: 1,
        pageCount: 1,
      },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call getAll with categorySlug and location parameter for clinics', async () => {
    const mockGetAll = getStrapiContent.clinics.getAll as jest.MockedFunction<
      typeof getStrapiContent.clinics.getAll
    >;

    mockGetAll.mockResolvedValue(mockClinicResponse);

    // Simulate calling the function with categorySlug and location parameter
    await getStrapiContent.clinics.getAll({
      categorySlug: 'acupuncture',
      location: 'New York',
      page: 1,
      pageSize: 12,
    });

    expect(mockGetAll).toHaveBeenCalledWith({
      categorySlug: 'acupuncture',
      location: 'New York',
      page: 1,
      pageSize: 12,
    });
  });

  it('should call getAll with categorySlug and location parameter for practitioners', async () => {
    const mockGetAll = getStrapiContent.practitioners.getAll as jest.MockedFunction<
      typeof getStrapiContent.practitioners.getAll
    >;

    mockGetAll.mockResolvedValue(mockClinicResponse);

    // Simulate calling the function with categorySlug and location parameter
    await getStrapiContent.practitioners.getAll({
      categorySlug: 'acupuncture',
      location: 'California',
      page: 1,
      pageSize: 12,
    });

    expect(mockGetAll).toHaveBeenCalledWith({
      categorySlug: 'acupuncture',
      location: 'California',
      page: 1,
      pageSize: 12,
    });
  });

  it('should handle empty location parameter', async () => {
    const mockGetAll = getStrapiContent.clinics.getAll as jest.MockedFunction<
      typeof getStrapiContent.clinics.getAll
    >;

    mockGetAll.mockResolvedValue(mockClinicResponse);

    // Simulate calling the function without location parameter
    await getStrapiContent.clinics.getAll({
      categorySlug: 'acupuncture',
      location: '',
      page: 1,
      pageSize: 12,
    });

    expect(mockGetAll).toHaveBeenCalledWith({
      categorySlug: 'acupuncture',
      location: '',
      page: 1,
      pageSize: 12,
    });
  });
});
