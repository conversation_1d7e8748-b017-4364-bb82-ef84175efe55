/**
 * Test for location filtering logic in categories pages
 */

import { getStrapiContent } from '@/lib/strapi';

// Mock the fetchAPI function
jest.mock('@/lib/strapi', () => ({
  getStrapiContent: {
    clinics: {
      getByCategorySlug: jest.fn(),
    },
    practitioners: {
      getByCategorySlug: jest.fn(),
    },
  },
}));

describe('Location Filtering in Categories', () => {
  const mockClinicResponse = {
    data: [
      {
        id: 1,
        name: 'Test Clinic',
        slug: 'test-clinic',
        address: {
          city: 'New York',
          stateProvince: 'NY',
          postalCode: '10001',
        },
      },
    ],
    meta: {
      pagination: {
        total: 1,
        pageCount: 1,
      },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call getByCategorySlug with location parameter for clinics', async () => {
    const mockGetByCategorySlug = getStrapiContent.clinics.getByCategorySlug as jest.MockedFunction<
      typeof getStrapiContent.clinics.getByCategorySlug
    >;
    
    mockGetByCategorySlug.mockResolvedValue(mockClinicResponse);

    // Simulate calling the function with location parameter
    await getStrapiContent.clinics.getByCategorySlug({
      slug: 'acupuncture',
      location: 'New York',
      page: 1,
      pageSize: 12,
    });

    expect(mockGetByCategorySlug).toHaveBeenCalledWith({
      slug: 'acupuncture',
      location: 'New York',
      page: 1,
      pageSize: 12,
    });
  });

  it('should call getByCategorySlug with location parameter for practitioners', async () => {
    const mockGetByCategorySlug = getStrapiContent.practitioners.getByCategorySlug as jest.MockedFunction<
      typeof getStrapiContent.practitioners.getByCategorySlug
    >;
    
    mockGetByCategorySlug.mockResolvedValue(mockClinicResponse);

    // Simulate calling the function with location parameter
    await getStrapiContent.practitioners.getByCategorySlug({
      slug: 'acupuncture',
      location: 'California',
      page: 1,
      pageSize: 12,
    });

    expect(mockGetByCategorySlug).toHaveBeenCalledWith({
      slug: 'acupuncture',
      location: 'California',
      page: 1,
      pageSize: 12,
    });
  });

  it('should handle empty location parameter', async () => {
    const mockGetByCategorySlug = getStrapiContent.clinics.getByCategorySlug as jest.MockedFunction<
      typeof getStrapiContent.clinics.getByCategorySlug
    >;
    
    mockGetByCategorySlug.mockResolvedValue(mockClinicResponse);

    // Simulate calling the function without location parameter
    await getStrapiContent.clinics.getByCategorySlug({
      slug: 'acupuncture',
      location: '',
      page: 1,
      pageSize: 12,
    });

    expect(mockGetByCategorySlug).toHaveBeenCalledWith({
      slug: 'acupuncture',
      location: '',
      page: 1,
      pageSize: 12,
    });
  });
});
