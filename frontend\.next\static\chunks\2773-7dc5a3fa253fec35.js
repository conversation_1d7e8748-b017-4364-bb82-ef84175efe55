"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2773],{2204:(e,t,a)=>{a.d(t,{default:()=>o});var r=a(5155),s=a(6874),l=a.n(s),i=a(2515),n=a(2115),c=a(8864);let o=e=>{let{practitioner:t,prefetchedData:a=!1}=e,[s,o]=(0,n.useState)(!1),d=(0,n.useRef)(!1);(0,n.useEffect)(()=>{if(d.current)return;let e=(0,c.b3)(t.slug);e&&o(!0),t._hasDetailedData&&!e&&((0,c.tq)(t),o(!0)),d.current=!0},[t]);let m=a||t._hasDetailedData||s?{pathname:"/practitioners/".concat(t.slug),query:{prefetched:"true"}}:"/practitioners/".concat(t.slug);return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,r.jsxs)("div",{className:"p-4 flex-grow",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,r.jsx)(l(),{href:m,className:"hover:text-emerald-600",children:t.name})}),t.isVerified&&(0,r.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,r.jsx)(i.AI8,{color:"#009967",size:14}),(0,r.jsx)("span",{children:"VERIFIED"})]}),t.bio&&(0,r.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:t.bio})]}),(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,r.jsx)(l(),{href:m,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Profile →"})})]})}},3199:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5155),s=a(2115),l=a(351);let i=e=>{let{placeholder:t="Search...",onSearch:a,buttonText:i="Search",className:n="",buttonClassName:c="",initialValue:o=""}=e,[d,m]=(0,s.useState)(o);return(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),a(d)},className:"flex w-full ".concat(n),children:[(0,r.jsxs)("div",{className:"relative flex-grow",children:[(0,r.jsx)("input",{type:"text",value:d,onChange:e=>m(e.target.value),placeholder:t,className:"w-full px-10 py-2 text-gray-700 bg-white border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-emerald-500","aria-label":"Search"}),(0,r.jsx)(l.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400"})]}),(0,r.jsx)("button",{type:"submit",className:"px-6 py-2 font-medium text-white bg-emerald-600 rounded-r-lg hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 ".concat(c),children:i})]})}},6772:(e,t,a)=>{a.d(t,{default:()=>c});var r=a(5155),s=a(6874),l=a.n(s),i=a(351),n=a(2515);let c=e=>{var t,a;let{clinic:s,showContactInfo:c=!0,prefetchedData:o=!1}=e,d=o?{pathname:"/clinics/".concat(s.slug),query:{prefetched:"true"}}:"/clinics/".concat(s.slug);return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,r.jsxs)("div",{className:"p-4 flex-grow",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,r.jsx)(l(),{href:d,className:"hover:text-emerald-600",children:s.name})}),s.isVerified&&(0,r.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,r.jsx)(n.AI8,{color:"#009967",size:14}),(0,r.jsx)("span",{children:"VERIFIED"})]}),s.description&&(0,r.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:s.description}),(0,r.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[s.address&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.HzC,{className:"mr-2 text-emerald-500"}),(0,r.jsxs)("span",{children:[s.address.city,", ",s.address.stateProvince]})]}),c&&(null==(t=s.contactInfo)?void 0:t.phoneNumber)&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.QFc,{className:"mr-2 text-emerald-500"}),(0,r.jsx)("span",{children:s.contactInfo.phoneNumber})]}),c&&(null==(a=s.contactInfo)?void 0:a.websiteUrl)&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.VeH,{className:"mr-2 text-emerald-500"}),(0,r.jsx)("a",{href:s.contactInfo.websiteUrl,target:"_blank",rel:"nofollow noopener noreferrer",className:"hover:text-emerald-600",children:"Visit Website"})]})]})]}),(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,r.jsx)(l(),{href:d,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Details →"})})]})}},8864:(e,t,a)=>{a.d(t,{b3:()=>i,tq:()=>l});let r={};function s(e){let t=r[e],a=Date.now();if(t&&t.expiry>a)return t.data;try{let t=sessionStorage.getItem("cache_".concat(e));if(t){let s=JSON.parse(t);if(s.expiry>a)return r[e]=s,s.data;sessionStorage.removeItem("cache_".concat(e))}}catch(e){console.error("Error retrieving data from sessionStorage:",e)}return null}function l(e){if(!e||!e.id||!e.slug)return;let t=s("practitioner_".concat(e.slug));t&&(!e._hasDetailedData||t._hasDetailedData)||function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5,s=Date.now();r[e]={data:t,timestamp:s,expiry:s+a};try{sessionStorage.setItem("cache_".concat(e),JSON.stringify({data:t,timestamp:s,expiry:s+a}))}catch(e){console.error("Error storing data in sessionStorage:",e)}}("practitioner_".concat(e.slug),e,18e5)}function i(e){return s("practitioner_".concat(e))}},9981:(e,t,a)=>{a.d(t,{default:()=>c});var r=a(5155),s=a(9907),l=a(6874),i=a.n(l),n=a(2112);let c=e=>{let{category:t}=e,a=e=>{if(!e)return"";if("object"==typeof e){var a,r;return e.url?e.url:(null==(r=e.data)||null==(a=r.attributes)?void 0:a.url)?e.data.attributes.url:(console.log("Could not extract URL from image object for ".concat(t.name,":"),e),"")}return e},l=a(t.featured_image),c=a(t.icon),o=l?(0,n.Jf)(l):"",d=c?(0,n.Jf)(c):"",m=!!o;return console.log("CategoryCard for ".concat(t.name,":"),{originalFeaturedImage:t.featured_image,extractedFeaturedImage:l,sanitizedFeaturedImage:o,originalIcon:t.icon,extractedIcon:c,sanitizedIcon:d,hasImage:m}),(0,r.jsx)(i(),{href:"/categories/".concat(t.slug),className:"block group",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform group-hover:shadow-lg group-hover:-translate-y-1",children:[(0,r.jsxs)("div",{className:"relative h-40 w-full",children:[m&&o?(0,r.jsx)(s.default,{src:o,alt:t.name,width:400,height:300,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1,showPlaceholder:!0}):(0,r.jsx)("div",{className:"absolute inset-0 bg-purple-200 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-purple-700 font-semibold text-xl",children:t.name.charAt(0)})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"}),t.icon&&d&&(0,r.jsx)("div",{className:"absolute top-4 left-4 bg-white p-2 rounded-full shadow-md",children:(0,r.jsxs)("div",{className:"relative h-8 w-8",children:[" ",(0,r.jsx)(s.default,{src:d,alt:"".concat(t.name," icon"),width:32,height:32,fillContainer:!0,className:"object-cover rounded-full",sizes:"32px",showPlaceholder:!1})]})}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:(0,r.jsx)("h3",{className:"text-xl font-semibold text-white",children:t.name})})]}),t.description&&(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:t.description})}),(0,r.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100",children:(0,r.jsxs)("span",{className:"text-emerald-600 group-hover:text-emerald-700 font-medium text-sm flex items-center",children:["Browse Clinics",(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})})}}}]);