# Updated Strapi Webhook Setup for On-Demand Revalidation

This guide provides updated instructions for setting up webhooks in Strapi to trigger on-demand revalidation in your Next.js application.

## Current Issues

The current webhook configuration has several issues:

1. **Outdated URL**: The webhook is pointing to an outdated URL (`https://naturalhealingnow-directory-2hysxhy6h.vercel.app/api/revalidate`) instead of your current domain (`www.naturalhealingnow.com`).

2. **<PERSON><PERSON> vs. Body**: The token is being sent as a custom header (`x-revalidate-token`) instead of in the request body as expected by the revalidation API.

3. **Missing Content Type Information**: The webhook payload doesn't include the necessary content type, ID, or slug information needed for proper revalidation.

## Updated Webhook Configuration

Follow these steps to update your webhook configuration in Strapi:

### 1. Access Strapi Admin Panel

1. Log in to your Strapi admin panel
2. Navigate to Settings > Webhooks
3. Edit the existing webhook or create a new one

### 2. Update Webhook Settings

For each content type (blog posts, practitioners, clinics, etc.), configure the webhook as follows:

#### For Blog Posts

1. **Name**: `Revalidate Blog Posts`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "blog-post" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "blog-post",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

#### For Practitioners

1. **Name**: `Revalidate Practitioners`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "practitioner" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "practitioner",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

#### For Clinics

1. **Name**: `Revalidate Clinics`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "clinic" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "clinic",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

#### For Categories

1. **Name**: `Revalidate Categories`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "category" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "category",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

#### For Specialties

1. **Name**: `Revalidate Specialties`
2. **URL**: `https://www.naturalhealingnow.com/api/revalidate`
3. **Headers**:
   - `Content-Type`: `application/json`
4. **Events**:
   - Select all events under "Entry" for the "specialty" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. **Request body**:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "specialty",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

## Testing the Webhooks

After configuring the webhooks, you should test them to ensure they're working correctly:

1. Run the test script to verify the revalidation API is working:
   ```bash
   node scripts/test-revalidation.js
   ```

2. Make a change to a content item in Strapi (e.g., update a blog post)

3. Check the Strapi webhook logs to ensure the webhook was triggered:
   - In Strapi admin panel, go to Settings > Webhooks
   - Click on the webhook you want to check
   - Scroll down to see the recent deliveries

4. Check the Next.js logs to ensure the revalidation API was called:
   - In Vercel dashboard, go to your project
   - Click on "Logs" in the sidebar
   - Filter for "revalidation" to see the revalidation API logs

## Troubleshooting

If the webhooks aren't working as expected, check the following:

1. **Webhook URL**: Make sure the webhook URL is correct and accessible from the internet.

2. **Token**: Verify that the token in the webhook payload matches the `PREVIEW_SECRET` environment variable in your Next.js application.

3. **Content Type**: Ensure the content type in the webhook payload matches the expected values in your revalidation API.

4. **Strapi Version**: Confirm that your Strapi version supports the webhook features you're using.

5. **Network Issues**: Check if there are any network issues preventing the webhook from reaching your Next.js application.

## Additional Resources

- [Strapi Webhooks Documentation](https://docs.strapi.io/dev-docs/webhooks)
- [Next.js Revalidation Documentation](https://nextjs.org/docs/app/building-your-application/data-fetching/revalidating)
