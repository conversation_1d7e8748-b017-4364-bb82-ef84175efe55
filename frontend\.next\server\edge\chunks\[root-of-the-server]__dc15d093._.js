(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__dc15d093._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "default": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
function middleware(request) {
    const { pathname } = request.nextUrl;
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    // Add security headers to all responses
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    // Enhanced cache control headers for static assets to improve performance
    if (isStaticAsset(pathname)) {
        const cacheSettings = getCacheControlForPath(pathname);
        response.headers.set('Cache-Control', cacheSettings);
    }
    // Special handling for image optimization API routes
    if (pathname.startsWith('/_next/image')) {
        // Extend cache duration for improved performance
        // This is important as image optimization is a CPU-intensive task
        response.headers.set('Cache-Control', 'public, max-age=86400, immutable');
    }
    return response;
}
/**
 * Determine if a path is a static asset
 */ function isStaticAsset(pathname) {
    return(// Next.js static files
    pathname.startsWith('/_next/static') || // Public folder assets
    pathname.startsWith('/images/') || pathname.endsWith('.svg') || pathname.endsWith('.png') || pathname.endsWith('.jpg') || pathname.endsWith('.jpeg') || pathname.endsWith('.gif') || pathname.endsWith('.ico') || pathname.endsWith('.woff') || pathname.endsWith('.woff2') || pathname.endsWith('.ttf') || pathname.endsWith('.otf'));
}
/**
 * Get appropriate Cache-Control header value based on the path
 */ function getCacheControlForPath(pathname) {
    // JavaScript and CSS bundles - longer cache since they have content hashed filenames
    if (pathname.match(/\/_next\/static\/(chunks|css)\/.+/)) {
        return 'public, max-age=31536000, immutable'; // 1 year
    }
    // Next.js build assets with hashed filenames
    if (pathname.match(/\/_next\/static\/.+\.[a-z0-9]+\.(js|css)$/)) {
        return 'public, max-age=31536000, immutable'; // 1 year
    }
    // Font files - rarely change
    if (pathname.match(/\.(woff|woff2|ttf|otf)$/)) {
        return 'public, max-age=31536000, immutable'; // 1 year
    }
    // Images in public folder
    if (pathname.match(/\/images\/.+\.(jpg|jpeg|png|gif|svg|webp|avif)$/)) {
        return 'public, max-age=604800, stale-while-revalidate=86400'; // 1 week, stale for 1 day
    }
    // Other static assets
    if (pathname.match(/\.(ico|svg|jpg|jpeg|png|gif|webp|avif)$/)) {
        return 'public, max-age=86400, stale-while-revalidate=3600'; // 1 day, stale for 1 hour
    }
    // Default for other static assets
    return 'public, max-age=3600'; // 1 hour
}
const config = {
    matcher: [
        // Run on all paths except for specific ones
        '/((?!api|_next/static|_next/image|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__dc15d093._.js.map