import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath, revalidateTag } from 'next/cache';

// Secret token to validate webhook requests
const REVALIDATION_TOKEN = process.env.REVALIDATION_TOKEN;

export async function POST(request: NextRequest) {
  try {
    // Verify the request has the correct token
    const secret = request.nextUrl.searchParams.get('secret');
    if (secret !== REVALIDATION_TOKEN) {
      return NextResponse.json(
        { message: 'Invalid revalidation token' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    
    // Log the webhook payload for debugging
    console.log('Received conditions revalidation webhook:', JSON.stringify({
      event: body.event,
      model: body.model,
      entry: body.entry ? { id: body.entry.id, slug: body.entry.slug } : null
    }));

    // Revalidate the conditions list page
    revalidatePath('/conditions');
    
    // Revalidate the conditions tag to update any pages using this data
    revalidateTag('strapi-conditions');
    revalidateTag('strapi-conditions-all');
    
    // If we have a specific condition entry, revalidate its detail page
    if (body.entry && body.entry.slug) {
      revalidatePath(`/conditions/${body.entry.slug}`);
      revalidateTag(`strapi-condition-${body.entry.slug}`);
    }

    return NextResponse.json(
      { 
        revalidated: true, 
        message: 'Conditions revalidated successfully',
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error revalidating conditions:', error);
    return NextResponse.json(
      { 
        revalidated: false, 
        message: 'Error revalidating conditions',
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
