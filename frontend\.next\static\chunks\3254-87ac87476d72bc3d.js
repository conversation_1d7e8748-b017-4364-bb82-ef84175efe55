"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3254],{3254:(e,t,r)=>{r.d(t,{default:()=>q});var a=r(5155),n=r(6874),s=r.n(n),o=r(2115),l=r(351),i=r(3310);function c(){let{user:e,signOut:t}=(0,i.A)(),[r,n]=(0,o.useState)(!1),c=(0,o.useRef)(null);(0,o.useEffect)(()=>{function e(e){c.current&&!c.current.contains(e.target)&&n(!1)}return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let d=async()=>{await t(),n(!1)},h=(null==e?void 0:e.email)&&e.email.length>20?"".concat(e.email.substring(0,17),"..."):null==e?void 0:e.email;return(0,a.jsxs)("div",{className:"relative",ref:c,children:[(0,a.jsxs)("button",{onClick:()=>n(!r),className:"flex items-center text-gray-700 hover:text-emerald-600 focus:outline-none","aria-expanded":r,"aria-haspopup":"true",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center mr-2",children:(0,a.jsx)(l.JXP,{className:"text-emerald-600"})}),(0,a.jsx)("span",{className:"hidden md:block max-w-[150px] truncate",children:h}),(0,a.jsx)(l.fK4,{className:"ml-1 transition-transform ".concat(r?"rotate-180":"")})]}),r&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200",children:[(0,a.jsxs)(s(),{href:"/account",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center",onClick:()=>n(!1),children:[(0,a.jsx)(l.JXP,{className:"mr-2"}),"My Account"]}),(0,a.jsxs)("button",{onClick:d,className:"w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center",children:[(0,a.jsx)(l.QeK,{className:"mr-2"}),"Sign Out"]})]})]})}var d=r(9907),h=r(6715),u=r(3464),m=r(1890);let p="https://nice-badge-2130241d6c.strapiapp.com",g="/api",x=()=>{let e=m.env.STRAPI_API_TOKEN;return e||console.warn("No STRAPI_API_TOKEN found in environment variables for server-side API client"),u.A.create({baseURL:"".concat(p).concat(g),headers:{"Content-Type":"application/json",...e?{Authorization:"Bearer ".concat(e)}:{}}})},f=()=>{let e=localStorage.getItem("jwt");return u.A.create({baseURL:"".concat(p).concat(g),headers:{"Content-Type":"application/json",...e?{Authorization:"Bearer ".concat(e)}:{}}})},v=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{let a=r?x():f();return(await a.get(e,t)).data}catch(t){throw console.error("Error fetching from API (".concat(e,"):"),t),t.response&&(console.error("Response data:",t.response.data),console.error("Response status:",t.response.status)),t}},w=e=>1e3*Math.pow(2,e),y=[408,429,500,502,503,504],j=e=>!e.response||y.includes(e.response.status),b=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;try{return await v(e,t,r)}catch(n){if(u.A.isAxiosError(n)&&j(n)&&a<2){let n=w(a);return console.warn("Retrying API request to ".concat(e," after ").concat(n,"ms (attempt ").concat(a+1,"/").concat(2,")")),await new Promise(e=>setTimeout(e,n)),b(e,t,r,a+1)}throw E(n,e)}},N=async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;try{return await postToApi(e,t,r,a)}catch(s){if(axios.isAxiosError(s)&&j(s)&&n<2){let s=w(n);return console.warn("Retrying API POST to ".concat(e," after ").concat(s,"ms (attempt ").concat(n+1,"/").concat(2,")")),await new Promise(e=>setTimeout(e,s)),N(e,t,r,a,n+1)}throw E(s,e)}},E=(e,t)=>{let r="API Error (".concat(t,"): ");if(u.A.isAxiosError(e)){if(e.response){var a;let n=e.response.status,s=e.response.data,o=Error(r+="".concat(n," - ").concat((null==s||null==(a=s.error)?void 0:a.message)||"Unknown server error"));return o.status=n,o.endpoint=t,o.responseData=s,o}else if(e.request){let e=Error(r+="No response received from server");return e.endpoint=t,e.isNetworkError=!0,e}}return Error(r+=e.message||"Unknown error")},A=new Map;var I=r(5695);function C(e){let{href:t,children:r,className:n,prefetchApiEndpoint:l,prefetchApiParams:i,prefetchQueryKey:c,prefetchOnHover:d=!0,prefetchOnMount:u=!1,onClick:m,ariaLabel:p,title:g}=e,x=(0,h.jE)();(0,I.useRouter)();let[f,v]=(0,o.useState)(!1),w=async()=>{if(!f)try{let e=document.createElement("link");e.rel="prefetch",e.href=t,e.as="document",document.head.appendChild(e),l&&await function(e,t,r,a){let n=a||[t,r];return e.prefetchQuery({queryKey:n,queryFn:async()=>{let e={};return r&&(e.params=r),await b(t,e,!1)},staleTime:3e5})}(x,l,i,c),v(!0)}catch(e){console.error("Error preloading data:",e)}};return u&&!f&&w(),(0,a.jsx)(s(),{href:t,className:n,"aria-label":p,title:g,onClick:e=>{m&&m()},onMouseEnter:()=>{d&&w()},onTouchStart:()=>{d&&w()},prefetch:!1,children:r})}var _=r(2112),P=r(1890);let S=e=>{let{siteName:t,logoLight:r}=e,[n,h]=(0,o.useState)(!1),{user:u,isLoading:m}=(0,i.A)(),p=null==r?void 0:r.url,g=(null==r?void 0:r.alternativeText)||t,x=null==r?void 0:r.width,f=null==r?void 0:r.height,v="https://nice-badge-2130241d6c.strapiapp.com";P.env.IMAGE_HOSTNAME||v&&v.replace("strapiapp.com","media.strapiapp.com");let w="";return p&&(w=(0,_.zl)(p)),console.log("Logo URL:",{original:p,processed:w}),(0,a.jsx)("header",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsxs)(s(),{href:"/",className:"inline-block align-middle",children:[" ",p&&x&&f?(0,a.jsx)(d.default,{src:w,alt:g,width:x,height:f,className:"h-12 w-auto",aboveTheFold:!0,showPlaceholder:!1,priority:!0,unoptimized:!0}):p?(0,a.jsx)("img",{src:w,alt:g,className:"h-12 w-auto"}):t?(0,a.jsx)("span",{className:"text-2xl font-bold text-emerald-600",children:t}):(0,a.jsx)("span",{className:"text-2xl font-bold text-emerald-600",children:"My Directory Site"})]})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center",children:[(0,a.jsxs)("nav",{className:"flex space-x-8 mr-8",children:[(0,a.jsx)(C,{href:"/",className:"text-gray-700 hover:text-emerald-600",prefetchOnHover:!0,children:"Home"}),(0,a.jsx)(C,{href:"/clinics",className:"text-gray-700 hover:text-emerald-600",prefetchApiEndpoint:"/clinics",prefetchApiParams:{sort:"name:asc",pagination:{page:1,pageSize:12},populate:"*"},prefetchOnHover:!0,children:"Find a Clinic"}),(0,a.jsx)(C,{href:"/practitioners",className:"text-gray-700 hover:text-emerald-600",prefetchApiEndpoint:"/practitioners",prefetchApiParams:{sort:"name:asc",pagination:{page:1,pageSize:12},populate:"*"},prefetchOnHover:!0,children:"Find a Practitioner"}),(0,a.jsx)(C,{href:"/categories",className:"text-gray-700 hover:text-emerald-600",prefetchApiEndpoint:"/categories",prefetchApiParams:{sort:"name:asc",populate:"*"},prefetchOnHover:!0,children:"Categories"}),(0,a.jsx)(C,{href:"/blog",className:"text-gray-700 hover:text-emerald-600",prefetchApiEndpoint:"/blog-posts",prefetchApiParams:{sort:"publishDate:desc",pagination:{page:1,pageSize:10},populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}},prefetchOnHover:!0,children:"Blog"})]}),!m&&(u?(0,a.jsx)(c,{}):(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(s(),{href:"/signin",className:"text-gray-700 hover:text-emerald-600 font-medium",children:"Sign In"}),(0,a.jsx)(s(),{href:"/signup",className:"bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700",children:"Sign Up"})]}))]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("button",{onClick:()=>{h(!n)},className:"text-gray-700 hover:text-emerald-600 focus:outline-none","aria-label":n?"Close menu":"Open menu",children:n?(0,a.jsx)(l.yGN,{size:24}):(0,a.jsx)(l.ND1,{size:24})})})]}),n&&(0,a.jsxs)("nav",{className:"md:hidden mt-4 space-y-4 pb-4",children:[(0,a.jsx)(s(),{href:"/",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>h(!1),children:"Home"}),(0,a.jsx)(s(),{href:"/clinics",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>h(!1),children:"Find a Clinic"}),(0,a.jsx)(s(),{href:"/practitioners",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>h(!1),children:"Find a Practitioner"}),(0,a.jsx)(s(),{href:"/categories",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>h(!1),children:"Categories"}),(0,a.jsx)(s(),{href:"/blog",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>h(!1),children:"Blog"}),!m&&(u?(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:[(0,a.jsxs)(s(),{href:"/account",className:"flex items-center text-gray-700 hover:text-emerald-600",onClick:()=>h(!1),children:[(0,a.jsx)(l.JXP,{className:"mr-2"}),"My Account"]}),(0,a.jsx)("button",{onClick:()=>{h(!1)},className:"mt-2 flex items-center text-gray-700 hover:text-emerald-600",children:(0,a.jsx)("span",{className:"text-red-600",children:"Sign Out"})})]}):(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-4 mt-4 flex flex-col space-y-2",children:[(0,a.jsx)(s(),{href:"/signin",className:"block text-gray-700 hover:text-emerald-600 font-medium",onClick:()=>h(!1),children:"Sign In"}),(0,a.jsx)(s(),{href:"/signup",className:"block text-emerald-600 hover:text-emerald-700 font-medium",onClick:()=>h(!1),children:"Sign Up"})]}))]})]})})};var T=r(6766),k=r(1890);let L=e=>{let{siteName:t,logoLight:r,footerCategories:n}=e,o=new Date().getFullYear(),i="https://nice-badge-2130241d6c.strapiapp.com";k.env.IMAGE_HOSTNAME||i&&i.replace("strapiapp.com","media.strapiapp.com");let c=null;return(null==r?void 0:r.url)&&(c=(0,_.zl)(r.url)),r&&console.log("Footer Logo URL:",{original:r.url,processed:c}),(0,a.jsx)("footer",{className:"bg-gray-800 text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{children:[c?(0,a.jsx)(s(),{href:"/",className:"inline-block mb-4",children:(0,a.jsx)(T.default,{src:c,alt:(null==r?void 0:r.alternativeText)||t,width:(null==r?void 0:r.width)||150,height:(null==r?void 0:r.height)||40,className:"h-12 w-auto",unoptimized:!0,priority:!0})}):(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:t}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"Connecting you with holistic health practitioners and clinics to support your wellness journey."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("a",{href:"#",target:"_blank",rel:"nofollow noopener noreferrer",className:"text-gray-300 hover:text-white","aria-label":"Facebook",children:(0,a.jsx)(l.spH,{size:20})}),(0,a.jsx)("a",{href:"#",target:"_blank",rel:"nofollow noopener noreferrer",className:"text-gray-300 hover:text-white","aria-label":"Twitter",children:(0,a.jsx)(l.TC4,{size:20})}),(0,a.jsx)("a",{href:"#",target:"_blank",rel:"nofollow noopener noreferrer",className:"text-gray-300 hover:text-white","aria-label":"Instagram",children:(0,a.jsx)(l.eCe,{size:20})}),(0,a.jsx)("a",{href:"#",target:"_blank",rel:"nofollow noopener noreferrer",className:"text-gray-300 hover:text-white","aria-label":"LinkedIn",children:(0,a.jsx)(l.Wjy,{size:20})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(s(),{href:"/",className:"text-gray-300 hover:text-white",children:"Home"})}),(0,a.jsx)("li",{children:(0,a.jsx)(s(),{href:"/clinics",className:"text-gray-300 hover:text-white",children:"Find a Clinic"})}),(0,a.jsx)("li",{children:(0,a.jsx)(s(),{href:"/practitioners",className:"text-gray-300 hover:text-white",children:"Find a Practitioner"})}),(0,a.jsx)("li",{children:(0,a.jsx)(s(),{href:"/blog",className:"text-gray-300 hover:text-white",children:"Blog"})}),(0,a.jsx)("li",{children:(0,a.jsx)(s(),{href:"/about-us",className:"text-gray-300 hover:text-white",children:"About Us"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Categories"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[n&&n.length>0?n.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(s(),{href:e.attributes.slug&&"#"!==e.attributes.slug?"/categories/".concat(e.attributes.slug):"#",className:"text-gray-300 hover:text-white",children:e.attributes.name})},e.id)):(0,a.jsx)("li",{children:"No categories available."}),(0,a.jsx)("li",{children:(0,a.jsx)(s(),{href:"/categories",className:"text-gray-300 hover:text-white",children:"View All Categories"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Contact Us"}),(0,a.jsx)("p",{className:"text-gray-300 mb-2",children:"Have questions or feedback?"}),(0,a.jsx)(s(),{href:"/contact",className:"text-emerald-400 hover:text-emerald-300 mb-2 block",children:"Get in touch with us"}),(0,a.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,a.jsx)(s(),{href:"/privacy",className:"text-gray-300 hover:text-white text-sm block",children:"Privacy Policy"}),(0,a.jsx)(s(),{href:"/terms",className:"text-gray-300 hover:text-white text-sm block",children:"Terms of Service"}),(0,a.jsx)(s(),{href:"/affiliate-disclosure",className:"text-gray-300 hover:text-white text-sm block",children:"Affiliate Disclosure"})]})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-700 mt-8 pt-8 text-center text-gray-400",children:(0,a.jsxs)("p",{children:["\xa9 ",o," ",t,". All rights reserved."]})})]})})};var R=r(8358);class M extends o.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error caught by ErrorBoundary:",e,t),this.props.onError&&this.props.onError(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsx)(R.A,{error:this.state.error,resetErrorBoundary:()=>this.setState({hasError:!1,error:null})}):this.props.children}constructor(e){super(e),this.state={hasError:!1,error:null}}}var z=r(8191);let O=e=>{let{children:t}=e,{addErrorLog:r}=(0,z.q)();return(0,o.useEffect)(()=>{let e=e=>{console.error("Unhandled promise rejection:",e.reason),r(e.reason instanceof Error?e.reason:Error(String(e.reason)),"unhandled-promise-rejection")};return window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("unhandledrejection",e)}},[r]),(0,a.jsx)(M,{onError:(e,t)=>{r(e,"react-error-boundary")},children:t})};var D=r(3554),U=function(e){return e.BEFORE_INTERACTIVE="beforeInteractive",e.AFTER_INTERACTIVE="afterInteractive",e.LAZY_ONLOAD="lazyOnload",e.WORKER="worker",e}({});let F={};function B(){let e=Object.values(F);return(0,a.jsx)(a.Fragment,{children:e.map(e=>{if(!e.src&&!e.content)return null;let t=e.attributes||{};return e.content?(0,a.jsx)(D.default,{id:e.id,strategy:e.strategy||U.AFTER_INTERACTIVE,dangerouslySetInnerHTML:{__html:e.content},onLoad:e.onLoad,onError:e.onError?t=>{var r;return null==(r=e.onError)?void 0:r.call(e,Error("Failed to load script: ".concat(e.id)))}:void 0,...t},e.id):(0,a.jsx)(D.default,{id:e.id,src:e.src,strategy:e.strategy||U.AFTER_INTERACTIVE,onLoad:e.onLoad,onError:e.onError?t=>{var r;return null==(r=e.onError)?void 0:r.call(e,Error("Failed to load script: ".concat(e.id)))}:void 0,...t},e.id)})})}function H(e){let{googleAnalyticsId:t,enableInDevelopment:r=!1}=e;return(0,o.useEffect)(()=>{if(t){var e,r;F[(e={id:"google-analytics",src:"https://www.googletagmanager.com/gtag/js?id=".concat(t),strategy:U.AFTER_INTERACTIVE,attributes:{async:"true"}}).id]=e,F[(r={id:"google-analytics-init",content:"\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '".concat(t,"', {\n            page_path: window.location.pathname,\n          });\n        "),strategy:U.AFTER_INTERACTIVE}).id]=r}},[t,r]),null}function Z(e){let{dnsPrefetch:t=[],preconnect:r=[],preload:n=[],prefetch:s=[]}=e,o="\n    (function() {\n      const head = document.head;\n\n      // DNS Prefetch\n      ".concat(t.map(e=>"\n        const dnsPrefetch_".concat(e.replace(/[^a-zA-Z0-9]/g,"_")," = document.createElement('link');\n        dnsPrefetch_").concat(e.replace(/[^a-zA-Z0-9]/g,"_"),".rel = 'dns-prefetch';\n        dnsPrefetch_").concat(e.replace(/[^a-zA-Z0-9]/g,"_"),".href = '").concat(e,"';\n        head.appendChild(dnsPrefetch_").concat(e.replace(/[^a-zA-Z0-9]/g,"_"),");\n      ")).join(""),"\n\n      // Preconnect\n      ").concat(r.map(e=>"\n        const preconnect_".concat(e.replace(/[^a-zA-Z0-9]/g,"_")," = document.createElement('link');\n        preconnect_").concat(e.replace(/[^a-zA-Z0-9]/g,"_"),".rel = 'preconnect';\n        preconnect_").concat(e.replace(/[^a-zA-Z0-9]/g,"_"),".href = '").concat(e,"';\n        preconnect_").concat(e.replace(/[^a-zA-Z0-9]/g,"_"),".crossOrigin = 'anonymous';\n        head.appendChild(preconnect_").concat(e.replace(/[^a-zA-Z0-9]/g,"_"),");\n      ")).join(""),"\n\n      // Preload\n      ").concat(n.map((e,t)=>"\n        const preload_".concat(t," = document.createElement('link');\n        preload_").concat(t,".rel = 'preload';\n        preload_").concat(t,".href = '").concat(e.href,"';\n        preload_").concat(t,".as = '").concat(e.as,"';\n        ").concat(e.type?"preload_".concat(t,".type = '").concat(e.type,"';"):"","\n        ").concat(e.crossOrigin?"preload_".concat(t,".crossOrigin = '").concat(e.crossOrigin,"';"):"","\n        head.appendChild(preload_").concat(t,");\n      ")).join(""),"\n\n      // Prefetch\n      ").concat(s.map((e,t)=>"\n        const prefetch_".concat(t," = document.createElement('link');\n        prefetch_").concat(t,".rel = 'prefetch';\n        prefetch_").concat(t,".href = '").concat(e.href,"';\n        ").concat(e.as?"prefetch_".concat(t,".as = '").concat(e.as,"';"):"","\n        head.appendChild(prefetch_").concat(t,");\n      ")).join(""),"\n    })();\n  ");return(0,a.jsx)(D.default,{id:"resource-hints",strategy:"beforeInteractive",dangerouslySetInnerHTML:{__html:o}})}r(1890);let Q=()=>{let[e,t]=(0,o.useState)(!1),[r,a]=(0,o.useState)([]);return(0,o.useEffect)(()=>{},[]),null};var W=r(1890);let q=e=>{let{children:t,siteName:r,logoLight:n,footerCategories:s}=e,o="https://nice-badge-2130241d6c.strapiapp.com";return(0,a.jsxs)(O,{children:[(0,a.jsx)(Z,{dnsPrefetch:[o,"https://fonts.googleapis.com","https://fonts.gstatic.com"],preconnect:[o,"https://fonts.googleapis.com","https://fonts.gstatic.com"],preload:[{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap",as:"style"}]}),(0,a.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,a.jsx)(S,{siteName:r,logoLight:n}),(0,a.jsx)("main",{className:"flex-grow",children:t}),(0,a.jsx)(L,{siteName:r,logoLight:n,footerCategories:s}),(0,a.jsx)(H,{googleAnalyticsId:W.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,enableInDevelopment:!1}),(0,a.jsx)(B,{}),(0,a.jsx)(Q,{})]})]})}},3310:(e,t,r)=>{r.d(t,{AuthProvider:()=>c,A:()=>d});var a=r(5155),n=r(2115);let s=r(3464).A.create({baseURL:"https://nice-badge-2130241d6c.strapiapp.com",headers:{"Content-Type":"application/json"}}),o={register:async(e,t,r)=>{try{let a=await s.post("/api/auth/local/register",{username:e,email:t,password:r});return a.data.jwt&&(localStorage.setItem("jwt",a.data.jwt),localStorage.setItem("user",JSON.stringify(a.data.user))),{data:a.data,error:null}}catch(e){var a,n;return{data:null,error:(null==(n=e.response)||null==(a=n.data)?void 0:a.error)||{message:"An error occurred during registration"}}}},login:async(e,t)=>{try{let r=await s.post("/api/auth/local",{identifier:e,password:t});return r.data.jwt&&(localStorage.setItem("jwt",r.data.jwt),localStorage.setItem("user",JSON.stringify(r.data.user))),{data:r.data,error:null}}catch(e){var r,a;return{data:null,error:(null==(a=e.response)||null==(r=a.data)?void 0:r.error)||{message:"Invalid credentials"}}}},logout:()=>(localStorage.removeItem("jwt"),localStorage.removeItem("user"),{error:null}),getCurrentUser:()=>{let e=localStorage.getItem("jwt"),t=localStorage.getItem("user");if(!e||!t)return{user:null};try{return{user:JSON.parse(t)}}catch(e){return{user:null}}},forgotPassword:async e=>{try{return{data:(await s.post("/api/auth/forgot-password",{email:e})).data,error:null}}catch(e){var t,r;return{data:null,error:(null==(r=e.response)||null==(t=r.data)?void 0:t.error)||{message:"An error occurred during password reset request"}}}},resetPassword:async(e,t,r)=>{try{let a=await s.post("/api/auth/reset-password",{code:e,password:t,passwordConfirmation:r});return a.data.jwt&&(localStorage.setItem("jwt",a.data.jwt),localStorage.setItem("user",JSON.stringify(a.data.user))),{data:a.data,error:null}}catch(e){var a,n;return{data:null,error:(null==(n=e.response)||null==(a=n.data)?void 0:a.error)||{message:"An error occurred during password reset"}}}}};var l=r(5695);let i=(0,n.createContext)(void 0);function c(e){let{children:t}=e,[r,s]=(0,n.useState)(null),[c,d]=(0,n.useState)(!0),[h,u]=(0,n.useState)(!1),m=(0,l.useRouter)();(0,n.useEffect)(()=>{(async()=>{d(!0);try{let{user:e}=o.getCurrentUser();s(e),u(!!e)}catch(e){console.error("Error loading user:",e),s(null),u(!1)}finally{d(!1)}})();let e=()=>{let{user:e}=o.getCurrentUser();s(e),u(!!e),m.refresh()};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}},[m]);let p=async(e,t)=>{d(!0);let{data:r,error:a}=await o.login(e,t);return r&&(s(r.user),u(!0),m.refresh()),d(!1),{error:a}},g=async(e,t,r)=>{d(!0);let{data:a,error:n}=await o.register(e,t,r);return a&&(s(a.user),u(!0),m.refresh()),d(!1),{error:n}},x=async e=>{d(!0);let{error:t}=await o.forgotPassword(e);return d(!1),{error:t}},f=async(e,t,r)=>{d(!0);let{data:a,error:n}=await o.resetPassword(e,t,r);return a&&(s(a.user),u(!0),m.refresh()),d(!1),{error:n}};return(0,a.jsx)(i.Provider,{value:{user:r,isLoading:c,signIn:p,signUp:g,signOut:()=>{d(!0);let{error:e}=o.logout();return s(null),u(!1),m.refresh(),d(!1),{error:e}},forgotPassword:x,resetPassword:f,isAuthenticated:h},children:t})}function d(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4663:(e,t,r)=>{r.r(t),r.d(t,{default:()=>l,imageUtils:()=>i});var a=r(1890);let n={count:0,errors:0,totalTime:0,slowestTime:0,slowestImage:""},s={enableMetrics:"true"===a.env.NEXT_PUBLIC_CACHE_METRICS,useHighQuality:!0,disableOptimization:"true"===a.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION,defaultQuality:85,avifQuality:80,webpQuality:85,jpegQuality:90,pngQuality:90,maxDevicePixelRatio:3,minWidth:20,blurUpRadius:10};function o(e,t,r){let a="https://nice-badge-2130241d6c.media.strapiapp.com",n=r||(e.toLowerCase().match(/\.avif$/i)?s.avifQuality:e.toLowerCase().match(/\.webp$/i)?s.webpQuality:e.toLowerCase().match(/\.jpe?g$/i)?s.jpegQuality:e.toLowerCase().match(/\.png$/i)?s.pngQuality:e.toLowerCase().match(/\.(jpe?g|png)$/i)?s.jpegQuality:s.webpQuality),o=Math.min(window.devicePixelRatio||1,s.maxDevicePixelRatio);if(t<s.minWidth)return e;try{let r=Math.round(t*o),s=new URL(e.startsWith("http")?e:"".concat(a).concat(e.startsWith("/")?e:"/".concat(e)));if(s.hostname.includes("strapiapp.com")||s.hostname.includes("localhost")){s.searchParams.set("w",r.toString()),s.searchParams.set("q",n.toString());let t=e.toLowerCase().match(/\.(jpe?g|png)$/i);s.searchParams.has("format")||s.searchParams.set("format",t?"avif":"webp");{let e=Array.from(s.searchParams.entries()).sort();s.search=e.map(e=>{let[t,r]=e;return"".concat(t,"=").concat(r)}).join("&")}t&&s.searchParams.set("sharp","10"),"http:"===s.protocol&&(s.protocol="https:")}return s.toString()}catch(r){if(e.startsWith("/"))return"".concat(a).concat(e,"?w=").concat(t,"&q=").concat(n);return e}}function l(e){let{src:t,width:r,quality:a}=e,l=s.enableMetrics?performance.now():0;if(!t)return"";try{if(!(t&&!s.disableOptimization&&!("string"==typeof t&&[".svg",".gif",".webp",".avif"].some(e=>t.toLowerCase().endsWith(e))||t.startsWith("http")&&!t.includes("strapiapp.com")&&!t.includes("localhost:1337"))&&1))return t;let e=o(t,r,a);if(s.enableMetrics&&l){let e=performance.now()-l;n.count++,n.totalTime+=e,e>n.slowestTime&&(n.slowestTime=e,n.slowestImage=t)}return e}catch(e){return s.enableMetrics&&n.errors++,t}}let i={getBlurDataUrl:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return e?"".concat(o(e,t,10),"&blur=80"):""},preloadImage:(e,t)=>{if(!e)return;let r=new Image;return r.src=l({src:e,width:t,quality:s.defaultQuality}),r},resetMetrics:()=>{n.count=0,n.errors=0,n.totalTime=0,n.slowestTime=0,n.slowestImage=""},getMetrics:()=>({...n})}},8191:(e,t,r)=>{r.d(t,{ErrorProvider:()=>o,q:()=>l});var a=r(5155),n=r(2115);let s=(0,n.createContext)(void 0),o=e=>{let{children:t}=e,[r,o]=(0,n.useState)(null),[l,i]=(0,n.useState)([]);return(0,a.jsx)(s.Provider,{value:{globalError:r,setGlobalError:o,clearGlobalError:()=>{o(null)},addErrorLog:(e,t)=>{let r={id:Date.now().toString(),error:e,timestamp:new Date,source:t};i(e=>[r,...e].slice(0,10))},errorLogs:l},children:t})},l=()=>{let e=(0,n.useContext)(s);if(void 0===e)throw Error("useError must be used within an ErrorProvider");return e}},8358:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(5155);r(2115);var n=r(6874),s=r.n(n),o=r(351);let l=e=>{let{error:t,resetErrorBoundary:r,message:n="Something went wrong",showHomeLink:l=!0,showRefreshButton:i=!0}=e;return null==t||t.message,(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 my-4 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4 text-red-600",children:[(0,a.jsx)(o.eHT,{className:"w-6 h-6 mr-2"}),(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"Error Encountered"})]}),(0,a.jsx)("p",{className:"mb-4 text-gray-700",children:n}),!1,(0,a.jsxs)("div",{className:"flex flex-wrap gap-3 mt-4",children:[i&&r&&(0,a.jsxs)("button",{onClick:r,className:"flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors",children:[(0,a.jsx)(o.jTZ,{className:"mr-2"}),"Try Again"]}),i&&!r&&(0,a.jsxs)("button",{onClick:()=>window.location.reload(),className:"flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors",children:[(0,a.jsx)(o.jTZ,{className:"mr-2"}),"Refresh Page"]}),l&&(0,a.jsxs)(s(),{href:"/",className:"flex items-center px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors",children:[(0,a.jsx)(o.V5Y,{className:"mr-2"}),"Go to Homepage"]})]})]})}},9907:(e,t,r)=>{r.d(t,{default:()=>c});var a=r(5155),n=r(2115),s=r(6766),o=r(351),l=r(4663);let i=e=>{var t;let{src:r,alt:i,width:c,height:d,className:h="",fallbackClassName:u="",showPlaceholder:m=!0,advancedBlur:p=!1,preload:g=!1,fadeIn:x=!0,wrapperAs:f="div",fillContainer:v=!1,sizes:w="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",style:y,priority:j=!1,qualityOverride:b,...N}=e,E=(0,n.useRef)(!0),[A,I]=(0,n.useState)(j?"loaded":"loading"),[C,_]=(0,n.useState)({width:v?void 0:c,height:v?void 0:d}),P="string"==typeof r?r:(null==r?void 0:r.src)||(null==r?void 0:r.url)||(null==r||null==(t=r.default)?void 0:t.src)||null,S=p&&m&&P?l.imageUtils.getBlurDataUrl(P,20):m?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+":void 0;(0,n.useEffect)(()=>{if(g&&P&&!j&&1){let e=l.imageUtils.preloadImage(P,"number"==typeof c?c:500);e&&(e.onload=()=>{E.current&&I("loaded")},e.onerror=()=>{E.current&&I("error")})}return()=>{E.current=!1}},[P,g,j,c]);let T="number"==typeof c&&"number"==typeof d&&c>0&&d>0?c/d:void 0,k=(0,n.useCallback)(e=>{if(!v&&(null==e?void 0:e.target)){let{naturalWidth:t,naturalHeight:r}=e.target;t&&r&&E.current&&_({width:t,height:r})}E.current&&I("loaded")},[v]),L=(0,n.useCallback)(()=>{I("error")},[P,i]),R=j?"eager":"lazy";if("error"===A||!P)return(0,a.jsx)("div",{className:"flex items-center justify-center bg-gray-100 ".concat(u||(v?"":h)),style:{width:v?"100%":c,height:v?"100%":d,aspectRatio:T?"".concat(T):void 0,...y},role:"img","aria-label":i||"Image failed to load",children:(0,a.jsx)(o.fZZ,{className:"text-gray-400 w-1/5 h-1/5"})});let M=[h,"loaded"===A?"opacity-100":"opacity-0",x?"transition-opacity duration-300":""].filter(Boolean).join(" "),z={objectFit:(null==y?void 0:y.objectFit)||"cover",aspectRatio:v?void 0:T?"".concat(T):void 0,...y,width:v||null==y?void 0:y.width,height:v||null==y?void 0:y.height},O=(0,a.jsx)(s.default,{src:P,alt:i||"",width:v?void 0:C.width,height:v?void 0:C.height,fill:v,className:M,loading:R,fetchPriority:j?"high":g?"low":"auto",priority:j,sizes:w,style:z,placeholder:m?"blur":"empty",blurDataURL:S,onLoad:k,onError:L,quality:b,...N}),D=v?{width:"100%",height:"100%",position:"relative",...y}:{width:C.width,height:C.height,aspectRatio:T?"".concat(T):void 0,position:"relative",...y};return(0,a.jsxs)(f,{className:"relative ".concat(v?"w-full h-full":""),style:D,children:[O,"loading"===A&&m&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-100 animate-pulse ".concat(u||""),style:{width:"100%",height:"100%"},"aria-hidden":"true"})]})};i.displayName="LazyImage";let c=(0,n.memo)(i)}}]);