"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[927],{927:(e,t,a)=>{a.d(t,{default:()=>d});var r=a(5155),i=a(9907),l=a(6874),s=a.n(l),o=a(3319),n=a(351),c=a(2112);let d=e=>{var t,a,l;let{post:d,showReadingTime:h=!1,showShareButton:u=!1,showBadge:m=!1}=e,g=(0,c.Jf)(d.featured_image),p=!!d.featured_image,x=(0,c.Jf)(null==(t=d.author)?void 0:t.profile_picture);null==(a=d.author)||a.profile_picture;let f=d.reading_time||(d.content?(e=>{var t;return Math.max(1,Math.ceil(((null==e||null==(t=e.split(/\s+/))?void 0:t.length)||0)/200))})(d.content):2),w=x&&(x.startsWith("http")||x.startsWith("/")||x.startsWith("data:")||window.location.origin&&!x.startsWith("http"))?x:"",v=(0,o.GP)(new Date(d.publish_date),"MMMM d, yyyy"),b=(null==(l=Object.getOwnPropertyDescriptor(d,"view_count"))?void 0:l.value)||0;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,r.jsxs)(s(),{href:"/blog/".concat(d.slug),className:"block relative h-48 w-full overflow-hidden",children:[" ",p?(0,r.jsx)(i.default,{src:g,alt:d.title||"Blog post image",width:600,height:400,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1,showPlaceholder:!0,advancedBlur:!0,fadeIn:!0,preload:d.isFeatured||b>10}):(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-50 to-teal-100 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-emerald-700 font-semibold text-xl opacity-50",children:d.title.charAt(0)})}),m&&(!0===d.isFeatured||b>0)&&(0,r.jsx)("div",{className:"absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ".concat(!0===d.isFeatured?"bg-emerald-600 text-white":"bg-amber-500 text-white"),children:!0===d.isFeatured?"Featured Post":"Popular Post"})]}),(0,r.jsxs)("div",{className:"p-4 flex-grow flex flex-col",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:v}),h&&(0,r.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,r.jsx)(n.Ohp,{className:"mr-1"}),(0,r.jsxs)("span",{children:[f," min read"]})]})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800 flex-grow",children:(0,r.jsx)(s(),{href:"/blog/".concat(d.slug),className:"hover:text-emerald-600 line-clamp-2",children:d.title})}),d.excerpt&&(0,r.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:d.excerpt}),d.author&&(0,r.jsxs)("div",{className:"flex items-center mt-auto pt-4 border-t border-gray-100",children:[" ",(0,r.jsxs)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-200 shadow-sm",children:[" ",w&&(e=>{try{return e&&(e.startsWith("http")||e.startsWith("/")||e.startsWith("data:"))}catch(e){return!1}})(w)?(0,r.jsx)(i.default,{src:w,alt:d.author.name||"Author image",width:40,height:40,fillContainer:!0,className:"object-cover rounded-full",sizes:"40px",showPlaceholder:!0,fadeIn:!0}):(0,r.jsx)("div",{className:"absolute inset-0 bg-emerald-100 flex items-center justify-center",children:(0,r.jsx)(n.JXP,{className:"text-emerald-700 text-lg"})})]}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("span",{className:"block text-xs text-gray-500 mb-0.5",children:"Written by"}),(0,r.jsx)(s(),{href:"/blog/authors/".concat(d.author.slug),className:"font-medium text-gray-800 hover:text-emerald-600",children:d.author.name})]})]})]}),(0,r.jsxs)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto flex justify-between items-center",children:[(0,r.jsx)(s(),{href:"/blog/".concat(d.slug),className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"Read More →"}),u&&(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors",onClick:e=>{e.preventDefault(),navigator.share?navigator.share({title:d.title,text:d.excerpt||"",url:"".concat(window.location.origin,"/blog/").concat(d.slug)}).catch(e=>{console.error("Error sharing:",e)}):navigator.clipboard.writeText("".concat(window.location.origin,"/blog/").concat(d.slug)).then(()=>{alert("Link copied to clipboard!")}).catch(e=>{console.error("Could not copy text: ",e)})},"aria-label":"Share article",children:(0,r.jsx)(n.Pum,{size:18})}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-emerald-600 p-1 transition-colors","aria-label":"Save article",children:(0,r.jsx)(n.Y19,{size:18})})]})]})]})}},4663:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o,imageUtils:()=>n});var r=a(1890);let i={count:0,errors:0,totalTime:0,slowestTime:0,slowestImage:""},l={enableMetrics:"true"===r.env.NEXT_PUBLIC_CACHE_METRICS,useHighQuality:!0,disableOptimization:"true"===r.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION,defaultQuality:85,avifQuality:80,webpQuality:85,jpegQuality:90,pngQuality:90,maxDevicePixelRatio:3,minWidth:20,blurUpRadius:10};function s(e,t,a){let r="https://nice-badge-2130241d6c.media.strapiapp.com",i=a||(e.toLowerCase().match(/\.avif$/i)?l.avifQuality:e.toLowerCase().match(/\.webp$/i)?l.webpQuality:e.toLowerCase().match(/\.jpe?g$/i)?l.jpegQuality:e.toLowerCase().match(/\.png$/i)?l.pngQuality:e.toLowerCase().match(/\.(jpe?g|png)$/i)?l.jpegQuality:l.webpQuality),s=Math.min(window.devicePixelRatio||1,l.maxDevicePixelRatio);if(t<l.minWidth)return e;try{let a=Math.round(t*s),l=new URL(e.startsWith("http")?e:"".concat(r).concat(e.startsWith("/")?e:"/".concat(e)));if(l.hostname.includes("strapiapp.com")||l.hostname.includes("localhost")){l.searchParams.set("w",a.toString()),l.searchParams.set("q",i.toString());let t=e.toLowerCase().match(/\.(jpe?g|png)$/i);l.searchParams.has("format")||l.searchParams.set("format",t?"avif":"webp");{let e=Array.from(l.searchParams.entries()).sort();l.search=e.map(e=>{let[t,a]=e;return"".concat(t,"=").concat(a)}).join("&")}t&&l.searchParams.set("sharp","10"),"http:"===l.protocol&&(l.protocol="https:")}return l.toString()}catch(a){if(e.startsWith("/"))return"".concat(r).concat(e,"?w=").concat(t,"&q=").concat(i);return e}}function o(e){let{src:t,width:a,quality:r}=e,o=l.enableMetrics?performance.now():0;if(!t)return"";try{if(!(t&&!l.disableOptimization&&!("string"==typeof t&&[".svg",".gif",".webp",".avif"].some(e=>t.toLowerCase().endsWith(e))||t.startsWith("http")&&!t.includes("strapiapp.com")&&!t.includes("localhost:1337"))&&1))return t;let e=s(t,a,r);if(l.enableMetrics&&o){let e=performance.now()-o;i.count++,i.totalTime+=e,e>i.slowestTime&&(i.slowestTime=e,i.slowestImage=t)}return e}catch(e){return l.enableMetrics&&i.errors++,t}}let n={getBlurDataUrl:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return e?"".concat(s(e,t,10),"&blur=80"):""},preloadImage:(e,t)=>{if(!e)return;let a=new Image;return a.src=o({src:e,width:t,quality:l.defaultQuality}),a},resetMetrics:()=>{i.count=0,i.errors=0,i.totalTime=0,i.slowestTime=0,i.slowestImage=""},getMetrics:()=>({...i})}},9907:(e,t,a)=>{a.d(t,{default:()=>c});var r=a(5155),i=a(2115),l=a(6766),s=a(351),o=a(4663);let n=e=>{var t;let{src:a,alt:n,width:c,height:d,className:h="",fallbackClassName:u="",showPlaceholder:m=!0,advancedBlur:g=!1,preload:p=!1,fadeIn:x=!0,wrapperAs:f="div",fillContainer:w=!1,sizes:v="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",style:b,priority:y=!1,qualityOverride:j,...N}=e,I=(0,i.useRef)(!0),[M,P]=(0,i.useState)(y?"loaded":"loading"),[C,W]=(0,i.useState)({width:w?void 0:c,height:w?void 0:d}),_="string"==typeof a?a:(null==a?void 0:a.src)||(null==a?void 0:a.url)||(null==a||null==(t=a.default)?void 0:t.src)||null,L=g&&m&&_?o.imageUtils.getBlurDataUrl(_,20):m?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+":void 0;(0,i.useEffect)(()=>{if(p&&_&&!y&&1){let e=o.imageUtils.preloadImage(_,"number"==typeof c?c:500);e&&(e.onload=()=>{I.current&&P("loaded")},e.onerror=()=>{I.current&&P("error")})}return()=>{I.current=!1}},[_,p,y,c]);let D="number"==typeof c&&"number"==typeof d&&c>0&&d>0?c/d:void 0,T=(0,i.useCallback)(e=>{if(!w&&(null==e?void 0:e.target)){let{naturalWidth:t,naturalHeight:a}=e.target;t&&a&&I.current&&W({width:t,height:a})}I.current&&P("loaded")},[w]),A=(0,i.useCallback)(()=>{P("error")},[_,n]),S=y?"eager":"lazy";if("error"===M||!_)return(0,r.jsx)("div",{className:"flex items-center justify-center bg-gray-100 ".concat(u||(w?"":h)),style:{width:w?"100%":c,height:w?"100%":d,aspectRatio:D?"".concat(D):void 0,...b},role:"img","aria-label":n||"Image failed to load",children:(0,r.jsx)(s.fZZ,{className:"text-gray-400 w-1/5 h-1/5"})});let Q=[h,"loaded"===M?"opacity-100":"opacity-0",x?"transition-opacity duration-300":""].filter(Boolean).join(" "),R={objectFit:(null==b?void 0:b.objectFit)||"cover",aspectRatio:w?void 0:D?"".concat(D):void 0,...b,width:w||null==b?void 0:b.width,height:w||null==b?void 0:b.height},B=(0,r.jsx)(l.default,{src:_,alt:n||"",width:w?void 0:C.width,height:w?void 0:C.height,fill:w,className:Q,loading:S,fetchPriority:y?"high":p?"low":"auto",priority:y,sizes:v,style:R,placeholder:m?"blur":"empty",blurDataURL:L,onLoad:T,onError:A,quality:j,...N}),z=w?{width:"100%",height:"100%",position:"relative",...b}:{width:C.width,height:C.height,aspectRatio:D?"".concat(D):void 0,position:"relative",...b};return(0,r.jsxs)(f,{className:"relative ".concat(w?"w-full h-full":""),style:z,children:[B,"loading"===M&&m&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-100 animate-pulse ".concat(u||""),style:{width:"100%",height:"100%"},"aria-hidden":"true"})]})};n.displayName="LazyImage";let c=(0,i.memo)(n)}}]);