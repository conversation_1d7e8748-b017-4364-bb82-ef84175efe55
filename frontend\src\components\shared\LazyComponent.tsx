'use client';

import { Suspense, lazy, ComponentType } from 'react';
import dynamic from 'next/dynamic';
import SkeletonLoader from './SkeletonLoader';

interface LazyComponentProps {
  /**
   * The component to be lazily loaded
   */
  component: () => Promise<{ default: ComponentType<any> }>;
  
  /**
   * Props to pass to the component
   */
  props?: Record<string, any>;
  
  /**
   * Type of skeleton loader to show while loading
   */
  skeletonType?: 'text' | 'card' | 'image' | 'profile' | 'table';
  
  /**
   * Number of skeleton items to show
   */
  skeletonCount?: number;
  
  /**
   * Custom loading component to show instead of the skeleton
   */
  loadingComponent?: React.ReactNode;
  
  /**
   * Whether to disable server-side rendering
   */
  ssr?: boolean;
}

/**
 * LazyComponent - A wrapper for lazy loading components with Next.js
 * 
 * This component uses next/dynamic to lazily load components and
 * provides a skeleton loader or custom loading component while loading.
 * 
 * @example
 * ```tsx
 * // Basic usage
 * <LazyComponent 
 *   component={() => import('../components/HeavyComponent')}
 *   props={{ someData: data }}
 *   skeletonType="card"
 * />
 * 
 * // With custom loading component
 * <LazyComponent 
 *   component={() => import('../components/HeavyComponent')}
 *   loadingComponent={<div>Loading...</div>}
 *   ssr={false}
 * />
 * ```
 */
const LazyComponent: React.FC<LazyComponentProps> = ({
  component,
  props = {},
  skeletonType = 'card',
  skeletonCount = 1,
  loadingComponent,
  ssr = true,
}) => {
  // Create a loading component based on props
  const LoadingFallback = () => (
    loadingComponent || <SkeletonLoader type={skeletonType} count={skeletonCount} />
  );
  
  // Use next/dynamic to load the component
  const DynamicComponent = dynamic(component, {
    loading: LoadingFallback,
    ssr,
  });
  
  return <DynamicComponent {...props} />;
};

export default LazyComponent;

/**
 * createLazyComponent - A utility function to create a lazy component
 * 
 * This function creates a lazy component that can be used directly in JSX.
 * 
 * @example
 * ```tsx
 * // Create a lazy component
 * const LazyHeavyComponent = createLazyComponent(() => import('../components/HeavyComponent'), {
 *   skeletonType: 'card',
 *   ssr: false,
 * });
 * 
 * // Use it in JSX
 * <LazyHeavyComponent someData={data} />
 * ```
 */
export function createLazyComponent<T>(
  componentImport: () => Promise<{ default: ComponentType<T> }>,
  options: Omit<LazyComponentProps, 'component' | 'props'> = {}
) {
  const LazyComponentWrapper = (props: T) => (
    <LazyComponent
      component={componentImport}
      props={props}
      skeletonType={options.skeletonType}
      skeletonCount={options.skeletonCount}
      loadingComponent={options.loadingComponent}
      ssr={options.ssr}
    />
  );
  
  return LazyComponentWrapper;
}
