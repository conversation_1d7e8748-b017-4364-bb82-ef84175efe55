exports.id=270,exports.ids=[270],exports.modules={1065:(e,t,r)=>{Promise.resolve().then(r.bind(r,31369))},6363:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>c,A:()=>d});var a=r(60687),s=r(43210);let n=r(51060).A.create({baseURL:"https://nice-badge-2130241d6c.strapiapp.com",headers:{"Content-Type":"application/json"}}),i={register:async(e,t,r)=>{try{let a=await n.post("/api/auth/local/register",{username:e,email:t,password:r});return a.data.jwt&&(localStorage.setItem("jwt",a.data.jwt),localStorage.setItem("user",JSON.stringify(a.data.user))),{data:a.data,error:null}}catch(e){return{data:null,error:e.response?.data?.error||{message:"An error occurred during registration"}}}},login:async(e,t)=>{try{let r=await n.post("/api/auth/local",{identifier:e,password:t});return r.data.jwt&&(localStorage.setItem("jwt",r.data.jwt),localStorage.setItem("user",JSON.stringify(r.data.user))),{data:r.data,error:null}}catch(e){return{data:null,error:e.response?.data?.error||{message:"Invalid credentials"}}}},logout:()=>(localStorage.removeItem("jwt"),localStorage.removeItem("user"),{error:null}),forgotPassword:async e=>{try{return{data:(await n.post("/api/auth/forgot-password",{email:e})).data,error:null}}catch(e){return{data:null,error:e.response?.data?.error||{message:"An error occurred during password reset request"}}}},resetPassword:async(e,t,r)=>{try{let a=await n.post("/api/auth/reset-password",{code:e,password:t,passwordConfirmation:r});return a.data.jwt&&(localStorage.setItem("jwt",a.data.jwt),localStorage.setItem("user",JSON.stringify(a.data.user))),{data:a.data,error:null}}catch(e){return{data:null,error:e.response?.data?.error||{message:"An error occurred during password reset"}}}}};var o=r(16189);let l=(0,s.createContext)(void 0);function c({children:e}){let[t,r]=(0,s.useState)(null),[n,c]=(0,s.useState)(!0),[d,h]=(0,s.useState)(!1),p=(0,o.useRouter)(),u=async(e,t)=>{c(!0);let{data:a,error:s}=await i.login(e,t);return a&&(r(a.user),h(!0),p.refresh()),c(!1),{error:s}},m=async(e,t,a)=>{c(!0);let{data:s,error:n}=await i.register(e,t,a);return s&&(r(s.user),h(!0),p.refresh()),c(!1),{error:n}},g=async e=>{c(!0);let{error:t}=await i.forgotPassword(e);return c(!1),{error:t}},f=async(e,t,a)=>{c(!0);let{data:s,error:n}=await i.resetPassword(e,t,a);return s&&(r(s.user),h(!0),p.refresh()),c(!1),{error:n}};return(0,a.jsx)(l.Provider,{value:{user:t,isLoading:n,signIn:u,signUp:m,signOut:()=>{c(!0);let{error:e}=i.logout();return r(null),h(!1),p.refresh(),c(!1),{error:e}},forgotPassword:g,resetPassword:f,isAuthenticated:d},children:e})}function d(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},8811:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(60687);r(43210);var s=r(24559),n=r.n(s);function i({error:e,reset:t}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:n().className,children:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-md p-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full bg-red-100",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})})}),(0,a.jsx)("h1",{className:"text-xl font-semibold text-center text-gray-900 mb-2",children:"Something went wrong!"}),(0,a.jsx)("p",{className:"text-gray-600 text-center mb-6",children:"We're sorry, but there was a critical error loading the application."}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("button",{onClick:t,className:"px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors",children:"Try Again"})}),!1]})})})})}r(44263)},10673:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,imageUtils:()=>o});let a={count:0,errors:0,totalTime:0,slowestTime:0,slowestImage:""},s={enableMetrics:"true"===process.env.NEXT_PUBLIC_CACHE_METRICS,useHighQuality:!0,disableOptimization:"true"===process.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION,defaultQuality:85,avifQuality:80,webpQuality:85,jpegQuality:90,pngQuality:90,maxDevicePixelRatio:3,minWidth:20,blurUpRadius:10};function n(e,t,r){let a="https://nice-badge-2130241d6c.media.strapiapp.com",n=r||(e.toLowerCase().match(/\.avif$/i)?s.avifQuality:e.toLowerCase().match(/\.webp$/i)?s.webpQuality:e.toLowerCase().match(/\.jpe?g$/i)?s.jpegQuality:e.toLowerCase().match(/\.png$/i)?s.pngQuality:e.toLowerCase().match(/\.(jpe?g|png)$/i)?s.jpegQuality:s.webpQuality);if(t<s.minWidth)return e;try{let r=Math.round(+t),s=new URL(e.startsWith("http")?e:`${a}${e.startsWith("/")?e:`/${e}`}`);if(s.hostname.includes("strapiapp.com")||s.hostname.includes("localhost")){s.searchParams.set("w",r.toString()),s.searchParams.set("q",n.toString());let t=e.toLowerCase().match(/\.(jpe?g|png)$/i);s.searchParams.has("format")||s.searchParams.set("format",t?"avif":"webp");{let e=Array.from(s.searchParams.entries()).sort();s.search=e.map(([e,t])=>`${e}=${t}`).join("&")}t&&s.searchParams.set("sharp","10"),"http:"===s.protocol&&(s.protocol="https:")}return s.toString()}catch(r){if(e.startsWith("/"))return`${a}${e}?w=${t}&q=${n}`;return e}}function i({src:e,width:t,quality:r}){let i=s.enableMetrics?performance.now():0;if(!e)return"";try{if(!(e&&!s.disableOptimization&&!("string"==typeof e&&[".svg",".gif",".webp",".avif"].some(t=>e.toLowerCase().endsWith(t))||e.startsWith("http")&&!e.includes("strapiapp.com")&&!e.includes("localhost:1337"))&&1))return e;let o=n(e,t,r);if(s.enableMetrics&&i){let t=performance.now()-i;a.count++,a.totalTime+=t,t>a.slowestTime&&(a.slowestTime=t,a.slowestImage=e)}return o}catch(t){return s.enableMetrics&&a.errors++,e}}let o={getBlurDataUrl:(e,t=10)=>e?`${n(e,t,10)}&blur=80`:"",preloadImage:(e,t)=>{},resetMetrics:()=>{a.count=0,a.errors=0,a.totalTime=0,a.slowestTime=0,a.slowestImage=""},getMetrics:()=>({...a})}},12961:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,51767,23)),Promise.resolve().then(r.t.bind(r,50893,23)),Promise.resolve().then(r.t.bind(r,48182,23)),Promise.resolve().then(r.bind(r,96800)),Promise.resolve().then(r.t.bind(r,47429,23)),Promise.resolve().then(r.bind(r,14143)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,71041)),Promise.resolve().then(r.bind(r,82113))},14143:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\layout\\Layout.tsx","default")},14329:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(60687);r(43210);var s=r(66798),n=r(25743);function i({error:e,reset:t}){let{addErrorLog:r}=(0,n.q)();return(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)(s.A,{error:e,resetErrorBoundary:t,message:"Sorry, something went wrong while loading this page.",showHomeLink:!0,showRefreshButton:!0})})}},24587:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(60687),s=r(43210),n=r(30474),i=r(17019),o=r(10673);let l=({src:e,alt:t,width:r,height:l,className:c="",fallbackClassName:d="",showPlaceholder:h=!0,advancedBlur:p=!1,preload:u=!1,fadeIn:m=!0,wrapperAs:g="div",fillContainer:f=!1,sizes:x="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",style:v,priority:b=!1,qualityOverride:y,...w})=>{let j=(0,s.useRef)(!0),[N,A]=(0,s.useState)(b?"loaded":"loading"),[C,P]=(0,s.useState)({width:f?void 0:r,height:f?void 0:l}),E="string"==typeof e?e:e?.src||e?.url||e?.default?.src||null,I=p&&h&&E?o.imageUtils.getBlurDataUrl(E,20):h?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+":void 0;(0,s.useEffect)(()=>()=>{j.current=!1},[E,u,b,r]);let _="number"==typeof r&&"number"==typeof l&&r>0&&l>0?r/l:void 0,$=(0,s.useCallback)(e=>{if(!f&&e?.target){let{naturalWidth:t,naturalHeight:r}=e.target;t&&r&&j.current&&P({width:t,height:r})}j.current&&A("loaded")},[f]),T=(0,s.useCallback)(()=>{A("error")},[E,t]),S=b?"eager":"lazy";if("error"===N||!E)return(0,a.jsx)("div",{className:`flex items-center justify-center bg-gray-100 ${d||(f?"":c)}`,style:{width:f?"100%":r,height:f?"100%":l,aspectRatio:_?`${_}`:void 0,...v},role:"img","aria-label":t||"Image failed to load",children:(0,a.jsx)(i.fZZ,{className:"text-gray-400 w-1/5 h-1/5"})});let L=[c,"loaded"===N?"opacity-100":"opacity-0",m?"transition-opacity duration-300":""].filter(Boolean).join(" "),R={objectFit:v?.objectFit||"cover",aspectRatio:f?void 0:_?`${_}`:void 0,...v,width:f?void 0:v?.width,height:f?void 0:v?.height},k=(0,a.jsx)(n.default,{src:E,alt:t||"",width:f?void 0:C.width,height:f?void 0:C.height,fill:f,className:L,loading:S,fetchPriority:b?"high":u?"low":"auto",priority:b,sizes:x,style:R,placeholder:h?"blur":"empty",blurDataURL:I,onLoad:$,onError:T,quality:y,...w}),U=f?{width:"100%",height:"100%",position:"relative",...v}:{width:C.width,height:C.height,aspectRatio:_?`${_}`:void 0,position:"relative",...v};return(0,a.jsxs)(g,{className:`relative ${f?"w-full h-full":""}`,style:U,children:[k,"loading"===N&&h&&(0,a.jsx)("div",{className:`absolute inset-0 bg-gray-100 animate-pulse ${d||""}`,style:{width:"100%",height:"100%"},"aria-hidden":"true"})]})};l.displayName="LazyImage";let c=(0,s.memo)(l)},25743:(e,t,r)=>{"use strict";r.d(t,{ErrorProvider:()=>i,q:()=>o});var a=r(60687),s=r(43210);let n=(0,s.createContext)(void 0),i=({children:e})=>{let[t,r]=(0,s.useState)(null),[i,o]=(0,s.useState)([]);return(0,a.jsx)(n.Provider,{value:{globalError:t,setGlobalError:r,clearGlobalError:()=>{r(null)},addErrorLog:(e,t)=>{let r={id:Date.now().toString(),error:e,timestamp:new Date,source:t};o(e=>[r,...e].slice(0,10))},errorLogs:i},children:e})},o=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useError must be used within an ErrorProvider");return e}},28136:(e,t,r)=>{"use strict";r.d(t,{Jf:()=>h,Rb:()=>p,zl:()=>d});var a=r(66501);let s={NEXT_PUBLIC_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_MEDIA_URL:"https://nice-badge-2130241d6c.media.strapiapp.com",NEXT_PUBLIC_SITE_URL:process.env.NEXT_PUBLIC_SITE_URL,IMAGE_HOSTNAME:process.env.IMAGE_HOSTNAME,NODE_ENV:"production"},n=s.NEXT_PUBLIC_STRAPI_API_URL||s.NEXT_PUBLIC_API_URL||("development"===s.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.strapiapp.com"),i=(()=>{if(s.NEXT_PUBLIC_STRAPI_MEDIA_URL)return l(c(s.NEXT_PUBLIC_STRAPI_MEDIA_URL));if(s.IMAGE_HOSTNAME)return l(c(s.IMAGE_HOSTNAME));try{let e=new URL(n);if(e.hostname.endsWith("strapiapp.com"))return`${l(e.protocol)}//${e.hostname.replace("strapiapp.com","media.strapiapp.com")}`;return l(c(n))}catch(e){return"development"===s.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.media.strapiapp.com"}})(),o=s.NEXT_PUBLIC_SITE_URL||(s.NEXT_PUBLIC_API_URL&&s.NEXT_PUBLIC_API_URL.includes("strapiapp.com")?s.NEXT_PUBLIC_API_URL.replace(".strapiapp.com",".vercel.app"):"https://naturalhealingnow.vercel.app");function l(e){return e?e.replace(/^http:/,"https:"):e}function c(e){return e&&e.endsWith("/")?e.slice(0,-1):e}function d(e){return e?e.startsWith("http://")||e.startsWith("https://")?h(e):i?`${i}/${e.startsWith("/")?e.substring(1):e}`:(a.Ay.warn("STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaPath",{imagePath:e}),`${n}/${e.startsWith("/")?e.substring(1):e}`):""}function h(e){let t;if("string"==typeof e&&(e.startsWith("https://")||e.startsWith("http://")||e.startsWith("/")))return e.startsWith("http://")?e.replace(/^http:/,"https:"):e;if(!e)return"";if("object"==typeof e&&e.url&&"string"==typeof e.url)t=e.url;else{if("string"!=typeof e)return a.Ay.warn("Invalid input type for sanitizeUrl. Expected string or object with url property.",{inputType:typeof e}),"";t=e}(t=t.trim()).toLowerCase().startsWith("undefined")&&(t=t.substring(9),a.Ay.info('Removed "undefined" prefix from URL',{original:e,new:t}));let r=n.replace(/^https?:\/\//,"").split("/")[0],s=i.replace(/^https?:\/\//,"").split("/")[0];if(r&&s&&t.includes(r)&&t.includes(s)){let e=RegExp(`(https?://)?(${r})(/*)(https?://)?(${s})`,"gi"),n=`https://${s}`;if(e.test(t)){let i=t;t=t.replace(e,n),a.Ay.info("Fixed concatenated Strapi domains",{original:i,fixed:t,apiDomain:r,mediaDomain:s})}}if(t.includes("https//")){let e=t;t=t.replace(/https\/\//g,"https://"),a.Ay.info("Fixed missing colon in URL (https//)",{original:e,fixed:t})}if(t.startsWith("//")?t=`https:${t}`:(t.includes("media.strapiapp.com")||t.includes(s))&&!t.startsWith("http")?t=`https://${t}`:(t.startsWith("localhost")||t.startsWith(r.split(".")[0]))&&(t=`https://${t}`),t.startsWith("/"))return t;if(t.startsWith("http://")||t.startsWith("https://"))try{return new URL(t),t}catch(e){if(a.Ay.error("URL parsing failed after sanitization attempts",{url:t,error:e}),!t.includes("://")&&!t.includes("."))return t;return""}return i&&t&&!t.includes("://")?(a.Ay.debug("Assuming relative media path, prepending STRAPI_MEDIA_URL",{path:t}),`/${t}`):(a.Ay.warn("sanitizeUrl could not produce a valid absolute or relative URL",{originalInput:e,finalSanitized:t}),t)}function p(e){if(!e)return;let t=h(e);if(t){if(t.startsWith("http://")||t.startsWith("https://"))return t.replace(/^http:/,"https:");if(i){let r=`${i}${t.startsWith("/")?"":"/"}${t}`;return a.Ay.debug("Constructed OG image URL from relative path",{original:e,final:r}),r.replace(/^http:/,"https:")}if(a.Ay.warn("Could not determine OG image URL confidently",{originalUrl:e,processedUrl:t}),n)return`${n}${t.startsWith("/")?"":"/"}${t}`.replace(/^http:/,"https:")}}"development"===s.NODE_ENV&&a.Ay.debug("Media Utils Initialized:",{EFFECTIVE_STRAPI_URL:n,STRAPI_MEDIA_URL:i,SITE_URL:o})},28351:(e,t,r)=>{"use strict";r.d(t,{default:()=>D});var a=r(60687),s=r(85814),n=r.n(s),i=r(43210),o=r(17019),l=r(6363);function c(){let{user:e,signOut:t}=(0,l.A)(),[r,s]=(0,i.useState)(!1),c=(0,i.useRef)(null),d=async()=>{await t(),s(!1)},h=e?.email&&e.email.length>20?`${e.email.substring(0,17)}...`:e?.email;return(0,a.jsxs)("div",{className:"relative",ref:c,children:[(0,a.jsxs)("button",{onClick:()=>s(!r),className:"flex items-center text-gray-700 hover:text-emerald-600 focus:outline-none","aria-expanded":r,"aria-haspopup":"true",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center mr-2",children:(0,a.jsx)(o.JXP,{className:"text-emerald-600"})}),(0,a.jsx)("span",{className:"hidden md:block max-w-[150px] truncate",children:h}),(0,a.jsx)(o.fK4,{className:`ml-1 transition-transform ${r?"rotate-180":""}`})]}),r&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200",children:[(0,a.jsxs)(n(),{href:"/account",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center",onClick:()=>s(!1),children:[(0,a.jsx)(o.JXP,{className:"mr-2"}),"My Account"]}),(0,a.jsxs)("button",{onClick:d,className:"w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center",children:[(0,a.jsx)(o.QeK,{className:"mr-2"}),"Sign Out"]})]})]})}var d=r(24587),h=r(8693),p=r(51060);let u="https://nice-badge-2130241d6c.strapiapp.com",m="/api",g=()=>{let e=process.env.STRAPI_API_TOKEN;return e||console.warn("No STRAPI_API_TOKEN found in environment variables for server-side API client"),p.A.create({baseURL:`${u}${m}`,headers:{"Content-Type":"application/json",...e?{Authorization:`Bearer ${e}`}:{}}})},f=()=>p.A.create({baseURL:`${u}${m}`,headers:{"Content-Type":"application/json"}}),x=async(e,t={},r=!0)=>{try{let a=r?g():f();return(await a.get(e,t)).data}catch(t){throw console.error(`Error fetching from API (${e}):`,t),t.response&&(console.error("Response data:",t.response.data),console.error("Response status:",t.response.status)),t}},v=e=>1e3*Math.pow(2,e),b=[408,429,500,502,503,504],y=e=>!e.response||b.includes(e.response.status),w=async(e,t={},r=!0,a=0)=>{try{return await x(e,t,r)}catch(s){if(p.A.isAxiosError(s)&&y(s)&&a<2){let s=v(a);return console.warn(`Retrying API request to ${e} after ${s}ms (attempt ${a+1}/2)`),await new Promise(e=>setTimeout(e,s)),w(e,t,r,a+1)}throw j(s,e)}},j=(e,t)=>{let r=`API Error (${t}): `;if(p.A.isAxiosError(e)){if(e.response){let a=e.response.status,s=e.response.data,n=Error(r+=`${a} - ${s?.error?.message||"Unknown server error"}`);return n.status=a,n.endpoint=t,n.responseData=s,n}else if(e.request){let e=Error(r+="No response received from server");return e.endpoint=t,e.isNetworkError=!0,e}}return Error(r+=e.message||"Unknown error")},N=new Map;var A=r(16189);function C({href:e,children:t,className:r,prefetchApiEndpoint:s,prefetchApiParams:o,prefetchQueryKey:l,prefetchOnHover:c=!0,prefetchOnMount:d=!1,onClick:p,ariaLabel:u,title:m}){let g=(0,h.jE)();(0,A.useRouter)();let[f,x]=(0,i.useState)(!1),v=async()=>{if(!f)try{let t=document.createElement("link");t.rel="prefetch",t.href=e,t.as="document",document.head.appendChild(t),s&&await function(e,t,r,a){let s=a||[t,r];return e.prefetchQuery({queryKey:s,queryFn:async()=>{let e={};return r&&(e.params=r),await w(t,e,!1)},staleTime:3e5})}(g,s,o,l),x(!0)}catch(e){console.error("Error preloading data:",e)}};return d&&!f&&v(),(0,a.jsx)(n(),{href:e,className:r,"aria-label":u,title:m,onClick:e=>{p&&p()},onMouseEnter:()=>{c&&v()},onTouchStart:()=>{c&&v()},prefetch:!1,children:t})}var P=r(28136);let E=({siteName:e,logoLight:t})=>{let[r,s]=(0,i.useState)(!1),{user:h,isLoading:p}=(0,l.A)(),u=t?.url,m=t?.alternativeText||e,g=t?.width,f=t?.height,x="https://nice-badge-2130241d6c.strapiapp.com";process.env.IMAGE_HOSTNAME||x&&x.replace("strapiapp.com","media.strapiapp.com");let v="";return u&&(v=(0,P.zl)(u)),console.log("Logo URL:",{original:u,processed:v}),(0,a.jsx)("header",{className:"bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsxs)(n(),{href:"/",className:"inline-block align-middle",children:[" ",u&&g&&f?(0,a.jsx)(d.default,{src:v,alt:m,width:g,height:f,className:"h-12 w-auto",aboveTheFold:!0,showPlaceholder:!1,priority:!0,unoptimized:!0}):u?(0,a.jsx)("img",{src:v,alt:m,className:"h-12 w-auto"}):e?(0,a.jsx)("span",{className:"text-2xl font-bold text-emerald-600",children:e}):(0,a.jsx)("span",{className:"text-2xl font-bold text-emerald-600",children:"My Directory Site"})]})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center",children:[(0,a.jsxs)("nav",{className:"flex space-x-8 mr-8",children:[(0,a.jsx)(C,{href:"/",className:"text-gray-700 hover:text-emerald-600",prefetchOnHover:!0,children:"Home"}),(0,a.jsx)(C,{href:"/clinics",className:"text-gray-700 hover:text-emerald-600",prefetchApiEndpoint:"/clinics",prefetchApiParams:{sort:"name:asc",pagination:{page:1,pageSize:12},populate:"*"},prefetchOnHover:!0,children:"Find a Clinic"}),(0,a.jsx)(C,{href:"/practitioners",className:"text-gray-700 hover:text-emerald-600",prefetchApiEndpoint:"/practitioners",prefetchApiParams:{sort:"name:asc",pagination:{page:1,pageSize:12},populate:"*"},prefetchOnHover:!0,children:"Find a Practitioner"}),(0,a.jsx)(C,{href:"/categories",className:"text-gray-700 hover:text-emerald-600",prefetchApiEndpoint:"/categories",prefetchApiParams:{sort:"name:asc",populate:"*"},prefetchOnHover:!0,children:"Categories"}),(0,a.jsx)(C,{href:"/blog",className:"text-gray-700 hover:text-emerald-600",prefetchApiEndpoint:"/blog-posts",prefetchApiParams:{sort:"publishDate:desc",pagination:{page:1,pageSize:10},populate:{featuredImage:!0,author_blogs:{populate:{profilePicture:!0}}}},prefetchOnHover:!0,children:"Blog"})]}),!p&&(h?(0,a.jsx)(c,{}):(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(n(),{href:"/signin",className:"text-gray-700 hover:text-emerald-600 font-medium",children:"Sign In"}),(0,a.jsx)(n(),{href:"/signup",className:"bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700",children:"Sign Up"})]}))]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("button",{onClick:()=>{s(!r)},className:"text-gray-700 hover:text-emerald-600 focus:outline-none","aria-label":r?"Close menu":"Open menu",children:r?(0,a.jsx)(o.yGN,{size:24}):(0,a.jsx)(o.ND1,{size:24})})})]}),r&&(0,a.jsxs)("nav",{className:"md:hidden mt-4 space-y-4 pb-4",children:[(0,a.jsx)(n(),{href:"/",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>s(!1),children:"Home"}),(0,a.jsx)(n(),{href:"/clinics",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>s(!1),children:"Find a Clinic"}),(0,a.jsx)(n(),{href:"/practitioners",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>s(!1),children:"Find a Practitioner"}),(0,a.jsx)(n(),{href:"/categories",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>s(!1),children:"Categories"}),(0,a.jsx)(n(),{href:"/blog",className:"block text-gray-700 hover:text-emerald-600",onClick:()=>s(!1),children:"Blog"}),!p&&(h?(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:[(0,a.jsxs)(n(),{href:"/account",className:"flex items-center text-gray-700 hover:text-emerald-600",onClick:()=>s(!1),children:[(0,a.jsx)(o.JXP,{className:"mr-2"}),"My Account"]}),(0,a.jsx)("button",{onClick:()=>{s(!1)},className:"mt-2 flex items-center text-gray-700 hover:text-emerald-600",children:(0,a.jsx)("span",{className:"text-red-600",children:"Sign Out"})})]}):(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-4 mt-4 flex flex-col space-y-2",children:[(0,a.jsx)(n(),{href:"/signin",className:"block text-gray-700 hover:text-emerald-600 font-medium",onClick:()=>s(!1),children:"Sign In"}),(0,a.jsx)(n(),{href:"/signup",className:"block text-emerald-600 hover:text-emerald-700 font-medium",onClick:()=>s(!1),children:"Sign Up"})]}))]})]})})};var I=r(30474);let _=({siteName:e,logoLight:t,footerCategories:r})=>{let s=new Date().getFullYear(),i="https://nice-badge-2130241d6c.strapiapp.com";process.env.IMAGE_HOSTNAME||i&&i.replace("strapiapp.com","media.strapiapp.com");let l=null;return t?.url&&(l=(0,P.zl)(t.url)),t&&console.log("Footer Logo URL:",{original:t.url,processed:l}),(0,a.jsx)("footer",{className:"bg-gray-800 text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{children:[l?(0,a.jsx)(n(),{href:"/",className:"inline-block mb-4",children:(0,a.jsx)(I.default,{src:l,alt:t?.alternativeText||e,width:t?.width||150,height:t?.height||40,className:"h-12 w-auto",unoptimized:!0,priority:!0})}):(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:e}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"Connecting you with holistic health practitioners and clinics to support your wellness journey."}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("a",{href:"#",target:"_blank",rel:"nofollow noopener noreferrer",className:"text-gray-300 hover:text-white","aria-label":"Facebook",children:(0,a.jsx)(o.spH,{size:20})}),(0,a.jsx)("a",{href:"#",target:"_blank",rel:"nofollow noopener noreferrer",className:"text-gray-300 hover:text-white","aria-label":"Twitter",children:(0,a.jsx)(o.TC4,{size:20})}),(0,a.jsx)("a",{href:"#",target:"_blank",rel:"nofollow noopener noreferrer",className:"text-gray-300 hover:text-white","aria-label":"Instagram",children:(0,a.jsx)(o.eCe,{size:20})}),(0,a.jsx)("a",{href:"#",target:"_blank",rel:"nofollow noopener noreferrer",className:"text-gray-300 hover:text-white","aria-label":"LinkedIn",children:(0,a.jsx)(o.Wjy,{size:20})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/",className:"text-gray-300 hover:text-white",children:"Home"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/clinics",className:"text-gray-300 hover:text-white",children:"Find a Clinic"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/practitioners",className:"text-gray-300 hover:text-white",children:"Find a Practitioner"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/blog",className:"text-gray-300 hover:text-white",children:"Blog"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/about-us",className:"text-gray-300 hover:text-white",children:"About Us"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Categories"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[r&&r.length>0?r.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:e.attributes.slug&&"#"!==e.attributes.slug?`/categories/${e.attributes.slug}`:"#",className:"text-gray-300 hover:text-white",children:e.attributes.name})},e.id)):(0,a.jsx)("li",{children:"No categories available."}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/categories",className:"text-gray-300 hover:text-white",children:"View All Categories"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Contact Us"}),(0,a.jsx)("p",{className:"text-gray-300 mb-2",children:"Have questions or feedback?"}),(0,a.jsx)(n(),{href:"/contact",className:"text-emerald-400 hover:text-emerald-300 mb-2 block",children:"Get in touch with us"}),(0,a.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,a.jsx)(n(),{href:"/privacy",className:"text-gray-300 hover:text-white text-sm block",children:"Privacy Policy"}),(0,a.jsx)(n(),{href:"/terms",className:"text-gray-300 hover:text-white text-sm block",children:"Terms of Service"}),(0,a.jsx)(n(),{href:"/affiliate-disclosure",className:"text-gray-300 hover:text-white text-sm block",children:"Affiliate Disclosure"})]})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-700 mt-8 pt-8 text-center text-gray-400",children:(0,a.jsxs)("p",{children:["\xa9 ",s," ",e,". All rights reserved."]})})]})})};var $=r(66798);class T extends i.Component{constructor(e){super(e),this.state={hasError:!1,error:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error caught by ErrorBoundary:",e,t),this.props.onError&&this.props.onError(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsx)($.A,{error:this.state.error,resetErrorBoundary:()=>this.setState({hasError:!1,error:null})}):this.props.children}}var S=r(25743);let L=({children:e})=>{let{addErrorLog:t}=(0,S.q)();return(0,i.useEffect)(()=>{let e=e=>{console.error("Unhandled promise rejection:",e.reason),t(e.reason instanceof Error?e.reason:Error(String(e.reason)),"unhandled-promise-rejection")};return window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("unhandledrejection",e)}},[t]),(0,a.jsx)(T,{onError:(e,r)=>{t(e,"react-error-boundary")},children:e})};var R=r(72600),k=function(e){return e.BEFORE_INTERACTIVE="beforeInteractive",e.AFTER_INTERACTIVE="afterInteractive",e.LAZY_ONLOAD="lazyOnload",e.WORKER="worker",e}({});let U={};function M(){let e=Object.values(U);return(0,a.jsx)(a.Fragment,{children:e.map(e=>{if(!e.src&&!e.content)return null;let t=e.attributes||{};return e.content?(0,a.jsx)(R.default,{id:e.id,strategy:e.strategy||k.AFTER_INTERACTIVE,dangerouslySetInnerHTML:{__html:e.content},onLoad:e.onLoad,onError:e.onError?t=>e.onError?.(Error(`Failed to load script: ${e.id}`)):void 0,...t},e.id):(0,a.jsx)(R.default,{id:e.id,src:e.src,strategy:e.strategy||k.AFTER_INTERACTIVE,onLoad:e.onLoad,onError:e.onError?t=>e.onError?.(Error(`Failed to load script: ${e.id}`)):void 0,...t},e.id)})})}function B({googleAnalyticsId:e,enableInDevelopment:t=!1}){return null}function O({dnsPrefetch:e=[],preconnect:t=[],preload:r=[],prefetch:s=[]}){let n=`
    (function() {
      const head = document.head;

      // DNS Prefetch
      ${e.map(e=>`
        const dnsPrefetch_${e.replace(/[^a-zA-Z0-9]/g,"_")} = document.createElement('link');
        dnsPrefetch_${e.replace(/[^a-zA-Z0-9]/g,"_")}.rel = 'dns-prefetch';
        dnsPrefetch_${e.replace(/[^a-zA-Z0-9]/g,"_")}.href = '${e}';
        head.appendChild(dnsPrefetch_${e.replace(/[^a-zA-Z0-9]/g,"_")});
      `).join("")}

      // Preconnect
      ${t.map(e=>`
        const preconnect_${e.replace(/[^a-zA-Z0-9]/g,"_")} = document.createElement('link');
        preconnect_${e.replace(/[^a-zA-Z0-9]/g,"_")}.rel = 'preconnect';
        preconnect_${e.replace(/[^a-zA-Z0-9]/g,"_")}.href = '${e}';
        preconnect_${e.replace(/[^a-zA-Z0-9]/g,"_")}.crossOrigin = 'anonymous';
        head.appendChild(preconnect_${e.replace(/[^a-zA-Z0-9]/g,"_")});
      `).join("")}

      // Preload
      ${r.map((e,t)=>`
        const preload_${t} = document.createElement('link');
        preload_${t}.rel = 'preload';
        preload_${t}.href = '${e.href}';
        preload_${t}.as = '${e.as}';
        ${e.type?`preload_${t}.type = '${e.type}';`:""}
        ${e.crossOrigin?`preload_${t}.crossOrigin = '${e.crossOrigin}';`:""}
        head.appendChild(preload_${t});
      `).join("")}

      // Prefetch
      ${s.map((e,t)=>`
        const prefetch_${t} = document.createElement('link');
        prefetch_${t}.rel = 'prefetch';
        prefetch_${t}.href = '${e.href}';
        ${e.as?`prefetch_${t}.as = '${e.as}';`:""}
        head.appendChild(prefetch_${t});
      `).join("")}
    })();
  `;return(0,a.jsx)(R.default,{id:"resource-hints",strategy:"beforeInteractive",dangerouslySetInnerHTML:{__html:n}})}let z=()=>{let[e,t]=(0,i.useState)(!1),[r,a]=(0,i.useState)([]);return(0,i.useEffect)(()=>{},[]),null},D=({children:e,siteName:t,logoLight:r,footerCategories:s})=>{let n="https://nice-badge-2130241d6c.strapiapp.com";return(0,a.jsxs)(L,{children:[(0,a.jsx)(O,{dnsPrefetch:[n,"https://fonts.googleapis.com","https://fonts.gstatic.com"],preconnect:[n,"https://fonts.googleapis.com","https://fonts.gstatic.com"],preload:[{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap",as:"style"}]}),(0,a.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,a.jsx)(E,{siteName:t,logoLight:r}),(0,a.jsx)("main",{className:"flex-grow",children:e}),(0,a.jsx)(_,{siteName:t,logoLight:r,footerCategories:s}),(0,a.jsx)(B,{googleAnalyticsId:process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,enableInDevelopment:!1}),(0,a.jsx)(M,{}),(0,a.jsx)(z,{})]})]})}},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var a=r(12907);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\contexts\\AuthContext.tsx","useAuth")},31369:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx","default")},33787:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},42921:(e,t,r)=>{Promise.resolve().then(r.bind(r,8811))},44263:()=>{},45851:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(60687),s=r(43210),n=r(92314),i=r(8693);function o({children:e}){let[t]=(0,s.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:3e5,gcTime:18e5,refetchOnWindowFocus:!0}}}));return(0,a.jsxs)(i.Ht,{client:t,children:[e,!1]})}},49409:(e,t,r)=>{Promise.resolve().then(r.bind(r,88741)),Promise.resolve().then(r.bind(r,96959)),Promise.resolve().then(r.bind(r,81012)),Promise.resolve().then(r.bind(r,57570)),Promise.resolve().then(r.t.bind(r,79167,23)),Promise.resolve().then(r.bind(r,28351)),Promise.resolve().then(r.bind(r,6363)),Promise.resolve().then(r.bind(r,25743)),Promise.resolve().then(r.bind(r,45851))},50293:(e,t,r)=>{Promise.resolve().then(r.bind(r,54431))},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(37413),s=r(4536),n=r.n(s),i=r(73993);function o(){return(0,a.jsx)("div",{className:"container mx-auto px-4 py-16 flex flex-col items-center justify-center min-h-[60vh]",children:(0,a.jsxs)("div",{className:"text-center max-w-md",children:[(0,a.jsx)("div",{className:"flex justify-center mb-6",children:(0,a.jsx)(i.y3G,{className:"w-16 h-16 text-emerald-600"})}),(0,a.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"404 - Page Not Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"The page you are looking for doesn't exist or has been moved."}),(0,a.jsxs)(n(),{href:"/",className:"inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 transition-colors",children:[(0,a.jsx)(i.V5Y,{className:"mr-2"}),"Back to Homepage"]})]})})}},54431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx","default")},54920:(e,t,r)=>{"use strict";r.d(t,{Sk:()=>h,TW:()=>m,U0:()=>u,YA:()=>l,bW:()=>i,fv:()=>n,g6:()=>d,qC:()=>o,sm:()=>c,tz:()=>p});var a=r(61120);r(62351);var s=r(58446);process.env.NEXT_PUBLIC_CACHE_METRICS;let n=(0,a.cache)(async()=>s.$.global.getSettings({cache:"force-cache",next:{revalidate:3600,tags:["strapi-global-setting"]}})),i=(0,a.cache)(async e=>{let t=e?.next||{revalidate:604800,tags:["strapi-categories","strapi-categories-all"]};return s.$.categories.getAll({...e,cache:"force-cache",next:t})}),o=(0,a.cache)(async e=>{let t=e?.next||{revalidate:604800,tags:["strapi-categories","strapi-categories-footer"]};return s.$.categories.getFooterCategories({...e,cache:"force-cache",next:t})});(0,a.cache)(async e=>{let t=e?.next||{revalidate:604800,tags:["strapi-specialties","strapi-specialties-all"]};return s.$.specialties.getAll({...e,cache:"force-cache",next:t})}),(0,a.cache)(async(e={})=>{let t=e.next||{revalidate:3600,tags:["strapi-clinics",`page-${e.page||1}`]};return s.$.clinics.getAll({...e,next:t})}),(0,a.cache)(async(e,t)=>{let r=t?.next||{revalidate:3600,tags:["strapi-clinics",`strapi-clinic-${e}`]};return s.$.clinics.getBySlug(e,{next:r})});let l=(0,a.cache)(async e=>{let t=e?.next||{revalidate:3600,tags:["strapi-clinics","strapi-clinics-featured"]};return s.$.clinics.getFeatured({next:t})});(0,a.cache)(async(e={})=>{let t=e.next||{revalidate:3600,tags:["strapi-practitioners",`page-${e.page||1}`]};return s.$.practitioners.getAll({...e,next:t})}),(0,a.cache)(async(e,t)=>{let r=t?.next||{revalidate:3600,tags:["strapi-practitioner",`strapi-practitioner-${e}`]};return s.$.practitioners.getBySlug(e,{next:r})});let c=(0,a.cache)(async e=>{let t=e?.next||{revalidate:3600,tags:["strapi-practitioners","strapi-practitioners-featured"]};return s.$.practitioners.getFeatured({next:t})}),d=(0,a.cache)(async(e={})=>{let t=e.next||{revalidate:3600,tags:["strapi-blog-posts",`page-${e.page||1}`]};return s.$.blog.getPosts({...e,sort:e.sort||["publishDate:desc"],next:t})});(0,a.cache)(async(e,t)=>{let r=t?.next||{revalidate:3600,tags:["strapi-blog-posts",`strapi-blog-post-${e}`]};return s.$.blog.getPostBySlug(e,{next:r})});let h=(0,a.cache)(async e=>{let t=e?.next||{revalidate:3600,tags:["strapi-blog-posts","strapi-blog-posts-featured"]};return s.$.blog.getPosts({filters:{isFeatured:{$eq:!0}},sort:["publishDate:desc"],pagination:{page:1,pageSize:10},next:t})}),p=(0,a.cache)(async e=>{let t=e?.next||{revalidate:3600,tags:["strapi-blog-categories","strapi-categories"]};return s.$.blog.getCategories({next:t})}),u=(0,a.cache)(async e=>{let t=e?.next||{revalidate:3600,tags:["strapi-blog-tags","strapi-tags"]};return s.$.blog.getTags({next:t})}),m=(0,a.cache)(async e=>{let t=e?.next||{revalidate:3600,tags:["strapi-global-setting","strapi-blog-homepage"]};return s.$.global.getBlogHomepage({next:t})});(0,a.cache)(async(e,t)=>{let r=t?.next||{revalidate:3600,tags:["strapi-specialties",`strapi-specialty-${e}`]};return s.$.specialties.getBySlug(e,{next:r})}),(0,a.cache)(async(e,t)=>{let r=t?.next||{revalidate:3600,tags:["strapi-conditions",`strapi-condition-${e}`]};return s.$.conditions.getBySlug(e,{next:r})}),(0,a.cache)(async e=>{let t=e?.next||{revalidate:3600,tags:["strapi-conditions","strapi-conditions-all"]};return s.$.conditions.getAll({next:t})}),(0,a.cache)(async(e,t)=>{let r=t?.next||{revalidate:3600,tags:["strapi-categories",`strapi-category-${e}`]};return s.$.categories.getBySlug(e,{next:r})})},61135:()=>{},63861:(e,t,r)=>{Promise.resolve().then(r.bind(r,14329))},66501:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i});var a=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let s={enabled:!1,level:"info",prefix:"[NHN]"};function n(e,t,...r){if(!s.enabled)return;let i=Object.values(a),o=i.indexOf(s.level);if(i.indexOf(e)>=o){let a=s.prefix?`${s.prefix} `:"",n=`${a}${t}`;switch(e){case"debug":console.debug(n,...r);break;case"info":console.info(n,...r);break;case"warn":console.warn(n,...r);break;case"error":console.error(n,...r)}}}let i={debug:function(e,...t){n("debug",e,...t)},info:function(e,...t){n("info",e,...t)},warn:function(e,...t){n("warn",e,...t)},error:function(e,...t){n("error",e,...t)},configure:function(e){s={...s,...e}}}},66798:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(60687);r(43210);var s=r(85814),n=r.n(s),i=r(17019);let o=({error:e,resetErrorBoundary:t,message:r="Something went wrong",showHomeLink:s=!0,showRefreshButton:o=!0})=>(e?.message,(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 my-4 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4 text-red-600",children:[(0,a.jsx)(i.eHT,{className:"w-6 h-6 mr-2"}),(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"Error Encountered"})]}),(0,a.jsx)("p",{className:"mb-4 text-gray-700",children:r}),!1,(0,a.jsxs)("div",{className:"flex flex-wrap gap-3 mt-4",children:[o&&t&&(0,a.jsxs)("button",{onClick:t,className:"flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors",children:[(0,a.jsx)(i.jTZ,{className:"mr-2"}),"Try Again"]}),o&&!t&&(0,a.jsxs)("button",{onClick:()=>window.location.reload(),className:"flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors",children:[(0,a.jsx)(i.jTZ,{className:"mr-2"}),"Refresh Page"]}),s&&(0,a.jsxs)(n(),{href:"/",className:"flex items-center px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors",children:[(0,a.jsx)(i.V5Y,{className:"mr-2"}),"Go to Homepage"]})]})]}))},68131:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71041:(e,t,r)=>{"use strict";r.d(t,{ErrorProvider:()=>s});var a=r(12907);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call ErrorProvider() from the server but ErrorProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\contexts\\ErrorContext.tsx","ErrorProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useError() from the server but useError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\contexts\\ErrorContext.tsx","useError"),(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\contexts\\\\ErrorContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\contexts\\ErrorContext.tsx","default")},82113:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\providers\\\\QueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\providers\\QueryProvider.tsx","default")},87190:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,generateMetadata:()=>u});var a=r(37413),s=r(85041),n=r.n(s);r(61135);var i=r(96800),o=r(29131),l=r(71041),c=r(14143),d=r(82113),h=r(54920),p=r(23487);async function u(){let e="/favicon.ico",t="Natural Healing Now - Holistic Health Directory",r="Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.",a="https://nice-badge-2130241d6c.strapiapp.com";try{let s=await (0,h.fv)();if(s?.data){let n=null;if(n="attributes"in s.data&&s.data.attributes?s.data.attributes:s.data){let s=n.favicon,i=s?.data?.attributes??s?.attributes??s;if(i?.url){let t=i.url;t&&a&&(e=t.startsWith("/")?`${a}${t}`:t)}t=n.siteName||t,r=n.defaultSeoDescription||r}}}catch(e){console.error("Failed to fetch global settings for metadata:",e)}return{title:t,description:r,icons:{icon:e,shortcut:e,apple:e}}}async function m({children:e}){let t="Natural Healing Now",r=null,s=[];try{let[e,a]=await Promise.all([(0,h.fv)(),(0,h.qC)()]);if(e?.data){let a=null;if(a="attributes"in e.data&&e.data.attributes?e.data.attributes:e.data){t=a.siteName||t;let e=a.logoLight,s=e?.data?.attributes??e?.attributes??e;if(s?.url){let e="string"==typeof s.id?parseInt(s.id,10):"number"==typeof s.id?s.id:void 0;void 0!==e&&isNaN(e)&&(e=void 0),r={id:e||0,name:s.name||"",alternativeText:s.alternativeText||t,caption:s.caption,width:s.width,height:s.height,formats:s.formats,hash:s.hash||"",ext:s.ext||"",mime:s.mime||"",size:s.size||0,url:s.url,previewUrl:s.previewUrl,provider:s.provider||"local",provider_metadata:s.provider_metadata,createdAt:s.createdAt||"",updatedAt:s.updatedAt||"",publishedAt:null===s.publishedAt?void 0:s.publishedAt}}}}a?.data&&Array.isArray(a.data)?s=a.data.map(e=>{let t,r;return t="attributes"in e&&e.attributes?e.attributes:e,{id:e.id,attributes:{name:t.name||"Unknown Category",slug:t.slug||"unknown-category"}}}).filter(Boolean):null===a&&console.warn("Received null for categoriesResponse in RootLayout, likely due to fetch error.")}catch(e){console.error("Failed to fetch initial data in RootLayout:",e)}return(0,a.jsxs)("html",{lang:"en",children:[process.env.NEXT_PUBLIC_GTM_ID&&(0,a.jsx)(p.GoogleTagManager,{gtmId:process.env.NEXT_PUBLIC_GTM_ID}),(0,a.jsxs)("body",{className:n().className,children:[(0,a.jsx)(l.ErrorProvider,{children:(0,a.jsx)(o.AuthProvider,{children:(0,a.jsx)(d.default,{children:(0,a.jsx)(c.default,{siteName:t,logoLight:r,footerCategories:s,children:e})})})}),(0,a.jsx)(i.Analytics,{})]})]})}},98814:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))}};