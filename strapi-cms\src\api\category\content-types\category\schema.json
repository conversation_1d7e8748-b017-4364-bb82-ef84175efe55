{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category (Directory)", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "richtext"}, "icon": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["files", "images"]}, "featuredImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "clinics": {"type": "relation", "relation": "manyToMany", "target": "api::clinic.clinic", "mappedBy": "categories"}, "practitioners": {"type": "relation", "relation": "manyToMany", "target": "api::practitioner.practitioner", "mappedBy": "categories"}, "showInFooter": {"type": "boolean"}, "contentBottomCategory": {"type": "richtext"}}}