/**
 * Utility functions for finding related blog posts
 */

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  content?: string;
  featured_image?: string | null;
  published_at: string; // Changed from publish_date to match actual data structure
  reading_time?: number;
  author?: {
    id: string; // Added id field for author
    name: string;
    slug: string;
    profile_picture?: string | null;
    bio?: string | null;
  } | null;
  categories?: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  tags?: string[];
  related_posts?: Array<{
    id: string;
    title: string;
    slug: string;
    excerpt?: string | null;
    content?: string;
    reading_time?: number;
  }>;
}

/**
 * Calculate similarity score between two posts based on various factors
 */
export function calculateSimilarityScore(post1: BlogPost, post2: BlogPost): number {
  let score = 0;
  let debugInfo: Record<string, number> = {};

  // Don't compare the same post
  if (post1.id === post2.id) return 0;

  // Check for matching categories
  if (post1.categories && post2.categories) {
    const post1CategoryIds = post1.categories.map(cat => cat.id);
    const post2CategoryIds = post2.categories.map(cat => cat.id);

    let categoryScore = 0;
    // Add points for each matching category
    post1CategoryIds.forEach(catId => {
      if (post2CategoryIds.includes(catId)) {
        categoryScore += 3; // Categories are strong indicators of relation
      }
    });

    score += categoryScore;
    debugInfo.categoryScore = categoryScore;
  } else {
    console.log(`Missing categories for comparison: post1 has ${post1.categories?.length || 0}, post2 has ${post2.categories?.length || 0}`);
  }

  // Check for matching tags
  if (post1.tags && post2.tags) {
    let tagScore = 0;
    post1.tags.forEach(tag => {
      if (post2.tags?.includes(tag)) {
        tagScore += 2; // Tags are good indicators of relation
      }
    });

    score += tagScore;
    debugInfo.tagScore = tagScore;
  } else {
    console.log(`Missing tags for comparison: post1 has ${post1.tags?.length || 0}, post2 has ${post2.tags?.length || 0}`);
  }

  // Check for title similarity (simple word matching)
  const post1Words = post1.title.toLowerCase().split(/\s+/);
  const post2Words = post2.title.toLowerCase().split(/\s+/);

  // Count matching significant words (excluding common words)
  const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'as'];

  let titleScore = 0;
  post1Words.forEach(word => {
    if (word.length > 3 && !commonWords.includes(word) && post2Words.includes(word)) {
      titleScore += 1;
    }
  });

  score += titleScore;
  debugInfo.titleScore = titleScore;

  // Check for content similarity if available
  if (post1.content && post2.content) {
    // Extract keywords from content (simplified approach)
    const post1ContentWords = post1.content.toLowerCase().split(/\s+/)
      .filter(word => word.length > 4 && !commonWords.includes(word))
      .slice(0, 100); // Limit to first 100 significant words

    const post2ContentWords = post2.content.toLowerCase().split(/\s+/)
      .filter(word => word.length > 4 && !commonWords.includes(word))
      .slice(0, 100);

    // Count matching significant words
    let contentMatchCount = 0;
    post1ContentWords.forEach(word => {
      if (post2ContentWords.includes(word)) {
        contentMatchCount++;
      }
    });

    // Add score based on content similarity
    let contentScore = contentMatchCount * 0.2; // Lower weight for content matches
    score += contentScore;
    debugInfo.contentScore = contentScore;
  } else {
    console.log(`Missing content for comparison: post1 has content: ${!!post1.content}, post2 has content: ${!!post2.content}`);
  }

  // Check for author similarity
  let authorScore = 0;
  if (post1.author && post2.author && post1.author.id === post2.author.id) {
    authorScore = 1; // Same author gives a small boost
    score += authorScore;
    debugInfo.authorScore = authorScore;
  }

  // Check for recency (prefer more recent posts)
  const post1Date = new Date(post1.published_at).getTime(); // Changed from publish_date to published_at
  const post2Date = new Date(post2.published_at).getTime(); // Changed from publish_date to published_at
  const currentDate = new Date().getTime();

  // Calculate recency score (higher for newer posts)
  const daysSincePost2 = Math.floor((currentDate - post2Date) / (1000 * 60 * 60 * 24));
  let recencyScore = 0;
  if (daysSincePost2 < 30) { // Boost posts from last 30 days
    recencyScore = 1 - (daysSincePost2 / 30); // Gradually decreases from 1 to 0
    score += recencyScore;
    debugInfo.recencyScore = recencyScore;
  }

  // Log detailed score breakdown for debugging
  console.log(`Similarity score between "${post1.title}" and "${post2.title}": ${score}`, debugInfo);

  return score;
}

/**
 * Find related posts for a given post
 */
export function findRelatedPosts(
  currentPost: BlogPost,
  allPosts: BlogPost[],
  limit: number = 3
): BlogPost[] {
  console.log(`Finding related posts for: "${currentPost.title}" (ID: ${currentPost.id})`);
  console.log(`Total posts to compare: ${allPosts.length}`);

  // Validate input data
  if (!currentPost.categories) {
    console.warn("Current post has no categories");
  }

  if (!currentPost.tags) {
    console.warn("Current post has no tags");
  }

  // Calculate similarity scores for all posts
  const scoredPosts = allPosts
    .filter(post => post.id !== currentPost.id) // Exclude current post
    .map(post => {
      const score = calculateSimilarityScore(currentPost, post);
      return { post, score };
    })
    .sort((a, b) => b.score - a.score); // Sort by score (highest first)

  // Log top scores for debugging
  console.log("Top scoring posts:");
  scoredPosts.slice(0, Math.min(5, scoredPosts.length)).forEach((item, index) => {
    console.log(`${index + 1}. "${item.post.title}" - Score: ${item.score}`);
  });

  // Return top N posts
  return scoredPosts.slice(0, limit).map(item => item.post);
}
