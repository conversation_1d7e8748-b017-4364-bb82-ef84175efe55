{"kind": "singleType", "collectionName": "blog_homepages", "info": {"singularName": "blog-homepage", "pluralName": "blog-homepages", "displayName": "Blog Homepage", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"pageTitle": {"type": "string", "default": "Blog"}, "pageDescription": {"type": "richtext"}, "blog_posts": {"type": "relation", "relation": "oneToMany", "target": "api::blog-post.blog-post"}}}