(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8717],{4478:(e,r,s)=>{Promise.resolve().then(s.bind(s,9321))},9321:(e,r,s)=>{"use strict";s.d(r,{default:()=>c});var t=s(5155),l=s(2115),n=s(6500),o=s(3464);let a=async function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let{params:s,...t}=r,l="/api/strapi-proxy?endpoint=".concat(encodeURIComponent(e)),n=new URLSearchParams;return s&&Object.entries(s).forEach(e=>{let[r,s]=e;"object"==typeof s&&null!==s?n.append(r,JSON.stringify(s)):n.append(r,String(s))}),(await o.A.get(l,{...t,params:n})).data}catch(r){throw console.error("Error fetching from proxy (".concat(e,"):"),r),r.response&&(console.error("Response data:",r.response.data),console.error("Response status:",r.response.status)),r}};function c(){var e,r,s;let[o,c]=(0,l.useState)(null),[d,i]=(0,l.useState)(null),[u,p]=(0,l.useState)(!1),[h,x]=(0,l.useState)(null),[b,m]=(0,l.useState)(null),[g,f]=(0,l.useState)(!1),{data:j,isLoading:y,error:N,usingProxy:v}=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},[s,t]=(0,l.useState)(null),[o,c]=(0,l.useState)(!0),[d,i]=(0,l.useState)(null),[u,p]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let s=!0;return(async()=>{c(!0),i(null);try{let l=await (0,n.f)(e,r);s&&(t(l),c(!1))}catch(n){var l,o,d;if(console.log("Direct Strapi request failed, trying proxy...",n),(null==(l=n.message)?void 0:l.includes("CORS"))||(null==(o=n.message)?void 0:o.includes("Network Error"))||(null==(d=n.message)?void 0:d.includes("Failed to fetch")))try{p(!0);let l=await a(e,r);s&&(t(l),c(!1))}catch(e){console.error("Proxy request also failed:",e),s&&(i(e instanceof Error?e:Error(String(e))),c(!1))}else s&&(i(n instanceof Error?n:Error(String(n))),c(!1))}})(),()=>{s=!1}},[e,JSON.stringify(r)]),{data:s,isLoading:o,error:d,usingProxy:u}}("/clinics",{params:{filters:{isFeatured:{$eq:!0}},populate:"*"}}),S=async()=>{p(!0),i(null),c(null);try{let e=await fetch("".concat("https://nice-badge-2130241d6c.strapiapp.com","/api/clinics?filters[isFeatured][$eq]=true&populate=*"));if(!e.ok)throw Error("HTTP error! Status: ".concat(e.status));let r=await e.json();c(r)}catch(e){console.error("Direct fetch error:",e),i(e.message||"An error occurred")}finally{p(!1)}},w=async()=>{f(!0),m(null),x(null);try{let e=await fetch("/api/strapi-proxy?endpoint=/clinics&filters[isFeatured][$eq]=true&populate=*");if(!e.ok)throw Error("HTTP error! Status: ".concat(e.status));let r=await e.json();x(r)}catch(e){console.error("Proxy fetch error:",e),m(e.message||"An error occurred")}finally{f(!1)}};return(0,l.useEffect)(()=>{console.log("NEXT_PUBLIC_API_URL:","https://nice-badge-2130241d6c.strapiapp.com")},[]),(0,t.jsxs)("div",{className:"p-6 max-w-4xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"CORS Test Component"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"border p-4 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Direct Fetch Test"}),(0,t.jsx)("button",{onClick:S,disabled:u,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mb-4 disabled:opacity-50",children:u?"Loading...":"Test Direct Fetch"}),d&&(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:[(0,t.jsx)("p",{className:"font-bold",children:"Error:"}),(0,t.jsx)("p",{children:d})]}),o&&(0,t.jsxs)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:[(0,t.jsx)("p",{className:"font-bold",children:"Success!"}),(0,t.jsxs)("p",{children:["Found ",(null==(e=o.data)?void 0:e.length)||0," clinics"]})]})]}),(0,t.jsxs)("div",{className:"border p-4 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Proxy Fetch Test"}),(0,t.jsx)("button",{onClick:w,disabled:g,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mb-4 disabled:opacity-50",children:g?"Loading...":"Test Proxy Fetch"}),b&&(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:[(0,t.jsx)("p",{className:"font-bold",children:"Error:"}),(0,t.jsx)("p",{children:b})]}),h&&(0,t.jsxs)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:[(0,t.jsx)("p",{className:"font-bold",children:"Success!"}),(0,t.jsxs)("p",{children:["Found ",(null==(r=h.data)?void 0:r.length)||0," clinics"]})]})]}),(0,t.jsxs)("div",{className:"border p-4 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Fallback Hook Test"}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("p",{className:"font-semibold",children:"Status:"}),(0,t.jsx)("p",{children:y?"Loading...":v?"Using Proxy":"Direct Success"})]}),N&&(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:[(0,t.jsx)("p",{className:"font-bold",children:"Error:"}),(0,t.jsx)("p",{children:N.message})]}),j&&(0,t.jsxs)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded",children:[(0,t.jsx)("p",{className:"font-bold",children:"Success!"}),(0,t.jsxs)("p",{children:["Found ",(null==(s=j.data)?void 0:s.length)||0," clinics"]}),(0,t.jsxs)("p",{className:"text-sm mt-2",children:["Method: ",v?"Proxy":"Direct"]})]})]})]}),(0,t.jsxs)("div",{className:"mt-8 p-4 bg-gray-100 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment Info"}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"NEXT_PUBLIC_API_URL:"})," ","https://nice-badge-2130241d6c.strapiapp.com"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Browser Origin:"})," ",window.location.origin]})]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[3464,6052,5825,8441,1684,7358],()=>r(4478)),_N_E=e.O()}]);