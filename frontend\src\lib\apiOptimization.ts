import { AxiosRequestConfig } from 'axios';
import { enhancedFetchFromApi } from './enhancedApiUtils';

/**
 * Cache storage for API responses
 * Key: Cache key (endpoint + serialized params)
 * Value: { data, timestamp, ttl }
 */
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

const apiCache = new Map<string, CacheEntry<any>>();

/**
 * Generate a cache key from endpoint and params
 */
export function generateCacheKey(endpoint: string, params?: any): string {
  return `${endpoint}:${JSON.stringify(params || {})}`;
}

/**
 * Check if a cache entry is still valid
 */
export function isCacheValid<T>(cacheEntry: CacheEntry<T> | undefined): boolean {
  if (!cacheEntry) return false;
  
  const now = Date.now();
  const expiryTime = cacheEntry.timestamp + cacheEntry.ttl;
  
  return now < expiryTime;
}

/**
 * Fetch data from API with caching
 * 
 * @param endpoint API endpoint
 * @param options Axios request options
 * @param ttl Cache TTL in milliseconds (default: 5 minutes)
 * @param bypassCache Whether to bypass the cache and force a fresh fetch
 * @returns Promise with the API response
 */
export async function fetchWithCache<T>(
  endpoint: string,
  options: AxiosRequestConfig = {},
  ttl: number = 5 * 60 * 1000, // 5 minutes default
  bypassCache: boolean = false
): Promise<T> {
  const cacheKey = generateCacheKey(endpoint, options.params);
  const cachedData = apiCache.get(cacheKey);
  
  // Return cached data if valid and not bypassing cache
  if (!bypassCache && isCacheValid(cachedData)) {
    return cachedData.data;
  }
  
  // Fetch fresh data
  const data = await enhancedFetchFromApi<T>(endpoint, options, typeof window === 'undefined');
  
  // Cache the response
  apiCache.set(cacheKey, {
    data,
    timestamp: Date.now(),
    ttl,
  });
  
  return data;
}

/**
 * Clear a specific cache entry
 */
export function clearCacheEntry(endpoint: string, params?: any): void {
  const cacheKey = generateCacheKey(endpoint, params);
  apiCache.delete(cacheKey);
}

/**
 * Clear all cache entries
 */
export function clearCache(): void {
  apiCache.clear();
}

/**
 * Prefetch data and store in cache
 * Useful for preloading data that will be needed soon
 */
export async function prefetchData<T>(
  endpoint: string,
  options: AxiosRequestConfig = {},
  ttl: number = 5 * 60 * 1000
): Promise<void> {
  try {
    await fetchWithCache<T>(endpoint, options, ttl, true);
  } catch (error) {
    console.error(`Error prefetching data from ${endpoint}:`, error);
    // Silently fail on prefetch errors
  }
}

/**
 * Batch multiple API requests into a single request
 * This reduces the number of network requests
 * 
 * @param requests Array of request objects with endpoint and options
 * @returns Promise with an array of responses
 */
export async function batchRequests<T>(
  requests: Array<{ endpoint: string; options?: AxiosRequestConfig }>
): Promise<T[]> {
  return Promise.all(
    requests.map(({ endpoint, options = {} }) => 
      enhancedFetchFromApi<T>(endpoint, options, typeof window === 'undefined')
    )
  );
}

/**
 * Create optimized params for pagination
 * This ensures we only request the data we need
 */
export function createPaginationParams(
  page: number = 1,
  pageSize: number = 10,
  fields: string[] = []
): Record<string, any> {
  const params: Record<string, any> = {
    pagination: {
      page,
      pageSize,
    },
  };
  
  // Add fields to select if provided
  if (fields.length > 0) {
    params.fields = fields;
  }
  
  return params;
}

/**
 * Create optimized params for filtering
 */
export function createFilterParams(
  filters: Record<string, any> = {},
  sort: string | string[] = [],
  fields: string[] = []
): Record<string, any> {
  const params: Record<string, any> = {};
  
  // Add filters if provided
  if (Object.keys(filters).length > 0) {
    params.filters = filters;
  }
  
  // Add sort if provided
  if (sort.length > 0) {
    params.sort = Array.isArray(sort) ? sort : [sort];
  }
  
  // Add fields to select if provided
  if (fields.length > 0) {
    params.fields = fields;
  }
  
  return params;
}
