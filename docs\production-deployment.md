# Production Deployment Guide for Natural Healing Now

This guide outlines the steps to deploy the Natural Healing Now website to production.

## Prerequisites

- Access to Strapi Cloud account
- Access to Vercel account
- Access to domain registrar (if applicable)
- All environment variables documented and ready

## Step 1: Fix Known Issues

Before deploying to production, fix the known issue with the clinic appointment options:

```bash
# Navigate to the project root
cd /path/to/naturalhealingnow

# Run the fix script for production
NODE_ENV=production node scripts/fix-clinic-appointment-options.js
```

## Step 2: Deploy Strapi to Production

### Option 1: Strapi Cloud (Recommended)

1. Log in to your Strapi Cloud account
2. Create a new project or select your existing project
3. Connect your GitHub repository
4. Configure environment variables:
   - Database connection details
   - API tokens
   - Admin credentials
   - Any other required environment variables
5. Deploy the Strapi application
6. Verify the admin panel is accessible

### Option 2: Self-Hosted (Railway, Hetzner, or DigitalOcean)

1. Set up a new server or container
2. Clone the repository
3. Install dependencies:
   ```bash
   cd strapi-cms
   npm install
   ```
4. Configure environment variables
5. Build the Strapi application:
   ```bash
   npm run build
   ```
6. Start the Strapi application:
   ```bash
   npm run start
   ```
7. Set up a reverse proxy (Nginx or similar) to serve the Strapi application
8. Configure SSL certificates

## Step 3: Migrate Content to Production

### Option 1: Using Strapi Transfer

1. Export data from development:
   ```bash
   cd strapi-cms
   npm run strapi transfer:export
   ```
2. Import data to production:
   ```bash
   npm run strapi transfer:import
   ```

### Option 2: Manual Migration

1. Export content types from development:
   ```bash
   cd strapi-cms
   npm run strapi export
   ```
2. Import content types to production:
   ```bash
   npm run strapi import
   ```
3. Upload media files to production

## Step 4: Deploy Next.js Frontend to Vercel

1. Log in to your Vercel account
2. Import your GitHub repository
3. Configure environment variables:
   - `NEXT_PUBLIC_STRAPI_API_URL`: URL of your Strapi API
   - `NEXT_PUBLIC_SITE_URL`: URL of your frontend site
   - Any other required environment variables
4. Deploy the application
5. Verify the deployment is successful

## Step 5: Configure Domain and DNS

1. Add your custom domain in Vercel
2. Update DNS records at your domain registrar
3. Configure SSL certificates (automatic with Vercel)

## Step 6: Verify Deployment

Follow the deployment verification plan to ensure everything is working correctly:

1. Check all pages load correctly
2. Verify content is displayed properly
3. Test search functionality
4. Test navigation
5. Check mobile responsiveness
6. Run performance tests

## Step 7: Post-Deployment Tasks

1. Set up monitoring
2. Configure analytics
3. Set up regular backups
4. Document the production environment

## Troubleshooting

### Common Issues

#### Strapi Admin Error with Appointment Options

If you encounter an error in the Strapi admin panel related to appointment options, run the fix script:

```bash
NODE_ENV=production node scripts/fix-clinic-appointment-options.js
```

#### Missing Content After Migration

If content is missing after migration:

1. Check that all content types were properly exported
2. Verify that relations between content types are preserved
3. Check that media files were properly uploaded

#### Frontend API Connection Issues

If the frontend cannot connect to the Strapi API:

1. Verify the `NEXT_PUBLIC_STRAPI_API_URL` environment variable is set correctly
2. Check CORS settings in Strapi
3. Verify API tokens are configured correctly

## Rollback Procedure

If you need to rollback the deployment:

1. Revert to the previous Vercel deployment
2. Restore the Strapi database from backup
3. Verify the rollback was successful

## Contact

For assistance with deployment issues, contact:

- Technical Support: [<EMAIL>](mailto:<EMAIL>)
