import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath, revalidateTag } from 'next/cache';

// Secret token to validate webhook requests
const REVALIDATION_TOKEN = process.env.REVALIDATION_TOKEN;

export async function POST(request: NextRequest) {
  try {
    // Verify the request has the correct token
    const secret = request.nextUrl.searchParams.get('secret');
    if (secret !== REVALIDATION_TOKEN) {
      return NextResponse.json(
        { message: 'Invalid revalidation token' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    
    // Log the webhook payload for debugging
    console.log('Received specialities revalidation webhook:', JSON.stringify({
      event: body.event,
      model: body.model,
      entry: body.entry ? { id: body.entry.id, slug: body.entry.slug } : null
    }));

    // Revalidate the specialities list page
    revalidatePath('/specialities');
    
    // Revalidate the specialities tag to update any pages using this data
    revalidateTag('strapi-specialties');
    revalidateTag('strapi-specialties-all');
    
    // If we have a specific specialty entry, revalidate its detail page
    if (body.entry && body.entry.slug) {
      revalidatePath(`/specialities/${body.entry.slug}`);
      revalidateTag(`strapi-specialty-${body.entry.slug}`);
    }

    return NextResponse.json(
      { 
        revalidated: true, 
        message: 'Specialities revalidated successfully',
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error revalidating specialities:', error);
    return NextResponse.json(
      { 
        revalidated: false, 
        message: 'Error revalidating specialities',
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
