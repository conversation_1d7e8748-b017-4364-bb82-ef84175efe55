const request = require('supertest');
const strapiHelpers = require('../helpers/strapi');

describe('API Endpoints', () => {
  let jwt;
  let clinic;

  // Before all tests, create a user and get JWT token
  beforeAll(async () => {
    jwt = await strapiHelpers.createUserAndLogin();
  });

  // After all tests, clean up
  afterAll(async () => {
    await strapiHelpers.cleanupCollection('api::clinic.clinic');
  });

  describe('Clinic API', () => {
    it('should create a clinic', async () => {
      const response = await request(strapi.server.httpServer)
        .post('/api/clinics')
        .set('accept', 'application/json')
        .set('Content-Type', 'application/json')
        .set('Authorization', `Bearer ${jwt}`)
        .send({
          data: {
            name: 'Test Clinic',
            slug: 'test-clinic',
            description: 'This is a test clinic',
          },
        });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.attributes.name).toBe('Test Clinic');
      
      // Save clinic for later tests
      clinic = response.body.data;
    });

    it('should get a list of clinics', async () => {
      const response = await request(strapi.server.httpServer)
        .get('/api/clinics')
        .set('accept', 'application/json');

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should get a specific clinic by ID', async () => {
      if (!clinic) {
        throw new Error('Clinic not created in previous test');
      }

      const response = await request(strapi.server.httpServer)
        .get(`/api/clinics/${clinic.id}`)
        .set('accept', 'application/json');

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(clinic.id);
      expect(response.body.data.attributes.name).toBe('Test Clinic');
    });

    it('should update a clinic', async () => {
      if (!clinic) {
        throw new Error('Clinic not created in previous test');
      }

      const response = await request(strapi.server.httpServer)
        .put(`/api/clinics/${clinic.id}`)
        .set('accept', 'application/json')
        .set('Content-Type', 'application/json')
        .set('Authorization', `Bearer ${jwt}`)
        .send({
          data: {
            description: 'Updated description',
          },
        });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.attributes.description).toBe('Updated description');
    });

    it('should delete a clinic', async () => {
      if (!clinic) {
        throw new Error('Clinic not created in previous test');
      }

      const response = await request(strapi.server.httpServer)
        .delete(`/api/clinics/${clinic.id}`)
        .set('accept', 'application/json')
        .set('Authorization', `Bearer ${jwt}`);

      expect(response.status).toBe(200);
    });
  });
});
