{"kind": "collectionType", "collectionName": "blog_categories", "info": {"singularName": "blog-category", "pluralName": "blog-categories", "displayName": "Blog Category"}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "richtext"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "blog_posts": {"type": "relation", "relation": "manyToMany", "target": "api::blog-post.blog-post", "mappedBy": "blog_categories"}}}