/**
 * Test script for blog-related revalidation
 * 
 * This script tests the revalidation API for blog posts, categories, tags, and authors
 */

const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env.local
const envPath = path.resolve(process.cwd(), 'frontend', '.env.local');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.warn('No .env.local file found in frontend directory');
  dotenv.config();
}

// Get site URL and revalidation token from environment variables
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';
const REVALIDATE_TOKEN = process.env.REVALIDATE_TOKEN || process.env.PREVIEW_SECRET;

if (!REVALIDATE_TOKEN) {
  console.error('No revalidation token found in environment variables');
  process.exit(1);
}

console.log(`Using site URL: ${SITE_URL}`);
console.log(`Revalidation API URL: ${SITE_URL}/api/revalidate`);
console.log(`Revalidation token: ${REVALIDATE_TOKEN.substring(0, 5)}...`);

// Function to run a test
async function runTest(name, payload, headers = {}) {
  console.log(`\n🧪 Running test: ${name}`);
  
  const url = `${SITE_URL}/api/revalidate`;
  
  // Add content type header if not provided
  if (!headers['Content-Type']) {
    headers['Content-Type'] = 'application/json';
  }
  
  console.log(`URL: ${url}`);
  console.log(`Headers: ${JSON.stringify(headers, null, 2)}`);
  console.log(`Payload: ${JSON.stringify(payload, null, 2)}`);
  
  try {
    const response = await axios.post(url, payload, { headers });
    
    if (response.status === 200) {
      console.log(`✅ Test passed!`);
      console.log(`Status: ${response.status}`);
      console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
      return true;
    } else {
      console.log(`❌ Test failed!`);
      console.log(`Status: ${response.status}`);
      console.log(`Response: ${JSON.stringify(response.data, null, 2)}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Test failed!`);
    console.log(`Error: ${error.message}`);
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return false;
  }
}

// Main function to run all tests
async function runTests() {
  console.log(`🧪 Testing blog-related revalidation API...`);
  
  let passedTests = 0;
  let totalTests = 0;
  
  // Test 1: Revalidate blog post
  totalTests++;
  if (await runTest(
    'Revalidate blog post',
    {
      token: REVALIDATE_TOKEN,
      contentType: 'blog-post',
      id: 123,
      slug: 'test-blog-post'
    }
  )) {
    passedTests++;
  }
  
  // Test 2: Revalidate blog category
  totalTests++;
  if (await runTest(
    'Revalidate blog category',
    {
      token: REVALIDATE_TOKEN,
      contentType: 'blog-category',
      id: 456,
      slug: 'test-category'
    }
  )) {
    passedTests++;
  }
  
  // Test 3: Revalidate blog tag
  totalTests++;
  if (await runTest(
    'Revalidate blog tag',
    {
      token: REVALIDATE_TOKEN,
      contentType: 'blog-tag',
      id: 789,
      slug: 'test-tag'
    }
  )) {
    passedTests++;
  }
  
  // Test 4: Revalidate blog author
  totalTests++;
  if (await runTest(
    'Revalidate blog author',
    {
      token: REVALIDATE_TOKEN,
      contentType: 'author-blog',
      id: 101,
      slug: 'test-author'
    }
  )) {
    passedTests++;
  }
  
  // Test 5: Strapi webhook payload format (blog post)
  totalTests++;
  if (await runTest(
    'Strapi webhook payload format (blog post)',
    {
      event: 'entry.update',
      model: 'api::blog-post.blog-post',
      entry: {
        id: 123,
        slug: 'test-blog-post'
      }
    },
    {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${REVALIDATE_TOKEN}`
    }
  )) {
    passedTests++;
  }
  
  // Test 6: Strapi webhook payload format (blog category)
  totalTests++;
  if (await runTest(
    'Strapi webhook payload format (blog category)',
    {
      event: 'entry.update',
      model: 'api::blog-category.blog-category',
      entry: {
        id: 456,
        slug: 'test-category'
      }
    },
    {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${REVALIDATE_TOKEN}`
    }
  )) {
    passedTests++;
  }
  
  // Print test results
  console.log(`\n🧪 Test results: ${passedTests}/${totalTests} tests passed`);
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});
