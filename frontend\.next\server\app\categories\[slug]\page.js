(()=>{var e={};e.id=5093,e.ids=[5093],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12045:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\blog\\\\MarkdownContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\blog\\MarkdownContent.tsx","default")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50203:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N,dynamic:()=>m,dynamicParams:()=>h,generateMetadata:()=>w,generateStaticParams:()=>f,revalidate:()=>x});var a=r(37413),s=r(4536),i=r.n(s),n=r(73993),o=r(58446),l=r(39916),c=r(14007),d=r(10592),u=r(12045),p=r(74124),g=r(69290);let m="force-static",x=43200,h=!0;async function f(){try{let e=await o.$.categories.getAllSlugs({cache:"force-cache",next:{revalidate:43200,tags:["strapi-categories-slugs"]}});if(e&&e.data&&Array.isArray(e.data))return console.log(`Pre-rendering ${e.data.length} category detail pages`),e.data.filter(e=>null!==e&&"string"==typeof e.slug).map(e=>({slug:e.slug}));return[]}catch(e){return console.error("Error fetching category slugs for generateStaticParams:",e),[]}}let b="https://nice-badge-2130241d6c.strapiapp.com",y=e=>{if(!e)return null;if("string"==typeof e)return e.startsWith("http://")||e.startsWith("https://")?e:b?`${b}${e}`:null;if(e&&"object"==typeof e){let t=e.data?.attributes?.url;if(t||(t=e.url),t||(t=e.data?.url),"string"==typeof t)return t.startsWith("http://")||t.startsWith("https://")?t:`${b}${t}`}return console.warn("Could not extract URL from media object:",e),null};async function v(e){try{let t=await o.$.categories.getBySlug(e,{next:{tags:["strapi-categories-list",`strapi-category-${e}`]},cache:"force-cache"});if(t?.data&&Array.isArray(t.data)&&t.data.length>0)return t.data[0];return null}catch(t){return console.error(`Error fetching category with slug ${e}:`,t),null}}let j=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";async function w({params:e}){let t=await e,r=await v(t.slug);if(!r)return{title:"Category Not Found | Natural Healing Now",description:"The requested category could not be found."};let a=r.seo,s=`${r.name||"Category"} | Natural Healing Now`,i=r.description||`Learn about ${r.name||"this category"} and find related clinics and practitioners.`,n=a?.metaTitle||s,o=a?.metaDescription||i,l=y(a?.metaImage),c=`/categories/${r.slug}`,d=j?`${j}${c}`:c,u=a?.openGraph?.ogTitle||n,p=a?.openGraph?.ogDescription||o,g=y(a?.openGraph?.ogImage)||l,m=a?.openGraph?.ogUrl||d,x=a?.openGraph?.ogType||"website";return{title:n,description:o,alternates:{canonical:d},openGraph:{title:u,description:p,url:m,type:["website","article","book","profile"].includes(x)?x:"website",siteName:"Natural Healing Now",images:g?[{url:g}]:void 0}}}async function N({params:e,searchParams:t}){let r=await e,s=r.slug,m=await t,x=m?.query||"",h=m?.location||"",f=Number(m?.page)||1,b="practitioners"===(m?.tab||"clinics")?"practitioners":"clinics",j=await v(s);j||(0,l.notFound)();let{name:w="Unnamed Category",description:N="No description available.",icon:P=null,featuredImage:C=null,relatedConditions:q=null,contentBottomCategory:$}=j,A=y(P),k=y(C),I=0,_=0,E=1,T=x||h,S=T?{cache:"no-store"}:{next:{tags:["strapi-categories-list",`strapi-category-${s}`],revalidate:43200},cache:"force-cache"};try{let e=await o.$.clinics.getAll({filters:{categories:{slug:{$eq:s}}},query:T?x:"",location:T?h:"",page:1,pageSize:1,...S});I=e?.meta?.pagination?.total||0;let t=await o.$.practitioners.getAll({filters:{categories:{slug:{$eq:s}}},query:T?x:"",location:T?h:"",page:1,pageSize:1,...S});_=t?.meta?.pagination?.total||0}catch(e){console.error("Error fetching counts for category page:",e)}let z=[],G=1;try{let e=await o.$.clinics.getAll({filters:{categories:{slug:{$eq:s}}},query:"clinics"===b?x:"",location:"clinics"===b?h:"",page:"clinics"===b?f:1,pageSize:12,...S});z=(e?.data||[]).map(e=>(function(e){if(!e||!e.id||!e.name)return null;let t=y(e.logo),r=y(e.featuredImage);return{id:String(e.id),name:e.name,slug:e.slug||`clinic-${e.id}`,description:e.description,logo:t,featuredImage:r,address:e.address||{city:"Unknown",stateProvince:"N/A"},contactInfo:e.contactInfo,isVerified:e.isVerified||!1}})(e.attributes||e)).filter(Boolean),G=e?.meta?.pagination?.pageCount||1}catch(e){console.error(`Error fetching clinics for category ${s}:`,e)}let M=[],R=1;try{let e=await o.$.practitioners.getAll({filters:{categories:{slug:{$eq:s}}},query:"practitioners"===b?x:"",location:"practitioners"===b?h:"",page:"practitioners"===b?f:1,pageSize:12,...S});M=(e?.data||[]).map(e=>{var t;return(t=e.attributes||e)&&t.id&&t.name?{id:String(t.id),name:t.name,slug:t.slug||`practitioner-${t.id}`,title:t.title,qualifications:t.qualifications,profilePicture:y(t.profilePicture),isVerified:t.isVerified||!1,bio:t.bio}:null}).filter(e=>null!==e),R=e?.meta?.pagination?.pageCount||1}catch(e){console.error(`Error fetching practitioners for category ${s}:`,e)}E="clinics"===b?G:R;let U=q?[...q]:[];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-gray-100 py-3",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(i(),{href:"/",className:"hover:text-emerald-600",children:"Home"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)(i(),{href:"/categories",className:"hover:text-emerald-600",children:"Categories"}),(0,a.jsx)("span",{className:"mx-2",children:"/"}),(0,a.jsx)("span",{className:"text-gray-800",children:w})]})})}),(0,a.jsxs)("div",{className:"bg-emerald-600 text-white py-12 relative overflow-hidden",children:[k&&(0,a.jsx)("div",{className:"absolute inset-0 opacity-20 bg-cover bg-center",style:{backgroundImage:`url(${k})`}}),(0,a.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[A&&(0,a.jsx)("div",{className:"relative h-12 w-12 mr-4 bg-white rounded-full p-1 flex-shrink-0",children:(0,a.jsx)("div",{className:"absolute inset-0 rounded-full bg-contain bg-center bg-no-repeat",style:{backgroundImage:`url(${A})`}})}),(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold",children:w})]}),N&&(0,a.jsx)("p",{className:"text-lg max-w-3xl mb-4",children:N}),(0,a.jsxs)("div",{className:"flex gap-4 text-sm",children:[(0,a.jsxs)("span",{children:[I," Clinics"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[_," Practitioners"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white shadow-sm sticky top-0 z-20",children:[" ",(0,a.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(d.default,{placeholder:`Search within ${w}...`,paramName:"query"})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(d.default,{placeholder:"City, state, or zip code",paramName:"location",icon:(0,a.jsx)(n.HzC,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})})}),(0,a.jsx)("div",{children:(0,a.jsxs)("button",{className:"w-full md:w-auto flex items-center justify-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-lg hover:bg-emerald-200",children:[(0,a.jsx)(n.K7R,{}),(0,a.jsx)("span",{children:"Filters"})]})})]})})]}),(0,a.jsx)("div",{className:"py-8 bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)(p.default,{slug:r.slug,pageType:"categories",clinicCount:I,practitionerCount:_,initialTab:b}),(0,a.jsx)(g.default,{clinics:z,practitioners:M,totalPages:E,initialTab:b})]})}),U.length>0&&(0,a.jsx)("div",{className:"py-12 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:["Common Conditions Addressed by ",w]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:U.map((e,t)=>(0,a.jsx)("span",{className:"bg-emerald-50 text-emerald-700 px-3 py-1 rounded-full text-sm",children:e},t))})]})}),$&&(0,a.jsxs)("div",{className:"py-12 bg-white border-t border-gray-200 flex justify-center",children:[" ",(0,a.jsxs)("div",{className:"w-full max-w-4xl px-4 prose lg:prose-lg",children:[" ",(0,a.jsx)(u.default,{content:$})]})]}),(0,a.jsx)(c.A,{})]})}},52163:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,90612)),Promise.resolve().then(r.bind(r,68016)),Promise.resolve().then(r.bind(r,2699)),Promise.resolve().then(r.bind(r,51006))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59074:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>l});var a=r(65239),s=r(48088),i=r(31369),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let l={children:["",{children:["categories",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,50203)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\categories\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\categories\\[slug]\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/categories/[slug]/page",pathname:"/categories/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90612:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(60687),s=r(84189),i=r(3832),n=r(43210),o=r(66501);async function l(e,t){try{let r=await fetch("/api/analytics/post-view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({postId:e,postSlug:t})});if(!r.ok)throw Error(`Failed to track post view: ${r.statusText}`)}catch(e){o.Ay.error("Error tracking post view:",e)}}let c=({content:e,postId:t,postSlug:r,applyNoFollow:o=!0})=>((0,n.useEffect)(()=>{if(t&&r){let e=setTimeout(()=>{l(t,r)},2e3);return()=>clearTimeout(e)}},[t,r]),(0,a.jsxs)("div",{className:"mb-8",children:[" ",(0,a.jsx)(s.oz,{components:{h1:({node:e,...t})=>(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mt-8 mb-4",...t}),h2:({node:e,...t})=>(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mt-6 mb-3",...t}),h3:({node:e,...t})=>(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800 mt-5 mb-2",...t}),h4:({node:e,...t})=>(0,a.jsx)("h4",{className:"text-lg font-bold text-gray-800 mt-4 mb-2",...t}),p:({node:e,...t})=>(0,a.jsx)("p",{className:"text-gray-700 mb-4",...t}),a:({node:e,href:t,...r})=>{let s=t&&(t.startsWith("http://")||t.startsWith("https://")),i="";return s&&(o&&(i+="nofollow "),i+="noopener noreferrer"),(0,a.jsx)("a",{className:"text-emerald-600 hover:text-emerald-700 underline",href:t,rel:i.trim()||void 0,target:s?"_blank":void 0,...r})},ul:({node:e,...t})=>(0,a.jsx)("ul",{className:"list-disc pl-6 mb-4",...t}),ol:({node:e,...t})=>(0,a.jsx)("ol",{className:"list-decimal pl-6 mb-4",...t}),li:({node:e,...t})=>(0,a.jsx)("li",{className:"mb-1",...t}),blockquote:({node:e,...t})=>(0,a.jsx)("blockquote",{className:"border-l-4 border-emerald-500 pl-4 italic my-4",...t})},rehypePlugins:[i.A],children:e})]}))},92491:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,12045)),Promise.resolve().then(r.bind(r,10592)),Promise.resolve().then(r.bind(r,69290)),Promise.resolve().then(r.bind(r,74124))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,1330,3376,6391,2975,255,5373,8446,270,7424],()=>r(59074));module.exports=a})();