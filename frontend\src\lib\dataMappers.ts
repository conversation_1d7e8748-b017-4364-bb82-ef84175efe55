/**
 * Utility functions for mapping data from Strapi to the format expected by components
 */

// Define blog post type
export type BlogPost = {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  featured_image?: string | null;
  publish_date: string;
  content?: string;
  reading_time?: number;
  isFeatured?: boolean;
  view_count?: number;
  author?: {
    name: string;
    slug: string;
    profile_picture?: string | null;
  } | null;
};

/**
 * Maps a blog post from Strapi API format to the format expected by components
 */
export const mapBlogPostData = (post: any): BlogPost => {
  if (!post) return null as any;

  const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || '';

  // Extract author data if available
  const authorData = post.author_blogs?.[0] || null;

  // Helper function to safely get the profile picture URL
  const getProfilePictureUrl = (authorData: any) => {
    // Check for different possible structures of profilePicture data
    if (!authorData) return null;

    // Helper function to process URL
    const processImageUrl = (imageUrl: string) => {
      // Check if this is a Strapi Cloud URL (contains media.strapiapp.com)
      if (imageUrl.includes('media.strapiapp.com')) {
        // Ensure it has https:// prefix
        return imageUrl.startsWith('http') ? imageUrl : `https://${imageUrl}`;
      } else if (imageUrl.startsWith('http')) {
        // Already an absolute URL
        return imageUrl;
      } else {
        // Relative URL, prepend the Strapi API URL
        return `${strapiBaseUrl}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`;
      }
    };

    let profilePicUrl = null;

    // Case 1: Direct URL in profilePicture.url
    if (authorData.profilePicture?.url) {
      profilePicUrl = processImageUrl(authorData.profilePicture.url);
    }
    // Case 2: Nested data structure with data.attributes
    else if (authorData.profilePicture?.data?.attributes?.url) {
      profilePicUrl = processImageUrl(authorData.profilePicture.data.attributes.url);
    }
    // Case 3: Direct formats for responsive images
    else if (authorData.profilePicture?.formats?.thumbnail?.url) {
      profilePicUrl = processImageUrl(authorData.profilePicture.formats.thumbnail.url);
    }
    // Case 4: Nested formats
    else if (authorData.profilePicture?.data?.attributes?.formats?.thumbnail?.url) {
      profilePicUrl = processImageUrl(authorData.profilePicture.data.attributes.formats.thumbnail.url);
    }

    return profilePicUrl;
  };

  // Get the profile picture URL using the helper function
  const profilePictureUrl = getProfilePictureUrl(authorData);

  const author = authorData ? {
    name: authorData.name || 'Unknown Author',
    slug: authorData.slug || '',
    profile_picture: profilePictureUrl
  } : null;

  // Extract featured image URL
  let featuredImageUrl = null;
  if (post.featuredImage?.url) {
    const imageUrl = post.featuredImage.url;

    // Check if this is a Strapi Cloud URL (contains media.strapiapp.com)
    if (imageUrl.includes('media.strapiapp.com')) {
      // Ensure it has https:// prefix
      featuredImageUrl = imageUrl.startsWith('http') ? imageUrl : `https://${imageUrl}`;
    } else if (imageUrl.startsWith('http')) {
      // Already an absolute URL
      featuredImageUrl = imageUrl;
    } else {
      // Relative URL, prepend the Strapi API URL
      featuredImageUrl = `${strapiBaseUrl}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`;
    }
  }

  // Handle documentId as the primary ID in Strapi v5
  const id = post.id || post.documentId || '';

  // Create the blog post object
  const blogPost = {
    id,
    title: post.title || 'Untitled Post',
    slug: post.slug || `post-${id}`,
    excerpt: post.excerpt || null,
    featured_image: featuredImageUrl,
    publish_date: post.publishDate || post.createdAt || new Date().toISOString(),
    content: post.content || '',
    reading_time: post.readingTime || 2,
    isFeatured: post.isFeatured || false,
    view_count: post.view_count || 0,
    author
  };

  return blogPost;
};
