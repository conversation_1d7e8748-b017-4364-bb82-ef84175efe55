(()=>{var e={};e.id=5425,e.ids=[5425],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1199:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,dynamicParams:()=>s,generateMetadata:()=>u,generateStaticParams:()=>d,revalidate:()=>l});var n=r(37413),o=r(58446),a=r(79854),i=r(39916);let s=!0,l=86400;function c(e){let t=e.split("-");if(t.length<2)return null;let r=t[t.length-1].toUpperCase(),n=t.slice(0,-1).join(" ");return 2===r.length&&/^[A-Z]+$/.test(r)||console.warn(`Parsed state "${r}" from slug "${e}" does not look like a valid state code.`),{city:n.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),state:r}}async function d(){let e=new Set;console.log("Starting generateStaticParams for category/city pages...");try{let t=await o.$.categories.getAll({pagination:{pageSize:1e3},fields:["slug"],populate:{}}),r=t?.data||[];for(let t of(console.log(`Fetched ${r.length} categories.`),r)){let r=t.slug;if(!r)continue;let n=await o.$.clinics.getAll({filters:{categories:{slug:{$eq:r}}},fields:[],populate:{address:{fields:["city","stateProvince"]}},pagination:{pageSize:1e4}});for(let t of n?.data||[]){let n=t.attributes?.address,o=n?.city,a=n?.stateProvince;if(o&&a){let t=function(e,t){if(!e||!t)return"";let r=e.toLowerCase().replace(/\s+/g,"-"),n=t.toLowerCase();return`${r}-${n}`}(o,a);t&&e.add(`${r}/${t}`)}}}let n=Array.from(e).map(e=>{let[t,r]=e.split("/");return{slug:t,cityStateSlug:r}});return console.log(`Generated ${n.length} unique static params for category/city pages.`),n}catch(e){return console.error("Error during generateStaticParams for category/city pages:",e),[]}}async function u({params:e}){let{slug:t,cityStateSlug:r}=e,n=c(r),a=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com";if(console.log(`Using site URL for canonical URLs: ${a}`),!n)return{title:"Invalid Location",description:"The location specified in the URL is invalid."};let{city:i,state:s}=n,l=await o.$.categories.getBySlug(t),d=l?.data?.[0],u=d?.name,f=`/categories/${t}/${r}`,p=a?`${a}${f}`:f;return u?{title:`Best ${u} in ${i}, ${s} - Find Top Clinics | Natural Healing Now`,description:`Looking for the best ${u} in ${i}, ${s}? Discover top-rated clinics and practitioners near you. Book appointments easily on Natural Healing Now.`,alternates:{canonical:p},other:{"cache-tags":["strapi-clinics","strapi-categories",`strapi-category-${t}`,`strapi-category-${t}-clinics`,`strapi-category-${t}-location-${r}`]}}:(console.warn(`Category name not found for slug: ${t}. Returning not found metadata.`),{title:"Category Not Found | Natural Healing Now",description:`The requested category page for "${t}" in ${i}, ${s} could not be found.`})}async function f({params:e}){let{slug:t,cityStateSlug:r}=e,s=c(r);s||(console.error(`Failed to parse cityStateSlug: ${r}`),(0,i.notFound)());let{city:l,state:d}=s;console.log(`Fetching category details for slug: ${t}`);let u=await o.$.categories.getBySlug(t);console.log("Category Response from Strapi:",JSON.stringify(u,null,2));let f=u?.data?.[0];f&&void 0!==f.name||(console.error(`Category not found or missing name for slug: ${t}`),(0,i.notFound)());let p=f.name,g=[];try{console.log(`Fetching clinics for category ${t} in ${l}, ${d}`);let e=["strapi-clinics","strapi-categories",`strapi-category-${t}`,`strapi-category-${t}-clinics`,`strapi-category-${t}-location-${r}`],n=await o.$.clinics.getAll({filters:{$and:[{categories:{slug:{$eq:t}}},{address:{city:{$eq:l},stateProvince:{$eq:d}}}]},populate:{logo:!0,address:!0,contactInfo:!0,categories:!0},pagination:{pageSize:24},next:{tags:e}});g=n?.data||[],console.log(`Found ${g.length} clinics for ${p} in ${l}, ${d}`);let a=n?.meta?.pagination,i=a?.total||0,s=a?.pageCount||1,c=a?.page||1;console.log(`Pagination info: ${i} total items, page ${c} of ${s}`)}catch(e){console.error(`Error fetching clinics for category ${t} in ${l}, ${d}:`,e)}return(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,n.jsxs)("h1",{className:"text-3xl font-bold mb-2",children:[p," in ",l,", ",d]}),(0,n.jsxs)("p",{className:"text-lg text-gray-600 mb-6",children:["Discover the best ",p," in ",l,", ",d]}),g.length>0?(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(e=>{let t={id:e.id.toString(),name:e.name,slug:e.slug,isVerified:e.isVerified,description:e.description,address:{city:e.address?.city??"N/A",stateProvince:e.address?.stateProvince??"N/A"},logo:e.logo?.data?.attributes?.url||null,contactInfo:e.contactInfo?{phoneNumber:e.contactInfo.phoneNumber,websiteUrl:e.contactInfo.websiteUrl}:null};return t.address||(t.address={city:"N/A",stateProvince:"N/A"}),(0,n.jsx)(a.default,{clinic:t},t.id)})}):(0,n.jsxs)("p",{className:"text-center text-gray-500 mt-10",children:["No clinics found for ",p," in ",l,", ",d," at this time."]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26800:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(60687),o=r(85814),a=r.n(o),i=r(17019),s=r(20255);let l=({clinic:e,showContactInfo:t=!0,prefetchedData:r=!1})=>{let o=r?{pathname:`/clinics/${e.slug}`,query:{prefetched:"true"}}:`/clinics/${e.slug}`;return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 flex-grow",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,n.jsx)(a(),{href:o,className:"hover:text-emerald-600",children:e.name})}),e.isVerified&&(0,n.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,n.jsx)(s.AI8,{color:"#009967",size:14}),(0,n.jsx)("span",{children:"VERIFIED"})]}),e.description&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.description}),(0,n.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[e.address&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.HzC,{className:"mr-2 text-emerald-500"}),(0,n.jsxs)("span",{children:[e.address.city,", ",e.address.stateProvince]})]}),t&&e.contactInfo?.phoneNumber&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.QFc,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("span",{children:e.contactInfo.phoneNumber})]}),t&&e.contactInfo?.websiteUrl&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.VeH,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("a",{href:e.contactInfo.websiteUrl,target:"_blank",rel:"nofollow noopener noreferrer",className:"hover:text-emerald-600",children:"Visit Website"})]})]})]}),(0,n.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,n.jsx)(a(),{href:o,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Details →"})})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33650:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>l});var n=r(65239),o=r(48088),a=r(31369),i=r(30893),s={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>i[e]);r.d(t,s);let l={children:["",{children:["categories",{children:["[slug]",{children:["[cityStateSlug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1199)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\categories\\[slug]\\[cityStateSlug]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\categories\\[slug]\\[cityStateSlug]\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/categories/[slug]/[cityStateSlug]/page",pathname:"/categories/[slug]/[cityStateSlug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33873:e=>{"use strict";e.exports=require("path")},37676:(e,t,r)=>{Promise.resolve().then(r.bind(r,26800))},39916:(e,t,r)=>{"use strict";var n=r(97576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"unauthorized")&&r.d(t,{unauthorized:function(){return n.unauthorized}})},48976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return o}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function o(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67180:(e,t,r)=>{Promise.resolve().then(r.bind(r,79854))},70899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,i.isNextRouterError)(t)||(0,a.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,o.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),o=r(52637),a=r(51846),i=r(31162),s=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79854:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\clinics\\\\ClinicCard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\clinics\\ClinicCard.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return i},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return c},permanentRedirect:function(){return l},redirect:function(){return s}});let n=r(52836),o=r(49026),a=r(19121).actionAsyncStorage;function i(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let a=Object.defineProperty(Error(o.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a.digest=o.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",a}function s(e,t){var r;throw null!=t||(t=(null==a||null==(r=a.getStore())?void 0:r.isAction)?o.RedirectType.push:o.RedirectType.replace),i(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=o.RedirectType.replace),i(e,t,n.RedirectStatusCode.PermanentRedirect)}function c(e){return(0,o.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94735:e=>{"use strict";e.exports=require("events")},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return o.RedirectType},forbidden:function(){return i.forbidden},notFound:function(){return a.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(86897),o=r(49026),a=r(62765),i=r(48976),s=r(70899),l=r(163);class c extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new c}delete(){throw new c}set(){throw new c}sort(){throw new c}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,1330,3376,6391,2975,255,8446,270],()=>r(33650));module.exports=n})();