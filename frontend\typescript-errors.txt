.next/types/app/blog/[slug]/page.ts(34,29): error TS2344: Type '{ params: { slug: string; }; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/blog/[slug]/page.ts(38,31): error TS2344: Type 'Props' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/blog/authors/[slug]/page.ts(34,29): error TS2344: Type 'AuthorPageProps' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/blog/categories/[slug]/page.ts(34,29): error TS2344: Type '{ params: { slug: string; }; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/blog/categories/[slug]/page.ts(38,31): error TS2344: Type 'Props' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/blog/categories/page.ts(34,29): error TS2344: Type 'BlogCategoriesPageProps' does not satisfy the constraint 'PageProps'.
  Types of property 'searchParams' are incompatible.
    Type '{ query?: string | undefined; page?: string | undefined; } | undefined' is not assignable to type 'Promise<any> | undefined'.
      Type '{ query?: string | undefined; page?: string | undefined; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/blog/page.ts(34,29): error TS2344: Type 'BlogPageProps' does not satisfy the constraint 'PageProps'.
  Types of property 'searchParams' are incompatible.
    Type '{ query?: string | undefined; page?: string | undefined; category?: string | undefined; } | undefined' is not assignable to type 'Promise<any> | undefined'.
      Type '{ query?: string | undefined; page?: string | undefined; category?: string | undefined; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/blog/tags/[slug]/page.ts(34,29): error TS2344: Type '{ params: { slug: string; }; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/categories/[slug]/[cityStateSlug]/page.ts(34,29): error TS2344: Type 'CategoryCityPageProps' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; cityStateSlug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/categories/[slug]/[cityStateSlug]/page.ts(38,31): error TS2344: Type 'CategoryCityPageProps' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; cityStateSlug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/categories/[slug]/page.ts(34,29): error TS2344: Type '{ params: PageParams; searchParams: CategoryPageSearchParams; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type 'PageParams' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/categories/[slug]/page.ts(38,31): error TS2344: Type '{ params: PageParams; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type 'PageParams' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/categories/page.ts(34,29): error TS2344: Type 'CategoriesPageProps' does not satisfy the constraint 'PageProps'.
  Types of property 'searchParams' are incompatible.
    Type '{ query?: string | undefined; page?: string | undefined; } | undefined' is not assignable to type 'Promise<any> | undefined'.
      Type '{ query?: string | undefined; page?: string | undefined; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/clinics/[slug]/page.ts(34,29): error TS2344: Type '{ params: { slug: string; }; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/clinics/[slug]/page.ts(38,31): error TS2344: Type '{ params: { slug: string; }; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/clinics/page.ts(34,29): error TS2344: Type 'ClinicsPageProps' does not satisfy the constraint 'PageProps'.
  Types of property 'searchParams' are incompatible.
    Type '{ query?: string | undefined; location?: string | undefined; page?: string | undefined; } | undefined' is not assignable to type 'Promise<any> | undefined'.
      Type '{ query?: string | undefined; location?: string | undefined; page?: string | undefined; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/conditions/[slug]/page.ts(34,29): error TS2344: Type '{ params: PageParams; searchParams: ConditionPageSearchParams; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type 'PageParams' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/conditions/[slug]/page.ts(38,31): error TS2344: Type '{ params: PageParams; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type 'PageParams' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/practitioners/[slug]/page.ts(34,29): error TS2344: Type '{ params: { slug: string; }; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/practitioners/[slug]/page.ts(38,31): error TS2344: Type '{ params: { slug: string; }; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type '{ slug: string; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/practitioners/page.ts(34,29): error TS2344: Type 'PractitionersPageProps' does not satisfy the constraint 'PageProps'.
  Types of property 'searchParams' are incompatible.
    Type '{ query?: string | undefined; page?: string | undefined; } | undefined' is not assignable to type 'Promise<any> | undefined'.
      Type '{ query?: string | undefined; page?: string | undefined; }' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/specialities/[slug]/page.ts(34,29): error TS2344: Type '{ params: PageParams; searchParams: SpecialtyPageSearchParams; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type 'PageParams' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
.next/types/app/specialities/[slug]/page.ts(38,31): error TS2344: Type '{ params: PageParams; }' does not satisfy the constraint 'PageProps'.
  Types of property 'params' are incompatible.
    Type 'PageParams' is missing the following properties from type 'Promise<any>': then, catch, finally, [Symbol.toStringTag]
e2e/accessibility.spec.ts(2,10): error TS2614: Module '"@axe-core/playwright"' has no exported member 'injectAxe'. Did you mean to use 'import injectAxe from "@axe-core/playwright"' instead?
e2e/accessibility.spec.ts(2,21): error TS2614: Module '"@axe-core/playwright"' has no exported member 'checkA11y'. Did you mean to use 'import checkA11y from "@axe-core/playwright"' instead?
e2e/accessibility.spec.ts(40,17): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
e2e/accessibility.spec.ts(54,17): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
e2e/blog/blog.spec.ts(26,17): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
e2e/blog/blog.spec.ts(45,17): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
e2e/clinics/clinics.spec.ts(31,19): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
e2e/clinics/clinics.spec.ts(34,17): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
e2e/clinics/clinics.spec.ts(52,17): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
e2e/practitioners/practitioners.spec.ts(31,19): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
e2e/practitioners/practitioners.spec.ts(34,17): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
e2e/practitioners/practitioners.spec.ts(52,17): error TS2769: No overload matches this call.
  Overload 1 of 5, '(condition: boolean, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'boolean'.
  Overload 2 of 5, '(callback: ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>, description?: string | undefined): void', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'ConditionBody<PlaywrightTestArgs & PlaywrightTestOptions & PlaywrightWorkerArgs & PlaywrightWorkerOptions>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(63,45): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(66,23): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(67,36): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(70,23): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(71,36): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(77,39): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(80,18): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(81,31): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(84,18): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(85,31): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(91,45): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(92,45): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(93,56): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(106,67): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(112,51): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(115,26): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(116,39): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(119,26): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(120,39): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(122,51): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(123,51): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(133,22): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(134,35): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(144,51): error TS2339: Property 'toBeInTheDocument' does not exist on type 'Matchers<void, HTMLElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(154,45): error TS2339: Property 'toBeInTheDocument' does not exist on type 'Matchers<void, HTMLElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(164,57): error TS2339: Property 'toBeInTheDocument' does not exist on type 'Matchers<void, HTMLElement | null>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(170,19): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(171,19): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostFooter.test.tsx(184,35): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(47,48): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(52,47): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(58,24): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(59,37): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(64,44): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(71,23): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(72,36): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(75,23): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(76,36): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLAnchorElement | null>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(82,19): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(83,19): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(93,48): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(104,53): error TS2339: Property 'toBeInTheDocument' does not exist on type 'Matchers<void, HTMLElement | null>'.
src/__tests__/components/blog/BlogPostHeader.test.tsx(105,56): error TS2339: Property 'toBeInTheDocument' does not exist on type 'Matchers<void, HTMLElement | null>'.
src/__tests__/components/clinics/ClinicsList.test.tsx(43,42): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/clinics/ClinicsList.test.tsx(72,54): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/clinics/ClinicsList.test.tsx(73,47): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/clinics/ClinicsList.test.tsx(87,47): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/clinics/ClinicsList.test.tsx(101,58): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/shared/SearchInput.test.tsx(20,26): error TS7006: Parameter 'fn' implicitly has an 'any' type.
src/__tests__/components/shared/SearchInput.test.tsx(29,19): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/shared/SearchInput.test.tsx(33,18): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<SVGSVGElement | null>'.
src/__tests__/components/shared/SearchInput.test.tsx(41,19): error TS2339: Property 'toHaveAttribute' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/shared/SearchInput.test.tsx(55,19): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/__tests__/components/shared/SearchInput.test.tsx(69,19): error TS2339: Property 'toBeInTheDocument' does not exist on type 'JestMatchers<HTMLElement>'.
src/app/account/page.tsx(14,8): error TS2739: Type '{ children: Element; }' is missing the following properties from type 'LayoutProps': siteName, logoLight, footerCategories
src/app/actions/secure-action.ts(14,29): error TS2339: Property 'get' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/app/api/analytics/post-view/route.ts(24,35): error TS2339: Property 'get' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/app/api/analytics/post-view/route.ts(28,22): error TS2339: Property 'get' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/app/api/analytics/post-view/route.ts(29,19): error TS2339: Property 'set' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/app/api/protected-data/route.ts(28,29): error TS2339: Property 'get' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/app/api/protected-example/route.ts(12,29): error TS2339: Property 'get' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/app/api/secure-example/route.ts(13,31): error TS2339: Property 'get' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/app/blog/[slug]/page.tsx(228,18): error TS7006: Parameter 'p' implicitly has an 'any' type.
src/app/blog/[slug]/page.tsx(233,9): error TS2322: Type 'BlogPost[]' is not assignable to type 'import("C:/AI Coding Projects/naturalhealingnow/frontend/src/lib/blogUtils").BlogPost[]'.
  Type 'BlogPost' is not assignable to type 'import("C:/AI Coding Projects/naturalhealingnow/frontend/src/lib/blogUtils").BlogPost'.
    Types of property 'content' are incompatible.
      Type 'string | undefined' is not assignable to type 'string'.
        Type 'undefined' is not assignable to type 'string'.
src/app/blog/[slug]/page.tsx(266,12): error TS2339: Property 'seo' does not exist on type 'BlogPost'.
src/app/blog/[slug]/page.tsx(267,21): error TS2339: Property 'seo' does not exist on type 'BlogPost'.
src/app/blog/[slug]/page.tsx(268,35): error TS2339: Property 'seo' does not exist on type 'BlogPost'.
src/app/blog/[slug]/page.tsx(269,28): error TS2339: Property 'seo' does not exist on type 'BlogPost'.
src/app/blog/[slug]/page.tsx(271,52): error TS2339: Property 'seo' does not exist on type 'BlogPost'.
src/app/blog/[slug]/page.tsx(287,29): error TS2551: Property 'publish_date' does not exist on type 'BlogPost'. Did you mean 'published_at'?
src/app/blog/[slug]/page.tsx(288,28): error TS2339: Property 'updated_at' does not exist on type 'BlogPost'.
src/app/blog/[slug]/page.tsx(288,47): error TS2551: Property 'publish_date' does not exist on type 'BlogPost'. Did you mean 'published_at'?
src/app/blog/authors/page.tsx(38,6): error TS2739: Type '{ children: Element[]; }' is missing the following properties from type 'LayoutProps': siteName, logoLight, footerCategories
src/app/blog/categories/[slug]/page.tsx(215,7): error TS2322: Type '{ images?: { url: string; }[] | undefined; title: string; description: string; url: string; siteName: any; type: string; }' is not assignable to type 'OpenGraph | null | undefined'.
  Types of property 'type' are incompatible.
    Type 'string' is not assignable to type '"article" | "profile" | "website" | "book" | "music.song" | "music.album" | "music.playlist" | "music.radio_station" | "video.movie" | "video.episode" | "video.tv_show" | "video.other" | undefined'.
src/app/blog/categories/[slug]/page.tsx(219,33): error TS2339: Property 'siteName' does not exist on type '{ title?: string | undefined; description?: string | undefined; image?: { url?: string | undefined; } | undefined; url?: string | undefined; type?: string | undefined; }'.
src/app/blog/categories/page.tsx(97,8): error TS2739: Type '{ children: Element[]; }' is missing the following properties from type 'LayoutProps': siteName, logoLight, footerCategories
src/app/blog/page.tsx(148,24): error TS2339: Property 'content' does not exist on type 'FlatStrapiBlogPost'.
src/app/blog/page.tsx(162,22): error TS2339: Property 'isFeatured' does not exist on type 'FlatStrapiBlogPost'.
src/app/blog/page.tsx(163,22): error TS2339: Property 'view_count' does not exist on type 'FlatStrapiBlogPost'.
src/app/blog/page.tsx(312,18): error TS7006: Parameter 'post' implicitly has an 'any' type.
src/app/blog/page.tsx(355,48): error TS18047: 'featuredPost' is possibly 'null'.
src/app/blog/page.tsx(372,21): error TS7006: Parameter 'post' implicitly has an 'any' type.
src/app/blog/page.tsx(383,19): error TS7006: Parameter 'post' implicitly has an 'any' type.
src/app/blog/tags/page.tsx(71,6): error TS2739: Type '{ children: Element[]; }' is missing the following properties from type 'LayoutProps': siteName, logoLight, footerCategories
src/app/clinics/[slug]/page.tsx(175,33): error TS2339: Property 'canonicalURL' does not exist on type '{ metaTitle?: string | null | undefined; metaDescription?: string | null | undefined; metaImage?: { url?: string | undefined; } | null | undefined; openGraph?: { ogTitle?: string | null | undefined; ogDescription?: string | ... 1 more ... | undefined; ogImage?: { ...; } | ... 1 more ... | undefined; ogUrl?: string |...'.
src/app/clinics/[slug]/page.tsx(181,42): error TS2551: Property 'image' does not exist on type '{ ogTitle?: string | null | undefined; ogDescription?: string | null | undefined; ogImage?: { url?: string | undefined; } | null | undefined; ogUrl?: string | null | undefined; ogType?: string | ... 1 more ... | undefined; }'. Did you mean 'ogImage'?
src/app/clinics/[slug]/page.tsx(201,44): error TS2339: Property 'type' does not exist on type '{ ogTitle?: string | null | undefined; ogDescription?: string | null | undefined; ogImage?: { url?: string | undefined; } | null | undefined; ogUrl?: string | null | undefined; ogType?: string | ... 1 more ... | undefined; }'.
src/app/clinics/[slug]/page.tsx(215,32): error TS2551: Property 'title' does not exist on type '{ ogTitle?: string | null | undefined; ogDescription?: string | null | undefined; ogImage?: { url?: string | undefined; } | null | undefined; ogUrl?: string | null | undefined; ogType?: string | ... 1 more ... | undefined; }'. Did you mean 'ogTitle'?
src/app/clinics/[slug]/page.tsx(216,38): error TS2551: Property 'description' does not exist on type '{ ogTitle?: string | null | undefined; ogDescription?: string | null | undefined; ogImage?: { url?: string | undefined; } | null | undefined; ogUrl?: string | null | undefined; ogType?: string | ... 1 more ... | undefined; }'. Did you mean 'ogDescription'?
src/app/clinics/[slug]/page.tsx(217,30): error TS2339: Property 'url' does not exist on type '{ ogTitle?: string | null | undefined; ogDescription?: string | null | undefined; ogImage?: { url?: string | undefined; } | null | undefined; ogUrl?: string | null | undefined; ogType?: string | ... 1 more ... | undefined; }'.
src/app/clinics/[slug]/page.tsx(219,35): error TS2339: Property 'siteName' does not exist on type '{ ogTitle?: string | null | undefined; ogDescription?: string | null | undefined; ogImage?: { url?: string | undefined; } | null | undefined; ogUrl?: string | null | undefined; ogType?: string | ... 1 more ... | undefined; }'.
src/app/clinics/[slug]/page.tsx(232,34): error TS2551: Property 'title' does not exist on type '{ ogTitle?: string | null | undefined; ogDescription?: string | null | undefined; ogImage?: { url?: string | undefined; } | null | undefined; ogUrl?: string | null | undefined; ogType?: string | ... 1 more ... | undefined; }'. Did you mean 'ogTitle'?
src/app/clinics/[slug]/page.tsx(233,40): error TS2551: Property 'description' does not exist on type '{ ogTitle?: string | null | undefined; ogDescription?: string | null | undefined; ogImage?: { url?: string | undefined; } | null | undefined; ogUrl?: string | null | undefined; ogType?: string | ... 1 more ... | undefined; }'. Did you mean 'ogDescription'?
src/app/clinics/page.tsx(232,17): error TS7006: Parameter 'specialty' implicitly has an 'any' type.
src/app/conditions/[slug]/page.tsx(294,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/app/conditions/[slug]/page.tsx(296,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/app/contact/page.tsx(29,6): error TS2739: Type '{ children: Element[]; }' is missing the following properties from type 'LayoutProps': siteName, logoLight, footerCategories
src/app/forgot-password/page.tsx(12,6): error TS2739: Type '{ children: Element; }' is missing the following properties from type 'LayoutProps': siteName, logoLight, footerCategories
src/app/layout.tsx(31,32): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(32,31): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(32,61): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(33,39): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(41,21): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(41,41): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(42,26): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(44,28): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(46,25): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(113,32): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(114,31): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(114,61): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(115,39): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(123,21): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(123,41): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(124,26): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(126,28): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(128,25): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(178,42): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(179,54): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(180,53): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(180,80): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(181,69): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(181,97): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(182,35): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(187,31): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(189,60): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(190,32): error TS2339: Property 'data' does not exist on type '{}'.
src/app/layout.tsx(191,33): error TS2339: Property 'data' does not exist on type '{}'.
src/app/optimized-example/page.tsx(71,10): error TS18046: 'categories' is of type 'unknown'.
src/app/optimized-example/page.tsx(91,10): error TS18046: 'clinics' is of type 'unknown'.
src/app/optimized-example/page.tsx(111,10): error TS18046: 'practitioners' is of type 'unknown'.
src/app/optimized-example/page.tsx(135,10): error TS18046: 'blogPosts' is of type 'unknown'.
src/app/page.tsx(311,13): error TS7006: Parameter 'post' implicitly has an 'any' type.
src/app/page.tsx(315,13): error TS7006: Parameter 'post' implicitly has an 'any' type.
src/app/page.tsx(450,27): error TS7006: Parameter 'category' implicitly has an 'any' type.
src/app/page.tsx(451,24): error TS7006: Parameter 'category' implicitly has an 'any' type.
src/app/page.tsx(539,27): error TS7006: Parameter 'clinic' implicitly has an 'any' type.
src/app/page.tsx(540,24): error TS7006: Parameter 'clinic' implicitly has an 'any' type.
src/app/page.tsx(610,27): error TS7006: Parameter 'practitioner' implicitly has an 'any' type.
src/app/page.tsx(611,24): error TS7006: Parameter 'practitioner' implicitly has an 'any' type.
src/app/page.tsx(680,50): error TS7006: Parameter 'post' implicitly has an 'any' type.
src/app/practitioners/page.tsx(210,17): error TS7006: Parameter 'specialty' implicitly has an 'any' type.
src/app/search/page.tsx(192,60): error TS7006: Parameter 'post' implicitly has an 'any' type.
src/app/specialities/[slug]/page.tsx(431,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/app/specialities/[slug]/page.tsx(433,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/app/specialities/page.tsx(175,39): error TS7006: Parameter 'item' implicitly has an 'any' type.
src/app/specialities/page.tsx(175,45): error TS7006: Parameter 'index' implicitly has an 'any' type.
src/app/specialities/page.tsx(205,19): error TS7006: Parameter 'specialty' implicitly has an 'any' type.
src/app/specialities/page.tsx(206,16): error TS7006: Parameter 'specialty' implicitly has an 'any' type.
src/app/specialities/page.tsx(252,29): error TS7006: Parameter 'clinic' implicitly has an 'any' type.
src/app/specialities/page.tsx(253,26): error TS7006: Parameter 'clinic' implicitly has an 'any' type.
src/app/specialities/page.tsx(271,29): error TS7006: Parameter 'clinic' implicitly has an 'any' type.
src/app/specialities/page.tsx(272,26): error TS7006: Parameter 'clinic' implicitly has an 'any' type.
src/app/specialities/page.tsx(356,3): error TS2322: Type '(ProcessedSpecialty | null)[]' is not assignable to type 'ProcessedSpecialty[]'.
  Type 'ProcessedSpecialty | null' is not assignable to type 'ProcessedSpecialty'.
    Type 'null' is not assignable to type 'ProcessedSpecialty'.
src/components/auth/ForgotPasswordForm.tsx(21,31): error TS2554: Expected 3 arguments, but got 1.
src/components/auth/ServerProtectedRoute.tsx(22,29): error TS2339: Property 'get' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/components/clinics/ClinicsList.tsx(28,21): error TS7006: Parameter 'clinic' implicitly has an 'any' type.
src/components/examples/CorsTestComponent.tsx(158,11): error TS2322: Type 'unknown' is not assignable to type 'ReactNode'.
src/components/examples/CorsTestComponent.tsx(161,38): error TS2339: Property 'data' does not exist on type '{}'.
src/components/examples/ErrorHandlingExample.tsx(86,50): error TS2339: Property 'data' does not exist on type '{}'.
src/components/examples/ErrorHandlingExample.tsx(86,63): error TS2339: Property 'data' does not exist on type '{}'.
src/components/examples/FormActionExample.tsx(3,10): error TS2305: Module '"next/navigation"' has no exported member 'useActionState'.
src/components/providers/ReactQueryProvider.tsx(50,51): error TS2322: Type '"bottom-right"' is not assignable to type 'DevtoolsPosition | undefined'.
src/components/shared/APIErrorFallback.tsx(34,16): error TS18048: 'statusCode' is possibly 'undefined'.
src/components/shared/LazyComponent.tsx(110,7): error TS2322: Type 'T' is not assignable to type 'Record<string, any> | undefined'.
src/components/shared/LazyImage.tsx(111,7): error TS2448: Block-scoped variable 'setHasError' used before its declaration.
src/components/shared/LazyImage.tsx(111,7): error TS2454: Variable 'setHasError' is used before being assigned.
src/components/shared/LazyImage.tsx(116,5): error TS2448: Block-scoped variable 'setHasError' used before its declaration.
src/components/shared/LazyImage.tsx(116,5): error TS2454: Variable 'setHasError' is used before being assigned.
src/hooks/useOptimisticMutation.ts(60,20): error TS2339: Property 'onError' does not exist on type 'Omit<UseMutationOptions<TData, TError, TVariables, TContext>, "onError" | "mutationFn" | "onMutate" | "onSettled">'.
src/hooks/useOptimisticMutation.ts(61,17): error TS2339: Property 'onError' does not exist on type 'Omit<UseMutationOptions<TData, TError, TVariables, TContext>, "onError" | "mutationFn" | "onMutate" | "onSettled">'.
src/hooks/useOptimisticMutation.ts(70,20): error TS2339: Property 'onSettled' does not exist on type 'Omit<UseMutationOptions<TData, TError, TVariables, TContext>, "onError" | "mutationFn" | "onMutate" | "onSettled">'.
src/hooks/useOptimisticMutation.ts(71,17): error TS2339: Property 'onSettled' does not exist on type 'Omit<UseMutationOptions<TData, TError, TVariables, TContext>, "onError" | "mutationFn" | "onMutate" | "onSettled">'.
src/lib/apiOptimization.ts(56,12): error TS18048: 'cachedData' is possibly 'undefined'.
src/lib/serverApi.ts(78,29): error TS2339: Property 'get' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/lib/serverAuth.ts(22,22): error TS2339: Property 'get' does not exist on type 'Promise<ReadonlyRequestCookies>'.
src/lib/serverErrorHandling.ts(21,16): error TS2503: Cannot find namespace 'JSX'.
src/lib/serverErrorHandling.ts(55,21): error TS2503: Cannot find namespace 'JSX'.
src/lib/supabase.ts(1,30): error TS2307: Cannot find module '@supabase/supabase-js' or its corresponding type declarations.
