'use client';

import { AxiosRequestConfig } from 'axios';
import { enhancedFetchFromApi } from './enhancedApiUtils';
import { prefetchData } from './apiOptimization';
import { QueryClient } from '@tanstack/react-query';

/**
 * Preload API data using React Query
 * 
 * @param queryClient QueryClient instance
 * @param endpoint API endpoint to preload
 * @param params Request parameters
 * @param queryKey Optional custom query key (defaults to [endpoint, params])
 */
export function preloadApiData<T = any>(
  queryClient: QueryClient,
  endpoint: string,
  params?: AxiosRequestConfig['params'],
  queryKey?: unknown[]
) {
  // Use provided queryKey or default to [endpoint, params]
  const key = queryKey || [endpoint, params];
  
  // Prefetch the data and store in React Query cache
  return queryClient.prefetchQuery({
    queryKey: key,
    queryFn: async () => {
      const options: AxiosRequestConfig = {};
      if (params) {
        options.params = params;
      }
      
      return await enhancedFetch<PERSON>rom<PERSON>pi<T>(endpoint, options, false);
    },
    // Cache for 5 minutes
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Preload API data using the existing cache mechanism
 * 
 * @param endpoint API endpoint to preload
 * @param params Request parameters
 * @param ttl Cache TTL in milliseconds (default: 5 minutes)
 */
export function preloadApiDataWithCache<T = any>(
  endpoint: string,
  params?: AxiosRequestConfig['params'],
  ttl: number = 5 * 60 * 1000
) {
  const options: AxiosRequestConfig = {};
  if (params) {
    options.params = params;
  }
  
  return prefetchData<T>(endpoint, options, ttl);
}

/**
 * Preload an image
 * 
 * @param src Image URL to preload
 * @returns Promise that resolves when the image is loaded
 */
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

/**
 * Preload a page by prefetching its resources
 * 
 * @param path Page path to preload
 */
export function preloadPage(path: string): void {
  // Create a link element for prefetching
  const link = document.createElement('link');
  link.rel = 'prefetch';
  link.href = path;
  link.as = 'document';
  
  // Add to document head
  document.head.appendChild(link);
}

/**
 * Preload a font
 * 
 * @param fontUrl Font URL to preload
 * @param fontFormat Font format (e.g., 'woff2', 'woff', 'truetype')
 */
export function preloadFont(fontUrl: string, fontFormat: string = 'woff2'): void {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = fontUrl;
  link.as = 'font';
  link.type = `font/${fontFormat}`;
  link.crossOrigin = 'anonymous';
  
  document.head.appendChild(link);
}
