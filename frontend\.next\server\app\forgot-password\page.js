(()=>{var e={};e.id=2162,e.ids=[2162],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36200:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,metadata:()=>a});var s=t(37413),o=t(14143),n=t(62769);let a={title:"Forgot Password - Natural Healing Now",description:"Reset your Natural Healing Now account password"};function i(){return(0,s.jsx)(o.default,{children:(0,s.jsx)("div",{className:"container mx-auto py-12 px-4",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)(n.default,{})})})})}},52284:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.default,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>d});var s=t(65239),o=t(48088),n=t(31369),a=t(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);t.d(r,i);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36200)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\forgot-password\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62769:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\auth\\\\ForgotPasswordForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\auth\\ForgotPasswordForm.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90567:(e,r,t)=>{"use strict";t.d(r,{default:()=>d});var s=t(60687),o=t(43210),n=t(6363),a=t(85814),i=t.n(a);function d(){let[e,r]=(0,o.useState)(""),[t,a]=(0,o.useState)(""),[d,l]=(0,o.useState)(""),[u,c]=(0,o.useState)(!1),{resetPassword:p}=(0,n.A)(),m=async r=>{r.preventDefault(),a(""),l(""),c(!0);try{let{error:r}=await p(e);r?a(r.message||"Failed to send password reset email"):l("Password reset instructions have been sent to your email")}catch(e){console.error("Error during password reset:",e),a("An unexpected error occurred")}finally{c(!1)}};return(0,s.jsxs)("div",{className:"max-w-md w-full mx-auto p-6 bg-white rounded-lg shadow-md",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-gray-800",children:"Reset Password"}),t&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-red-100 text-red-700 rounded-md",children:t}),d&&(0,s.jsx)("div",{className:"mb-4 p-3 bg-green-100 text-green-700 rounded-md",children:d}),(0,s.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,s.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>r(e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500",placeholder:"Enter your registered email"})]}),(0,s.jsx)("button",{type:"submit",disabled:u||!!d,className:"w-full bg-emerald-600 text-white py-2 px-4 rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Sending...":"Send Reset Link"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Remember your password?"," ",(0,s.jsx)(i(),{href:"/signin",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"Sign in"})]})})]})}},92621:(e,r,t)=>{Promise.resolve().then(t.bind(t,90567)),Promise.resolve().then(t.bind(t,28351))},94735:e=>{"use strict";e.exports=require("events")},94829:(e,r,t)=>{Promise.resolve().then(t.bind(t,62769)),Promise.resolve().then(t.bind(t,14143))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,1330,3376,6391,2975,8446,270],()=>t(52284));module.exports=s})();