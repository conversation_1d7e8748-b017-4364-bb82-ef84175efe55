# Server Configuration
# --------------------
HOST=0.0.0.0
PORT=1337
# The public URL of your Strapi application (used for links, redirects, etc.)
# Example: https://your-strapi-domain.com
APP_URL=http://localhost:1337
# Node environment (development, production)
NODE_ENV=development

# Database Configuration
# ----------------------
# Choose your database client: 'sqlite', 'postgres', 'mysql'
DATABASE_CLIENT=sqlite

# --- SQLite Configuration (default for easy local setup) ---
# File path for the SQLite database file
DATABASE_FILENAME=.tmp/data.db

# --- PostgreSQL Configuration (Example for Production) ---
# Uncomment and configure if using PostgreSQL
# DATABASE_CLIENT=postgres
# DATABASE_HOST=127.0.0.1
# DATABASE_PORT=5432
# DATABASE_NAME=strapi
# DATABASE_USERNAME=strapi
# DATABASE_PASSWORD=strapi
# DATABASE_SSL=false
# DATABASE_SCHEMA=public # Optional: specify if not using 'public'
# DATABASE_URL= # Optional: Alternatively, provide a connection string URL

# --- MySQL Configuration (Example for Production) ---
# Uncomment and configure if using MySQL
# DATABASE_CLIENT=mysql
# DATABASE_HOST=127.0.0.1
# DATABASE_PORT=3306
# DATABASE_NAME=strapi
# DATABASE_USERNAME=strapi
# DATABASE_PASSWORD=strapi
# DATABASE_SSL=false

# Security - IMPORTANT: Generate strong, unique secrets for production!
# ---------------------------------------------------------------------
# You can generate secrets using: node -e "console.log(require('crypto').randomBytes(16).toString('base64'))"
ADMIN_JWT_SECRET=generate-a-strong-secret
API_TOKEN_SALT=generate-a-strong-salt
APP_KEYS=generate-a-strong-key,generate-another-strong-key
JWT_SECRET=generate-a-strong-secret

# Optional: Cloudinary for Media Library (if not using local uploads)
# CLOUDINARY_NAME=
# CLOUDINARY_KEY=
# CLOUDINARY_SECRET=

# Optional: Email Provider (e.g., SendGrid, AWS SES) for password resets, etc.
# EMAIL_PROVIDER=
# EMAIL_PROVIDER_API_KEY=
# EMAIL_DEFAULT_FROM=
# EMAIL_DEFAULT_REPLY_TO=
