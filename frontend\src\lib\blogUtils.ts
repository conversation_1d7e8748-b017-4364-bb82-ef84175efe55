/**
 * Utility functions for blog posts
 */
import logger from './logger';
import { getStrapiMediaUrl } from './mediaUtils';
import { StrapiBlogPost, StrapiAuthor } from '@/types/strapi';

/**
 * Interface for the blog post structure used in components
 */
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  content: string;
  featured_image: string | null;
  published_at: string;
  reading_time?: number;
  author: {
    id: string;
    name: string;
    slug: string;
    profile_picture: string | null;
    bio?: string | null;
  };
  categories: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  tags: Array<string>;
  related_posts: Array<{
    id: string;
    title: string;
    slug: string;
    excerpt?: string | null;
    content: string; // Make content non-optional for related_posts as well
    reading_time?: number;
  }>;
}

/**
 * Generate an excerpt from HTML content
 * @param htmlContent - HTML content to extract excerpt from
 * @param maxLength - Maximum length of the excerpt
 * @returns Plain text excerpt
 */
export function generateExcerpt(htmlContent: string, maxLength: number = 160): string {
  if (!htmlContent) return '';

  // Basic HTML tag stripping and trimming
  const textContent = htmlContent.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();

  if (textContent.length <= maxLength) {
    return textContent;
  }

  // Truncate and add ellipsis
  return textContent.substring(0, maxLength).trimEnd() + '...';
}

/**
 * Format a date string
 * @param dateString - Date string to format
 * @returns Formatted date string
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Truncate text by word count
 * @param text - Text to truncate
 * @param maxWords - Maximum number of words
 * @returns Truncated text
 */
export function truncateByWords(text: string | null | undefined, maxWords: number): string {
  if (!text) return '';

  const words = text.split(' ');

  if (words.length <= maxWords) {
    return text;
  }

  return words.slice(0, maxWords).join(' ') + '...';
}

/**
 * Calculate reading time for a text
 * @param text - Text to calculate reading time for
 * @returns Reading time in minutes
 */
export function calculateReadingTime(text: string): number {
  const wordsPerMinute = 200;
  const wordCount = text?.split(/\s+/)?.length || 0;
  return Math.max(1, Math.ceil(wordCount / wordsPerMinute));
}

/**
 * Map Strapi API response to our blog post structure
 * @param strapiPost - Strapi blog post data, typed as StrapiBlogPost
 * @returns Mapped blog post or null if invalid
 */
export function mapStrapiBlogPostToProps(strapiPost: StrapiBlogPost): BlogPost | null {
  if (!strapiPost) {
    logger.warn("No blog post data to map");
    return null;
  }

  // Assume v5 structure primarily, but handle potential 'attributes' nesting within populated fields.
  const id = strapiPost.id;
  const data = strapiPost; // Start with the direct object for v5

  if (!id || !data) {
    logger.warn("Invalid blog post data structure:", strapiPost);
    return null;
  }

  const apiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || '';
  logger.debug("Using API URL:", apiUrl);

  // Extract categories
  let categories: Array<{id: string; name: string; slug: string}> = [];
  try {
    const categoriesData = data.blog_categories; // Access directly based on populate key

    if (Array.isArray(categoriesData)) {
      categories = categoriesData.map((cat: any) => {
        const catData = cat.attributes || cat; // Handle potential attributes nesting within the category object
        return {
          id: cat.id || '',
          name: catData.name || 'Unnamed Category',
          slug: catData.slug || 'unnamed-category'
        };
      });
    }
  } catch (e) {
    logger.warn("Error extracting categories:", e);
  }

  // Extract tags
  let tags: string[] = [];
  try {
    const tagsData = data.blog_tags; // Access directly

    if (Array.isArray(tagsData)) {
      tags = tagsData.map((tag: any) => {
        const tagData = tag.attributes || tag; // Handle potential attributes nesting
        return tagData.name || '';
      }).filter(Boolean);
    }
  } catch (e) {
    logger.warn("Error extracting tags:", e);
  }

  // Extract related posts
  let relatedPosts: Array<{id: string; title: string; slug: string; excerpt?: string | null; content?: string}> = [];
  try {
    const relatedPostsData = data.related_posts; // Access directly

    if (Array.isArray(relatedPostsData)) {
      relatedPosts = relatedPostsData.map((post: any) => {
        const postData = post.attributes || post; // Handle potential attributes nesting
        return {
          id: post.id || '',
          title: postData.title || 'Untitled Post',
          slug: postData.slug || 'untitled-post',
          excerpt: postData.excerpt || null,
          content: postData.content || '' // Ensure content is always a string
        };
      });
    }
  } catch (e) {
    logger.warn("Error extracting related posts:", e);
  }

  // Extract author information
  let author = {
    id: '0',
    name: 'Unknown Author',
    slug: 'unknown-author',
    profile_picture: null as string | null,
    bio: null as string | null
  };

  try {
    // Simplify author access for v5, assuming 'author_blogs' relation from populate
    const authorRelation = data.author_blogs;
    const authorData = Array.isArray(authorRelation) ? authorRelation[0] : authorRelation;

    if (authorData) {
      // StrapiAuthor is flat, no .attributes needed for authorData itself
      const authorAttrs = authorData; 
      const profilePictureUrl = getStrapiMediaUrl(authorAttrs.profilePicture);

      author = {
        id: String(authorData.id || '0'), // Ensure ID is string
        name: authorAttrs.name || 'Unknown Author',
        slug: authorAttrs.slug || 'unknown-author',
        profile_picture: profilePictureUrl,
        bio: authorAttrs.bio || null // Extract author bio
      };
    }
  } catch (e) {
    logger.warn("Error extracting author information:", e);
  }

  // Extract featured image URL
  const featuredImageUrl = getStrapiMediaUrl(data.featuredImage);

  // Get content and other fields with fallbacks
  const title = data.title || 'Untitled Post';
  const slug = data.slug || 'untitled-post';
  const excerpt = data.excerpt || null;
  const content = data.content || '<p>No content available.</p>'; // data.content is string from StrapiBlogPost
  const publishedAt = data.publishDate || data.publishedAt || data.createdAt || new Date().toISOString(); // Removed data.publish_date

  // Calculate reading time from content
  const readingTime = calculateReadingTime(content);

  // Create the blog post object without view_count directly in the object
  const blogPost = {
    id: String(id), // Ensure ID is string
    title,
    slug,
    excerpt,
    content,
    featured_image: featuredImageUrl,
    published_at: publishedAt,
    reading_time: readingTime,
    author,
    categories,
    tags,
    related_posts: relatedPosts.map(post => {
      // For related posts, ensure content is a string and calculate reading time
      const relatedPostContent = post.content || ''; // Ensures content is a string for reading time calculation
      const estimatedReadingTime = relatedPostContent ?
        calculateReadingTime(relatedPostContent) :
        (post.excerpt ? calculateReadingTime(post.excerpt.repeat(5)) : 2);

      return {
        ...post,
        content: relatedPostContent, // Explicitly set content to be a string
        reading_time: estimatedReadingTime
      };
    })
  };

  // Add view_count as a non-enumerable property if it exists in the data
  if (data.view_count !== undefined) {
    Object.defineProperty(blogPost, 'view_count', {
      value: data.view_count,
      enumerable: false
    });
  }

  return blogPost;
}
