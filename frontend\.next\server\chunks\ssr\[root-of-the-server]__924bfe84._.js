module.exports = {

"[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "inter_59dee874-module__9CtR0q__className",
});
}}),
"[next]/internal/font/google/inter_59dee874.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_59dee874$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_59dee874$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Inter', 'Inter Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_59dee874$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_59dee874$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/src/contexts/AuthContext.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AuthProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/AuthContext.tsx <module evaluation>", "AuthProvider");
const useAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/AuthContext.tsx <module evaluation>", "useAuth");
}}),
"[project]/src/contexts/AuthContext.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const AuthProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/AuthContext.tsx", "AuthProvider");
const useAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/AuthContext.tsx", "useAuth");
}}),
"[project]/src/contexts/AuthContext.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/contexts/ErrorContext.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ErrorProvider": (()=>ErrorProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useError": (()=>useError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const ErrorProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ErrorProvider() from the server but ErrorProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/ErrorContext.tsx <module evaluation>", "ErrorProvider");
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/contexts/ErrorContext.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/ErrorContext.tsx <module evaluation>", "default");
const useError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useError() from the server but useError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/ErrorContext.tsx <module evaluation>", "useError");
}}),
"[project]/src/contexts/ErrorContext.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ErrorProvider": (()=>ErrorProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useError": (()=>useError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const ErrorProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ErrorProvider() from the server but ErrorProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/ErrorContext.tsx", "ErrorProvider");
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/contexts/ErrorContext.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/ErrorContext.tsx", "default");
const useError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useError() from the server but useError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/contexts/ErrorContext.tsx", "useError");
}}),
"[project]/src/contexts/ErrorContext.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ErrorContext$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/contexts/ErrorContext.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ErrorContext$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/contexts/ErrorContext.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ErrorContext$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/components/layout/Layout.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/layout/Layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/Layout.tsx <module evaluation>", "default");
}}),
"[project]/src/components/layout/Layout.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/layout/Layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/Layout.tsx", "default");
}}),
"[project]/src/components/layout/Layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Layout$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/layout/Layout.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Layout$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/layout/Layout.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Layout$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/providers/QueryProvider.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/providers/QueryProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/QueryProvider.tsx <module evaluation>", "default");
}}),
"[project]/src/providers/QueryProvider.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/providers/QueryProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/providers/QueryProvider.tsx", "default");
}}),
"[project]/src/providers/QueryProvider.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$QueryProvider$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/providers/QueryProvider.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$QueryProvider$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/providers/QueryProvider.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$QueryProvider$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[project]/src/lib/strapi.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fetchAPI": (()=>fetchAPI),
    "getStrapiContent": (()=>getStrapiContent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qs$2f$lib$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/qs/lib/index.js [app-rsc] (ecmascript)"); // Import qs
;
// Get the API URL from environment variables
const API_URL = ("TURBOPACK compile-time value", "https://nice-badge-2130241d6c.strapiapp.com");
// Ensure we have a valid API URL, especially in production
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
// Define a fallback URL for development only
const FALLBACK_URL = ("TURBOPACK compile-time truthy", 1) ? 'http://localhost:1337' : ("TURBOPACK unreachable", undefined);
// Use the API_URL if available, otherwise use the fallback (only in development)
const EFFECTIVE_API_URL = API_URL || FALLBACK_URL;
// Always log the API URL being used
console.log('Using Strapi API URL:', EFFECTIVE_API_URL || 'No API URL found in environment variables');
// Get the Strapi Media URL for images (used for debugging)
const STRAPI_MEDIA_URL = ("TURBOPACK compile-time truthy", 1) ? EFFECTIVE_API_URL.replace('strapiapp.com', 'media.strapiapp.com') : ("TURBOPACK unreachable", undefined);
// Log the media URL for debugging
if (STRAPI_MEDIA_URL) {
    console.log('Strapi Media URL:', STRAPI_MEDIA_URL);
}
const fetchAPI = async (endpoint, options = {})=>{
    const requestId = Math.random().toString(36).substring(2, 8);
    // Construct the full URL for the fetch request
    // Ensure endpoint starts with a slash if it's not already relative from /api
    const apiPath = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    let urlForFetch = `${EFFECTIVE_API_URL}/api${apiPath}`;
    try {
        const API_TOKEN = process.env.STRAPI_API_TOKEN;
        const baseHeaders = {
            'Content-Type': 'application/json',
            ...API_TOKEN && {
                Authorization: `Bearer ${API_TOKEN}`
            }
        };
        // In Next.js 15, fetch is uncached by default
        // We need to explicitly opt-in to caching with 'force-cache'
        // or set a revalidation period
        // Determine the cache setting based on revalidate option
        let cacheOption = 'no-store'; // Default to no-store in Next.js 15
        // If options.cache is explicitly provided, use it
        if (options.cache) {
            cacheOption = options.cache;
        } else if (options.next?.revalidate !== undefined) {
            cacheOption = options.next.revalidate === 0 ? 'no-store' : 'force-cache';
        } else {
            // Default to 'force-cache' if revalidate is not specified,
            // to ensure ISR pages (which might not pass revalidate on every fetch) still cache.
            // This aligns with the need to opt-into caching for ISR.
            cacheOption = 'force-cache';
        }
        // Log cache settings in development for debugging
        if ("TURBOPACK compile-time truthy", 1) {
            console.log(`[${requestId}] Fetch cache settings for ${endpoint}:`, {
                cache: cacheOption,
                revalidate: options.next?.revalidate,
                tags: options.next?.tags || []
            });
        }
        const fetchOptions = {
            method: options.method || 'GET',
            headers: {
                ...baseHeaders,
                ...options.headers || {}
            },
            // Explicitly set cache option for Next.js 15 compatibility
            cache: cacheOption,
            // Ensure next options (like tags and revalidate) are consistently applied
            next: {
                // Default to an empty array for tags if not provided
                tags: options.next?.tags || [],
                // Preserve revalidate if explicitly set
                ...options.next?.revalidate !== undefined && {
                    revalidate: options.next.revalidate
                }
            }
        };
        // If revalidate is specifically false (cache forever), ensure it's passed correctly
        if (options.next?.revalidate === false && fetchOptions.next) {
            fetchOptions.next.revalidate = false;
        }
        // Handle query parameters for GET requests using 'qs'
        if ((fetchOptions.method === 'GET' || fetchOptions.method === 'HEAD') && options.params) {
            const queryString = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qs$2f$lib$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].stringify(options.params, {
                encodeValuesOnly: true
            });
            if (queryString) {
                urlForFetch = `${urlForFetch}?${queryString}`;
            }
        } else if (options.params && fetchOptions.method !== 'GET' && fetchOptions.method !== 'HEAD') {
            // For POST, PUT, etc., set the body
            fetchOptions.body = JSON.stringify(options.params);
        }
        if ("TURBOPACK compile-time truthy", 1) {
            console.log(`[${requestId}] Requesting Strapi API (native fetch): ${urlForFetch}`);
            if (fetchOptions.next?.tags) console.log(`[${requestId}] Using cache tags:`, fetchOptions.next.tags);
            if (fetchOptions.next?.revalidate !== undefined) console.log(`[${requestId}] Using revalidation:`, fetchOptions.next.revalidate);
            if (options.params) console.log(`[${requestId}] Request params (raw):`, JSON.stringify(options.params, null, 2));
        }
        const response = await fetch(urlForFetch, fetchOptions);
        if (!response.ok) {
            let errorData;
            try {
                errorData = await response.json();
            } catch (e) {
                errorData = {
                    message: response.statusText,
                    details: await response.text().catch(()=>'')
                };
            }
            console.error(`[${requestId}] API Error (${urlForFetch}): Status ${response.status}`, errorData);
            const error = new Error(`API Error: ${response.status} ${response.statusText}`);
            // @ts-ignore
            error.response = {
                status: response.status,
                data: errorData
            };
            throw error;
        }
        const responseData = await response.json();
        if ("TURBOPACK compile-time truthy", 1) {
            console.log(`[${requestId}] API request successful for ${urlForFetch}`);
            if (responseData) {
                console.log(`[${requestId}] Response structure:`, JSON.stringify({
                    hasData: !!responseData?.data,
                    dataType: responseData?.data ? Array.isArray(responseData.data) ? 'array' : 'object' : 'none',
                    dataLength: responseData?.data && Array.isArray(responseData.data) ? responseData.data.length : 'n/a',
                    hasMeta: !!responseData?.meta
                }, null, 2));
            }
        }
        return responseData;
    } catch (error) {
        const isProduction = ("TURBOPACK compile-time value", "development") === 'production';
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            console.error(`[${requestId}] Error fetching from API (${urlForFetch}):`, error.message || 'Unknown error');
            if (error.response) {
                console.error(`[${requestId}] Response status:`, error.response.status);
                console.error(`[${requestId}] Response data:`, error.response.data);
            } else if (error.request) {
                console.error(`[${requestId}] No response received. Request details for ${urlForFetch}`);
            } else {
                console.error(`[${requestId}] Error details:`, error);
            }
        }
        if (error.message && error.message.includes('ECONNREFUSED')) {
            console.error(`[${requestId}] CRITICAL ERROR: Connection refused for ${urlForFetch}.`);
            if (urlForFetch.includes('127.0.0.1') || urlForFetch.includes('localhost')) {
                console.error(`[${requestId}] Attempting to connect to localhost. Check NEXT_PUBLIC_API_URL in Vercel environment variables if this is a deployed environment.`);
            }
        }
        if (error.response?.status === 400) {
            console.error(`[${requestId}] Bad Request (400) - Check query parameters for Strapi v5 compatibility.`);
        }
        error.requestId = requestId;
        throw error;
    }
};
const getStrapiContent = {
    // Clinic related queries
    clinics: {
        getAll: async (params = {})=>{
            const { query, location, specialtySlug, conditionSlug, categorySlug, page = 1, pageSize = 12, cache, next, ...restParams } = params; // Destructure slugs and separate cache/next options
            // Base query params (exclude cache and next options)
            const queryParams = {
                ...restParams,
                populate: restParams.populate || '*',
                publicationState: 'live',
                pagination: {
                    page: page,
                    pageSize: pageSize
                },
                filters: {
                    ...restParams.filters || {}
                }
            };
            // Build filters dynamically
            const filters = {
                ...restParams.filters || {}
            };
            let combinedFilters = []; // Use an array for $and
            // Name/Service/Specialty/Condition filter using $or
            if (query) {
                combinedFilters.push({
                    $or: [
                        {
                            name: {
                                $containsi: query
                            }
                        },
                        {
                            services: {
                                name: {
                                    $containsi: query
                                }
                            }
                        },
                        {
                            specialties: {
                                name: {
                                    $containsi: query
                                }
                            }
                        },
                        {
                            conditions: {
                                name: {
                                    $containsi: query
                                }
                            }
                        },
                        {
                            categories: {
                                name: {
                                    $containsi: query
                                }
                            }
                        },
                        {
                            description: {
                                $containsi: query
                            }
                        } // Added clinic description search
                    ]
                });
            }
            // Location filter - Revised structure: $or inside the component filter
            if (location) {
                combinedFilters.push({
                    address: {
                        $or: [
                            {
                                streetAddress1: {
                                    $containsi: location
                                }
                            },
                            // { streetAddress2: { $containsi: location } }, // Removed invalid key
                            {
                                city: {
                                    $containsi: location
                                }
                            },
                            {
                                stateProvince: {
                                    $containsi: location
                                }
                            },
                            {
                                postalCode: {
                                    $containsi: location
                                }
                            }
                        ]
                    }
                });
            }
            // Specialty filter
            if (specialtySlug) {
                combinedFilters.push({
                    specialties: {
                        slug: {
                            $eq: specialtySlug
                        }
                    }
                });
            }
            // Condition filter
            if (conditionSlug) {
                combinedFilters.push({
                    conditions: {
                        slug: {
                            $eq: conditionSlug
                        }
                    }
                });
            }
            // Category filter
            if (categorySlug) {
                combinedFilters.push({
                    categories: {
                        slug: {
                            $eq: categorySlug
                        }
                    }
                });
            }
            // Combine all filters using $and if multiple filters exist
            if (combinedFilters.length > 1) {
                filters.$and = combinedFilters;
            } else if (combinedFilters.length === 1) {
                Object.assign(filters, combinedFilters[0]);
            }
            // Assign the constructed filters back to queryParams
            queryParams.filters = filters;
            // Add cache tags if specialtySlug is provided
            if (specialtySlug) {
                // Define cache tags for specialty-related queries
                const cacheTags = [
                    'strapi-clinics',
                    'strapi-specialties',
                    `strapi-specialty-${specialtySlug}`,
                    `strapi-specialty-${specialtySlug}-clinics`
                ];
                // Add page number to cache tags
                cacheTags.push(`strapi-specialty-${specialtySlug}-page-${page}`);
                // Add query to cache tags if provided
                if (query) {
                    cacheTags.push(`strapi-specialty-${specialtySlug}-query-${query}`);
                }
                // Add location to cache tags if provided
                if (location) {
                    cacheTags.push(`strapi-specialty-${specialtySlug}-location-${location}`);
                }
                // Fetch clinics with cache tags
                const nextOptionsForSpecialty = {
                    tags: cacheTags
                };
                if (next?.revalidate !== undefined) {
                    nextOptionsForSpecialty.revalidate = next.revalidate;
                }
                return fetchAPI(`/clinics`, {
                    params: queryParams,
                    cache: cache,
                    next: nextOptionsForSpecialty
                });
            }
            // Add cache tags if categorySlug is provided
            if (categorySlug) {
                // Define cache tags for category-related queries
                const cacheTags = [
                    'strapi-clinics',
                    'strapi-categories',
                    `strapi-category-${categorySlug}`,
                    `strapi-category-${categorySlug}-clinics`
                ];
                // Add page number to cache tags
                cacheTags.push(`strapi-category-${categorySlug}-page-${page}`);
                // Add query to cache tags if provided
                if (query) {
                    cacheTags.push(`strapi-category-${categorySlug}-query-${query}`);
                }
                // Add location to cache tags if provided
                if (location) {
                    cacheTags.push(`strapi-category-${categorySlug}-location-${location}`);
                }
                // Fetch clinics with cache tags
                const nextOptionsForCategory = {
                    tags: cacheTags
                };
                if (next?.revalidate !== undefined) {
                    nextOptionsForCategory.revalidate = next.revalidate;
                }
                return fetchAPI(`/clinics`, {
                    params: queryParams,
                    cache: cache,
                    next: nextOptionsForCategory
                });
            }
            // Define default cache tags for clinic list
            const defaultCacheTags = [
                'strapi-clinics-list'
            ];
            // Add page number to cache tags
            const pageNumber = queryParams.pagination?.page || 1;
            defaultCacheTags.push(`strapi-clinics-page-${pageNumber}`);
            // Add query to cache tags if provided
            if (query) {
                defaultCacheTags.push(`strapi-clinics-query-${query}`);
            }
            // Add location to cache tags if provided
            if (location) {
                defaultCacheTags.push(`strapi-clinics-location-${location}`);
            }
            // Merge with any existing cache tags from next options
            const effectiveTags = [
                ...next?.tags || [],
                ...defaultCacheTags
            ];
            const nextOptionsDefault = {
                tags: effectiveTags
            };
            if (next?.revalidate !== undefined) {
                nextOptionsDefault.revalidate = next.revalidate;
            }
            // Fetch clinics with cache tags
            return fetchAPI(`/clinics`, {
                params: queryParams,
                cache: cache,
                next: nextOptionsDefault
            });
        },
        getAllSlugs: async (options = {})=>{
            // Default tags for clinic slugs
            const defaultTags = [
                'strapi-clinics-slugs'
            ];
            // Combine default tags with any provided tags
            const combinedTags = [
                ...defaultTags,
                ...options.next?.tags || []
            ];
            // In Next.js 15, we need to explicitly opt-in to caching
            return fetchAPI(`/clinics`, {
                params: {
                    fields: [
                        'slug'
                    ],
                    pagination: {
                        pageSize: 250
                    }
                },
                // Explicitly set cache option for Next.js 15
                cache: options.cache || 'force-cache',
                next: {
                    // Use combined tags
                    tags: combinedTags,
                    // Default to 12 hours revalidation if not specified
                    revalidate: options.next?.revalidate ?? 43200,
                    // Spread other next options
                    ...options.next || {}
                }
            });
        },
        getBySlug: async (slug, options = {})=>{
            const defaultTags = [
                'strapi-clinics',
                `strapi-clinic-${slug}`
            ];
            // Combine default tags with any provided tags
            const combinedTags = [
                ...defaultTags,
                ...options.next?.tags || []
            ];
            // In Next.js 15, we need to explicitly opt-in to caching
            return fetchAPI(`/clinics`, {
                params: {
                    filters: {
                        slug: {
                            $eq: slug
                        }
                    },
                    populate: {
                        logo: true,
                        address: true,
                        contactInfo: true,
                        location: true,
                        openingHours: true,
                        services: true,
                        specialties: true,
                        conditions: true,
                        practitioners: {
                            populate: {
                                profilePicture: true
                            }
                        },
                        appointment_options: true,
                        payment_methods: true,
                        seo: true
                    }
                },
                // Explicitly set cache option for Next.js 15
                cache: options.cache || 'force-cache',
                next: {
                    ...options.next || {},
                    // Use combined tags
                    tags: combinedTags,
                    // Default to 12 hours revalidation if not specified
                    revalidate: options.next?.revalidate ?? 43200
                }
            });
        },
        getFeatured: async (options = {})=>fetchAPI(`/clinics`, {
                params: {
                    filters: {
                        isFeatured: {
                            $eq: true
                        }
                    },
                    populate: '*'
                },
                ...options
            }),
        // New function to get clinics by category slug with pagination and filtering
        getByCategorySlug: async ({ slug, query = '', location = '', page = 1, pageSize = 12 })=>{
            try {
                console.log(`Fetching clinics for category slug: ${slug}, page: ${page}, query: ${query}, location: ${location}`);
                // Build filters using the same approach as getAll function
                const filters = {
                    categories: {
                        slug: {
                            $eq: slug
                        }
                    }
                };
                // Build combined filters array for complex queries
                const combinedFilters = [];
                // Always include the category filter
                combinedFilters.push({
                    categories: {
                        slug: {
                            $eq: slug
                        }
                    }
                });
                // Add search query if provided
                if (query) {
                    combinedFilters.push({
                        $or: [
                            {
                                name: {
                                    $containsi: query
                                }
                            },
                            {
                                services: {
                                    name: {
                                        $containsi: query
                                    }
                                }
                            },
                            {
                                specialties: {
                                    name: {
                                        $containsi: query
                                    }
                                }
                            },
                            {
                                conditions: {
                                    name: {
                                        $containsi: query
                                    }
                                }
                            }
                        ]
                    });
                }
                // Add location filter if provided
                if (location) {
                    combinedFilters.push({
                        address: {
                            $or: [
                                {
                                    streetAddress1: {
                                        $containsi: location
                                    }
                                },
                                {
                                    city: {
                                        $containsi: location
                                    }
                                },
                                {
                                    stateProvince: {
                                        $containsi: location
                                    }
                                },
                                {
                                    postalCode: {
                                        $containsi: location
                                    }
                                }
                            ]
                        }
                    });
                }
                // Use $and only if we have multiple filters
                const finalFilters = combinedFilters.length > 1 ? {
                    $and: combinedFilters
                } : filters;
                // Construct query parameters
                const queryParams = {
                    filters: finalFilters,
                    pagination: {
                        page,
                        pageSize
                    },
                    populate: {
                        logo: true,
                        featuredImage: true,
                        address: true,
                        contactInfo: true,
                        categories: true
                    },
                    publicationState: 'live'
                };
                // Define cache tags for this request to enable targeted revalidation
                const cacheTags = [
                    'strapi-clinics',
                    'strapi-categories',
                    `strapi-category-${slug}`,
                    `strapi-category-${slug}-clinics`,
                    `strapi-category-${slug}-page-${page}`
                ];
                // Add the query to the cache tag if it exists
                if (query) {
                    cacheTags.push(`strapi-category-${slug}-query-${query}`);
                }
                // Add the location to the cache tag if it exists
                if (location) {
                    cacheTags.push(`strapi-category-${slug}-location-${location}`);
                }
                // Use fetchAPI with cache tags
                return fetchAPI(`/clinics`, {
                    params: queryParams,
                    next: {
                        tags: cacheTags
                    }
                });
            } catch (error) {
                console.error(`Error in getByCategorySlug for clinics with slug ${slug}:`, error);
                throw error;
            }
        }
    },
    // Practitioner related queries
    practitioners: {
        getAll: async (params = {})=>{
            const { query, location, specialtySlug, conditionSlug, categorySlug, page = 1, pageSize = 12, cache, next, ...restParams } = params; // Destructure slugs and separate cache/next options
            // Base query params (exclude cache and next options)
            const queryParams = {
                ...restParams,
                populate: restParams.populate || '*',
                publicationState: 'live',
                pagination: {
                    page: page,
                    pageSize: pageSize
                },
                filters: {
                    ...restParams.filters || {}
                }
            };
            // Build filters dynamically
            const filters = {
                ...restParams.filters || {}
            };
            let combinedFilters = []; // Use an array for $and
            // Name/Specialty/Condition filter using $or
            if (query) {
                combinedFilters.push({
                    $or: [
                        {
                            name: {
                                $containsi: query
                            }
                        },
                        {
                            specialties: {
                                name: {
                                    $containsi: query
                                }
                            }
                        },
                        {
                            conditions: {
                                name: {
                                    $containsi: query
                                }
                            }
                        }
                    ]
                });
            }
            // Location filter (assuming practitioners have direct address component) - Revised structure
            // NOTE: If practitioners are linked via clinics, this filter might need adjustment.
            // PRACTITIONERS DO NOT HAVE LOCATION - DO NOT FILTER BY IT
            // if (location) {
            //    combinedFilters.push({
            //     address: { // Target the component
            //       $or: [ // Apply OR logic *within* the component's fields (Removed streetAddress2)
            //         { streetAddress1: { $containsi: location } },
            //         // { streetAddress2: { $containsi: location } }, // Removed invalid key
            //         { city: { $containsi: location } },
            //         { stateProvince: { $containsi: location } },
            //         { postalCode: { $containsi: location } },
            //       ]
            //     }
            //   });
            // }
            // Specialty filter
            if (specialtySlug) {
                combinedFilters.push({
                    specialties: {
                        slug: {
                            $eq: specialtySlug
                        }
                    }
                });
            }
            // Condition filter
            if (conditionSlug) {
                combinedFilters.push({
                    conditions: {
                        slug: {
                            $eq: conditionSlug
                        }
                    }
                });
            }
            // Category filter
            if (categorySlug) {
                combinedFilters.push({
                    categories: {
                        slug: {
                            $eq: categorySlug
                        }
                    }
                });
            }
            // Combine all filters using $and if multiple filters exist
            if (combinedFilters.length > 1) {
                filters.$and = combinedFilters;
            } else if (combinedFilters.length === 1) {
                Object.assign(filters, combinedFilters[0]);
            }
            // Assign the constructed filters back to queryParams
            queryParams.filters = filters;
            // Add cache tags if specialtySlug is provided
            if (specialtySlug) {
                // Define cache tags for specialty-related queries
                const cacheTags = [
                    'strapi-practitioners',
                    'strapi-specialties',
                    `strapi-specialty-${specialtySlug}`,
                    `strapi-specialty-${specialtySlug}-practitioners`
                ];
                // Add page number to cache tags
                cacheTags.push(`strapi-specialty-${specialtySlug}-page-${page}`);
                // Add query to cache tags if provided
                if (query) {
                    cacheTags.push(`strapi-specialty-${specialtySlug}-query-${query}`);
                }
                // Add location to cache tags if provided
                if (location) {
                    cacheTags.push(`strapi-specialty-${specialtySlug}-location-${location}`);
                }
                // Fetch practitioners with cache tags
                const nextOptionsForSpecialty = {
                    tags: cacheTags
                };
                if (next?.revalidate !== undefined) {
                    nextOptionsForSpecialty.revalidate = next.revalidate;
                }
                return fetchAPI(`/practitioners`, {
                    params: queryParams,
                    cache: cache,
                    next: nextOptionsForSpecialty
                });
            }
            // Add cache tags if categorySlug is provided
            if (categorySlug) {
                // Define cache tags for category-related queries
                const cacheTags = [
                    'strapi-practitioners',
                    'strapi-categories',
                    `strapi-category-${categorySlug}`,
                    `strapi-category-${categorySlug}-practitioners`
                ];
                // Add page number to cache tags
                cacheTags.push(`strapi-category-${categorySlug}-page-${page}`);
                // Add query to cache tags if provided
                if (query) {
                    cacheTags.push(`strapi-category-${categorySlug}-query-${query}`);
                }
                // Add location to cache tags if provided
                if (location) {
                    cacheTags.push(`strapi-category-${categorySlug}-location-${location}`);
                }
                // Fetch practitioners with cache tags
                const nextOptionsForCategory = {
                    tags: cacheTags
                };
                if (next?.revalidate !== undefined) {
                    nextOptionsForCategory.revalidate = next.revalidate;
                }
                return fetchAPI(`/practitioners`, {
                    params: queryParams,
                    cache: cache,
                    next: nextOptionsForCategory
                });
            }
            // Define default cache tags for practitioner list
            const defaultCacheTags = [
                'strapi-practitioners-list'
            ];
            // Add page number to cache tags
            const pageNumber = queryParams.pagination?.page || 1;
            defaultCacheTags.push(`strapi-practitioners-page-${pageNumber}`);
            // Add query to cache tags if provided
            if (query) {
                defaultCacheTags.push(`strapi-practitioners-query-${query}`);
            }
            // Add location to cache tags if provided
            if (location) {
                defaultCacheTags.push(`strapi-practitioners-location-${location}`);
            }
            // Merge with any existing cache tags from next options
            const effectiveTags = [
                ...next?.tags || [],
                ...defaultCacheTags
            ];
            const nextOptionsDefault = {
                tags: effectiveTags
            };
            if (next?.revalidate !== undefined) {
                nextOptionsDefault.revalidate = next.revalidate;
            }
            // Fetch practitioners with cache tags
            return fetchAPI(`/practitioners`, {
                params: queryParams,
                cache: cache,
                next: nextOptionsDefault
            });
        },
        getAllSlugs: async (options = {})=>{
            const allSlugs = [];
            let page = 1;
            // Strapi's default max page size is often 100. Using a safe common limit.
            // If your Strapi instance is configured for a higher limit, this can be adjusted.
            const effectivePageSize = 100;
            let moreSlugsToFetch = true;
            const defaultTags = [
                'strapi-practitioners-slugs'
            ];
            // Consolidate fetch options for reuse, ensuring cache and next options from params are respected
            const fetchOptionsBase = {
                cache: options.cache || 'force-cache',
                next: {
                    revalidate: options.next?.revalidate ?? 43200,
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ],
                    ...options.next || {}
                }
            };
            // Ensure tags are unique if combined from multiple sources
            if (fetchOptionsBase.next.tags) {
                fetchOptionsBase.next.tags = Array.from(new Set(fetchOptionsBase.next.tags));
            }
            while(moreSlugsToFetch){
                try {
                    const response = await fetchAPI(`/practitioners`, {
                        params: {
                            fields: [
                                'slug'
                            ],
                            pagination: {
                                page: page,
                                pageSize: effectivePageSize
                            }
                        },
                        // Pass the consolidated cache/next options
                        cache: fetchOptionsBase.cache,
                        next: fetchOptionsBase.next
                    });
                    if (response?.data && Array.isArray(response.data)) {
                        response.data.forEach((item)=>{
                            // Strapi v5 flattens the response, so attributes are directly on the item.
                            // No need to check item.attributes.slug typically.
                            if (item && item.slug) {
                                allSlugs.push({
                                    slug: item.slug
                                });
                            }
                        });
                        // Determine if there are more pages to fetch
                        const paginationInfo = response.meta?.pagination;
                        if (response.data.length < effectivePageSize || !paginationInfo || page >= paginationInfo.pageCount) {
                            moreSlugsToFetch = false;
                        } else {
                            page++;
                        }
                    } else {
                        // No data or unexpected format
                        moreSlugsToFetch = false;
                    }
                } catch (error) {
                    console.error(`Error fetching page ${page} of practitioner slugs:`, error);
                    moreSlugsToFetch = false; // Stop on error
                }
            }
            return {
                data: allSlugs
            };
        },
        getBySlug: async (slug, options = {})=>{
            const defaultTags = [
                'strapi-practitioner',
                `strapi-practitioner-${slug}`
            ]; // Changed 'strapi-practitioners' to 'strapi-practitioner'
            return fetchAPI(`/practitioners`, {
                params: {
                    filters: {
                        slug: {
                            $eq: slug
                        }
                    },
                    populate: {
                        profilePicture: true,
                        contactInfo: true,
                        specialties: true,
                        conditions: true,
                        clinics: true,
                        seo: true
                    }
                },
                // In Next.js 15, we need to explicitly opt-in to caching
                cache: options.cache || 'force-cache',
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ],
                    // Default to 12 hours revalidation if not specified
                    revalidate: options.next?.revalidate ?? 43200
                }
            });
        },
        getFeatured: async (options = {})=>fetchAPI(`/practitioners`, {
                params: {
                    filters: {
                        isFeatured: {
                            $eq: true
                        }
                    },
                    populate: '*'
                },
                ...options
            }),
        // New function to get practitioners by category slug with pagination and filtering
        getByCategorySlug: async ({ slug, query = '', location = '', page = 1, pageSize = 12 })=>{
            try {
                // Build filters using the same approach as getAll function
                const filters = {
                    categories: {
                        slug: {
                            $eq: slug
                        }
                    }
                };
                // Build combined filters array for complex queries
                const combinedFilters = [];
                // Always include the category filter
                combinedFilters.push({
                    categories: {
                        slug: {
                            $eq: slug
                        }
                    }
                });
                // Add search query if provided
                if (query) {
                    combinedFilters.push({
                        $or: [
                            {
                                name: {
                                    $containsi: query
                                }
                            },
                            {
                                title: {
                                    $containsi: query
                                }
                            },
                            {
                                qualifications: {
                                    $containsi: query
                                }
                            },
                            {
                                specialties: {
                                    name: {
                                        $containsi: query
                                    }
                                }
                            },
                            {
                                conditions: {
                                    name: {
                                        $containsi: query
                                    }
                                }
                            }
                        ]
                    });
                }
                // Add location filter if provided
                if (location) {
                    combinedFilters.push({
                        address: {
                            $or: [
                                {
                                    streetAddress1: {
                                        $containsi: location
                                    }
                                },
                                {
                                    city: {
                                        $containsi: location
                                    }
                                },
                                {
                                    stateProvince: {
                                        $containsi: location
                                    }
                                },
                                {
                                    postalCode: {
                                        $containsi: location
                                    }
                                }
                            ]
                        }
                    });
                }
                // Use $and only if we have multiple filters
                const finalFilters = combinedFilters.length > 1 ? {
                    $and: combinedFilters
                } : filters;
                // Construct query parameters
                const queryParams = {
                    filters: finalFilters,
                    pagination: {
                        page,
                        pageSize
                    },
                    populate: {
                        profilePicture: true,
                        contactInfo: true,
                        specialties: true,
                        conditions: true,
                        categories: true
                    },
                    publicationState: 'live'
                };
                // Define cache tags for this request to enable targeted revalidation
                const cacheTags = [
                    'strapi-practitioners',
                    'strapi-categories',
                    `strapi-category-${slug}`,
                    `strapi-category-${slug}-practitioners`,
                    `strapi-category-${slug}-page-${page}`
                ];
                // Add the query to the cache tag if it exists
                if (query) {
                    cacheTags.push(`strapi-category-${slug}-query-${query}`);
                }
                // Add the location to the cache tag if it exists
                if (location) {
                    cacheTags.push(`strapi-category-${slug}-location-${location}`);
                }
                // Use fetchAPI with cache tags
                return fetchAPI(`/practitioners`, {
                    params: queryParams,
                    next: {
                        tags: cacheTags
                    }
                });
            } catch (error) {
                console.error(`Error in getByCategorySlug for practitioners with slug ${slug}:`, error);
                throw error;
            }
        }
    },
    // Category related queries
    categories: {
        getAll: async (params = {})=>{
            const { query, location, page = 1, pageSize = 12, cache, next, ...restParams } = params; // Destructure cache/next options
            // Base query params (exclude cache and next options)
            const queryParams = {
                ...restParams,
                populate: restParams.populate || {
                    // Explicitly populate media fields to ensure proper structure
                    icon: true,
                    featuredImage: true
                },
                publicationState: 'live',
                pagination: {
                    page: page,
                    pageSize: pageSize
                },
                filters: {
                    ...restParams.filters || {}
                }
            };
            // Add name filter if query is provided
            if (query && queryParams.filters) {
                queryParams.filters.name = {
                    $containsi: query
                };
            }
            // Define cache tags for this request to enable targeted revalidation
            const defaultCacheTags = [
                'strapi-categories',
                'strapi-categories-slugs'
            ];
            // Add page number to cache tags
            if (queryParams.pagination?.page) defaultCacheTags.push(`strapi-categories-page-${queryParams.pagination.page}`);
            // Add query to cache tags if provided
            if (query) defaultCacheTags.push(`strapi-categories-query-${query}`);
            // Merge with any existing cache tags from next options
            const effectiveTags = [
                ...next?.tags || [],
                ...defaultCacheTags
            ];
            const nextOptions = {
                tags: effectiveTags
            };
            if (next?.revalidate !== undefined) {
                nextOptions.revalidate = next.revalidate;
            }
            return fetchAPI(`/categories`, {
                params: queryParams,
                cache: cache,
                next: nextOptions
            });
        },
        getBySlug: async (slug, options = {})=>{
            const defaultCacheTags = [
                'strapi-categories',
                `strapi-category-${slug}`,
                `strapi-category-${slug}-clinics`,
                `strapi-category-${slug}-practitioners`
            ];
            return fetchAPI(`/categories`, {
                params: {
                    filters: {
                        slug: {
                            $eq: slug
                        }
                    },
                    populate: {
                        seo: {
                            populate: {
                                openGraph: {
                                    populate: {
                                        ogImage: true
                                    }
                                },
                                metaImage: true
                            }
                        },
                        icon: true,
                        featuredImage: true,
                        clinics: {
                            populate: {
                                logo: true,
                                featuredImage: true,
                                address: true,
                                contactInfo: true
                            }
                        },
                        practitioners: {
                            populate: {
                                profilePicture: true,
                                contactInfo: true
                            }
                        }
                    },
                    publicationState: 'live'
                },
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultCacheTags,
                        ...options.next?.tags || []
                    ]
                }
            });
        },
        getAllSlugs: async (options = {})=>{
            const defaultTags = [
                'strapi-categories-slugs'
            ];
            return fetchAPI(`/categories`, {
                params: {
                    fields: [
                        'slug'
                    ],
                    pagination: {
                        pageSize: 1000
                    }
                },
                cache: options.cache || 'force-cache',
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ],
                    revalidate: options.next?.revalidate ?? 43200
                }
            });
        },
        // Function to get categories specifically for the footer
        getFooterCategories: async (options = {})=>{
            const defaultTags = [
                'strapi-categories',
                'strapi-categories-footer'
            ];
            return fetchAPI(`/categories`, {
                params: {
                    filters: {
                        showInFooter: {
                            $eq: true
                        }
                    },
                    populate: '*',
                    publicationState: 'live'
                },
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ]
                }
            });
        }
    },
    // Blog related queries
    blog: {
        getPosts: async (params = {})=>{
            try {
                // Create a clean query params object with proper structure for Strapi v5
                const queryParams = {
                    publicationState: 'live'
                };
                // Extract pagination parameters
                let page = 1;
                let pageSize = 10;
                // Handle pagination separately to ensure correct structure
                if (params.pagination) {
                    queryParams.pagination = params.pagination;
                    page = params.pagination.page || 1;
                    pageSize = params.pagination.pageSize || 10;
                } else if (params.page || params.pageSize) {
                    // Support for legacy pagination parameters
                    page = params.page || 1;
                    pageSize = params.pageSize || 10;
                    queryParams.pagination = {
                        page,
                        pageSize
                    };
                }
                // Handle sort parameter
                if (params.sort) {
                    queryParams.sort = params.sort;
                } else {
                    // Default to newest first if no sort parameter is provided
                    queryParams.sort = [
                        'publishDate:desc'
                    ];
                }
                // Handle filters
                if (params.filters) {
                    queryParams.filters = params.filters;
                } else if (params.categorySlug) {
                    // Support for direct categorySlug parameter
                    queryParams.filters = {
                        ...queryParams.filters || {},
                        blog_categories: {
                            slug: {
                                $eq: params.categorySlug
                            }
                        }
                    };
                } else if (params.tagSlug) {
                    // Support for direct tagSlug parameter
                    queryParams.filters = {
                        ...queryParams.filters || {},
                        blog_tags: {
                            slug: {
                                $eq: params.tagSlug
                            }
                        }
                    };
                } else if (params.query) {
                    // Support for direct query parameter
                    queryParams.filters = {
                        ...queryParams.filters || {},
                        $or: [
                            {
                                title: {
                                    $containsi: params.query
                                }
                            },
                            {
                                excerpt: {
                                    $containsi: params.query
                                }
                            }
                        ]
                    };
                }
                // Handle populate parameter with fallback to default comprehensive fields
                if (params.populate) {
                    queryParams.populate = params.populate;
                } else {
                    queryParams.populate = {
                        featuredImage: true,
                        author_blogs: {
                            populate: {
                                profilePicture: true
                            }
                        },
                        blog_categories: true,
                        blog_tags: true
                    };
                }
                // Query parameters prepared for blog posts
                // Define cache tags for this request to enable targeted revalidation
                const cacheTags = [
                    'strapi-blog-posts'
                ];
                // Add category-specific cache tag if filtering by category
                if (params.categorySlug) {
                    cacheTags.push(`strapi-category-${params.categorySlug}`);
                    cacheTags.push(`strapi-category-${params.categorySlug}-page-${page}`);
                }
                // Add tag-specific cache tag if filtering by tag
                if (params.tagSlug) {
                    cacheTags.push(`strapi-tag-${params.tagSlug}`);
                    cacheTags.push(`strapi-tag-${params.tagSlug}-page-${page}`);
                }
                // Add query-specific cache tag if filtering by query
                if (params.query) {
                    cacheTags.push(`strapi-blog-query-${params.query}`);
                }
                // Fetch blog posts with the constructed query parameters and cache tags
                const response = await fetchAPI(`/blog-posts`, {
                    params: queryParams,
                    next: {
                        tags: cacheTags
                    }
                });
                // Log the response structure for debugging
                if ("TURBOPACK compile-time truthy", 1) {
                    console.log('Blog getPosts response structure:', JSON.stringify({
                        hasData: !!response?.data,
                        dataIsArray: Array.isArray(response?.data),
                        dataLength: Array.isArray(response?.data) ? response.data.length : 'not an array',
                        hasMeta: !!response?.meta,
                        hasPagination: !!response?.meta?.pagination,
                        totalItems: response?.meta?.pagination?.total || 'unknown'
                    }));
                }
                return response;
            } catch (error) {
                console.error('Error in blog.getPosts:', error);
                // Add more detailed error logging
                if (error.response) {
                    console.error('Response status:', error.response.status);
                    console.error('Response data:', error.response.data);
                    console.error('Response headers:', error.response.headers);
                }
                throw error;
            }
        },
        getAllSlugs: async (options = {})=>{
            const defaultTags = [
                'strapi-blog-posts-slugs'
            ];
            return fetchAPI(`/blog-posts`, {
                params: {
                    fields: [
                        'slug'
                    ],
                    pagination: {
                        pageSize: 1000
                    }
                },
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ]
                }
            });
        },
        getPostBySlug: async (slug, options = {})=>{
            const defaultCacheTags = [
                'strapi-blog-posts',
                `strapi-blog-post-${slug}`
            ];
            return fetchAPI(`/blog-posts`, {
                params: {
                    filters: {
                        slug: {
                            $eq: slug
                        }
                    },
                    populate: {
                        seo: {
                            populate: {
                                metaImage: true,
                                openGraph: true
                            }
                        },
                        featuredImage: true,
                        author_blogs: {
                            fields: [
                                'id',
                                'name',
                                'slug',
                                'bio'
                            ],
                            populate: {
                                profilePicture: true
                            }
                        },
                        blog_categories: {
                            fields: [
                                'id',
                                'name',
                                'slug'
                            ]
                        },
                        blog_tags: {
                            fields: [
                                'id',
                                'name',
                                'slug'
                            ]
                        }
                    }
                },
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultCacheTags,
                        ...options.next?.tags || []
                    ]
                }
            });
        },
        getCategories: async (params = {}, fetchOptions = {})=>{
            const { next: paramsNext, ...strapiParams } = params; // Separate next from Strapi query params
            const queryParams = {
                ...strapiParams,
                publicationState: 'live',
                populate: strapiParams.populate || '*'
            };
            const defaultCacheTags = [
                'strapi-categories',
                'strapi-blog-categories'
            ];
            return fetchAPI(`/blog-categories`, {
                params: queryParams,
                next: {
                    ...fetchOptions.next || {},
                    ...paramsNext || {},
                    tags: [
                        ...defaultCacheTags,
                        ...fetchOptions.next?.tags || [],
                        ...paramsNext?.tags || []
                    ]
                }
            });
        },
        getCategoryBySlug: async (slug, params = {}, fetchOptions = {})=>{
            try {
                // Default pagination to show the last 12 blog posts
                const page = params.pagination?.page || 1;
                const pageSize = params.pagination?.pageSize || 12;
                // Default sort to show the most recent posts first
                const sort = params.sort || 'publishDate:desc';
                // Prepare to make API call to /blog-categories
                // Define cache tags for this request to enable targeted revalidation
                const cacheTags = [
                    'strapi-categories',
                    'strapi-blog-categories',
                    `strapi-category-${slug}`,
                    `strapi-category-${slug}-page-${page}`
                ];
                // Improved query structure for Strapi v5
                // The key change is to separate the blog_posts population from pagination
                // This ensures we get the category with ALL its posts, but paginated
                const queryParams = {
                    filters: {
                        slug: {
                            $eq: slug
                        }
                    },
                    // This pagination is for the blog categories themselves
                    pagination: {
                        page: 1,
                        pageSize: 1 // We only need one category
                    },
                    sort: [
                        sort
                    ],
                    populate: {
                        blog_posts: {
                            fields: [
                                'title',
                                'slug',
                                'excerpt',
                                'publishDate',
                                'publishedAt'
                            ],
                            populate: {
                                featuredImage: true,
                                author_blogs: {
                                    populate: {
                                        profilePicture: true
                                    }
                                }
                            },
                            // This pagination is for the blog posts within the category
                            pagination: {
                                page,
                                pageSize
                            },
                            sort: [
                                sort
                            ]
                        },
                        seo: true
                    }
                };
                // Query params prepared for slug ${slug}
                // In Strapi 5, use a simpler query structure
                const response = await fetchAPI(`/blog-categories`, {
                    params: queryParams,
                    next: {
                        tags: cacheTags
                    }
                });
                // Response received for category with slug ${slug}
                return response;
            } catch (error) {
                console.error(`Error in getCategoryBySlug for slug ${slug}:`, error);
                // Try a simpler approach as fallback
                try {
                    // Use a very simple query structure as fallback
                    const { next: paramsNextFallback, ...strapiParamsFallback } = params;
                    return await fetchAPI(`/blog-categories`, {
                        params: {
                            ...strapiParamsFallback,
                            filters: {
                                slug: {
                                    $eq: slug
                                }
                            },
                            populate: {
                                blog_posts: {
                                    populate: [
                                        'featuredImage',
                                        'author_blogs.profilePicture'
                                    ]
                                },
                                seo: true
                            }
                        },
                        next: {
                            ...fetchOptions.next || {},
                            ...paramsNextFallback || {}
                        }
                    });
                } catch (fallbackError) {
                    console.error(`Fallback also failed for slug ${slug}:`, fallbackError);
                    throw fallbackError;
                }
            }
        },
        getTags: async (options = {})=>{
            const defaultTags = [
                'strapi-tags',
                'strapi-blog-tags'
            ];
            return fetchAPI('/blog-tags', {
                params: {
                    populate: '*'
                },
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ]
                }
            });
        },
        getTagBySlug: async (slug, options = {})=>{
            const defaultTags = [
                'strapi-tags',
                'strapi-blog-tags',
                `strapi-tag-${slug}`
            ];
            return fetchAPI(`/blog-tags`, {
                params: {
                    filters: {
                        slug: {
                            $eq: slug
                        }
                    },
                    populate: {
                        blog_posts: {
                            populate: {
                                featuredImage: {
                                    fields: [
                                        'url',
                                        'alternativeText',
                                        'width',
                                        'height',
                                        'formats'
                                    ] // Be explicit
                                },
                                author_blogs: {
                                    populate: {
                                        profilePicture: {
                                            fields: [
                                                'url',
                                                'alternativeText',
                                                'width',
                                                'height',
                                                'formats'
                                            ] // Be explicit
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ]
                }
            });
        },
        getAuthors: <AUTHORS>
            // Default to 'force-cache' and 12hr revalidation if not specified by caller
            const cacheSetting = options.cache ?? 'force-cache';
            const revalidateSetting = options.next?.revalidate ?? 43200;
            const defaultTags = [
                'strapi-authors',
                'strapi-blog-authors'
            ];
            return fetchAPI(`/authors`, {
                params: {
                    populate: '*'
                },
                cache: cacheSetting,
                next: {
                    ...options.next,
                    revalidate: revalidateSetting,
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ]
                }
            });
        },
        getAuthorBySlug: async (slug, options = {})=>{
            // Default to 'force-cache' and 12hr revalidation if not specified by caller
            const cacheSetting = options.cache ?? 'force-cache';
            const revalidateSetting = options.next?.revalidate ?? 43200;
            const defaultTags = [
                'strapi-authors',
                'strapi-blog-authors',
                `strapi-author-${slug}`
            ];
            return fetchAPI(`/authors`, {
                params: {
                    filters: {
                        slug: {
                            $eq: slug
                        }
                    },
                    populate: '*'
                },
                cache: cacheSetting,
                next: {
                    ...options.next,
                    revalidate: revalidateSetting,
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ]
                }
            });
        }
    },
    // Condition related queries
    conditions: {
        getAll: async (params = {})=>{
            const { query, location, page = 1, pageSize = 12, cache, next, ...restParams } = params;
            const queryParams = {
                ...restParams,
                populate: restParams.populate || '*',
                publicationState: 'live',
                pagination: {
                    page: page,
                    pageSize: pageSize
                },
                filters: {
                    ...restParams.filters || {}
                }
            };
            if (query && queryParams.filters) queryParams.filters.name = {
                $containsi: query
            };
            // Define cache tags for this request
            const defaultCacheTags = [
                'strapi-conditions'
            ];
            if (queryParams.pagination?.page) defaultCacheTags.push(`strapi-conditions-page-${queryParams.pagination.page}`);
            if (query) defaultCacheTags.push(`strapi-conditions-query-${query}`);
            // Merge with any existing cache tags from next options
            const effectiveTags = [
                ...next?.tags || [],
                ...defaultCacheTags
            ];
            const nextOptions = {
                tags: effectiveTags
            };
            if (next?.revalidate !== undefined) {
                nextOptions.revalidate = next.revalidate;
            }
            return fetchAPI(`/conditions`, {
                params: queryParams,
                cache: cache,
                next: nextOptions
            });
        },
        getBySlug: async (slug, options = {})=>{
            const defaultTags = [
                'strapi-conditions',
                `strapi-condition-${slug}`
            ];
            return fetchAPI(`/conditions`, {
                params: {
                    filters: {
                        slug: {
                            $eq: slug
                        }
                    },
                    populate: {
                        seo: true
                    }
                },
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ]
                }
            });
        },
        getAllSlugs: async (options = {})=>{
            const defaultTags = [
                'strapi-conditions-slugs'
            ];
            return fetchAPI(`/conditions`, {
                params: {
                    fields: [
                        'slug'
                    ],
                    pagination: {
                        pageSize: 1000
                    }
                },
                cache: options.cache || 'force-cache',
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ],
                    revalidate: options.next?.revalidate ?? 43200
                }
            });
        }
    },
    // About Us (Single Type)
    aboutUs: {
        get: async (options = {})=>fetchAPI(`/about-us`, {
                params: {
                    populate: '*',
                    publicationState: 'live'
                },
                ...options
            })
    },
    // Privacy Policy (Single Type)
    privacyPolicy: {
        get: async (options = {})=>fetchAPI(`/privacy-policy`, {
                params: {
                    populate: '*',
                    publicationState: 'live'
                },
                ...options
            })
    },
    // Terms of Service (Single Type)
    termsOfService: {
        get: async (options = {})=>fetchAPI(`/terms-of-service`, {
                params: {
                    populate: '*',
                    publicationState: 'live'
                },
                ...options
            })
    },
    // Affiliate Disclosure (Single Type)
    affiliateDisclosure: {
        get: async (options = {})=>fetchAPI(`/affiliate-disclosure`, {
                params: {
                    populate: '*',
                    publicationState: 'live'
                },
                ...options
            })
    },
    // Global settings
    global: {
        getSettings: async (options = {})=>fetchAPI(`/global-setting`, {
                params: {
                    populate: '*',
                    publicationState: 'live'
                },
                ...options
            }),
        getHomepage: async (options = {})=>fetchAPI(`/homepage`, {
                params: {
                    populate: '*',
                    publicationState: 'live'
                },
                ...options
            }),
        getBlogHomepage: async (options = {})=>fetchAPI(`/blog-homepage`, {
                params: {
                    populate: '*',
                    publicationState: 'live'
                },
                ...options
            })
    },
    // Specialty related queries
    specialties: {
        getAll: async (params = {})=>{
            const { query, location, page = 1, pageSize = 12, cache, next, ...restParams } = params;
            const queryParams = {
                ...restParams,
                populate: restParams.populate || '*',
                publicationState: 'live',
                pagination: {
                    page: page,
                    pageSize: pageSize
                },
                filters: {
                    ...restParams.filters || {}
                }
            };
            if (query && queryParams.filters) queryParams.filters.name = {
                $containsi: query
            };
            // Define cache tags for this request
            const defaultCacheTags = [
                'strapi-specialties'
            ];
            if (queryParams.pagination?.page) defaultCacheTags.push(`strapi-specialties-page-${queryParams.pagination.page}`);
            if (query) defaultCacheTags.push(`strapi-specialties-query-${query}`);
            // Merge with any existing cache tags from next options
            const effectiveTags = [
                ...next?.tags || [],
                ...defaultCacheTags
            ];
            const nextOptions = {
                tags: effectiveTags
            };
            if (next?.revalidate !== undefined) {
                nextOptions.revalidate = next.revalidate;
            }
            try {
                const response = await fetchAPI(`/specialties`, {
                    params: queryParams,
                    cache: cache,
                    next: nextOptions
                });
                // If the data is an array and has items, check the first item
                if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
                    const firstItem = response.data[0];
                    // If the item doesn't have attributes but has direct properties,
                    // transform it to match the expected structure
                    if (!firstItem.attributes && firstItem.id) {
                        // Create a new array with transformed items
                        response.data = response.data.map((item)=>{
                            // Skip if already has attributes or is invalid
                            if (item.attributes || !item.id) return item;
                            // Create a copy of the item without the properties we'll move to attributes
                            const { id, ...rest } = item;
                            // Return a new object with the expected structure
                            return {
                                id,
                                attributes: {
                                    ...rest
                                }
                            };
                        });
                    }
                }
                return response;
            } catch (error) {
                console.error("Error in getAll for specialties:", error);
                throw error;
            }
        },
        getAllSlugs: async (options = {})=>{
            const defaultTags = [
                'strapi-specialties-slugs'
            ];
            return fetchAPI(`/specialties`, {
                params: {
                    fields: [
                        'slug'
                    ],
                    pagination: {
                        pageSize: 1000
                    }
                },
                cache: options.cache || 'force-cache',
                next: {
                    ...options.next || {},
                    tags: [
                        ...defaultTags,
                        ...options.next?.tags || []
                    ],
                    revalidate: options.next?.revalidate ?? 43200
                }
            });
        },
        getBySlug: async (slug, options = {})=>{
            const defaultCacheTags = [
                'strapi-specialties',
                `strapi-specialty-${slug}`
            ];
            try {
                const response = await fetchAPI(`/specialties`, {
                    params: {
                        filters: {
                            slug: {
                                $eq: slug
                            }
                        },
                        populate: {
                            seo: true,
                            featuredImage: true,
                            clinics: true,
                            practitioners: true
                        }
                    },
                    next: {
                        ...options.next || {},
                        tags: [
                            ...defaultCacheTags,
                            ...options.next?.tags || []
                        ]
                    }
                });
                // If the data is an array and has items, check the first item
                if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
                    const firstItem = response.data[0];
                    // If the item doesn't have attributes but has direct properties,
                    // transform it to match the expected structure
                    if (!firstItem.attributes && firstItem.id) {
                        // Create a new array with transformed items
                        response.data = response.data.map((item)=>{
                            // Skip if already has attributes or is invalid
                            if (item.attributes || !item.id) return item;
                            // Create a copy of the item without the properties we'll move to attributes
                            const { id, ...rest } = item;
                            // Return a new object with the expected structure
                            return {
                                id,
                                attributes: {
                                    ...rest
                                }
                            };
                        });
                    }
                }
                return response;
            } catch (error) {
                console.error(`Error in getBySlug for specialty ${slug}:`, error);
                throw error;
            }
        }
    },
    // SEO helpers
    seo: {
        getMetadata: (entity)=>{
            if (!entity) return null;
            // Extract SEO data from the entity's SEO field (from the SEO plugin)
            const seo = entity.attributes?.seo?.data?.attributes || null;
            const metaSocial = entity.attributes?.metaSocial || [];
            return {
                title: seo?.metaTitle || entity.attributes?.title || entity.attributes?.name,
                description: seo?.metaDescription || entity.attributes?.description || entity.attributes?.excerpt,
                openGraph: metaSocial?.find((item)=>item.socialNetwork === 'Facebook') || null,
                twitter: metaSocial?.find((item)=>item.socialNetwork === 'Twitter') || null,
                structuredData: seo?.structuredData || null,
                canonicalURL: seo?.canonicalURL || null,
                metaRobots: seo?.metaRobots || null
            };
        }
    }
};
}}),
"[project]/src/lib/serverCache.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced server-side caching system for Next.js 15 and Strapi 5
 * 
 * Key improvements:
 * - Adaptive caching based on content type and environment
 * - Stale-while-revalidate strategy using tags for optimal performance
 * - Tiered cache hierarchy with contextual invalidation
 * - Performance monitoring for cache operations
 * - Optimized for Next.js 15's improved cache API
 */ __turbopack_context__.s({
    "getBlogCategories": (()=>getBlogCategories),
    "getBlogHomepage": (()=>getBlogHomepage),
    "getBlogPostBySlug": (()=>getBlogPostBySlug),
    "getBlogPosts": (()=>getBlogPosts),
    "getBlogTags": (()=>getBlogTags),
    "getCategories": (()=>getCategories),
    "getCategoryBySlug": (()=>getCategoryBySlug),
    "getClinicBySlug": (()=>getClinicBySlug),
    "getClinics": (()=>getClinics),
    "getConditionBySlug": (()=>getConditionBySlug),
    "getConditions": (()=>getConditions),
    "getFeaturedBlogPosts": (()=>getFeaturedBlogPosts),
    "getFeaturedClinics": (()=>getFeaturedClinics),
    "getFeaturedPractitioners": (()=>getFeaturedPractitioners),
    "getFooterCategories": (()=>getFooterCategories),
    "getGlobalSettings": (()=>getGlobalSettings),
    "getPractitionerBySlug": (()=>getPractitionerBySlug),
    "getPractitioners": (()=>getPractitioners),
    "getSpecialties": (()=>getSpecialties),
    "getSpecialtyBySlug": (()=>getSpecialtyBySlug),
    "revalidateAllContent": (()=>revalidateAllContent),
    "revalidateContent": (()=>revalidateContent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/strapi.ts [app-rsc] (ecmascript)");
;
;
;
// Performance monitoring
const ENABLE_CACHE_METRICS = process.env.NEXT_PUBLIC_CACHE_METRICS === 'true';
const cacheMetrics = {
    hits: 0,
    misses: 0,
    revalidations: 0,
    requestTimes: []
};
// Content categories with their respective cache durations based on environment
// Tiered caching approach with shorter times in development
const CONTENT_CACHE_CONFIG = {
    // Static/rarely changing content 
    'global-setting': ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 60,
    'categories': ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 300,
    'specialties': ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 300,
    'conditions': ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 300,
    // Semi-dynamic content
    'clinics': ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 180,
    'practitioners': ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 180,
    // Frequently changing content
    'blog-posts': ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 120,
    // Default fallback
    'default': ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 60 // 1 hour in prod, 1 min in dev
};
// Enhanced related content mapping for intelligent revalidation cascades
// This defines which content types should be revalidated together
const RELATED_CONTENT = {
    'blog-posts': [
        'blog-categories',
        'blog-tags',
        'blog-homepage',
        'homepage'
    ],
    'clinics': [
        'categories',
        'specialties',
        'homepage'
    ],
    'practitioners': [
        'specialties',
        'homepage'
    ],
    'categories': [
        'clinics'
    ],
    'specialties': [
        'clinics',
        'practitioners'
    ],
    'conditions': [
        'clinics',
        'practitioners'
    ]
};
// Stale-while-revalidate configuration (in seconds)
// This extends cache lifetime while fetching fresh data in background
const SWR_EXTENSION = {
    'global-setting': 86400,
    'categories': 86400,
    'specialties': 86400,
    'conditions': 86400,
    'clinics': 43200,
    'practitioners': 43200,
    'blog-posts': 21600,
    'default': 3600 // 1 hour stale extension
};
/**
 * Enhanced cache tag generation for more precise invalidation
 * Creates a hierarchical tag structure for granular cache control
 * 
 * @param contentType - The content type to generate tags for
 * @param id - Optional ID for entity-specific tags
 * @param additionalTags - Any extra tags to include
 * @returns Array of cache tags
 */ function generateCacheTags(contentType, id, additionalTags = []) {
    // Start with base content type tag
    const tags = [
        `strapi-${contentType}`
    ];
    // Add hierarchy tags for better organization and targeted invalidation
    if (contentType.includes('-')) {
        const [parent, child] = contentType.split('-');
        tags.push(`strapi-${parent}`);
        // For collection types, add a collection tag
        if (child === 'posts' || child === 'categories' || child === 'tags') {
            tags.push(`strapi-collection-${parent}`);
        }
    }
    // Add page-specific tag for paginated content
    if (id && !isNaN(Number(id)) && Number(id) > 0) {
        tags.push(`strapi-${contentType}-page-${id}`);
    } else if (id) {
        tags.push(`strapi-${contentType}-${id}`);
    }
    // Add additional custom tags
    if (additionalTags.length > 0) {
        tags.push(...additionalTags);
    }
    return tags;
}
function revalidateContent(contentType, id, options = {}) {
    const startTime = performance.now();
    const { revalidateRelated = true, reason = 'manual', priority = 'normal' } = options;
    // Revalidate the primary content
    const tags = generateCacheTags(contentType, id);
    // Log operation start for debugging
    if ("TURBOPACK compile-time truthy", 1) {
        console.log(`🔄 Revalidating ${contentType}${id ? ` (ID: ${id})` : ''} - Reason: ${reason}`);
    }
    // Execute revalidation
    tags.forEach((tag)=>{
        try {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(tag);
            if (ENABLE_CACHE_METRICS) {
                cacheMetrics.revalidations++;
            }
        } catch (error) {
            console.error(`Error revalidating tag ${tag}:`, error);
        }
    });
    // Revalidate related content types if specified
    if (revalidateRelated && contentType in RELATED_CONTENT) {
        const relatedTypes = RELATED_CONTENT[contentType];
        relatedTypes.forEach((relatedType)=>{
            // Skip high-volume related invalidations for low priority updates
            if (priority === 'low' && (relatedType === 'homepage' || relatedType === 'blog-homepage')) {
                return;
            }
            const relatedTags = generateCacheTags(relatedType);
            relatedTags.forEach((tag)=>{
                try {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(tag);
                    if (ENABLE_CACHE_METRICS) {
                        cacheMetrics.revalidations++;
                    }
                } catch (error) {
                    console.error(`Error revalidating related tag ${tag}:`, error);
                }
            });
        });
    }
    // Performance tracking
    if (ENABLE_CACHE_METRICS) {
        const duration = performance.now() - startTime;
        cacheMetrics.requestTimes.push(duration);
        // Log performance metrics periodically
        if (cacheMetrics.revalidations % 10 === 0) {
            const avgTime = cacheMetrics.requestTimes.reduce((sum, time)=>sum + time, 0) / cacheMetrics.requestTimes.length;
            console.log(`Cache metrics: ${cacheMetrics.revalidations} revalidations, avg time: ${avgTime.toFixed(2)}ms`);
        }
    }
    // Log completion
    if ("TURBOPACK compile-time truthy", 1) {
        console.log(`✅ Revalidated cache for ${contentType}${id ? ` (ID: ${id})` : ''} in ${(performance.now() - startTime).toFixed(2)}ms`);
    }
}
function revalidateAllContent(options = {}) {
    const startTime = performance.now();
    const { reason = 'manual', excludeTypes = [], highPriorityOnly = false } = options;
    // Content types organized by priority
    const highPriorityTypes = [
        'global-setting',
        'homepage',
        'blog-homepage'
    ];
    const mediumPriorityTypes = [
        'blog-posts',
        'clinics',
        'practitioners'
    ];
    const lowPriorityTypes = [
        'categories',
        'specialties',
        'conditions',
        'blog-categories',
        'blog-tags'
    ];
    // Determine which types to revalidate
    const typesToRevalidate = [
        ...highPriorityTypes,
        ...highPriorityOnly ? [] : [
            ...mediumPriorityTypes,
            ...lowPriorityTypes
        ]
    ].filter((type)=>!excludeTypes.includes(type));
    // Track already revalidated tags to avoid duplicates
    const revalidatedTags = new Set();
    // Log operation start
    console.log(`🔄 Starting batch revalidation (${reason}): ${typesToRevalidate.length} content types`);
    // Process each content type
    typesToRevalidate.forEach((contentType)=>{
        const tags = generateCacheTags(contentType);
        tags.forEach((tag)=>{
            // Skip if already revalidated in this batch
            if (!revalidatedTags.has(tag)) {
                try {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])(tag);
                    revalidatedTags.add(tag);
                    if (ENABLE_CACHE_METRICS) {
                        cacheMetrics.revalidations++;
                    }
                } catch (error) {
                    console.error(`Error in batch revalidation for tag ${tag}:`, error);
                }
            }
        });
    });
    // Log completion with performance metrics
    const duration = performance.now() - startTime;
    console.log(`✅ Batch revalidation complete. ${revalidatedTags.size} unique tags processed in ${duration.toFixed(2)}ms`);
}
/**
 * Enhanced cache configuration generator with stale-while-revalidate support
 * 
 * @param contentType - The content type to get cache config for
 * @param options - Optional configuration overrides
 * @returns Next.js cache configuration object
 */ function getCacheConfig(contentType, options) {
    // Determine revalidation time from options, type-specific config, or default
    const fallbackRevalidate = options?.revalidate !== undefined ? options.revalidate : contentType in CONTENT_CACHE_CONFIG ? CONTENT_CACHE_CONFIG[contentType] : CONTENT_CACHE_CONFIG.default;
    // Generate appropriate cache tags
    const defaultTags = generateCacheTags(contentType);
    const cacheTags = options?.tags || defaultTags;
    // Determine if we should use stale-while-revalidate
    const enableSWR = options?.staleWhileRevalidate !== false;
    // Configure stale-while-revalidate if enabled
    if (enableSWR && fallbackRevalidate !== false) {
        // Get SWR extension value (either from options, type-specific config, or default)
        const swrValue = typeof options?.staleWhileRevalidate === 'number' ? options.staleWhileRevalidate : contentType in SWR_EXTENSION ? SWR_EXTENSION[contentType] : SWR_EXTENSION.default;
        // Add Cache-Control header with stale-while-revalidate
        return {
            revalidate: fallbackRevalidate,
            tags: cacheTags,
            extraHeaders: {
                'Cache-Control': `public, max-age=${fallbackRevalidate}, stale-while-revalidate=${swrValue}`
            }
        };
    }
    // Basic config without SWR
    return {
        revalidate: fallbackRevalidate,
        tags: cacheTags
    };
}
/**
 * createOptimizedFetch: Higher-order function that wraps data fetching with advanced caching
 * 
 * @param fetcher - The actual data fetching function
 * @param options - Cache configuration options
 * @returns Cached function with the same signature as the original fetcher
 */ function createOptimizedFetch(fetcher, options) {
    // Use simplified version with fixed tags for better TypeScript compatibility
    const cacheTags = options.tags || [
        `strapi-${options.contentType}`
    ];
    // Determine revalidation time from options or config
    const revalidateTime = options.revalidate ?? CONTENT_CACHE_CONFIG[options.contentType] ?? CONTENT_CACHE_CONFIG.default;
    // Return a cached version of the fetcher
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unstable_cache"])(async (...args)=>{
        const startTime = ENABLE_CACHE_METRICS ? performance.now() : 0;
        try {
            const result = await fetcher(...args);
            // Track performance if enabled
            if (ENABLE_CACHE_METRICS && startTime) {
                const duration = performance.now() - startTime;
                cacheMetrics.requestTimes.push(duration);
                cacheMetrics.hits++;
            }
            return result;
        } catch (error) {
            if (ENABLE_CACHE_METRICS) {
                cacheMetrics.misses++;
            }
            throw error;
        }
    }, // Use simpler string keys to avoid type issues
    [
        `${options.contentType}`
    ], // Cache configuration with fixed tags
    {
        revalidate: revalidateTime,
        tags: cacheTags
    });
}
const getGlobalSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].global.getSettings({
        cache: 'force-cache',
        next: {
            revalidate: 3600,
            tags: [
                'strapi-global-setting'
            ]
        }
    });
});
const getCategories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    // Default to 1 week revalidation and specific tags if not provided
    const nextOptions = options?.next || {
        revalidate: 604800,
        tags: [
            'strapi-categories',
            'strapi-categories-all'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].categories.getAll({
        ...options,
        cache: 'force-cache',
        next: nextOptions
    });
});
const getFooterCategories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    // Default to 1 week revalidation and specific tags if not provided
    const nextOptions = options?.next || {
        revalidate: 604800,
        tags: [
            'strapi-categories',
            'strapi-categories-footer'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].categories.getFooterCategories({
        ...options,
        cache: 'force-cache',
        next: nextOptions
    });
});
const getSpecialties = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    // Default to 1 week revalidation and specific tags if not provided
    const nextOptions = options?.next || {
        revalidate: 604800,
        tags: [
            'strapi-specialties',
            'strapi-specialties-all'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].specialties.getAll({
        ...options,
        cache: 'force-cache',
        next: nextOptions
    });
});
const getClinics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options = {})=>{
    const nextOptions = options.next || {
        revalidate: 3600,
        tags: [
            'strapi-clinics',
            `page-${options.page || 1}`
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].clinics.getAll({
        ...options,
        next: nextOptions
    });
});
const getClinicBySlug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (slug, options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-clinics',
            `strapi-clinic-${slug}`
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].clinics.getBySlug(slug, {
        next: nextOptions
    });
});
const getFeaturedClinics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-clinics',
            'strapi-clinics-featured'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].clinics.getFeatured({
        next: nextOptions
    });
});
const getPractitioners = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options = {})=>{
    const nextOptions = options.next || {
        revalidate: 3600,
        tags: [
            'strapi-practitioners',
            `page-${options.page || 1}`
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].practitioners.getAll({
        ...options,
        next: nextOptions
    });
});
const getPractitionerBySlug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (slug, options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-practitioner',
            `strapi-practitioner-${slug}`
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].practitioners.getBySlug(slug, {
        next: nextOptions
    });
});
const getFeaturedPractitioners = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-practitioners',
            'strapi-practitioners-featured'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].practitioners.getFeatured({
        next: nextOptions
    });
});
const getBlogPosts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options = {})=>{
    const nextOptions = options.next || {
        revalidate: 3600,
        tags: [
            'strapi-blog-posts',
            `page-${options.page || 1}`
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].blog.getPosts({
        ...options,
        sort: options.sort || [
            'publishDate:desc'
        ],
        next: nextOptions
    });
});
const getBlogPostBySlug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (slug, options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-blog-posts',
            `strapi-blog-post-${slug}`
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].blog.getPostBySlug(slug, {
        next: nextOptions
    });
});
const getFeaturedBlogPosts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-blog-posts',
            'strapi-blog-posts-featured'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].blog.getPosts({
        filters: {
            isFeatured: {
                $eq: true
            }
        },
        sort: [
            'publishDate:desc'
        ],
        pagination: {
            page: 1,
            pageSize: 10
        },
        next: nextOptions
    });
});
const getBlogCategories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-blog-categories',
            'strapi-categories'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].blog.getCategories({
        next: nextOptions
    });
});
const getBlogTags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-blog-tags',
            'strapi-tags'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].blog.getTags({
        next: nextOptions
    });
});
const getBlogHomepage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-global-setting',
            'strapi-blog-homepage'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].global.getBlogHomepage({
        next: nextOptions
    });
});
const getSpecialtyBySlug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (slug, options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-specialties',
            `strapi-specialty-${slug}`
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].specialties.getBySlug(slug, {
        next: nextOptions
    });
});
const getConditionBySlug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (slug, options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-conditions',
            `strapi-condition-${slug}`
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].conditions.getBySlug(slug, {
        next: nextOptions
    });
});
const getConditions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-conditions',
            'strapi-conditions-all'
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].conditions.getAll({
        next: nextOptions
    });
});
const getCategoryBySlug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cache"])(async (slug, options)=>{
    const nextOptions = options?.next || {
        revalidate: 3600,
        tags: [
            'strapi-categories',
            `strapi-category-${slug}`
        ]
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$strapi$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getStrapiContent"].categories.getBySlug(slug, {
        next: nextOptions
    });
});
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_59dee874$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_59dee874.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$analytics$2f$dist$2f$next$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@vercel/analytics/dist/next/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ErrorContext$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ErrorContext.tsx [app-rsc] (ecmascript)");
// Remove direct fetchFromApiServer import, use cached functions instead
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/Layout.tsx [app-rsc] (ecmascript)"); // Import Layout component
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$QueryProvider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/QueryProvider.tsx [app-rsc] (ecmascript)"); // Updated import path for React Query Provider
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serverCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/serverCache.ts [app-rsc] (ecmascript)"); // Import cached functions
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$next$2f$third$2d$parties$2f$dist$2f$google$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@next/third-parties/dist/google/index.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
async function generateMetadata() {
    let faviconUrl = '/favicon.ico';
    let siteTitle = 'Natural Healing Now - Holistic Health Directory';
    let siteDescription = 'Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.';
    const strapiBaseUrl = ("TURBOPACK compile-time value", "http://localhost:1337");
    try {
        // Fetch global settings using the cached function with longer cache time
        const globalSettingsResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serverCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getGlobalSettings"])();
        if (globalSettingsResponse?.data) {
            let attributesData = null;
            // Handle both Strapi v4 and v5 response formats
            if ('attributes' in globalSettingsResponse.data && globalSettingsResponse.data.attributes) {
                attributesData = globalSettingsResponse.data.attributes;
            } else {
                attributesData = globalSettingsResponse.data;
            }
            if (attributesData) {
                // Process favicon data
                const faviconField = attributesData.favicon;
                const faviconActualMedia = faviconField?.data?.attributes ?? faviconField?.attributes ?? faviconField;
                if (faviconActualMedia?.url) {
                    const faviconPath = faviconActualMedia.url;
                    if (faviconPath && strapiBaseUrl) {
                        faviconUrl = faviconPath.startsWith('/') ? `${strapiBaseUrl}${faviconPath}` : faviconPath;
                    }
                }
                // Set title and description from global settings
                siteTitle = attributesData.siteName || siteTitle;
                siteDescription = attributesData.defaultSeoDescription || siteDescription;
            }
        }
    } catch (error) {
        console.error("Failed to fetch global settings for metadata:", error);
    // Use default values if the fetch fails
    }
    return {
        title: siteTitle,
        description: siteDescription,
        icons: {
            icon: faviconUrl,
            shortcut: faviconUrl,
            apple: faviconUrl
        }
    };
}
async function RootLayout({ children }) {
    let siteName = 'Natural Healing Now';
    let logoLight = null;
    let footerCategories = [];
    try {
        // Fetch global settings and footer categories in parallel using cached functions
        // These functions now use longer cache times (24 hours for global settings, 1 week for categories)
        // and explicit 'force-cache' option for Next.js 15 compatibility
        const [globalSettingsResponse, categoriesResponse] = await Promise.all([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serverCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getGlobalSettings"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serverCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getFooterCategories"])()
        ]);
        // Process global settings
        if (globalSettingsResponse?.data) {
            let attributesData = null;
            // Handle both Strapi v4 and v5 response formats
            if ('attributes' in globalSettingsResponse.data && globalSettingsResponse.data.attributes) {
                attributesData = globalSettingsResponse.data.attributes;
            } else {
                attributesData = globalSettingsResponse.data;
            }
            if (attributesData) {
                // Set site name from global settings
                siteName = attributesData.siteName || siteName;
                // Process logo data
                const logoField = attributesData.logoLight;
                const logoActualMedia = logoField?.data?.attributes ?? logoField?.attributes ?? logoField;
                if (logoActualMedia?.url) {
                    const mediaAttrs = logoActualMedia;
                    // Parse ID safely
                    let parsedId = typeof mediaAttrs.id === 'string' ? parseInt(mediaAttrs.id, 10) : typeof mediaAttrs.id === 'number' ? mediaAttrs.id : undefined;
                    if (parsedId !== undefined && isNaN(parsedId)) parsedId = undefined;
                    // Create logo object with all required properties
                    logoLight = {
                        id: parsedId || 0,
                        name: mediaAttrs.name || '',
                        alternativeText: mediaAttrs.alternativeText || siteName,
                        caption: mediaAttrs.caption,
                        width: mediaAttrs.width,
                        height: mediaAttrs.height,
                        formats: mediaAttrs.formats,
                        hash: mediaAttrs.hash || '',
                        ext: mediaAttrs.ext || '',
                        mime: mediaAttrs.mime || '',
                        size: mediaAttrs.size || 0,
                        url: mediaAttrs.url,
                        previewUrl: mediaAttrs.previewUrl,
                        provider: mediaAttrs.provider || 'local',
                        provider_metadata: mediaAttrs.provider_metadata,
                        createdAt: mediaAttrs.createdAt || '',
                        updatedAt: mediaAttrs.updatedAt || '',
                        publishedAt: mediaAttrs.publishedAt === null ? undefined : mediaAttrs.publishedAt
                    };
                }
            }
        }
        // Process categories for footer
        if (categoriesResponse?.data && Array.isArray(categoriesResponse.data)) {
            footerCategories = categoriesResponse.data.map((catEntry)=>{
                // Handle both Strapi v4 and v5 response formats
                let catAttributes;
                let catId;
                if ('attributes' in catEntry && catEntry.attributes) {
                    catAttributes = catEntry.attributes;
                    catId = catEntry.id;
                } else {
                    catAttributes = catEntry;
                    catId = catEntry.id;
                }
                return {
                    id: catId,
                    attributes: {
                        name: catAttributes.name || 'Unknown Category',
                        slug: catAttributes.slug || 'unknown-category'
                    }
                };
            }).filter(Boolean);
        } else if (categoriesResponse === null) {
            console.warn("Received null for categoriesResponse in RootLayout, likely due to fetch error.");
        }
    } catch (error) {
        console.error("Failed to fetch initial data in RootLayout:", error);
    // Use default values if the fetch fails
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        children: [
            process.env.NEXT_PUBLIC_GTM_ID && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$next$2f$third$2d$parties$2f$dist$2f$google$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GoogleTagManager"], {
                gtmId: process.env.NEXT_PUBLIC_GTM_ID
            }, void 0, false, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 249,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_59dee874$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].className,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ErrorContext$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ErrorProvider"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AuthProvider"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$QueryProvider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$Layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                    siteName: siteName,
                                    logoLight: logoLight,
                                    footerCategories: footerCategories,
                                    children: children
                                }, void 0, false, {
                                    fileName: "[project]/src/app/layout.tsx",
                                    lineNumber: 256,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/layout.tsx",
                                lineNumber: 254,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/layout.tsx",
                            lineNumber: 253,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 252,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vercel$2f$analytics$2f$dist$2f$next$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Analytics"], {}, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 262,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 251,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 247,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__924bfe84._.js.map