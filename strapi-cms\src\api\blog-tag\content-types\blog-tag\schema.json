{"kind": "collectionType", "collectionName": "blog_tags", "info": {"singularName": "blog-tag", "pluralName": "blog-tags", "displayName": "Blog Tag", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name"}, "blog_posts": {"type": "relation", "relation": "manyToMany", "target": "api::blog-post.blog-post", "inversedBy": "blog_tags"}}}