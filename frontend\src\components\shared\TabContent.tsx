'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import ClinicCard from '@/components/clinics/ClinicCard';
import PractitionerCard from '@/components/practitioners/PractitionerCard';
import ClientPagination from '@/components/shared/ClientPagination';

interface TransformedClinic {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  logo: string | null;
  featuredImage: string | null;
  address: {
    city: string;
    stateProvince: string;
  };
  contactInfo?: {
    phoneNumber?: string;
    websiteUrl?: string;
  } | null;
  isVerified?: boolean;
}

interface TransformedPractitioner {
  id: string;
  name: string;
  slug: string;
  title?: string | null;
  qualifications?: string | null;
  profilePicture: string | null;
  isVerified?: boolean;
}

type TabType = 'clinics' | 'practitioners';

interface TabContentProps {
  clinics: TransformedClinic[];
  practitioners: TransformedPractitioner[];
  totalPages: number;
  initialTab?: TabType;
}

/**
 * Client-side component that displays the content based on the active tab
 * This ensures proper re-rendering when the tab changes via client-side navigation
 */
export default function TabContent({
  clinics,
  practitioners,
  totalPages,
  initialTab = 'clinics'
}: TabContentProps) {
  const searchParams = useSearchParams();
  
  // Get the active tab from URL or use the initial tab
  const [activeTab, setActiveTab] = useState<TabType>(
    (searchParams.get('tab') === 'practitioners' ? 'practitioners' : 'clinics') || initialTab
  );

  // Update the active tab when the URL changes
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    const newActiveTab: TabType = tabParam === 'practitioners' ? 'practitioners' : 'clinics';
    
    if (newActiveTab !== activeTab) {
      setActiveTab(newActiveTab);
    }
  }, [searchParams, activeTab]);

  return (
    <div key={activeTab}>
      {activeTab === 'clinics' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {clinics.length > 0 ? (
            clinics.map((clinic) => (
              <ClinicCard key={clinic.id} clinic={clinic} showContactInfo={false} />
            ))
          ) : (
            <div className="col-span-full text-center py-8">
              <p className="text-gray-500">No clinics found matching your criteria.</p>
            </div>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {practitioners.length > 0 ? (
            practitioners.map((practitioner) => (
              <PractitionerCard key={practitioner.id} practitioner={practitioner} />
            ))
          ) : (
            <div className="col-span-full text-center py-8">
              <p className="text-gray-500">No practitioners found matching your criteria.</p>
            </div>
          )}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <ClientPagination totalPages={totalPages} />
        </div>
      )}
    </div>
  );
}
