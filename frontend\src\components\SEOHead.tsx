"use client";

import Head from 'next/head';
import { useEffect, useState } from 'react';
import { getOgImageUrl } from '@/lib/mediaUtils';

interface StrapiSEO {
  metaTitle?: string;
  metaDescription?: string;
  metaRobots?: string;
  structuredData?: string;
  metaViewport?: string;
  canonicalURL?: string;
  metaSocial?: Array<{
    socialNetwork: 'Facebook' | 'Twitter';
    title?: string;
    description?: string;
    image?: {
      data?: {
        attributes?: {
          url?: string;
        }
      }
    }
  }>;
  keywords?: string;
  metaImage?: {
    data?: {
      attributes?: {
        url?: string;
      }
    }
  };
}

interface SEOHeadProps {
  seo?: StrapiSEO;
  defaultTitle?: string;
  defaultDescription?: string;
  defaultOgImage?: string;
  pageType?: 'website' | 'article';
}

export default function SEOHead({
  seo,
  defaultTitle = 'Natural Healing Now - Holistic Health Directory',
  defaultDescription = 'Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.',
  defaultOgImage = '',
  pageType = 'website'
}: SEOHeadProps) {
  // Safely access nested properties with fallbacks
  const title = seo?.metaTitle || defaultTitle;
  const description = seo?.metaDescription || defaultDescription;
  const robots = seo?.metaRobots || 'index, follow';
  const canonicalUrl = seo?.canonicalURL || '';
  const structuredData = seo?.structuredData || '';

  // Social media specific metadata
  const fbSocial = seo?.metaSocial?.find(item => item.socialNetwork === 'Facebook');
  const twitterSocial = seo?.metaSocial?.find(item => item.socialNetwork === 'Twitter');

  // Get raw OG image URL from meta social if available, otherwise use standard meta image
  const rawOgImage = fbSocial?.image?.data?.attributes?.url
    || seo?.metaImage?.data?.attributes?.url
    || defaultOgImage;

  // Get the OG image URL - using direct Strapi media URL for better compatibility
  const ogImage = getOgImageUrl(rawOgImage);

  // Get raw Twitter image URL
  const rawTwitterImage = twitterSocial?.image?.data?.attributes?.url
    || seo?.metaImage?.data?.attributes?.url
    || defaultOgImage;

  // Get the Twitter image URL - using direct Strapi media URL for better compatibility
  const twitterImage = getOgImageUrl(rawTwitterImage);

  const ogTitle = fbSocial?.title || title;
  const ogDescription = fbSocial?.description || description;
  const twitterTitle = twitterSocial?.title || title;
  const twitterDescription = twitterSocial?.description || description;

  // Parse structured data if it exists
  const [parsedStructuredData, setParsedStructuredData] = useState<any>(null);

  useEffect(() => {
    if (structuredData) {
      try {
        const parsed = JSON.parse(structuredData);
        setParsedStructuredData(parsed);
      } catch (error) {
        console.error('Error parsing structured data:', error);
      }
    }
  }, [structuredData]);

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      {robots && <meta name="robots" content={robots} />}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}

      {/* OpenGraph / Facebook */}
      <meta property="og:type" content={pageType} />
      <meta property="og:title" content={ogTitle} />
      <meta property="og:description" content={ogDescription} />
      {ogImage && <meta property="og:image" content={ogImage} />}

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={twitterTitle} />
      <meta name="twitter:description" content={twitterDescription} />
      {twitterImage && <meta name="twitter:image" content={twitterImage} />}

      {/* JSON-LD Structured Data */}
      {parsedStructuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(parsedStructuredData) }}
        />
      )}
    </Head>
  );
}
