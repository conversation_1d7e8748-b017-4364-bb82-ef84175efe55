"use client";

import { useEffect, useRef } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { cachePractitioner, getCachedPractitioner } from '@/lib/clientCache';

interface PractitionerPrefetcherProps {
  practitioner: any;
}

/**
 * This component handles prefetching practitioner data
 * It stores the data in the client-side cache when a user visits a practitioner page
 * and retrieves it when they navigate back to the practitioners list
 */
const PractitionerPrefetcher = ({ practitioner }: PractitionerPrefetcherProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const hasCached = useRef(false);
  
  useEffect(() => {
    // Only cache once per component instance to avoid unnecessary work
    if (hasCached.current) return;
    
    // Store the practitioner data in the client-side cache
    if (practitioner && practitioner.id && practitioner.slug) {
      // Check if we already have this practitioner in the cache
      const cachedData = getCachedPractitioner(practitioner.slug);
      
      // Only cache if we don't already have it or if our data is more complete
      if (!cachedData || !cachedData._hasDetailedData) {
        // Mark this data as having detailed information
        const dataToCache = {
          ...practitioner,
          _hasDetailedData: true
        };
        
        cachePractitioner(dataToCache);
        hasCached.current = true;
      }
    }
    
    // If we're on a practitioner detail page and the prefetched flag is set,
    // update the URL to remove the flag (to avoid sharing URLs with the flag)
    if (pathname.includes('/practitioners/') && searchParams.get('prefetched') === 'true') {
      // Create a new URLSearchParams object
      const newParams = new URLSearchParams(searchParams.toString());
      // Remove the prefetched parameter
      newParams.delete('prefetched');
      // Replace the URL without the prefetched parameter
      const newPathname = newParams.toString() 
        ? `${pathname}?${newParams.toString()}` 
        : pathname;
      router.replace(newPathname, { scroll: false });
    }
  }, [practitioner, pathname, searchParams, router]);
  
  // This component doesn't render anything
  return null;
};

export default PractitionerPrefetcher;