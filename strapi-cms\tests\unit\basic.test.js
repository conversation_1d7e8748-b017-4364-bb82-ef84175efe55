/**
 * Basic unit tests that don't require a running Strapi server
 */

describe('Basic Tests', () => {
  // Test environment setup
  it('should have Je<PERSON> working correctly', () => {
    expect(1 + 1).toBe(2);
  });

  // Test environment variables
  it('should have test environment variables', () => {
    expect(process.env.NODE_ENV).toBe('test');
  });

  // Test mock objects
  it('should have access to the mock Strapi instance', () => {
    expect(global.strapi).toBeDefined();
    expect(global.strapi.server).toBeDefined();
    expect(global.strapi.server.httpServer).toBeDefined();
  });

  // Test test configuration
  it('should have test configuration', () => {
    expect(global.testConfig).toBeDefined();
    expect(global.testConfig.apiUrl).toBe('http://localhost:1337');
  });
});
