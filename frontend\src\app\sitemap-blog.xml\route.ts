import { getStrapiContent } from '@/lib/strapi';

// Define the site URL from environment variable
// We need an absolute URL for sitemaps to work properly
// For sitemaps, we should use the frontend URL, not the Strapi API URL
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log a warning if SITE_URL is not set
if (!process.env.NEXT_PUBLIC_SITE_URL && !process.env.NEXT_PUBLIC_API_URL) {
  console.warn('WARNING: Neither NEXT_PUBLIC_SITE_URL nor NEXT_PUBLIC_API_URL environment variables are set. Using default vercel.app URL as a placeholder.');
}

// Log the URL being used for debugging
console.log(`Using site URL for blog sitemap: ${SITE_URL}`);

// Ensure SITE_URL doesn't have a trailing slash
const normalizedSiteUrl = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;

// Define the page size for pagination - increased to handle more blog posts
const PAGE_SIZE = 500; // Increased from 100 to 500 to handle more blog posts in a single request

// Define the type for sitemap entries
interface SitemapEntry {
  url: string;
  lastModified: Date;
  changeFrequency: string;
  priority: number;
}

// This is a Route Handler for Next.js App Router
// See: https://nextjs.org/docs/app/building-your-application/routing/route-handlers
export async function GET(_request: Request): Promise<Response> {
  // Initialize the sitemap array
  let sitemapEntries: SitemapEntry[] = [];

  try {
    console.log('Generating blog sitemap...');

    // First, get the total count of blog posts
    const countResponse = await getStrapiContent.blog.getPosts({
      pagination: { pageSize: 1, page: 1 },
      fields: ['id', 'slug'], // Include slug for debugging
    });

    // Log the count response structure for debugging
    console.log('Count response structure:', JSON.stringify({
      hasData: !!countResponse?.data,
      dataIsArray: Array.isArray(countResponse?.data),
      dataLength: Array.isArray(countResponse?.data) ? countResponse.data.length : 'not an array',
      hasMeta: !!countResponse?.meta,
      hasPagination: !!countResponse?.meta?.pagination,
      totalItems: countResponse?.meta?.pagination?.total || 'unknown'
    }));

    // Try to get the total count from the pagination metadata
    let totalPosts = countResponse?.meta?.pagination?.total || 0;

    // If we couldn't get the total from pagination metadata, try to estimate it
    if (totalPosts === 0 && Array.isArray(countResponse?.data)) {
      // If we have data but no pagination metadata, make a second request to get all posts
      console.log('No pagination metadata found, fetching all posts to count them');

      try {
        const allPostsResponse = await getStrapiContent.blog.getPosts({
          pagination: { pageSize: 1000 }, // Use a large page size to get as many posts as possible
          fields: ['id'], // Only fetch IDs for counting
        });

        if (Array.isArray(allPostsResponse?.data)) {
          totalPosts = allPostsResponse.data.length;
          console.log(`Counted ${totalPosts} posts from direct data array`);
        }
      } catch (countError) {
        console.error('Error fetching all posts for counting:', countError);
      }
    }

    const totalPages = Math.ceil(totalPosts / PAGE_SIZE);

    console.log(`Found ${totalPosts} total blog posts, will fetch in ${totalPages} pages with page size ${PAGE_SIZE}`);
    console.log(`API URL being used: ${process.env.NEXT_PUBLIC_API_URL || 'Not set'}`);
    console.log(`Site URL being used: ${SITE_URL}`);

    // Log pagination details from the count response
    if (countResponse?.meta?.pagination) {
      console.log('Pagination details:', JSON.stringify(countResponse.meta.pagination));
    }

    // Fetch posts page by page
    for (let page = 1; page <= totalPages; page++) {
      console.log(`Fetching blog posts page ${page} of ${totalPages}...`);

      const postsResponse = await getStrapiContent.blog.getPosts({
        pagination: { pageSize: PAGE_SIZE, page },
        fields: ['slug', 'updatedAt', 'publishDate', 'createdAt'], // Only fetch necessary fields
        sort: 'publishDate:desc',
        publicationState: 'live', // Only fetch published posts
      });

      // Log pagination details from this response
      if (postsResponse?.meta?.pagination) {
        console.log(`Pagination details for page ${page}:`, JSON.stringify(postsResponse.meta.pagination));
      }

      const posts = postsResponse?.data || [];
      console.log(`Retrieved ${posts.length} blog posts on page ${page}`);

      // Log the structure of the first post for debugging
      if (posts.length > 0) {
        console.log('First post structure:', JSON.stringify({
          hasAttributes: !!posts[0].attributes,
          hasDirectSlug: !!posts[0].slug,
          attributesSlug: posts[0].attributes?.slug,
          keys: Object.keys(posts[0])
        }));
      } else {
        console.log('No posts found in the response');
        console.log('Full response:', JSON.stringify(postsResponse));
      }

      // Check if we got fewer posts than expected (except for the last page)
      if (posts.length < PAGE_SIZE && page < totalPages) {
        console.warn(`Warning: Got only ${posts.length} blog posts on page ${page}, expected ${PAGE_SIZE}. This may indicate a pagination issue.`);
      }

      const postRoutes = posts.map((post: any) => {
        // Handle both Strapi v4 and v5 response formats
        const attributes = post.attributes || post;

        // Try to get the slug from various possible locations
        let slug = null;
        if (attributes.slug) {
          slug = attributes.slug;
        } else if (post.slug) {
          slug = post.slug;
        } else if (post.documentId) {
          // If we have a documentId but no slug, log this anomaly
          console.log(`Post with documentId ${post.documentId} has no slug`);
        }

        // Get the last modified date from various possible fields
        let lastModified;
        if (attributes.updatedAt) {
          lastModified = new Date(attributes.updatedAt);
        } else if (attributes.publishDate) {
          lastModified = new Date(attributes.publishDate);
        } else if (attributes.createdAt) {
          lastModified = new Date(attributes.createdAt);
        } else if (post.updatedAt) {
          lastModified = new Date(post.updatedAt);
        } else if (post.publishDate) {
          lastModified = new Date(post.publishDate);
        } else if (post.createdAt) {
          lastModified = new Date(post.createdAt);
        } else {
          lastModified = new Date();
        }

        if (!slug) {
          console.log(`Post with no slug:`, JSON.stringify(post));
          return null;
        }

        return {
          url: `${normalizedSiteUrl}/blog/${slug}`,
          lastModified,
          changeFrequency: 'weekly',
          priority: 0.7,
        };
      }).filter(Boolean);

      // Add this batch of posts to the sitemap
      sitemapEntries = [...sitemapEntries, ...postRoutes];
    }

    // Add the blog index page
    sitemapEntries.unshift({
      url: `${normalizedSiteUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    });

    // Fetch blog categories
    const categoriesResponse = await getStrapiContent.blog.getCategories({
      pagination: { pageSize: 100 }
    });

    const categories = categoriesResponse?.data || [];

    const categoryEntries = categories.map((category: any) => {
      const attributes = category.attributes || category;
      const slug = attributes.slug || category.slug;

      if (!slug) return null;

      return {
        url: `${normalizedSiteUrl}/blog/categories/${slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.6,
      };
    }).filter(Boolean);

    // Add categories to the sitemap
    sitemapEntries = [...sitemapEntries, ...categoryEntries];

    // Fetch blog tags
    const tagsResponse = await getStrapiContent.blog.getTags();
    const tags = tagsResponse?.data || [];

    const tagEntries = tags.map((tag: any) => {
      const attributes = tag.attributes || tag;
      const slug = attributes.slug || tag.slug;

      if (!slug) return null;

      return {
        url: `${normalizedSiteUrl}/blog/tags/${slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.6,
      };
    }).filter(Boolean);

    // Add tags to the sitemap
    sitemapEntries = [...sitemapEntries, ...tagEntries];

    console.log(`Final sitemap entries count (including index page): ${sitemapEntries.length}`);

    // If we didn't get any blog posts but we know they exist, try a direct approach
    if (sitemapEntries.length <= 1 && totalPosts > 0) {
      console.log('No blog posts were added to the sitemap, trying direct approach');

      try {
        // Make a single request with a large page size
        const directResponse = await getStrapiContent.blog.getPosts({
          pagination: { pageSize: 1000 }, // Use a large page size to get as many posts as possible
          fields: ['slug', 'updatedAt', 'publishDate', 'createdAt'],
        });

        if (Array.isArray(directResponse?.data) && directResponse.data.length > 0) {
          console.log(`Retrieved ${directResponse.data.length} blog posts directly`);

          const directRoutes = directResponse.data.map((post: any) => {
            // Handle both Strapi v4 and v5 response formats
            const attributes = post.attributes || post;

            // Try to get the slug from various possible locations
            let slug = null;
            if (attributes.slug) {
              slug = attributes.slug;
            } else if (post.slug) {
              slug = post.slug;
            }

            if (!slug) return null;

            // Get the last modified date
            const lastModified = new Date(
              attributes.updatedAt || attributes.publishDate || attributes.createdAt ||
              post.updatedAt || post.publishDate || post.createdAt ||
              new Date()
            );

            return {
              url: `${normalizedSiteUrl}/blog/${slug}`,
              lastModified,
              changeFrequency: 'weekly',
              priority: 0.7,
            };
          }).filter(Boolean);

          // Add these directly retrieved posts to the sitemap
          sitemapEntries = [...sitemapEntries, ...directRoutes];
          console.log(`Added ${directRoutes.length} blog posts to sitemap via direct approach`);
        }
      } catch (directError) {
        console.error('Error in direct approach:', directError);
      }
    }

    // Log a summary of the sitemap generation process
    console.log(`Sitemap generation summary:
    - Total blog posts found: ${totalPosts}
    - Total pages fetched: ${totalPages}
    - Page size used: ${PAGE_SIZE}
    - Final sitemap entries: ${sitemapEntries.length}
    - Sitemap URL: ${normalizedSiteUrl}/sitemap-blog.xml
    `);

    // Convert the sitemap to XML
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${sitemapEntries.map((entry) => `
  <url>
    <loc>${entry.url}</loc>
    <lastmod>${entry.lastModified.toISOString()}</lastmod>
    <changefreq>${entry.changeFrequency}</changefreq>
    <priority>${entry.priority}</priority>
  </url>
  `).join('')}
</urlset>`;

    // Return the XML with the correct content type
    return new Response(xml, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Content-Type-Options': 'nosniff',
      },
    });
  } catch (error: any) {
    console.error('Error generating blog sitemap:', error);

    // Log more detailed error information
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Error request:', error.request);
    } else {
      console.error('Error message:', error.message);
    }

    // Log API URL and environment variables for debugging
    console.error('API URL:', process.env.NEXT_PUBLIC_API_URL);
    console.error('Site URL:', process.env.NEXT_PUBLIC_SITE_URL);

    // Return a basic XML in case of error
    const errorXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${normalizedSiteUrl}/blog</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;

    return new Response(errorXml, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'no-cache', // Don't cache error responses
        'X-Content-Type-Options': 'nosniff',
      },
    });
  }
}
