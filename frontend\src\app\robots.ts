import { MetadataRoute } from 'next';

// Define the site URL from environment variable
// We need an absolute URL for sitemaps to work properly
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log the URL being used for debugging
console.log(`Using site URL for robots.txt: ${SITE_URL}`);

// Ensure SITE_URL doesn't have a trailing slash
const normalizedSiteUrl = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;

export default function robots(): MetadataRoute.Robots {
  // Log that this function is being called to generate robots.txt
  console.log('Generating robots.txt from App Router robots.ts file');

  return {
    rules: {
      userAgent: '*',
      allow: '/',
    },
    sitemap: [
      `${normalizedSiteUrl}/sitemap.xml`,
      `${normalizedSiteUrl}/sitemap-index.xml`,
      `${normalizedSiteUrl}/sitemap-blog.xml`,
      `${normalizedSiteUrl}/sitemap-clinics.xml`,
      `${normalizedSiteUrl}/sitemap-practitioners.xml`,
    ],
    host: normalizedSiteUrl,
  };
}
