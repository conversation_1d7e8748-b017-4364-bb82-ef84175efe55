export type StrapiQueryParams = {
  filters?: Record<string, any>;
  populate?: Record<string, any> | string[] | string;
  fields?: string[];
  sort?: string[];
  pagination?: { page?: number; pageSize?: number; withCount?: boolean } | boolean;
};

export function buildQueryParams(params: StrapiQueryParams): URLSearchParams {
  const searchParams = new URLSearchParams();

  // Handle filters
  if (params.filters) {
    // Strapi expects filters to be stringified JSON for complex queries
    // For simple key-value, it can be direct, but JSON.stringify is safer for complex cases.
    // Example: filters[field][operator]=value
    // Using qs library is recommended for complex filter structures, but for simplicity here:
    Object.entries(params.filters).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        Object.entries(value).forEach(([operator, val]) => {
          searchParams.append(`filters[${key}][${operator}]`, String(val));
        });
      } else {
        searchParams.append(`filters[${key}]`, String(value));
      }
    });
  }

  // Handle population (deep and optimized)
  if (params.populate) {
    if (typeof params.populate === 'string') {
      searchParams.append('populate', params.populate);
    } else if (Array.isArray(params.populate)) {
      params.populate.forEach((item, index) => {
        searchParams.append(`populate[${index}]`, item);
      });
    } else {
      // For object-based population (deep population)
      // Strapi's qs parsing handles nested objects for populate
      // e.g. populate[relation][populate][nestedRelation]=true
      // A simple JSON.stringify might not be what Strapi expects here.
      // Strapi's `qs` library handles this by creating parameters like:
      // populate[relation][fields][0]=fieldA&populate[relation][populate][nestedRelation][fields][0]=nestedFieldB
      // For simplicity, we'll use a basic approach. For complex deep population,
      // a more robust qs-like stringifier or direct object passing (if supported by fetch) would be needed.
      // The `strapi-plugin-populate-deep` or careful manual construction is often used.
      // Let's assume a simplified string/array for now, or a flat object for direct populate.
      // If complex deep population is needed, this part needs to be more robust.
      // For now, let's stick to what was in the plan, which is a simplified JSON.stringify for objects.
      // This might need adjustment based on how Strapi parses complex populate objects.
      // The Strapi docs suggest qs for complex queries.
      // A common way is `populate[relation1][populate][subRelation]=*`
      // Or `populate[relation1][fields]=field1,field2`
      // Let's refine this to handle nested objects better for populate
      const populateToString = (obj: Record<string, any>, prefix = 'populate'): string[] => {
        let parts: string[] = [];
        Object.entries(obj).forEach(([key, value]) => {
          const newPrefix = prefix ? `${prefix}[${key}]` : key;
          if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
            if (value.hasOwnProperty('populate') || value.hasOwnProperty('fields') || value.hasOwnProperty('filters') || value.hasOwnProperty('sort')) {
               parts = parts.concat(populateToString(value, newPrefix));
            } else { // Default to populate all fields if it's an object without specific keys
                searchParams.append(newPrefix, '*');
            }
          } else if (Array.isArray(value)) {
             value.forEach((v, i) => searchParams.append(`${newPrefix}[${i}]`, String(v)) );
          }
          else if (value === true) {
            searchParams.append(newPrefix, '*'); // or specific fields if known
          } else if (value) {
            searchParams.append(newPrefix, String(value));
          }
        });
        return parts;
      };
      if (typeof params.populate === 'object' && !Array.isArray(params.populate)) {
        populateToString(params.populate);
      }
    }
  }

  // Handle field selection
  if (params.fields) {
    params.fields.forEach((field, index) => {
      searchParams.append(`fields[${index}]`, field);
    });
  }

  // Handle sorting
  if (params.sort) {
    params.sort.forEach((sortRule, index) => {
      searchParams.append(`sort[${index}]`, sortRule);
    });
  }

  // Handle pagination
  if (params.pagination) {
    if (typeof params.pagination === 'object') {
        if (params.pagination.page !== undefined) {
            searchParams.append('pagination[page]', String(params.pagination.page));
        }
        if (params.pagination.pageSize !== undefined) {
            searchParams.append('pagination[pageSize]', String(params.pagination.pageSize));
        }
        if (params.pagination.withCount !== undefined) {
            searchParams.append('pagination[withCount]', String(params.pagination.withCount));
        }
    }
    // if params.pagination is true, Strapi uses default pagination. No need to append.
  }

  return searchParams;
}
