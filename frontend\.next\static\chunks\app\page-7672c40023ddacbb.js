(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{2296:(e,t,l)=>{"use strict";l.d(t,{default:()=>f});var s=l(5155),a=l(2115),r=l(6772),i=l(2204),c=l(9981),n=l(927),d=l(9363),o=l(3227),m=l(6874),x=l.n(m),h=l(3199);let u=e=>{let{children:t,threshold:l=.1,rootMargin:r="200px",placeholder:i,disabled:c=!1,className:n="",id:d}=e,o=(0,a.useRef)(null),[m,x]=(0,a.useState)(c);return(0,a.useEffect)(()=>{if(c)return void x(!0);let e=o.current;if(!e)return;let t=new IntersectionObserver(l=>{let[s]=l;s.isIntersecting&&(x(!0),t.unobserve(e))},{threshold:l,rootMargin:r});return t.observe(e),()=>{e&&t.unobserve(e)}},[c,l,r]),(0,s.jsx)("div",{ref:o,className:n,id:d,children:m?t:i||null})},g=e=>{var t,l;if(!e)return null;let s="https://nice-badge-2130241d6c.strapiapp.com",a=(null==(t=e.author_blogs)?void 0:t[0])||null,r=(e=>{var t,l,a,r,i,c,n,d,o,m,x,h;if(!e)return null;let u=e=>e.includes("media.strapiapp.com")?e.startsWith("http")?e:"https://".concat(e):e.startsWith("http")?e:"".concat(s).concat(e.startsWith("/")?"":"/").concat(e),g=null;return(null==(t=e.profilePicture)?void 0:t.url)?g=u(e.profilePicture.url):(null==(r=e.profilePicture)||null==(a=r.data)||null==(l=a.attributes)?void 0:l.url)?g=u(e.profilePicture.data.attributes.url):(null==(n=e.profilePicture)||null==(c=n.formats)||null==(i=c.thumbnail)?void 0:i.url)?g=u(e.profilePicture.formats.thumbnail.url):(null==(h=e.profilePicture)||null==(x=h.data)||null==(m=x.attributes)||null==(o=m.formats)||null==(d=o.thumbnail)?void 0:d.url)&&(g=u(e.profilePicture.data.attributes.formats.thumbnail.url)),g})(a),i=a?{name:a.name||"Unknown Author",slug:a.slug||"",profile_picture:r}:null,c=null;if(null==(l=e.featuredImage)?void 0:l.url){let t=e.featuredImage.url;c=t.includes("media.strapiapp.com")?t.startsWith("http")?t:"https://".concat(t):t.startsWith("http")?t:"".concat(s).concat(t.startsWith("/")?"":"/").concat(t)}let n=e.id||e.documentId||"";return{id:n,title:e.title||"Untitled Post",slug:e.slug||"post-".concat(n),excerpt:e.excerpt||null,featured_image:c,publish_date:e.publishDate||e.createdAt||new Date().toISOString(),content:e.content||"",reading_time:e.readingTime||2,isFeatured:e.isFeatured||!1,view_count:e.view_count||0,author:i}},p=e=>e.includes("media.strapiapp.com")?e.startsWith("http")?e:"https://".concat(e):e.startsWith("http")?e:"".concat("https://nice-badge-2130241d6c.strapiapp.com").concat(e.startsWith("/")?"":"/").concat(e);function f(e){var t;let{initialData:l}=e,[m,f]=(0,a.useState)(""),[j,b]=(0,a.useState)(null),[v,N]=(0,a.useState)(!l),[y,w]=(0,a.useState)((null==l?void 0:l.clinics)||[]),[P,_]=(0,a.useState)((null==l?void 0:l.practitioners)||[]),[A,S]=(0,a.useState)((null==l?void 0:l.categories)||[]),[F,W]=(0,a.useState)((null==l||null==(t=l.featuredPosts)?void 0:t[0])||null),[C,k]=(0,a.useState)((null==l?void 0:l.latestPosts)||[]),[M,I]=(0,a.useState)((null==l?void 0:l.popularPosts)||[]);return(0,a.useEffect)(()=>{l||(N(!0),Promise.all([fetch("/api/clinics?filters[isFeatured][$eq]=true&populate=*").then(e=>e.json()),fetch("/api/practitioners?filters[isFeatured][$eq]=true&populate=*").then(e=>e.json()),fetch("/api/categories?pagination[pageSize]=4&populate=*").then(e=>e.json()),fetch("/api/blog-posts?sort=publishDate:desc&pagination[pageSize]=8&populate[featuredImage]=true&populate[author_blogs][populate][profilePicture]=true").then(e=>e.json())]).then(e=>{let[t,l,s,a]=e;w((null==t?void 0:t.data)||[]),_((null==l?void 0:l.data)||[]),S((null==s?void 0:s.data)||[]);let r=(null==a?void 0:a.data)||[],i=r.filter(e=>e.isFeatured).slice(0,1).map(g),c=r.slice(0,4).map(g).filter(e=>null!==e),n=r.slice(0,4).map(g).filter(e=>null!==e);W(i[0]||c[0]||null),k(c),I(n),b(null)}).catch(e=>{console.error("Error fetching homepage data:",e),b("Failed to load some data. Please try again later.")}).finally(()=>{N(!1)}))},[l]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("section",{className:"bg-gradient-to-r from-emerald-500 to-teal-600 text-white py-16 md:py-24",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Find Natural Healing Solutions Near You"}),(0,s.jsx)("p",{className:"text-xl mb-8",children:"Connect with holistic health practitioners and clinics to support your wellness journey."}),(0,s.jsx)("div",{className:"mb-8 max-w-2xl mx-auto",children:(0,s.jsx)(h.A,{placeholder:"Search for clinics, practitioners, or health topics...",onSearch:e=>{f(e),e.trim()&&(window.location.href="/search?q=".concat(encodeURIComponent(e)))},buttonText:"Search",buttonClassName:"bg-emerald-800 text-white hover:bg-emerald-900",className:"shadow-lg"})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(x(),{href:"/clinics",className:"bg-white text-emerald-600 hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold text-center",children:"Find a Clinic"}),(0,s.jsx)(x(),{href:"/practitioners",className:"bg-emerald-700 hover:bg-emerald-800 text-white px-6 py-3 rounded-lg font-semibold text-center",children:"Find a Practitioner"}),(0,s.jsx)(x(),{href:"/blog",className:"bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 rounded-lg font-semibold text-center",children:"Read Our Blog"})]})]})})}),(0,s.jsx)(u,{threshold:.1,rootMargin:"200px",placeholder:(0,s.jsx)("section",{className:"py-12 bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Explore Categories"}),(0,s.jsx)("div",{className:"w-32 h-6 bg-gray-200 rounded animate-pulse"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:[,,,,].fill(0).map((e,t)=>(0,s.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t))})]})}),children:(0,s.jsx)("section",{className:"py-12 bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Explore Categories"}),(0,s.jsx)(x(),{href:"/categories",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Categories"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:v?[,,,,].fill(0).map((e,t)=>(0,s.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t)):A.filter(e=>!!(null==e?void 0:e.id)).map(e=>{var t,l;return(0,s.jsx)(c.default,{category:{id:e.id,name:e.name||"Unnamed Category",slug:e.slug||"category-".concat(e.id),description:e.description,icon:(null==(t=e.icon)?void 0:t.url)?p(e.icon.url):null,featured_image:(null==(l=e.featuredImage)?void 0:l.url)?p(e.featuredImage.url):null}},e.id)})})]})})}),F&&!v&&(0,s.jsx)("section",{className:"py-16",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Article"}),(0,s.jsx)(x(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Articles"})]}),(0,s.jsx)(d.A,{post:F,badgeType:F.isFeatured?"featured":"recent"})]})}),(0,s.jsx)(u,{threshold:.1,rootMargin:"200px",placeholder:(0,s.jsx)("section",{className:"py-16",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Clinics"}),(0,s.jsx)("div",{className:"w-32 h-6 bg-gray-200 rounded animate-pulse"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[,,,].fill(0).map((e,t)=>(0,s.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t))})]})}),children:(0,s.jsx)("section",{className:"py-16",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Clinics"}),(0,s.jsx)(x(),{href:"/clinics",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Clinics"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:v?[,,,].fill(0).map((e,t)=>(0,s.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t)):y.length>0?y.slice(0,3).map(e=>(0,s.jsx)(r.default,{clinic:e},e.id)):(0,s.jsx)("div",{className:"col-span-3 text-center py-8",children:(0,s.jsx)("p",{className:"text-gray-500",children:"No featured clinics available at the moment."})})})]})})}),(0,s.jsx)(u,{threshold:.1,rootMargin:"200px",placeholder:(0,s.jsx)("section",{className:"py-16 bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Practitioners"}),(0,s.jsx)("div",{className:"w-32 h-6 bg-gray-200 rounded animate-pulse"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[,,,,].fill(0).map((e,t)=>(0,s.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t))})]})}),children:(0,s.jsx)("section",{className:"py-16 bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Featured Practitioners"}),(0,s.jsx)(x(),{href:"/practitioners",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Practitioners"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:v?[,,,,].fill(0).map((e,t)=>(0,s.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"},t)):P.length>0?P.slice(0,4).map(e=>(0,s.jsx)(i.default,{practitioner:e},e.id)):(0,s.jsx)("div",{className:"col-span-4 text-center py-8",children:(0,s.jsx)("p",{className:"text-gray-500",children:"No featured practitioners available at the moment."})})})]})})}),C.length>0&&!v&&(0,s.jsx)(u,{threshold:.1,rootMargin:"200px",placeholder:(0,s.jsx)("section",{className:"py-16",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Latest Articles"}),(0,s.jsx)("div",{className:"w-32 h-6 bg-gray-200 rounded animate-pulse"})]}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,s.jsx)("div",{className:"lg:w-2/3",children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[,,].fill(0).map((e,t)=>(0,s.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg animate-pulse"},t))})}),(0,s.jsxs)("div",{className:"lg:w-1/3 space-y-6",children:[(0,s.jsx)("div",{className:"h-64 bg-gray-200 rounded-lg animate-pulse"}),(0,s.jsx)("div",{className:"h-48 bg-gray-200 rounded-lg animate-pulse"})]})]})]})}),children:(0,s.jsx)("section",{className:"py-16",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800",children:"Latest Articles"}),(0,s.jsx)(x(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"View All Articles"})]}),(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,s.jsx)("div",{className:"lg:w-2/3",children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:C.slice(0,2).map(e=>(0,s.jsx)(n.default,{post:e,showReadingTime:!0,showShareButton:!0,showBadge:!0},e.id))})}),(0,s.jsxs)("div",{className:"lg:w-1/3 space-y-6",children:[M.length>0&&(0,s.jsx)(o.A,{posts:M}),(0,s.jsxs)("div",{className:"bg-emerald-50 rounded-lg shadow-sm p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"Stay Updated"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Get the latest natural healing tips and articles delivered to your inbox."}),(0,s.jsx)(x(),{href:"/newsletter",className:"inline-block bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm",children:"Subscribe to Newsletter"})]})]})]})]})})}),(0,s.jsx)("section",{className:"py-16 bg-emerald-600 text-white",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Ready to Start Your Wellness Journey?"}),(0,s.jsx)("p",{className:"text-xl mb-8 max-w-3xl mx-auto",children:"Discover holistic health practitioners and clinics that can help you achieve optimal health and well-being."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(x(),{href:"/categories",className:"bg-white text-emerald-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold inline-block",children:"Explore Health Categories"}),(0,s.jsx)(x(),{href:"/blog",className:"bg-emerald-700 hover:bg-emerald-800 text-white px-8 py-3 rounded-lg font-semibold inline-block",children:"Read Health Articles"})]})]})}),j&&(0,s.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6 mx-auto max-w-4xl",children:(0,s.jsx)("span",{className:"block sm:inline",children:j})})]})}},2940:(e,t,l)=>{Promise.resolve().then(l.bind(l,2296))},3227:(e,t,l)=>{"use strict";l.d(t,{A:()=>o});var s=l(5155),a=l(9907),r=l(6874),i=l.n(r),c=l(3319),n=l(2112),d=l(351);let o=e=>{let{posts:t}=e;return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Popular Articles"}),(0,s.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-16 h-16 relative rounded overflow-hidden",children:e.featured_image?(0,s.jsx)(a.default,{src:(0,n.Jf)(e.featured_image),alt:e.title,width:64,height:64,fillContainer:!0,className:"object-cover",sizes:"64px",showPlaceholder:!0}):(0,s.jsx)("div",{className:"w-full h-full bg-emerald-100 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-emerald-700",children:e.title.charAt(0)})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-800 line-clamp-2",children:(0,s.jsx)(i(),{href:"/blog/".concat(e.slug),className:"hover:text-emerald-600",children:e.title})}),(0,s.jsxs)("div",{className:"flex items-center text-xs text-gray-500 mt-1",children:[e.author&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.JXP,{className:"text-emerald-600 mr-1.5"}),(0,s.jsx)(i(),{href:"/blog/authors/".concat(e.author.slug),className:"hover:text-emerald-600 mr-2",children:e.author.name}),(0,s.jsx)("span",{className:"mx-1",children:"•"})]}),(0,c.GP)(new Date(e.publish_date),"MMM d, yyyy")]})]})]},e.id))})]})}},9363:(e,t,l)=>{"use strict";l.d(t,{A:()=>o});var s=l(5155),a=l(9907),r=l(6874),i=l.n(r),c=l(3319),n=l(351),d=l(2112);let o=e=>{var t,l;let{post:r,badgeType:o="featured"}=e,m=(0,d.Jf)(r.featured_image),x=!!r.featured_image,h=(0,d.Jf)(null==(t=r.author)?void 0:t.profile_picture);null==(l=r.author)||l.profile_picture;let u=r.reading_time||(r.content?(e=>{var t;return Math.max(1,Math.ceil(((null==e||null==(t=e.split(/\s+/))?void 0:t.length)||0)/200))})(r.content):2),g=h&&(h.startsWith("http")||h.startsWith("/")||h.startsWith("data:")||window.location.origin&&!h.startsWith("http"))?h:"",p=(0,c.GP)(new Date(r.publish_date),"MMMM d, yyyy");return(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-md overflow-hidden transition-transform hover:shadow-lg",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row",children:[(0,s.jsxs)("div",{className:"md:w-1/2 relative h-64 md:h-auto",children:[x?(0,s.jsx)(a.default,{src:m,alt:r.title||"Featured post image",width:800,height:600,fillContainer:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,showPlaceholder:!0}):(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-100 to-teal-200 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-emerald-700 font-semibold text-2xl opacity-50",children:r.title.charAt(0)})}),(0,s.jsx)("div",{className:"absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium ".concat("featured"===o?"bg-emerald-600 text-white":"popular"===o?"bg-amber-500 text-white":"bg-blue-500 text-white"),children:"featured"===o?"Featured Post":"popular"===o?"Popular Post":"Latest Post"})]}),(0,s.jsxs)("div",{className:"md:w-1/2 p-6 md:p-8 flex flex-col",children:[(0,s.jsxs)("div",{className:"flex flex-wrap gap-3 text-sm text-gray-500 mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.wIk,{className:"mr-1"}),(0,s.jsx)("span",{children:p})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.Ohp,{className:"mr-1"}),(0,s.jsxs)("span",{children:[u," min read"]})]})]}),(0,s.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-3",children:(0,s.jsx)(i(),{href:"/blog/".concat(r.slug),className:"hover:text-emerald-600",children:r.title})}),r.excerpt&&(0,s.jsx)("p",{className:"text-gray-600 mb-6 line-clamp-3 md:line-clamp-4",children:r.excerpt}),(0,s.jsxs)("div",{className:"mt-auto flex items-center justify-between",children:[r.author&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-200 shadow-sm",children:g&&(e=>{try{return e&&(e.startsWith("http")||e.startsWith("/")||e.startsWith("data:"))}catch(e){return!1}})(g)?(0,s.jsx)(a.default,{src:g,alt:r.author.name||"Author image",width:40,height:40,fillContainer:!0,className:"object-cover rounded-full",sizes:"40px",priority:!1,showPlaceholder:!0}):(0,s.jsx)("div",{className:"absolute inset-0 bg-emerald-100 flex items-center justify-center",children:(0,s.jsx)(n.JXP,{className:"text-emerald-700 text-lg"})})}),(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 mb-0.5",children:"Written by"}),(0,s.jsx)(i(),{href:"/blog/authors/".concat(r.author.slug),className:"font-medium text-gray-800 hover:text-emerald-600",children:r.author.name})]})]}),(0,s.jsxs)(i(),{href:"/blog/".concat(r.slug),className:"inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm",children:["Read Article ",(0,s.jsx)("span",{className:"ml-1",children:"→"})]})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[844,5479,6874,3063,6079,2112,927,2773,8441,1684,7358],()=>t(2940)),_N_E=e.O()}]);