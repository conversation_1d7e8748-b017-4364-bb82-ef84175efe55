'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import ClinicCard from '@/components/clinics/ClinicCard';
import Pagination from '@/components/shared/Pagination';
import { getStrapiContent } from '@/lib/strapi';

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL;

// Define types for clinic data (copied and adapted from original page.tsx)
interface RawStrapiClinic {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  logo?: { url?: string; } | null;
  featuredImage?: { url?: string; } | null;
  address: {
    city: string;
    stateProvince: string;
    streetAddress1?: string;
    streetAddress?: string;
    postalCode?: string;
    country?: string;
  };
  contactInfo?: {
    phoneNumber?: string;
    websiteUrl?: string;
    emailAddress?: string;
  } | null;
  isVerified?: boolean;
  location?: {
    googlePlaceId?: string | null;
    latitude?: number | null;
    longitude?: number | null;
  } | null;
  openingHours?: Array<{
    id: string | number;
    day: string;
    openTime?: string | null;
    closeTime?: string | null;
    closed?: boolean | null;
  }> | null;
  services?: Array<{ id: string; name: string; slug: string }> | null;
  specialties?: Array<{ id: string; name: string; slug: string }> | null;
  conditions?: Array<{ id: string; name: string; slug: string }> | null;
  practitioners?: Array<{
    id: string;
    name: string;
    slug: string;
    title?: string;
    profilePicture?: { url?: string } | null;
  }> | null;
  appointment_options?: Array<{ id: string; name: string; slug: string }> | null;
  payment_methods?: Array<{ id: string; name: string; slug: string }> | null;
  seo?: {
    metaTitle?: string | null;
    metaDescription?: string | null;
    metaImage?: { url?: string } | null;
    openGraph?: {
      ogTitle?: string | null;
      ogDescription?: string | null;
      ogImage?: { url?: string } | null;
      ogUrl?: string | null;
      ogType?: string | null;
    } | null;
    structuredData?: string | object | null;
  } | null;
}

interface TransformedClinic {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  logo: string | null;
  featuredImage: string | null;
  address: {
    city: string;
    stateProvince: string;
    streetAddress1?: string;
    streetAddress?: string;
    postalCode?: string;
    country?: string;
  };
  contactInfo?: {
    phoneNumber?: string;
    websiteUrl?: string;
    emailAddress?: string;
  } | null;
  isVerified?: boolean;
  location?: {
    googlePlaceId?: string | null;
    latitude?: number | null;
    longitude?: number | null;
  } | null;
  openingHours?: Array<{
    id: string | number;
    day: string;
    openTime?: string | null;
    closeTime?: string | null;
    closed?: boolean | null;
  }> | null;
  services?: Array<{ id: string; name: string; slug: string }> | null;
  specialties?: Array<{ id: string; name: string; slug: string }> | null;
  conditions?: Array<{ id: string; name: string; slug: string }> | null;
  practitioners?: Array<{
    id: string;
    name: string;
    slug: string;
    title?: string;
    profilePicture?: string | null;
  }> | null;
  appointment_options?: Array<{ id: string; name: string; slug: string }> | null;
  payment_methods?: Array<{ id: string; name: string; slug: string }> | null;
  seo?: {
    metaTitle?: string | null;
    metaDescription?: string | null;
    metaImage?: string | null;
    openGraph?: {
      ogTitle?: string | null;
      ogDescription?: string | null;
      ogImage?: string | null;
      ogUrl?: string | null;
      ogType?: string | null;
    } | null;
    structuredData?: string | object | null;
  } | null;
}

// Props for the ClinicsList component, now receiving data directly
interface ClinicsListProps {
  clinicsToDisplay: TransformedClinic[]; // Changed from initialClinics
  totalPages: number;                   // Changed from initialTotalPages
  totalCount: number;                   // Changed from initialTotalCount
  currentPage: number;                  // Added currentPage for Pagination
}

interface StrapiApiResponse<T> { // This might not be needed here anymore if no fetching
  data: T[];
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

const getStrapiMediaUrl = (url: string | undefined | null): string | null => {
  if (!url) return null;
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return `${STRAPI_URL}${url}`;
};

function transformClinicData(rawClinic: RawStrapiClinic): TransformedClinic | null {
  if (!rawClinic || !rawClinic.id || !rawClinic.name) {
    console.warn(`Skipping clinic with missing ID or Name: ID ${rawClinic?.id}`);
    return null;
  }

  const transformedPractitioners = rawClinic.practitioners?.map(practitioner => ({
    ...practitioner,
    profilePicture: practitioner.profilePicture?.url ? getStrapiMediaUrl(practitioner.profilePicture.url) : null
  })) || null;

  const transformedSeo = rawClinic.seo ? {
    ...rawClinic.seo,
    metaImage: rawClinic.seo.metaImage?.url ? getStrapiMediaUrl(rawClinic.seo.metaImage.url) : null,
    openGraph: rawClinic.seo.openGraph ? {
      ...rawClinic.seo.openGraph,
      ogImage: rawClinic.seo.openGraph.ogImage?.url ? getStrapiMediaUrl(rawClinic.seo.openGraph.ogImage.url) : null
    } : null
  } : null;

  return {
    id: String(rawClinic.id),
    name: rawClinic.name || 'Unnamed Clinic',
    slug: rawClinic.slug || `clinic-${rawClinic.id}`,
    description: rawClinic.description,
    logo: getStrapiMediaUrl(rawClinic.logo?.url),
    featuredImage: getStrapiMediaUrl(rawClinic.featuredImage?.url),
    address: rawClinic.address || { city: 'Unknown', stateProvince: 'N/A' },
    contactInfo: rawClinic.contactInfo,
    isVerified: rawClinic.isVerified || false,
    location: rawClinic.location,
    openingHours: rawClinic.openingHours,
    services: rawClinic.services,
    specialties: rawClinic.specialties,
    conditions: rawClinic.conditions,
    practitioners: transformedPractitioners,
    appointment_options: rawClinic.appointment_options,
    payment_methods: rawClinic.payment_methods,
    seo: transformedSeo
  };
} // Ensure transformClinicData is properly closed

// This is the main component that will be imported into page.tsx
// It now directly receives display data from the server component.
export default function ClinicsList({ 
  clinicsToDisplay, 
  totalPages, 
  totalCount,
  currentPage // Receive currentPage for Pagination
}: ClinicsListProps) {
  // This component no longer fetches data or manages loading/error states for the list.
  // It simply renders the data provided by the parent server component.
  // The SearchInput component will still update URL params, causing the parent
  // server component (ClinicsPage) to re-fetch and pass new props here.

  // Loading state for the list itself is implicitly handled by the server component's rendering.
  // If ClinicsPage is rendering, it means data is fetched or being fetched.
  // A Suspense boundary around SearchInput in page.tsx handles its own loading.

  // Error state for the list should be handled by ClinicsPage or a global error boundary.
  // If clinicsToDisplay is empty due to a server-side fetch error,
  // ClinicsPage might pass an empty array or handle it before rendering this.

  if (!clinicsToDisplay) {
    // This case should ideally be handled by the parent server component
    // (e.g., by not rendering ClinicsList or showing an error message there)
    // but as a fallback:
    return <div className="text-center py-8 text-gray-500">Loading clinics or no data available...</div>;
  }
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          {totalCount} Clinics Found
        </h2>
        <div className="flex items-center gap-2">
          <span className="text-gray-600">Sort by:</span>
          <select className="border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
            <option>Relevance</option>
            <option>Distance</option>
            <option>Name (A-Z)</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {clinicsToDisplay.length > 0 ? (
          clinicsToDisplay.map((clinic: TransformedClinic) => (
            <ClinicCard
              key={clinic.id}
              clinic={clinic}
              showContactInfo={false} // Assuming this is still desired for list view
              prefetchedData={true} // Data is now always "pre-fetched" by server component
            />
          ))
        ) : (
          <div className="col-span-3 text-center py-8">
            <p className="text-gray-500">No clinics found. Please try adjusting your search criteria.</p>
          </div>
        )}
      </div>

      {totalPages > 1 && (
        <div className="mt-12 flex justify-center">
          {/* Pagination now uses currentPage passed as prop */}
          <Pagination totalPages={totalPages} currentPage={currentPage} />
        </div>
      )}
    </div>
  );
}
// Removed the wrapper ClinicsList component, the default export is now the display component.
// The logic for useSearchParams is now handled by the parent server component (ClinicsPage).
