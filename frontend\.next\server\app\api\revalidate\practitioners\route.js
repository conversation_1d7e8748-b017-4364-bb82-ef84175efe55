(()=>{var e={};e.id=2221,e.ids=[2221],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57590:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>R,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>v});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>d});var o=r(96559),s=r(48088),i=r(37719),n=r(32190),l=r(62351);let c=process.env.STRAPI_REVALIDATE_SECRET||process.env.REVALIDATE_TOKEN||process.env.PREVIEW_SECRET;async function d(e){if(!c)return console.error("CRITICAL: STRAPI_REVALIDATE_SECRET, REVALIDATE_TOKEN, or PREVIEW_SECRET is not set. Revalidation endpoint for practitioners is disabled."),n.NextResponse.json({message:"Revalidation secret not configured."},{status:500});if((e.headers.get("X-Revalidate-Secret")||e.headers.get("Authorization")?.replace("Bearer ","")||e.headers.get("x-webhook-secret"))!==c)return console.warn("Invalid practitioner revalidation attempt: Incorrect or missing authentication header."),console.log("Received headers:",Object.fromEntries(e.headers.entries())),n.NextResponse.json({message:"Invalid token"},{status:401});try{let t;console.log("Received webhook request for practitioners revalidation");try{t=await e.json(),console.log("Webhook payload:",JSON.stringify(t,null,2))}catch(e){return console.error("Failed to parse webhook payload:",e),n.NextResponse.json({message:"Invalid JSON payload"},{status:400})}let{event:r,model:a,entry:o}=t;if(console.log("Webhook event:",r),console.log("Webhook model:",a),console.log("Webhook entry:",o?`ID: ${o.id}`:"No entry data"),!("practitioner"===a||"api::practitioner.practitioner"===a||"string"==typeof a&&a.includes("practitioner")))return console.log(`Revalidation skipped: Not a practitioner model. Received: ${a}`),n.NextResponse.json({message:`Revalidation skipped: Not a practitioner model. Received: ${a}`},{status:200});let s=[];if(o&&o.slug){let e=o.slug,t=`strapi-practitioner-${e}`;s.push(t),console.log(`Adding tag for specific practitioner: ${t} (slug: ${e})`)}s.push("strapi-practitioners-slugs"),console.log("Adding tag: strapi-practitioners-slugs"),s.push("strapi-practitioners-list"),console.log("Adding tag: strapi-practitioners-list");for(let e=1;e<=10;e++)s.push(`strapi-practitioners-page-${e}`);if(console.log("Adding tags for paginated practitioner pages (1-10)"),!(s.length>0))return console.log("No tags to revalidate."),n.NextResponse.json({revalidated:!1,message:"No tags to revalidate."});for(let e of(console.log("Revalidating tags:",s.join(", ")),s))try{(0,l.revalidateTag)(e),console.log(`Successfully revalidated tag: ${e}`)}catch(t){console.error(`Error revalidating tag ${e}:`,t)}try{(0,l.revalidatePath)("/practitioners"),console.log("Successfully revalidated path: /practitioners")}catch(e){console.error("Error revalidating path /practitioners:",e)}return n.NextResponse.json({revalidated:!0,revalidatedTags:s,revalidatedPaths:["/practitioners"],timestamp:new Date().toISOString()})}catch(e){return console.error("Error during practitioners revalidation:",e),n.NextResponse.json({message:"Error revalidating practitioners",error:e.message},{status:500})}}async function p(e){if(!c)return n.NextResponse.json({message:"Revalidation secret not configured."},{status:500});let t=e.headers.get("X-Revalidate-Secret"),r=new URL(e.url),a=r.searchParams.get("tag");if(t!==c&&r.searchParams.get("secret")!==c)return console.warn("Invalid GET practitioner revalidation attempt: Incorrect or missing X-Revalidate-Secret header/secret query param."),n.NextResponse.json({message:"Invalid token"},{status:401});if(!a)return n.NextResponse.json({message:"Missing tag parameter for GET revalidation"},{status:400});try{return(0,l.revalidateTag)(a),console.log(`Manual practitioner revalidation successful for tag: ${a}`),n.NextResponse.json({revalidated:!0,revalidatedTag:a,timestamp:new Date().toISOString()})}catch(e){return console.error(`Error during manual practitioner revalidation for tag ${a}:`,e),n.NextResponse.json({message:"Error revalidating practitioner tag",error:e.message},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/revalidate/practitioners/route",pathname:"/api/revalidate/practitioners",filename:"route",bundlePath:"app/api/revalidate/practitioners/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\revalidate\\practitioners\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:u,workUnitAsyncStorage:v,serverHooks:R}=g;function h(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:v})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,6391,580],()=>r(57590));module.exports=a})();