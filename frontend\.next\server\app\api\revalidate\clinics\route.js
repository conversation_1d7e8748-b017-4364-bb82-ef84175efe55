(()=>{var e={};e.id=3441,e.ids=[3441],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63461:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>v});var r={};t.r(r),t.d(r,{GET:()=>p,POST:()=>d});var a=t(96559),i=t(48088),o=t(37719),n=t(32190),l=t(62351);let c=process.env.STRAPI_REVALIDATE_SECRET||process.env.REVALIDATE_TOKEN||process.env.PREVIEW_SECRET;async function d(e){let s,t;if(!c)return console.error("CRITICAL: STRAPI_REVALIDATE_SECRET, REVALIDATE_TOKEN, or PREVIEW_SECRET is not set. Revalidation endpoint is disabled."),n.NextResponse.json({message:"Revalidation secret not configured."},{status:500});let r=e.headers.get("X-Revalidate-Secret")||e.headers.get("x-revalidate-secret")||e.headers.get("Authorization")?.replace("Bearer ","")||e.headers.get("authorization")?.replace("Bearer ","")||e.headers.get("x-webhook-secret");try{let r=e.clone();s=(t=await r.json()).token||t.secret,console.log("Webhook payload:",JSON.stringify(t,null,2))}catch(e){console.log("No JSON body or failed to parse body"),t={}}let a=new URL(e.url).searchParams.get("secret");if((r||s||a)!==c)return console.warn("Invalid revalidation attempt: Incorrect or missing authentication."),console.log("Received headers:",Object.fromEntries(e.headers.entries())),n.NextResponse.json({message:"Invalid token"},{status:401});try{console.log("Received webhook request for clinics revalidation");let{event:e,model:s,entry:r}=t||{};if(console.log("Webhook event:",e),console.log("Webhook model:",s),console.log("Webhook entry:",r?`ID: ${r.id}`:"No entry data"),!(!s||"clinic"===s||"api::clinic.clinic"===s||"string"==typeof s&&s.includes("clinic"))&&s)return console.log(`Revalidation skipped: Not a clinic model. Received: ${s}`),n.NextResponse.json({message:`Revalidation skipped: Not a clinic model. Received: ${s}`},{status:200});let a=[];if(r&&r.slug){let e=r.slug,s=`strapi-clinic-${e}`;a.push(s),console.log(`Adding tag for specific clinic: ${s} (slug: ${e})`)}a.push("strapi-clinics-slugs"),console.log("Adding tag: strapi-clinics-slugs"),a.push("strapi-clinics-list"),console.log("Adding tag: strapi-clinics-list");for(let e=1;e<=10;e++)a.push(`strapi-clinics-page-${e}`);if(console.log("Adding tags for paginated clinic pages (1-10)"),a.push("strapi-specialties"),a.push("strapi-conditions"),a.push("strapi-clinics-locations"),console.log("Adding tags for specialties, conditions, and locations"),!(a.length>0))return console.log("No tags to revalidate."),n.NextResponse.json({revalidated:!1,message:"No tags to revalidate."});for(let e of(console.log("Revalidating tags:",a.join(", ")),a))try{(0,l.revalidateTag)(e),console.log(`Successfully revalidated tag: ${e}`)}catch(s){console.error(`Error revalidating tag ${e}:`,s)}try{(0,l.revalidatePath)("/clinics"),console.log("Successfully revalidated path: /clinics"),r&&r.slug&&((0,l.revalidatePath)(`/clinics/${r.slug}`),console.log(`Successfully revalidated path: /clinics/${r.slug}`))}catch(e){console.error("Error revalidating paths:",e)}return n.NextResponse.json({revalidated:!0,revalidatedTags:a,revalidatedPaths:r&&r.slug?["/clinics",`/clinics/${r.slug}`]:["/clinics"],timestamp:new Date().toISOString()})}catch(e){return console.error("Error during clinics revalidation:",e),n.NextResponse.json({message:"Error revalidating clinics",error:e.message},{status:500})}}async function p(e){if(!c)return n.NextResponse.json({message:"Revalidation secret not configured."},{status:500});let s=e.headers.get("X-Revalidate-Secret")||e.headers.get("x-revalidate-secret"),t=new URL(e.url),r=t.searchParams.get("tag"),a=t.searchParams.get("slug"),i=t.searchParams.get("secret");if(s!==c&&i!==c)return console.warn("Invalid GET revalidation attempt: Incorrect or missing authentication."),n.NextResponse.json({message:"Invalid token"},{status:401});try{let e=[],s=[];if(r)e.push(r);else if(e.push("strapi-clinics-list"),e.push("strapi-clinics-slugs"),a)e.push(`strapi-clinic-${a}`),s.push(`/clinics/${a}`);else{for(let s=1;s<=10;s++)e.push(`strapi-clinics-page-${s}`);e.push("strapi-specialties"),e.push("strapi-conditions"),e.push("strapi-clinics-locations")}for(let t of(s.push("/clinics"),e))(0,l.revalidateTag)(t),console.log(`Manual revalidation successful for tag: ${t}`);for(let e of s)(0,l.revalidatePath)(e),console.log(`Manual revalidation successful for path: ${e}`);return n.NextResponse.json({revalidated:!0,revalidatedTags:e,revalidatedPaths:s,timestamp:new Date().toISOString()})}catch(e){return console.error("Error during manual revalidation:",e),n.NextResponse.json({message:"Error revalidating",error:e.message},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/revalidate/clinics/route",pathname:"/api/revalidate/clinics",filename:"route",bundlePath:"app/api/revalidate/clinics/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\api\\revalidate\\clinics\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:v,serverHooks:h}=u;function f(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:v})}},78335:()=>{},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[7719,6391,580],()=>t(63461));module.exports=r})();