'use client';

import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useRouter, usePathname } from 'next/navigation';
import BlogPostCard from '@/components/blog/BlogPostCard';
import Pagination from '@/components/shared/Pagination';
import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import FeaturedPost from '@/components/blog/FeaturedPost';
import PopularPosts from '@/components/blog/PopularPosts';
import NewsletterSignup from '@/components/blog/NewsletterSignup';
import TagCloud from '@/components/blog/TagCloud';
import Link from 'next/link';

// Define the props interface to receive data from the server component
interface BlogContentProps {
  initialPosts: BlogPost[];
  initialFeaturedPost: BlogPost | null;
  initialPopularPosts: BlogPost[];
  categories: BlogCategory[];
  tags: string[];
  totalPages: number;
}

// Reuse the BlogPost and BlogCategory interfaces from the page component
interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  featured_image: string | null;
  publish_date: string;
  isFeatured?: boolean;
  view_count?: number;
  reading_time?: number;
  content?: string;
  author: {
    name: string;
    slug: string;
    profile_picture: string | null;
  } | null;
}

interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  count: number;
}

// Memoize the BlogPostCard to prevent unnecessary re-renders
const MemoizedBlogPostCard = memo(BlogPostCard);

/**
 * Optimized BlogContent component that:
 * 1. Uses memoization to prevent unnecessary re-renders
 * 2. Efficiently manages state based on URL parameters
 * 3. Improves client-side performance with proper React patterns
 */
export default function BlogContent({
  initialPosts,
  initialFeaturedPost,
  initialPopularPosts,
  categories,
  tags,
  totalPages: initialTotalPages,
}: BlogContentProps) {
  // Get search params and navigation utilities
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  
  // Extract and memoize query parameters to reduce re-renders
  const query = useMemo(() => searchParams.get('query') || '', [searchParams]);
  const categorySlug = useMemo(() => searchParams.get('category') || '', [searchParams]);
  const currentPage = useMemo(() => Number(searchParams.get('page')) || 1, [searchParams]);

  // State to store posts and pagination with proper initialization
  const [posts, setPosts] = useState<BlogPost[]>(initialPosts);
  const [featuredPost, setFeaturedPost] = useState<BlogPost | null>(initialFeaturedPost);
  const [totalPages, setTotalPages] = useState<number>(initialTotalPages);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  
  // Memoize the condition for showing the featured post to avoid recalculations
  const showFeaturedPost = useMemo(() => 
    !query && !categorySlug && featuredPost !== null,
    [query, categorySlug, featuredPost]
  );
  
  // Memoize the current category for improved performance
  const currentCategory = useMemo(() => 
    categories.find(c => c.slug === categorySlug),
    [categories, categorySlug]
  );
  
  // Function to handle client-side navigation for filtering
  const handleFilterChange = useCallback((type: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    
    if (value) {
      params.set(type, value);
      params.delete('page'); // Reset to first page when filters change
    } else {
      params.delete(type);
    }
    
    router.push(`${pathname}?${params.toString()}`);
  }, [pathname, router, searchParams]);
  
  // Effect to update state based on search params
  // Using a callback pattern to avoid state updates with the same data
  useEffect(() => {
    const updateState = async () => {
      // Only enter loading state for actual data fetching scenarios
      if (query || categorySlug || currentPage > 1) {
        setIsLoading(true);
        
        // In a real implementation with client-side data fetching:
        // try {
        //   const result = await fetchBlogPosts({query, categorySlug, page: currentPage});
        //   setPosts(result.data);
        //   setFeaturedPost(null); // Hide featured post when filtering
        //   setTotalPages(result.meta.pagination?.pageCount || 1);
        // } catch (error) {
        //   console.error('Error fetching blog posts:', error);
        // }
        
        // For this implementation, we'll work with the server-provided data:
        setPosts(initialPosts);
        setFeaturedPost(query || categorySlug ? null : initialFeaturedPost);
        setTotalPages(initialTotalPages);
        
        // Short timeout to prevent flickering for fast operations
        setTimeout(() => setIsLoading(false), 100);
      } else {
        // When returning to main view, restore initial state without loading indicator
        setPosts(initialPosts);
        setFeaturedPost(initialFeaturedPost);
        setTotalPages(initialTotalPages);
      }
    };
    
    updateState();
    
    // Track page views when the URL changes
    const pageViewTracker = setTimeout(() => {
      // Only track if this were a real analytics implementation
      // trackPageView({path: pathname, search: searchParams.toString()});
    }, 500);
    
    return () => clearTimeout(pageViewTracker);
  }, [query, categorySlug, currentPage, initialPosts, initialFeaturedPost, initialTotalPages, pathname, searchParams]);

  return (
    <>
      {/* Featured Post Section */}
      {showFeaturedPost && featuredPost && (
        <div className="bg-gray-50 py-12">
          <div className="container mx-auto px-4">
            <FeaturedPost
              post={featuredPost}
              badgeType={
                // If the post is explicitly marked as featured in Strapi
                featuredPost.isFeatured === true ? 'featured' :
                // If the post is shown because it's popular (has views)
                (typeof featuredPost.view_count === 'number' && featuredPost.view_count > 0) ? 'popular' :
                // If it's the latest post (fallback)
                'recent'
              }
            />
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-12">
          {/* Main Content */}
          <div className="lg:w-2/3">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
              {query ? (
                <>Search Results for "{query}"</>
              ) : categorySlug ? (
                <>Articles in {categories.find(c => c.slug === categorySlug)?.name || categorySlug}</>
              ) : (
                <>Latest Articles</>
              )}
            </h2>

            {/* Blog Posts Grid */}
            {posts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {posts.map((post: BlogPost) => (
                  <BlogPostCard
                    key={post.id}
                    post={post}
                    showReadingTime={true}
                    showShareButton={true}
                    showBadge={true}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center p-8 bg-gray-50 rounded-lg">
                <p className="text-gray-500">
                  {query
                    ? `No blog posts found matching "${query}".`
                    : categorySlug
                      ? `No articles found in this category.`
                      : 'No blog posts found. Check back soon!'}
                </p>
              </div>
            )}

            {/* Pagination */}
            <div className="mt-12 flex justify-center">
              <Pagination totalPages={totalPages} />
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:w-1/3 space-y-8">
            {/* Popular Posts */}
            {initialPopularPosts.length > 0 && (
              <PopularPosts posts={initialPopularPosts} />
            )}

            {/* Enhanced Newsletter Signup */}
            <NewsletterSignup />

            {/* Tags Cloud */}
            {tags.length > 0 && (
              <TagCloud tags={tags} />
            )}
          </div>
        </div>

        {/* Topic Collections/Categories Showcase - Full Width */}
        {categories.filter(cat => cat.count > 0).length > 0 && !query && !categorySlug && (
          <div className="mt-16 py-16 bg-gray-50">
            <div className="container mx-auto px-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-8 text-center">Explore Topics</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-8">
                {categories
                  .filter(category => category.count > 0)
                  .slice(0, 10)
                  .map((category) => (
                    <Link
                      key={category.id}
                      href={`/blog?category=${category.slug}`}
                      className="group block"
                    >
                      <div className="bg-white rounded-lg shadow-sm overflow-hidden transition-all group-hover:shadow-md group-hover:-translate-y-1">
                        <div className="h-32 bg-emerald-100 relative">
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-emerald-700 text-2xl font-semibold">{category.name.charAt(0)}</span>
                          </div>
                        </div>
                        <div className="p-5">
                          <h3 className="font-semibold text-gray-800 group-hover:text-emerald-600 text-center">{category.name}</h3>
                          <p className="text-sm text-gray-500 mt-2 text-center">
                            {category.count} {category.count === 1 ? 'article' : 'articles'}
                          </p>
                        </div>
                      </div>
                    </Link>
                  ))}
              </div>
              {categories.filter(cat => cat.count > 0).length > 10 && (
                <div className="text-center mt-10">
                  <Link
                    href="/blog/categories"
                    className="inline-flex items-center justify-center px-5 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
                  >
                    View All Topics
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
}
