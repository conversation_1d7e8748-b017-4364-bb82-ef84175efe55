/**
 * Strapi command to import clinics from CSV
 * 
 * This command imports clinics from a CSV file into the Strapi database.
 * It uses the Strapi entity service to create or update clinic entries.
 * 
 * Usage: 
 * 1. Start Strapi console: npm run strapi console
 * 2. Run: await require('./scripts/import-command.js')(strapi)
 */

const importClinics = require('./import-clinics');

module.exports = async (strapi) => {
  console.log('Starting clinic import from CSV...');
  
  try {
    // Install csv-parser if not already installed
    try {
      require('csv-parser');
    } catch (e) {
      console.log('Installing csv-parser dependency...');
      const { execSync } = require('child_process');
      execSync('npm install csv-parser', { stdio: 'inherit' });
      console.log('csv-parser installed successfully.');
    }
    
    // Run the import
    const result = await importClinics(strapi);
    console.log('Import completed successfully!');
    return result;
  } catch (error) {
    console.error('Import failed:', error);
    throw error;
  }
};
