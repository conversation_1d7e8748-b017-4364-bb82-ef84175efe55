"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{21:(e,r,t)=>{t.d(r,{default:()=>p});var n=t(5155),a=t(5695),s=t(2115),c=t(6772),i=t(2204),l=t(2461),o=t(9416),u=t(2596);let d=(e,r)=>r<=7?Array.from({length:r},(e,r)=>r+1):e<=3?[1,2,3,"...",r-1,r]:e>=r-2?[1,2,"...",r-2,r-1,r]:[1,"...",e-1,e,e+1,"...",r];function m(e){let{totalPages:r}=e,t=(0,a.usePathname)(),c=(0,a.useSearchParams)(),i=Number(c.get("page"))||1,l=(0,s.useCallback)(e=>{let r=new URLSearchParams(c.toString());return r.set("page",e.toString()),"".concat(t,"?").concat(r.toString())},[t,c]),o=(0,s.useCallback)(e=>{if("..."===e)return;let r=new URLSearchParams(c.toString());r.set("page",e.toString()),window.history.pushState(null,"","".concat(t,"?").concat(r.toString())),window.dispatchEvent(new CustomEvent("paginationchange",{detail:{page:e}}))},[t,c]),u=d(i,r);return r<=1?null:(0,n.jsxs)("div",{className:"inline-flex",children:[(0,n.jsx)(h,{direction:"left",href:l(i-1),onClick:()=>i>1&&o(i-1),isDisabled:i<=1}),(0,n.jsx)("div",{className:"flex -space-x-px",children:u.map((e,r)=>{let t;return 0===r&&(t="first"),r===u.length-1&&(t="last"),1===u.length&&(t="single"),"..."===e&&(t="middle"),(0,n.jsx)(f,{page:e,href:l(e),onClick:()=>o(e),position:t,isActive:i===e},"".concat(e,"-").concat(r))})}),(0,n.jsx)(h,{direction:"right",href:l(i+1),onClick:()=>i<r&&o(i+1),isDisabled:i>=r})]})}function f(e){let{page:r,href:t,onClick:a,isActive:s,position:c}=e,i=(0,u.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===c||"single"===c,"rounded-r-md":"last"===c||"single"===c,"z-10 bg-emerald-600 border-emerald-600 text-white":s,"hover:bg-gray-100":!s&&"middle"!==c,"text-gray-300 pointer-events-none":"middle"===c});return s||"middle"===c?(0,n.jsx)("div",{className:i,children:r}):(0,n.jsx)("button",{onClick:a,className:i,children:r})}function h(e){let{href:r,onClick:t,direction:a,isDisabled:s}=e,c=(0,u.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":s,"hover:bg-gray-100":!s,"mr-2 md:mr-4":"left"===a,"ml-2 md:ml-4":"right"===a}),i="left"===a?(0,n.jsx)(l.A,{className:"w-4"}):(0,n.jsx)(o.A,{className:"w-4"});return s?(0,n.jsx)("div",{className:c,children:i}):(0,n.jsx)("button",{onClick:t,className:c,children:i})}function p(e){let{clinics:r,practitioners:t,totalPages:l,initialTab:o="clinics"}=e,u=(0,a.useSearchParams)(),[d,f]=(0,s.useState)(("practitioners"===u.get("tab")?"practitioners":"clinics")||o);return(0,s.useEffect)(()=>{let e="practitioners"===u.get("tab")?"practitioners":"clinics";e!==d&&f(e)},[u,d]),(0,n.jsxs)("div",{children:["clinics"===d?(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.length>0?r.map(e=>(0,n.jsx)(c.default,{clinic:e,showContactInfo:!1},e.id)):(0,n.jsx)("div",{className:"col-span-full text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:"No clinics found matching your criteria."})})}):(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.length>0?t.map(e=>(0,n.jsx)(i.default,{practitioner:e},e.id)):(0,n.jsx)("div",{className:"col-span-full text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:"No practitioners found matching your criteria."})})}),l>1&&(0,n.jsx)("div",{className:"mt-8 flex justify-center",children:(0,n.jsx)(m,{totalPages:l})})]},d)}},1942:(e,r,t)=>{t.d(r,{default:()=>o});var n=t(5155),a=t(5695),s=t(2115),c=t(351),i=t(6874),l=t.n(i);function o(e){let{slug:r,pageType:t,clinicCount:i,practitionerCount:o,initialTab:u="clinics"}=e,d=(0,a.useSearchParams)();(0,a.usePathname)();let m=(0,a.useRouter)(),[f,h]=(0,s.useState)(("practitioners"===d.get("tab")?"practitioners":"clinics")||u);(0,s.useEffect)(()=>{let e="practitioners"===d.get("tab")?"practitioners":"clinics";e!==f&&h(e)},[d,f]);let p=e=>n=>{n.preventDefault();let a=new URLSearchParams(d);a.set("tab",e),m.push("/".concat(t,"/").concat(r,"?").concat(a.toString())),h(e)};return(0,n.jsx)("div",{className:"mb-6 border-b border-gray-200",children:(0,n.jsxs)("nav",{className:"-mb-px flex space-x-8","aria-label":"Tabs",children:[(0,n.jsxs)(l(),{href:"/".concat(t,"/").concat(r,"?tab=clinics"),className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\n            ".concat("clinics"===f?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),"aria-current":"clinics"===f?"page":void 0,onClick:p("clinics"),children:[(0,n.jsx)(c.V5Y,{className:"h-4 w-4"}),(0,n.jsxs)("span",{children:["categories"===t?"Clinics":"Related Clinics"," (",i,")"]})]}),(0,n.jsxs)(l(),{href:"/".concat(t,"/").concat(r,"?tab=practitioners"),className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\n            ".concat("practitioners"===f?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),"aria-current":"practitioners"===f?"page":void 0,onClick:p("practitioners"),children:[(0,n.jsx)(c.JXP,{className:"h-4 w-4"}),(0,n.jsxs)("span",{children:["categories"===t?"Practitioners":"Related Practitioners"," (",o,")"]})]})]})})}},2204:(e,r,t)=>{t.d(r,{default:()=>o});var n=t(5155),a=t(6874),s=t.n(a),c=t(2515),i=t(2115),l=t(8864);let o=e=>{let{practitioner:r,prefetchedData:t=!1}=e,[a,o]=(0,i.useState)(!1),u=(0,i.useRef)(!1);(0,i.useEffect)(()=>{if(u.current)return;let e=(0,l.b3)(r.slug);e&&o(!0),r._hasDetailedData&&!e&&((0,l.tq)(r),o(!0)),u.current=!0},[r]);let d=t||r._hasDetailedData||a?{pathname:"/practitioners/".concat(r.slug),query:{prefetched:"true"}}:"/practitioners/".concat(r.slug);return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 flex-grow",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,n.jsx)(s(),{href:d,className:"hover:text-emerald-600",children:r.name})}),r.isVerified&&(0,n.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,n.jsx)(c.AI8,{color:"#009967",size:14}),(0,n.jsx)("span",{children:"VERIFIED"})]}),r.bio&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:r.bio})]}),(0,n.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,n.jsx)(s(),{href:d,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Profile →"})})]})}},2461:(e,r,t)=>{t.d(r,{A:()=>a});var n=t(2115);let a=n.forwardRef(function(e,r){let{title:t,titleId:a,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},s),t?n.createElement("title",{id:a},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},2596:(e,r,t)=>{t.d(r,{A:()=>n});let n=function(){for(var e,r,t=0,n="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,n,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var s=r.length;for(t=0;t<s;t++)r[t]&&(n=e(r[t]))&&(a&&(a+=" "),a+=n)}else for(n in r)r[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=r);return n}},4436:(e,r,t)=>{t.d(r,{k5:()=>u});var n=t(2115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=n.createContext&&n.createContext(a),c=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function o(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){var n,a,s;n=e,a=r,s=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in n?Object.defineProperty(n,a,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[a]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return r=>n.createElement(d,i({attr:o({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,o({key:t},r.attr),e(r.child)))}(e.child))}function d(e){var r=r=>{var t,{attr:a,size:s,title:l}=e,u=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)t=s[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,c),d=s||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,u,{className:t,style:o(o({color:e.color||r.color},r.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),l&&n.createElement("title",null,l),e.children)};return void 0!==s?n.createElement(s.Consumer,null,e=>r(e)):r(a)}},5695:(e,r,t)=>{var n=t(8999);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},6772:(e,r,t)=>{t.d(r,{default:()=>l});var n=t(5155),a=t(6874),s=t.n(a),c=t(351),i=t(2515);let l=e=>{var r,t;let{clinic:a,showContactInfo:l=!0,prefetchedData:o=!1}=e,u=o?{pathname:"/clinics/".concat(a.slug),query:{prefetched:"true"}}:"/clinics/".concat(a.slug);return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 flex-grow",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,n.jsx)(s(),{href:u,className:"hover:text-emerald-600",children:a.name})}),a.isVerified&&(0,n.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,n.jsx)(i.AI8,{color:"#009967",size:14}),(0,n.jsx)("span",{children:"VERIFIED"})]}),a.description&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:a.description}),(0,n.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[a.address&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(c.HzC,{className:"mr-2 text-emerald-500"}),(0,n.jsxs)("span",{children:[a.address.city,", ",a.address.stateProvince]})]}),l&&(null==(r=a.contactInfo)?void 0:r.phoneNumber)&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(c.QFc,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("span",{children:a.contactInfo.phoneNumber})]}),l&&(null==(t=a.contactInfo)?void 0:t.websiteUrl)&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(c.VeH,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("a",{href:a.contactInfo.websiteUrl,target:"_blank",rel:"nofollow noopener noreferrer",className:"hover:text-emerald-600",children:"Visit Website"})]})]})]}),(0,n.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,n.jsx)(s(),{href:u,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Details →"})})]})}},8400:(e,r,t)=>{t.d(r,{default:()=>i});var n=t(5155),a=t(2115),s=t(5695),c=t(351);function i(e){var r;let{placeholder:t,paramName:i="query",icon:l}=e,o=(0,s.useSearchParams)(),u=(0,s.usePathname)(),{replace:d}=(0,s.useRouter)(),m=function(e,r,t){var n=this,s=(0,a.useRef)(null),c=(0,a.useRef)(0),i=(0,a.useRef)(null),l=(0,a.useRef)([]),o=(0,a.useRef)(),u=(0,a.useRef)(),d=(0,a.useRef)(e),m=(0,a.useRef)(!0);d.current=e;var f="undefined"!=typeof window,h=!r&&0!==r&&f;if("function"!=typeof e)throw TypeError("Expected a function");r=+r||0;var p=!!(t=t||{}).leading,x=!("trailing"in t)||!!t.trailing,g="maxWait"in t,v="debounceOnServer"in t&&!!t.debounceOnServer,b=g?Math.max(+t.maxWait||0,r):null;return(0,a.useEffect)(function(){return m.current=!0,function(){m.current=!1}},[]),(0,a.useMemo)(function(){var e=function(e){var r=l.current,t=o.current;return l.current=o.current=null,c.current=e,u.current=d.current.apply(t,r)},t=function(e,r){h&&cancelAnimationFrame(i.current),i.current=h?requestAnimationFrame(e):setTimeout(e,r)},a=function(e){if(!m.current)return!1;var t=e-s.current;return!s.current||t>=r||t<0||g&&e-c.current>=b},y=function(r){return i.current=null,x&&l.current?e(r):(l.current=o.current=null,u.current)},j=function e(){var n=Date.now();if(a(n))return y(n);if(m.current){var i=r-(n-s.current);t(e,g?Math.min(i,b-(n-c.current)):i)}},w=function(){if(f||v){var d=Date.now(),h=a(d);if(l.current=[].slice.call(arguments),o.current=n,s.current=d,h){if(!i.current&&m.current)return c.current=s.current,t(j,r),p?e(s.current):u.current;if(g)return t(j,r),e(s.current)}return i.current||t(j,r),u.current}};return w.cancel=function(){i.current&&(h?cancelAnimationFrame(i.current):clearTimeout(i.current)),c.current=0,l.current=s.current=o.current=i.current=null},w.isPending=function(){return!!i.current},w.flush=function(){return i.current?y(Date.now()):u.current},w},[p,g,r,b,x,h,f,v])}(e=>{console.log("Searching... ".concat(e));let r=new URLSearchParams(o);r.set("page","1"),e?r.set(i,e):r.delete(i),d("".concat(u,"?").concat(r.toString()))},500);return(0,n.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,n.jsx)("label",{htmlFor:i,className:"sr-only",children:"Search"}),(0,n.jsx)("input",{id:i,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:t,onChange:e=>{m(e.target.value)},defaultValue:null==(r=o.get(i))?void 0:r.toString()}),l||(0,n.jsx)(c.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},8864:(e,r,t)=>{t.d(r,{b3:()=>c,tq:()=>s});let n={};function a(e){let r=n[e],t=Date.now();if(r&&r.expiry>t)return r.data;try{let r=sessionStorage.getItem("cache_".concat(e));if(r){let a=JSON.parse(r);if(a.expiry>t)return n[e]=a,a.data;sessionStorage.removeItem("cache_".concat(e))}}catch(e){console.error("Error retrieving data from sessionStorage:",e)}return null}function s(e){if(!e||!e.id||!e.slug)return;let r=a("practitioner_".concat(e.slug));r&&(!e._hasDetailedData||r._hasDetailedData)||function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5,a=Date.now();n[e]={data:r,timestamp:a,expiry:a+t};try{sessionStorage.setItem("cache_".concat(e),JSON.stringify({data:r,timestamp:a,expiry:a+t}))}catch(e){console.error("Error storing data in sessionStorage:",e)}}("practitioner_".concat(e.slug),e,18e5)}function c(e){return a("practitioner_".concat(e))}},9416:(e,r,t)=>{t.d(r,{A:()=>a});var n=t(2115);let a=n.forwardRef(function(e,r){let{title:t,titleId:a,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},s),t?n.createElement("title",{id:a},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})}}]);