"use client"; // Required for useFormState and other hooks

import Link from 'next/link';
import { FiMail, FiSend } from 'react-icons/fi';
import { Metadata } from 'next'; // Metadata will be static as this is now a client component
import { useFormState, useFormStatus } from 'react-dom';
import { useEffect, useRef } from 'react';
import { sendContactMessage } from './actions';

// Metadata can't be dynamically exported from client components in the same way.
// For SEO, it's often better to keep pages that need dynamic metadata as Server Components
// or use the generateMetadata function in a layout or parent server component.
// Since this page is now client-side for form handling, we'll keep metadata simple.
// If dynamic metadata based on server data were needed, a different structure might be chosen.

// export const metadata: Metadata = { // This won't work as expected in a client component
// title: 'Contact Us - Natural Healing Now',
// description: 'Get in touch with Natural Healing Now. We\'re here to help you find the right resources for your wellness journey.',
// };

const initialState = {
  message: '',
  success: false,
  errors: undefined,
};

function SubmitButton() {
  const { pending } = useFormStatus();
  return (
    <button
      type="submit"
      aria-disabled={pending}
      disabled={pending}
      className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold flex items-center disabled:opacity-70 disabled:cursor-not-allowed"
    >
      <FiSend className="mr-2" />
      {pending ? 'Sending...' : 'Send Message'}
    </button>
  );
}

export default function ContactPage() {
  const [state, formAction] = useFormState(sendContactMessage, initialState);
  const formRef = useRef<HTMLFormElement>(null);

  const contactInfo = {
    email: '<EMAIL>', // This was the original mock, ensure it matches CONTACT_FORM_RECIPIENT if it's for display
  };

  useEffect(() => {
    if (state.success) {
      formRef.current?.reset(); // Reset form on success
    }
  }, [state.success]);

  return (
    <>
      {/* Page Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Contact Us</h1>
          <p className="text-lg max-w-3xl mx-auto">
            Have questions about natural healing options? We're here to help you find the right resources for your wellness journey.
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col items-center gap-12">
          {/* Contact Form */}
          <div className="w-full max-w-xl">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">Send Us a Message</h2>
            
            {state.message && !state.success && (
              <div className="mb-4 p-3 rounded-md bg-red-100 text-red-700 text-sm">
                {state.message}
              </div>
            )}
            {state.message && state.success && (
              <div className="mb-4 p-3 rounded-md bg-green-100 text-green-700 text-sm">
                {state.message}
              </div>
            )}

            <form ref={formRef} action={formAction} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Your Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="John Doe"
                  />
                  {state.errors?.name && (
                    <p className="mt-1 text-xs text-red-600">{state.errors.name.join(', ')}</p>
                  )}
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="<EMAIL>"
                  />
                  {state.errors?.email && (
                    <p className="mt-1 text-xs text-red-600">{state.errors.email.join(', ')}</p>
                  )}
                </div>
              </div>

              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                  Subject
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="How can we help you?"
                />
                {state.errors?.subject && (
                  <p className="mt-1 text-xs text-red-600">{state.errors.subject.join(', ')}</p>
                )}
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={6}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="Your message here..."
                ></textarea>
                {state.errors?.message && (
                  <p className="mt-1 text-xs text-red-600">{state.errors.message.join(', ')}</p>
                )}
              </div>

              <div className="flex justify-center">
                <SubmitButton />
              </div>
            </form>
          </div>

          {/* Contact Information */}
          <div className="w-full max-w-xl text-center">
            <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
              <p className="text-gray-700 mb-4">
                For direct inquiries, you can also reach us at:
              </p>
              <div className="space-y-6">
                <div className="flex items-center justify-center">
                  <div className="flex-shrink-0">
                    <div className="h-12 w-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                      <FiMail className="h-6 w-6 text-emerald-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="mt-0 text-gray-600 text-lg">
                      <a href={`mailto:${process.env.NEXT_PUBLIC_CONTACT_EMAIL || contactInfo.email}`} className="hover:text-emerald-600 font-medium">
                        {process.env.NEXT_PUBLIC_CONTACT_EMAIL || contactInfo.email}
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
