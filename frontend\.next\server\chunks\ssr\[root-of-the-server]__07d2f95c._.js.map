{"version": 3, "sources": [], "sections": [{"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/strapiAuth.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Define the base URL for Strapi API\nconst API_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';\n\n// Define types for authentication responses\nexport interface StrapiUser {\n  id: number;\n  username: string;\n  email: string;\n  provider: string;\n  confirmed: boolean;\n  blocked: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface StrapiAuthResponse {\n  jwt: string;\n  user: StrapiUser;\n}\n\n// Create an axios instance for authentication requests\nconst authAPI = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Authentication helper functions\nexport const strapiAuth = {\n  // Register a new user\n  register: async (username: string, email: string, password: string) => {\n    try {\n      const response = await authAPI.post<StrapiAuthResponse>('/api/auth/local/register', {\n        username,\n        email,\n        password,\n      });\n      \n      // Store the JWT token in localStorage\n      if (response.data.jwt) {\n        localStorage.setItem('jwt', response.data.jwt);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n      }\n      \n      return { data: response.data, error: null };\n    } catch (error: any) {\n      return { \n        data: null, \n        error: error.response?.data?.error || { \n          message: 'An error occurred during registration' \n        } \n      };\n    }\n  },\n\n  // Login an existing user\n  login: async (identifier: string, password: string) => {\n    try {\n      const response = await authAPI.post<StrapiAuthResponse>('/api/auth/local', {\n        identifier, // Can be email or username\n        password,\n      });\n      \n      // Store the JWT token in localStorage\n      if (response.data.jwt) {\n        localStorage.setItem('jwt', response.data.jwt);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n      }\n      \n      return { data: response.data, error: null };\n    } catch (error: any) {\n      return { \n        data: null, \n        error: error.response?.data?.error || { \n          message: 'Invalid credentials' \n        } \n      };\n    }\n  },\n\n  // Logout the current user\n  logout: () => {\n    localStorage.removeItem('jwt');\n    localStorage.removeItem('user');\n    return { error: null };\n  },\n\n  // Get the current user from localStorage\n  getCurrentUser: () => {\n    if (typeof window === 'undefined') {\n      return { user: null };\n    }\n    \n    const jwt = localStorage.getItem('jwt');\n    const user = localStorage.getItem('user');\n    \n    if (!jwt || !user) {\n      return { user: null };\n    }\n    \n    try {\n      return { user: JSON.parse(user) };\n    } catch {\n      return { user: null };\n    }\n  },\n\n  // Check if the user is authenticated\n  isAuthenticated: () => {\n    if (typeof window === 'undefined') {\n      return false;\n    }\n    \n    const jwt = localStorage.getItem('jwt');\n    return !!jwt;\n  },\n\n  // Get the JWT token\n  getToken: () => {\n    if (typeof window === 'undefined') {\n      return null;\n    }\n    \n    return localStorage.getItem('jwt');\n  },\n\n  // Forgot password\n  forgotPassword: async (email: string) => {\n    try {\n      const response = await authAPI.post('/api/auth/forgot-password', {\n        email,\n      });\n      \n      return { data: response.data, error: null };\n    } catch (error: any) {\n      return { \n        data: null, \n        error: error.response?.data?.error || { \n          message: 'An error occurred during password reset request' \n        } \n      };\n    }\n  },\n\n  // Reset password\n  resetPassword: async (code: string, password: string, passwordConfirmation: string) => {\n    try {\n      const response = await authAPI.post('/api/auth/reset-password', {\n        code,\n        password,\n        passwordConfirmation,\n      });\n      \n      // Store the JWT token in localStorage if the response includes it\n      if (response.data.jwt) {\n        localStorage.setItem('jwt', response.data.jwt);\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n      }\n      \n      return { data: response.data, error: null };\n    } catch (error: any) {\n      return { \n        data: null, \n        error: error.response?.data?.error || { \n          message: 'An error occurred during password reset' \n        } \n      };\n    }\n  },\n};\n\n// Create an authenticated API instance that includes the JWT token in requests\nexport const createAuthenticatedAPI = () => {\n  const token = strapiAuth.getToken();\n  \n  return axios.create({\n    baseURL: API_URL,\n    headers: {\n      'Content-Type': 'application/json',\n      ...(token ? { Authorization: `Bearer ${token}` } : {}),\n    },\n  });\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,qCAAqC;AACrC,MAAM,UAAU,6DAA0C;AAmB1D,uDAAuD;AACvD,MAAM,UAAU,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,aAAa;IACxB,sBAAsB;IACtB,UAAU,OAAO,UAAkB,OAAe;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAqB,4BAA4B;gBAClF;gBACA;gBACA;YACF;YAEA,sCAAsC;YACtC,IAAI,SAAS,IAAI,CAAC,GAAG,EAAE;gBACrB,aAAa,OAAO,CAAC,OAAO,SAAS,IAAI,CAAC,GAAG;gBAC7C,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI;YAChE;YAEA,OAAO;gBAAE,MAAM,SAAS,IAAI;gBAAE,OAAO;YAAK;QAC5C,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,MAAM;gBACN,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;oBACpC,SAAS;gBACX;YACF;QACF;IACF;IAEA,yBAAyB;IACzB,OAAO,OAAO,YAAoB;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAqB,mBAAmB;gBACzE;gBACA;YACF;YAEA,sCAAsC;YACtC,IAAI,SAAS,IAAI,CAAC,GAAG,EAAE;gBACrB,aAAa,OAAO,CAAC,OAAO,SAAS,IAAI,CAAC,GAAG;gBAC7C,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI;YAChE;YAEA,OAAO;gBAAE,MAAM,SAAS,IAAI;gBAAE,OAAO;YAAK;QAC5C,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,MAAM;gBACN,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;oBACpC,SAAS;gBACX;YACF;QACF;IACF;IAEA,0BAA0B;IAC1B,QAAQ;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,yCAAyC;IACzC,gBAAgB;QACd,wCAAmC;YACjC,OAAO;gBAAE,MAAM;YAAK;QACtB;;QAEA,MAAM;QACN,MAAM;IAWR;IAEA,qCAAqC;IACrC,iBAAiB;QACf,wCAAmC;YACjC,OAAO;QACT;;QAEA,MAAM;IAER;IAEA,oBAAoB;IACpB,UAAU;QACR,wCAAmC;YACjC,OAAO;QACT;;IAGF;IAEA,kBAAkB;IAClB,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC,6BAA6B;gBAC/D;YACF;YAEA,OAAO;gBAAE,MAAM,SAAS,IAAI;gBAAE,OAAO;YAAK;QAC5C,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,MAAM;gBACN,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;oBACpC,SAAS;gBACX;YACF;QACF;IACF;IAEA,iBAAiB;IACjB,eAAe,OAAO,MAAc,UAAkB;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,IAAI,CAAC,4BAA4B;gBAC9D;gBACA;gBACA;YACF;YAEA,kEAAkE;YAClE,IAAI,SAAS,IAAI,CAAC,GAAG,EAAE;gBACrB,aAAa,OAAO,CAAC,OAAO,SAAS,IAAI,CAAC,GAAG;gBAC7C,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI;YAChE;YAEA,OAAO;gBAAE,MAAM,SAAS,IAAI;gBAAE,OAAO;YAAK;QAC5C,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,MAAM;gBACN,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;oBACpC,SAAS;gBACX;YACF;QACF;IACF;AACF;AAGO,MAAM,yBAAyB;IACpC,MAAM,QAAQ,WAAW,QAAQ;IAEjC,OAAO,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAClB,SAAS;QACT,SAAS;YACP,gBAAgB;YAChB,GAAI,QAAQ;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC,IAAI,CAAC,CAAC;QACvD;IACF;AACF", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { createContext, useContext, useEffect, useState, ReactNode } from 'react';\r\nimport { strapiAuth, StrapiUser } from '@/lib/strapiAuth';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ntype User = StrapiUser | null;\r\n\r\ntype AuthContextType = {\r\n  user: User;\r\n  isLoading: boolean;\r\n  signIn: (identifier: string, password: string) => Promise<{ error: any }>;\r\n  signUp: (username: string, email: string, password: string) => Promise<{ error: any }>;\r\n  signOut: () => { error: any };\r\n  forgotPassword: (email: string) => Promise<{ error: any }>;\r\n  resetPassword: (code: string, password: string, passwordConfirmation: string) => Promise<{ error: any }>;\r\n  isAuthenticated: boolean;\r\n};\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport function AuthProvider({ children }: { children: ReactNode }) {\r\n  const [user, setUser] = useState<User>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    // Check for active session on initial load\r\n    const initializeAuth = async () => {\r\n      setIsLoading(true);\r\n\r\n      try {\r\n        // Get user from localStorage (set during login)\r\n        const { user } = strapiAuth.getCurrentUser();\r\n        setUser(user);\r\n        setIsAuthenticated(!!user);\r\n      } catch (error) {\r\n        console.error('Error loading user:', error);\r\n        setUser(null);\r\n        setIsAuthenticated(false);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    initializeAuth();\r\n\r\n    // Add event listener for storage changes (for multi-tab support)\r\n    const handleStorageChange = () => {\r\n      const { user } = strapiAuth.getCurrentUser();\r\n      setUser(user);\r\n      setIsAuthenticated(!!user);\r\n      router.refresh();\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n\r\n    // Clean up event listener on unmount\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [router]);\r\n\r\n  const signIn = async (identifier: string, password: string) => {\r\n    setIsLoading(true);\r\n    const { data, error } = await strapiAuth.login(identifier, password);\r\n\r\n    if (data) {\r\n      setUser(data.user);\r\n      setIsAuthenticated(true);\r\n      router.refresh();\r\n    }\r\n\r\n    setIsLoading(false);\r\n    return { error };\r\n  };\r\n\r\n  const signUp = async (username: string, email: string, password: string) => {\r\n    setIsLoading(true);\r\n    const { data, error } = await strapiAuth.register(username, email, password);\r\n\r\n    if (data) {\r\n      setUser(data.user);\r\n      setIsAuthenticated(true);\r\n      router.refresh();\r\n    }\r\n\r\n    setIsLoading(false);\r\n    return { error };\r\n  };\r\n\r\n  const signOut = () => {\r\n    setIsLoading(true);\r\n    const { error } = strapiAuth.logout();\r\n    setUser(null);\r\n    setIsAuthenticated(false);\r\n    router.refresh();\r\n    setIsLoading(false);\r\n    return { error };\r\n  };\r\n\r\n  const forgotPassword = async (email: string) => {\r\n    setIsLoading(true);\r\n    const { error } = await strapiAuth.forgotPassword(email);\r\n    setIsLoading(false);\r\n    return { error };\r\n  };\r\n\r\n  const resetPassword = async (code: string, password: string, passwordConfirmation: string) => {\r\n    setIsLoading(true);\r\n    const { data, error } = await strapiAuth.resetPassword(code, password, passwordConfirmation);\r\n\r\n    if (data) {\r\n      setUser(data.user);\r\n      setIsAuthenticated(true);\r\n      router.refresh();\r\n    }\r\n\r\n    setIsLoading(false);\r\n    return { error };\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    isLoading,\r\n    signIn,\r\n    signUp,\r\n    signOut,\r\n    forgotPassword,\r\n    resetPassword,\r\n    isAuthenticated\r\n  };\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\r\n}\r\n\r\nexport function useAuth() {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2CAA2C;QAC3C,MAAM,iBAAiB;YACrB,aAAa;YAEb,IAAI;gBACF,gDAAgD;gBAChD,MAAM,EAAE,IAAI,EAAE,GAAG,wHAAA,CAAA,aAAU,CAAC,cAAc;gBAC1C,QAAQ;gBACR,mBAAmB,CAAC,CAAC;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,QAAQ;gBACR,mBAAmB;YACrB,SAAU;gBACR,aAAa;YACf;QACF;QAEA;QAEA,iEAAiE;QACjE,MAAM,sBAAsB;YAC1B,MAAM,EAAE,IAAI,EAAE,GAAG,wHAAA,CAAA,aAAU,CAAC,cAAc;YAC1C,QAAQ;YACR,mBAAmB,CAAC,CAAC;YACrB,OAAO,OAAO;QAChB;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,qCAAqC;QACrC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,SAAS,OAAO,YAAoB;QACxC,aAAa;QACb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,YAAY;QAE3D,IAAI,MAAM;YACR,QAAQ,KAAK,IAAI;YACjB,mBAAmB;YACnB,OAAO,OAAO;QAChB;QAEA,aAAa;QACb,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,UAAkB,OAAe;QACrD,aAAa;QACb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,UAAU,OAAO;QAEnE,IAAI,MAAM;YACR,QAAQ,KAAK,IAAI;YACjB,mBAAmB;YACnB,OAAO,OAAO;QAChB;QAEA,aAAa;QACb,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,aAAa;QACb,MAAM,EAAE,KAAK,EAAE,GAAG,wHAAA,CAAA,aAAU,CAAC,MAAM;QACnC,QAAQ;QACR,mBAAmB;QACnB,OAAO,OAAO;QACd,aAAa;QACb,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,iBAAiB,OAAO;QAC5B,aAAa;QACb,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAClD,aAAa;QACb,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,gBAAgB,OAAO,MAAc,UAAkB;QAC3D,aAAa;QACb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,MAAM,UAAU;QAEvE,IAAI,MAAM;YACR,QAAQ,KAAK,IAAI;YACjB,mBAAmB;YACnB,OAAO,OAAO;QAChB;QAEA,aAAa;QACb,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/contexts/ErrorContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\n\ninterface ErrorContextType {\n  globalError: Error | null;\n  setGlobalError: (error: Error | null) => void;\n  clearGlobalError: () => void;\n  addErrorLog: (error: Error, source?: string) => void;\n  errorLogs: ErrorLog[];\n}\n\ninterface ErrorLog {\n  id: string;\n  error: Error;\n  timestamp: Date;\n  source?: string;\n}\n\nconst ErrorContext = createContext<ErrorContextType | undefined>(undefined);\n\ninterface ErrorProviderProps {\n  children: ReactNode;\n}\n\n/**\n * Provider component for the global error context\n */\nexport const ErrorProvider: React.FC<ErrorProviderProps> = ({ children }) => {\n  const [globalError, setGlobalError] = useState<Error | null>(null);\n  const [errorLogs, setErrorLogs] = useState<ErrorLog[]>([]);\n\n  const clearGlobalError = () => {\n    setGlobalError(null);\n  };\n\n  const addErrorLog = (error: Error, source?: string) => {\n    const newErrorLog: ErrorLog = {\n      id: Date.now().toString(),\n      error,\n      timestamp: new Date(),\n      source,\n    };\n\n    // Keep only the last 10 errors to avoid memory issues\n    setErrorLogs(prevLogs => [newErrorLog, ...prevLogs].slice(0, 10));\n    \n    // Log to console in development\n    if (process.env.NODE_ENV === 'development') {\n      console.error(`Error from ${source || 'unknown source'}:`, error);\n    }\n  };\n\n  return (\n    <ErrorContext.Provider\n      value={{\n        globalError,\n        setGlobalError,\n        clearGlobalError,\n        addErrorLog,\n        errorLogs,\n      }}\n    >\n      {children}\n    </ErrorContext.Provider>\n  );\n};\n\n/**\n * Hook to use the error context\n */\nexport const useError = (): ErrorContextType => {\n  const context = useContext(ErrorContext);\n  if (context === undefined) {\n    throw new Error('useError must be used within an ErrorProvider');\n  }\n  return context;\n};\n\nexport default ErrorContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAmBA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAS1D,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEzD,MAAM,mBAAmB;QACvB,eAAe;IACjB;IAEA,MAAM,cAAc,CAAC,OAAc;QACjC,MAAM,cAAwB;YAC5B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA,WAAW,IAAI;YACf;QACF;QAEA,sDAAsD;QACtD,aAAa,CAAA,WAAY;gBAAC;mBAAgB;aAAS,CAAC,KAAK,CAAC,GAAG;QAE7D,gCAAgC;QAChC,wCAA4C;YAC1C,QAAQ,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,iBAAiB,CAAC,CAAC,EAAE;QAC7D;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QACpB,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAKO,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/auth/UserAccountDropdown.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport Link from 'next/link';\r\nimport { FiUser, FiLogOut, FiChevronDown } from 'react-icons/fi';\r\n\r\nexport default function UserAccountDropdown() {\r\n  const { user, signOut } = useAuth();\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setIsOpen(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const handleSignOut = async () => {\r\n    await signOut();\r\n    setIsOpen(false);\r\n  };\r\n\r\n  // Truncate email for display if needed\r\n  const displayEmail = user?.email && user.email.length > 20\r\n    ? `${user.email.substring(0, 17)}...`\r\n    : user?.email;\r\n\r\n  return (\r\n    <div className=\"relative\" ref={dropdownRef}>\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"flex items-center text-gray-700 hover:text-emerald-600 focus:outline-none\"\r\n        aria-expanded={isOpen}\r\n        aria-haspopup=\"true\"\r\n      >\r\n        <div className=\"w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center mr-2\">\r\n          <FiUser className=\"text-emerald-600\" />\r\n        </div>\r\n        <span className=\"hidden md:block max-w-[150px] truncate\">{displayEmail}</span>\r\n        <FiChevronDown className={`ml-1 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\r\n      </button>\r\n\r\n      {isOpen && (\r\n        <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200\">\r\n          <Link\r\n            href=\"/account\"\r\n            className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\"\r\n            onClick={() => setIsOpen(false)}\r\n          >\r\n            <FiUser className=\"mr-2\" />\r\n            My Account\r\n          </Link>\r\n          <button\r\n            onClick={handleSignOut}\r\n            className=\"w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center\"\r\n          >\r\n            <FiLogOut className=\"mr-2\" />\r\n            Sign Out\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,MAAM;QACN,UAAU;IACZ;IAEA,uCAAuC;IACvC,MAAM,eAAe,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,GAAG,KACpD,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GACnC,MAAM;IAEV,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,iBAAe;gBACf,iBAAc;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBAAK,WAAU;kCAA0C;;;;;;kCAC1D,8OAAC,8IAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,0BAA0B,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;YAGpF,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,UAAU;;0CAEzB,8OAAC,8IAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAS;;;;;;;kCAG7B,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,8IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAS;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/imageLoader.js"], "sourcesContent": ["// src/lib/imageLoader.js\r\n\r\n/**\r\n * High-performance image loader for Next.js 15 and Strapi 5\r\n *\r\n * Advanced features:\r\n * - Adaptive format selection (AVIF/WebP) based on browser support\r\n * - Progressive image loading with optimal quality settings\r\n * - Automatic responsive sizing with device-pixel-ratio awareness\r\n * - Smart caching with deterministic URL parameters\r\n * - Bandwidth-aware quality optimization\r\n * - Special handling for SVGs, GIFs, and animated content\r\n * - Performance monitoring with detailed metrics\r\n * - Blur-up placeholder support for improved perceived performance\r\n *\r\n * @param {object} params - The parameters for the loader.\r\n * @param {string} params.src - The source URL of the image.\r\n * @param {number} params.width - The requested width of the image.\r\n * @param {number} [params.quality] - The requested quality of the image.\r\n * @returns {string} The optimized image URL.\r\n */\r\n\r\n// Performance metric collection\r\nconst metrics = {\r\n  count: 0,\r\n  errors: 0,\r\n  totalTime: 0,\r\n  slowestTime: 0,\r\n  slowestImage: '',\r\n};\r\n\r\n// Cache for dimension detection to avoid layout shifts\r\nconst imageDimensionsCache = new Map();\r\n\r\n// Environment configuration with defaults\r\nconst CONFIG = {\r\n  enableMetrics: process.env.NEXT_PUBLIC_CACHE_METRICS === 'true',\r\n  useHighQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true',\r\n  disableOptimization: process.env.NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION === 'true',\r\n  defaultQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 85 : 75,\r\n  avifQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 80 : 70,  // AVIF can use lower quality settings\r\n  webpQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 85 : 75,  // WebP standard quality\r\n  jpegQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 90 : 80,  // JPEG needs higher quality\r\n  pngQuality: process.env.NEXT_PUBLIC_HIGH_QUALITY_IMAGES === 'true' ? 90 : 80,   // PNG needs higher quality\r\n  maxDevicePixelRatio: 3,             // Maximum device pixel ratio to support for high-DPI screens\r\n  minWidth: 20,                       // Minimum width to optimize (smaller will pass through)\r\n  blurUpRadius: 10,                   // Blur radius for placeholder images\r\n};\r\n\r\n/**\r\n * Determines if an image should be optimized or passed through as-is\r\n */\r\nfunction shouldOptimizeImage(src) {\r\n  if (!src || CONFIG.disableOptimization) return false;\r\n  \r\n  // Skip optimization for these formats\r\n  const skipFormats = ['.svg', '.gif', '.webp', '.avif'];\r\n  if (typeof src === 'string' && skipFormats.some(format => src.toLowerCase().endsWith(format))) {\r\n    return false;\r\n  }\r\n  \r\n  // Skip optimization for external domains we don't control\r\n  if (src.startsWith('http') && \r\n      !src.includes('strapiapp.com') && \r\n      !src.includes('localhost:1337')) {\r\n    return false;\r\n  }\r\n  \r\n  return true;\r\n}\r\n\r\n/**\r\n * Determines the optimal quality setting based on the image format\r\n */\r\nfunction getOptimalQuality(src, requestedQuality) {\r\n  if (requestedQuality) return requestedQuality;\r\n  \r\n  // Format-specific quality settings\r\n  if (src.toLowerCase().match(/\\.avif$/i)) return CONFIG.avifQuality;\r\n  if (src.toLowerCase().match(/\\.webp$/i)) return CONFIG.webpQuality;\r\n  if (src.toLowerCase().match(/\\.jpe?g$/i)) return CONFIG.jpegQuality;\r\n  if (src.toLowerCase().match(/\\.png$/i)) return CONFIG.pngQuality;\r\n  \r\n  // Default quality based on whether the image appears to be a photo\r\n  const isPhoto = src.toLowerCase().match(/\\.(jpe?g|png)$/i);\r\n  return isPhoto ? CONFIG.jpegQuality : CONFIG.webpQuality;\r\n}\r\n\r\n/**\r\n * Constructs an optimized image URL\r\n */\r\nfunction buildOptimizedUrl(src, width, quality) {\r\n  // Determine base URL for media with fallbacks\r\n  const strapiMediaUrl = process.env.NEXT_PUBLIC_STRAPI_MEDIA_URL || \r\n                         process.env.NEXT_PUBLIC_API_URL || \r\n                         'https://nice-badge-2130241d6c.media.strapiapp.com';\r\n  \r\n  // Get optimal quality setting\r\n  const finalQuality = getOptimalQuality(src, quality);\r\n  \r\n  // Calculate optimal width based on device pixel ratio and requested width\r\n  // This creates sharper images on high-DPI screens\r\n  const devicePixelRatio = typeof window !== 'undefined' ? \r\n    Math.min(window.devicePixelRatio || 1, CONFIG.maxDevicePixelRatio) : 1;\r\n  \r\n  // Never optimize below minimum width\r\n  if (width < CONFIG.minWidth) {\r\n    return src;\r\n  }\r\n  \r\n  // Create a proper URL object for manipulation\r\n  try {\r\n    const optimalWidth = Math.round(width * devicePixelRatio);\r\n    \r\n    const url = new URL(\r\n      src.startsWith('http') \r\n        ? src \r\n        : `${strapiMediaUrl}${src.startsWith('/') ? src : `/${src}`}`\r\n    );\r\n    \r\n    // Only add optimization parameters for Strapi domains we control\r\n    if (url.hostname.includes('strapiapp.com') || \r\n        url.hostname.includes('localhost')) {\r\n      \r\n      // Use content-aware sizing (never upscale small images)\r\n      url.searchParams.set('w', optimalWidth.toString());\r\n      url.searchParams.set('q', finalQuality.toString());\r\n      \r\n      // Check if original is a photo (high entropy) or illustration (low entropy)\r\n      const isPhoto = src.toLowerCase().match(/\\.(jpe?g|png)$/i);\r\n      \r\n      if (!url.searchParams.has('format')) {\r\n        // Prefer AVIF for photos for better compression, WebP for everything else\r\n        const preferredFormat = isPhoto ? 'avif' : 'webp';\r\n        url.searchParams.set('format', preferredFormat);\r\n      }\r\n      \r\n      // Add cache-busting parameter only in development to ensure fresh images\r\n      if (process.env.NODE_ENV === 'development') {\r\n        url.searchParams.set('t', Date.now().toString());\r\n      } else {\r\n        // In production, ensure deterministic URLs for better caching\r\n        // Sort query parameters to ensure consistent cache keys\r\n        const params = Array.from(url.searchParams.entries()).sort();\r\n        url.search = params.map(([k, v]) => `${k}=${v}`).join('&');\r\n      }\r\n      \r\n      // Apply filters for image optimization if needed\r\n      if (isPhoto) {\r\n        // Apply minimal sharpening for photos\r\n        url.searchParams.set('sharp', '10');\r\n      }\r\n      \r\n      // Ensure HTTPS for production\r\n      if (process.env.NODE_ENV === 'production' && url.protocol === 'http:') {\r\n        url.protocol = 'https:';\r\n      }\r\n      \r\n      return url.toString();\r\n    }\r\n    \r\n    return url.toString();\r\n  } catch (e) {\r\n    // If URL parsing fails, construct URL manually as fallback\r\n    if (src.startsWith('/')) {\r\n      return `${strapiMediaUrl}${src}?w=${width}&q=${finalQuality}`;\r\n    }\r\n    \r\n    // Last resort: return original source\r\n    return src;\r\n  }\r\n}\r\n\r\n/**\r\n * The main image loader function\r\n */\r\nexport default function optimizedImageLoader({ src, width, quality }) {\r\n  // Start performance monitoring\r\n  const startTime = CONFIG.enableMetrics ? performance.now() : 0;\r\n  \r\n  // Handle empty source gracefully\r\n  if (!src) return '';\r\n  \r\n  try {\r\n    // Determine if we should optimize this image\r\n    if (!shouldOptimizeImage(src)) {\r\n      return src; // Pass through without optimization\r\n    }\r\n    \r\n    // Build optimized URL\r\n    const finalUrl = buildOptimizedUrl(src, width, quality);\r\n    \r\n    // Performance monitoring in development or when metrics are enabled\r\n    if (CONFIG.enableMetrics && startTime) {\r\n      const duration = performance.now() - startTime;\r\n      metrics.count++;\r\n      metrics.totalTime += duration;\r\n      \r\n      if (duration > metrics.slowestTime) {\r\n        metrics.slowestTime = duration;\r\n        metrics.slowestImage = src;\r\n      }\r\n      \r\n      // Log periodically in development\r\n      if (process.env.NODE_ENV === 'development' && metrics.count % 10 === 0) {\r\n        console.log(`🖼️ Image optimization metrics:\r\n- Processed: ${metrics.count} images\r\n- Avg time: ${(metrics.totalTime / metrics.count).toFixed(2)}ms\r\n- Errors: ${metrics.errors}\r\n- Slowest: ${metrics.slowestTime.toFixed(2)}ms (${metrics.slowestImage.substring(0, 50)}...)\r\n`);\r\n      }\r\n    }\r\n    \r\n    return finalUrl;\r\n  } catch (error) {\r\n    // Increment error counter\r\n    if (CONFIG.enableMetrics) {\r\n      metrics.errors++;\r\n    }\r\n    \r\n    // Log error in development\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.warn('Image loader error:', error, 'for src:', src);\r\n    }\r\n    \r\n    // Safety fallback\r\n    return src;\r\n  }\r\n}\r\n\r\n// Export additional utilities that might be useful\r\nexport const imageUtils = {\r\n  getBlurDataUrl: (src, size = 10) => {\r\n    if (!src) return '';\r\n    // Generate tiny placeholder for blur-up effect\r\n    return `${buildOptimizedUrl(src, size, 10)}&blur=80`;\r\n  },\r\n  \r\n  preloadImage: (src, width) => {\r\n    if (typeof window === 'undefined' || !src) return;\r\n    const img = new Image();\r\n    img.src = optimizedImageLoader({ src, width, quality: CONFIG.defaultQuality });\r\n    return img;\r\n  },\r\n  \r\n  resetMetrics: () => {\r\n    metrics.count = 0;\r\n    metrics.errors = 0;\r\n    metrics.totalTime = 0;\r\n    metrics.slowestTime = 0;\r\n    metrics.slowestImage = '';\r\n  },\r\n  \r\n  getMetrics: () => ({ ...metrics }),\r\n};\r\n"], "names": [], "mappings": "AAAA,yBAAyB;AAEzB;;;;;;;;;;;;;;;;;;CAkBC,GAED,gCAAgC;;;;;AAChC,MAAM,UAAU;IACd,OAAO;IACP,QAAQ;IACR,WAAW;IACX,aAAa;IACb,cAAc;AAChB;AAEA,uDAAuD;AACvD,MAAM,uBAAuB,IAAI;AAEjC,0CAA0C;AAC1C,MAAM,SAAS;IACb,eAAe,QAAQ,GAAG,CAAC,yBAAyB,KAAK;IACzD,gBAAgB,6CAAgD;IAChE,qBAAqB,QAAQ,GAAG,CAAC,sCAAsC,KAAK;IAC5E,gBAAgB,uCAAyD;IACzE,aAAa,uCAAyD;IACtE,aAAa,uCAAyD;IACtE,aAAa,uCAAyD;IACtE,YAAY,uCAAyD;IACrE,qBAAqB;IACrB,UAAU;IACV,cAAc;AAChB;AAEA;;CAEC,GACD,SAAS,oBAAoB,GAAG;IAC9B,IAAI,CAAC,OAAO,OAAO,mBAAmB,EAAE,OAAO;IAE/C,sCAAsC;IACtC,MAAM,cAAc;QAAC;QAAQ;QAAQ;QAAS;KAAQ;IACtD,IAAI,OAAO,QAAQ,YAAY,YAAY,IAAI,CAAC,CAAA,SAAU,IAAI,WAAW,GAAG,QAAQ,CAAC,UAAU;QAC7F,OAAO;IACT;IAEA,0DAA0D;IAC1D,IAAI,IAAI,UAAU,CAAC,WACf,CAAC,IAAI,QAAQ,CAAC,oBACd,CAAC,IAAI,QAAQ,CAAC,mBAAmB;QACnC,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,kBAAkB,GAAG,EAAE,gBAAgB;IAC9C,IAAI,kBAAkB,OAAO;IAE7B,mCAAmC;IACnC,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC,aAAa,OAAO,OAAO,WAAW;IAClE,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC,aAAa,OAAO,OAAO,WAAW;IAClE,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC,cAAc,OAAO,OAAO,WAAW;IACnE,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC,YAAY,OAAO,OAAO,UAAU;IAEhE,mEAAmE;IACnE,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC;IACxC,OAAO,UAAU,OAAO,WAAW,GAAG,OAAO,WAAW;AAC1D;AAEA;;CAEC,GACD,SAAS,kBAAkB,GAAG,EAAE,KAAK,EAAE,OAAO;IAC5C,8CAA8C;IAC9C,MAAM,iBAAiB,4KAEA;IAEvB,8BAA8B;IAC9B,MAAM,eAAe,kBAAkB,KAAK;IAE5C,0EAA0E;IAC1E,kDAAkD;IAClD,MAAM,mBAAmB,6EAC8C;IAEvE,qCAAqC;IACrC,IAAI,QAAQ,OAAO,QAAQ,EAAE;QAC3B,OAAO;IACT;IAEA,8CAA8C;IAC9C,IAAI;QACF,MAAM,eAAe,KAAK,KAAK,CAAC,QAAQ;QAExC,MAAM,MAAM,IAAI,IACd,IAAI,UAAU,CAAC,UACX,MACA,GAAG,iBAAiB,IAAI,UAAU,CAAC,OAAO,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE;QAGjE,iEAAiE;QACjE,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,oBACtB,IAAI,QAAQ,CAAC,QAAQ,CAAC,cAAc;YAEtC,wDAAwD;YACxD,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,aAAa,QAAQ;YAC/C,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,aAAa,QAAQ;YAE/C,4EAA4E;YAC5E,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC;YAExC,IAAI,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,WAAW;gBACnC,0EAA0E;gBAC1E,MAAM,kBAAkB,UAAU,SAAS;gBAC3C,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU;YACjC;YAEA,yEAAyE;YACzE,wCAA4C;gBAC1C,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ;YAC/C,OAAO;;YAKP;YAEA,iDAAiD;YACjD,IAAI,SAAS;gBACX,sCAAsC;gBACtC,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS;YAChC;YAEA,8BAA8B;YAC9B,uCAAuE;;YAEvE;YAEA,OAAO,IAAI,QAAQ;QACrB;QAEA,OAAO,IAAI,QAAQ;IACrB,EAAE,OAAO,GAAG;QACV,2DAA2D;QAC3D,IAAI,IAAI,UAAU,CAAC,MAAM;YACvB,OAAO,GAAG,iBAAiB,IAAI,GAAG,EAAE,MAAM,GAAG,EAAE,cAAc;QAC/D;QAEA,sCAAsC;QACtC,OAAO;IACT;AACF;AAKe,SAAS,qBAAqB,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE;IAClE,+BAA+B;IAC/B,MAAM,YAAY,OAAO,aAAa,GAAG,YAAY,GAAG,KAAK;IAE7D,iCAAiC;IACjC,IAAI,CAAC,KAAK,OAAO;IAEjB,IAAI;QACF,6CAA6C;QAC7C,IAAI,CAAC,oBAAoB,MAAM;YAC7B,OAAO,KAAK,oCAAoC;QAClD;QAEA,sBAAsB;QACtB,MAAM,WAAW,kBAAkB,KAAK,OAAO;QAE/C,oEAAoE;QACpE,IAAI,OAAO,aAAa,IAAI,WAAW;YACrC,MAAM,WAAW,YAAY,GAAG,KAAK;YACrC,QAAQ,KAAK;YACb,QAAQ,SAAS,IAAI;YAErB,IAAI,WAAW,QAAQ,WAAW,EAAE;gBAClC,QAAQ,WAAW,GAAG;gBACtB,QAAQ,YAAY,GAAG;YACzB;YAEA,kCAAkC;YAClC,IAAI,oDAAyB,iBAAiB,QAAQ,KAAK,GAAG,OAAO,GAAG;gBACtE,QAAQ,GAAG,CAAC,CAAC;aACR,EAAE,QAAQ,KAAK,CAAC;YACjB,EAAE,CAAC,QAAQ,SAAS,GAAG,QAAQ,KAAK,EAAE,OAAO,CAAC,GAAG;UACnD,EAAE,QAAQ,MAAM,CAAC;WAChB,EAAE,QAAQ,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,QAAQ,YAAY,CAAC,SAAS,CAAC,GAAG,IAAI;AACxF,CAAC;YACK;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,0BAA0B;QAC1B,IAAI,OAAO,aAAa,EAAE;YACxB,QAAQ,MAAM;QAChB;QAEA,2BAA2B;QAC3B,wCAA4C;YAC1C,QAAQ,IAAI,CAAC,uBAAuB,OAAO,YAAY;QACzD;QAEA,kBAAkB;QAClB,OAAO;IACT;AACF;AAGO,MAAM,aAAa;IACxB,gBAAgB,CAAC,KAAK,OAAO,EAAE;QAC7B,IAAI,CAAC,KAAK,OAAO;QACjB,+CAA+C;QAC/C,OAAO,GAAG,kBAAkB,KAAK,MAAM,IAAI,QAAQ,CAAC;IACtD;IAEA,cAAc,CAAC,KAAK;QAClB,wCAA2C;;QAC3C,MAAM;IAGR;IAEA,cAAc;QACZ,QAAQ,KAAK,GAAG;QAChB,QAAQ,MAAM,GAAG;QACjB,QAAQ,SAAS,GAAG;QACpB,QAAQ,WAAW,GAAG;QACtB,QAAQ,YAAY,GAAG;IACzB;IAEA,YAAY,IAAM,CAAC;YAAE,GAAG,OAAO;QAAC,CAAC;AACnC", "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/LazyImage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useRef, memo } from 'react';\r\nimport Image, { ImageProps } from 'next/image';\r\nimport { FiImage } from 'react-icons/fi';\r\nimport { imageUtils } from '@/lib/imageLoader';\r\n\r\ninterface LazyImageProps extends Omit<ImageProps, 'onLoad' | 'onError' | 'blurDataURL'> {\r\n  /**\r\n   * CSS class for the fallback container when image is not loaded\r\n   */\r\n  fallbackClassName?: string;\r\n\r\n  /**\r\n   * Whether to show a blur placeholder while loading\r\n   */\r\n  showPlaceholder?: boolean;\r\n  \r\n  /**\r\n   * Quality level for the image (will override default quality settings)\r\n   * Higher values = better quality but larger file size\r\n   */\r\n  qualityOverride?: number;\r\n  \r\n  /**\r\n   * Enable advanced blur-up placeholder technique\r\n   */\r\n  advancedBlur?: boolean;\r\n  \r\n  /**\r\n   * Whether to preload the image before it enters the viewport\r\n   * Good for images that are just below the fold\r\n   */\r\n  preload?: boolean;\r\n  \r\n  /**\r\n   * Whether to add fade-in animation when image loads\r\n   */\r\n  fadeIn?: boolean;\r\n  \r\n  /**\r\n   * Element to use as a wrapper (div or picture)\r\n   */\r\n  wrapperAs?: 'div' | 'picture';\r\n  \r\n  /**\r\n   * If true, the image will fill its parent container.\r\n   * The parent container must have `position: relative`.\r\n   * `width` and `height` props will be used for aspect ratio and `srcset` but not for fixed sizing.\r\n   */\r\n  fillContainer?: boolean;\r\n}\r\n\r\n/**\r\n * High-Performance LazyImage component with advanced optimizations\r\n * - Uses next/image with additional performance enhancements\r\n * - Progressive loading with intelligent blur-up technique\r\n * - Smart preloading for critical images\r\n * - Avoids layout shifts with aspect ratio preservation\r\n * - Efficient resource loading with modern browser hints\r\n * - Advanced error handling with graceful degradation\r\n * - Zero overhead when fully loaded\r\n */\r\nconst LazyImage: React.FC<LazyImageProps> = ({\r\n  src,\r\n  alt,\r\n  width,\r\n  height,\r\n  className = '',\r\n  fallbackClassName = '',\r\n  showPlaceholder = true,\r\n  advancedBlur = false,\r\n  preload = false,\r\n  fadeIn = true,\r\n  wrapperAs = 'div',\r\n  fillContainer = false,\r\n  sizes = '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw', // More optimized responsive sizes\r\n  style,\r\n  priority = false,\r\n  qualityOverride,\r\n  ...rest\r\n}) => {\r\n  // Track component mounted state to prevent state updates after unmount\r\n  const isMounted = useRef(true);\r\n  \r\n  // Single state to track loading status: \"loading\" | \"loaded\" | \"error\"\r\n  const [loadState, setLoadState] = useState<'loading' | 'loaded' | 'error'>(\r\n    priority ? 'loaded' : 'loading' // Assume priority images are already loaded for better UX\r\n  );\r\n\r\n  // Keep track of actual dimensions for aspect ratio calculations\r\n  // If fillContainer is true, width/height props are for aspect ratio, not fixed size.\r\n  const [dimensions, setDimensions] = useState({ \r\n    width: fillContainer ? undefined : width, \r\n    height: fillContainer ? undefined : height \r\n  });\r\n  \r\n  // Process source with enhanced handling for various formats\r\n  const imageSrc = typeof src === 'string' \r\n    ? src \r\n    : (src as any)?.src || (src as any)?.url || (src as any)?.default?.src || null;\r\n\r\n  // Advanced blur data URL generation using our optimized loader\r\n  const blurDataURL = advancedBlur && showPlaceholder && imageSrc\r\n    ? imageUtils.getBlurDataUrl(imageSrc, 20) // Generate an optimized tiny preview image\r\n    : showPlaceholder \r\n      ? 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+'\r\n      : undefined;\r\n\r\n  // Handle preloading for important images that are just outside the viewport\r\n  useEffect(() => {\r\n    if (preload && imageSrc && !priority && typeof window !== 'undefined') {\r\n      const img = imageUtils.preloadImage(imageSrc, typeof width === 'number' ? width : 500);\r\n      \r\n      // When preloaded, update state if still mounted\r\n      if (img) {\r\n        img.onload = () => {\r\n          if (isMounted.current) {\r\n            setLoadState('loaded');\r\n          }\r\n        };\r\n        \r\n        img.onerror = () => {\r\n          if (isMounted.current) {\r\n            setLoadState('error');\r\n          }\r\n        };\r\n      }\r\n    }\r\n    \r\n    // Clean up on unmount\r\n    return () => {\r\n      isMounted.current = false;\r\n    };\r\n  }, [imageSrc, preload, priority, width]);\r\n  \r\n  // Calculate aspect ratio for responsive images\r\n  const aspectRatio = \r\n    typeof width === 'number' && typeof height === 'number' && width > 0 && height > 0\r\n      ? width / height\r\n      : undefined;\r\n\r\n  // Memoized callbacks to prevent recreating functions on each render\r\n  const handleLoad = useCallback((event: any) => {\r\n    // Update with actual loaded dimensions to prevent layout shift, only if not in fillContainer mode\r\n    if (!fillContainer && event?.target) {\r\n      const { naturalWidth, naturalHeight } = event.target;\r\n      if (naturalWidth && naturalHeight) {\r\n        if (isMounted.current) {\r\n          setDimensions({ width: naturalWidth, height: naturalHeight });\r\n        }\r\n      }\r\n    }\r\n    if (isMounted.current) {\r\n      setLoadState('loaded');\r\n    }\r\n  }, [fillContainer]);\r\n\r\n  const handleError = useCallback(() => {\r\n    setLoadState('error');\r\n    // Only log in development to avoid excessive logs in production\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.error('Image failed to load:', { src: imageSrc, alt });\r\n    }\r\n  }, [imageSrc, alt]);\r\n\r\n  // Calculate loading strategy based on priority\r\n  const loadingStrategy = priority ? 'eager' : 'lazy';\r\n\r\n  // Calculate placeholder strategy\r\n  const placeholderStrategy = showPlaceholder ? 'blur' : 'empty';\r\n\r\n  // Render fallback for errors or missing source\r\n  if (loadState === 'error' || !imageSrc) {\r\n    return (\r\n      <div\r\n        className={`flex items-center justify-center bg-gray-100 ${fallbackClassName || (fillContainer ? '' : className)}`}\r\n        style={{ \r\n          width: fillContainer ? '100%' : width, \r\n          height: fillContainer ? '100%' : height, \r\n          aspectRatio: aspectRatio ? `${aspectRatio}` : undefined,\r\n          ...style \r\n        }}\r\n        role=\"img\"\r\n        aria-label={alt || 'Image failed to load'}\r\n      >\r\n        <FiImage className=\"text-gray-400 w-1/5 h-1/5\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Combine transition classes for smoother loading experience\r\n  const imageClasses = [\r\n    className,\r\n    loadState === 'loaded' ? 'opacity-100' : 'opacity-0',\r\n    fadeIn ? 'transition-opacity duration-300' : ''\r\n  ].filter(Boolean).join(' ');\r\n\r\n  // Create responsive style object with aspect ratio preservation\r\n  const responsiveStyle = {\r\n    objectFit: (style?.objectFit || 'cover') as 'cover' | 'contain' | 'fill' | 'none' | 'scale-down',\r\n    aspectRatio: fillContainer ? undefined : (aspectRatio ? `${aspectRatio}` : undefined), // Aspect ratio handled by parent in fill mode\r\n    ...style,\r\n    width: fillContainer ? undefined : style?.width, // Let fill handle width/height\r\n    height: fillContainer ? undefined : style?.height,\r\n  };\r\n\r\n  // Image render with optimized props\r\n  const imageElement = (\r\n    <Image\r\n      src={imageSrc}\r\n      alt={alt || ''}\r\n      width={fillContainer ? undefined : dimensions.width} // next/image requires width/height unless fill is true\r\n      height={fillContainer ? undefined : dimensions.height}\r\n      fill={fillContainer}\r\n      className={imageClasses} // className from props is applied here\r\n      loading={loadingStrategy}\r\n      fetchPriority={priority ? \"high\" : preload ? \"low\" : \"auto\"}\r\n      priority={priority}\r\n      sizes={sizes}\r\n      style={responsiveStyle}\r\n      placeholder={placeholderStrategy}\r\n      blurDataURL={blurDataURL}\r\n      onLoad={handleLoad}\r\n      onError={handleError}\r\n      quality={qualityOverride}\r\n      {...rest}\r\n    />\r\n  );\r\n\r\n  // Wrapper element (div or picture) with appropriate ARIA attributes\r\n  const WrapperElement = wrapperAs as any;\r\n  \r\n  const wrapperStyle = fillContainer \r\n    ? { width: '100%', height: '100%', position: 'relative' as const, ...style } // Ensure parent has dimensions\r\n    : { width: dimensions.width, height: dimensions.height, aspectRatio: aspectRatio ? `${aspectRatio}` : undefined, position: 'relative' as const, ...style };\r\n\r\n  // If fillContainer, className from props should apply to the wrapper for sizing, not the image.\r\n  // However, className often contains object-fit, which should apply to the image.\r\n  // This is tricky. For now, className from props is passed to Image, assuming it contains object-fit.\r\n  // If fillContainer, the wrapper takes full size of its parent.\r\n\r\n  return (\r\n    <WrapperElement \r\n      className={`relative ${fillContainer ? 'w-full h-full' : ''}`} \r\n      style={wrapperStyle}\r\n    >\r\n      {imageElement}\r\n      \r\n      {/* Enhanced loading placeholder with ARIA attributes for accessibility */}\r\n      {loadState === 'loading' && showPlaceholder && (\r\n        <div\r\n          className={`absolute inset-0 bg-gray-100 animate-pulse ${fallbackClassName || ''}`}\r\n          style={{ width: '100%', height: '100%' }} // Placeholder should always fill the wrapper\r\n          aria-hidden=\"true\"\r\n        />\r\n      )}\r\n    </WrapperElement>\r\n  );\r\n};\r\n\r\n// Use displayName for better debugging\r\nLazyImage.displayName = 'LazyImage';\r\n\r\n// Memoize the component to prevent unnecessary re-renders\r\nexport default memo(LazyImage);\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAqDA;;;;;;;;;CASC,GACD,MAAM,YAAsC,CAAC,EAC3C,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACd,oBAAoB,EAAE,EACtB,kBAAkB,IAAI,EACtB,eAAe,KAAK,EACpB,UAAU,KAAK,EACf,SAAS,IAAI,EACb,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,QAAQ,0DAA0D,EAClE,KAAK,EACL,WAAW,KAAK,EAChB,eAAe,EACf,GAAG,MACJ;IACC,uEAAuE;IACvE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,uEAAuE;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvC,WAAW,WAAW,UAAU,0DAA0D;;IAG5F,gEAAgE;IAChE,qFAAqF;IACrF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO,gBAAgB,YAAY;QACnC,QAAQ,gBAAgB,YAAY;IACtC;IAEA,4DAA4D;IAC5D,MAAM,WAAW,OAAO,QAAQ,WAC5B,MACA,AAAC,KAAa,OAAQ,KAAa,OAAQ,KAAa,SAAS,OAAO;IAE5E,+DAA+D;IAC/D,MAAM,cAAc,gBAAgB,mBAAmB,WACnD,yHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,UAAU,IAAI,2CAA2C;OACnF,kBACE,+NACA;IAEN,4EAA4E;IAC5E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAuE;;QAiBvE;QAEA,sBAAsB;QACtB,OAAO;YACL,UAAU,OAAO,GAAG;QACtB;IACF,GAAG;QAAC;QAAU;QAAS;QAAU;KAAM;IAEvC,+CAA+C;IAC/C,MAAM,cACJ,OAAO,UAAU,YAAY,OAAO,WAAW,YAAY,QAAQ,KAAK,SAAS,IAC7E,QAAQ,SACR;IAEN,oEAAoE;IACpE,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,kGAAkG;QAClG,IAAI,CAAC,iBAAiB,OAAO,QAAQ;YACnC,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,MAAM,MAAM;YACpD,IAAI,gBAAgB,eAAe;gBACjC,IAAI,UAAU,OAAO,EAAE;oBACrB,cAAc;wBAAE,OAAO;wBAAc,QAAQ;oBAAc;gBAC7D;YACF;QACF;QACA,IAAI,UAAU,OAAO,EAAE;YACrB,aAAa;QACf;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,aAAa;QACb,gEAAgE;QAChE,wCAA4C;YAC1C,QAAQ,KAAK,CAAC,yBAAyB;gBAAE,KAAK;gBAAU;YAAI;QAC9D;IACF,GAAG;QAAC;QAAU;KAAI;IAElB,+CAA+C;IAC/C,MAAM,kBAAkB,WAAW,UAAU;IAE7C,iCAAiC;IACjC,MAAM,sBAAsB,kBAAkB,SAAS;IAEvD,+CAA+C;IAC/C,IAAI,cAAc,WAAW,CAAC,UAAU;QACtC,qBACE,8OAAC;YACC,WAAW,CAAC,6CAA6C,EAAE,qBAAqB,CAAC,gBAAgB,KAAK,SAAS,GAAG;YAClH,OAAO;gBACL,OAAO,gBAAgB,SAAS;gBAChC,QAAQ,gBAAgB,SAAS;gBACjC,aAAa,cAAc,GAAG,aAAa,GAAG;gBAC9C,GAAG,KAAK;YACV;YACA,MAAK;YACL,cAAY,OAAO;sBAEnB,cAAA,8OAAC,8IAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,6DAA6D;IAC7D,MAAM,eAAe;QACnB;QACA,cAAc,WAAW,gBAAgB;QACzC,SAAS,oCAAoC;KAC9C,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,gEAAgE;IAChE,MAAM,kBAAkB;QACtB,WAAY,OAAO,aAAa;QAChC,aAAa,gBAAgB,YAAa,cAAc,GAAG,aAAa,GAAG;QAC3E,GAAG,KAAK;QACR,OAAO,gBAAgB,YAAY,OAAO;QAC1C,QAAQ,gBAAgB,YAAY,OAAO;IAC7C;IAEA,oCAAoC;IACpC,MAAM,6BACJ,8OAAC,6HAAA,CAAA,UAAK;QACJ,KAAK;QACL,KAAK,OAAO;QACZ,OAAO,gBAAgB,YAAY,WAAW,KAAK;QACnD,QAAQ,gBAAgB,YAAY,WAAW,MAAM;QACrD,MAAM;QACN,WAAW;QACX,SAAS;QACT,eAAe,WAAW,SAAS,UAAU,QAAQ;QACrD,UAAU;QACV,OAAO;QACP,OAAO;QACP,aAAa;QACb,aAAa;QACb,QAAQ;QACR,SAAS;QACT,SAAS;QACR,GAAG,IAAI;;;;;;IAIZ,oEAAoE;IACpE,MAAM,iBAAiB;IAEvB,MAAM,eAAe,gBACjB;QAAE,OAAO;QAAQ,QAAQ;QAAQ,UAAU;QAAqB,GAAG,KAAK;IAAC,EAAE,+BAA+B;OAC1G;QAAE,OAAO,WAAW,KAAK;QAAE,QAAQ,WAAW,MAAM;QAAE,aAAa,cAAc,GAAG,aAAa,GAAG;QAAW,UAAU;QAAqB,GAAG,KAAK;IAAC;IAE3J,gGAAgG;IAChG,iFAAiF;IACjF,qGAAqG;IACrG,+DAA+D;IAE/D,qBACE,8OAAC;QACC,WAAW,CAAC,SAAS,EAAE,gBAAgB,kBAAkB,IAAI;QAC7D,OAAO;;YAEN;YAGA,cAAc,aAAa,iCAC1B,8OAAC;gBACC,WAAW,CAAC,2CAA2C,EAAE,qBAAqB,IAAI;gBAClF,OAAO;oBAAE,OAAO;oBAAQ,QAAQ;gBAAO;gBACvC,eAAY;;;;;;;;;;;;AAKtB;AAEA,uCAAuC;AACvC,UAAU,WAAW,GAAG;qDAGT,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1072, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/apiUtils.ts"], "sourcesContent": ["/**\r\n * API utilities for making authenticated requests to Strapi\r\n * Handles both client-side and server-side requests\r\n */\r\nimport axios, { AxiosRequestConfig } from 'axios';\r\n\r\n// Get Strapi URL from environment variable\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1337';\r\nconst API_PATH = '/api';\r\n\r\n/**\r\n * Creates an authenticated API client for server-side requests\r\n * Uses the API token from environment variables\r\n */\r\nexport const createServerSideApiClient = () => {\r\n  const API_TOKEN = process.env.STRAPI_API_TOKEN;\r\n\r\n  if (!API_TOKEN) {\r\n    console.warn('No STRAPI_API_TOKEN found in environment variables for server-side API client');\r\n  }\r\n\r\n  return axios.create({\r\n    baseURL: `${API_URL}${API_PATH}`,\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      ...(API_TOKEN ? { Authorization: `Bearer ${API_TOKEN}` } : {}),\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Creates an authenticated API client for client-side requests\r\n * Uses the JWT token from localStorage for user authentication\r\n */\r\nexport const createClientSideApiClient = () => {\r\n  // Only access localStorage in the browser\r\n  const getToken = () => {\r\n    if (typeof window !== 'undefined') {\r\n      return localStorage.getItem('jwt');\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const token = getToken();\r\n\r\n  return axios.create({\r\n    baseURL: `${API_URL}${API_PATH}`,\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { Authorization: `Bearer ${token}` } : {}),\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Makes a GET request to the Strapi API\r\n * Automatically determines whether to use server-side or client-side authentication\r\n */\r\nexport const fetchFromApi = async <T>(\r\n  endpoint: string,\r\n  options: AxiosRequestConfig = {},\r\n  isServerSide = typeof window === 'undefined'\r\n): Promise<T> => {\r\n  try {\r\n    const apiClient = isServerSide\r\n      ? createServerSideApiClient()\r\n      : createClientSideApiClient();\r\n\r\n    const response = await apiClient.get<T>(endpoint, options);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(`Error fetching from API (${endpoint}):`, error);\r\n\r\n    if (error.response) {\r\n      console.error('Response data:', error.response.data);\r\n      console.error('Response status:', error.response.status);\r\n    }\r\n\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Makes a POST request to the Strapi API\r\n * Automatically determines whether to use server-side or client-side authentication\r\n */\r\nexport const postToApi = async <T>(\r\n  endpoint: string,\r\n  data: any,\r\n  options: AxiosRequestConfig = {},\r\n  isServerSide = typeof window === 'undefined'\r\n): Promise<T> => {\r\n  try {\r\n    const apiClient = isServerSide\r\n      ? createServerSideApiClient()\r\n      : createClientSideApiClient();\r\n\r\n    const response = await apiClient.post<T>(endpoint, data, options);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(`Error posting to API (${endpoint}):`, error);\r\n\r\n    if (error.response) {\r\n      console.error('Response data:', error.response.data);\r\n      console.error('Response status:', error.response.status);\r\n    }\r\n\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Makes a PUT request to the Strapi API\r\n * Automatically determines whether to use server-side or client-side authentication\r\n */\r\nexport const putToApi = async <T>(\r\n  endpoint: string,\r\n  data: any,\r\n  options: AxiosRequestConfig = {},\r\n  isServerSide = typeof window === 'undefined'\r\n): Promise<T> => {\r\n  try {\r\n    const apiClient = isServerSide\r\n      ? createServerSideApiClient()\r\n      : createClientSideApiClient();\r\n\r\n    const response = await apiClient.put<T>(endpoint, data, options);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(`Error putting to API (${endpoint}):`, error);\r\n\r\n    if (error.response) {\r\n      console.error('Response data:', error.response.data);\r\n      console.error('Response status:', error.response.status);\r\n    }\r\n\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Makes a DELETE request to the Strapi API\r\n * Automatically determines whether to use server-side or client-side authentication\r\n */\r\nexport const deleteFromApi = async <T>(\r\n  endpoint: string,\r\n  options: AxiosRequestConfig = {},\r\n  isServerSide = typeof window === 'undefined'\r\n): Promise<T> => {\r\n  try {\r\n    const apiClient = isServerSide\r\n      ? createServerSideApiClient()\r\n      : createClientSideApiClient();\r\n\r\n    const response = await apiClient.delete<T>(endpoint, options);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error(`Error deleting from API (${endpoint}):`, error);\r\n\r\n    if (error.response) {\r\n      console.error('Response data:', error.response.data);\r\n      console.error('Response status:', error.response.status);\r\n    }\r\n\r\n    throw error;\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AACD;;AAEA,2CAA2C;AAC3C,MAAM,UAAU,mFAAmC;AACnD,MAAM,WAAW;AAMV,MAAM,4BAA4B;IACvC,MAAM,YAAY,QAAQ,GAAG,CAAC,gBAAgB;IAE9C,IAAI,CAAC,WAAW;QACd,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAClB,SAAS,GAAG,UAAU,UAAU;QAChC,SAAS;YACP,gBAAgB;YAChB,GAAI,YAAY;gBAAE,eAAe,CAAC,OAAO,EAAE,WAAW;YAAC,IAAI,CAAC,CAAC;QAC/D;IACF;AACF;AAMO,MAAM,4BAA4B;IACvC,0CAA0C;IAC1C,MAAM,WAAW;QACf,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEA,MAAM,QAAQ;IAEd,OAAO,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAClB,SAAS,GAAG,UAAU,UAAU;QAChC,SAAS;YACP,gBAAgB;YAChB,GAAI,QAAQ;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC,IAAI,CAAC,CAAC;QACvD;IACF;AACF;AAMO,MAAM,eAAe,OAC1B,UACA,UAA8B,CAAC,CAAC,EAChC,eAAe,gBAAkB,WAAW;IAE5C,IAAI;QACF,MAAM,YAAY,eACd,8BACA;QAEJ,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,UAAU;QAClD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,SAAS,EAAE,CAAC,EAAE;QAExD,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACnD,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;QACzD;QAEA,MAAM;IACR;AACF;AAMO,MAAM,YAAY,OACvB,UACA,MACA,UAA8B,CAAC,CAAC,EAChC,eAAe,gBAAkB,WAAW;IAE5C,IAAI;QACF,MAAM,YAAY,eACd,8BACA;QAEJ,MAAM,WAAW,MAAM,UAAU,IAAI,CAAI,UAAU,MAAM;QACzD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,EAAE,CAAC,EAAE;QAErD,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACnD,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;QACzD;QAEA,MAAM;IACR;AACF;AAMO,MAAM,WAAW,OACtB,UACA,MACA,UAA8B,CAAC,CAAC,EAChC,eAAe,gBAAkB,WAAW;IAE5C,IAAI;QACF,MAAM,YAAY,eACd,8BACA;QAEJ,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,UAAU,MAAM;QACxD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,EAAE,CAAC,EAAE;QAErD,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACnD,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;QACzD;QAEA,MAAM;IACR;AACF;AAMO,MAAM,gBAAgB,OAC3B,UACA,UAA8B,CAAC,CAAC,EAChC,eAAe,gBAAkB,WAAW;IAE5C,IAAI;QACF,MAAM,YAAY,eACd,8BACA;QAEJ,MAAM,WAAW,MAAM,UAAU,MAAM,CAAI,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,SAAS,EAAE,CAAC,EAAE;QAExD,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YACnD,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;QACzD;QAEA,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/enhancedApiUtils.ts"], "sourcesContent": ["import axios, { AxiosRequestConfig, AxiosError } from 'axios';\nimport { fetchFromApi, postTo<PERSON>pi, putToApi, deleteFrom<PERSON><PERSON> } from './apiUtils';\n\n// Maximum number of retries for transient errors\nconst MAX_RETRIES = 2;\n\n// Delay between retries in milliseconds (exponential backoff)\nconst getRetryDelay = (retryCount: number) => Math.pow(2, retryCount) * 1000;\n\n// Error types that might be transient and worth retrying\nconst TRANSIENT_ERROR_CODES = [408, 429, 500, 502, 503, 504];\n\n/**\n * Determines if an error is likely transient and worth retrying\n */\nconst isTransientError = (error: AxiosError): boolean => {\n  if (!error.response) {\n    // Network errors are often transient\n    return true;\n  }\n  \n  return TRANSIENT_ERROR_CODES.includes(error.response.status);\n};\n\n/**\n * Enhanced version of fetch<PERSON>rom<PERSON><PERSON> with retry logic for transient errors\n */\nexport const enhancedFetchFromApi = async <T>(\n  endpoint: string,\n  options: AxiosRequestConfig = {},\n  isServerSide = typeof window === 'undefined',\n  retryCount = 0\n): Promise<T> => {\n  try {\n    return await fetchFromApi<T>(endpoint, options, isServerSide);\n  } catch (error) {\n    if (\n      axios.isAxiosError(error) && \n      isTransientError(error) && \n      retryCount < MAX_RETRIES\n    ) {\n      // Wait before retrying (exponential backoff)\n      const delay = getRetryDelay(retryCount);\n      console.warn(`Retrying API request to ${endpoint} after ${delay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`);\n      \n      await new Promise(resolve => setTimeout(resolve, delay));\n      \n      // Retry the request\n      return enhancedFetchFromApi<T>(endpoint, options, isServerSide, retryCount + 1);\n    }\n    \n    // Format error for better debugging\n    const formattedError = formatApiError(error, endpoint);\n    throw formattedError;\n  }\n};\n\n/**\n * Enhanced version of postToApi with retry logic for transient errors\n */\nexport const enhancedPostToApi = async <T>(\n  endpoint: string,\n  data: any,\n  options: AxiosRequestConfig = {},\n  isServerSide = typeof window === 'undefined',\n  retryCount = 0\n): Promise<T> => {\n  try {\n    return await postToApi<T>(endpoint, data, options, isServerSide);\n  } catch (error) {\n    if (\n      axios.isAxiosError(error) && \n      isTransientError(error) && \n      retryCount < MAX_RETRIES\n    ) {\n      const delay = getRetryDelay(retryCount);\n      console.warn(`Retrying API POST to ${endpoint} after ${delay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`);\n      \n      await new Promise(resolve => setTimeout(resolve, delay));\n      \n      return enhancedPostToApi<T>(endpoint, data, options, isServerSide, retryCount + 1);\n    }\n    \n    const formattedError = formatApiError(error, endpoint);\n    throw formattedError;\n  }\n};\n\n/**\n * Formats API errors for better debugging and user feedback\n */\nexport const formatApiError = (error: any, endpoint: string): Error => {\n  let errorMessage = `API Error (${endpoint}): `;\n  \n  if (axios.isAxiosError(error)) {\n    if (error.response) {\n      // Server responded with an error status\n      const status = error.response.status;\n      const data = error.response.data;\n      \n      errorMessage += `${status} - ${data?.error?.message || 'Unknown server error'}`;\n      \n      // Create a custom error with additional properties\n      const customError = new Error(errorMessage);\n      (customError as any).status = status;\n      (customError as any).endpoint = endpoint;\n      (customError as any).responseData = data;\n      \n      return customError;\n    } else if (error.request) {\n      // Request was made but no response received\n      errorMessage += 'No response received from server';\n      \n      const customError = new Error(errorMessage);\n      (customError as any).endpoint = endpoint;\n      (customError as any).isNetworkError = true;\n      \n      return customError;\n    }\n  }\n  \n  // For non-Axios errors\n  errorMessage += error.message || 'Unknown error';\n  return new Error(errorMessage);\n};\n\n// Export other enhanced API utilities as needed\nexport const enhancedPutToApi = async <T>(\n  endpoint: string,\n  data: any,\n  options: AxiosRequestConfig = {},\n  isServerSide = typeof window === 'undefined'\n): Promise<T> => {\n  try {\n    return await putToApi<T>(endpoint, data, options, isServerSide);\n  } catch (error) {\n    const formattedError = formatApiError(error, endpoint);\n    throw formattedError;\n  }\n};\n\nexport const enhancedDeleteFromApi = async <T>(\n  endpoint: string,\n  options: AxiosRequestConfig = {},\n  isServerSide = typeof window === 'undefined'\n): Promise<T> => {\n  try {\n    return await deleteFromApi<T>(endpoint, options, isServerSide);\n  } catch (error) {\n    const formattedError = formatApiError(error, endpoint);\n    throw formattedError;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEA,iDAAiD;AACjD,MAAM,cAAc;AAEpB,8DAA8D;AAC9D,MAAM,gBAAgB,CAAC,aAAuB,KAAK,GAAG,CAAC,GAAG,cAAc;AAExE,yDAAyD;AACzD,MAAM,wBAAwB;IAAC;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AAE5D;;CAEC,GACD,MAAM,mBAAmB,CAAC;IACxB,IAAI,CAAC,MAAM,QAAQ,EAAE;QACnB,qCAAqC;QACrC,OAAO;IACT;IAEA,OAAO,sBAAsB,QAAQ,CAAC,MAAM,QAAQ,CAAC,MAAM;AAC7D;AAKO,MAAM,uBAAuB,OAClC,UACA,UAA8B,CAAC,CAAC,EAChC,eAAe,gBAAkB,WAAW,EAC5C,aAAa,CAAC;IAEd,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAK,UAAU,SAAS;IAClD,EAAE,OAAO,OAAO;QACd,IACE,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UACnB,iBAAiB,UACjB,aAAa,aACb;YACA,6CAA6C;YAC7C,MAAM,QAAQ,cAAc;YAC5B,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,SAAS,OAAO,EAAE,MAAM,YAAY,EAAE,aAAa,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;YAE9G,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,oBAAoB;YACpB,OAAO,qBAAwB,UAAU,SAAS,cAAc,aAAa;QAC/E;QAEA,oCAAoC;QACpC,MAAM,iBAAiB,eAAe,OAAO;QAC7C,MAAM;IACR;AACF;AAKO,MAAM,oBAAoB,OAC/B,UACA,MACA,UAA8B,CAAC,CAAC,EAChC,eAAe,gBAAkB,WAAW,EAC5C,aAAa,CAAC;IAEd,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAK,UAAU,MAAM,SAAS;IACrD,EAAE,OAAO,OAAO;QACd,IACE,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UACnB,iBAAiB,UACjB,aAAa,aACb;YACA,MAAM,QAAQ,cAAc;YAC5B,QAAQ,IAAI,CAAC,CAAC,qBAAqB,EAAE,SAAS,OAAO,EAAE,MAAM,YAAY,EAAE,aAAa,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;YAE3G,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO,kBAAqB,UAAU,MAAM,SAAS,cAAc,aAAa;QAClF;QAEA,MAAM,iBAAiB,eAAe,OAAO;QAC7C,MAAM;IACR;AACF;AAKO,MAAM,iBAAiB,CAAC,OAAY;IACzC,IAAI,eAAe,CAAC,WAAW,EAAE,SAAS,GAAG,CAAC;IAE9C,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,QAAQ;QAC7B,IAAI,MAAM,QAAQ,EAAE;YAClB,wCAAwC;YACxC,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;YACpC,MAAM,OAAO,MAAM,QAAQ,CAAC,IAAI;YAEhC,gBAAgB,GAAG,OAAO,GAAG,EAAE,MAAM,OAAO,WAAW,wBAAwB;YAE/E,mDAAmD;YACnD,MAAM,cAAc,IAAI,MAAM;YAC7B,YAAoB,MAAM,GAAG;YAC7B,YAAoB,QAAQ,GAAG;YAC/B,YAAoB,YAAY,GAAG;YAEpC,OAAO;QACT,OAAO,IAAI,MAAM,OAAO,EAAE;YACxB,4CAA4C;YAC5C,gBAAgB;YAEhB,MAAM,cAAc,IAAI,MAAM;YAC7B,YAAoB,QAAQ,GAAG;YAC/B,YAAoB,cAAc,GAAG;YAEtC,OAAO;QACT;IACF;IAEA,uBAAuB;IACvB,gBAAgB,MAAM,OAAO,IAAI;IACjC,OAAO,IAAI,MAAM;AACnB;AAGO,MAAM,mBAAmB,OAC9B,UACA,MACA,UAA8B,CAAC,CAAC,EAChC,eAAe,gBAAkB,WAAW;IAE5C,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAK,UAAU,MAAM,SAAS;IACpD,EAAE,OAAO,OAAO;QACd,MAAM,iBAAiB,eAAe,OAAO;QAC7C,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OACnC,UACA,UAA8B,CAAC,CAAC,EAChC,eAAe,gBAAkB,WAAW;IAE5C,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAK,UAAU,SAAS;IACnD,EAAE,OAAO,OAAO;QACd,MAAM,iBAAiB,eAAe,OAAO;QAC7C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/apiOptimization.ts"], "sourcesContent": ["import { AxiosRequestConfig } from 'axios';\nimport { enhancedFetchFromApi } from './enhancedApiUtils';\n\n/**\n * Cache storage for API responses\n * Key: Cache key (endpoint + serialized params)\n * Value: { data, timestamp, ttl }\n */\ninterface CacheEntry<T> {\n  data: T;\n  timestamp: number;\n  ttl: number;\n}\n\nconst apiCache = new Map<string, CacheEntry<any>>();\n\n/**\n * Generate a cache key from endpoint and params\n */\nexport function generateCacheKey(endpoint: string, params?: any): string {\n  return `${endpoint}:${JSON.stringify(params || {})}`;\n}\n\n/**\n * Check if a cache entry is still valid\n */\nexport function isCacheValid<T>(cacheEntry: CacheEntry<T> | undefined): boolean {\n  if (!cacheEntry) return false;\n  \n  const now = Date.now();\n  const expiryTime = cacheEntry.timestamp + cacheEntry.ttl;\n  \n  return now < expiryTime;\n}\n\n/**\n * Fetch data from API with caching\n * \n * @param endpoint API endpoint\n * @param options Axios request options\n * @param ttl Cache TTL in milliseconds (default: 5 minutes)\n * @param bypassCache Whether to bypass the cache and force a fresh fetch\n * @returns Promise with the API response\n */\nexport async function fetchWithCache<T>(\n  endpoint: string,\n  options: AxiosRequestConfig = {},\n  ttl: number = 5 * 60 * 1000, // 5 minutes default\n  bypassCache: boolean = false\n): Promise<T> {\n  const cacheKey = generateCacheKey(endpoint, options.params);\n  const cachedData = apiCache.get(cacheKey);\n  \n  // Return cached data if valid and not bypassing cache\n  if (!bypassCache && isCacheValid(cachedData)) {\n    return cachedData.data;\n  }\n  \n  // Fetch fresh data\n  const data = await enhancedFetchFromApi<T>(endpoint, options, typeof window === 'undefined');\n  \n  // Cache the response\n  apiCache.set(cacheKey, {\n    data,\n    timestamp: Date.now(),\n    ttl,\n  });\n  \n  return data;\n}\n\n/**\n * Clear a specific cache entry\n */\nexport function clearCacheEntry(endpoint: string, params?: any): void {\n  const cacheKey = generateCacheKey(endpoint, params);\n  apiCache.delete(cacheKey);\n}\n\n/**\n * Clear all cache entries\n */\nexport function clearCache(): void {\n  apiCache.clear();\n}\n\n/**\n * Prefetch data and store in cache\n * Useful for preloading data that will be needed soon\n */\nexport async function prefetchData<T>(\n  endpoint: string,\n  options: AxiosRequestConfig = {},\n  ttl: number = 5 * 60 * 1000\n): Promise<void> {\n  try {\n    await fetchWithCache<T>(endpoint, options, ttl, true);\n  } catch (error) {\n    console.error(`Error prefetching data from ${endpoint}:`, error);\n    // Silently fail on prefetch errors\n  }\n}\n\n/**\n * Batch multiple API requests into a single request\n * This reduces the number of network requests\n * \n * @param requests Array of request objects with endpoint and options\n * @returns Promise with an array of responses\n */\nexport async function batchRequests<T>(\n  requests: Array<{ endpoint: string; options?: AxiosRequestConfig }>\n): Promise<T[]> {\n  return Promise.all(\n    requests.map(({ endpoint, options = {} }) => \n      enhancedFetchFromApi<T>(endpoint, options, typeof window === 'undefined')\n    )\n  );\n}\n\n/**\n * Create optimized params for pagination\n * This ensures we only request the data we need\n */\nexport function createPaginationParams(\n  page: number = 1,\n  pageSize: number = 10,\n  fields: string[] = []\n): Record<string, any> {\n  const params: Record<string, any> = {\n    pagination: {\n      page,\n      pageSize,\n    },\n  };\n  \n  // Add fields to select if provided\n  if (fields.length > 0) {\n    params.fields = fields;\n  }\n  \n  return params;\n}\n\n/**\n * Create optimized params for filtering\n */\nexport function createFilterParams(\n  filters: Record<string, any> = {},\n  sort: string | string[] = [],\n  fields: string[] = []\n): Record<string, any> {\n  const params: Record<string, any> = {};\n  \n  // Add filters if provided\n  if (Object.keys(filters).length > 0) {\n    params.filters = filters;\n  }\n  \n  // Add sort if provided\n  if (sort.length > 0) {\n    params.sort = Array.isArray(sort) ? sort : [sort];\n  }\n  \n  // Add fields to select if provided\n  if (fields.length > 0) {\n    params.fields = fields;\n  }\n  \n  return params;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;;AAaA,MAAM,WAAW,IAAI;AAKd,SAAS,iBAAiB,QAAgB,EAAE,MAAY;IAC7D,OAAO,GAAG,SAAS,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU,CAAC,IAAI;AACtD;AAKO,SAAS,aAAgB,UAAqC;IACnE,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,aAAa,WAAW,SAAS,GAAG,WAAW,GAAG;IAExD,OAAO,MAAM;AACf;AAWO,eAAe,eACpB,QAAgB,EAChB,UAA8B,CAAC,CAAC,EAChC,MAAc,IAAI,KAAK,IAAI,EAC3B,cAAuB,KAAK;IAE5B,MAAM,WAAW,iBAAiB,UAAU,QAAQ,MAAM;IAC1D,MAAM,aAAa,SAAS,GAAG,CAAC;IAEhC,sDAAsD;IACtD,IAAI,CAAC,eAAe,aAAa,aAAa;QAC5C,OAAO,WAAW,IAAI;IACxB;IAEA,mBAAmB;IACnB,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAK,UAAU,SAAS,gBAAkB;IAEhF,qBAAqB;IACrB,SAAS,GAAG,CAAC,UAAU;QACrB;QACA,WAAW,KAAK,GAAG;QACnB;IACF;IAEA,OAAO;AACT;AAKO,SAAS,gBAAgB,QAAgB,EAAE,MAAY;IAC5D,MAAM,WAAW,iBAAiB,UAAU;IAC5C,SAAS,MAAM,CAAC;AAClB;AAKO,SAAS;IACd,SAAS,KAAK;AAChB;AAMO,eAAe,aACpB,QAAgB,EAChB,UAA8B,CAAC,CAAC,EAChC,MAAc,IAAI,KAAK,IAAI;IAE3B,IAAI;QACF,MAAM,eAAkB,UAAU,SAAS,KAAK;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC,EAAE;IAC1D,mCAAmC;IACrC;AACF;AASO,eAAe,cACpB,QAAmE;IAEnE,OAAO,QAAQ,GAAG,CAChB,SAAS,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,GACtC,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAK,UAAU,SAAS,gBAAkB;AAGnE;AAMO,SAAS,uBACd,OAAe,CAAC,EAChB,WAAmB,EAAE,EACrB,SAAmB,EAAE;IAErB,MAAM,SAA8B;QAClC,YAAY;YACV;YACA;QACF;IACF;IAEA,mCAAmC;IACnC,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,OAAO,MAAM,GAAG;IAClB;IAEA,OAAO;AACT;AAKO,SAAS,mBACd,UAA+B,CAAC,CAAC,EACjC,OAA0B,EAAE,EAC5B,SAAmB,EAAE;IAErB,MAAM,SAA8B,CAAC;IAErC,0BAA0B;IAC1B,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;QACnC,OAAO,OAAO,GAAG;IACnB;IAEA,uBAAuB;IACvB,IAAI,KAAK,MAAM,GAAG,GAAG;QACnB,OAAO,IAAI,GAAG,MAAM,OAAO,CAAC,QAAQ,OAAO;YAAC;SAAK;IACnD;IAEA,mCAAmC;IACnC,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,OAAO,MAAM,GAAG;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/preload.ts"], "sourcesContent": ["'use client';\n\nimport { AxiosRequestConfig } from 'axios';\nimport { enhancedFetchFromApi } from './enhancedApiUtils';\nimport { prefetchData } from './apiOptimization';\nimport { QueryClient } from '@tanstack/react-query';\n\n/**\n * Preload API data using React Query\n * \n * @param queryClient QueryClient instance\n * @param endpoint API endpoint to preload\n * @param params Request parameters\n * @param queryKey Optional custom query key (defaults to [endpoint, params])\n */\nexport function preloadApiData<T = any>(\n  queryClient: QueryClient,\n  endpoint: string,\n  params?: AxiosRequestConfig['params'],\n  queryKey?: unknown[]\n) {\n  // Use provided queryKey or default to [endpoint, params]\n  const key = queryKey || [endpoint, params];\n  \n  // Prefetch the data and store in React Query cache\n  return queryClient.prefetchQuery({\n    queryKey: key,\n    queryFn: async () => {\n      const options: AxiosRequestConfig = {};\n      if (params) {\n        options.params = params;\n      }\n      \n      return await enhancedFetch<PERSON>rom<PERSON>pi<T>(endpoint, options, false);\n    },\n    // Cache for 5 minutes\n    staleTime: 5 * 60 * 1000,\n  });\n}\n\n/**\n * Preload API data using the existing cache mechanism\n * \n * @param endpoint API endpoint to preload\n * @param params Request parameters\n * @param ttl Cache TTL in milliseconds (default: 5 minutes)\n */\nexport function preloadApiDataWithCache<T = any>(\n  endpoint: string,\n  params?: AxiosRequestConfig['params'],\n  ttl: number = 5 * 60 * 1000\n) {\n  const options: AxiosRequestConfig = {};\n  if (params) {\n    options.params = params;\n  }\n  \n  return prefetchData<T>(endpoint, options, ttl);\n}\n\n/**\n * Preload an image\n * \n * @param src Image URL to preload\n * @returns Promise that resolves when the image is loaded\n */\nexport function preloadImage(src: string): Promise<void> {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onload = () => resolve();\n    img.onerror = reject;\n    img.src = src;\n  });\n}\n\n/**\n * Preload a page by prefetching its resources\n * \n * @param path Page path to preload\n */\nexport function preloadPage(path: string): void {\n  // Create a link element for prefetching\n  const link = document.createElement('link');\n  link.rel = 'prefetch';\n  link.href = path;\n  link.as = 'document';\n  \n  // Add to document head\n  document.head.appendChild(link);\n}\n\n/**\n * Preload a font\n * \n * @param fontUrl Font URL to preload\n * @param fontFormat Font format (e.g., 'woff2', 'woff', 'truetype')\n */\nexport function preloadFont(fontUrl: string, fontFormat: string = 'woff2'): void {\n  const link = document.createElement('link');\n  link.rel = 'preload';\n  link.href = fontUrl;\n  link.as = 'font';\n  link.type = `font/${fontFormat}`;\n  link.crossOrigin = 'anonymous';\n  \n  document.head.appendChild(link);\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAJA;;;AAeO,SAAS,eACd,WAAwB,EACxB,QAAgB,EAChB,MAAqC,EACrC,QAAoB;IAEpB,yDAAyD;IACzD,MAAM,MAAM,YAAY;QAAC;QAAU;KAAO;IAE1C,mDAAmD;IACnD,OAAO,YAAY,aAAa,CAAC;QAC/B,UAAU;QACV,SAAS;YACP,MAAM,UAA8B,CAAC;YACrC,IAAI,QAAQ;gBACV,QAAQ,MAAM,GAAG;YACnB;YAEA,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAK,UAAU,SAAS;QAC1D;QACA,sBAAsB;QACtB,WAAW,IAAI,KAAK;IACtB;AACF;AASO,SAAS,wBACd,QAAgB,EAChB,MAAqC,EACrC,MAAc,IAAI,KAAK,IAAI;IAE3B,MAAM,UAA8B,CAAC;IACrC,IAAI,QAAQ;QACV,QAAQ,MAAM,GAAG;IACnB;IAEA,OAAO,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAK,UAAU,SAAS;AAC5C;AAQO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG,IAAM;QACnB,IAAI,OAAO,GAAG;QACd,IAAI,GAAG,GAAG;IACZ;AACF;AAOO,SAAS,YAAY,IAAY;IACtC,wCAAwC;IACxC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,GAAG,GAAG;IACX,KAAK,IAAI,GAAG;IACZ,KAAK,EAAE,GAAG;IAEV,uBAAuB;IACvB,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B;AAQO,SAAS,YAAY,OAAe,EAAE,aAAqB,OAAO;IACvE,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,GAAG,GAAG;IACX,KAAK,IAAI,GAAG;IACZ,KAAK,EAAE,GAAG;IACV,KAAK,IAAI,GAAG,CAAC,KAAK,EAAE,YAAY;IAChC,KAAK,WAAW,GAAG;IAEnB,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/LinkWithPreload.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useState } from 'react';\nimport Link from 'next/link';\nimport { useQueryClient } from '@tanstack/react-query';\nimport { preloadApiData, preloadPage } from '@/lib/preload';\nimport { useRouter } from 'next/navigation';\n\ninterface LinkWithPreloadProps {\n  href: string;\n  children: ReactNode;\n  className?: string;\n  prefetchApiEndpoint?: string;\n  prefetchApiParams?: Record<string, any>;\n  prefetchQueryKey?: unknown[];\n  prefetchOnHover?: boolean;\n  prefetchOnMount?: boolean;\n  onClick?: () => void;\n  ariaLabel?: string;\n  title?: string;\n}\n\n/**\n * Enhanced Link component with data preloading capabilities\n * \n * Extends Next.js Link with the ability to preload API data when hovering\n * or mounting the link, improving perceived performance for the user.\n */\nexport default function LinkWithPreload({\n  href,\n  children,\n  className,\n  prefetchApiEndpoint,\n  prefetchApiParams,\n  prefetchQueryKey,\n  prefetchOnHover = true,\n  prefetchOnMount = false,\n  onClick,\n  ariaLabel,\n  title,\n}: LinkWithPreloadProps) {\n  const queryClient = useQueryClient();\n  const router = useRouter();\n  const [hasPrefetched, setHasPrefetched] = useState(false);\n  \n  // Function to handle preloading\n  const handlePreload = async () => {\n    if (hasPrefetched) return;\n    \n    try {\n      // Preload the page\n      preloadPage(href);\n      \n      // Preload API data if endpoint is provided\n      if (prefetchApiEndpoint) {\n        await preloadApiData(\n          queryClient,\n          prefetchApiEndpoint,\n          prefetchApiParams,\n          prefetchQueryKey\n        );\n      }\n      \n      setHasPrefetched(true);\n    } catch (error) {\n      console.error('Error preloading data:', error);\n      // Don't throw - preloading errors shouldn't affect the user experience\n    }\n  };\n  \n  // Preload on mount if enabled\n  if (prefetchOnMount && !hasPrefetched) {\n    handlePreload();\n  }\n  \n  return (\n    <Link\n      href={href}\n      className={className}\n      aria-label={ariaLabel}\n      title={title}\n      onClick={(e) => {\n        if (onClick) onClick();\n      }}\n      onMouseEnter={() => {\n        if (prefetchOnHover) {\n          handlePreload();\n        }\n      }}\n      onTouchStart={() => {\n        if (prefetchOnHover) {\n          handlePreload();\n        }\n      }}\n      prefetch={false} // We're handling prefetching manually\n    >\n      {children}\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AA4Be,SAAS,gBAAgB,EACtC,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,mBAAmB,EACnB,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,IAAI,EACtB,kBAAkB,KAAK,EACvB,OAAO,EACP,SAAS,EACT,KAAK,EACgB;IACrB,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,gCAAgC;IAChC,MAAM,gBAAgB;QACpB,IAAI,eAAe;QAEnB,IAAI;YACF,mBAAmB;YACnB,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE;YAEZ,2CAA2C;YAC3C,IAAI,qBAAqB;gBACvB,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EACjB,aACA,qBACA,mBACA;YAEJ;YAEA,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,uEAAuE;QACzE;IACF;IAEA,8BAA8B;IAC9B,IAAI,mBAAmB,CAAC,eAAe;QACrC;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW;QACX,cAAY;QACZ,OAAO;QACP,SAAS,CAAC;YACR,IAAI,SAAS;QACf;QACA,cAAc;YACZ,IAAI,iBAAiB;gBACnB;YACF;QACF;QACA,cAAc;YACZ,IAAI,iBAAiB;gBACnB;YACF;QACF;QACA,UAAU;kBAET;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/logger.ts"], "sourcesContent": ["/**\r\n * Utility for consistent logging throughout the application\r\n * Only logs in development mode by default\r\n */\r\n\r\n/**\r\n * Log levels\r\n */\r\nexport enum LogLevel {\r\n  DEBUG = 'debug',\r\n  INFO = 'info',\r\n  WARN = 'warn',\r\n  ERROR = 'error'\r\n}\r\n\r\n/**\r\n * Configuration for the logger\r\n */\r\ninterface LoggerConfig {\r\n  enabled: boolean;\r\n  level: LogLevel;\r\n  prefix?: string;\r\n}\r\n\r\n/**\r\n * Default configuration\r\n * Only enabled in development mode\r\n * Uses INFO level by default to reduce noise\r\n */\r\nconst defaultConfig: LoggerConfig = {\r\n  enabled: process.env.NODE_ENV === 'development',\r\n  level: LogLevel.INFO, // Changed from DEBUG to INFO to reduce logging noise\r\n  prefix: '[NHN]'\r\n};\r\n\r\n/**\r\n * Current configuration\r\n */\r\nlet config: LoggerConfig = { ...defaultConfig };\r\n\r\n/**\r\n * Configure the logger\r\n * @param newConfig - New configuration to merge with current config\r\n */\r\nexport function configureLogger(newConfig: Partial<LoggerConfig>): void {\r\n  config = { ...config, ...newConfig };\r\n}\r\n\r\n/**\r\n * Log a message at the specified level\r\n * @param level - Log level\r\n * @param message - Message to log\r\n * @param args - Additional arguments to log\r\n */\r\nfunction log(level: LogLevel, message: string, ...args: any[]): void {\r\n  if (!config.enabled) return;\r\n\r\n  const logLevels = Object.values(LogLevel);\r\n  const configLevelIndex = logLevels.indexOf(config.level);\r\n  const messageLevelIndex = logLevels.indexOf(level);\r\n\r\n  // Only log if the message level is >= the configured level\r\n  if (messageLevelIndex >= configLevelIndex) {\r\n    const prefix = config.prefix ? `${config.prefix} ` : '';\r\n    const formattedMessage = `${prefix}${message}`;\r\n\r\n    switch (level) {\r\n      case LogLevel.DEBUG:\r\n        console.debug(formattedMessage, ...args);\r\n        break;\r\n      case LogLevel.INFO:\r\n        console.info(formattedMessage, ...args);\r\n        break;\r\n      case LogLevel.WARN:\r\n        console.warn(formattedMessage, ...args);\r\n        break;\r\n      case LogLevel.ERROR:\r\n        console.error(formattedMessage, ...args);\r\n        break;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Log a debug message\r\n * @param message - Message to log\r\n * @param args - Additional arguments to log\r\n */\r\nexport function debug(message: string, ...args: any[]): void {\r\n  log(LogLevel.DEBUG, message, ...args);\r\n}\r\n\r\n/**\r\n * Log an info message\r\n * @param message - Message to log\r\n * @param args - Additional arguments to log\r\n */\r\nexport function info(message: string, ...args: any[]): void {\r\n  log(LogLevel.INFO, message, ...args);\r\n}\r\n\r\n/**\r\n * Log a warning message\r\n * @param message - Message to log\r\n * @param args - Additional arguments to log\r\n */\r\nexport function warn(message: string, ...args: any[]): void {\r\n  log(LogLevel.WARN, message, ...args);\r\n}\r\n\r\n/**\r\n * Log an error message\r\n * @param message - Message to log\r\n * @param args - Additional arguments to log\r\n */\r\nexport function error(message: string, ...args: any[]): void {\r\n  log(LogLevel.ERROR, message, ...args);\r\n}\r\n\r\n/**\r\n * Default logger object\r\n */\r\nexport default {\r\n  debug,\r\n  info,\r\n  warn,\r\n  error,\r\n  configure: configureLogger\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;CAEC;;;;;;;;;AACM,IAAA,AAAK,kCAAA;;;;;WAAA;;AAgBZ;;;;CAIC,GACD,MAAM,gBAA8B;IAClC,SAAS,oDAAyB;IAClC,KAAK;IACL,QAAQ;AACV;AAEA;;CAEC,GACD,IAAI,SAAuB;IAAE,GAAG,aAAa;AAAC;AAMvC,SAAS,gBAAgB,SAAgC;IAC9D,SAAS;QAAE,GAAG,MAAM;QAAE,GAAG,SAAS;IAAC;AACrC;AAEA;;;;;CAKC,GACD,SAAS,IAAI,KAAe,EAAE,OAAe,EAAE,GAAG,IAAW;IAC3D,IAAI,CAAC,OAAO,OAAO,EAAE;IAErB,MAAM,YAAY,OAAO,MAAM,CAAC;IAChC,MAAM,mBAAmB,UAAU,OAAO,CAAC,OAAO,KAAK;IACvD,MAAM,oBAAoB,UAAU,OAAO,CAAC;IAE5C,2DAA2D;IAC3D,IAAI,qBAAqB,kBAAkB;QACzC,MAAM,SAAS,OAAO,MAAM,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG;QACrD,MAAM,mBAAmB,GAAG,SAAS,SAAS;QAE9C,OAAQ;YACN;gBACE,QAAQ,KAAK,CAAC,qBAAqB;gBACnC;YACF;gBACE,QAAQ,IAAI,CAAC,qBAAqB;gBAClC;YACF;gBACE,QAAQ,IAAI,CAAC,qBAAqB;gBAClC;YACF;gBACE,QAAQ,KAAK,CAAC,qBAAqB;gBACnC;QACJ;IACF;AACF;AAOO,SAAS,MAAM,OAAe,EAAE,GAAG,IAAW;IACnD,aAAoB,YAAY;AAClC;AAOO,SAAS,KAAK,OAAe,EAAE,GAAG,IAAW;IAClD,YAAmB,YAAY;AACjC;AAOO,SAAS,KAAK,OAAe,EAAE,GAAG,IAAW;IAClD,YAAmB,YAAY;AACjC;AAOO,SAAS,MAAM,OAAe,EAAE,GAAG,IAAW;IACnD,aAAoB,YAAY;AAClC;uCAKe;IACb;IACA;IACA;IACA;IACA,WAAW;AACb", "debugId": null}}, {"offset": {"line": 1631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/mediaUtils.ts"], "sourcesContent": ["/**\r\n * Optimized utility functions for handling media URLs and assets\r\n * Simplified for better performance and reliability\r\n */\r\nimport logger from './logger';\r\n\r\n// Environment variables for media handling\r\nconst ENV = {\r\n  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n  NEXT_PUBLIC_STRAPI_API_URL: process.env.NEXT_PUBLIC_STRAPI_API_URL,\r\n  NEXT_PUBLIC_STRAPI_MEDIA_URL: process.env.NEXT_PUBLIC_STRAPI_MEDIA_URL,\r\n  NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,\r\n  IMAGE_HOSTNAME: process.env.IMAGE_HOSTNAME,\r\n  NODE_ENV: process.env.NODE_ENV\r\n};\r\n\r\n// Resolve Strapi API URL with fallback\r\nconst EFFECTIVE_STRAPI_URL = ENV.NEXT_PUBLIC_STRAPI_API_URL || \r\n                             ENV.NEXT_PUBLIC_API_URL || \r\n                             (ENV.NODE_ENV === 'development' ? 'http://localhost:1337' : 'https://nice-badge-2130241d6c.strapiapp.com');\r\n\r\n// Determine media URL with smarter defaults\r\nconst STRAPI_MEDIA_URL = (() => {\r\n  // Use explicit media URL if provided\r\n  if (ENV.NEXT_PUBLIC_STRAPI_MEDIA_URL) {\r\n    return ensureHttps(removeTrailingSlash(ENV.NEXT_PUBLIC_STRAPI_MEDIA_URL));\r\n  }\r\n  \r\n  // Use IMAGE_HOSTNAME if provided\r\n  if (ENV.IMAGE_HOSTNAME) {\r\n    return ensureHttps(removeTrailingSlash(ENV.IMAGE_HOSTNAME));\r\n  }\r\n  \r\n  // Derive from API URL\r\n  try {\r\n    const apiUrl = new URL(EFFECTIVE_STRAPI_URL);\r\n    // For Strapi Cloud, generate media URL\r\n    if (apiUrl.hostname.endsWith('strapiapp.com')) {\r\n      return `${ensureHttps(apiUrl.protocol)}//${apiUrl.hostname.replace('strapiapp.com', 'media.strapiapp.com')}`;\r\n    }\r\n    // For other URLs, use as-is\r\n    return ensureHttps(removeTrailingSlash(EFFECTIVE_STRAPI_URL));\r\n  } catch (e) {\r\n    // Fallback for invalid URLs\r\n    return ENV.NODE_ENV === 'development' \r\n      ? 'http://localhost:1337' \r\n      : 'https://nice-badge-2130241d6c.media.strapiapp.com';\r\n  }\r\n})();\r\n\r\n// Site URL for SEO and Open Graph\r\nconst SITE_URL = ENV.NEXT_PUBLIC_SITE_URL ||\r\n  (ENV.NEXT_PUBLIC_API_URL && ENV.NEXT_PUBLIC_API_URL.includes('strapiapp.com')\r\n    ? ENV.NEXT_PUBLIC_API_URL.replace('.strapiapp.com', '.vercel.app')\r\n    : 'https://naturalhealingnow.vercel.app');\r\n\r\n// Helper functions\r\nfunction ensureHttps(url: string): string {\r\n  if (!url) return url;\r\n  return url.replace(/^http:/, 'https:');\r\n}\r\n\r\nfunction removeTrailingSlash(url: string): string {\r\n  if (!url) return url;\r\n  return url.endsWith('/') ? url.slice(0, -1) : url;\r\n}\r\n\r\n// Log configuration in development\r\nif (ENV.NODE_ENV === 'development') {\r\n  logger.debug('Media Utils Initialized:', {\r\n    EFFECTIVE_STRAPI_URL,\r\n    STRAPI_MEDIA_URL,\r\n    SITE_URL\r\n  });\r\n}\r\n\r\n/**\r\n * Gets the correct Strapi media URL for an image path.\r\n * Ensures that relative paths are correctly prepended with the Strapi media URL.\r\n * Attempts to fix malformed full URLs.\r\n *\r\n * @param imagePath - The image path or filename, or a full URL.\r\n * @returns The properly formatted media URL.\r\n */\r\nexport function getStrapiMediaPath(imagePath: string): string {\r\n  if (!imagePath) return '';\r\n\r\n  // If it's already a full URL, try to sanitize it, especially for concatenated domains\r\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\r\n    return sanitizeUrl(imagePath); // sanitizeUrl will handle malformed full URLs\r\n  }\r\n\r\n  // If it's a relative path, prepend the STRAPI_MEDIA_URL\r\n  // This ensures we're using the correct media domain\r\n  if (STRAPI_MEDIA_URL) {\r\n    return `${STRAPI_MEDIA_URL}/${imagePath.startsWith('/') ? imagePath.substring(1) : imagePath}`;\r\n  }\r\n  // Fallback if STRAPI_MEDIA_URL is somehow not set (should be rare with new logic)\r\n  logger.warn('STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaPath', { imagePath });\r\n  return `${EFFECTIVE_STRAPI_URL}/${imagePath.startsWith('/') ? imagePath.substring(1) : imagePath}`;\r\n}\r\n\r\n\r\n/**\r\n * Creates an absolute URL for Strapi media assets.\r\n * Handles different Strapi response formats and sanitizes the URL.\r\n *\r\n * @param mediaInput - The media object or URL string from Strapi.\r\n * @param options - Optional configuration.\r\n * @returns The absolute URL or null if no URL could be extracted.\r\n */\r\nexport function getStrapiMediaUrl(\r\n  mediaInput: any,\r\n  options: { debug?: boolean } = { debug: false }\r\n): string | null {\r\n  if (options.debug) {\r\n    logger.debug(\"getStrapiMediaUrl input:\", {\r\n      type: typeof mediaInput,\r\n      isNull: mediaInput === null,\r\n      isUndefined: mediaInput === undefined,\r\n      value: mediaInput\r\n    });\r\n  }\r\n\r\n  if (!mediaInput) return null;\r\n\r\n  let urlToProcess: string | null = null;\r\n\r\n  if (typeof mediaInput === 'string') {\r\n    urlToProcess = mediaInput;\r\n  } else if (typeof mediaInput === 'object') {\r\n    urlToProcess =\r\n      mediaInput.url ||\r\n      mediaInput.data?.attributes?.url ||\r\n      mediaInput.data?.url ||\r\n      null;\r\n  }\r\n\r\n  if (!urlToProcess) {\r\n    if (options.debug || process.env.NODE_ENV === 'production') {\r\n      logger.warn(\"Could not extract initial URL from mediaInput in getStrapiMediaUrl\", { mediaInput });\r\n    }\r\n    return null;\r\n  }\r\n\r\n  const sanitizedUrl = sanitizeUrl(urlToProcess);\r\n\r\n  if (!sanitizedUrl) {\r\n     if (options.debug || process.env.NODE_ENV === 'production') {\r\n      logger.warn(\"URL became empty after sanitization in getStrapiMediaUrl\", { originalUrl: urlToProcess });\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // If sanitizeUrl returned an absolute URL, use it\r\n  if (sanitizedUrl.startsWith('http://') || sanitizedUrl.startsWith('https://')) {\r\n    return sanitizedUrl;\r\n  }\r\n\r\n  // If it's a relative path after sanitization, prepend the STRAPI_MEDIA_URL\r\n  if (STRAPI_MEDIA_URL) {\r\n    return `${STRAPI_MEDIA_URL}${sanitizedUrl.startsWith('/') ? '' : '/'}${sanitizedUrl}`;\r\n  }\r\n  \r\n  // Fallback: if STRAPI_MEDIA_URL is not available, use EFFECTIVE_STRAPI_URL (less ideal for media)\r\n  logger.warn('STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl', { sanitizedUrl });\r\n  return `${EFFECTIVE_STRAPI_URL}${sanitizedUrl.startsWith('/') ? '' : '/'}${sanitizedUrl}`;\r\n}\r\n\r\n/**\r\n * Gets the profile picture URL for an author\r\n *\r\n * @param authorData - The author data from Strapi\r\n * @returns The absolute URL or null if no URL could be extracted\r\n */\r\nexport function getProfilePictureUrl(authorData: any): string | null {\r\n  if (!authorData || !authorData.profilePicture) return null;\r\n\r\n  const profilePic = authorData.profilePicture;\r\n  \r\n  // Try to get URL from various possible structures\r\n  const url = profilePic.url || \r\n              profilePic.data?.attributes?.url || \r\n              profilePic.data?.url ||\r\n              profilePic.formats?.thumbnail?.url;\r\n\r\n  return url ? getStrapiMediaUrl(url) : getStrapiMediaUrl(profilePic); // Fallback to passing the whole object\r\n}\r\n\r\n/**\r\n * Gets the featured image URL for a post\r\n *\r\n * @param postData - The post data from Strapi\r\n * @returns The absolute URL or null if no URL could be extracted\r\n */\r\nexport function getFeaturedImageUrl(postData: any): string | null {\r\n  if (!postData || !postData.featuredImage) return null;\r\n\r\n  const featuredImg = postData.featuredImage;\r\n\r\n  const url = featuredImg.url ||\r\n              featuredImg.data?.attributes?.url ||\r\n              featuredImg.data?.url;\r\n\r\n  return url ? getStrapiMediaUrl(url) : getStrapiMediaUrl(featuredImg); // Fallback\r\n}\r\n\r\n/**\r\n * Sanitizes a URL by attempting to fix common issues like concatenated domains\r\n * and ensure it uses HTTPS.\r\n *\r\n * @param urlInput - The URL to sanitize (string or object with a 'url' property).\r\n * @returns The sanitized URL string, or an empty string if input is invalid or unfixable.\r\n */\r\nexport function sanitizeUrl(urlInput: any): string {\r\n  // Fast path for common case: already valid URL or relative path starting with /\r\n  if (typeof urlInput === 'string' && (urlInput.startsWith('https://') || urlInput.startsWith('http://') || urlInput.startsWith('/'))) {\r\n    // Ensure https for absolute URLs\r\n    if (urlInput.startsWith('http://')) {\r\n      return urlInput.replace(/^http:/, 'https:');\r\n    }\r\n    return urlInput;\r\n  }\r\n\r\n  if (!urlInput) return '';\r\n\r\n  let currentUrl: string;\r\n\r\n  if (typeof urlInput === 'object' && urlInput.url && typeof urlInput.url === 'string') {\r\n    currentUrl = urlInput.url;\r\n  } else if (typeof urlInput === 'string') {\r\n    currentUrl = urlInput;\r\n  } else {\r\n    logger.warn('Invalid input type for sanitizeUrl. Expected string or object with url property.', { inputType: typeof urlInput });\r\n    return '';\r\n  }\r\n\r\n  // Trim whitespace\r\n  currentUrl = currentUrl.trim();\r\n\r\n  // Remove 'undefined' prefix if present\r\n  if (currentUrl.toLowerCase().startsWith('undefined')) {\r\n    currentUrl = currentUrl.substring('undefined'.length);\r\n    logger.info('Removed \"undefined\" prefix from URL', { original: urlInput, new: currentUrl });\r\n  }\r\n  \r\n  // Attempt to fix the specific concatenation: base_strapi_url + media_strapi_url\r\n  // Example: https://nice-badge-2130241d6c.strapiapp.comhttps://nice-badge-2130241d6c.media.strapiapp.com/image.jpg\r\n  // Should become: https://nice-badge-2130241d6c.media.strapiapp.com/image.jpg\r\n  const strapiApiDomain = EFFECTIVE_STRAPI_URL.replace(/^https?:\\/\\//, '').split('/')[0]; // e.g., nice-badge-2130241d6c.strapiapp.com\r\n  const strapiMediaDomain = STRAPI_MEDIA_URL.replace(/^https?:\\/\\//, '').split('/')[0]; // e.g., nice-badge-2130241d6c.media.strapiapp.com\r\n\r\n  if (strapiApiDomain && strapiMediaDomain && currentUrl.includes(strapiApiDomain) && currentUrl.includes(strapiMediaDomain)) {\r\n    const regex = new RegExp(`(https?:\\/\\/)?(${strapiApiDomain})(\\/*)(https?:\\/\\/)?(${strapiMediaDomain})`, 'gi');\r\n    const replacement = `https://${strapiMediaDomain}`;\r\n    if (regex.test(currentUrl)) {\r\n      const originalForLog = currentUrl;\r\n      currentUrl = currentUrl.replace(regex, replacement);\r\n      logger.info('Fixed concatenated Strapi domains', { original: originalForLog, fixed: currentUrl, apiDomain: strapiApiDomain, mediaDomain: strapiMediaDomain });\r\n    }\r\n  }\r\n  \r\n  // Fix missing colon in https// -> https://\r\n  if (currentUrl.includes('https//')) {\r\n    const originalForLog = currentUrl;\r\n    currentUrl = currentUrl.replace(/https\\/\\//g, 'https://');\r\n    logger.info('Fixed missing colon in URL (https//)', { original: originalForLog, fixed: currentUrl });\r\n  }\r\n\r\n  // Ensure HTTPS for known media domains or if it's a full URL without protocol\r\n  if (currentUrl.startsWith('//')) {\r\n     currentUrl = `https:${currentUrl}`;\r\n  } else if ((currentUrl.includes('media.strapiapp.com') || currentUrl.includes(strapiMediaDomain)) && !currentUrl.startsWith('http')) {\r\n     currentUrl = `https://${currentUrl}`;\r\n  } else if (currentUrl.startsWith('localhost') || currentUrl.startsWith(strapiApiDomain.split('.')[0])) { // Heuristic for relative paths that look like hostnames\r\n     currentUrl = `https://${currentUrl}`; // Assume https if protocol missing for these\r\n  }\r\n\r\n\r\n  // If it's a relative path (starts with /), it's fine as is for now.\r\n  // getStrapiMediaUrl will prepend the correct base.\r\n  if (currentUrl.startsWith('/')) {\r\n    return currentUrl;\r\n  }\r\n\r\n  // If it's now a valid absolute URL, return it\r\n  if (currentUrl.startsWith('http://') || currentUrl.startsWith('https://')) {\r\n    try {\r\n      // Validate if it's a proper URL\r\n      new URL(currentUrl);\r\n      return currentUrl;\r\n    } catch (e) {\r\n      logger.error('URL parsing failed after sanitization attempts', { url: currentUrl, error: e });\r\n      // If parsing fails, it might be a relative path that doesn't start with /\r\n      // or completely malformed.\r\n      // If it looks like a path (no protocol, no domain), return it for further processing.\r\n      if (!currentUrl.includes('://') && !currentUrl.includes('.')) { // Simple check for path-like string\r\n          return currentUrl;\r\n      }\r\n      return ''; // Unfixable\r\n    }\r\n  }\r\n  \r\n  // If it's not an absolute URL and not a relative path starting with /,\r\n  // it might be a path without a leading slash.\r\n  // This case should be handled by the caller (getStrapiMediaUrl) by prepending the base URL.\r\n  // However, if STRAPI_MEDIA_URL is available, we can assume it's a media path.\r\n  if (STRAPI_MEDIA_URL && currentUrl && !currentUrl.includes('://')) {\r\n    logger.debug('Assuming relative media path, prepending STRAPI_MEDIA_URL', { path: currentUrl });\r\n    return `/${currentUrl}`; // Return as relative path for getStrapiMediaUrl to handle\r\n  }\r\n\r\n  logger.warn('sanitizeUrl could not produce a valid absolute or relative URL', { originalInput: urlInput, finalSanitized: currentUrl });\r\n  return currentUrl; // Return what we have, or empty if it was invalid from start\r\n}\r\n\r\n/**\r\n * Transforms a Strapi media URL to use the site domain for SEO purposes\r\n * This is especially useful for og:image URLs that should use your domain\r\n * instead of the Strapi media domain\r\n *\r\n * NOTE: For OG images, we now use the direct Strapi media URL instead of transforming\r\n * This function is kept for backward compatibility with other parts of the codebase\r\n *\r\n * @param url - The original Strapi media URL\r\n * @returns The transformed URL using the site domain or the original URL for media\r\n */\r\nexport function transformToSiteDomainUrl(url: string | null | undefined): string | null {\r\n  if (!url) return null;\r\n  const sanitizedUrl = sanitizeUrl(url);\r\n  if (!sanitizedUrl) return null;\r\n\r\n  // For media URLs from media.strapiapp.com, return them directly (ensuring https)\r\n  if (sanitizedUrl.includes('media.strapiapp.com')) {\r\n    const directMediaUrl = sanitizedUrl.startsWith('http') ? sanitizedUrl : `https://${sanitizedUrl}`;\r\n    logger.debug('Using direct Strapi media URL (transformToSiteDomainUrl)', { url: directMediaUrl });\r\n    return directMediaUrl.replace(/^http:/, 'https:');\r\n  }\r\n\r\n  // If it's not a Strapi URL (doesn't include strapiapp.com), return as is\r\n  if (!sanitizedUrl.includes('strapiapp.com')) {\r\n    return sanitizedUrl.replace(/^http:/, 'https:');\r\n  }\r\n\r\n  try {\r\n    const urlObj = new URL(sanitizedUrl.startsWith('http') ? sanitizedUrl : `https://${sanitizedUrl}`);\r\n    const path = urlObj.pathname;\r\n    const siteDomain = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;\r\n    const transformedUrl = `${siteDomain}${path}`;\r\n    logger.debug('Transformed URL for SEO (transformToSiteDomainUrl)', { original: sanitizedUrl, transformed: transformedUrl });\r\n    return transformedUrl.replace(/^http:/, 'https:');\r\n  } catch (error) {\r\n    logger.error('Error transforming URL in transformToSiteDomainUrl', { url: sanitizedUrl, error });\r\n    return sanitizedUrl.replace(/^http:/, 'https:'); // Fallback\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the correct OG image URL for social sharing.\r\n * Ensures it's an absolute URL, preferably from STRAPI_MEDIA_URL.\r\n *\r\n * @param url The image URL from Strapi or a relative path.\r\n * @returns The properly formatted URL for OG tags, or undefined.\r\n */\r\nexport function getOgImageUrl(url: string | null | undefined): string | undefined {\r\n  if (!url) return undefined;\r\n\r\n  let processedUrl = sanitizeUrl(url);\r\n  if (!processedUrl) return undefined;\r\n\r\n  // If it's already an absolute URL (likely from sanitizeUrl fixing it or it was already absolute)\r\n  if (processedUrl.startsWith('http://') || processedUrl.startsWith('https://')) {\r\n    return processedUrl.replace(/^http:/, 'https:');\r\n  }\r\n\r\n  // If it's a relative path (e.g., /uploads/image.jpg or image.jpg)\r\n  if (STRAPI_MEDIA_URL) {\r\n    const finalUrl = `${STRAPI_MEDIA_URL}${processedUrl.startsWith('/') ? '' : '/'}${processedUrl}`;\r\n    logger.debug('Constructed OG image URL from relative path', { original: url, final: finalUrl });\r\n    return finalUrl.replace(/^http:/, 'https:');\r\n  }\r\n  \r\n  logger.warn('Could not determine OG image URL confidently', { originalUrl: url, processedUrl });\r\n  // Fallback: if STRAPI_MEDIA_URL is not set, try with EFFECTIVE_STRAPI_URL (less ideal)\r\n  if (EFFECTIVE_STRAPI_URL) {\r\n    return `${EFFECTIVE_STRAPI_URL}${processedUrl.startsWith('/') ? '' : '/'}${processedUrl}`.replace(/^http:/, 'https:');\r\n  }\r\n  return undefined;\r\n}\r\n\r\nexport default {\r\n  getStrapiMediaUrl,\r\n  getProfilePictureUrl,\r\n  getFeaturedImageUrl,\r\n  sanitizeUrl,\r\n  transformToSiteDomainUrl,\r\n  getOgImageUrl,\r\n  SITE_URL // Export SITE_URL for use in other files\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AACD;;AAEA,2CAA2C;AAC3C,MAAM,MAAM;IACV,mBAAmB;IACnB,0BAA0B;IAC1B,4BAA4B;IAC5B,oBAAoB;IACpB,gBAAgB,QAAQ,GAAG,CAAC,cAAc;IAC1C,QAAQ;AACV;AAEA,uCAAuC;AACvC,MAAM,uBAAuB,IAAI,0BAA0B,IAC9B,IAAI,mBAAmB,IACvB,CAAC,IAAI,QAAQ,KAAK,gBAAgB,0BAA0B,6CAA6C;AAEtI,4CAA4C;AAC5C,MAAM,mBAAmB,CAAC;IACxB,qCAAqC;IACrC,IAAI,IAAI,4BAA4B,EAAE;QACpC,OAAO,YAAY,oBAAoB,IAAI,4BAA4B;IACzE;IAEA,iCAAiC;IACjC,IAAI,IAAI,cAAc,EAAE;QACtB,OAAO,YAAY,oBAAoB,IAAI,cAAc;IAC3D;IAEA,sBAAsB;IACtB,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,uCAAuC;QACvC,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,kBAAkB;YAC7C,OAAO,GAAG,YAAY,OAAO,QAAQ,EAAE,EAAE,EAAE,OAAO,QAAQ,CAAC,OAAO,CAAC,iBAAiB,wBAAwB;QAC9G;QACA,4BAA4B;QAC5B,OAAO,YAAY,oBAAoB;IACzC,EAAE,OAAO,GAAG;QACV,4BAA4B;QAC5B,OAAO,IAAI,QAAQ,KAAK,gBACpB,0BACA;IACN;AACF,CAAC;AAED,kCAAkC;AAClC,MAAM,WAAW,IAAI,oBAAoB,IACvC,CAAC,IAAI,mBAAmB,IAAI,IAAI,mBAAmB,CAAC,QAAQ,CAAC,mBACzD,IAAI,mBAAmB,CAAC,OAAO,CAAC,kBAAkB,iBAClD,sCAAsC;AAE5C,mBAAmB;AACnB,SAAS,YAAY,GAAW;IAC9B,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IAAI,OAAO,CAAC,UAAU;AAC/B;AAEA,SAAS,oBAAoB,GAAW;IACtC,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO,IAAI,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK;AAChD;AAEA,mCAAmC;AACnC,IAAI,IAAI,QAAQ,KAAK,eAAe;IAClC,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,4BAA4B;QACvC;QACA;QACA;IACF;AACF;AAUO,SAAS,mBAAmB,SAAiB;IAClD,IAAI,CAAC,WAAW,OAAO;IAEvB,sFAAsF;IACtF,IAAI,UAAU,UAAU,CAAC,cAAc,UAAU,UAAU,CAAC,aAAa;QACvE,OAAO,YAAY,YAAY,8CAA8C;IAC/E;IAEA,wDAAwD;IACxD,oDAAoD;IACpD,IAAI,kBAAkB;QACpB,OAAO,GAAG,iBAAiB,CAAC,EAAE,UAAU,UAAU,CAAC,OAAO,UAAU,SAAS,CAAC,KAAK,WAAW;IAChG;IACA,kFAAkF;IAClF,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,gGAAgG;QAAE;IAAU;IACxH,OAAO,GAAG,qBAAqB,CAAC,EAAE,UAAU,UAAU,CAAC,OAAO,UAAU,SAAS,CAAC,KAAK,WAAW;AACpG;AAWO,SAAS,kBACd,UAAe,EACf,UAA+B;IAAE,OAAO;AAAM,CAAC;IAE/C,IAAI,QAAQ,KAAK,EAAE;QACjB,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,4BAA4B;YACvC,MAAM,OAAO;YACb,QAAQ,eAAe;YACvB,aAAa,eAAe;YAC5B,OAAO;QACT;IACF;IAEA,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI,eAA8B;IAElC,IAAI,OAAO,eAAe,UAAU;QAClC,eAAe;IACjB,OAAO,IAAI,OAAO,eAAe,UAAU;QACzC,eACE,WAAW,GAAG,IACd,WAAW,IAAI,EAAE,YAAY,OAC7B,WAAW,IAAI,EAAE,OACjB;IACJ;IAEA,IAAI,CAAC,cAAc;QACjB,IAAI,QAAQ,KAAK,IAAI,oDAAyB,cAAc;YAC1D,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,sEAAsE;gBAAE;YAAW;QACjG;QACA,OAAO;IACT;IAEA,MAAM,eAAe,YAAY;IAEjC,IAAI,CAAC,cAAc;QAChB,IAAI,QAAQ,KAAK,IAAI,oDAAyB,cAAc;YAC3D,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,4DAA4D;gBAAE,aAAa;YAAa;QACtG;QACA,OAAO;IACT;IAEA,kDAAkD;IAClD,IAAI,aAAa,UAAU,CAAC,cAAc,aAAa,UAAU,CAAC,aAAa;QAC7E,OAAO;IACT;IAEA,2EAA2E;IAC3E,IAAI,kBAAkB;QACpB,OAAO,GAAG,mBAAmB,aAAa,UAAU,CAAC,OAAO,KAAK,MAAM,cAAc;IACvF;IAEA,kGAAkG;IAClG,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,+FAA+F;QAAE;IAAa;IAC1H,OAAO,GAAG,uBAAuB,aAAa,UAAU,CAAC,OAAO,KAAK,MAAM,cAAc;AAC3F;AAQO,SAAS,qBAAqB,UAAe;IAClD,IAAI,CAAC,cAAc,CAAC,WAAW,cAAc,EAAE,OAAO;IAEtD,MAAM,aAAa,WAAW,cAAc;IAE5C,kDAAkD;IAClD,MAAM,MAAM,WAAW,GAAG,IACd,WAAW,IAAI,EAAE,YAAY,OAC7B,WAAW,IAAI,EAAE,OACjB,WAAW,OAAO,EAAE,WAAW;IAE3C,OAAO,MAAM,kBAAkB,OAAO,kBAAkB,aAAa,uCAAuC;AAC9G;AAQO,SAAS,oBAAoB,QAAa;IAC/C,IAAI,CAAC,YAAY,CAAC,SAAS,aAAa,EAAE,OAAO;IAEjD,MAAM,cAAc,SAAS,aAAa;IAE1C,MAAM,MAAM,YAAY,GAAG,IACf,YAAY,IAAI,EAAE,YAAY,OAC9B,YAAY,IAAI,EAAE;IAE9B,OAAO,MAAM,kBAAkB,OAAO,kBAAkB,cAAc,WAAW;AACnF;AASO,SAAS,YAAY,QAAa;IACvC,gFAAgF;IAChF,IAAI,OAAO,aAAa,YAAY,CAAC,SAAS,UAAU,CAAC,eAAe,SAAS,UAAU,CAAC,cAAc,SAAS,UAAU,CAAC,IAAI,GAAG;QACnI,iCAAiC;QACjC,IAAI,SAAS,UAAU,CAAC,YAAY;YAClC,OAAO,SAAS,OAAO,CAAC,UAAU;QACpC;QACA,OAAO;IACT;IAEA,IAAI,CAAC,UAAU,OAAO;IAEtB,IAAI;IAEJ,IAAI,OAAO,aAAa,YAAY,SAAS,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK,UAAU;QACpF,aAAa,SAAS,GAAG;IAC3B,OAAO,IAAI,OAAO,aAAa,UAAU;QACvC,aAAa;IACf,OAAO;QACL,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,oFAAoF;YAAE,WAAW,OAAO;QAAS;QAC7H,OAAO;IACT;IAEA,kBAAkB;IAClB,aAAa,WAAW,IAAI;IAE5B,uCAAuC;IACvC,IAAI,WAAW,WAAW,GAAG,UAAU,CAAC,cAAc;QACpD,aAAa,WAAW,SAAS,CAAC,YAAY,MAAM;QACpD,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,uCAAuC;YAAE,UAAU;YAAU,KAAK;QAAW;IAC3F;IAEA,gFAAgF;IAChF,kHAAkH;IAClH,6EAA6E;IAC7E,MAAM,kBAAkB,qBAAqB,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,4CAA4C;IACpI,MAAM,oBAAoB,iBAAiB,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,kDAAkD;IAExI,IAAI,mBAAmB,qBAAqB,WAAW,QAAQ,CAAC,oBAAoB,WAAW,QAAQ,CAAC,oBAAoB;QAC1H,MAAM,QAAQ,IAAI,OAAO,CAAC,eAAe,EAAE,gBAAgB,qBAAqB,EAAE,kBAAkB,CAAC,CAAC,EAAE;QACxG,MAAM,cAAc,CAAC,QAAQ,EAAE,mBAAmB;QAClD,IAAI,MAAM,IAAI,CAAC,aAAa;YAC1B,MAAM,iBAAiB;YACvB,aAAa,WAAW,OAAO,CAAC,OAAO;YACvC,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,qCAAqC;gBAAE,UAAU;gBAAgB,OAAO;gBAAY,WAAW;gBAAiB,aAAa;YAAkB;QAC7J;IACF;IAEA,2CAA2C;IAC3C,IAAI,WAAW,QAAQ,CAAC,YAAY;QAClC,MAAM,iBAAiB;QACvB,aAAa,WAAW,OAAO,CAAC,cAAc;QAC9C,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,wCAAwC;YAAE,UAAU;YAAgB,OAAO;QAAW;IACpG;IAEA,8EAA8E;IAC9E,IAAI,WAAW,UAAU,CAAC,OAAO;QAC9B,aAAa,CAAC,MAAM,EAAE,YAAY;IACrC,OAAO,IAAI,CAAC,WAAW,QAAQ,CAAC,0BAA0B,WAAW,QAAQ,CAAC,kBAAkB,KAAK,CAAC,WAAW,UAAU,CAAC,SAAS;QAClI,aAAa,CAAC,QAAQ,EAAE,YAAY;IACvC,OAAO,IAAI,WAAW,UAAU,CAAC,gBAAgB,WAAW,UAAU,CAAC,gBAAgB,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QACpG,aAAa,CAAC,QAAQ,EAAE,YAAY,EAAE,6CAA6C;IACtF;IAGA,oEAAoE;IACpE,mDAAmD;IACnD,IAAI,WAAW,UAAU,CAAC,MAAM;QAC9B,OAAO;IACT;IAEA,8CAA8C;IAC9C,IAAI,WAAW,UAAU,CAAC,cAAc,WAAW,UAAU,CAAC,aAAa;QACzE,IAAI;YACF,gCAAgC;YAChC,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAO,GAAG;YACV,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,kDAAkD;gBAAE,KAAK;gBAAY,OAAO;YAAE;YAC3F,0EAA0E;YAC1E,2BAA2B;YAC3B,sFAAsF;YACtF,IAAI,CAAC,WAAW,QAAQ,CAAC,UAAU,CAAC,WAAW,QAAQ,CAAC,MAAM;gBAC1D,OAAO;YACX;YACA,OAAO,IAAI,YAAY;QACzB;IACF;IAEA,uEAAuE;IACvE,8CAA8C;IAC9C,4FAA4F;IAC5F,8EAA8E;IAC9E,IAAI,oBAAoB,cAAc,CAAC,WAAW,QAAQ,CAAC,QAAQ;QACjE,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,6DAA6D;YAAE,MAAM;QAAW;QAC7F,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE,0DAA0D;IACrF;IAEA,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,kEAAkE;QAAE,eAAe;QAAU,gBAAgB;IAAW;IACpI,OAAO,YAAY,6DAA6D;AAClF;AAaO,SAAS,yBAAyB,GAA8B;IACrE,IAAI,CAAC,KAAK,OAAO;IACjB,MAAM,eAAe,YAAY;IACjC,IAAI,CAAC,cAAc,OAAO;IAE1B,iFAAiF;IACjF,IAAI,aAAa,QAAQ,CAAC,wBAAwB;QAChD,MAAM,iBAAiB,aAAa,UAAU,CAAC,UAAU,eAAe,CAAC,QAAQ,EAAE,cAAc;QACjG,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,4DAA4D;YAAE,KAAK;QAAe;QAC/F,OAAO,eAAe,OAAO,CAAC,UAAU;IAC1C;IAEA,yEAAyE;IACzE,IAAI,CAAC,aAAa,QAAQ,CAAC,kBAAkB;QAC3C,OAAO,aAAa,OAAO,CAAC,UAAU;IACxC;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,IAAI,aAAa,UAAU,CAAC,UAAU,eAAe,CAAC,QAAQ,EAAE,cAAc;QACjG,MAAM,OAAO,OAAO,QAAQ;QAC5B,MAAM,aAAa,SAAS,QAAQ,CAAC,OAAO,SAAS,KAAK,CAAC,GAAG,CAAC,KAAK;QACpE,MAAM,iBAAiB,GAAG,aAAa,MAAM;QAC7C,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sDAAsD;YAAE,UAAU;YAAc,aAAa;QAAe;QACzH,OAAO,eAAe,OAAO,CAAC,UAAU;IAC1C,EAAE,OAAO,OAAO;QACd,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sDAAsD;YAAE,KAAK;YAAc;QAAM;QAC9F,OAAO,aAAa,OAAO,CAAC,UAAU,WAAW,WAAW;IAC9D;AACF;AASO,SAAS,cAAc,GAA8B;IAC1D,IAAI,CAAC,KAAK,OAAO;IAEjB,IAAI,eAAe,YAAY;IAC/B,IAAI,CAAC,cAAc,OAAO;IAE1B,iGAAiG;IACjG,IAAI,aAAa,UAAU,CAAC,cAAc,aAAa,UAAU,CAAC,aAAa;QAC7E,OAAO,aAAa,OAAO,CAAC,UAAU;IACxC;IAEA,kEAAkE;IAClE,IAAI,kBAAkB;QACpB,MAAM,WAAW,GAAG,mBAAmB,aAAa,UAAU,CAAC,OAAO,KAAK,MAAM,cAAc;QAC/F,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,+CAA+C;YAAE,UAAU;YAAK,OAAO;QAAS;QAC7F,OAAO,SAAS,OAAO,CAAC,UAAU;IACpC;IAEA,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,gDAAgD;QAAE,aAAa;QAAK;IAAa;IAC7F,uFAAuF;IACvF,IAAI,sBAAsB;QACxB,OAAO,GAAG,uBAAuB,aAAa,UAAU,CAAC,OAAO,KAAK,MAAM,cAAc,CAAC,OAAO,CAAC,UAAU;IAC9G;IACA,OAAO;AACT;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 1963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link';\r\nimport { useState } from 'react';\r\nimport { FiMenu, FiX, FiUser } from 'react-icons/fi';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport UserAccountDropdown from '@/components/auth/UserAccountDropdown';\r\nimport LazyImage from '@/components/shared/LazyImage'; // Import LazyImage component\r\nimport LinkWithPreload from '@/components/shared/LinkWithPreload'; // Import LinkWithPreload component\r\nimport { sanitizeUrl, getStrapiMediaPath } from '@/lib/mediaUtils';\r\n\r\n// Define the type for the actual logoLight prop received from Strapi's API response\r\n// (It's the media object itself, not nested under data/attributes when populated)\r\ninterface LogoLightMedia {\r\n  id: number;\r\n  name: string;\r\n  alternativeText?: string | null;\r\n  caption?: string | null;\r\n  width?: number;\r\n  height?: number;\r\n  formats?: any; // Can be more specific if needed\r\n  hash: string;\r\n  ext: string;\r\n  mime: string;\r\n  size: number;\r\n  url: string;\r\n  previewUrl?: string | null;\r\n  provider: string;\r\n  provider_metadata?: any;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  publishedAt?: string; // publishedAt might not always be present\r\n}\r\n\r\n// Define props for the Header component\r\ninterface HeaderProps {\r\n  siteName: string;\r\n  logoLight: LogoLightMedia | null; // Use the corrected type\r\n}\r\n\r\n// Accept props in the component function signature\r\nconst Header = ({ siteName, logoLight }: HeaderProps) => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const { user, isLoading } = useAuth();\r\n\r\n  // Prepare logo variables directly from the logoLight prop\r\n  const logoUrl = logoLight?.url;\r\n  const logoAlt = logoLight?.alternativeText || siteName; // Use siteName as fallback alt text\r\n  const logoWidth = logoLight?.width;\r\n  const logoHeight = logoLight?.height;\r\n\r\n  // Using the imported sanitizeUrl function from mediaUtils\r\n\r\n  // Get the Strapi API URL from environment variables\r\n  const strapiApiUrl = process.env.NEXT_PUBLIC_API_URL || '';\r\n\r\n  // Get the Strapi Media URL from environment variables (or derive it from API URL)\r\n  const strapiMediaUrl = process.env.IMAGE_HOSTNAME ||\r\n    (strapiApiUrl ? strapiApiUrl.replace('strapiapp.com', 'media.strapiapp.com') : '');\r\n\r\n  // For Strapi Cloud, construct the media URL correctly\r\n  let fullLogoUrl = '';\r\n\r\n  if (logoUrl) {\r\n    // Use the new getStrapiMediaPath function to properly handle the URL\r\n    // This function will handle all the edge cases and prevent malformed URLs\r\n    fullLogoUrl = getStrapiMediaPath(logoUrl);\r\n\r\n    // Log the URL in development for debugging\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Header Logo URL:', {\r\n        original: logoUrl,\r\n        processed: fullLogoUrl\r\n      });\r\n    }\r\n  }\r\n\r\n  // Log the URL in production for debugging\r\n  if (process.env.NODE_ENV === 'production') {\r\n    console.log('Logo URL:', {\r\n      original: logoUrl,\r\n      processed: fullLogoUrl\r\n    });\r\n  }\r\n\r\n  const toggleMenu = () => {\r\n    setIsMenuOpen(!isMenuOpen);\r\n  };\r\n\r\n  return (\r\n    <header className=\"bg-white shadow-sm\">\r\n      <div className=\"container mx-auto px-4 py-4\">\r\n        <div className=\"flex justify-between items-center\">\r\n          {/* Logo - Conditional Rendering */}\r\n          <div className=\"flex-shrink-0\">\r\n            <Link href=\"/\" className=\"inline-block align-middle\"> {/* Remove text styles, apply conditionally or to image/span */}\r\n              {logoUrl && logoWidth && logoHeight ? (\r\n                <LazyImage // Use LazyImage for optimization\r\n                  src={fullLogoUrl}\r\n                  alt={logoAlt}\r\n                  width={logoWidth} // Use actual width from Strapi\r\n                  height={logoHeight} // Use actual height from Strapi\r\n                  className=\"h-12 w-auto\" // Constrain height, width adjusts automatically\r\n                  aboveTheFold={true} // Logo is above the fold\r\n                  showPlaceholder={false} // No placeholder for logo\r\n                  priority // Prioritize loading the logo\r\n                  unoptimized={process.env.NODE_ENV === 'production'} // Use unoptimized in production to avoid issues\r\n                />\r\n              ) : logoUrl ? ( // Fallback if width/height missing, use standard img\r\n                 <img\r\n                  src={fullLogoUrl}\r\n                  alt={logoAlt}\r\n                  className=\"h-12 w-auto\" // Basic styling\r\n                />\r\n              ) : siteName ? (\r\n                <span className=\"text-2xl font-bold text-emerald-600\">{siteName}</span> // Apply text style here\r\n              ) : (\r\n                <span className=\"text-2xl font-bold text-emerald-600\">My Directory Site</span> // Fallback with style\r\n              )}\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Desktop Navigation */}\r\n          <div className=\"hidden md:flex items-center\">\r\n            <nav className=\"flex space-x-8 mr-8\">\r\n              <LinkWithPreload\r\n                href=\"/\"\r\n                className=\"text-gray-700 hover:text-emerald-600\"\r\n                prefetchOnHover={true}\r\n              >\r\n                Home\r\n              </LinkWithPreload>\r\n              <LinkWithPreload\r\n                href=\"/clinics\"\r\n                className=\"text-gray-700 hover:text-emerald-600\"\r\n                prefetchApiEndpoint=\"/clinics\"\r\n                prefetchApiParams={{\r\n                  sort: 'name:asc',\r\n                  pagination: { page: 1, pageSize: 12 },\r\n                  populate: '*'\r\n                }}\r\n                prefetchOnHover={true}\r\n              >\r\n                Find a Clinic\r\n              </LinkWithPreload>\r\n              <LinkWithPreload\r\n                href=\"/practitioners\"\r\n                className=\"text-gray-700 hover:text-emerald-600\"\r\n                prefetchApiEndpoint=\"/practitioners\"\r\n                prefetchApiParams={{\r\n                  sort: 'name:asc',\r\n                  pagination: { page: 1, pageSize: 12 },\r\n                  populate: '*'\r\n                }}\r\n                prefetchOnHover={true}\r\n              >\r\n                Find a Practitioner\r\n              </LinkWithPreload>\r\n              <LinkWithPreload\r\n                href=\"/categories\"\r\n                className=\"text-gray-700 hover:text-emerald-600\"\r\n                prefetchApiEndpoint=\"/categories\"\r\n                prefetchApiParams={{\r\n                  sort: 'name:asc',\r\n                  populate: '*'\r\n                }}\r\n                prefetchOnHover={true}\r\n              >\r\n                Categories\r\n              </LinkWithPreload>\r\n              <LinkWithPreload\r\n                href=\"/blog\"\r\n                className=\"text-gray-700 hover:text-emerald-600\"\r\n                prefetchApiEndpoint=\"/blog-posts\"\r\n                prefetchApiParams={{\r\n                  sort: 'publishDate:desc',\r\n                  pagination: { page: 1, pageSize: 10 },\r\n                  populate: {\r\n                    featuredImage: true,\r\n                    author_blogs: {\r\n                      populate: {\r\n                        profilePicture: true\r\n                      }\r\n                    }\r\n                  }\r\n                }}\r\n                prefetchOnHover={true}\r\n              >\r\n                Blog\r\n              </LinkWithPreload>\r\n            </nav>\r\n\r\n            {/* Authentication UI */}\r\n            {!isLoading && (\r\n              user ? (\r\n                <UserAccountDropdown />\r\n              ) : (\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <Link\r\n                    href=\"/signin\"\r\n                    className=\"text-gray-700 hover:text-emerald-600 font-medium\"\r\n                  >\r\n                    Sign In\r\n                  </Link>\r\n                  <Link\r\n                    href=\"/signup\"\r\n                    className=\"bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700\"\r\n                  >\r\n                    Sign Up\r\n                  </Link>\r\n                </div>\r\n              )\r\n            )}\r\n          </div>\r\n\r\n          {/* Mobile Menu Button */}\r\n          <div className=\"md:hidden\">\r\n            <button\r\n              onClick={toggleMenu}\r\n              className=\"text-gray-700 hover:text-emerald-600 focus:outline-none\"\r\n              aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}\r\n            >\r\n              {isMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile Navigation */}\r\n        {isMenuOpen && (\r\n          <nav className=\"md:hidden mt-4 space-y-4 pb-4\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"block text-gray-700 hover:text-emerald-600\"\r\n              onClick={() => setIsMenuOpen(false)}\r\n            >\r\n              Home\r\n            </Link>\r\n            <Link\r\n              href=\"/clinics\"\r\n              className=\"block text-gray-700 hover:text-emerald-600\"\r\n              onClick={() => setIsMenuOpen(false)}\r\n            >\r\n              Find a Clinic\r\n            </Link>\r\n            <Link\r\n              href=\"/practitioners\"\r\n              className=\"block text-gray-700 hover:text-emerald-600\"\r\n              onClick={() => setIsMenuOpen(false)}\r\n            >\r\n              Find a Practitioner\r\n            </Link>\r\n            <Link\r\n              href=\"/categories\"\r\n              className=\"block text-gray-700 hover:text-emerald-600\"\r\n              onClick={() => setIsMenuOpen(false)}\r\n            >\r\n              Categories\r\n            </Link>\r\n            <Link\r\n              href=\"/blog\"\r\n              className=\"block text-gray-700 hover:text-emerald-600\"\r\n              onClick={() => setIsMenuOpen(false)}\r\n            >\r\n              Blog\r\n            </Link>\r\n\r\n            {/* Mobile Authentication UI */}\r\n            {!isLoading && (\r\n              user ? (\r\n                <div className=\"border-t border-gray-200 pt-4 mt-4\">\r\n                  <Link\r\n                    href=\"/account\"\r\n                    className=\"flex items-center text-gray-700 hover:text-emerald-600\"\r\n                    onClick={() => setIsMenuOpen(false)}\r\n                  >\r\n                    <FiUser className=\"mr-2\" />\r\n                    My Account\r\n                  </Link>\r\n                  <button\r\n                    onClick={() => {\r\n                      setIsMenuOpen(false);\r\n                      // Sign out is handled within the dropdown, but here we close the menu\r\n                    }}\r\n                    className=\"mt-2 flex items-center text-gray-700 hover:text-emerald-600\"\r\n                  >\r\n                    <span className=\"text-red-600\">Sign Out</span>\r\n                  </button>\r\n                </div>\r\n              ) : (\r\n                <div className=\"border-t border-gray-200 pt-4 mt-4 flex flex-col space-y-2\">\r\n                  <Link\r\n                    href=\"/signin\"\r\n                    className=\"block text-gray-700 hover:text-emerald-600 font-medium\"\r\n                    onClick={() => setIsMenuOpen(false)}\r\n                  >\r\n                    Sign In\r\n                  </Link>\r\n                  <Link\r\n                    href=\"/signup\"\r\n                    className=\"block text-emerald-600 hover:text-emerald-700 font-medium\"\r\n                    onClick={() => setIsMenuOpen(false)}\r\n                  >\r\n                    Sign Up\r\n                  </Link>\r\n                </div>\r\n              )\r\n            )}\r\n          </nav>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA,iPAAuD,6BAA6B;AACpF,6PAAmE,mCAAmC;AACtG;AATA;;;;;;;;;;AAwCA,mDAAmD;AACnD,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAe;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAElC,0DAA0D;IAC1D,MAAM,UAAU,WAAW;IAC3B,MAAM,UAAU,WAAW,mBAAmB,UAAU,oCAAoC;IAC5F,MAAM,YAAY,WAAW;IAC7B,MAAM,aAAa,WAAW;IAE9B,0DAA0D;IAE1D,oDAAoD;IACpD,MAAM,eAAe,mFAAmC;IAExD,kFAAkF;IAClF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAC/C,CAAC,uCAAe,aAAa,OAAO,CAAC,iBAAiB,6DAA2B;IAEnF,sDAAsD;IACtD,IAAI,cAAc;IAElB,IAAI,SAAS;QACX,qEAAqE;QACrE,0EAA0E;QAC1E,cAAc,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD,EAAE;QAEjC,2CAA2C;QAC3C,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,oBAAoB;gBAC9B,UAAU;gBACV,WAAW;YACb;QACF;IACF;IAEA,0CAA0C;IAC1C,uCAA2C;;IAK3C;IAEA,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;oCAA4B;oCAClD,WAAW,aAAa,2BACvB,8OAAC,0IAAU,iCAAiC;oCAA3C,CAAA,UAAS;wCACR,KAAK;wCACL,KAAK;wCACL,OAAO;wCACP,QAAQ;wCACR,WAAU,cAAc,gDAAgD;;wCACxE,cAAc;wCACd,iBAAiB;wCACjB,QAAQ;wCACR,aAAa,oDAAyB;;;;;+CAEtC,wBACD,8OAAC;wCACA,KAAK;wCACL,KAAK;wCACL,WAAU,cAAc,gBAAgB;;;;;+CAExC,yBACF,8OAAC;wCAAK,WAAU;kDAAuC;;;;;6CAAiB,wBAAwB;qDAEhG,8OAAC;wCAAK,WAAU;kDAAsC;;;;;6CAAyB,sBAAsB;;;;;;;;;;;;sCAM3G,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,+IAAA,CAAA,UAAe;4CACd,MAAK;4CACL,WAAU;4CACV,iBAAiB;sDAClB;;;;;;sDAGD,8OAAC,+IAAA,CAAA,UAAe;4CACd,MAAK;4CACL,WAAU;4CACV,qBAAoB;4CACpB,mBAAmB;gDACjB,MAAM;gDACN,YAAY;oDAAE,MAAM;oDAAG,UAAU;gDAAG;gDACpC,UAAU;4CACZ;4CACA,iBAAiB;sDAClB;;;;;;sDAGD,8OAAC,+IAAA,CAAA,UAAe;4CACd,MAAK;4CACL,WAAU;4CACV,qBAAoB;4CACpB,mBAAmB;gDACjB,MAAM;gDACN,YAAY;oDAAE,MAAM;oDAAG,UAAU;gDAAG;gDACpC,UAAU;4CACZ;4CACA,iBAAiB;sDAClB;;;;;;sDAGD,8OAAC,+IAAA,CAAA,UAAe;4CACd,MAAK;4CACL,WAAU;4CACV,qBAAoB;4CACpB,mBAAmB;gDACjB,MAAM;gDACN,UAAU;4CACZ;4CACA,iBAAiB;sDAClB;;;;;;sDAGD,8OAAC,+IAAA,CAAA,UAAe;4CACd,MAAK;4CACL,WAAU;4CACV,qBAAoB;4CACpB,mBAAmB;gDACjB,MAAM;gDACN,YAAY;oDAAE,MAAM;oDAAG,UAAU;gDAAG;gDACpC,UAAU;oDACR,eAAe;oDACf,cAAc;wDACZ,UAAU;4DACR,gBAAgB;wDAClB;oDACF;gDACF;4CACF;4CACA,iBAAiB;sDAClB;;;;;;;;;;;;gCAMF,CAAC,aAAa,CACb,qBACE,8OAAC,iJAAA,CAAA,UAAmB;;;;yDAEpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;wCAKP;;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAY,aAAa,eAAe;0CAEvC,2BAAa,8OAAC,8IAAA,CAAA,MAAG;oCAAC,MAAM;;;;;yDAAS,8OAAC,8IAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gBAMrD,4BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;sCAGD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;sCAGD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;sCAGD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;sCAGD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;wBAKA,CAAC,aAAa,CACb,qBACE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;;sDAE7B,8OAAC,8IAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAS;;;;;;;8CAG7B,8OAAC;oCACC,SAAS;wCACP,cAAc;oCACd,sEAAsE;oCACxE;oCACA,WAAU;8CAEV,cAAA,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;;;;;iDAInC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;;;;;;gCAKP;;;;;;;;;;;;;;;;;;AAMZ;uCAEe", "debugId": null}}, {"offset": {"line": 2412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// Removed useState, useEffect, getStrapiContent imports as data is now passed via props\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { FiFacebook, FiTwitter, FiInstagram, FiLinkedin } from 'react-icons/fi';\r\nimport { sanitizeUrl, getStrapiMediaPath } from '@/lib/mediaUtils';\r\n\r\n// Define the type for the logoLight prop, matching Layout.tsx\r\ninterface LogoLightMedia {\r\n  id: number;\r\n  name: string;\r\n  alternativeText?: string | null;\r\n  caption?: string | null;\r\n  width?: number;\r\n  height?: number;\r\n  formats?: any;\r\n  hash: string;\r\n  ext: string;\r\n  mime: string;\r\n  size: number;\r\n  url: string;\r\n  previewUrl?: string | null;\r\n  provider: string;\r\n  provider_metadata?: any;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  publishedAt?: string;\r\n}\r\n\r\n// Define type for fetched category data\r\ninterface FooterCategory {\r\n  id: number;\r\n  attributes: {\r\n    name: string;\r\n    slug: string;\r\n  };\r\n}\r\n\r\ninterface FooterProps {\r\n  siteName: string;\r\n  logoLight: LogoLightMedia | null;\r\n  footerCategories: FooterCategory[]; // Add footerCategories prop\r\n}\r\n\r\n// Accept footerCategories as a prop\r\nconst Footer = ({ siteName, logoLight, footerCategories }: FooterProps) => {\r\n  const currentYear = new Date().getFullYear();\r\n\r\n  // Using the imported sanitizeUrl function from mediaUtils\r\n\r\n  // Get the Strapi API URL from environment variables\r\n  const strapiApiUrl = process.env.NEXT_PUBLIC_API_URL || '';\r\n\r\n  // Get the Strapi Media URL from environment variables (or derive it from API URL)\r\n  const strapiMediaUrl = process.env.IMAGE_HOSTNAME ||\r\n    (strapiApiUrl ? strapiApiUrl.replace('strapiapp.com', 'media.strapiapp.com') : '');\r\n\r\n  // For Strapi Cloud, construct the media URL correctly\r\n  let logoUrl = null;\r\n\r\n  if (logoLight?.url) {\r\n    // Use the new getStrapiMediaPath function to properly handle the URL\r\n    // This function will handle all the edge cases and prevent malformed URLs\r\n    logoUrl = getStrapiMediaPath(logoLight.url);\r\n\r\n    // Log the URL in development for debugging\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Footer Logo URL:', {\r\n        original: logoLight.url,\r\n        processed: logoUrl\r\n      });\r\n    }\r\n  }\r\n\r\n  // Log the URL in production for debugging\r\n  if (process.env.NODE_ENV === 'production' && logoLight) {\r\n    console.log('Footer Logo URL:', {\r\n      original: logoLight.url,\r\n      processed: logoUrl\r\n    });\r\n  }\r\n\r\n  // Removed useEffect and related state variables\r\n\r\n  return (\r\n    <footer className=\"bg-gray-800 text-white\">\r\n      <div className=\"container mx-auto px-4 py-12\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n          {/* About */}\r\n          <div>\r\n            {logoUrl ? (\r\n              <Link href=\"/\" className=\"inline-block mb-4\">\r\n                <Image\r\n                  src={logoUrl}\r\n                  alt={logoLight?.alternativeText || siteName}\r\n                  width={logoLight?.width || 150} // Provide default or use actual width\r\n                  height={logoLight?.height || 40} // Provide default or use actual height\r\n                  className=\"h-12 w-auto\" // Adjust styling as needed\r\n                  unoptimized={process.env.NODE_ENV === 'production'} // Use unoptimized in production to avoid issues\r\n                  priority // Prioritize loading the logo as it's important\r\n                />\r\n              </Link>\r\n            ) : (\r\n              <h3 className=\"text-xl font-semibold mb-4\">{siteName}</h3>\r\n            )}\r\n            <p className=\"text-gray-300 mb-4\">\r\n              Connecting you with holistic health practitioners and clinics to support your wellness journey.\r\n            </p>\r\n            <div className=\"flex space-x-4\">\r\n              {/* Assuming these hrefs will eventually be external URLs */}\r\n              <a href=\"#\" target=\"_blank\" rel=\"nofollow noopener noreferrer\" className=\"text-gray-300 hover:text-white\" aria-label=\"Facebook\">\r\n                <FiFacebook size={20} />\r\n              </a>\r\n              <a href=\"#\" target=\"_blank\" rel=\"nofollow noopener noreferrer\" className=\"text-gray-300 hover:text-white\" aria-label=\"Twitter\">\r\n                <FiTwitter size={20} />\r\n              </a>\r\n              <a href=\"#\" target=\"_blank\" rel=\"nofollow noopener noreferrer\" className=\"text-gray-300 hover:text-white\" aria-label=\"Instagram\">\r\n                <FiInstagram size={20} />\r\n              </a>\r\n              <a href=\"#\" target=\"_blank\" rel=\"nofollow noopener noreferrer\" className=\"text-gray-300 hover:text-white\" aria-label=\"LinkedIn\">\r\n                <FiLinkedin size={20} />\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Quick Links */}\r\n          <div>\r\n            <h3 className=\"text-xl font-semibold mb-4\">Quick Links</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li>\r\n                <Link href=\"/\" className=\"text-gray-300 hover:text-white\">\r\n                  Home\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/clinics\" className=\"text-gray-300 hover:text-white\">\r\n                  Find a Clinic\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/practitioners\" className=\"text-gray-300 hover:text-white\">\r\n                  Find a Practitioner\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/blog\" className=\"text-gray-300 hover:text-white\">\r\n                  Blog\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/about-us\" className=\"text-gray-300 hover:text-white\">\r\n                  About Us\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Categories */}\r\n          <div>\r\n            <h3 className=\"text-xl font-semibold mb-4\">Categories</h3>\r\n            <ul className=\"space-y-2\">\r\n              {/* Render directly from props, handle empty array case */}\r\n              {footerCategories && footerCategories.length > 0 ? (\r\n                footerCategories.map((category) => (\r\n                  <li key={category.id}>\r\n                    <Link\r\n                      // Use '#' as href if slug is missing due to permission issues\r\n                      href={category.attributes.slug && category.attributes.slug !== '#' ? `/categories/${category.attributes.slug}` : '#'}\r\n                      className=\"text-gray-300 hover:text-white\"\r\n                    >\r\n                      {/* Display name, potentially the default 'Unnamed Category' */}\r\n                      {category.attributes.name}\r\n                    </Link>\r\n                  </li>\r\n                ))\r\n              ) : (\r\n                <li>No categories available.</li> // Display message if no categories fetched/passed\r\n              )}\r\n              {/* Always show the \"View All\" link */}\r\n              <li>\r\n                <Link href=\"/categories\" className=\"text-gray-300 hover:text-white\">\r\n                  View All Categories\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Contact */}\r\n          <div>\r\n            <h3 className=\"text-xl font-semibold mb-4\">Contact Us</h3>\r\n            <p className=\"text-gray-300 mb-2\">Have questions or feedback?</p>\r\n            <Link href=\"/contact\" className=\"text-emerald-400 hover:text-emerald-300 mb-2 block\">\r\n              Get in touch with us\r\n            </Link>\r\n            {/* Moved Links */}\r\n            <div className=\"mt-2 space-y-1\">\r\n               <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white text-sm block\">\r\n                  Privacy Policy\r\n               </Link>\r\n               <Link href=\"/terms\" className=\"text-gray-300 hover:text-white text-sm block\">\r\n                  Terms of Service\r\n               </Link>\r\n               <Link href=\"/affiliate-disclosure\" className=\"text-gray-300 hover:text-white text-sm block\">\r\n                  Affiliate Disclosure\r\n               </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center text-gray-400\">\r\n          <p>&copy; {currentYear} {siteName}. All rights reserved.</p>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA,wFAAwF;AACxF;AACA;AACA;AACA;AANA;;;;;;AA6CA,oCAAoC;AACpC,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAe;IACpE,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,0DAA0D;IAE1D,oDAAoD;IACpD,MAAM,eAAe,mFAAmC;IAExD,kFAAkF;IAClF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAC/C,CAAC,uCAAe,aAAa,OAAO,CAAC,iBAAiB,6DAA2B;IAEnF,sDAAsD;IACtD,IAAI,UAAU;IAEd,IAAI,WAAW,KAAK;QAClB,qEAAqE;QACrE,0EAA0E;QAC1E,UAAU,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,GAAG;QAE1C,2CAA2C;QAC3C,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,oBAAoB;gBAC9B,UAAU,UAAU,GAAG;gBACvB,WAAW;YACb;QACF;IACF;IAEA,0CAA0C;IAC1C,uCAAwD;;IAKxD;IAEA,gDAAgD;IAEhD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;gCACE,wBACC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK;wCACL,KAAK,WAAW,mBAAmB;wCACnC,OAAO,WAAW,SAAS;wCAC3B,QAAQ,WAAW,UAAU;wCAC7B,WAAU,cAAc,2BAA2B;;wCACnD,aAAa,oDAAyB;wCACtC,QAAQ;;;;;;;;;;yDAIZ,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAE9C,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAE,MAAK;4CAAI,QAAO;4CAAS,KAAI;4CAA+B,WAAU;4CAAiC,cAAW;sDACnH,cAAA,8OAAC,8IAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;;;;;;sDAEpB,8OAAC;4CAAE,MAAK;4CAAI,QAAO;4CAAS,KAAI;4CAA+B,WAAU;4CAAiC,cAAW;sDACnH,cAAA,8OAAC,8IAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;sDAEnB,8OAAC;4CAAE,MAAK;4CAAI,QAAO;4CAAS,KAAI;4CAA+B,WAAU;4CAAiC,cAAW;sDACnH,cAAA,8OAAC,8IAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;;;;;;sDAErB,8OAAC;4CAAE,MAAK;4CAAI,QAAO;4CAAS,KAAI;4CAA+B,WAAU;4CAAiC,cAAW;sDACnH,cAAA,8OAAC,8IAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAMxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAiC;;;;;;;;;;;sDAI5D,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;sDAInE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAAiC;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAiC;;;;;;;;;;;sDAIhE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAQxE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;wCAEX,oBAAoB,iBAAiB,MAAM,GAAG,IAC7C,iBAAiB,GAAG,CAAC,CAAC,yBACpB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,8DAA8D;oDAC9D,MAAM,SAAS,UAAU,CAAC,IAAI,IAAI,SAAS,UAAU,CAAC,IAAI,KAAK,MAAM,CAAC,YAAY,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE,GAAG;oDACjH,WAAU;8DAGT,SAAS,UAAU,CAAC,IAAI;;;;;;+CAPpB,SAAS,EAAE;;;;sEAYtB,8OAAC;sDAAG;;;;;iDAA8B,kDAAkD;;sDAGtF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAQ1E,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAqD;;;;;;8CAIrF,8OAAC;oCAAI,WAAU;;sDACZ,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA+C;;;;;;sDAG/E,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA+C;;;;;;sDAG7E,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAwB,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;8BAOnG,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAAQ;4BAAY;4BAAE;4BAAS;;;;;;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 2865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/ErrorFallback.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { FiAlertTriangle, FiRefreshCw, FiHome } from 'react-icons/fi';\n\ninterface ErrorFallbackProps {\n  error?: Error | null;\n  resetErrorBoundary?: () => void;\n  message?: string;\n  showHomeLink?: boolean;\n  showRefreshButton?: boolean;\n}\n\n/**\n * A reusable error fallback component that displays a user-friendly error message\n * with options to refresh the page or navigate home.\n */\nconst ErrorFallback: React.FC<ErrorFallbackProps> = ({\n  error,\n  resetErrorBoundary,\n  message = 'Something went wrong',\n  showHomeLink = true,\n  showRefreshButton = true,\n}) => {\n  // Determine if we should show the technical error details\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const errorMessage = error?.message || message;\n  \n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 my-4 max-w-2xl mx-auto\">\n      <div className=\"flex items-center mb-4 text-red-600\">\n        <FiAlertTriangle className=\"w-6 h-6 mr-2\" />\n        <h2 className=\"text-xl font-semibold\">Error Encountered</h2>\n      </div>\n      \n      <p className=\"mb-4 text-gray-700\">{message}</p>\n      \n      {/* Show technical error details only in development */}\n      {isDevelopment && error && (\n        <div className=\"mb-4 p-3 bg-gray-100 rounded overflow-auto max-h-40 text-sm\">\n          <p className=\"font-mono text-red-600\">{errorMessage}</p>\n          {error.stack && (\n            <pre className=\"mt-2 text-xs text-gray-700 whitespace-pre-wrap\">\n              {error.stack.split('\\n').slice(1, 5).join('\\n')}\n            </pre>\n          )}\n        </div>\n      )}\n      \n      <div className=\"flex flex-wrap gap-3 mt-4\">\n        {showRefreshButton && resetErrorBoundary && (\n          <button\n            onClick={resetErrorBoundary}\n            className=\"flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors\"\n          >\n            <FiRefreshCw className=\"mr-2\" />\n            Try Again\n          </button>\n        )}\n        \n        {showRefreshButton && !resetErrorBoundary && (\n          <button\n            onClick={() => window.location.reload()}\n            className=\"flex items-center px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors\"\n          >\n            <FiRefreshCw className=\"mr-2\" />\n            Refresh Page\n          </button>\n        )}\n        \n        {showHomeLink && (\n          <Link href=\"/\" className=\"flex items-center px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors\">\n            <FiHome className=\"mr-2\" />\n            Go to Homepage\n          </Link>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ErrorFallback;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAcA;;;CAGC,GACD,MAAM,gBAA8C,CAAC,EACnD,KAAK,EACL,kBAAkB,EAClB,UAAU,sBAAsB,EAChC,eAAe,IAAI,EACnB,oBAAoB,IAAI,EACzB;IACC,0DAA0D;IAC1D,MAAM,gBAAgB,oDAAyB;IAC/C,MAAM,eAAe,OAAO,WAAW;IAEvC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,8OAAC;wBAAG,WAAU;kCAAwB;;;;;;;;;;;;0BAGxC,8OAAC;gBAAE,WAAU;0BAAsB;;;;;;YAGlC,iBAAiB,uBAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;oBACtC,MAAM,KAAK,kBACV,8OAAC;wBAAI,WAAU;kCACZ,MAAM,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;;;;;;0BAMlD,8OAAC;gBAAI,WAAU;;oBACZ,qBAAqB,oCACpB,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,8IAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAS;;;;;;;oBAKnC,qBAAqB,CAAC,oCACrB,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;;0CAEV,8OAAC,8IAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAS;;;;;;;oBAKnC,8BACC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC,8IAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAS;;;;;;;;;;;;;;;;;;;AAOvC;uCAEe", "debugId": null}}, {"offset": {"line": 3019, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ErrorInfo, ReactNode } from 'react';\nimport ErrorFallback from './ErrorFallback';\n\ninterface ErrorBoundaryProps {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n}\n\ninterface ErrorBoundaryState {\n  hasError: boolean;\n  error: Error | null;\n}\n\n/**\n * A client-side error boundary component that catches JavaScript errors\n * in its child component tree and displays a fallback UI.\n */\nclass ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {\n    // Log the error to an error reporting service\n    console.error('Error caught by ErrorBoundary:', error, errorInfo);\n    \n    // Call the onError callback if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n  }\n\n  render(): ReactNode {\n    if (this.state.hasError) {\n      // You can render any custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n      \n      return (\n        <ErrorFallback \n          error={this.state.error} \n          resetErrorBoundary={() => this.setState({ hasError: false, error: null })} \n        />\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBA;;;CAGC,GACD,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YACX,UAAU;YACV,OAAO;QACT;IACF;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,4DAA4D;QAC5D,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAQ;QAC1D,8CAA8C;QAC9C,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,wCAAwC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IACF;IAEA,SAAoB;QAClB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,wCAAwC;YACxC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,qBACE,8OAAC,6IAAA,CAAA,UAAa;gBACZ,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBACvB,oBAAoB,IAAM,IAAI,CAAC,QAAQ,CAAC;wBAAE,UAAU;wBAAO,OAAO;oBAAK;;;;;;QAG7E;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 3083, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/GlobalErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport ErrorBoundary from './ErrorBoundary';\nimport { useError } from '@/contexts/ErrorContext';\n\ninterface GlobalErrorBoundaryProps {\n  children: React.ReactNode;\n}\n\n/**\n * A global error boundary component that integrates with the ErrorContext\n * to provide consistent error handling across the application.\n */\nconst GlobalErrorBoundary: React.FC<GlobalErrorBoundaryProps> = ({ children }) => {\n  const { addErrorLog } = useError();\n\n  // Handle unhandled promise rejections\n  useEffect(() => {\n    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {\n      console.error('Unhandled promise rejection:', event.reason);\n      addErrorLog(\n        event.reason instanceof Error ? event.reason : new Error(String(event.reason)),\n        'unhandled-promise-rejection'\n      );\n    };\n\n    // Add event listener for unhandled promise rejections\n    window.addEventListener('unhandledrejection', handleUnhandledRejection);\n\n    // Clean up event listener\n    return () => {\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection);\n    };\n  }, [addErrorLog]);\n\n  return (\n    <ErrorBoundary\n      onError={(error, errorInfo) => {\n        addErrorLog(error, 'react-error-boundary');\n        \n        // You could also send the error to an error reporting service here\n        // Example: sendToErrorReportingService(error, errorInfo);\n      }}\n    >\n      {children}\n    </ErrorBoundary>\n  );\n};\n\nexport default GlobalErrorBoundary;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUA;;;CAGC,GACD,MAAM,sBAA0D,CAAC,EAAE,QAAQ,EAAE;IAC3E,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAE/B,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,2BAA2B,CAAC;YAChC,QAAQ,KAAK,CAAC,gCAAgC,MAAM,MAAM;YAC1D,YACE,MAAM,MAAM,YAAY,QAAQ,MAAM,MAAM,GAAG,IAAI,MAAM,OAAO,MAAM,MAAM,IAC5E;QAEJ;QAEA,sDAAsD;QACtD,OAAO,gBAAgB,CAAC,sBAAsB;QAE9C,0BAA0B;QAC1B,OAAO;YACL,OAAO,mBAAmB,CAAC,sBAAsB;QACnD;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC,6IAAA,CAAA,UAAa;QACZ,SAAS,CAAC,OAAO;YACf,YAAY,OAAO;QAEnB,mEAAmE;QACnE,0DAA0D;QAC5D;kBAEC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/lib/scriptManager.ts"], "sourcesContent": ["'use client';\n\n/**\n * Script loading strategies\n */\nexport enum ScriptStrategy {\n  /**\n   * Load the script before any Next.js code and before any page hydration occurs\n   */\n  BEFORE_INTERACTIVE = 'beforeInteractive',\n  \n  /**\n   * Load the script early but after some hydration on the page occurs\n   */\n  AFTER_INTERACTIVE = 'afterInteractive',\n  \n  /**\n   * Load the script later during browser idle time\n   */\n  LAZY_ONLOAD = 'lazyOnload',\n  \n  /**\n   * Load the script in a web worker (experimental)\n   */\n  WORKER = 'worker',\n}\n\n/**\n * Script configuration\n */\nexport interface ScriptConfig {\n  /**\n   * Script source URL\n   */\n  src?: string;\n  \n  /**\n   * Inline script content\n   */\n  content?: string;\n  \n  /**\n   * Unique identifier for the script\n   */\n  id: string;\n  \n  /**\n   * Loading strategy\n   */\n  strategy?: ScriptStrategy;\n  \n  /**\n   * Whether to load the script on all pages\n   */\n  global?: boolean;\n  \n  /**\n   * Callback to execute when the script has loaded\n   */\n  onLoad?: () => void;\n  \n  /**\n   * Callback to execute when the script fails to load\n   */\n  onError?: (error: Error) => void;\n  \n  /**\n   * Additional script attributes\n   */\n  attributes?: Record<string, string>;\n}\n\n/**\n * Registry of scripts to be loaded\n */\nconst scriptRegistry: Record<string, ScriptConfig> = {};\n\n/**\n * Register a script to be loaded\n * \n * @param config Script configuration\n */\nexport function registerScript(config: ScriptConfig): void {\n  scriptRegistry[config.id] = config;\n}\n\n/**\n * Get all registered scripts\n * \n * @returns Array of script configurations\n */\nexport function getRegisteredScripts(): ScriptConfig[] {\n  return Object.values(scriptRegistry);\n}\n\n/**\n * Get a specific registered script by ID\n * \n * @param id Script ID\n * @returns Script configuration or undefined if not found\n */\nexport function getRegisteredScript(id: string): ScriptConfig | undefined {\n  return scriptRegistry[id];\n}\n\n/**\n * Unregister a script\n * \n * @param id Script ID\n */\nexport function unregisterScript(id: string): void {\n  delete scriptRegistry[id];\n}\n\n/**\n * Check if a script is registered\n * \n * @param id Script ID\n * @returns True if the script is registered\n */\nexport function isScriptRegistered(id: string): boolean {\n  return !!scriptRegistry[id];\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAKO,IAAA,AAAK,wCAAA;IACV;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;WAlBS;;AAmEZ;;CAEC,GACD,MAAM,iBAA+C,CAAC;AAO/C,SAAS,eAAe,MAAoB;IACjD,cAAc,CAAC,OAAO,EAAE,CAAC,GAAG;AAC9B;AAOO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB;AAQO,SAAS,oBAAoB,EAAU;IAC5C,OAAO,cAAc,CAAC,GAAG;AAC3B;AAOO,SAAS,iBAAiB,EAAU;IACzC,OAAO,cAAc,CAAC,GAAG;AAC3B;AAQO,SAAS,mBAAmB,EAAU;IAC3C,OAAO,CAAC,CAAC,cAAc,CAAC,GAAG;AAC7B", "debugId": null}}, {"offset": {"line": 3183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/OptimizedScripts.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport Script from 'next/script';\nimport { getRegisteredScripts, ScriptStrategy } from '@/lib/scriptManager';\n\n/**\n * Component to render all registered scripts with proper optimization\n */\nexport default function OptimizedScripts() {\n  const scripts = getRegisteredScripts();\n  \n  return (\n    <>\n      {scripts.map((script) => {\n        // Skip scripts without src or content\n        if (!script.src && !script.content) {\n          return null;\n        }\n        \n        // Prepare additional attributes\n        const attributes = script.attributes || {};\n        \n        if (script.content) {\n          // Render inline script\n          return (\n            <Script\n              key={script.id}\n              id={script.id}\n              strategy={script.strategy || ScriptStrategy.AFTER_INTERACTIVE}\n              dangerouslySetInnerHTML={{ __html: script.content }}\n              onLoad={script.onLoad}\n              onError={script.onError ? \n                (e) => script.onError?.(new Error(`Failed to load script: ${script.id}`)) : \n                undefined\n              }\n              {...attributes}\n            />\n          );\n        } else {\n          // Render external script\n          return (\n            <Script\n              key={script.id}\n              id={script.id}\n              src={script.src}\n              strategy={script.strategy || ScriptStrategy.AFTER_INTERACTIVE}\n              onLoad={script.onLoad}\n              onError={script.onError ? \n                (e) => script.onError?.(new Error(`Failed to load script: ${script.id}`)) : \n                undefined\n              }\n              {...attributes}\n            />\n          );\n        }\n      })}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AASe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD;IAEnC,qBACE;kBACG,QAAQ,GAAG,CAAC,CAAC;YACZ,sCAAsC;YACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,OAAO,EAAE;gBAClC,OAAO;YACT;YAEA,gCAAgC;YAChC,MAAM,aAAa,OAAO,UAAU,IAAI,CAAC;YAEzC,IAAI,OAAO,OAAO,EAAE;gBAClB,uBAAuB;gBACvB,qBACE,8OAAC,8HAAA,CAAA,UAAM;oBAEL,IAAI,OAAO,EAAE;oBACb,UAAU,OAAO,QAAQ,IAAI,2HAAA,CAAA,iBAAc,CAAC,iBAAiB;oBAC7D,yBAAyB;wBAAE,QAAQ,OAAO,OAAO;oBAAC;oBAClD,QAAQ,OAAO,MAAM;oBACrB,SAAS,OAAO,OAAO,GACrB,CAAC,IAAM,OAAO,OAAO,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,OAAO,EAAE,EAAE,KACvE;oBAED,GAAG,UAAU;mBATT,OAAO,EAAE;;;;;YAYpB,OAAO;gBACL,yBAAyB;gBACzB,qBACE,8OAAC,8HAAA,CAAA,UAAM;oBAEL,IAAI,OAAO,EAAE;oBACb,KAAK,OAAO,GAAG;oBACf,UAAU,OAAO,QAAQ,IAAI,2HAAA,CAAA,iBAAc,CAAC,iBAAiB;oBAC7D,QAAQ,OAAO,MAAM;oBACrB,SAAS,OAAO,OAAO,GACrB,CAAC,IAAM,OAAO,OAAO,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,OAAO,EAAE,EAAE,KACvE;oBAED,GAAG,UAAU;mBATT,OAAO,EAAE;;;;;YAYpB;QACF;;AAGN", "debugId": null}}, {"offset": {"line": 3243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/analytics/AnalyticsScripts.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { registerScript, ScriptStrategy } from '@/lib/scriptManager';\n\ninterface AnalyticsScriptsProps {\n  /**\n   * Google Analytics measurement ID (e.g., G-XXXXXXXXXX)\n   */\n  googleAnalyticsId?: string;\n  \n  /**\n   * Whether to enable analytics in development mode\n   */\n  enableInDevelopment?: boolean;\n}\n\n/**\n * Component to register analytics scripts\n * \n * This component doesn't render anything, it just registers scripts\n * to be loaded by the OptimizedScripts component.\n */\nexport default function AnalyticsScripts({\n  googleAnalyticsId,\n  enableInDevelopment = false,\n}: AnalyticsScriptsProps) {\n  useEffect(() => {\n    // Skip in development mode unless explicitly enabled\n    if (process.env.NODE_ENV === 'development' && !enableInDevelopment) {\n      return;\n    }\n    \n    // Register Google Analytics if ID is provided\n    if (googleAnalyticsId) {\n      // Register the GA script\n      registerScript({\n        id: 'google-analytics',\n        src: `https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`,\n        strategy: ScriptStrategy.AFTER_INTERACTIVE,\n        attributes: {\n          async: 'true',\n        },\n      });\n      \n      // Register the GA initialization script\n      registerScript({\n        id: 'google-analytics-init',\n        content: `\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '${googleAnalyticsId}', {\n            page_path: window.location.pathname,\n          });\n        `,\n        strategy: ScriptStrategy.AFTER_INTERACTIVE,\n      });\n    }\n    \n    // Add more analytics scripts as needed\n    \n  }, [googleAnalyticsId, enableInDevelopment]);\n  \n  // This component doesn't render anything\n  return null;\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAuBe,SAAS,iBAAiB,EACvC,iBAAiB,EACjB,sBAAsB,KAAK,EACL;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qDAAqD;QACrD,IAAI,oDAAyB,iBAAiB,CAAC,qBAAqB;YAClE;QACF;QAEA,8CAA8C;QAC9C,IAAI,mBAAmB;YACrB,yBAAyB;YACzB,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;gBACb,IAAI;gBACJ,KAAK,CAAC,4CAA4C,EAAE,mBAAmB;gBACvE,UAAU,2HAAA,CAAA,iBAAc,CAAC,iBAAiB;gBAC1C,YAAY;oBACV,OAAO;gBACT;YACF;YAEA,wCAAwC;YACxC,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;gBACb,IAAI;gBACJ,SAAS,CAAC;;;;0BAIQ,EAAE,kBAAkB;;;QAGtC,CAAC;gBACD,UAAU,2HAAA,CAAA,iBAAc,CAAC,iBAAiB;YAC5C;QACF;IAEA,uCAAuC;IAEzC,GAAG;QAAC;QAAmB;KAAoB;IAE3C,yCAAyC;IACzC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/ResourceHints.tsx"], "sourcesContent": ["'use client';\n\n// Using Script component from next/script for client-side resource hints\nimport Script from 'next/script';\n\ninterface ResourceHintsProps {\n  /**\n   * DNS prefetch domains\n   */\n  dnsPrefetch?: string[];\n\n  /**\n   * Preconnect domains\n   */\n  preconnect?: string[];\n\n  /**\n   * Preload resources\n   */\n  preload?: {\n    href: string;\n    as: 'script' | 'style' | 'image' | 'font' | 'fetch' | 'document';\n    type?: string;\n    crossOrigin?: 'anonymous' | 'use-credentials';\n  }[];\n\n  /**\n   * Prefetch resources\n   */\n  prefetch?: {\n    href: string;\n    as?: 'script' | 'style' | 'image' | 'font' | 'fetch' | 'document';\n  }[];\n}\n\n/**\n * Component to add resource hints to the document head\n *\n * This component adds DNS prefetch, preconnect, preload, and prefetch\n * resource hints to improve performance.\n */\nexport default function ResourceHints({\n  dnsPrefetch = [],\n  preconnect = [],\n  preload = [],\n  prefetch = [],\n}: ResourceHintsProps) {\n  // In Next.js 15 with App Router, we need to inject these hints using a script\n  // that runs on the client side to add the resource hints to the document head\n\n  // Create a script that will inject the resource hints\n  const injectResourceHints = `\n    (function() {\n      const head = document.head;\n\n      // DNS Prefetch\n      ${dnsPrefetch.map(domain => `\n        const dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')} = document.createElement('link');\n        dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.rel = 'dns-prefetch';\n        dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.href = '${domain}';\n        head.appendChild(dnsPrefetch_${domain.replace(/[^a-zA-Z0-9]/g, '_')});\n      `).join('')}\n\n      // Preconnect\n      ${preconnect.map(domain => `\n        const preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')} = document.createElement('link');\n        preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.rel = 'preconnect';\n        preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.href = '${domain}';\n        preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.crossOrigin = 'anonymous';\n        head.appendChild(preconnect_${domain.replace(/[^a-zA-Z0-9]/g, '_')});\n      `).join('')}\n\n      // Preload\n      ${preload.map((resource, index) => `\n        const preload_${index} = document.createElement('link');\n        preload_${index}.rel = 'preload';\n        preload_${index}.href = '${resource.href}';\n        preload_${index}.as = '${resource.as}';\n        ${resource.type ? `preload_${index}.type = '${resource.type}';` : ''}\n        ${resource.crossOrigin ? `preload_${index}.crossOrigin = '${resource.crossOrigin}';` : ''}\n        head.appendChild(preload_${index});\n      `).join('')}\n\n      // Prefetch\n      ${prefetch.map((resource, index) => `\n        const prefetch_${index} = document.createElement('link');\n        prefetch_${index}.rel = 'prefetch';\n        prefetch_${index}.href = '${resource.href}';\n        ${resource.as ? `prefetch_${index}.as = '${resource.as}';` : ''}\n        head.appendChild(prefetch_${index});\n      `).join('')}\n    })();\n  `;\n\n  return (\n    <Script id=\"resource-hints\" strategy=\"beforeInteractive\" dangerouslySetInnerHTML={{ __html: injectResourceHints }} />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,yEAAyE;AACzE;AAHA;;;AAyCe,SAAS,cAAc,EACpC,cAAc,EAAE,EAChB,aAAa,EAAE,EACf,UAAU,EAAE,EACZ,WAAW,EAAE,EACM;IACnB,8EAA8E;IAC9E,8EAA8E;IAE9E,sDAAsD;IACtD,MAAM,sBAAsB,CAAC;;;;;MAKzB,EAAE,YAAY,GAAG,CAAC,CAAA,SAAU,CAAC;0BACT,EAAE,OAAO,OAAO,CAAC,iBAAiB,KAAK;oBAC7C,EAAE,OAAO,OAAO,CAAC,iBAAiB,KAAK;oBACvC,EAAE,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,OAAO;qCACxC,EAAE,OAAO,OAAO,CAAC,iBAAiB,KAAK;MACtE,CAAC,EAAE,IAAI,CAAC,IAAI;;;MAGZ,EAAE,WAAW,GAAG,CAAC,CAAA,SAAU,CAAC;yBACT,EAAE,OAAO,OAAO,CAAC,iBAAiB,KAAK;mBAC7C,EAAE,OAAO,OAAO,CAAC,iBAAiB,KAAK;mBACvC,EAAE,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,OAAO;mBACzD,EAAE,OAAO,OAAO,CAAC,iBAAiB,KAAK;oCACtB,EAAE,OAAO,OAAO,CAAC,iBAAiB,KAAK;MACrE,CAAC,EAAE,IAAI,CAAC,IAAI;;;MAGZ,EAAE,QAAQ,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;sBACpB,EAAE,MAAM;gBACd,EAAE,MAAM;gBACR,EAAE,MAAM,SAAS,EAAE,SAAS,IAAI,CAAC;gBACjC,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,CAAC;QACrC,EAAE,SAAS,IAAI,GAAG,CAAC,QAAQ,EAAE,MAAM,SAAS,EAAE,SAAS,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;QACrE,EAAE,SAAS,WAAW,GAAG,CAAC,QAAQ,EAAE,MAAM,gBAAgB,EAAE,SAAS,WAAW,CAAC,EAAE,CAAC,GAAG,GAAG;iCACjE,EAAE,MAAM;MACnC,CAAC,EAAE,IAAI,CAAC,IAAI;;;MAGZ,EAAE,SAAS,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;uBACpB,EAAE,MAAM;iBACd,EAAE,MAAM;iBACR,EAAE,MAAM,SAAS,EAAE,SAAS,IAAI,CAAC;QAC1C,EAAE,SAAS,EAAE,GAAG,CAAC,SAAS,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG;kCACtC,EAAE,MAAM;MACpC,CAAC,EAAE,IAAI,CAAC,IAAI;;EAEhB,CAAC;IAED,qBACE,8OAAC,8HAAA,CAAA,UAAM;QAAC,IAAG;QAAiB,UAAS;QAAoB,yBAAyB;YAAE,QAAQ;QAAoB;;;;;;AAEpH", "debugId": null}}, {"offset": {"line": 3369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/shared/EnvCheck.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\n/**\n * Component to check environment variables and display warnings in development\n * This component will only render in development mode\n */\nconst EnvCheck = () => {\n  const [showWarning, setShowWarning] = useState(false);\n  const [warnings, setWarnings] = useState<string[]>([]);\n\n  useEffect(() => {\n    // Only run in development mode\n    if (process.env.NODE_ENV !== 'development') {\n      return;\n    }\n\n    const newWarnings: string[] = [];\n\n    // Check for API URL\n    if (!process.env.NEXT_PUBLIC_API_URL) {\n      newWarnings.push('NEXT_PUBLIC_API_URL is not set. This is required for API requests.');\n    }\n\n    // Check for API token\n    if (!process.env.STRAPI_API_TOKEN) {\n      newWarnings.push('STRAPI_API_TOKEN is not set. This is required for authenticated API requests.');\n    }\n\n    // Set warnings and show if there are any\n    setWarnings(newWarnings);\n    setShowWarning(newWarnings.length > 0);\n  }, []);\n\n  // Don't render anything in production or if there are no warnings\n  if (process.env.NODE_ENV !== 'development' || !showWarning) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed bottom-0 left-0 right-0 bg-amber-100 border-t border-amber-300 p-4 z-50\">\n      <div className=\"container mx-auto\">\n        <h3 className=\"text-amber-800 font-bold mb-2\">⚠️ Environment Variable Warnings</h3>\n        <ul className=\"list-disc pl-5 text-amber-700\">\n          {warnings.map((warning, index) => (\n            <li key={index}>{warning}</li>\n          ))}\n        </ul>\n        <p className=\"mt-2 text-amber-600 text-sm\">\n          These warnings are only visible in development mode. Check your .env.local file.\n        </p>\n        <button\n          onClick={() => setShowWarning(false)}\n          className=\"mt-2 px-3 py-1 bg-amber-200 hover:bg-amber-300 text-amber-800 rounded text-sm\"\n        >\n          Dismiss\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default EnvCheck;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA;;;CAGC,GACD,MAAM,WAAW;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,uCAA4C;;QAE5C;QAEA,MAAM,cAAwB,EAAE;QAEhC,oBAAoB;QACpB,uCAAsC;;QAEtC;QAEA,sBAAsB;QACtB,IAAI,CAAC,QAAQ,GAAG,CAAC,gBAAgB,EAAE;YACjC,YAAY,IAAI,CAAC;QACnB;QAEA,yCAAyC;QACzC,YAAY;QACZ,eAAe,YAAY,MAAM,GAAG;IACtC,GAAG,EAAE;IAEL,kEAAkE;IAClE,IAAI,oDAAyB,iBAAiB,CAAC,aAAa;QAC1D,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAgC;;;;;;8BAC9C,8OAAC;oBAAG,WAAU;8BACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;sCAAgB;2BAAR;;;;;;;;;;8BAGb,8OAAC;oBAAE,WAAU;8BAA8B;;;;;;8BAG3C,8OAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 3468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/components/layout/Layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode } from 'react';\r\nimport Header from './Header';\r\nimport Footer from './Footer';\r\nimport GlobalErrorBoundary from '../shared/GlobalErrorBoundary';\r\nimport OptimizedScripts from '../shared/OptimizedScripts';\r\nimport AnalyticsScripts from '../analytics/AnalyticsScripts';\r\nimport ResourceHints from '../shared/ResourceHints';\r\nimport EnvCheck from '../shared/EnvCheck';\r\n\r\n// Define the type for the actual logoLight prop received from Strapi's API response\r\n// This should match the type expected by the Header component\r\ninterface LogoLightMedia {\r\n  id: number;\r\n  name: string;\r\n  alternativeText?: string | null;\r\n  caption?: string | null;\r\n  width?: number;\r\n  height?: number;\r\n  formats?: any; // Can be more specific if needed\r\n  hash: string;\r\n  ext: string;\r\n  mime: string;\r\n  size: number;\r\n  url: string;\r\n  previewUrl?: string | null;\r\n  provider: string;\r\n  provider_metadata?: any;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  publishedAt?: string; // publishedAt might not always be present\r\n}\r\n\r\n// Define type for fetched category data (matching structure from RootLayout)\r\ninterface FooterCategory {\r\n  id: number;\r\n  attributes: {\r\n    name: string;\r\n    slug: string;\r\n  };\r\n}\r\n\r\n\r\ninterface LayoutProps {\r\n  children: ReactNode;\r\n  siteName: string; // Add siteName prop\r\n  logoLight: LogoLightMedia | null; // Use the corrected type matching Header\r\n  footerCategories: FooterCategory[]; // Add footerCategories prop\r\n}\r\n\r\n// Receive the new props\r\nconst Layout = ({ children, siteName, logoLight, footerCategories }: LayoutProps) => {\r\n  // Define critical resources for preloading\r\n  const strapiBaseUrl = process.env.NEXT_PUBLIC_API_URL || '';\r\n\r\n  return (\r\n    <GlobalErrorBoundary>\r\n      {/* Add resource hints for critical domains and assets */}\r\n      <ResourceHints\r\n        dnsPrefetch={[\r\n          strapiBaseUrl,\r\n          'https://fonts.googleapis.com',\r\n          'https://fonts.gstatic.com',\r\n        ]}\r\n        preconnect={[\r\n          strapiBaseUrl,\r\n          'https://fonts.googleapis.com',\r\n          'https://fonts.gstatic.com',\r\n        ]}\r\n        preload={[\r\n          // Preload critical fonts\r\n          {\r\n            href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',\r\n            as: 'style',\r\n          },\r\n          // Don't preload logo - it's already loaded by the Header and Footer components\r\n          // This was causing the duplicate logo loading with incorrect URL format\r\n        ]}\r\n      />\r\n\r\n      <div className=\"flex flex-col min-h-screen\">\r\n        {/* Pass the props down to the Header component */}\r\n        <Header siteName={siteName} logoLight={logoLight} />\r\n        <main className=\"flex-grow\">{children}</main>\r\n        {/* Pass siteName, logoLight, and footerCategories props to Footer */}\r\n        <Footer siteName={siteName} logoLight={logoLight} footerCategories={footerCategories} />\r\n\r\n        {/* Register analytics scripts */}\r\n        <AnalyticsScripts\r\n          googleAnalyticsId={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}\r\n          enableInDevelopment={false}\r\n        />\r\n\r\n        {/* Render optimized scripts */}\r\n        <OptimizedScripts />\r\n\r\n        {/* Environment variable check (only visible in development) */}\r\n        <EnvCheck />\r\n      </div>\r\n    </GlobalErrorBoundary>\r\n  );\r\n};\r\n\r\nexport default Layout;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAmDA,wBAAwB;AACxB,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAe;IAC9E,2CAA2C;IAC3C,MAAM,gBAAgB,mFAAmC;IAEzD,qBACE,8OAAC,mJAAA,CAAA,UAAmB;;0BAElB,8OAAC,6IAAA,CAAA,UAAa;gBACZ,aAAa;oBACX;oBACA;oBACA;iBACD;gBACD,YAAY;oBACV;oBACA;oBACA;iBACD;gBACD,SAAS;oBACP,yBAAyB;oBACzB;wBACE,MAAM;wBACN,IAAI;oBACN;iBAGD;;;;;;0BAGH,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,sIAAA,CAAA,UAAM;wBAAC,UAAU;wBAAU,WAAW;;;;;;kCACvC,8OAAC;wBAAK,WAAU;kCAAa;;;;;;kCAE7B,8OAAC,sIAAA,CAAA,UAAM;wBAAC,UAAU;wBAAU,WAAW;wBAAW,kBAAkB;;;;;;kCAGpE,8OAAC,mJAAA,CAAA,UAAgB;wBACf,mBAAmB,QAAQ,GAAG,CAAC,6BAA6B;wBAC5D,qBAAqB;;;;;;kCAIvB,8OAAC,gJAAA,CAAA,UAAgB;;;;;kCAGjB,8OAAC,wIAAA,CAAA,UAAQ;;;;;;;;;;;;;;;;;AAIjB;uCAEe", "debugId": null}}, {"offset": {"line": 3583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/naturalhealingnow/frontend/src/providers/QueryProvider.tsx"], "sourcesContent": ["'use client'; // This directive is essential for providers using client-side hooks\r\n\r\nimport React, { useState } from 'react';\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\r\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\r\n\r\ninterface QueryProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport default function QueryProvider({ children }: QueryProviderProps) {\r\n  // Initialize QueryClient. It's good practice to create it once.\r\n  // useState ensures it's not recreated on every render.\r\n  const [queryClient] = useState(\r\n    () =>\r\n      new QueryClient({\r\n        defaultOptions: {\r\n          queries: {\r\n            // Default staleTime: 0 means queries are considered stale immediately.\r\n            // You might want to set a global staleTime, e.g., 5 minutes.\r\n            staleTime: 1000 * 60 * 5, // 5 minutes\r\n            // gcTime (garbage collection time): Data is kept in cache for this long after all observers are unmounted.\r\n            // Renamed from cacheTime in v4. Default is 5 minutes.\r\n            gcTime: 1000 * 60 * 30, // 30 minutes\r\n            // Default refetchOnWindowFocus: true. Refetches query on window focus.\r\n            refetchOnWindowFocus: process.env.NODE_ENV === 'production', // Disable in dev for less noise\r\n            // Default retry: 3 times with exponential backoff.\r\n            // retry: 1, // Or configure as needed\r\n          },\r\n        },\r\n      })\r\n  );\r\n\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      {children}\r\n      {/* React Query Devtools are super helpful for debugging */}\r\n      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}\r\n    </QueryClientProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA,cAAc,oEAAoE;;;;;AAUnE,SAAS,cAAc,EAAE,QAAQ,EAAsB;IACpE,gEAAgE;IAChE,uDAAuD;IACvD,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC3B,IACE,IAAI,6KAAA,CAAA,cAAW,CAAC;YACd,gBAAgB;gBACd,SAAS;oBACP,uEAAuE;oBACvE,6DAA6D;oBAC7D,WAAW,OAAO,KAAK;oBACvB,2GAA2G;oBAC3G,sDAAsD;oBACtD,QAAQ,OAAO,KAAK;oBACpB,uEAAuE;oBACvE,sBAAsB,oDAAyB;gBAGjD;YACF;QACF;IAGJ,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;YAEA,oDAAyB,+BAAiB,8OAAC,oLAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGpF", "debugId": null}}]}