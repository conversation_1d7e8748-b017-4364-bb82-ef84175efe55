# Recommended ISR Strategy for Natural Healing Now

This document outlines the recommended Incremental Static Regeneration (ISR) strategy for the Natural Healing Now website to optimize performance and reduce server-side rendering.

## Current Issues

The current implementation has several issues:

1. **Inconsistent ISR Configuration**: Some pages use `revalidate = false` (on-demand revalidation only), while others use `revalidate = 3600` (time-based revalidation every hour). This inconsistency leads to unpredictable caching behavior.

2. **Webhook Configuration Issues**: The Strapi webhook is not properly configured to trigger revalidation when content changes.

3. **Duplicate API Requests**: The logs show duplicate requests for the same endpoints, suggesting inefficient data fetching.

## Recommended Strategy

### 1. Consistent ISR Configuration

For all dynamic content pages, we recommend using:

```typescript
// Enable ISR with on-demand revalidation
// No revalidation period - will only revalidate when triggered by webhook
export const revalidate = false;
// Allow dynamic params to be generated on-demand
export const dynamicParams = true;
```

This approach should be used for:
- Blog posts (`/blog/[slug]`)
- Blog category pages (`/blog/categories/[slug]`)
- Clinic pages (`/clinics/[slug]`)
- Practitioner pages (`/practitioners/[slug]`)
- Specialty pages (`/specialities/[slug]`)
- Condition pages (`/conditions/[slug]`)
- Homepage

For list pages that show multiple items, we recommend using a short revalidation period as a fallback:

```typescript
// Enable ISR with a short fallback revalidation period
// This ensures that if webhooks fail, the page will still be updated eventually
export const revalidate = 3600; // 1 hour
```

This approach should be used for:
- Blog list page (`/blog`)
- Clinics list page (`/clinics`)
- Practitioners list page (`/practitioners`)
- Categories list page (`/categories`)
- Specialties list page (`/specialities`)

### 2. Proper Webhook Configuration

Strapi webhooks should be configured to call the revalidation API when content changes:

1. **Webhook URL**: `https://www.naturalhealingnow.com/api/revalidate`
2. **Headers**:
   - `Content-Type`: `application/json`
3. **Request Body**:
   ```json
   {
     "token": "YOUR_REVALIDATION_TOKEN",
     "contentType": "CONTENT_TYPE",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

Where:
- `YOUR_REVALIDATION_TOKEN` is the value of `PREVIEW_SECRET` in your Vercel environment variables
- `CONTENT_TYPE` is the type of content being updated (e.g., `blog-post`, `clinic`, `practitioner`)
- `{{entry.id}}` and `{{entry.slug}}` are placeholders that Strapi will replace with the actual values

### 3. Efficient Data Fetching

To reduce duplicate API requests:

1. **Use React Query with Proper Caching**:
   ```typescript
   const { data, isLoading, error } = useQuery({
     queryKey: ['data', id],
     queryFn: () => fetchData(id),
     staleTime: Infinity, // Never stale (rely on on-demand revalidation)
     gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
   });
   ```

2. **Implement Server-Side Caching**:
   ```typescript
   export const getClinicBySlug = cache(async (slug: string) => {
     return fetchContentType('clinics', {
       params: {
         filters: { slug: { $eq: slug } },
         populate: '*',
       },
     });
   });
   ```

3. **Use Next.js Cache Tags**:
   ```typescript
   const data = await fetch(`https://api.example.com/data`, {
     next: { tags: ['data'] },
   });
   ```

## Implementation Plan

1. **Update ISR Configuration**:
   - Set `revalidate = false` for all dynamic content pages
   - Set `revalidate = 3600` for all list pages

2. **Update Strapi Webhooks**:
   - Update the webhook URL to `https://www.naturalhealingnow.com/api/revalidate`
   - Configure the webhook to send the token in the request body
   - Ensure the webhook includes the necessary content type information

3. **Optimize Data Fetching**:
   - Review and update React Query configuration
   - Implement server-side caching for all data fetching functions
   - Use Next.js cache tags for all fetch requests

## Testing

After implementing these changes, test the following:

1. **On-Demand Revalidation**:
   - Make a change to a content item in Strapi
   - Verify that the webhook is triggered
   - Verify that the revalidation API is called
   - Verify that the page is revalidated

2. **Fallback Revalidation**:
   - Disable webhooks temporarily
   - Make a change to a content item in Strapi
   - Verify that the page is revalidated after the fallback period

3. **Data Fetching**:
   - Monitor the network tab in the browser
   - Verify that there are no duplicate API requests
   - Verify that data is properly cached

## Conclusion

By implementing this ISR strategy, you will:

1. **Improve Performance**: Pages will be served from the CDN most of the time, reducing server load and improving response times.

2. **Reduce Costs**: Fewer server-side renders means lower Vercel compute costs.

3. **Enhance Reliability**: Even if webhooks fail, pages will still be updated eventually thanks to the fallback revalidation period.

4. **Optimize Data Fetching**: Proper caching will reduce the number of API requests, further improving performance and reducing costs.
