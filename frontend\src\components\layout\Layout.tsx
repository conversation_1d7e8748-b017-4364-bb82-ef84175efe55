"use client";

import { ReactNode } from 'react';
import Header from './Header';
import Footer from './Footer';
import GlobalErrorBoundary from '../shared/GlobalErrorBoundary';
import OptimizedScripts from '../shared/OptimizedScripts';
import AnalyticsScripts from '../analytics/AnalyticsScripts';
import ResourceHints from '../shared/ResourceHints';
import EnvCheck from '../shared/EnvCheck';

// Define the type for the actual logoLight prop received from Strapi's API response
// This should match the type expected by the Header component
interface LogoLightMedia {
  id: number;
  name: string;
  alternativeText?: string | null;
  caption?: string | null;
  width?: number;
  height?: number;
  formats?: any; // Can be more specific if needed
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string | null;
  provider: string;
  provider_metadata?: any;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string; // publishedAt might not always be present
}

// Define type for fetched category data (matching structure from RootLayout)
interface FooterCategory {
  id: number;
  attributes: {
    name: string;
    slug: string;
  };
}


interface LayoutProps {
  children: ReactNode;
  siteName: string; // Add siteName prop
  logoLight: LogoLightMedia | null; // Use the corrected type matching Header
  footerCategories: FooterCategory[]; // Add footerCategories prop
}

// Receive the new props
const Layout = ({ children, siteName, logoLight, footerCategories }: LayoutProps) => {
  // Define critical resources for preloading
  const strapiBaseUrl = process.env.NEXT_PUBLIC_API_URL || '';

  return (
    <GlobalErrorBoundary>
      {/* Add resource hints for critical domains and assets */}
      <ResourceHints
        dnsPrefetch={[
          strapiBaseUrl,
          'https://fonts.googleapis.com',
          'https://fonts.gstatic.com',
        ]}
        preconnect={[
          strapiBaseUrl,
          'https://fonts.googleapis.com',
          'https://fonts.gstatic.com',
        ]}
        preload={[
          // Preload critical fonts
          {
            href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
            as: 'style',
          },
          // Don't preload logo - it's already loaded by the Header and Footer components
          // This was causing the duplicate logo loading with incorrect URL format
        ]}
      />

      <div className="flex flex-col min-h-screen">
        {/* Pass the props down to the Header component */}
        <Header siteName={siteName} logoLight={logoLight} />
        <main className="flex-grow">{children}</main>
        {/* Pass siteName, logoLight, and footerCategories props to Footer */}
        <Footer siteName={siteName} logoLight={logoLight} footerCategories={footerCategories} />

        {/* Register analytics scripts */}
        <AnalyticsScripts
          googleAnalyticsId={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}
          enableInDevelopment={false}
        />

        {/* Render optimized scripts */}
        <OptimizedScripts />

        {/* Environment variable check (only visible in development) */}
        <EnvCheck />
      </div>
    </GlobalErrorBoundary>
  );
};

export default Layout;
