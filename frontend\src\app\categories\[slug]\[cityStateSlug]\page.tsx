import { getStrapiContent } from '@/lib/strapi';
import { Metadata } from 'next';
import ClinicCard from '@/components/clinics/ClinicCard'; // Assuming this component exists and is suitable
import { notFound } from 'next/navigation';
// Removed problematic import for ApiClinicClinic

// Define a type for the Strapi entity structure (matching API response from getAll)
// Assuming getAll returns a flattened structure based on previous logs
type StrapiClinicEntity = {
  id: number;
  name: string;
  slug: string;
  isVerified?: boolean;
  description?: string | null;
  logo?: any; // Use 'any' for simplicity or import Media type if needed
  featuredImage?: any;
  address?: { // Address component might still be nested within the flat structure
    city?: string;
    stateProvince?: string;
    // Add other address fields if needed by the card later
  } | null;
  contactInfo?: {
    phoneNumber?: string;
    websiteUrl?: string;
  } | null;
  // Add other attributes if needed by the card
  [key: string]: any; // Allow other attributes
};


// Enable ISR with fallback: 'blocking' behavior implicitly by setting revalidate
// dynamicParams = true allows generating pages on demand if not pre-built during build time.
export const dynamicParams = true;
export const revalidate = 86400; // Revalidate ISR pages every 24 hours

// Helper function to generate city-state slug
function generateCityStateSlug(city: string, state: string): string {
  if (!city || !state) return '';
  // Ensure city names like "New York City" become "new-york-city"
  const citySlugPart = city.toLowerCase().replace(/\s+/g, '-');
  const stateSlugPart = state.toLowerCase();
  return `${citySlugPart}-${stateSlugPart}`;
}

// Helper function to parse city-state slug
function parseCityStateSlug(cityStateSlug: string): { city: string; state: string } | null {
  const parts = cityStateSlug.split('-');
  if (parts.length < 2) return null;

  const state = parts[parts.length - 1].toUpperCase();
  const city = parts.slice(0, -1).join(' '); // Reconstruct city name, replacing hyphens with spaces

  // Basic validation: check if state looks like a 2-letter code
  if (state.length !== 2 || !/^[A-Z]+$/.test(state)) {
      console.warn(`Parsed state "${state}" from slug "${cityStateSlug}" does not look like a valid state code.`);
      // Depending on strictness, you might return null here
  }

  // Capitalize first letter of each word in the city name
  const formattedCity = city
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return { city: formattedCity, state };
}


export async function generateStaticParams() {
  const paramsSet = new Set<string>(); // Use a Set to store unique 'categorySlug/cityStateSlug' strings
  console.log("Starting generateStaticParams for category/city pages...");

  try {
    // 1. Fetch all categories (use a large page size to get most/all)
    // Strapi defaults might limit this, consider fetching all pages if necessary
    const categoriesResponse = await getStrapiContent.categories.getAll({
        pagination: { pageSize: 1000 }, // Adjust if more categories exist
        fields: ['slug'], // Only need the slug
        populate: {} // Explicitly request no population to ensure fields works correctly
    });
    const categories = categoriesResponse?.data || [];
    console.log(`Fetched ${categories.length} categories.`);

    // 2. For each category, fetch its clinics' locations
    for (const category of categories) {
      // Correct access: getAll seems to return slug directly on the object
      const categorySlug = category.slug;
      if (!categorySlug) continue;

      // Fetch clinics associated with this category, only getting address fields
      // Using a large page size. If > 10k clinics per category, pagination loop is needed.
      const clinicsResponse = await getStrapiContent.clinics.getAll({
        filters: { categories: { slug: { $eq: categorySlug } } },
        fields: [], // Don't fetch top-level clinic fields
        populate: {
          address: { // Populate only the address component
            fields: ['city', 'stateProvince'] // Select only city and state
          }
        },
        pagination: { pageSize: 10000 } // Fetch up to 10k clinics per category
      });

      const clinics = clinicsResponse?.data || [];

      // 3. Extract unique city/state combinations and generate slugs
      for (const clinic of clinics) {
        const address = clinic.attributes?.address;
        const city = address?.city;
        const state = address?.stateProvince;

        if (city && state) {
          const cityStateSlug = generateCityStateSlug(city, state);
          if (cityStateSlug) {
            // Store as 'categorySlug/cityStateSlug' to ensure uniqueness of the pair
            paramsSet.add(`${categorySlug}/${cityStateSlug}`);
          }
        }
      }
    }

    // 4. Convert Set back to the required format for Next.js
    const params = Array.from(paramsSet).map(slugPair => {
      const [slugValue, cityStateSlug] = slugPair.split('/'); // Get the actual category slug value
      return { slug: slugValue, cityStateSlug }; // Return object with key 'slug' matching the file path segment [slug]
    });

    console.log(`Generated ${params.length} unique static params for category/city pages.`);
    // console.log("Sample params:", params.slice(0, 10)); // Optional: Log a few samples for debugging

    return params;

  } catch (error) {
    console.error("Error during generateStaticParams for category/city pages:", error);
    return []; // Return empty array on error to avoid build failure
  }
}

// Generate dynamic metadata for SEO
export async function generateMetadata({ params }: CategoryCityPageProps): Promise<Metadata> {
  const { slug, cityStateSlug } = params; // Use 'slug' here
  const locationInfo = parseCityStateSlug(cityStateSlug);

  // Define the site URL from environment variable with proper fallback to your actual domain
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

  // Log the URL being used for debugging
  if (process.env.NODE_ENV === 'production') {
    console.log(`Using site URL for canonical URLs: ${siteUrl}`);
  }

  if (!locationInfo) {
    return {
      title: 'Invalid Location',
      description: 'The location specified in the URL is invalid.',
      // No canonical for invalid pages
    };
  }

  const { city, state } = locationInfo;

  // Fetch category details to get its name
  const categoryResponse = await getStrapiContent.categories.getBySlug(slug); // Use 'slug' here
  // Correctly access the name directly from the category object in the data array
  const category = categoryResponse?.data?.[0];
  const categoryName = category?.name; // Access name directly

  // Construct the canonical URL path
  const canonicalPath = `/categories/${slug}/${cityStateSlug}`;
  const canonicalUrl = siteUrl ? `${siteUrl}${canonicalPath}` : canonicalPath; // Fallback to relative path if base URL is missing

  // Handle case where category or name is not found
  if (!categoryName) {
    console.warn(`Category name not found for slug: ${slug}. Returning not found metadata.`);
    // Option: Return metadata indicating not found, consistent with page component logic
    return {
        title: `Category Not Found | Natural Healing Now`,
        description: `The requested category page for "${slug}" in ${city}, ${state} could not be found.`,
        // No canonical for not found pages
    };
    // Alternative: Use a generic title/description if preferred
    // title = `Clinics in ${city}, ${state} | Natural Healing Now`;
    // description = `Find clinics and practitioners in ${city}, ${state}. Explore options on Natural Healing Now.`;
  }

  // Construct title and description using the confirmed categoryName
  const title = `Best ${categoryName} in ${city}, ${state} - Find Top Clinics | Natural Healing Now`;
  const description = `Looking for the best ${categoryName} in ${city}, ${state}? Discover top-rated clinics and practitioners near you. Book appointments easily on Natural Healing Now.`;

  // TODO: Potentially fetch SEO component data from the category if it exists and merge
  // const categorySeo = categoryResponse?.data?.[0]?.attributes?.seo;

  return {
    title: title,
    description: description,
    alternates: {
      canonical: canonicalUrl,
    },
    // Add cache tags for targeted revalidation
    other: {
      'cache-tags': [
        'strapi-clinics',
        'strapi-categories',
        `strapi-category-${slug}`,
        `strapi-category-${slug}-clinics`,
        `strapi-category-${slug}-location-${cityStateSlug}`
      ]
    },
    // openGraph: { // Example Open Graph data
    //   title: title,
    //   description: description,
    //   url: canonicalUrl, // Use the full canonical URL
    //   // images: [ ... ] // Add images if available
    // },
    // twitter: { // Example Twitter card data
    //   card: 'summary_large_image',
    //   title: title,
    //   description: description,
    //   // images: [ ... ]
    // },
  };
}


// The Page Component
interface CategoryCityPageProps {
  params: {
    slug: string; // Use 'slug' here
    cityStateSlug: string;
  };
}

export default async function CategoryCityPage({ params }: CategoryCityPageProps) {
  const { slug, cityStateSlug } = params; // Use 'slug' here

  // 1. Parse cityStateSlug back to city and state
  const locationInfo = parseCityStateSlug(cityStateSlug);
  if (!locationInfo) {
    console.error(`Failed to parse cityStateSlug: ${cityStateSlug}`);
    notFound(); // Show 404 if slug is invalid
  }
  const { city, state } = locationInfo;

  // 2. Fetch category details (needed for display name)
  console.log(`Fetching category details for slug: ${slug}`);
  const categoryResponse = await getStrapiContent.categories.getBySlug(slug); // Use 'slug' here
  console.log('Category Response from Strapi:', JSON.stringify(categoryResponse, null, 2)); // Log the full response
  // Access the first item in the data array directly
  const category = categoryResponse?.data?.[0];

  // Check if category exists and has a name property
  if (!category || typeof category.name === 'undefined') {
    console.error(`Category not found or missing name for slug: ${slug}`); // Use 'slug' here
    notFound(); // Show 404 if category doesn't exist or lacks name
  }
  // Access name directly from the category object
  const categoryName = category.name;

  // 3. Fetch filtered clinics using getAll with combined filters
  let clinics: any[] = [];

  try {
    console.log(`Fetching clinics for category ${slug} in ${city}, ${state}`);

    // Define cache tags for this request
    const cacheTags = [
      'strapi-clinics',
      'strapi-categories',
      `strapi-category-${slug}`,
      `strapi-category-${slug}-clinics`,
      `strapi-category-${slug}-location-${cityStateSlug}`
    ];

    // Use the getAll function with combined filters for category and location
    const clinicsResponse = await getStrapiContent.clinics.getAll({
      filters: {
        $and: [
          { categories: { slug: { $eq: slug } } },
          {
            address: {
              city: { $eq: city },
              stateProvince: { $eq: state }
            }
          }
        ]
      },
      populate: {
        logo: true,
        address: true,
        contactInfo: true,
        categories: true
      },
      pagination: { pageSize: 24 }, // Show more results on location pages
      next: {
        tags: cacheTags // Add cache tags for targeted revalidation
      }
    });

    clinics = clinicsResponse?.data || [];
    console.log(`Found ${clinics.length} clinics for ${categoryName} in ${city}, ${state}`);

    // Get pagination info from response if needed
    const pagination = clinicsResponse?.meta?.pagination;
    const totalItems = pagination?.total || 0;
    const totalPages = pagination?.pageCount || 1;
    const currentPage = pagination?.page || 1;

    console.log(`Pagination info: ${totalItems} total items, page ${currentPage} of ${totalPages}`);
  } catch (error) {
    console.error(`Error fetching clinics for category ${slug} in ${city}, ${state}:`, error);
    // clinics will remain an empty array
  }


  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-2">
        {categoryName} in {city}, {state}
      </h1>
      <p className="text-lg text-gray-600 mb-6">
        Discover the best {categoryName} in {city}, {state}
      </p>

      {clinics.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Transform data structure within map to match ClinicCardProps */}
          {clinics.map((clinicEntity: StrapiClinicEntity) => {
            // Create the flat structure expected by ClinicCard, accessing props directly
            const clinicForCard = {
              id: clinicEntity.id.toString(), // Convert id to string if ClinicCard expects string
              name: clinicEntity.name, // Access directly
              slug: clinicEntity.slug, // Access directly
              isVerified: clinicEntity.isVerified, // Access directly
              description: clinicEntity.description, // Access directly
              // Handle potentially missing address or its fields gracefully
              address: {
                city: clinicEntity.address?.city ?? 'N/A', // Access directly, provide fallback
                stateProvince: clinicEntity.address?.stateProvince ?? 'N/A', // Access directly, provide fallback
              },
              // Extract logo URL if needed by ClinicCard (assuming structure)
              // Note: Media objects might still be nested under data/attributes even in flat responses
              logo: clinicEntity.logo?.data?.attributes?.url || null,
              // Extract contact info
              contactInfo: clinicEntity.contactInfo ? {
                 phoneNumber: clinicEntity.contactInfo.phoneNumber, // Access directly
                 websiteUrl: clinicEntity.contactInfo.websiteUrl, // Access directly
              } : null,
              // Add other fields directly from clinicEntity as needed by ClinicCard
            };
             // Ensure the address object is always present, even if empty
             if (!clinicForCard.address) {
               clinicForCard.address = { city: 'N/A', stateProvince: 'N/A' };
             }

            return <ClinicCard key={clinicForCard.id} clinic={clinicForCard} />;
          })}
        </div>
      ) : (
        <p className="text-center text-gray-500 mt-10">
          No clinics found for {categoryName} in {city}, {state} at this time.
        </p>
      )}

      {/* TODO: Add Pagination component if implementing pagination */}
    </div>
  );
}
