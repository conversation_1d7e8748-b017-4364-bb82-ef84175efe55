import Link from 'next/link';
import { FiArrowLeft, FiUser } from 'react-icons/fi';
import BlogPostCard from '@/components/blog/BlogPostCard';
// Import both fetchAPI and getStrapiContent
import { fetchAPI, getStrapiContent } from '@/lib/strapi';
import { sanitizeUrl } from '@/lib/mediaUtils';
import Image from 'next/image';
// import Pagination from '@/components/shared/Pagination'; // No longer directly used here
import AuthorPostsList from '@/components/blog/AuthorPostsList'; // Import the new component
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Suspense } from 'react'; // Import Suspense

// Enable ISR with time-based revalidation (12 hours)
export const revalidate = 43200; // 12 hours in seconds

// Force static rendering for this page
export const dynamic = 'force-static';

// Allow dynamic params to be generated on-demand (still needed for generateStaticParams)
export const dynamicParams = true;

// Helper function to get full Strapi URL
const getStrapiMedia = (url: string | null | undefined) => {
  if (!url) return null;
  // Check if URL is already absolute
  if (url.startsWith('http') || url.startsWith('//')) {
    return url;
  }
  // Otherwise, prepend Strapi URL
  return `${process.env.NEXT_PUBLIC_STRAPI_API_URL || ''}${url}`;
};

// Define interface for component props
interface AuthorPageProps {
  params: { slug: string };
  searchParams?: { page?: string };
}

// Define pagination parameters interface
interface PaginationParams {
  page?: number;
  pageSize?: number;
}

// Define the props type for generateMetadata
type MetadataProps = {
  params: { slug: string };
  searchParams?: { page?: string };
};

// Generate Metadata function
export async function generateMetadata(
  { params, searchParams = {} }: MetadataProps
): Promise<Metadata> {
  // Access slug directly from params
  const slug = params.slug;

  // Define the site URL from environment variable with proper fallback to your actual domain
  const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

  // Log the URL being used for debugging
  if (process.env.NODE_ENV === 'production') {
    console.log(`Using site URL for canonical URLs: ${SITE_URL}`);
  }

  try {
    // Get the current page from search params
    const currentPage = Number(searchParams.page) || 1;
    const pageSize = 12; // Show 12 posts per page

    const response = await getAuthorData(slug, {
      page: currentPage,
      pageSize: pageSize
    });

    if (!response || !response.data || response.data.length === 0) {
      return {
        title: 'Author Not Found | Natural Healing Now',
        description: 'The requested author could not be found.'
      };
    }

    const authorData = response.data[0];

    // Fallback values
    const pageNumber = Number(searchParams.page) || 1;
    const pageText = pageNumber > 1 ? ` - Page ${pageNumber}` : '';
    const defaultTitle = `${authorData.name}${pageText} - Author | Natural Healing Now`;
    const defaultDescription = authorData.bio ||
      `Explore articles by ${authorData.name} on Natural Healing Now.`;

    // Canonical URL
    const pageParam = pageNumber > 1 ? `?page=${pageNumber}` : '';
    const canonicalPath = `/blog/authors/${slug}${pageParam}`;
    const canonicalUrl = SITE_URL ? `${SITE_URL}${canonicalPath}` : canonicalPath;

    // Construct metadata
    const metadata: Metadata = {
      title: defaultTitle,
      description: defaultDescription,
      robots: 'index, follow',
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title: defaultTitle,
        description: defaultDescription,
        url: canonicalUrl,
        siteName: 'Natural Healing Now',
        type: 'profile',
      },
      twitter: {
        card: 'summary',
        title: defaultTitle,
        description: defaultDescription,
      },
    };

    return metadata;
  } catch (error) {
    console.error(`Error generating metadata for author ${slug}:`, error);
    return {
      title: 'Author | Natural Healing Now',
      description: 'Explore our authors and their articles on natural healing.'
    };
  }
}

// Generate static paths for all authors at build time
export async function generateStaticParams() {
  try {
    // In Next.js 15, we need to explicitly opt-in to caching
    const response = await getStrapiContent.blog.getAuthors({ // Fetch only slugs
      cache: 'force-cache', // Explicitly ensure caching for generateStaticParams
      next: {
        revalidate: 43200, // Revalidate slugs every 12 hours
        tags: ['strapi-authors-slugs'] // Tag for revalidation
      }
    });

    if (response && response.data && Array.isArray(response.data)) {
      console.log(`Pre-rendering ${response.data.length} author detail pages`);

      return response.data
        .filter((item: any): item is { slug: string } =>
          item !== null && typeof item.slug === 'string')
        .map((item: { slug: string }) => ({
          slug: item.slug,
        }));
    }

    console.error('Failed to fetch author slugs for generateStaticParams');
    return []; // Return empty array on error to prevent build failure
  } catch (error) {
    console.error('Error fetching author slugs for generateStaticParams:', error);
    return []; // Return empty array on error to prevent build failure
  }
}

// Separate async function for data fetching
async function getAuthorData(slug: string, paginationParams: PaginationParams = {}) {
  // Default pagination values
  const page = paginationParams.page || 1;
  const pageSize = paginationParams.pageSize || 12;

  console.log(`Fetching author data for slug: "${slug}" (page ${page}, pageSize ${pageSize})`);

  try {
    // Step 1: First, get the author data without posts to get the author ID
    const authorResponse = await fetchAPI('/authors', {
      params: {
        filters: { slug: { $eq: slug } },
        populate: {
          profilePicture: true // Populate author's profile picture
        }
      },
      cache: 'force-cache', // Explicitly opt-in to caching for ISR
      next: {
        revalidate: 43200, // 12 hours
        tags: ['strapi-authors', 'strapi-blog-authors', `strapi-author-${slug}`]
      }
    });

    // Check if author exists
    if (!authorResponse?.data || !Array.isArray(authorResponse.data) || authorResponse.data.length === 0) {
      console.error(`No author found with slug: ${slug}`);
      return { data: [], meta: { pagination: { page, pageSize, pageCount: 0, total: 0 } } };
    }

    // Get the author ID
    const authorId = authorResponse.data[0].id;
    console.log(`Found author with ID: ${authorId}`);

    // Step 2: Now fetch the blog posts for this author with pagination
    const postsResponse = await fetchAPI(`/blog-posts`, {
      params: {
        filters: {
          author_blogs: {
            id: { $eq: authorId }
          }
        },
        pagination: {
          page,
          pageSize
        },
        sort: ['publishDate:desc'], // Show newest posts first
        populate: {
          featuredImage: true,
          author_blogs: {
            populate: {
              profilePicture: true
            }
          }
        }
      },
      cache: 'force-cache', // Explicitly opt-in to caching for ISR
      next: {
        revalidate: 43200, // 12 hours
        tags: [
          'strapi-blog-posts',
          'strapi-authors',
          `strapi-author-${slug}`,
          `strapi-author-${slug}-page-${page}`
        ]
      }
    });

    console.log(`Found ${postsResponse?.data?.length || 0} posts for author ID ${authorId}`);

    // Step 3: Combine the responses
    const combinedResponse = {
      data: [
        {
          ...authorResponse.data[0],
          blog_posts: postsResponse.data || []
        }
      ],
      meta: postsResponse.meta || { pagination: { page, pageSize, pageCount: 0, total: 0 } }
    };

    // Log the combined response structure
    console.log(`Combined author response for slug ${slug}:`, JSON.stringify({
      hasData: !!combinedResponse?.data,
      isArray: Array.isArray(combinedResponse?.data),
      length: Array.isArray(combinedResponse?.data) ? combinedResponse.data.length : 'not an array',
      hasPosts: Array.isArray(combinedResponse?.data) && combinedResponse.data.length > 0 &&
               Array.isArray(combinedResponse.data[0].blog_posts) ?
               combinedResponse.data[0].blog_posts.length : 0,
      hasPagination: !!combinedResponse?.meta?.pagination,
      paginationTotal: combinedResponse?.meta?.pagination?.total || 0
    }));

    return combinedResponse;
  } catch (error) {
    console.error(`Error fetching author data for slug ${slug}:`, error);
    throw error;
  }
}

// Use the defined interface for props
export default async function AuthorPage({ params, searchParams = {} }: AuthorPageProps) {
  // Await the params object before accessing its properties (Next.js 15+)
  const awaitedParams = await params;
  // For static rendering, we fetch the first page of posts.
  // Pagination will be handled client-side by AuthorPostsList and AuthorPagePagination.
  const currentPage = 1; // Default to page 1 for initial static render

  // Call the separate data fetching function using the awaited slug and pagination for the first page
  const authorResponse = await getAuthorData(awaitedParams.slug, {
    page: currentPage, // Fetch page 1
    pageSize: 12
  });

  // If author not found (or API error), show a not found page
  if (!authorResponse || !authorResponse.data || authorResponse.data.length === 0) {
    console.error(`Author not found with slug: ${awaitedParams.slug}`);
    return notFound();
  }

  const authorEntry = authorResponse.data[0];

  // Add safety check for the entry itself
  if (!authorEntry) {
    console.error(`Author entry missing in response for slug: ${awaitedParams.slug}`);
    return notFound();
  }

  // Access fields directly from authorEntry (Strapi 5 flattened)
  const authorAttributes = authorEntry; // Use the entry directly as attributes are flattened

  // Extract blog posts data - with our new approach, blog_posts is already an array
  const postsData = Array.isArray(authorAttributes.blog_posts) ? authorAttributes.blog_posts : [];
  // Access media field directly, get URL from it - handle both object and string cases
  let authorProfilePicUrl = null;

  // Log the profile picture data for debugging
  console.log("Author profile picture data:", JSON.stringify(authorAttributes.profilePicture || "No profile picture"));

  // Handle different possible structures for Strapi v5
  if (authorAttributes.profilePicture) {
    // For Strapi v5, the structure might be different
    if (typeof authorAttributes.profilePicture === 'string') {
      // If it's a direct URL string
      authorProfilePicUrl = getStrapiMedia(authorAttributes.profilePicture);
    } else if (authorAttributes.profilePicture.url) {
      // If it has a direct URL property
      authorProfilePicUrl = getStrapiMedia(authorAttributes.profilePicture.url);
    } else if (authorAttributes.profilePicture.data?.attributes?.url) {
      // Strapi v5 nested structure
      authorProfilePicUrl = getStrapiMedia(authorAttributes.profilePicture.data.attributes.url);
    } else {
      // Pass the whole object to getStrapiMedia
      authorProfilePicUrl = getStrapiMedia(authorAttributes.profilePicture);
    }
  }

  // If we still don't have a URL and have an IMAGE_HOSTNAME, try to construct one
  if (!authorProfilePicUrl && process.env.IMAGE_HOSTNAME) {
    const imageHostname = process.env.IMAGE_HOSTNAME;
    // Try to extract a filename if possible
    if (authorAttributes.profilePicture?.data?.attributes?.name) {
      const filename = authorAttributes.profilePicture.data.attributes.name;
      authorProfilePicUrl = `${imageHostname}/${filename}`;
    }
  }

  return (
    <>
      {/* Breadcrumb */}
      <div className="bg-gray-100 py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-emerald-600">Home</Link>
            <span className="mx-2">/</span>
            <Link href="/blog" className="hover:text-emerald-600">Blog</Link>
            <span className="mx-2">/</span>
            <Link href="/blog/authors" className="hover:text-emerald-600">Authors</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-800">{authorAttributes.name}</span>
          </div>
        </div>
      </div>

      {/* Author Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row md:items-center gap-6">
            {/* Author Image */}
            <div className={`relative h-24 w-24 rounded-full overflow-hidden ${!authorProfilePicUrl ? 'bg-emerald-500' : ''} flex items-center justify-center flex-shrink-0`}>
              {authorProfilePicUrl ? (
                <Image
                  src={authorProfilePicUrl}
                  alt={authorAttributes.name || 'Author profile picture'}
                  width={96}
                  height={96}
                  className="object-cover rounded-full"
                  sizes="96px"
                />
              ) : (
                <FiUser className="text-white text-4xl" /> // Placeholder Icon
              )}
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold mb-2">{authorAttributes.name}</h1>
              <p className="text-emerald-100">{authorAttributes.qualifications}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Author Bio & Contact */}
      <div className="bg-white py-12 border-b">
        <div className="container mx-auto px-4">
          {/* Use Flexbox for side-by-side layout on medium screens and up */}
          <div className="flex flex-col md:flex-row gap-8 lg:gap-12">
            {/* About Author Section (Main Content) */}
            <div className="md:w-2/3">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">About {authorAttributes.name}</h2>
              {/* TODO: Add Markdown rendering if bio supports it */}
              <div className="prose max-w-none text-gray-600">
                {/* Render bio using a div if it might contain HTML or needs styling */}
                {/* Or use a simple <p> if it's plain text */}
                <p>{authorAttributes.bio || "No bio available."}</p>
              </div>
            </div>

            {/* Contact Information Section (Sidebar) */}
            <div className="md:w-1/3 flex-shrink-0 bg-[#f9f9f9] p-6 rounded-[10px]"> {/* Changed bg-gray-100 to bg-[#f9f9f9], set border radius to 10px */}
              <h3 className="text-xl font-bold text-gray-800 mb-4">Contact Information</h3>
              <div className="space-y-3 text-gray-600">
                {/* Example: Email (Check if authorAttributes.email exists) */}
                {authorAttributes.email && (
                  <p>
                    <strong>Email:</strong> <a href={`mailto:${authorAttributes.email}`} className="text-emerald-600 hover:underline">{authorAttributes.email}</a>
                  </p>
                )}
                {/* Example: Website (Check if authorAttributes.website exists) */}
                {authorAttributes.website && (
                  <p>
                    <strong>Website:</strong> <a href={authorAttributes.website} target="_blank" rel="noopener noreferrer" className="text-emerald-600 hover:underline">{authorAttributes.website}</a>
                  </p>
                )}
                {/* Add other contact fields like LinkedIn, Twitter etc. here */}
                {/* Placeholder if no contact info is available */}
                {!authorAttributes.email && !authorAttributes.website && (
                    <p>No contact information provided.</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Author's Posts - Now handled by AuthorPostsList component */}
      <AuthorPostsList
        author={authorAttributes}
        initialPosts={postsData.map((postItem: any) => {
          const postAttributes = postItem;
          const featuredImageUrl = sanitizeUrl(postAttributes.featuredImage);
          console.log(`[Author Page] Post "${postAttributes.title}": Generated Image URL:`, featuredImageUrl);
          return {
            id: postItem.id.toString(),
            title: postAttributes.title,
            slug: postAttributes.slug,
            excerpt: postAttributes.excerpt,
            featured_image: featuredImageUrl,
            publish_date: postAttributes.publishDate || postAttributes.publishedAt || new Date().toISOString(),
            author: {
              name: authorAttributes.name,
              slug: authorAttributes.slug,
              profile_picture: authorProfilePicUrl
            }
          };
        })}
        totalPages={authorResponse.meta?.pagination?.pageCount || 1}
        currentPage={currentPage} // Pass the initial page (1)
        authorProfilePicUrl={authorProfilePicUrl}
      />
    </>
  );
}
