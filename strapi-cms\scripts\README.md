# Strapi CMS Scripts

This directory contains utility scripts for the Strapi CMS.

## Import Clinics Script

The `import-clinics.js` script imports clinic data from a CSV file into the Strapi CMS.

### Features

- Imports clinics from CSV file into Strapi
- Checks if a clinic with the same slug already exists
- If a clinic exists, only updates fields that are empty in Strapi
- If a clinic doesn't exist, creates a new entry
- Handles component structures (address, contactInfo, location)
- Processes multi-select fields (appointmentOptions, paymentMethod)

### Prerequisites

1. Strapi running
2. CSV file in the correct format

### Setup

1. Install dependencies:
   ```
   cd scripts
   npm install
   ```

### Usage

There are two ways to run the import script:

#### Method 1: Using Strapi Console

1. Start the Strapi console:
   ```
   npm run strapi console
   ```

2. Run the import command:
   ```javascript
   await require('./scripts/import-command.js')(strapi)
   ```

#### Method 2: Using Strapi Command

1. Create a custom Strapi command (already done):
   ```javascript
   // In strapi-cms/src/commands/import-clinics.js
   module.exports = {
     command: 'import-clinics',
     description: 'Import clinics from CSV file',
     run: async ({ strapi }) => {
       await require('../../scripts/import-clinics.js')(strapi);
     }
   };
   ```

2. Run the command:
   ```
   npm run strapi import-clinics
   ```

### CSV File Format

The script expects a CSV file with the following columns:

- `id` - Optional ID (not used for import)
- `name` - Clinic name
- `slug` - Unique slug for the clinic
- `description` - Description of the clinic
- `isActive` - Boolean indicating if the clinic is active
- `isFeatured` - Boolean indicating if the clinic is featured
- `isVerified` - Boolean indicating if the clinic is verified
- `paid` - Boolean indicating if the clinic is paid
- `appointmentOptions` - Comma-separated list of appointment options
- `paymentMethod` - Comma-separated list of payment methods
- `phoneNumber` - Phone number for contact info
- `emailAddress` - Email address for contact info
- `websiteUrl` - Website URL for contact info
- `streetAddress1` - Street address line 1
- `streetAddress2` - Street address line 2
- `city` - City
- `stateProvince` - State/Province code (e.g., NY, CA)
- `country` - Country (defaults to USA)
- `postalCode` - Postal code
- `googlePlaceId` - Google Place ID
- `latitude` - Latitude coordinate
- `longitude` - Longitude coordinate
- `videoEmbedUrl` - URL for video embed
