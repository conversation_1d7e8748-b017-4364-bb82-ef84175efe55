'use server';

import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { unauthorized } from 'next/navigation';

/**
 * Verify the user's session
 * This is a helper function to check if the user is authenticated
 */
async function verifySession() {
  // Get the JWT token from cookies
  const cookieStore = cookies();
  const token = cookieStore.get('jwt')?.value;
  
  if (!token) {
    return null;
  }
  
  try {
    // In a real implementation, you would verify the token
    // This is a placeholder for your actual verification logic
    
    // For demonstration purposes only:
    if (token.length > 10) {
      return { userId: 'example-user-id', role: 'user' };
    }
    
    return null;
  } catch (error) {
    console.error('Session verification error:', error);
    return null;
  }
}

/**
 * Secure server action example
 * This demonstrates how to properly secure a server action in Next.js
 */
export async function secureAction(formData: FormData) {
  try {
    // Verify the user's session
    const session = await verifySession();
    
    // If no session exists, return unauthorized
    if (!session) {
      unauthorized();
    }
    
    // Check if the user has the required role (optional)
    // if (session.role !== 'admin') {
    //   return { error: 'Insufficient permissions' };
    // }
    
    // Process the form data
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    
    // Validate the input data
    if (!name || !email) {
      return { error: 'Name and email are required' };
    }
    
    // Perform the action (e.g., save to database)
    // This is a placeholder for your actual action logic
    
    // Return success response
    return { 
      success: true,
      message: 'Action completed successfully',
      data: { name, email }
    };
  } catch (error) {
    // Log the error but don't expose details to the client
    console.error('Error in secure server action:', error);
    
    // Return a generic error message
    return { 
      success: false,
      error: 'An error occurred processing your request'
    };
  }
}
