// Defines the basic structure of a Strapi entity in a flattened response
export interface StrapiBaseEntity {
  id: number;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string | null;
  locale?: string;
  // documentId is a v5 concept, but often the primary `id` is used.
  // Let's assume 'id' is the primary numeric ID.
}

// Represents a media item (image, video, etc.)
export interface StrapiMedia extends StrapiBaseEntity {
  name: string;
  alternativeText?: string | null;
  caption?: string | null;
  width?: number;
  height?: number;
  formats?: Record<string, StrapiMediaFormat>; // e.g., thumbnail, small, medium, large
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string | null;
  provider: string;
  provider_metadata?: any;
}

export interface StrapiMediaFormat {
  name: string;
  hash: string;
  ext: string;
  mime: string;
  path?: string | null;
  width: number;
  height: number;
  size: number;
  url: string;
}

// Wrapper for a single media item relation
export interface StrapiMediaRelation {
  data: StrapiMedia | null;
}

// Wrapper for multiple media items relation
export interface StrapiMultipleMediaRelation {
  data: StrapiMedia[];
}


// Generic type for a Strapi relation to a single item
export interface StrapiRelation<T> {
  data: (T & StrapiBaseEntity) | null;
}

// Generic type for a Strapi relation to multiple items
export interface StrapiMultipleRelation<T> {
  data: (T & StrapiBaseEntity)[];
}

// SEO Component
export interface SEOComponent {
  id: number;
  metaTitle: string;
  metaDescription: string;
  keywords?: string;
  metaRobots?: string;
  structuredData?: any;
  metaViewport?: string;
  canonicalURL?: string;
  metaImage?: StrapiMediaRelation;
  // add other SEO fields as needed
}

// Address Component (example)
export interface AddressComponent {
  id: number;
  street: string;
  city: string;
  state?: string;
  zipCode: string;
  country: string;
  // add other address fields
}

// ContactInfo Component (example)
export interface ContactInfoComponent {
  id: number;
  phone?: string;
  email?: string;
  website?: string;
  // add other contact fields
}

// Location Component (example - for map coordinates)
export interface LocationComponent {
    id: number;
    latitude?: number;
    longitude?: number;
    // any other location specific fields
}

// Define your specific content types below

export interface Category extends StrapiBaseEntity {
  name: string;
  slug: string;
  description?: string;
  // other category fields
  clinics?: StrapiMultipleRelation<Clinic>; // Example relation
}

export interface Practitioner extends StrapiBaseEntity {
  name: string;
  slug: string;
  title?: string;
  bio?: string;
  profilePicture?: StrapiMediaRelation;
  specialties?: StrapiMultipleRelation<Specialty>; // Example relation
  // other practitioner fields
}

export interface Specialty extends StrapiBaseEntity {
  name: string;
  slug: string;
  description?: string;
  // other specialty fields
}

export interface Condition extends StrapiBaseEntity {
  name: string;
  slug: string;
  description?: string;
  // other condition fields
}

export interface Service extends StrapiBaseEntity {
  name: string;
  slug: string;
  description?: string;
  // other service fields
}

export interface AppointmentOption extends StrapiBaseEntity {
    name: string;
    description?: string;
    // other appointment option fields
}

export interface PaymentMethod extends StrapiBaseEntity {
    name: string;
    description?: string;
    // other payment method fields
}


export interface Clinic extends StrapiBaseEntity {
  name: string;
  slug: string;
  description?: string; // Assuming rich text or markdown
  rating?: number;
  isFeatured?: boolean;

  logo?: StrapiMediaRelation;
  address?: AddressComponent; // Component
  contactInfo?: ContactInfoComponent; // Component
  location?: LocationComponent; // Component for map coordinates

  services?: StrapiMultipleRelation<Service>;
  specialties?: StrapiMultipleRelation<Specialty>;
  conditions?: StrapiMultipleRelation<Condition>;
  categories?: StrapiMultipleRelation<Category>;
  practitioners?: StrapiMultipleRelation<Practitioner>;
  appointment_options?: StrapiMultipleRelation<AppointmentOption>;
  payment_methods?: StrapiMultipleRelation<PaymentMethod>;

  seo?: SEOComponent; // Component

  // Add any other fields specific to your Clinic content type
  // openingHours, etc.
}

// General type for Strapi API settings or global data
export interface GlobalSettings extends StrapiBaseEntity {
  siteName: string;
  siteDescription?: string;
  logo?: StrapiMediaRelation;
  favicon?: StrapiMediaRelation;
  // other global settings
}
