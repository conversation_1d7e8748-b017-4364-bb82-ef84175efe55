{"kind": "collectionType", "collectionName": "appointment_options", "info": {"singularName": "appointment-option", "pluralName": "appointment-options", "displayName": "Appointment Option", "description": "Options for how appointments can be conducted"}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "clinics": {"type": "relation", "relation": "manyToMany", "target": "api::clinic.clinic", "mappedBy": "appointment_options"}, "slug": {"type": "uid", "targetField": "name"}}}