{"/api/analytics/post-view/route": "/api/analytics/post-view", "/api/clinics/[slug]/route": "/api/clinics/[slug]", "/api/clinics/route": "/api/clinics", "/api/protected-data/route": "/api/protected-data", "/api/protected-example/route": "/api/protected-example", "/api/revalidate/blog/route": "/api/revalidate/blog", "/api/revalidate/categories/route": "/api/revalidate/categories", "/api/revalidate/clinics/route": "/api/revalidate/clinics", "/api/revalidate/conditions/route": "/api/revalidate/conditions", "/api/revalidate/route": "/api/revalidate", "/api/revalidate/practitioners/route": "/api/revalidate/practitioners", "/api/secure-example/route": "/api/secure-example", "/api/strapi-proxy/route": "/api/strapi-proxy", "/api/test/route": "/api/test", "/sitemap-blog.xml/route": "/sitemap-blog.xml", "/api/revalidate/specialities/route": "/api/revalidate/specialities", "/sitemap-clinics.xml/route": "/sitemap-clinics.xml", "/sitemap-index.xml/route": "/sitemap-index.xml", "/sitemap-practitioners.xml/route": "/sitemap-practitioners.xml", "/sitemaps.xml/route": "/sitemaps.xml", "/clinics/sitemap.xml/route": "/clinics/sitemap.xml", "/blog/sitemap.xml/route": "/blog/sitemap.xml", "/favicon.ico/route": "/favicon.ico", "/robots.txt/route": "/robots.txt", "/sitemap.xml/route": "/sitemap.xml", "/practitioners/sitemap.xml/route": "/practitioners/sitemap.xml", "/test.xml/route": "/test.xml", "/account/page": "/account", "/about-us/page": "/about-us", "/affiliate-disclosure/page": "/affiliate-disclosure", "/_not-found/page": "/_not-found", "/api/example-server-component/page": "/api/example-server-component", "/blog/categories/page": "/blog/categories", "/blog/tags/[slug]/page": "/blog/tags/[slug]", "/blog/tags/page": "/blog/tags", "/optimized-client-example/page": "/optimized-client-example", "/optimized-example/page": "/optimized-example", "/page": "/", "/privacy/page": "/privacy", "/blog/authors/page": "/blog/authors", "/blog/[slug]/page": "/blog/[slug]", "/blog/page": "/blog", "/blog/categories/[slug]/page": "/blog/categories/[slug]", "/categories/[slug]/[cityStateSlug]/page": "/categories/[slug]/[cityStateSlug]", "/categories/page": "/categories", "/categories/[slug]/page": "/categories/[slug]", "/blog/authors/[slug]/page": "/blog/authors/[slug]", "/clinics/page": "/clinics", "/clinics/[slug]/page": "/clinics/[slug]", "/cors-test/page": "/cors-test", "/conditions/page": "/conditions", "/conditions/[slug]/page": "/conditions/[slug]", "/forgot-password/page": "/forgot-password", "/contact/page": "/contact", "/signin/page": "/signin", "/signup/page": "/signup", "/practitioners/[slug]/page": "/practitioners/[slug]", "/practitioners/page": "/practitioners", "/terms/page": "/terms", "/specialities/page": "/specialities", "/specialities/[slug]/page": "/specialities/[slug]", "/search/page": "/search"}