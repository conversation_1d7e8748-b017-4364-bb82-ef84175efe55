const axios = require('axios');

/**
 * Helper functions for testing with real Strapi data
 */
module.exports = {
  /**
   * Creates a test user and logs in
   * @returns {Promise<string>} JWT token
   */
  async createUserAndLogin() {
    // Test user data
    const testUser = {
      username: `tester-${Date.now()}`,
      email: `tester-${Date.now()}@example.com`,
      password: 'Password123!',
    };

    try {
      // First try to register a new user
      const registerResponse = await axios.post(
        `${global.testConfig.apiUrl}/api/auth/local/register`,
        testUser
      );

      return registerResponse.data.jwt;
    } catch (registerError) {
      console.error('Error registering test user:', registerError.response?.data || registerError.message);

      // If registration fails, try to login with default credentials
      try {
        return await this.login();
      } catch (loginError) {
        console.error('Error logging in with default credentials:', loginError.response?.data || loginError.message);
        throw loginError;
      }
    }
  },

  /**
   * Authenticates with Strapi and returns a JWT token
   * @param {Object} credentials - Login credentials
   * @returns {Promise<string>} JWT token
   */
  async login(credentials = {}) {
    const defaultCredentials = {
      identifier: process.env.STRAPI_TEST_USERNAME || '<EMAIL>',
      password: process.env.STRAPI_TEST_PASSWORD || 'Password123',
    };

    // Use provided credentials or fall back to defaults
    const loginData = {
      ...defaultCredentials,
      ...credentials,
    };

    try {
      const response = await axios.post(
        `${global.testConfig.apiUrl}/api/auth/local`,
        loginData
      );

      return response.data.jwt;
    } catch (error) {
      console.error('Login error:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Fetches entries from a collection
   * @param {string} endpoint - API endpoint (e.g., 'clinics', 'blogs')
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Collection entries
   */
  async getEntries(endpoint, params = {}) {
    try {
      const response = await axios.get(
        `${global.testConfig.apiUrl}/api/${endpoint}`,
        { params }
      );

      return response.data;
    } catch (error) {
      console.error(`Error fetching ${endpoint}:`, error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Fetches a single entry by ID
   * @param {string} endpoint - API endpoint (e.g., 'clinics', 'blogs')
   * @param {string|number} id - Entry ID
   * @returns {Promise<Object>} Entry data
   */
  async getEntry(endpoint, id) {
    try {
      const response = await axios.get(
        `${global.testConfig.apiUrl}/api/${endpoint}/${id}`
      );

      return response.data;
    } catch (error) {
      console.error(`Error fetching ${endpoint}/${id}:`, error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Creates a new entry (use with caution in tests)
   * @param {string} endpoint - API endpoint (e.g., 'clinics', 'blogs')
   * @param {Object} data - Entry data
   * @param {string} token - JWT token for authentication
   * @returns {Promise<Object>} Created entry
   */
  async createEntry(endpoint, data, token) {
    try {
      const response = await axios.post(
        `${global.testConfig.apiUrl}/api/${endpoint}`,
        { data },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error(`Error creating ${endpoint}:`, error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * Cleans up a collection by deleting test entries
   * @param {string} collectionName - Collection name (e.g., 'api::clinic.clinic')
   */
  async cleanupCollection(collectionName) {
    // This is a placeholder for cleanup logic
    // In a real implementation, you would connect to the database
    // and delete test entries, but for now we'll just log
    console.log(`Cleanup requested for collection: ${collectionName}`);

    // Since we're using real data in tests, we won't actually delete anything
    // to avoid affecting production data
    return true;
  },
};
