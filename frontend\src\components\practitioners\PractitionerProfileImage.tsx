'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';

interface PractitionerProfileImageProps {
  imageUrl: string | null;
  name: string;
}

const PractitionerProfileImage = ({ imageUrl, name }: PractitionerProfileImageProps) => {
  const [imageError, setImageError] = useState(false);
  const [validUrl, setValidUrl] = useState<string | null>(null);

  // Validate the URL when the component mounts or when imageUrl changes
  useEffect(() => {
    // Reset error state when URL changes
    setImageError(false);

    // Validate URL
    if (imageUrl && typeof imageUrl === 'string' && imageUrl.trim() !== '') {
      try {
        // Basic URL validation
        const url = imageUrl.startsWith('http') ? imageUrl : `https://${imageUrl}`;
        new URL(url); // This will throw if URL is invalid
        setValidUrl(url);
      } catch (e) {
        console.error('Invalid image URL:', imageUrl, e);
        setImageError(true);
        setValidUrl(null);
      }
    } else {
      setValidUrl(null);
    }
  }, [imageUrl]);

  // Show fallback for invalid URLs, errors, or when loading
  if (!validUrl || imageError) {
    return (
      <div className="bg-emerald-100 h-32 w-32 rounded-full flex items-center justify-center">
        <span className="text-emerald-700 font-bold text-3xl">
          {name ? name.charAt(0).toUpperCase() : '?'}
        </span>
      </div>
    );
  }

  return (
    <div className="h-32 w-32 rounded-full overflow-hidden">
      <Image
        src={validUrl}
        alt={`${name}'s profile picture`}
        width={128}
        height={128}
        className="object-cover rounded-full"
        priority={true}
        onError={(e) => {
          console.error('Error loading practitioner image:', {
            practitionerName: name,
            profilePictureUrl: validUrl,
            error: e
          });
          setImageError(true);
        }}
      />
    </div>
  );
};

export default PractitionerProfileImage;
