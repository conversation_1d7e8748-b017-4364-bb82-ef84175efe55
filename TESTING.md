# Testing Guide for Natural Healing Now

This document provides an overview of the testing setup and guidelines for the Natural Healing Now project.

## Frontend Testing

The frontend testing is set up using Jest, React Testing Library, and Playwright.

### Unit and Component Tests

Unit and component tests are written using Jest and React Testing Library.

#### Running Unit Tests

```bash
cd frontend
npm test                # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Generate test coverage report
```

#### Test Structure

- `frontend/src/__tests__/lib/`: Tests for utility functions
- `frontend/src/__tests__/components/`: Tests for React components
- `frontend/src/__tests__/a11y/`: Accessibility tests for components

### End-to-End Tests

End-to-end tests are written using Playwright.

#### Running E2E Tests

```bash
cd frontend
npm run test:e2e       # Run all E2E tests
npm run test:e2e:ui    # Run E2E tests with UI
```

#### Test Structure

- `frontend/e2e/home/<USER>
- `frontend/e2e/blog/`: Tests for the blog pages
- `frontend/e2e/clinics/`: Tests for the clinics pages
- `frontend/e2e/practitioners/`: Tests for the practitioners pages
- `frontend/e2e/accessibility.spec.ts`: Accessibility tests for all pages

### Accessibility Testing

Accessibility tests are implemented at both the component level and the page level.

#### Running Accessibility Tests

```bash
cd frontend
npm run test:a11y      # Run component-level accessibility tests
npm run test:e2e       # Run page-level accessibility tests (included in E2E tests)
```

## Backend Testing

The backend testing is set up using Jest and Axios to test against your real Strapi data.

### Running Backend Tests

```bash
cd strapi-cms
npm test               # Run all tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Generate test coverage report
npm run test:real      # Run tests with real Strapi data
```

### Test Structure

- `strapi-cms/tests/integration/`: Integration tests for API endpoints
- `strapi-cms/tests/helpers/`: Helper functions and setup files for testing

### Testing with Real Data

The backend tests are configured to use your real Strapi data. To set this up:

1. Edit the `.env.test` file in the `strapi-cms` directory:
   ```
   # Test environment configuration
   TEST_API_URL=http://localhost:1337
   TEST_WITH_REAL_DATA=true

   # Credentials for authenticated tests
   STRAPI_TEST_USERNAME=your-test-username
   STRAPI_TEST_PASSWORD=your-test-password
   ```

2. Make sure your Strapi instance is running when you run the tests
3. Use the `npm run test:real` command to run tests that use real data

## Writing Tests

### Frontend Unit Tests

When writing unit tests for the frontend, follow these guidelines:

1. Test one thing at a time
2. Use descriptive test names
3. Mock external dependencies
4. Test both success and error cases

Example:

```typescript
import { render, screen } from '@testing-library/react';
import MyComponent from '@/components/MyComponent';

describe('MyComponent', () => {
  it('should render correctly', () => {
    render(<MyComponent />);
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });
});
```

### Frontend E2E Tests

When writing E2E tests, follow these guidelines:

1. Test user flows, not implementation details
2. Make tests resilient to UI changes
3. Use page objects when appropriate
4. Include accessibility checks

Example:

```typescript
import { test, expect } from '@playwright/test';

test('user can navigate to about page', async ({ page }) => {
  await page.goto('/');
  await page.getByRole('link', { name: 'About' }).click();
  await expect(page).toHaveURL('/about');
});
```

### Backend Tests

When writing backend tests, follow these guidelines:

1. Test API endpoints
2. Test authentication and authorization
3. Be careful with tests that modify real data
4. Use helper functions for common operations

Example with real data:

```javascript
const strapiHelpers = require('../helpers/strapi');

describe('API Tests', () => {
  const runTests = process.env.TEST_WITH_REAL_DATA === 'true';

  (runTests ? it : it.skip)('should fetch data from the API', async () => {
    const result = await strapiHelpers.getEntries('blogs', {
      'pagination[page]': 1,
      'pagination[pageSize]': 5,
    });

    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
    expect(Array.isArray(result.data)).toBe(true);
  });
});
```

## Continuous Integration

Tests are automatically run in the CI pipeline when changes are pushed to the repository. The pipeline will fail if any tests fail.

## Test Coverage

Aim for high test coverage, especially for critical functionality. Use the coverage reports to identify areas that need more testing.
