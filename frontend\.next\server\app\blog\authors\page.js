(()=>{var e={};e.id=432,e.ids=[432],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3862:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.t.bind(t,46533,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32384:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>n});var s=t(65239),a=t(48088),o=t(31369),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let n={children:["",{children:["blog",{children:["authors",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,60180)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\authors\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\blog\\authors\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/blog/authors/page",pathname:"/blog/authors",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60180:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>x,revalidate:()=>u});var s=t(37413),a=t(4536),o=t.n(a),i=t(73993),l=t(53384),n=t(58446),d=t(82158),c=t(39916);let u=43200;async function h(){try{let e=await n.$.blog.getAuthors({cache:"force-cache",next:{revalidate:43200,tags:["strapi-authors","strapi-blog-authors"]}});if(!e||!e.data)return console.error("Failed to fetch authors data"),[];return e.data.map(e=>{let r=(0,d.Z5)(e.profilePicture),t=Array.isArray(e.blog_posts)?e.blog_posts.length:0,s=e.bio;return s&&"string"==typeof s&&(s=s.replace(/<[^>]*>/g,"")),{id:e.id,name:e.name||"Unknown Author",slug:e.slug||"",bio:s||null,profilePicture:r,email:e.email||null,website:e.website||null,post_count:t}})}catch(e){return console.error("Error fetching authors:",e),[]}}let x={title:"Blog Authors | Natural Healing Now",description:"Meet our expert contributors who share their knowledge and insights on holistic health topics."};async function m({searchParams:e}){let r=await h();if(!r||0===r.length)return(0,c.notFound)();let t=e?.query||"",a=t?function(e,r){if(!r)return e;let t=r.toLowerCase();return e.filter(e=>e.name.toLowerCase().includes(t)||e.bio&&e.bio.toLowerCase().includes(t))}(r,t):r;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Blog Authors"}),(0,s.jsx)("p",{className:"text-lg max-w-3xl",children:"Meet our expert contributors who share their knowledge and insights on holistic health topics."})]})}),(0,s.jsx)("div",{className:"bg-white shadow-md",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-6",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsx)("form",{action:"/blog/authors",method:"GET",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",name:"query",placeholder:"Search authors",defaultValue:t,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"}),(0,s.jsx)(i.CKj,{className:"absolute left-3 top-3 text-gray-400"}),(0,s.jsx)("button",{type:"submit",className:"absolute right-2 top-2 bg-emerald-500 text-white px-3 py-1 rounded-md text-sm hover:bg-emerald-600",children:"Search"})]})})})})}),(0,s.jsx)("div",{className:"py-12 bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-8",children:[t?`Search Results for "${t}"`:"All Authors",t&&(0,s.jsxs)("span",{className:"ml-2 text-sm font-normal text-gray-500",children:["(",a.length," ",1===a.length?"author":"authors"," found)"]})]}),a.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map(e=>(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[e.profilePicture?(0,s.jsx)("div",{className:"h-16 w-16 rounded-full overflow-hidden mr-4 flex-shrink-0 border border-gray-200",children:(0,s.jsx)(l.default,{src:e.profilePicture,alt:e.name,width:64,height:64,className:"object-cover w-full h-full",sizes:"64px"})}):(0,s.jsx)("div",{className:"bg-emerald-100 h-16 w-16 rounded-full flex items-center justify-center mr-4 flex-shrink-0",children:(0,s.jsx)(i.JXP,{className:"text-emerald-700 text-xl"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-800",children:(0,s.jsx)(o(),{href:`/blog/authors/${e.slug}`,className:"hover:text-emerald-600",children:e.name})}),e.email&&(0,s.jsx)("p",{className:"text-gray-500 text-sm truncate",children:e.email})]})]}),e.bio&&(0,s.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:e.bio}),(0,s.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[e.post_count," ",1===e.post_count?"article":"articles"]}),(0,s.jsx)(o(),{href:`/blog/authors/${e.slug}`,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Profile →"})]})]})},e.id.toString()))}):(0,s.jsxs)("div",{className:"bg-white p-8 rounded-lg text-center shadow",children:[(0,s.jsx)("p",{className:"text-gray-600",children:"No authors found matching your search criteria."}),(0,s.jsx)(o(),{href:"/blog/authors",className:"mt-4 inline-block text-emerald-600 hover:text-emerald-700",children:"View all authors"})]})]})}),(0,s.jsx)("div",{className:"py-8 bg-white",children:(0,s.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,s.jsx)(o(),{href:"/blog",className:"text-emerald-600 hover:text-emerald-700 font-medium",children:"← Back to Blog"})})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64110:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.t.bind(t,49603,23))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,1330,3376,6391,2975,4959,8446,270,7880],()=>t(32384));module.exports=s})();