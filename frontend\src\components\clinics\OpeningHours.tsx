import React from 'react';

// Define the structure for a single opening hour entry based on the Strapi component
type OpeningHour = {
  id: string | number; // Strapi components usually have an id
  day: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';
  openTime?: string | null; // Strapi time format HH:mm:ss.SSS
  closeTime?: string | null; // Strapi time format HH:mm:ss.SSS
  closed?: boolean | null;
};

// Define the props for the component
interface OpeningHoursProps {
  hours: OpeningHour[];
}

// Helper function to format Strapi time string (HH:mm:ss.SSS) to AM/PM
const formatTime = (timeString: string | null | undefined): string => {
  if (!timeString) {
    return 'N/A'; // Handle cases where time might be missing
  }
  try {
    // Create a dummy date object to use time formatting methods
    // Using UTC 'Z' to avoid local timezone shifts affecting the time part
    const date = new Date(`1970-01-01T${timeString}Z`);
    // Check if the date is valid after parsing
    if (isNaN(date.getTime())) {
      console.warn(`Invalid time string received: ${timeString}`);
      return 'Invalid Time';
    }
    // Format to h:mm AM/PM (e.g., 9:00 AM, 5:30 PM)
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'UTC' // Specify UTC to match the input interpretation
    });
  } catch (error) {
    console.error(`Error formatting time string "${timeString}":`, error);
    return 'Error'; // Return an error indicator
  }
};

// Define the order of days for sorting
const dayOrder: Record<OpeningHour['day'], number> = {
  Monday: 1,
  Tuesday: 2,
  Wednesday: 3,
  Thursday: 4,
  Friday: 5,
  Saturday: 6,
  Sunday: 7,
};

const OpeningHours: React.FC<OpeningHoursProps> = ({ hours }) => {
  if (!hours || hours.length === 0) {
    return null; // Don't render anything if no hours data is provided
  }

  // Sort the hours based on the defined day order
  const sortedHours = [...hours].sort((a, b) => dayOrder[a.day] - dayOrder[b.day]);

  return (
    <section className="bg-white rounded-lg shadow-sm p-6 mb-6">
      <h3 className="font-bold text-gray-800 mb-4">Opening Hours</h3>
      <ul className="space-y-2">
        {sortedHours.map((item) => (
          <li key={item.id || item.day} className="flex justify-between items-center">
            <span className="text-gray-600 w-1/3">{item.day}</span>
            {item.closed ? (
              <span className="font-medium text-red-600">Closed</span>
            ) : (
              <span className="font-medium text-gray-800 text-right">
                {formatTime(item.openTime)} - {formatTime(item.closeTime)}
              </span>
            )}
          </li>
        ))}
      </ul>
    </section>
  );
};

export default OpeningHours;
