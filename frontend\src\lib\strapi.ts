import qs from 'qs'; // Import qs

// Define parameter type interfaces for Strapi v5
interface StrapiPagination {
  page?: number;
  pageSize?: number;
  start?: number; // For offset pagination
  limit?: number; // For limit pagination
}

interface SortParams {
  sort?: string | string[];
}

interface FilterParams {
  [key: string]: any;
  filters?: FilterParams;
  populate?: string | string[] | Record<string, any>;
  fields?: string[]; // To select specific fields
  publicationState?: 'live' | 'preview';
  locale?: string;
}

// Combine interfaces for the main query parameters object
interface QueryParams extends SortParams, FilterParams {
  pagination?: StrapiPagination;
  populate?: string | string[] | Record<string, any>;
  fields?: string[];
  publicationState?: 'live' | 'preview';
  locale?: string;
  // Allow other potential top-level params, though filters/pagination/sort/populate/fields are standard
  [key: string]: any;
}

// Get the API URL from environment variables
const API_URL = process.env.NEXT_PUBLIC_API_URL;

// Ensure we have a valid API URL, especially in production
if (!API_URL) {
  if (process.env.NODE_ENV === 'production') {
    console.error('CRITICAL ERROR: NEXT_PUBLIC_API_URL is not defined in environment variables');
  } else {
    console.warn('WARNING: NEXT_PUBLIC_API_URL is not defined in environment variables, using fallback');
  }
}

// Define a fallback URL for development only
const FALLBACK_URL = process.env.NODE_ENV === 'development' ? 'http://localhost:1337' : '';

// Use the API_URL if available, otherwise use the fallback (only in development)
const EFFECTIVE_API_URL = API_URL || FALLBACK_URL;

// Always log the API URL being used
console.log('Using Strapi API URL:', EFFECTIVE_API_URL || 'No API URL found in environment variables');

// Get the Strapi Media URL for images (used for debugging)
const STRAPI_MEDIA_URL = EFFECTIVE_API_URL ?
  EFFECTIVE_API_URL.replace('strapiapp.com', 'media.strapiapp.com') :
  '';

// Log the media URL for debugging
if (STRAPI_MEDIA_URL) {
  console.log('Strapi Media URL:', STRAPI_MEDIA_URL);
}

// Define interface for Next.js cache options
interface NextCacheOptions {
  revalidate?: number | false;
  tags?: string[];
}

// Define interface for fetchAPI options
interface FetchAPIOptions {
  params?: any;
  headers?: Record<string, string>;
  next?: NextCacheOptions;
  [key: string]: any;
}

// Helper function to handle API responses and errors
export const fetchAPI = async (endpoint: string, options: FetchAPIOptions = {}) => {
  const requestId = Math.random().toString(36).substring(2, 8);
  // Construct the full URL for the fetch request
  // Ensure endpoint starts with a slash if it's not already relative from /api
  const apiPath = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  let urlForFetch = `${EFFECTIVE_API_URL}/api${apiPath}`;

  try {
    const API_TOKEN = process.env.STRAPI_API_TOKEN;
    const baseHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      ...(API_TOKEN && { Authorization: `Bearer ${API_TOKEN}` }),
    };

    // In Next.js 15, fetch is uncached by default
    // We need to explicitly opt-in to caching with 'force-cache'
    // or set a revalidation period

    // Determine the cache setting based on revalidate option
    let cacheOption: RequestCache = 'no-store'; // Default to no-store in Next.js 15

    // If options.cache is explicitly provided, use it
    if (options.cache) {
      cacheOption = options.cache as RequestCache;
    }
    // Otherwise, derive from revalidate for Next.js 15 ISR behavior
    // For ISR (revalidate = false or revalidate > 0), we need 'force-cache'.
    // For dynamic rendering (revalidate = 0), we need 'no-store'.
    // If revalidate is undefined, default to 'force-cache' to support ISR pages
    // that don't explicitly pass revalidate: false in every fetch call.
    else if (options.next?.revalidate !== undefined) {
      cacheOption = options.next.revalidate === 0 ? 'no-store' : 'force-cache';
    } else {
      // Default to 'force-cache' if revalidate is not specified,
      // to ensure ISR pages (which might not pass revalidate on every fetch) still cache.
      // This aligns with the need to opt-into caching for ISR.
      cacheOption = 'force-cache';
    }

    // Log cache settings in development for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${requestId}] Fetch cache settings for ${endpoint}:`, {
        cache: cacheOption,
        revalidate: options.next?.revalidate,
        tags: options.next?.tags || []
      });
    }

    const fetchOptions: RequestInit = {
      method: options.method || 'GET',
      headers: {
        ...baseHeaders,
        ...(options.headers || {}),
      },
      // Explicitly set cache option for Next.js 15 compatibility
      cache: cacheOption,
      // Ensure next options (like tags and revalidate) are consistently applied
      next: {
        // Default to an empty array for tags if not provided
        tags: options.next?.tags || [],
        // Preserve revalidate if explicitly set
        ...(options.next?.revalidate !== undefined && { revalidate: options.next.revalidate }),
      },
    };

    // If revalidate is specifically false (cache forever), ensure it's passed correctly
    if (options.next?.revalidate === false && fetchOptions.next) {
      fetchOptions.next.revalidate = false;
    }

    // Handle query parameters for GET requests using 'qs'
    if ((fetchOptions.method === 'GET' || fetchOptions.method === 'HEAD') && options.params) {
      const queryString = qs.stringify(options.params, { encodeValuesOnly: true });
      if (queryString) {
        urlForFetch = `${urlForFetch}?${queryString}`;
      }

    } else if (options.params && fetchOptions.method !== 'GET' && fetchOptions.method !== 'HEAD') {
      // For POST, PUT, etc., set the body
      fetchOptions.body = JSON.stringify(options.params);
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`[${requestId}] Requesting Strapi API (native fetch): ${urlForFetch}`);
      if (fetchOptions.next?.tags) console.log(`[${requestId}] Using cache tags:`, fetchOptions.next.tags);
      if (fetchOptions.next?.revalidate !== undefined) console.log(`[${requestId}] Using revalidation:`, fetchOptions.next.revalidate);
      if (options.params) console.log(`[${requestId}] Request params (raw):`, JSON.stringify(options.params, null, 2));
    }

    const response = await fetch(urlForFetch, fetchOptions);

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText, details: await response.text().catch(() => '') };
      }
      console.error(`[${requestId}] API Error (${urlForFetch}): Status ${response.status}`, errorData);
      const error = new Error(`API Error: ${response.status} ${response.statusText}`);
      // @ts-ignore
      error.response = { status: response.status, data: errorData };
      throw error;
    }

    const responseData = await response.json();

    if (process.env.NODE_ENV === 'development') {
      console.log(`[${requestId}] API request successful for ${urlForFetch}`);
      if (responseData) {
        console.log(`[${requestId}] Response structure:`, JSON.stringify({
          hasData: !!responseData?.data,
          dataType: responseData?.data ? (Array.isArray(responseData.data) ? 'array' : 'object') : 'none',
          dataLength: responseData?.data && Array.isArray(responseData.data) ? responseData.data.length : 'n/a',
          hasMeta: !!responseData?.meta,
        }, null, 2));
      }
    }
    return responseData;

  } catch (error: any) {
    const isProduction = process.env.NODE_ENV === 'production';
    if (isProduction) {
      console.error(`[${requestId}] API Error (${urlForFetch}):`, error.message || 'Unknown error');
      if (error.response?.status) {
        console.error(`[${requestId}] Status:`, error.response.status);
      }
    } else {
      console.error(`[${requestId}] Error fetching from API (${urlForFetch}):`, error.message || 'Unknown error');
      if (error.response) {
        console.error(`[${requestId}] Response status:`, error.response.status);
        console.error(`[${requestId}] Response data:`, error.response.data);
      } else if (error.request) { // This part is more relevant for Axios, fetch won't have error.request
        console.error(`[${requestId}] No response received. Request details for ${urlForFetch}`);
      } else {
        console.error(`[${requestId}] Error details:`, error);
      }
    }
    if (error.message && error.message.includes('ECONNREFUSED')) { // General ECONNREFUSED
      console.error(`[${requestId}] CRITICAL ERROR: Connection refused for ${urlForFetch}.`);
      if (urlForFetch.includes('127.0.0.1') || urlForFetch.includes('localhost')) {
        console.error(`[${requestId}] Attempting to connect to localhost. Check NEXT_PUBLIC_API_URL in Vercel environment variables if this is a deployed environment.`);
      }
    }
    if (error.response?.status === 400) {
      console.error(`[${requestId}] Bad Request (400) - Check query parameters for Strapi v5 compatibility.`);
    }
    error.requestId = requestId;
    throw error;
  }
};

// Define specific params type for getAll functions needing search/pagination
interface GetAllParams extends QueryParams {
  cache?: RequestCache; // Cache option for fetch
  next?: NextCacheOptions; // Explicitly add next for Next.js caching options
  query?: string; // For name/keyword search
  location?: string; // For location search
  specialtySlug?: string; // For filtering by specialty
  conditionSlug?: string; // For filtering by condition
  categorySlug?: string; // For filtering by category
  page?: number;
  pageSize?: number; // Allow overriding default page size
}

// Typed API functions for specific content types
export const getStrapiContent = {
  // Clinic related queries
  clinics: {
    getAll: async (params: GetAllParams = {}) => {
      const { query, location, specialtySlug, conditionSlug, categorySlug, page = 1, pageSize = 12, cache, next, ...restParams } = params; // Destructure slugs and separate cache/next options



      // Base query params (exclude cache and next options)
      const queryParams: QueryParams = {
        ...restParams,
        populate: restParams.populate || '*',
        publicationState: 'live',
        pagination: { // Add pagination object
          page: page,
          pageSize: pageSize,
        },
        filters: { // Add filters object
          ...(restParams.filters || {}), // Keep existing filters if any
        }
      };

      // Build filters dynamically
      const filters: FilterParams = { ...(restParams.filters || {}) };
      let combinedFilters: any[] = []; // Use an array for $and

      // Name/Service/Specialty/Condition filter using $or
      if (query) {
        combinedFilters.push({
          $or: [
            { name: { $containsi: query } },
            { services: { name: { $containsi: query } } },
            { specialties: { name: { $containsi: query } } },
            { conditions: { name: { $containsi: query } } },
            { categories: { name: { $containsi: query } } },
            { description: { $containsi: query } } // Added clinic description search
          ]
        });
      }

      // Location filter - Revised structure: $or inside the component filter
      if (location) {
        combinedFilters.push({
          address: { // Target the component
            $or: [ // Apply OR logic *within* the component's fields (Removed streetAddress2)
              { streetAddress1: { $containsi: location } },
              // { streetAddress2: { $containsi: location } }, // Removed invalid key
              { city: { $containsi: location } },
              { stateProvince: { $containsi: location } },
              { postalCode: { $containsi: location } },
            ]
          }
        });
      }

      // Specialty filter
      if (specialtySlug) {
         combinedFilters.push({ specialties: { slug: { $eq: specialtySlug } } });
      }

      // Condition filter
      if (conditionSlug) {
         combinedFilters.push({ conditions: { slug: { $eq: conditionSlug } } });
      }

      // Category filter
      if (categorySlug) {
         combinedFilters.push({ categories: { slug: { $eq: categorySlug } } });
      }

      // Combine all filters using $and if multiple filters exist
      if (combinedFilters.length > 1) {
        filters.$and = combinedFilters;
      } else if (combinedFilters.length === 1) {
        Object.assign(filters, combinedFilters[0]);
      }

      // Assign the constructed filters back to queryParams
      queryParams.filters = filters;

      // Add cache tags if specialtySlug is provided
      if (specialtySlug) {
        // Define cache tags for specialty-related queries
        const cacheTags = [
          'strapi-clinics',
          'strapi-specialties',
          `strapi-specialty-${specialtySlug}`,
          `strapi-specialty-${specialtySlug}-clinics`
        ];

        // Add page number to cache tags
        cacheTags.push(`strapi-specialty-${specialtySlug}-page-${page}`);

        // Add query to cache tags if provided
        if (query) {
          cacheTags.push(`strapi-specialty-${specialtySlug}-query-${query}`);
        }

        // Add location to cache tags if provided
        if (location) {
          cacheTags.push(`strapi-specialty-${specialtySlug}-location-${location}`);
        }

        // Fetch clinics with cache tags
        const nextOptionsForSpecialty: NextCacheOptions = { tags: cacheTags };
        if (next?.revalidate !== undefined) {
          nextOptionsForSpecialty.revalidate = next.revalidate;
        }
        return fetchAPI(`/clinics`, {
          params: queryParams,
          cache: cache,
          next: nextOptionsForSpecialty
        });
      }

      // Add cache tags if categorySlug is provided
      if (categorySlug) {
        // Define cache tags for category-related queries
        const cacheTags = [
          'strapi-clinics',
          'strapi-categories',
          `strapi-category-${categorySlug}`,
          `strapi-category-${categorySlug}-clinics`
        ];

        // Add page number to cache tags
        cacheTags.push(`strapi-category-${categorySlug}-page-${page}`);

        // Add query to cache tags if provided
        if (query) {
          cacheTags.push(`strapi-category-${categorySlug}-query-${query}`);
        }

        // Add location to cache tags if provided
        if (location) {
          cacheTags.push(`strapi-category-${categorySlug}-location-${location}`);
        }

        // Fetch clinics with cache tags
        const nextOptionsForCategory: NextCacheOptions = { tags: cacheTags };
        if (next?.revalidate !== undefined) {
          nextOptionsForCategory.revalidate = next.revalidate;
        }
        return fetchAPI(`/clinics`, {
          params: queryParams,
          cache: cache,
          next: nextOptionsForCategory
        });
      }

      // Define default cache tags for clinic list
      const defaultCacheTags = ['strapi-clinics-list'];

      // Add page number to cache tags
      const pageNumber = queryParams.pagination?.page || 1;
      defaultCacheTags.push(`strapi-clinics-page-${pageNumber}`);

      // Add query to cache tags if provided
      if (query) {
        defaultCacheTags.push(`strapi-clinics-query-${query}`);
      }

      // Add location to cache tags if provided
      if (location) {
        defaultCacheTags.push(`strapi-clinics-location-${location}`);
      }

      // Merge with any existing cache tags from next options
      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];

      const nextOptionsDefault: NextCacheOptions = { tags: effectiveTags };
      if (next?.revalidate !== undefined) {
        nextOptionsDefault.revalidate = next.revalidate;
      }
      // Fetch clinics with cache tags
      return fetchAPI(`/clinics`, {
        params: queryParams,
        cache: cache,
        next: nextOptionsDefault
      });
    },
    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => { // Fetch only slugs
      // Default tags for clinic slugs
      const defaultTags = ['strapi-clinics-slugs'];

      // Combine default tags with any provided tags
      const combinedTags = [...defaultTags, ...(options.next?.tags || [])];

      // In Next.js 15, we need to explicitly opt-in to caching
      return fetchAPI(`/clinics`, {
        params: {
          fields: ['slug'], // Only fetch the slug field
          pagination: {
            pageSize: 250, // Adjust if you have more clinics, or implement proper pagination
          },
        },
        // Explicitly set cache option for Next.js 15
        cache: options.cache || 'force-cache',
        next: {
          // Use combined tags
          tags: combinedTags,
          // Default to 12 hours revalidation if not specified
          revalidate: options.next?.revalidate ?? 43200,
          // Spread other next options
          ...(options.next || {})
        },
      });
    },
    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {
      const defaultTags = ['strapi-clinics', `strapi-clinic-${slug}`];

      // Combine default tags with any provided tags
      const combinedTags = [...defaultTags, ...(options.next?.tags || [])];

      // In Next.js 15, we need to explicitly opt-in to caching
      return fetchAPI(`/clinics`, {
        params: {
          filters: { slug: { $eq: slug } },
          populate: {
            logo: true,
            address: true,
            contactInfo: true,
            location: true,
            openingHours: true,
            services: true,
            specialties: true,
            conditions: true,
            practitioners: { populate: { profilePicture: true } },
            appointment_options: true,
            payment_methods: true,
            seo: true,
          }
        },
        // Explicitly set cache option for Next.js 15
        cache: options.cache || 'force-cache',
        next: {
          ...(options.next || {}),
          // Use combined tags
          tags: combinedTags,
          // Default to 12 hours revalidation if not specified
          revalidate: options.next?.revalidate ?? 43200
        }
      });
    },
    getFeatured: async (options: FetchAPIOptions = {}) => fetchAPI(`/clinics`, {
      params: {
        filters: { isFeatured: { $eq: true } },
        populate: '*'
      },
      ...options
    }),
    // New function to get clinics by category slug with pagination and filtering
    getByCategorySlug: async ({
      slug,
      query = '',
      location = '',
      page = 1,
      pageSize = 12
    }: {
      slug: string;
      query?: string;
      location?: string;
      page?: number;
      pageSize?: number;
    }) => {
      try {
        console.log(`Fetching clinics for category slug: ${slug}, page: ${page}, query: ${query}, location: ${location}`);

        // Build filters using the same approach as getAll function
        const filters: FilterParams = {
          categories: { slug: { $eq: slug } }
        };

        // Build combined filters array for complex queries
        const combinedFilters: any[] = [];

        // Always include the category filter
        combinedFilters.push({ categories: { slug: { $eq: slug } } });

        // Add search query if provided
        if (query) {
          combinedFilters.push({
            $or: [
              { name: { $containsi: query } },
              { services: { name: { $containsi: query } } },
              { specialties: { name: { $containsi: query } } },
              { conditions: { name: { $containsi: query } } }
            ]
          });
        }

        // Add location filter if provided
        if (location) {
          combinedFilters.push({
            address: {
              $or: [
                { streetAddress1: { $containsi: location } },
                { city: { $containsi: location } },
                { stateProvince: { $containsi: location } },
                { postalCode: { $containsi: location } },
              ]
            }
          });
        }

        // Use $and only if we have multiple filters
        const finalFilters: FilterParams = combinedFilters.length > 1
          ? { $and: combinedFilters }
          : filters;

        // Construct query parameters
        const queryParams: QueryParams = {
          filters: finalFilters,
          pagination: {
            page,
            pageSize
          },
          populate: {
            logo: true,
            featuredImage: true,
            address: true,
            contactInfo: true,
            categories: true
          },
          publicationState: 'live'
        };

        // Define cache tags for this request to enable targeted revalidation
        const cacheTags = [
          'strapi-clinics',
          'strapi-categories',
          `strapi-category-${slug}`,
          `strapi-category-${slug}-clinics`,
          `strapi-category-${slug}-page-${page}`
        ];

        // Add the query to the cache tag if it exists
        if (query) {
          cacheTags.push(`strapi-category-${slug}-query-${query}`);
        }

        // Add the location to the cache tag if it exists
        if (location) {
          cacheTags.push(`strapi-category-${slug}-location-${location}`);
        }



        // Use fetchAPI with cache tags
        return fetchAPI(`/clinics`, {
          params: queryParams,
          next: {
            tags: cacheTags
          }
        });
      } catch (error) {
        console.error(`Error in getByCategorySlug for clinics with slug ${slug}:`, error);
        throw error;
      }
    },
  },

  // Practitioner related queries
  practitioners: {
    getAll: async (params: GetAllParams = {}) => { // Use GetAllParams
      const { query, location, specialtySlug, conditionSlug, categorySlug, page = 1, pageSize = 12, cache, next, ...restParams } = params; // Destructure slugs and separate cache/next options



      // Base query params (exclude cache and next options)
      const queryParams: QueryParams = {
        ...restParams,
        populate: restParams.populate || '*',
        publicationState: 'live',
        pagination: { // Add pagination object
          page: page,
          pageSize: pageSize,
        },
        filters: { // Add filters object
          ...(restParams.filters || {}), // Keep existing filters if any
        }
      };

      // Build filters dynamically
      const filters: FilterParams = { ...(restParams.filters || {}) };
      let combinedFilters: any[] = []; // Use an array for $and

      // Name/Specialty/Condition filter using $or
      if (query) {
        combinedFilters.push({
          $or: [
            { name: { $containsi: query } },
            { specialties: { name: { $containsi: query } } }, // Search in specialty names
            { conditions: { name: { $containsi: query } } },  // Search in condition names
          ]
        });
      }

      // Location filter (assuming practitioners have direct address component) - Revised structure
      // NOTE: If practitioners are linked via clinics, this filter might need adjustment.
      if (location) {
         combinedFilters.push({
          address: { // Target the component
            $or: [ // Apply OR logic *within* the component's fields (Removed streetAddress2)
              { streetAddress1: { $containsi: location } },
              // { streetAddress2: { $containsi: location } }, // Removed invalid key
              { city: { $containsi: location } },
              { stateProvince: { $containsi: location } },
              { postalCode: { $containsi: location } },
            ]
          }
        });
      }

      // Specialty filter
      if (specialtySlug) {
         combinedFilters.push({ specialties: { slug: { $eq: specialtySlug } } });
      }

      // Condition filter
      if (conditionSlug) {
         combinedFilters.push({ conditions: { slug: { $eq: conditionSlug } } });
      }

      // Category filter
      if (categorySlug) {
         combinedFilters.push({ categories: { slug: { $eq: categorySlug } } });
      }

      // Combine all filters using $and if multiple filters exist
      if (combinedFilters.length > 1) {
        filters.$and = combinedFilters;
      } else if (combinedFilters.length === 1) {
        Object.assign(filters, combinedFilters[0]);
      }

      // Assign the constructed filters back to queryParams
      queryParams.filters = filters;

      // Add cache tags if specialtySlug is provided
      if (specialtySlug) {
        // Define cache tags for specialty-related queries
        const cacheTags = [
          'strapi-practitioners',
          'strapi-specialties',
          `strapi-specialty-${specialtySlug}`,
          `strapi-specialty-${specialtySlug}-practitioners`
        ];

        // Add page number to cache tags
        cacheTags.push(`strapi-specialty-${specialtySlug}-page-${page}`);

        // Add query to cache tags if provided
        if (query) {
          cacheTags.push(`strapi-specialty-${specialtySlug}-query-${query}`);
        }

        // Add location to cache tags if provided
        if (location) {
          cacheTags.push(`strapi-specialty-${specialtySlug}-location-${location}`);
        }

        // Fetch practitioners with cache tags
        const nextOptionsForSpecialty: NextCacheOptions = { tags: cacheTags };
        if (next?.revalidate !== undefined) {
          nextOptionsForSpecialty.revalidate = next.revalidate;
        }
        return fetchAPI(`/practitioners`, {
          params: queryParams,
          cache: cache,
          next: nextOptionsForSpecialty
        });
      }

      // Add cache tags if categorySlug is provided
      if (categorySlug) {
        // Define cache tags for category-related queries
        const cacheTags = [
          'strapi-practitioners',
          'strapi-categories',
          `strapi-category-${categorySlug}`,
          `strapi-category-${categorySlug}-practitioners`
        ];

        // Add page number to cache tags
        cacheTags.push(`strapi-category-${categorySlug}-page-${page}`);

        // Add query to cache tags if provided
        if (query) {
          cacheTags.push(`strapi-category-${categorySlug}-query-${query}`);
        }

        // Add location to cache tags if provided
        if (location) {
          cacheTags.push(`strapi-category-${categorySlug}-location-${location}`);
        }

        // Fetch practitioners with cache tags
        const nextOptionsForCategory: NextCacheOptions = { tags: cacheTags };
        if (next?.revalidate !== undefined) {
          nextOptionsForCategory.revalidate = next.revalidate;
        }
        return fetchAPI(`/practitioners`, {
          params: queryParams,
          cache: cache,
          next: nextOptionsForCategory
        });
      }

      // Define default cache tags for practitioner list
      const defaultCacheTags = ['strapi-practitioners-list'];

      // Add page number to cache tags
      const pageNumber = queryParams.pagination?.page || 1;
      defaultCacheTags.push(`strapi-practitioners-page-${pageNumber}`);

      // Add query to cache tags if provided
      if (query) {
        defaultCacheTags.push(`strapi-practitioners-query-${query}`);
      }

      // Add location to cache tags if provided
      if (location) {
        defaultCacheTags.push(`strapi-practitioners-location-${location}`);
      }

      // Merge with any existing cache tags from next options
      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];

      const nextOptionsDefault: NextCacheOptions = { tags: effectiveTags };
      if (next?.revalidate !== undefined) {
        nextOptionsDefault.revalidate = next.revalidate;
      }
      // Fetch practitioners with cache tags
      return fetchAPI(`/practitioners`, {
        params: queryParams,
        cache: cache,
        next: nextOptionsDefault
      });
    },
    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {
      const allSlugs: { slug: string }[] = [];
      let page = 1;
      // Strapi's default max page size is often 100. Using a safe common limit.
      // If your Strapi instance is configured for a higher limit, this can be adjusted.
      const effectivePageSize = 100;
      let moreSlugsToFetch = true;

      const defaultTags = ['strapi-practitioners-slugs'];
      // Consolidate fetch options for reuse, ensuring cache and next options from params are respected
      const fetchOptionsBase = {
        cache: options.cache || 'force-cache',
        next: {
          revalidate: options.next?.revalidate ?? 43200, // Default to 12 hours
          tags: [...defaultTags, ...(options.next?.tags || [])], // Combine default and provided tags
          ...(options.next || {}), // Spread other next options like specific revalidate or additional tags
        }
      };
      // Ensure tags are unique if combined from multiple sources
      if (fetchOptionsBase.next.tags) {
        fetchOptionsBase.next.tags = Array.from(new Set(fetchOptionsBase.next.tags));
      }


      while (moreSlugsToFetch) {
        try {
          const response = await fetchAPI(`/practitioners`, {
            params: {
              fields: ['slug'],
              pagination: {
                page: page,
                pageSize: effectivePageSize,
              },
            },
            // Pass the consolidated cache/next options
            cache: fetchOptionsBase.cache,
            next: fetchOptionsBase.next,
          });

          if (response?.data && Array.isArray(response.data)) {
            response.data.forEach((item: any) => {
              // Strapi v5 flattens the response, so attributes are directly on the item.
              // No need to check item.attributes.slug typically.
              if (item && item.slug) {
                allSlugs.push({ slug: item.slug });
              }
            });

            // Determine if there are more pages to fetch
            const paginationInfo = response.meta?.pagination;
            if (response.data.length < effectivePageSize || !paginationInfo || page >= paginationInfo.pageCount) {
              moreSlugsToFetch = false;
            } else {
              page++;
            }
          } else {
            // No data or unexpected format
            moreSlugsToFetch = false;
          }
        } catch (error) {
          console.error(`Error fetching page ${page} of practitioner slugs:`, error);
          moreSlugsToFetch = false; // Stop on error
        }
      }
      return { data: allSlugs };
    },
    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {
      const defaultTags = ['strapi-practitioner', `strapi-practitioner-${slug}`]; // Changed 'strapi-practitioners' to 'strapi-practitioner'
      return fetchAPI(`/practitioners`, {
        params: {
          filters: { slug: { $eq: slug } },
          populate: {
            profilePicture: true,
            contactInfo: true,
            specialties: true,
            conditions: true,
            clinics: true,
            seo: true
          }
        },
        // In Next.js 15, we need to explicitly opt-in to caching
        cache: options.cache || 'force-cache',
        next: {
          ...(options.next || {}),
          tags: [...defaultTags, ...(options.next?.tags || [])],
          // Default to 12 hours revalidation if not specified
          revalidate: options.next?.revalidate ?? 43200
        }
      });
    },
    getFeatured: async (options: FetchAPIOptions = {}) => fetchAPI(`/practitioners`, {
      params: {
        filters: { isFeatured: { $eq: true } },
        populate: '*'
      },
      ...options
    }),
    // New function to get practitioners by category slug with pagination and filtering
    getByCategorySlug: async ({
      slug,
      query = '',
      location = '',
      page = 1,
      pageSize = 12
    }: {
      slug: string;
      query?: string;
      location?: string;
      page?: number;
      pageSize?: number;
    }) => {
      try {

        // Build filters using the same approach as getAll function
        const filters: FilterParams = {
          categories: { slug: { $eq: slug } }
        };

        // Build combined filters array for complex queries
        const combinedFilters: any[] = [];

        // Always include the category filter
        combinedFilters.push({ categories: { slug: { $eq: slug } } });

        // Add search query if provided
        if (query) {
          combinedFilters.push({
            $or: [
              { name: { $containsi: query } },
              { title: { $containsi: query } },
              { qualifications: { $containsi: query } },
              { specialties: { name: { $containsi: query } } },
              { conditions: { name: { $containsi: query } } }
            ]
          });
        }

        // Add location filter if provided
        if (location) {
          combinedFilters.push({
            address: {
              $or: [
                { streetAddress1: { $containsi: location } },
                { city: { $containsi: location } },
                { stateProvince: { $containsi: location } },
                { postalCode: { $containsi: location } },
              ]
            }
          });
        }

        // Use $and only if we have multiple filters
        const finalFilters: FilterParams = combinedFilters.length > 1
          ? { $and: combinedFilters }
          : filters;

        // Construct query parameters
        const queryParams: QueryParams = {
          filters: finalFilters,
          pagination: {
            page,
            pageSize
          },
          populate: {
            profilePicture: true,
            contactInfo: true,
            specialties: true,
            conditions: true,
            categories: true
          },
          publicationState: 'live'
        };

        // Define cache tags for this request to enable targeted revalidation
        const cacheTags = [
          'strapi-practitioners',
          'strapi-categories',
          `strapi-category-${slug}`,
          `strapi-category-${slug}-practitioners`,
          `strapi-category-${slug}-page-${page}`
        ];

        // Add the query to the cache tag if it exists
        if (query) {
          cacheTags.push(`strapi-category-${slug}-query-${query}`);
        }

        // Add the location to the cache tag if it exists
        if (location) {
          cacheTags.push(`strapi-category-${slug}-location-${location}`);
        }



        // Use fetchAPI with cache tags
        return fetchAPI(`/practitioners`, {
          params: queryParams,
          next: {
            tags: cacheTags
          }
        });
      } catch (error) {
        console.error(`Error in getByCategorySlug for practitioners with slug ${slug}:`, error);
        throw error;
      }
    },
  },

  // Category related queries
  categories: {
    getAll: async (params: GetAllParams = {}) => { // Use GetAllParams
       const { query, location, page = 1, pageSize = 12, cache, next, ...restParams } = params; // Destructure cache/next options

      // Base query params (exclude cache and next options)
      const queryParams: QueryParams = {
        ...restParams,
        populate: restParams.populate || {
          // Explicitly populate media fields to ensure proper structure
          icon: true,
          featuredImage: true,
          // Include any other fields that need to be populated
        },
        publicationState: 'live',
        pagination: { // Add pagination object
          page: page,
          pageSize: pageSize,
        },
        filters: { // Add filters object
          ...(restParams.filters || {}), // Keep existing filters if any
        }
      };

      // Add name filter if query is provided
      if (query && queryParams.filters) {
        queryParams.filters.name = { $containsi: query };
      }

      // Define cache tags for this request to enable targeted revalidation
      const defaultCacheTags = ['strapi-categories', 'strapi-categories-slugs'];

      // Add page number to cache tags
      if (queryParams.pagination?.page) defaultCacheTags.push(`strapi-categories-page-${queryParams.pagination.page}`);

      // Add query to cache tags if provided
      if (query) defaultCacheTags.push(`strapi-categories-query-${query}`);

      // Merge with any existing cache tags from next options
      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];

      const nextOptions: NextCacheOptions = { tags: effectiveTags };
      if (next?.revalidate !== undefined) {
        nextOptions.revalidate = next.revalidate;
      }

      return fetchAPI(`/categories`, {
        params: queryParams, // queryParams should be built excluding 'next' from original params
        cache: cache,
        next: nextOptions
      });
    },
    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {
      const defaultCacheTags = [
        'strapi-categories',
        `strapi-category-${slug}`,
        `strapi-category-${slug}-clinics`,
        `strapi-category-${slug}-practitioners`
      ];
      return fetchAPI(`/categories`, {
        params: {
          filters: { slug: { $eq: slug } },
          populate: {
            seo: { populate: { openGraph: { populate: { ogImage: true } }, metaImage: true } },
            icon: true,
            featuredImage: true,
            clinics: { populate: { logo: true, featuredImage: true, address: true, contactInfo: true } },
            practitioners: { populate: { profilePicture: true, contactInfo: true } }
          },
          publicationState: 'live'
        },
        next: {
          ...(options.next || {}),
          tags: [...defaultCacheTags, ...(options.next?.tags || [])]
        }
      });
    },
    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {
      const defaultTags = ['strapi-categories-slugs'];
      return fetchAPI(`/categories`, {
        params: {
          fields: ['slug'],
          pagination: { pageSize: 1000 }, // Adjust if more items or implement pagination
        },
        cache: options.cache || 'force-cache',
        next: {
          ...(options.next || {}),
          tags: [...defaultTags, ...(options.next?.tags || [])],
          revalidate: options.next?.revalidate ?? 43200, // Default to 12 hours
        }
      });
    },
    // Function to get categories specifically for the footer
    getFooterCategories: async (options: FetchAPIOptions = {}) => {
      const defaultTags = ['strapi-categories', 'strapi-categories-footer'];
      return fetchAPI(`/categories`, {
        params: {
          filters: { showInFooter: { $eq: true } },
          populate: '*',
          publicationState: 'live'
        },
        next: {
          ...(options.next || {}),
          tags: [...defaultTags, ...(options.next?.tags || [])]
        }
      });
    },
  },

  // Blog related queries
  blog: {
    getPosts: async (params: QueryParams = {}) => {
      try {
        // Create a clean query params object with proper structure for Strapi v5
        const queryParams: Record<string, any> = {
          publicationState: 'live',
        };

        // Extract pagination parameters
        let page = 1;
        let pageSize = 10;

        // Handle pagination separately to ensure correct structure
        if (params.pagination) {
          queryParams.pagination = params.pagination;
          page = params.pagination.page || 1;
          pageSize = params.pagination.pageSize || 10;
        } else if (params.page || params.pageSize) {
          // Support for legacy pagination parameters
          page = params.page || 1;
          pageSize = params.pageSize || 10;
          queryParams.pagination = {
            page,
            pageSize
          };
        }

        // Handle sort parameter
        if (params.sort) {
          queryParams.sort = params.sort;
        } else {
          // Default to newest first if no sort parameter is provided
          queryParams.sort = ['publishDate:desc'];
        }

        // Handle filters
        if (params.filters) {
          queryParams.filters = params.filters;
        } else if (params.categorySlug) {
          // Support for direct categorySlug parameter
          queryParams.filters = {
            ...(queryParams.filters || {}),
            blog_categories: { slug: { $eq: params.categorySlug } }
          };
        } else if (params.tagSlug) {
          // Support for direct tagSlug parameter
          queryParams.filters = {
            ...(queryParams.filters || {}),
            blog_tags: { slug: { $eq: params.tagSlug } }
          };
        } else if (params.query) {
          // Support for direct query parameter
          queryParams.filters = {
            ...(queryParams.filters || {}),
            $or: [
              { title: { $containsi: params.query } },
              { excerpt: { $containsi: params.query } }
            ]
          };
        }

        // Handle populate parameter with fallback to default comprehensive fields
        if (params.populate) {
          queryParams.populate = params.populate;
        } else {
          queryParams.populate = {
            featuredImage: true,
            author_blogs: {
              populate: {
                profilePicture: true
              }
            },
            blog_categories: true,
            blog_tags: true
          };
        }

        // Query parameters prepared for blog posts

        // Define cache tags for this request to enable targeted revalidation
        const cacheTags = ['strapi-blog-posts'];

        // Add category-specific cache tag if filtering by category
        if (params.categorySlug) {
          cacheTags.push(`strapi-category-${params.categorySlug}`);
          cacheTags.push(`strapi-category-${params.categorySlug}-page-${page}`);
        }

        // Add tag-specific cache tag if filtering by tag
        if (params.tagSlug) {
          cacheTags.push(`strapi-tag-${params.tagSlug}`);
          cacheTags.push(`strapi-tag-${params.tagSlug}-page-${page}`);
        }

        // Add query-specific cache tag if filtering by query
        if (params.query) {
          cacheTags.push(`strapi-blog-query-${params.query}`);
        }

        // Fetch blog posts with the constructed query parameters and cache tags
        const response = await fetchAPI(`/blog-posts`, {
          params: queryParams,
          next: {
            tags: cacheTags
          }
        });

        // Log the response structure for debugging
        if (process.env.NODE_ENV === 'development') {
          console.log('Blog getPosts response structure:', JSON.stringify({
            hasData: !!response?.data,
            dataIsArray: Array.isArray(response?.data),
            dataLength: Array.isArray(response?.data) ? response.data.length : 'not an array',
            hasMeta: !!response?.meta,
            hasPagination: !!response?.meta?.pagination,
            totalItems: response?.meta?.pagination?.total || 'unknown'
          }));
        }

        return response;
      } catch (error: any) { // Type error as any to access response property
        console.error('Error in blog.getPosts:', error);
        // Add more detailed error logging
        if (error.response) {
          console.error('Response status:', error.response.status);
          console.error('Response data:', error.response.data);
          console.error('Response headers:', error.response.headers);
        }
        throw error;
      }
    },
    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {
      const defaultTags = ['strapi-blog-posts-slugs'];
      return fetchAPI(`/blog-posts`, {
        params: {
          fields: ['slug'],
          pagination: { pageSize: 1000 },
        },
        next: {
          ...(options.next || {}),
          tags: [...defaultTags, ...(options.next?.tags || [])]
        }
      });
    },
    getPostBySlug: async (slug: string, options: FetchAPIOptions = {}) => {
      const defaultCacheTags = ['strapi-blog-posts', `strapi-blog-post-${slug}`];
      return fetchAPI(`/blog-posts`, {
        params: {
          filters: { slug: { $eq: slug } },
          populate: {
            seo: { populate: { metaImage: true, openGraph: true } },
            featuredImage: true,
            author_blogs: { fields: ['id', 'name', 'slug', 'bio'], populate: { profilePicture: true } },
            blog_categories: { fields: ['id', 'name', 'slug'] },
            blog_tags: { fields: ['id', 'name', 'slug'] }
          }
        },
        next: {
          ...(options.next || {}),
          tags: [...defaultCacheTags, ...(options.next?.tags || [])]
        }
      });
    },
    getCategories: async (params: QueryParams = {}, fetchOptions: FetchAPIOptions = {}) => {
      const { next: paramsNext, ...strapiParams } = params; // Separate next from Strapi query params
      const queryParams = {
        ...strapiParams,
        publicationState: 'live',
        populate: strapiParams.populate || '*'
      };
      const defaultCacheTags = ['strapi-categories', 'strapi-blog-categories'];
      return fetchAPI(`/blog-categories`, {
        params: queryParams,
        next: {
          ...(fetchOptions.next || {}), // Options passed directly to getCategories
          ...(paramsNext || {}), // Options passed within the params object
          tags: [...defaultCacheTags, ...(fetchOptions.next?.tags || []), ...(paramsNext?.tags || [])]
        }
      });
    },
    getCategoryBySlug: async (slug: string, params: QueryParams = {}, fetchOptions: FetchAPIOptions = {}) => {
      try {
        // Default pagination to show the last 12 blog posts
        const page = params.pagination?.page || 1;
        const pageSize = params.pagination?.pageSize || 12;

        // Default sort to show the most recent posts first
        const sort = params.sort || 'publishDate:desc';

        // Prepare to make API call to /blog-categories

        // Define cache tags for this request to enable targeted revalidation
        const cacheTags = [
          'strapi-categories',
          'strapi-blog-categories',
          `strapi-category-${slug}`,
          `strapi-category-${slug}-page-${page}`
        ];

        // Improved query structure for Strapi v5
        // The key change is to separate the blog_posts population from pagination
        // This ensures we get the category with ALL its posts, but paginated
        const queryParams = {
          filters: {
            slug: {
              $eq: slug
            }
          },
          // This pagination is for the blog categories themselves
          pagination: {
            page: 1, // We only need the first page as we're filtering by slug
            pageSize: 1 // We only need one category
          },
          sort: [sort],
          populate: {
            blog_posts: {
              fields: ['title', 'slug', 'excerpt', 'publishDate', 'publishedAt'],
              populate: {
                featuredImage: true,
                author_blogs: {
                  populate: {
                    profilePicture: true
                  }
                }
              },
              // This pagination is for the blog posts within the category
              pagination: {
                page,
                pageSize
              },
              sort: [sort]
            },
            seo: true
          }
        };

        // Query params prepared for slug ${slug}

        // In Strapi 5, use a simpler query structure
        const response = await fetchAPI(`/blog-categories`, {
          params: queryParams,
          next: {
            tags: cacheTags
          }
        });

        // Response received for category with slug ${slug}

        return response;
      } catch (error) {
        console.error(`Error in getCategoryBySlug for slug ${slug}:`, error);

        // Try a simpler approach as fallback
        try {

          // Use a very simple query structure as fallback
          const { next: paramsNextFallback, ...strapiParamsFallback } = params;
          return await fetchAPI(`/blog-categories`, {
            params: {
              ...strapiParamsFallback,
              filters: { slug: { $eq: slug } },
              populate: { blog_posts: { populate: ['featuredImage', 'author_blogs.profilePicture'] }, seo: true }
            },
            next: { ...(fetchOptions.next || {}), ...(paramsNextFallback || {}) }
          });
        } catch (fallbackError) {
          console.error(`Fallback also failed for slug ${slug}:`, fallbackError);
          throw fallbackError;
        }
      }
    },
    getTags: async (options: FetchAPIOptions = {}) => {
      const defaultTags = ['strapi-tags', 'strapi-blog-tags'];
      return fetchAPI('/blog-tags', {
        params: { populate: '*' },
        next: {
          ...(options.next || {}),
          tags: [...defaultTags, ...(options.next?.tags || [])]
        }
      });
    },
    getTagBySlug: async (slug: string, options: FetchAPIOptions = {}) => {
      const defaultTags = ['strapi-tags', 'strapi-blog-tags', `strapi-tag-${slug}`];
      return fetchAPI(`/blog-tags`, {
        params: {
          filters: { slug: { $eq: slug } },
          populate: {
            blog_posts: {
              populate: {
                featuredImage: {
                  fields: ['url', 'alternativeText', 'width', 'height', 'formats'] // Be explicit
                },
                author_blogs: {
                  populate: {
                    profilePicture: {
                      fields: ['url', 'alternativeText', 'width', 'height', 'formats'] // Be explicit
                    }
                  }
                }
              }
            }
          }
        },
        next: {
          ...(options.next || {}),
          tags: [...defaultTags, ...(options.next?.tags || [])]
        }
      });
    },
    getAuthors: <AUTHORS>
      // Default to 'force-cache' and 12hr revalidation if not specified by caller
      const cacheSetting = options.cache ?? 'force-cache';
      const revalidateSetting = options.next?.revalidate ?? 43200;
      const defaultTags = ['strapi-authors', 'strapi-blog-authors'];
      return fetchAPI(`/authors`, {
        params: { populate: '*' },
        cache: cacheSetting,
        next: {
          ...options.next, // Spread caller's next options first
          revalidate: revalidateSetting,
          tags: [...defaultTags, ...(options.next?.tags || [])]
        }
      });
    },
    getAuthorBySlug: async (slug: string, options: FetchAPIOptions = {}) => {
      // Default to 'force-cache' and 12hr revalidation if not specified by caller
      const cacheSetting = options.cache ?? 'force-cache';
      const revalidateSetting = options.next?.revalidate ?? 43200;
      const defaultTags = ['strapi-authors', 'strapi-blog-authors', `strapi-author-${slug}`];
      return fetchAPI(`/authors`, {
        params: { filters: { slug: { $eq: slug } }, populate: '*' },
        cache: cacheSetting,
        next: {
          ...options.next, // Spread caller's next options first
          revalidate: revalidateSetting,
          tags: [...defaultTags, ...(options.next?.tags || [])]
        }
      });
    },
  },

  // Condition related queries
  conditions: {
    getAll: async (params: GetAllParams = {}) => {
      const { query, location, page = 1, pageSize = 12, cache, next, ...restParams } = params;
      const queryParams: QueryParams = {
        ...restParams,
        populate: restParams.populate || '*',
        publicationState: 'live',
        pagination: { page: page, pageSize: pageSize },
        filters: { ...(restParams.filters || {}) }
      };
      if (query && queryParams.filters) queryParams.filters.name = { $containsi: query };

      // Define cache tags for this request
      const defaultCacheTags = ['strapi-conditions'];
      if (queryParams.pagination?.page) defaultCacheTags.push(`strapi-conditions-page-${queryParams.pagination.page}`);
      if (query) defaultCacheTags.push(`strapi-conditions-query-${query}`);

      // Merge with any existing cache tags from next options
      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];

      const nextOptions: NextCacheOptions = { tags: effectiveTags };
      if (next?.revalidate !== undefined) {
        nextOptions.revalidate = next.revalidate;
      }

      return fetchAPI(`/conditions`, {
        params: queryParams,
        cache: cache,
        next: nextOptions
      });
    },
    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {
      const defaultTags = ['strapi-conditions', `strapi-condition-${slug}`];
      return fetchAPI(`/conditions`, {
        params: { filters: { slug: { $eq: slug } }, populate: { seo: true } },
        next: {
          ...(options.next || {}),
          tags: [...defaultTags, ...(options.next?.tags || [])]
        }
      });
    },
    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {
      const defaultTags = ['strapi-conditions-slugs'];
      return fetchAPI(`/conditions`, {
        params: {
          fields: ['slug'],
          pagination: { pageSize: 1000 }, // Adjust if more items or implement pagination
        },
        cache: options.cache || 'force-cache',
        next: {
          ...(options.next || {}),
          tags: [...defaultTags, ...(options.next?.tags || [])],
          revalidate: options.next?.revalidate ?? 43200,
        }
      });
    },
  },

  // About Us (Single Type)
  aboutUs: {
    get: async (options: FetchAPIOptions = {}) => fetchAPI(`/about-us`, { params: { populate: '*', publicationState: 'live' }, ...options }),
  },

  // Privacy Policy (Single Type)
  privacyPolicy: {
    get: async (options: FetchAPIOptions = {}) => fetchAPI(`/privacy-policy`, { params: { populate: '*', publicationState: 'live' }, ...options }),
  },

  // Terms of Service (Single Type)
  termsOfService: {
    get: async (options: FetchAPIOptions = {}) => fetchAPI(`/terms-of-service`, { params: { populate: '*', publicationState: 'live' }, ...options }),
  },

  // Affiliate Disclosure (Single Type)
  affiliateDisclosure: {
    get: async (options: FetchAPIOptions = {}) => fetchAPI(`/affiliate-disclosure`, { params: { populate: '*', publicationState: 'live' }, ...options }),
  },

  // Global settings
  global: {
    getSettings: async (options: FetchAPIOptions = {}) => fetchAPI(`/global-setting`, { params: { populate: '*', publicationState: 'live' }, ...options }),
    getHomepage: async (options: FetchAPIOptions = {}) => fetchAPI(`/homepage`, { params: { populate: '*', publicationState: 'live' }, ...options }),
    getBlogHomepage: async (options: FetchAPIOptions = {}) => fetchAPI(`/blog-homepage`, { params: { populate: '*', publicationState: 'live' }, ...options }),
  },

  // Specialty related queries
  specialties: {
    getAll: async (params: GetAllParams = {}) => {
      const { query, location, page = 1, pageSize = 12, cache, next, ...restParams } = params;
      const queryParams: QueryParams = {
        ...restParams,
        populate: restParams.populate || '*',
        publicationState: 'live',
        pagination: { page: page, pageSize: pageSize },
        filters: { ...(restParams.filters || {}) }
      };
      if (query && queryParams.filters) queryParams.filters.name = { $containsi: query };

      // Define cache tags for this request
      const defaultCacheTags = ['strapi-specialties'];
      if (queryParams.pagination?.page) defaultCacheTags.push(`strapi-specialties-page-${queryParams.pagination.page}`);
      if (query) defaultCacheTags.push(`strapi-specialties-query-${query}`);

      // Merge with any existing cache tags from next options
      const effectiveTags = [...(next?.tags || []), ...defaultCacheTags];

      const nextOptions: NextCacheOptions = { tags: effectiveTags };
      if (next?.revalidate !== undefined) {
        nextOptions.revalidate = next.revalidate;
      }

      try {
        const response = await fetchAPI(`/specialties`, {
          params: queryParams,
          cache: cache,
          next: nextOptions
        });

        // If the data is an array and has items, check the first item
        if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
          const firstItem = response.data[0];

          // If the item doesn't have attributes but has direct properties,
          // transform it to match the expected structure
          if (!firstItem.attributes && firstItem.id) {
            // Create a new array with transformed items
            response.data = response.data.map((item: any) => {
              // Skip if already has attributes or is invalid
              if (item.attributes || !item.id) return item;

              // Create a copy of the item without the properties we'll move to attributes
              const { id, ...rest } = item;

              // Return a new object with the expected structure
              return {
                id,
                attributes: { ...rest }
              };
            });
          }
        }

        return response;
      } catch (error) {
        console.error("Error in getAll for specialties:", error);
        throw error;
      }
    },
    getAllSlugs: async (options: FetchAPIOptions = {}): Promise<{ data: { slug: string }[] }> => {
      const defaultTags = ['strapi-specialties-slugs'];
      return fetchAPI(`/specialties`, {
        params: {
          fields: ['slug'],
          pagination: { pageSize: 1000 }, // Adjust if more items or implement pagination
        },
        cache: options.cache || 'force-cache',
        next: {
          ...(options.next || {}),
          tags: [...defaultTags, ...(options.next?.tags || [])],
          revalidate: options.next?.revalidate ?? 43200,
        }
      });
    },
    getBySlug: async (slug: string, options: FetchAPIOptions = {}) => {
      const defaultCacheTags = ['strapi-specialties', `strapi-specialty-${slug}`];
      try {
        const response = await fetchAPI(`/specialties`, {
          params: {
            filters: { slug: { $eq: slug } },
            populate: { seo: true, featuredImage: true, clinics: true, practitioners: true }
          },
          next: {
            ...(options.next || {}),
            tags: [...defaultCacheTags, ...(options.next?.tags || [])]
          }
        });

        // If the data is an array and has items, check the first item
        if (response?.data && Array.isArray(response.data) && response.data.length > 0) {
          const firstItem = response.data[0];

          // If the item doesn't have attributes but has direct properties,
          // transform it to match the expected structure
          if (!firstItem.attributes && firstItem.id) {
            // Create a new array with transformed items
            response.data = response.data.map((item: any) => {
              // Skip if already has attributes or is invalid
              if (item.attributes || !item.id) return item;

              // Create a copy of the item without the properties we'll move to attributes
              const { id, ...rest } = item;

              // Return a new object with the expected structure
              return {
                id,
                attributes: { ...rest }
              };
            });
          }
        }

        return response;
      } catch (error) {
        console.error(`Error in getBySlug for specialty ${slug}:`, error);
        throw error;
      }
    },
  },

  // SEO helpers
  seo: {
    getMetadata: (entity: any) => {
      if (!entity) return null;

      // Extract SEO data from the entity's SEO field (from the SEO plugin)
      const seo = entity.attributes?.seo?.data?.attributes || null;
      const metaSocial = entity.attributes?.metaSocial || [];

      return {
        title: seo?.metaTitle || entity.attributes?.title || entity.attributes?.name,
        description: seo?.metaDescription || entity.attributes?.description || entity.attributes?.excerpt,
        openGraph: metaSocial?.find((item: any) => item.socialNetwork === 'Facebook') || null,
        twitter: metaSocial?.find((item: any) => item.socialNetwork === 'Twitter') || null,
        structuredData: seo?.structuredData || null,
        canonicalURL: seo?.canonicalURL || null,
        metaRobots: seo?.metaRobots || null,
      };
    }
  }
};
