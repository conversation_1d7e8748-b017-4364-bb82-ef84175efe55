exports.id=4959,exports.ids=[4959],exports.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:o,objectFit:u}=e,a=n?40*n:t,l=i?40*i:r,d=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===u?"xMidYMid":"cover"===u?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(21122);let n=r(1322),i=r(27894),o=["-moz-initial","fill","none","scale-down",void 0];function u(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let d,s,c,{src:f,sizes:p,unoptimized:b=!1,priority:g=!1,loading:m,className:_,quality:h,width:R,height:y,fill:E=!1,style:v,overrideSrc:O,onLoad:j,onLoadingComplete:w,placeholder:P="empty",blurDataURL:C,fetchPriority:x,decoding:S="async",layout:M,objectFit:T,objectPosition:D,lazyBoundary:z,lazyRoot:F,...N}=e,{imgConf:I,showAltText:A,blurComplete:L,defaultLoader:B}=t,X=I||i.imageConfigDefault;if("allSizes"in X)d=X;else{let e=[...X.deviceSizes,...X.imageSizes].sort((e,t)=>e-t),t=X.deviceSizes.sort((e,t)=>e-t),n=null==(r=X.qualities)?void 0:r.sort((e,t)=>e-t);d={...X,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===B)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let k=N.loader||B;delete N.loader,delete N.srcSet;let G="__next_img_default"in k;if(G){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=k;k=t=>{let{config:r,...n}=t;return e(n)}}if(M){"fill"===M&&(E=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(v={...v,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!p&&(p=t)}let U="",H=a(R),W=a(y);if((l=f)&&"object"==typeof l&&(u(l)||void 0!==l.src)){let e=u(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(s=e.blurWidth,c=e.blurHeight,C=C||e.blurDataURL,U=e.src,!E)if(H||W){if(H&&!W){let t=H/e.width;W=Math.round(e.height*t)}else if(!H&&W){let t=W/e.height;H=Math.round(e.width*t)}}else H=e.width,W=e.height}let V=!g&&("lazy"===m||void 0===m);(!(f="string"==typeof f?f:U)||f.startsWith("data:")||f.startsWith("blob:"))&&(b=!0,V=!1),d.unoptimized&&(b=!0),G&&!d.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(b=!0);let q=a(h),K=Object.assign(E?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:T,objectPosition:D}:{},A?{}:{color:"transparent"},v),J=L||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:H,heightInt:W,blurWidth:s,blurHeight:c,blurDataURL:C||"",objectFit:K.objectFit})+'")':'url("'+P+'")',Y=o.includes(K.objectFit)?"fill"===K.objectFit?"100% 100%":"cover":K.objectFit,$=J?{backgroundSize:Y,backgroundPosition:K.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Q=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:o,sizes:u,loader:a}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,u),s=l.length-1;return{sizes:u||"w"!==d?u:"100vw",srcSet:l.map((e,n)=>a({config:t,src:r,quality:o,width:e})+" "+("w"===d?e:n+1)+d).join(", "),src:a({config:t,src:r,quality:o,width:l[s]})}}({config:d,src:f,unoptimized:b,width:H,quality:q,sizes:p,loader:k});return{props:{...N,loading:V?"lazy":m,fetchPriority:x,width:H,height:W,decoding:S,className:_,style:{...K,...$},sizes:Q.sizes,srcSet:Q.srcSet,src:O||Q.src},meta:{unoptimized:b,priority:g,placeholder:P,fill:E}}}},21122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},27894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},39916:(e,t,r)=>{"use strict";var n=r(97576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"unauthorized")&&r.d(t,{unauthorized:function(){return n.unauthorized}})},48976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49603:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("C:\\AI Coding Projects\\naturalhealingnow\\frontend\\node_modules\\next\\dist\\client\\image-component.js")},53384:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var n=r(70099),i=r.n(n)},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return a}});let n=r(72639),i=r(9131),o=r(49603),u=n._(r(52815));function a(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:u.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1440,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"custom",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=o.Image},70899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,u.isNextRouterError)(t)||(0,o.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,a.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),i=r(52637),o=r(51846),u=r(31162),a=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return u},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return s},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return l},redirect:function(){return a}});let n=r(52836),i=r(49026),o=r(19121).actionAsyncStorage;function u(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let o=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",o}function a(e,t){var r;throw null!=t||(t=(null==o||null==(r=o.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),u(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=i.RedirectType.replace),u(e,t,n.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function s(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return s},RedirectType:function(){return i.RedirectType},forbidden:function(){return u.forbidden},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return a.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(86897),i=r(49026),o=r(62765),u=r(48976),a=r(70899),l=r(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class s extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};