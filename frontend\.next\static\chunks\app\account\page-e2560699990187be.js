(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{5927:(e,s,l)=>{Promise.resolve().then(l.bind(l,8073))},8073:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>r});var n=l(5155),t=l(5695),a=l(3310),i=l(3254),c=l(2115);function d(e){let{children:s,redirectTo:l="/signin"}=e,{isAuthenticated:i,isLoading:d}=(0,a.A)(),r=(0,t.useRouter)();return((0,c.useEffect)(()=>{d||i||r.push(l)},[i,d,l,r]),d)?(0,n.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,n.jsx)("div",{className:"animate-pulse text-lg",children:"Loading..."})}):i?(0,n.jsx)(n.Fragment,{children:s}):null}function r(){let{user:e}=(0,a.A)(),s=(0,t.useRouter)();return(0,n.jsx)(d,{children:(0,n.jsx)(i.default,{children:(0,n.jsx)("div",{className:"container mx-auto py-12 px-4",children:(0,n.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-6 text-gray-800",children:"My Account"}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Account Information"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Email"}),(0,n.jsx)("p",{className:"font-medium",children:null==e?void 0:e.email})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Username"}),(0,n.jsx)("p",{className:"font-medium",children:null==e?void 0:e.username})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Account ID"}),(0,n.jsx)("p",{className:"font-medium",children:null==e?void 0:e.id})]})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Account Settings"}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsx)("button",{className:"px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2",onClick:()=>s.push("/forgot-password"),children:"Change Password"})})]})]})})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[844,6874,3063,3464,3120,2112,3254,8441,1684,7358],()=>s(5927)),_N_E=e.O()}]);