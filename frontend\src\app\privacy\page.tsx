import { getStrapiContent } from '@/lib/strapi';
import MarkdownContent from '@/components/blog/MarkdownContent';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

// Define the expected shape of the data (fields directly under data)
interface PrivacyPolicyData {
  id: number;
  title?: string; // Title might be in seo.metaTitle, making this optional
  content: string;
  seo?: any; // Using 'any' for flexibility, refine if SEO structure is known
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

interface StrapiSingleResponse<T> {
  data: T | null;
  meta: object;
}

// Function to generate metadata
export async function generateMetadata(): Promise<Metadata> {
  const defaultTitle = 'Privacy Policy';
  const defaultDescription = 'Read our Privacy Policy.';

  try {
    const response: StrapiSingleResponse<PrivacyPolicyData> = await getStrapiContent.privacyPolicy.get();
    const pageData = response?.data;

    if (!pageData) {
      return { title: defaultTitle, description: defaultDescription };
    }

    const seoData = pageData?.seo;
    const title = seoData?.metaTitle || pageData?.title || defaultTitle;
    const description = seoData?.metaDescription || defaultDescription;

    return {
      title: title,
      description: description,
      ...(seoData?.canonicalURL && { alternates: { canonical: seoData.canonicalURL } }),
      ...(seoData?.metaRobots && { robots: seoData.metaRobots }),
    };
  } catch (error) {
    console.error('Error fetching Privacy Policy metadata:', error);
    return { title: defaultTitle, description: 'Error loading page information.' };
  }
}

// The page component
export default async function PrivacyPolicyPage() {
  let pageData: PrivacyPolicyData | null = null;

  try {
    const rawResponse = await getStrapiContent.privacyPolicy.get();
    pageData = rawResponse?.data;
  } catch (error) {
    console.error('Failed to fetch Privacy Policy page data:', error);
  }

  if (!pageData) {
     console.error('Privacy Policy data object is null or undefined after fetch.');
     notFound();
  }

  if (!pageData.content) {
     console.error('Privacy Policy data fetched, but content field is missing or empty.');
     // Allow rendering with a message if content is missing
  }

  const title = pageData.seo?.metaTitle || pageData.title || 'Privacy Policy';
  const content = pageData.content;

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Title is handled by metadata, not shown in body */}
      <div className="prose lg:prose-xl max-w-none mx-auto">
        {content ? (
          <MarkdownContent content={content} />
        ) : (
          <p>Privacy Policy content is not available at the moment.</p>
        )}
      </div>
    </div>
  );
}
