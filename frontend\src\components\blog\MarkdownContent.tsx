"use client";

import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import { useEffect } from 'react';
import { trackPostView } from '@/lib/analytics';

interface MarkdownContentProps {
  content: string;
  postId?: string;
  postSlug?: string;
  applyNoFollow?: boolean; // Add optional prop, defaults to true
}

// Default applyNoFollow to true
const MarkdownContent = ({
  content,
  postId,
  postSlug,
  applyNoFollow = true
}: MarkdownContentProps) => {

  // Track post view when the component mounts
  useEffect(() => {
    if (postId && postSlug) {
      // Small delay to ensure the page has loaded
      const timer = setTimeout(() => {
        trackPostView(postId, postSlug);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [postId, postSlug]);
  return (
    <div className="mb-8"> {/* Removed prose prose-lg max-w-none */}
      <ReactMarkdown
        components={{
          h1: ({node, ...props}) => <h1 className="text-3xl font-bold text-gray-800 mt-8 mb-4" {...props} />,
          h2: ({node, ...props}) => <h2 className="text-2xl font-bold text-gray-800 mt-6 mb-3" {...props} />,
          h3: ({node, ...props}) => <h3 className="text-xl font-bold text-gray-800 mt-5 mb-2" {...props} />,
          h4: ({node, ...props}) => <h4 className="text-lg font-bold text-gray-800 mt-4 mb-2" {...props} />,
          p: ({node, ...props}) => <p className="text-gray-700 mb-4" {...props} />,
          a: ({node, href, ...props}) => { // Destructure href here
            const isExternal = href && (href.startsWith('http://') || href.startsWith('https://'));

            // Construct rel attribute based on applyNoFollow prop
            let rel = '';
            if (isExternal) {
              if (applyNoFollow) {
                rel += 'nofollow '; // Add nofollow only if prop is true
              }
              rel += 'noopener noreferrer'; // Always add these for security
            }

            const target = isExternal ? '_blank' : undefined;

            return (
              <a
                className="text-emerald-600 hover:text-emerald-700 underline"
                href={href}
                rel={rel.trim() || undefined} // Set rel only if it has content, otherwise undefined
                target={target}
                {...props}
              />
            );
          },
          ul: ({node, ...props}) => <ul className="list-disc pl-6 mb-4" {...props} />,
          ol: ({node, ...props}) => <ol className="list-decimal pl-6 mb-4" {...props} />,
          li: ({node, ...props}) => <li className="mb-1" {...props} />,
          blockquote: ({node, ...props}) => <blockquote className="border-l-4 border-emerald-500 pl-4 italic my-4" {...props} />
        }}
        rehypePlugins={[rehypeRaw]}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownContent;
