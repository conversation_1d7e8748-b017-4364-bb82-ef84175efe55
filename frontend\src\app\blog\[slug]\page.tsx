import Link from 'next/link';
import { getStrapiContent } from '@/lib/strapi';
import { notFound } from 'next/navigation';
import MarkdownContent from '@/components/blog/MarkdownContent';
import { Metadata, ResolvingMetadata } from 'next';
import { findRelatedPosts } from '@/lib/relatedPosts';
import {
  BlogPost, // This is the key import for typing
  mapStrapiBlogPostToProps,
  generateExcerpt
} from '@/lib/blogUtils';
import { StrapiBlogPost } from '@/types/strapi'; // Import StrapiBlogPost
import BlogPostHeader from '@/components/blog/BlogPostHeader';
import BlogPostFooter from '@/components/blog/BlogPostFooter';
import logger from '@/lib/logger';
import { getOgImageUrl } from '@/lib/mediaUtils';
// Import SITE_URL from mediaUtils
import mediaUtils from '@/lib/mediaUtils';

// Generate static paths for all blog posts at build time
export async function generateStaticParams() {
  try {
    const response = await getStrapiContent.blog.getAllSlugs();
    // Ensure response and response.data are not null and response.data is an array
    if (response && response.data && Array.isArray(response.data)) {
      // Filter out any items that might be null or don't have a slug property
      return response.data
        .filter((item: { slug: string } | null): item is { slug: string } => item !== null && typeof item.slug === 'string')
        .map((item: { slug: string }) => ({
          slug: item.slug,
        }));
    }
    // Log an error or return an empty array if data is not as expected
    logger.error('Failed to fetch blog post slugs or data is not in expected format for generateStaticParams:', response);
    return [];
  } catch (error) {
    logger.error('Error fetching blog post slugs for generateStaticParams:', error);
    return []; // Return empty array on error to prevent build failure
  }
}

// Enable ISR with on-demand revalidation
// No revalidation period - will only revalidate when triggered by webhook
export const revalidate = false;
// Allow dynamic params to be generated on-demand
export const dynamicParams = true;

// Mock data for fallback in case of API errors
const mockBlogPostDataObj = { // Renamed to avoid conflict with BlogPost type
  id: '1',
  title: 'The Benefits of Acupuncture for Stress Relief',
  slug: 'benefits-acupuncture-stress-relief',
  excerpt: 'Discover how acupuncture can help reduce stress and promote relaxation in your daily life.',
  content: `
    <p>In today's fast-paced world, stress has become a common companion for many of us. The constant demands of work, family, and social obligations can leave us feeling overwhelmed and depleted. While there are many approaches to managing stress, acupuncture has emerged as an effective and holistic solution that addresses both the physical and emotional aspects of stress.</p>
  `,
  featured_image: null,
  published_at: '2023-06-15T10:30:00Z',
  author: {
    id: '2',
    name: 'Michael Chen',
    slug: 'michael-chen',
    profile_picture: null
  },
  categories: [
    {
      id: '1',
      name: 'Acupuncture',
      slug: 'acupuncture'
    },
    {
      id: '5',
      name: 'Mental Health',
      slug: 'mental-health'
    }
  ],
  tags: [
    'stress relief',
    'acupuncture',
    'holistic health',
    'relaxation'
  ],
  related_posts: [
    {
      id: '2',
      title: 'Herbal Remedies for Common Digestive Issues',
      slug: 'herbal-remedies-digestive-issues',
      excerpt: 'Explore natural herbal solutions to address common digestive problems and promote gut health.'
    }
  ]
};

// Helper function to map related posts with correct typing
// Ensures the output matches BlogPost['related_posts'] structure
function mapRelatedPostsForPage(relatedPostsInput: BlogPost[]): BlogPost['related_posts'] {
  return relatedPostsInput.map((p: BlogPost) => { // Ensure 'p' is typed as BlogPost
    const contentString = typeof p.content === 'string' ? p.content : ''; // Ensure content is always a string
    const currentExcerpt = p.excerpt === null ? undefined : p.excerpt; // Match BlogPost['related_posts'][number]['excerpt'] which is string | null | undefined
    return {
      id: p.id,
      title: p.title,
      slug: p.slug,
      excerpt: currentExcerpt,
      content: contentString, // content is now guaranteed to be a string
      reading_time: p.reading_time
    };
  });
}

// Define the props type for generateMetadata
type Props = {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

// Generate Metadata function
export async function generateMetadata(
  { params, searchParams }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  // Await params before accessing its properties
  const awaitedParams = await params;
  // Access slug from awaited params
  const slug = awaitedParams.slug;
  const apiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || '';

  // Get the site URL from mediaUtils or environment variable
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

  // Log the URL being used for debugging
  if (process.env.NODE_ENV === 'development') { // Log only in development
    logger.debug(`Using site URL for canonical URLs in [slug] page: ${siteUrl}`);
  }

  try {
    const postResponse = await getStrapiContent.blog.getPostBySlug(slug);

    if (!postResponse || !postResponse.data || postResponse.data.length === 0) {
      return notFound(); // Use notFound() for consistency
    }

    const postData = postResponse.data[0];
    // Access data directly from postData, not postData.attributes for this response structure
    const postAttributes = postData; // Use postData directly
    const seoComponentData = postData.seo; // Access component data directly from postData

    // --- Fallback Logic ---
    const metaTitle = seoComponentData?.metaTitle || postAttributes.title || 'Blog Post';
    const metaDescription = seoComponentData?.metaDescription || generateExcerpt(postAttributes.content);

    // --- Image Handling ---
    let ogImageUrl: string | undefined = undefined;
    let twitterImageUrl: string | undefined = undefined;

    // Prefer SEO component image, then featured image
    // Adjust access based on direct structure
    const seoMetaImage = seoComponentData?.metaImage?.url; // Assuming direct url if populated
    const featuredImage = postAttributes.featuredImage?.url; // Assuming direct url if populated

    // Use the new getOgImageUrl function to get proper Strapi media URLs
    if (seoMetaImage) {
      ogImageUrl = getOgImageUrl(seoMetaImage);
      twitterImageUrl = getOgImageUrl(seoMetaImage);
    } else if (featuredImage) {
      ogImageUrl = getOgImageUrl(featuredImage);
      twitterImageUrl = getOgImageUrl(featuredImage);
    }

    // Log the image URLs for debugging
    logger.debug('Blog post og:image details for [slug] page:', {
      seoMetaImage,
      featuredImage,
      ogImageUrl,
      twitterImageUrl
    });

    // --- Construct Metadata ---
    const metadata: Metadata = {
      title: metaTitle,
      description: metaDescription,
      robots: seoComponentData?.metaRobots || 'index, follow', // Default to index, follow
      alternates: {
        canonical: seoComponentData?.canonicalURL || (siteUrl ? `${siteUrl}/blog/${slug}` : `/blog/${slug}`), // Use siteUrl if available
      },
      // Use openGraph data from SEO plugin if available
      openGraph: seoComponentData?.openGraph ? {
        title: seoComponentData.openGraph.title || metaTitle,
        description: seoComponentData.openGraph.description || metaDescription,
        type: seoComponentData.openGraph.type || 'article',
        url: seoComponentData.openGraph.url || seoComponentData?.canonicalURL || (siteUrl ? `${siteUrl}/blog/${slug}` : `/blog/${slug}`),
        siteName: seoComponentData.openGraph.siteName || 'Natural Healing Now',
        // Add article-specific properties
        publishedTime: postAttributes.published_at || postAttributes.publishDate || postAttributes.createdAt,
        authors: [postAttributes.author_blogs?.[0]?.name || 'Natural Healing Now'],
        // Use image from openGraph if available, otherwise use metaImage or featured image
        ...(seoComponentData.openGraph.image?.url ? {
          images: [{
            url: getOgImageUrl(seoComponentData.openGraph.image.url) || ''
          }]
        } : (ogImageUrl ? { images: [{ url: ogImageUrl }] } : {})),
      } : {
        // Fallback if no openGraph data in SEO plugin
        title: metaTitle,
        description: metaDescription,
        type: 'article',
        publishedTime: postAttributes.published_at || postAttributes.publishDate || postAttributes.createdAt,
        authors: [postAttributes.author_blogs?.[0]?.name || 'Natural Healing Now'],
        url: seoComponentData?.canonicalURL || (siteUrl ? `${siteUrl}/blog/${slug}` : `/blog/${slug}`),
        siteName: 'Natural Healing Now',
        images: ogImageUrl ? [{ url: ogImageUrl }] : [],
      },
      twitter: {
        card: 'summary_large_image',
        title: seoComponentData?.twitter?.title || metaTitle,
        description: seoComponentData?.twitter?.description || metaDescription,
        images: twitterImageUrl ? [twitterImageUrl] : [],
      },
    };

    // We'll validate the structured data here, but we'll inject it in the component return
    if (seoComponentData?.structuredData) {
      // Check if structuredData is already an object or a string
      if (typeof seoComponentData.structuredData === 'string') {
        try {
          // Just validate the JSON is parseable if it's a string
          JSON.parse(seoComponentData.structuredData);
        } catch (e) {
          logger.error("Invalid structured data JSON in [slug] page metadata:", { error: e, data: seoComponentData.structuredData });
        }
      }
      // If it's an object, it's already valid (no need to parse)
    }


    return metadata;

  } catch (error) {
    logger.error('Error generating metadata for blog post [slug]:', { slug, error });
    // Fallback metadata in case of error
    return {
      title: 'Error Fetching Post',
      description: 'Could not load blog post details.',
    };
  }
}


export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  // Await params before accessing its properties
  const awaitedParams = await params;
  // Access slug from awaited params
  const slug = awaitedParams.slug;
  let post: BlogPost | null = null;
  let algorithmRelatedPosts: BlogPost[] = []; // Renamed from relatedPosts to avoid confusion

  // Fetch blog post data
  try {
    // Step 1: Fetch current post with detailed logging
    logger.debug(`[BlogPostPage] Fetching post by slug: ${slug}`);
    const postResponse = await getStrapiContent.blog.getPostBySlug(slug);

    // Process Blog Post Data
    if (postResponse && postResponse.data && postResponse.data.length > 0) {
      post = mapStrapiBlogPostToProps(postResponse.data[0]);
    } else {
      logger.warn(`[BlogPostPage] Post not found for slug: ${slug}`);
      return notFound(); // Return 404 if post not found
    }

    // Step 2: Fetch potential related posts in a SINGLE batch request
    // Only if we don't already have related_posts from the API or they are empty
    if (post && (!post.related_posts || post.related_posts.length === 0)) {
      logger.debug(`[BlogPostPage] No pre-populated related posts for ${slug}. Fetching potential related posts in batch.`);

      // Create query to fetch posts that share categories or tags with current post
      const categoryIDs = post.categories?.map(c => c.id) || [];
      // Assuming post.tags is an array of tag names (strings). If they are objects with IDs, adjust accordingly.
      // For this example, let's assume tags are strings and we need to fetch posts that have ANY of these tags.
      // If your Strapi setup for tags is different (e.g., tags are relations with IDs), this filter needs adjustment.
      const tagNames = post.tags || [];

      let relatedPostsFilters: any = { id: { $ne: post.id } }; // Exclude current post

      // Build $or condition for categories or tags
      const orConditions = [];
      if (categoryIDs.length > 0) {
        orConditions.push({ blog_categories: { id: { $in: categoryIDs } } });
      }
      // If tags are simple strings and you want to match any of them:
      if (tagNames.length > 0) {
         // This assumes your Strapi `blog_tags` field can be filtered by an array of tag names.
         // If tags are a relation, you'd filter by `blog_tags: { name: { $in: tagNames } }` or similar.
         // For simplicity, let's assume a direct text match for now, or adjust if tags are relational.
         // A common setup is tags being a relation, so let's assume that:
         orConditions.push({ blog_tags: { name: { $in: tagNames } } });
      }

      if (orConditions.length > 0) {
        relatedPostsFilters.$or = orConditions;
      } else {
        // If no categories or tags, perhaps fetch most recent posts as a fallback
        logger.debug(`[BlogPostPage] No categories or tags for ${slug} to find related posts. Consider fetching recent posts.`);
        // Fallback: fetch recent posts if no categories/tags (optional, could also show no related posts)
        // relatedPostsFilters = { id: { $ne: post.id } }; // Reset to just exclude current post
      }


      // Only fetch if there are conditions to filter by (or a fallback strategy is defined)
      if (orConditions.length > 0) { // Or some other condition if you have a fallback
        const allPostsResponse = await getStrapiContent.blog.getPosts({
          sort: 'publishDate:desc',
          pagination: { page: 1, pageSize: 10 }, // Fetch a small batch, e.g., 10
          filters: relatedPostsFilters,
          populate: { 
            featuredImage: true,
            author_blogs: { populate: { profilePicture: true } },
            blog_categories: true, // For findRelatedPosts
            blog_tags: true,       // For findRelatedPosts
            // Explicitly request fields needed by findRelatedPosts and mapRelatedPostsForPage/BlogPostCard
            // Removed 'reading_time' as it's not a direct Strapi field for populate.fields
            fields: ['title', 'slug', 'excerpt', 'content', 'publishDate'] 
          }
        });

        if (allPostsResponse && Array.isArray(allPostsResponse.data)) {
          const allPostsForAlgorithm = allPostsResponse.data
            .map((item: StrapiBlogPost) => mapStrapiBlogPostToProps(item)) // mapStrapiBlogPostToProps should handle the 'fields'
            .filter((p): p is BlogPost => p !== null);

          // Use our algorithm to find related posts
          if (allPostsForAlgorithm.length > 0) {
            algorithmRelatedPosts = findRelatedPosts(post, allPostsForAlgorithm, 4);
            post.related_posts = mapRelatedPostsForPage(algorithmRelatedPosts);
            logger.debug(`[BlogPostPage] Found ${algorithmRelatedPosts.length} related posts via algorithm for ${slug}.`);
          } else {
            logger.debug(`[BlogPostPage] No potential related posts found from batch fetch for ${slug}.`);
          }
        }
      }
    } else if (post) {
      logger.debug(`[BlogPostPage] Using ${post.related_posts?.length || 0} pre-populated related posts for ${slug}.`);
    }

  } catch (error: any) {
    // Use mock post data only in development if the post fetch failed
    if (process.env.NODE_ENV === 'development') {
        logger.warn(`DEV ONLY: Failed to fetch post ${slug}, using mock data. Error:`, { error: error.message, postData: mockBlogPostDataObj }); // Use renamed mock data
        post = mockBlogPostDataObj as BlogPost; // Assign mock data with cast
    } else {
        logger.error(`PROD: Failed to fetch post ${slug}. Error:`, { error: error.message });
        // In production, if the fetch fails, return 404
        return notFound();
    }
  }

  // If post is still null after error handling (e.g., prod env failure), show 404
  if (!post) {
    return notFound();
  }

  // Ensure structuredData is a string before rendering
  let structuredDataString: string | null = null;

  // Check if post has SEO data with structured data
  const postSeo = (post as any).seo;
  if (postSeo?.structuredData) {
    if (typeof postSeo.structuredData === 'string') {
      structuredDataString = postSeo.structuredData;
    } else if (typeof postSeo.structuredData === 'object') {
      try {
        structuredDataString = JSON.stringify(postSeo.structuredData);
      } catch (e) {
        logger.error("Failed to stringify structuredData object in [slug] page:", { error: e, data: postSeo.structuredData });
      }
    }
  }

  // If no structured data from SEO plugin, create a default one
  if (!structuredDataString) {
    // Get SITE_URL from mediaUtils or environment variable
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

    // Process featured image URL for structured data
    let featuredImageUrl: string | undefined = undefined;
    if (post.featured_image) {
      featuredImageUrl = getOgImageUrl(post.featured_image);

      // Log in development for debugging
      if (process.env.NODE_ENV === 'development') {
        logger.debug('Using featured image in structured data for [slug] page:', {
          original: post.featured_image,
          processed: featuredImageUrl
        });
      }
    }

    // Create default structured data for blog post
    const defaultStructuredData = {
      "@context": "https://schema.org",
      "@type": "BlogPosting",
      "headline": post.title,
      "description": post.excerpt || "",
      "image": featuredImageUrl ? [featuredImageUrl] : [],
      "datePublished": post.published_at || "",
      "dateModified": post.published_at || "",
      "author": {
        "@type": "Person",
        "name": post.author?.name || "Natural Healing Now"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Natural Healing Now",
        "logo": {
          "@type": "ImageObject",
          "url": `${siteUrl}/logo.png`
        }
      },
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `${siteUrl}/blog/${post.slug}`
      }
    };

    structuredDataString = JSON.stringify(defaultStructuredData);
  }

  return (
    // Return content directly, wrapped in a fragment
    <>
      {/* Structured Data (JSON-LD) */}
      {structuredDataString && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: structuredDataString }}
        />
      )}

      {/* Breadcrumb */}
      <div className="bg-gray-100 py-3">
        <div className="container mx-auto px-4">
          <div className="flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-emerald-600">Home</Link>
            <span className="mx-2">/</span>
            <Link href="/blog" className="hover:text-emerald-600">Blog</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-800">{post.title}</span>
          </div>
        </div>
      </div>

      {/* Blog Post Content */}
      <article className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Post Header */}
          <BlogPostHeader post={post} />

          {/* Post Content */}
          <MarkdownContent
            content={post.content}
            postId={post.id}
            postSlug={post.slug}
          />

          {/* Post Footer */}
          <BlogPostFooter post={post} />
        </div>
      </article>
    </>
  );
}
