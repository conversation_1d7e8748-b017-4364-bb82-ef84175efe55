(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6923],{3620:(e,a,s)=>{Promise.resolve().then(s.bind(s,8102))},5695:(e,a,s)=>{"use strict";var r=s(8999);s.o(r,"useParams")&&s.d(a,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(a,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(a,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(a,{useSearchParams:function(){return r.useSearchParams}})},8102:(e,a,s)=>{"use strict";s.d(a,{default:()=>u});var r=s(5155),t=s(6874),n=s.n(t),i=s(5695);function u(){let e=(0,i.usePathname)();return(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Optimization Examples"}),(0,r.jsx)("p",{className:"mb-4",children:"These examples demonstrate different approaches to reducing API calls to Strapi."}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[{href:"/optimized-example",label:"Server-Side Optimization"},{href:"/optimized-client-example",label:"Client-Side Optimization"}].map(a=>(0,r.jsx)(n(),{href:a.href,className:"px-4 py-2 rounded ".concat(e===a.href?"bg-blue-500 text-white":"bg-white text-blue-500 hover:bg-blue-100"),children:a.label},a.href))})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[6874,8441,1684,7358],()=>a(3620)),_N_E=e.O()}]);