'use client';

import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';
import { enhancedPostToApi, enhancedPutToApi } from '@/lib/enhancedApiUtils';
import { useError } from '@/contexts/ErrorContext';

/**
 * Custom hook for optimistic mutations
 * 
 * Provides optimistic updates for better UX by immediately updating the UI
 * before the server confirms the change, then rolling back if there's an error
 * 
 * @param endpoint API endpoint for the mutation
 * @param queryKey Query key to invalidate after successful mutation
 * @param optimisticUpdate Function to update the cache optimistically
 * @param options Additional mutation options
 */
export function useOptimisticMutation<TData = any, TVariables = any, TError = Error, TContext = unknown>(
  endpoint: string,
  queryKey: unknown[],
  optimisticUpdate: (variables: TVariables, queryClient: ReturnType<typeof useQueryClient>) => TContext,
  options?: Omit<UseMutationOptions<TData, TError, TVariables, TContext>, 'mutationFn' | 'onMutate' | 'onError' | 'onSettled'>
) {
  const { addErrorLog } = useError();
  const queryClient = useQueryClient();
  
  return useMutation<TData, TError, TVariables, TContext>({
    mutationFn: async (variables) => {
      // Determine if this is a POST or PUT request based on presence of id
      const hasId = variables && typeof variables === 'object' && 'id' in variables;
      
      if (hasId) {
        return await enhancedPutToApi<TData>(endpoint, variables);
      } else {
        return await enhancedPostToApi<TData>(endpoint, variables);
      }
    },
    
    // Run before the mutation to update the cache optimistically
    onMutate: async (variables) => {
      // Cancel any outgoing refetches to avoid overwriting our optimistic update
      await queryClient.cancelQueries({ queryKey });
      
      // Apply the optimistic update to the cache
      return optimisticUpdate(variables, queryClient);
    },
    
    // If the mutation fails, use the context returned from onMutate to roll back
    onError: (err, variables, context) => {
      // Log the error
      const error = err instanceof Error ? err : new Error(String(err));
      addErrorLog(error, `useOptimisticMutation(${endpoint})`);
      
      // Roll back to the previous state if we have context
      if (context) {
        queryClient.setQueryData(queryKey, context);
      }
      
      // Call the original onError if provided
      if (options?.onError) {
        options.onError(err, variables, context);
      }
    },
    
    // Always refetch after error or success
    onSettled: (data, error, variables, context) => {
      queryClient.invalidateQueries({ queryKey });
      
      // Call the original onSettled if provided
      if (options?.onSettled) {
        options.onSettled(data, error, variables, context);
      }
    },
    
    ...options,
  });
}
