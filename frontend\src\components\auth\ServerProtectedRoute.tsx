import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';
import { verifyToken } from '@/lib/serverAuth';

interface ServerProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * A server component that protects routes requiring authentication
 * Redirects to the specified path if the user is not authenticated
 * 
 * Note: This component should be used in server components only
 */
export default async function ServerProtectedRoute({ 
  children, 
  redirectTo = '/signin' 
}: ServerProtectedRouteProps) {
  // Get the JWT token from cookies
  const cookieStore = cookies();
  const token = cookieStore.get('jwt')?.value;
  
  // If no token exists, redirect to login
  if (!token) {
    redirect(redirectTo);
  }
  
  // Verify the token with Strapi
  const { user, error } = await verifyToken(token);
  
  // If token verification fails, redirect to login
  if (error || !user) {
    redirect(redirectTo);
  }
  
  // If authenticated, render the children
  return <>{children}</>;
}
