{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\n\r\n/**\r\n * Optimized middleware for Next.js that enhances performance through:\r\n * 1. Improved static asset caching with appropriate Cache-Control headers\r\n * 2. Content security improvements\r\n * 3. Static path handling\r\n */\r\nexport default function middleware(request: NextRequest) {\r\n  const { pathname } = request.nextUrl;\r\n  const response = NextResponse.next();\r\n  \r\n  // Add security headers to all responses\r\n  response.headers.set('X-Content-Type-Options', 'nosniff');\r\n  response.headers.set('X-XSS-Protection', '1; mode=block');\r\n  \r\n  // Enhanced cache control headers for static assets to improve performance\r\n  if (isStaticAsset(pathname)) {\r\n    const cacheSettings = getCacheControlForPath(pathname);\r\n    response.headers.set('Cache-Control', cacheSettings);\r\n  }\r\n  \r\n  // Special handling for image optimization API routes\r\n  if (pathname.startsWith('/_next/image')) {\r\n    // Extend cache duration for improved performance\r\n    // This is important as image optimization is a CPU-intensive task\r\n    response.headers.set('Cache-Control', 'public, max-age=86400, immutable');\r\n  }\r\n  \r\n  return response;\r\n}\r\n\r\n/**\r\n * Determine if a path is a static asset\r\n */\r\nfunction isStaticAsset(pathname: string): boolean {\r\n  return (\r\n    // Next.js static files\r\n    pathname.startsWith('/_next/static') ||\r\n    // Public folder assets\r\n    pathname.startsWith('/images/') ||\r\n    pathname.endsWith('.svg') ||\r\n    pathname.endsWith('.png') ||\r\n    pathname.endsWith('.jpg') ||\r\n    pathname.endsWith('.jpeg') ||\r\n    pathname.endsWith('.gif') ||\r\n    pathname.endsWith('.ico') ||\r\n    pathname.endsWith('.woff') ||\r\n    pathname.endsWith('.woff2') ||\r\n    pathname.endsWith('.ttf') ||\r\n    pathname.endsWith('.otf')\r\n  );\r\n}\r\n\r\n/**\r\n * Get appropriate Cache-Control header value based on the path\r\n */\r\nfunction getCacheControlForPath(pathname: string): string {\r\n  // JavaScript and CSS bundles - longer cache since they have content hashed filenames\r\n  if (pathname.match(/\\/_next\\/static\\/(chunks|css)\\/.+/)) {\r\n    return 'public, max-age=31536000, immutable'; // 1 year\r\n  }\r\n  \r\n  // Next.js build assets with hashed filenames\r\n  if (pathname.match(/\\/_next\\/static\\/.+\\.[a-z0-9]+\\.(js|css)$/)) {\r\n    return 'public, max-age=31536000, immutable'; // 1 year\r\n  }\r\n  \r\n  // Font files - rarely change\r\n  if (pathname.match(/\\.(woff|woff2|ttf|otf)$/)) {\r\n    return 'public, max-age=31536000, immutable'; // 1 year\r\n  }\r\n  \r\n  // Images in public folder\r\n  if (pathname.match(/\\/images\\/.+\\.(jpg|jpeg|png|gif|svg|webp|avif)$/)) {\r\n    return 'public, max-age=604800, stale-while-revalidate=86400'; // 1 week, stale for 1 day\r\n  }\r\n  \r\n  // Other static assets\r\n  if (pathname.match(/\\.(ico|svg|jpg|jpeg|png|gif|webp|avif)$/)) {\r\n    return 'public, max-age=86400, stale-while-revalidate=3600'; // 1 day, stale for 1 hour\r\n  }\r\n  \r\n  // Default for other static assets\r\n  return 'public, max-age=3600'; // 1 hour\r\n}\r\n\r\n// Only run middleware on specific paths\r\nexport const config = {\r\n  matcher: [\r\n    // Run on all paths except for specific ones\r\n    '/((?!api|_next/static|_next/image|favicon.ico).*)'\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAQe,SAAS,WAAW,OAAoB;IACrD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IACpC,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,wCAAwC;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IAEzC,0EAA0E;IAC1E,IAAI,cAAc,WAAW;QAC3B,MAAM,gBAAgB,uBAAuB;QAC7C,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACxC;IAEA,qDAAqD;IACrD,IAAI,SAAS,UAAU,CAAC,iBAAiB;QACvC,iDAAiD;QACjD,kEAAkE;QAClE,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACxC;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,cAAc,QAAgB;IACrC,OACE,uBAAuB;IACvB,SAAS,UAAU,CAAC,oBACpB,uBAAuB;IACvB,SAAS,UAAU,CAAC,eACpB,SAAS,QAAQ,CAAC,WAClB,SAAS,QAAQ,CAAC,WAClB,SAAS,QAAQ,CAAC,WAClB,SAAS,QAAQ,CAAC,YAClB,SAAS,QAAQ,CAAC,WAClB,SAAS,QAAQ,CAAC,WAClB,SAAS,QAAQ,CAAC,YAClB,SAAS,QAAQ,CAAC,aAClB,SAAS,QAAQ,CAAC,WAClB,SAAS,QAAQ,CAAC;AAEtB;AAEA;;CAEC,GACD,SAAS,uBAAuB,QAAgB;IAC9C,qFAAqF;IACrF,IAAI,SAAS,KAAK,CAAC,sCAAsC;QACvD,OAAO,uCAAuC,SAAS;IACzD;IAEA,6CAA6C;IAC7C,IAAI,SAAS,KAAK,CAAC,8CAA8C;QAC/D,OAAO,uCAAuC,SAAS;IACzD;IAEA,6BAA6B;IAC7B,IAAI,SAAS,KAAK,CAAC,4BAA4B;QAC7C,OAAO,uCAAuC,SAAS;IACzD;IAEA,0BAA0B;IAC1B,IAAI,SAAS,KAAK,CAAC,oDAAoD;QACrE,OAAO,wDAAwD,0BAA0B;IAC3F;IAEA,sBAAsB;IACtB,IAAI,SAAS,KAAK,CAAC,4CAA4C;QAC7D,OAAO,sDAAsD,0BAA0B;IACzF;IAEA,kCAAkC;IAClC,OAAO,wBAAwB,SAAS;AAC1C;AAGO,MAAM,SAAS;IACpB,SAAS;QACP,4CAA4C;QAC5C;KACD;AACH"}}]}