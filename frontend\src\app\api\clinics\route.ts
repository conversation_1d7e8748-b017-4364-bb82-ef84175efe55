import { NextRequest, NextResponse } from 'next/server';
import { getClinicsList } from '@/lib/api/services/clinicService'; // Adjust path as needed

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  const page = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);
  const categorySlug = searchParams.get('category') || undefined;
  const specialtySlug = searchParams.get('specialty') || undefined;
  const conditionSlug = searchParams.get('condition') || undefined;
  const searchTerm = searchParams.get('search') || undefined;

  // Validate page and pageSize to prevent excessively large requests
  if (isNaN(page) || page < 1) {
    return NextResponse.json({ error: 'Invalid page number' }, { status: 400 });
  }
  if (isNaN(pageSize) || pageSize < 1 || pageSize > 100) { // Max pageSize e.g. 100
    return NextResponse.json({ error: 'Invalid page size (must be between 1 and 100)' }, { status: 400 });
  }

  try {
    // This uses the server-side cached function
    const clinicsResponse = await getClinicsList(
      page,
      pageSize,
      categorySlug,
      specialtySlug,
      conditionSlug,
      searchTerm
    );

    if (clinicsResponse.error) {
      const status = clinicsResponse.error?.status || 500;
      const message = clinicsResponse.error?.message || 'Failed to fetch clinics list';
      return NextResponse.json({ error: message }, { status });
    }

    return NextResponse.json(clinicsResponse);
  } catch (error) {
    console.error('Error fetching clinics list in API route:', error);
    const message = error instanceof Error ? error.message : 'Failed to fetch clinics list';
    return NextResponse.json({ error: message }, { status: 500 });
  }
}
