/**
 * Strapi command to import blog posts from CSV
 * 
 * This command imports blog posts from a CSV file into the Strapi database.
 * It uses the Strapi entity service to create or update blog post entries.
 * 
 * Usage: 
 * 1. Start Strapi console: npm run strapi console
 * 2. Run: await require('./scripts/import-blog-posts-command.js')(strapi)
 */

const importBlogPosts = require('./import-blog-posts');

module.exports = async (strapi) => {
  console.log('Starting blog posts import from CSV...');
  
  try {
    // Install dependencies if not already installed
    try {
      require('csv-parser');
      require('axios');
    } catch (e) {
      console.log('Installing dependencies...');
      const { execSync } = require('child_process');
      execSync('npm install csv-parser axios', { stdio: 'inherit' });
      console.log('Dependencies installed successfully.');
    }
    
    // Run the import
    const result = await importBlogPosts(strapi);
    console.log('Import completed successfully!');
    return result;
  } catch (error) {
    console.error('Import failed:', error);
    throw error;
  }
};
