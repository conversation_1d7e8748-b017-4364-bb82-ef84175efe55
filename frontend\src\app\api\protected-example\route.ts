import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { verifyToken } from '@/lib/serverAuth';

/**
 * Protected API route example
 * This route is protected by server-side authentication
 */
export async function GET(request: NextRequest) {
  // Get the JWT token from cookies
  const cookieStore = cookies();
  const token = cookieStore.get('jwt')?.value;
  
  // If no token exists, return unauthorized
  if (!token) {
    return NextResponse.json(
      { error: 'Unauthorized - No token provided' },
      { status: 401 }
    );
  }
  
  // Verify the token with Strapi
  const { user, error } = await verifyToken(token);
  
  // If token verification fails, return unauthorized
  if (error || !user) {
    return NextResponse.json(
      { error: 'Unauthorized - Invalid token' },
      { status: 401 }
    );
  }
  
  // If authenticated, return protected data
  return NextResponse.json({
    message: 'This is protected data',
    user: {
      id: user.id,
      username: user.username,
      email: user.email
    }
  });
}
