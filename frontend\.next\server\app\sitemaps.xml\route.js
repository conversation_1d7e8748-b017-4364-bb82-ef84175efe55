(()=>{var e={};e.id=9796,e.ids=[9796],e.modules={151:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>u,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var n={};s.r(n),s.d(n,{GET:()=>m});var o=s(96559),r=s(48088),a=s(37719);let i=process.env.NEXT_PUBLIC_SITE_URL||"https://www.naturalhealingnow.com",p=i.endsWith("/")?i.slice(0,-1):i;async function m(e){try{console.log("Redirecting sitemaps.xml to sitemap-index.xml...");let e=`<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${p}/sitemap.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${p}/sitemap-blog.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${p}/sitemap-clinics.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${p}/sitemap-practitioners.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
</sitemapindex>`;return new Response(e,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600","X-Content-Type-Options":"nosniff"}})}catch(e){return console.error("Error generating sitemaps.xml:",e),new Response(`<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${p}/sitemap.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
</sitemapindex>`,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"no-cache","X-Content-Type-Options":"nosniff"}})}}let l=new o.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/sitemaps.xml/route",pathname:"/sitemaps.xml",filename:"route",bundlePath:"app/sitemaps.xml/route"},resolvedPagePath:"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\sitemaps.xml\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:u}=l;function x(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,s)=>{"use strict";e.exports=s(44870)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[7719],()=>s(151));module.exports=n})();