import Layout from '@/components/layout/Layout';
import Link from 'next/link';
import { FiSearch, FiTag } from 'react-icons/fi';

// This will be replaced with a real API call to fetch tags data
const mockTags = [
  {
    name: 'stress relief',
    slug: 'stress-relief',
    post_count: 1
  },
  {
    name: 'acupuncture',
    slug: 'acupuncture',
    post_count: 1
  },
  {
    name: 'holistic health',
    slug: 'holistic-health',
    post_count: 3
  },
  {
    name: 'herbal remedies',
    slug: 'herbal-remedies',
    post_count: 1
  },
  {
    name: 'digestive health',
    slug: 'digestive-health',
    post_count: 1
  },
  {
    name: 'mental health',
    slug: 'mental-health',
    post_count: 1
  },
  {
    name: 'nutrition',
    slug: 'nutrition',
    post_count: 1
  },
  {
    name: 'natural medicine',
    slug: 'natural-medicine',
    post_count: 2
  },
  {
    name: 'gut health',
    slug: 'gut-health',
    post_count: 2
  },
  {
    name: 'relaxation',
    slug: 'relaxation',
    post_count: 1
  },
  {
    name: 'brain health',
    slug: 'brain-health',
    post_count: 1
  },
  {
    name: 'diet',
    slug: 'diet',
    post_count: 1
  }
];

export default function BlogTagsPage() {
  return (
    <>
      {/* Page Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Blog Tags</h1>
          <p className="text-lg max-w-3xl">
            Browse articles by topic to find the information you're looking for.
          </p>
        </div>
      </div>

      {/* Search Section */}
      <div className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-md mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Search tags"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              />
              <FiSearch className="absolute left-3 top-3 text-gray-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Tags Grid */}
      <div className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            All Tags
          </h2>

          <div className="flex flex-wrap gap-4">
            {mockTags.map((tag, index) => {
              // Format tag name for display (capitalize each word)
              const formattedTagName = tag.name
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');

              return (
                <Link
                  key={index}
                  href={`/blog/tags/${tag.slug}`}
                  className="bg-white border border-gray-200 hover:border-emerald-300 hover:bg-emerald-50 px-4 py-3 rounded-lg text-gray-700 flex items-center group"
                >
                  <FiTag className="mr-2 text-emerald-500 group-hover:text-emerald-600" />
                  <div>
                    <span className="font-medium block">{formattedTagName}</span>
                    <span className="text-xs text-gray-500">
                      {tag.post_count} {tag.post_count === 1 ? 'article' : 'articles'}
                    </span>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </div>

      {/* Popular Tags Section */}
      <div className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            Popular Tags
          </h2>

          <div className="flex flex-wrap gap-3">
            {mockTags
              .sort((a, b) => b.post_count - a.post_count)
              .slice(0, 6)
              .map((tag, index) => {
                // Format tag name for display (capitalize each word)
                const formattedTagName = tag.name
                  .split(' ')
                  .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(' ');

                return (
                  <Link
                    key={index}
                    href={`/blog/tags/${tag.slug}`}
                    className="bg-emerald-50 text-emerald-700 px-4 py-2 rounded-full hover:bg-emerald-100 flex items-center"
                  >
                    <FiTag className="mr-2" />
                    {formattedTagName} ({tag.post_count})
                  </Link>
                );
              })}
          </div>
        </div>
      </div>

      {/* Back to Blog Link */}
      <div className="py-8 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <Link
            href="/blog"
            className="text-emerald-600 hover:text-emerald-700 font-medium"
          >
            ← Back to Blog
          </Link>
        </div>
      </div>
    </>
  );
}
