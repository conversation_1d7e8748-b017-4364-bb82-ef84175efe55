"use client";

import LazyImage from '@/components/shared/LazyImage';
import Link from 'next/link';
import { format } from 'date-fns';
import { <PERSON><PERSON>lock, FiCalendar, FiUser } from 'react-icons/fi';
import { sanitizeUrl } from '@/lib/mediaUtils';

interface FeaturedPostProps {
  post: {
    id: string;
    title: string;
    slug: string;
    excerpt?: string | null;
    featured_image?: string | null;
    publish_date: string;
    content?: string;
    reading_time?: number; // Added reading_time field
    isFeatured?: boolean;
    view_count?: number;
    author?: {
      name: string;
      slug: string;
      profile_picture?: string | null;
    } | null;
  };
  badgeType?: 'featured' | 'popular' | 'recent';
}

const FeaturedPost = ({ post, badgeType = 'featured' }: FeaturedPostProps) => {
  // Use the imported sanitizeUrl function from mediaUtils

  const imageSrc = sanitizeUrl(post.featured_image);
  const hasImage = !!post.featured_image;
  const authorImageSrc = sanitizeUrl(post.author?.profile_picture);
  const hasAuthorImage = !!post.author?.profile_picture;

  // Use pre-calculated reading time if available, otherwise calculate it
  const calculateReadingTime = (content: string): number => {
    const wordsPerMinute = 200;
    const wordCount = content?.split(/\s+/)?.length || 0;
    return Math.max(1, Math.ceil(wordCount / wordsPerMinute));
  };

  // Use pre-calculated reading time if available, otherwise calculate from content or default to 2 min
  const readingTime = post.reading_time || (post.content ? calculateReadingTime(post.content) : 2);

  // Ensure authorImageSrc is a valid URL
  const validAuthorImageSrc = authorImageSrc && (
    authorImageSrc.startsWith('http') ||
    authorImageSrc.startsWith('/') ||
    authorImageSrc.startsWith('data:') ||
    // Handle relative URLs that might not start with a slash
    (typeof window !== 'undefined' && window.location.origin && !authorImageSrc.startsWith('http'))
  ) ? authorImageSrc : '';

  // Always debug author image URL in development to help troubleshoot
  if (process.env.NODE_ENV === 'development') {
    console.log(`Featured post author image for "${post.title}":`, {
      author: post.author,
      original: post.author?.profile_picture,
      processed: authorImageSrc,
      valid: validAuthorImageSrc,
      hasAuthorImage: hasAuthorImage
    });
  }

  // Additional check to ensure the URL is valid
  const isValidUrl = (url: string) => {
    try {
      return url && (
        url.startsWith('http') ||
        url.startsWith('/') ||
        url.startsWith('data:')
      );
    } catch (e) {
      return false;
    }
  };

  // Format the publish date
  const formattedDate = format(new Date(post.publish_date), 'MMMM d, yyyy');

  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden transition-transform hover:shadow-lg">
      <div className="flex flex-col md:flex-row">
        <div className="md:w-1/2 relative h-64 md:h-auto">
          {hasImage ? (
            <LazyImage
              src={imageSrc}
              alt={post.title || 'Featured post image'}
              width={800} // Used for aspect ratio hint
              height={600} // Used for aspect ratio hint
              fillContainer={true}
              className="object-cover" // w-full h-full handled by fillContainer
              sizes="(max-width: 768px) 100vw, 50vw"
              priority={true} // Set priority directly
              showPlaceholder={true}
            />
          ) : (
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-100 to-teal-200 flex items-center justify-center">
              <span className="text-emerald-700 font-semibold text-2xl opacity-50">
                {post.title.charAt(0)}
              </span>
            </div>
          )}
          <div className={`absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium ${
            badgeType === 'featured' ? 'bg-emerald-600 text-white' :
            badgeType === 'popular' ? 'bg-amber-500 text-white' :
            'bg-blue-500 text-white'
          }`}>
            {badgeType === 'featured' ? 'Featured Post' :
             badgeType === 'popular' ? 'Popular Post' :
             'Latest Post'}
          </div>
        </div>

        <div className="md:w-1/2 p-6 md:p-8 flex flex-col">
          <div className="flex flex-wrap gap-3 text-sm text-gray-500 mb-3">
            <div className="flex items-center">
              <FiCalendar className="mr-1" />
              <span>{formattedDate}</span>
            </div>
            <div className="flex items-center">
              <FiClock className="mr-1" />
              <span>{readingTime} min read</span>
            </div>
          </div>

          <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-3">
            <Link href={`/blog/${post.slug}`} className="hover:text-emerald-600">
              {post.title}
            </Link>
          </h2>

          {post.excerpt && (
            <p className="text-gray-600 mb-6 line-clamp-3 md:line-clamp-4">{post.excerpt}</p>
          )}

          <div className="mt-auto flex items-center justify-between">
            {post.author && (
              <div className="flex items-center">
                <div className="relative h-10 w-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-200 shadow-sm">
                  {validAuthorImageSrc && isValidUrl(validAuthorImageSrc) ? (
                    <LazyImage
                      src={validAuthorImageSrc}
                      alt={post.author.name || 'Author image'}
                      width={40} // Used for aspect ratio hint
                      height={40} // Used for aspect ratio hint
                      fillContainer={true}
                      className="object-cover rounded-full" // fillContainer will make it fill the parent div
                      sizes="40px" // Still useful for next/image optimization
                      priority={false} // Explicitly false for non-critical images
                      showPlaceholder={true}
                    />
                  ) : (
                    <div className="absolute inset-0 bg-emerald-100 flex items-center justify-center">
                      <FiUser className="text-emerald-700 text-lg" />
                    </div>
                  )}
                </div>
                <div className="text-sm">
                  <span className="block text-xs text-gray-500 mb-0.5">Written by</span>
                  <Link
                    href={`/blog/authors/${post.author.slug}`}
                    className="font-medium text-gray-800 hover:text-emerald-600"
                  >
                    {post.author.name}
                  </Link>
                </div>
              </div>
            )}

            <Link
              href={`/blog/${post.slug}`}
              className="inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm"
            >
              Read Article <span className="ml-1">→</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturedPost;
