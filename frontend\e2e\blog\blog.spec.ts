import { test, expect } from '@playwright/test';

test.describe('Blog Page', () => {
  test('should load the blog page successfully', async ({ page }) => {
    await page.goto('/blog');
    
    // Check that the page title is correct
    await expect(page).toHaveTitle(/Blog/);
    
    // Check that main sections are visible
    await expect(page.getByRole('heading', { name: /Latest Articles/i, exact: false })).toBeVisible();
    await expect(page.getByRole('heading', { name: /Popular Posts/i, exact: false })).toBeVisible();
  });

  test('should filter blog posts by category', async ({ page }) => {
    await page.goto('/blog');
    
    // Click on a category filter
    const categoryLinks = page.getByRole('link', { name: /category/i });
    if (await categoryLinks.count() > 0) {
      await categoryLinks.first().click();
      
      // Check that we've navigated to the category page
      await expect(page).toHaveURL(/\/blog\/categories\//);
    } else {
      test.skip('No category links found to test');
    }
  });

  test('should navigate to a blog post detail page', async ({ page }) => {
    await page.goto('/blog');
    
    // Click on the first blog post
    const blogPostLinks = page.locator('article a').filter({ hasText: /./ });
    if (await blogPostLinks.count() > 0) {
      await blogPostLinks.first().click();
      
      // Check that we've navigated to the blog post detail page
      await expect(page).toHaveURL(/\/blog\/.+/);
      
      // Check that the blog post content is displayed
      await expect(page.locator('article')).toBeVisible();
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
    } else {
      test.skip('No blog posts found to test');
    }
  });

  test('should search for blog posts', async ({ page }) => {
    await page.goto('/blog');
    
    // Fill the search input
    await page.getByPlaceholder(/Search/i).fill('health');
    
    // Press Enter to submit the search
    await page.keyboard.press('Enter');
    
    // Check that search results are displayed
    await expect(page.getByText(/Search Results/i)).toBeVisible();
  });
});
