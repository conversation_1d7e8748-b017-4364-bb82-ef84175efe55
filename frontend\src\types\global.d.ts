// Global type definitions for the application

// Extend the NodeJS namespace to include our environment variables
declare namespace NodeJS {
  interface ProcessEnv {
    // API URLs
    NEXT_PUBLIC_API_URL: string;
    STRAPI_API_TOKEN: string;

    // Image hostname for Next.js image optimization
    IMAGE_HOSTNAME: string;

    // Site URL for absolute URLs
    SITE_URL: string;

    // Preview secret for draft content
    PREVIEW_SECRET: string;

    // Node environment
    NODE_ENV: 'development' | 'production' | 'test';
  }
}

// Extend the Window interface for any global browser variables
interface Window {
  // Add any browser-specific globals here
}
