/**
 * Type definitions for Strapi API responses
 */

/**
 * Base Strapi media object
 */
export interface StrapiMedia {
  id?: number;
  name?: string;
  alternativeText?: string | null;
  caption?: string | null;
  width?: number | null;
  height?: number | null;
  formats?: {
    thumbnail?: {
      name?: string;
      hash?: string;
      ext?: string;
      mime?: string;
      width?: number;
      height?: number;
      size?: number;
      path?: string | null;
      url?: string;
    };
    [key: string]: any;
  } | null;
  hash?: string;
  ext?: string;
  mime?: string;
  size?: number;
  url: string;
  previewUrl?: string | null;
  provider?: string;
  provider_metadata?: any | null;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Strapi media with data wrapper (v4 style)
 */
export interface StrapiMediaData {
  data?: {
    id?: number;
    attributes?: StrapiMedia;
  } | null;
  url?: string; // Direct URL (v5 style)
}

/**
 * Strapi SEO component
 */
export interface StrapiSEO {
  id?: number;
  metaTitle?: string;
  metaDescription?: string;
  metaImage?: StrapiMediaData;
  metaRobots?: string;
  keywords?: string;
  metaViewport?: string;
  canonicalURL?: string;
  structuredData?: string;
  openGraph?: {
    title?: string;
    description?: string;
    type?: string;
    url?: string;
    image?: StrapiMediaData;
    siteName?: string;
  };
}

/**
 * Strapi author
 */
export interface StrapiAuthor {
  id: number | string;
  name: string;
  slug: string;
  email?: string | null;
  website?: string | null;
  bio?: string | null;
  profilePicture?: StrapiMediaData;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
}

/**
 * Strapi blog category
 */
export interface StrapiBlogCategory {
  id: number | string;
  name: string;
  slug: string;
  description?: string | null;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
}

/**
 * Strapi blog tag
 */
export interface StrapiBlogTag {
  id: number | string;
  name: string;
  slug: string;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
}

/**
 * Strapi blog post
 */
export interface StrapiBlogPost {
  id: number | string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string | null;
  publishDate?: string;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
  isFeatured?: boolean;
  view_count?: number; // Added view_count
  featuredImage?: StrapiMediaData;
  seo?: StrapiSEO;
  author_blogs?: StrapiAuthor[];
  blog_categories?: StrapiBlogCategory[];
  blog_tags?: StrapiBlogTag[];
  related_posts?: StrapiBlogPost[];
}

/**
 * Strapi address component
 */
export interface StrapiAddress {
  id?: number;
  city?: string;
  stateProvince?: string;
  country?: 'USA' | 'Canada';
  postalCode?: string;
  streetAddress?: string;
  streetAddress1?: string;
}

/**
 * Strapi contact information component
 */
export interface StrapiContactInfo {
  id?: number;
  email?: string;
  phoneNumber?: string;
  websiteUrl?: string;
  socialLinks?: {
    id?: number;
    platform?: string;
    url?: string;
    icon?: string;
  }[];
}

/**
 * Strapi clinic
 */
export interface StrapiClinic {
  id: number | string;
  name: string;
  slug: string;
  description?: string | null;
  isVerified?: boolean;
  isFeatured?: boolean;
  logo?: StrapiMediaData;
  featuredImage?: StrapiMediaData;
  address?: StrapiAddress;
  contactInfo?: StrapiContactInfo;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
  seo?: StrapiSEO;
}

/**
 * Strapi practitioner
 */
export interface StrapiPractitioner {
  id: number | string;
  name: string;
  slug: string;
  bio?: string | null;
  isVerified?: boolean;
  isFeatured?: boolean;
  isActive?: boolean;
  profilePicture?: StrapiMediaData;
  address?: StrapiAddress;
  contactInfo?: StrapiContactInfo;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
  seo?: StrapiSEO;
}

/**
 * Strapi category
 */
export interface StrapiCategory {
  id: number | string;
  name: string;
  slug: string;
  description?: string | null;
  featuredImage?: StrapiMediaData;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
  seo?: StrapiSEO;
}

/**
 * Strapi pagination metadata
 */
export interface StrapiPagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

/**
 * Strapi API response with pagination
 */
export interface StrapiResponse<T> {
  data: T[];
  meta: {
    pagination: StrapiPagination;
  };
}

/**
 * Strapi API response for a single entity
 */
export interface StrapiSingleResponse<T> {
  data: T;
  meta: object;
}

/**
 * Strapi API error
 */
export interface StrapiError {
  status: number;
  name: string;
  message: string;
  details?: any;
}
