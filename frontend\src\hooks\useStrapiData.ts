'use client';

import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { AxiosRequestConfig } from 'axios';
import { fetchWithDeduplication } from '@/lib/clientFetch';
import { generateQueryKey } from '@/lib/clientFetch';
import { CACHE_TIMES } from '@/lib/reactQuery';
import { useError } from '@/contexts/ErrorContext';

/**
 * Content type mapping for cache times
 */
type ContentType = keyof typeof CACHE_TIMES | 'default';

/**
 * Get cache configuration for a specific content type
 */
function getCacheConfig(contentType: ContentType) {
  if (contentType === 'default') {
    return {
      staleTime: CACHE_TIMES.semiStatic.staleTime,
      gcTime: CACHE_TIMES.semiStatic.gcTime,
    };
  }
  
  return {
    staleTime: CACHE_TIMES[contentType].staleTime,
    gcTime: CACHE_TIMES[contentType].gcTime,
  };
}

/**
 * Enhanced hook for fetching data from Strapi with React Query
 * 
 * @param endpoint - The Strapi API endpoint to fetch from
 * @param params - Query parameters to send with the request
 * @param contentType - Content type for cache configuration
 * @param options - Additional React Query options
 * @returns The React Query result object
 */
export function useStrapiData<TData = any, TError = Error>(
  endpoint: string,
  params?: Record<string, any>,
  contentType: ContentType = 'default',
  options?: Omit<UseQueryOptions<TData, TError, TData>, 'queryKey' | 'queryFn'>
) {
  const { addErrorLog } = useError();
  const queryKey = generateQueryKey(endpoint, params);
  const { staleTime, gcTime } = getCacheConfig(contentType);
  
  return useQuery<TData, TError>({
    queryKey,
    queryFn: async () => {
      try {
        const axiosOptions: AxiosRequestConfig = {};
        if (params) {
          axiosOptions.params = params;
        }
        
        return await fetchWithDeduplication<TData>(endpoint, axiosOptions);
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        addErrorLog(error, `useStrapiData(${endpoint})`);
        throw error;
      }
    },
    staleTime,
    gcTime,
    ...options,
  });
}

/**
 * Hook for fetching featured clinics
 */
export function useFeaturedClinics(options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiData(
    '/clinics',
    {
      filters: { isFeatured: { $eq: true } },
      populate: '*'
    },
    'clinics',
    options
  );
}

/**
 * Hook for fetching featured practitioners
 */
export function useFeaturedPractitioners(options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiData(
    '/practitioners',
    {
      filters: { isFeatured: { $eq: true } },
      populate: '*'
    },
    'practitioners',
    options
  );
}

/**
 * Hook for fetching categories
 */
export function useCategories(pageSize = 4, options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiData(
    '/categories',
    {
      pagination: { pageSize },
      populate: '*'
    },
    'categories',
    options
  );
}

/**
 * Hook for fetching featured blog posts
 */
export function useFeaturedBlogPosts(options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiData(
    '/blog-posts',
    {
      filters: { isFeatured: { $eq: true } },
      sort: 'publishDate:desc',
      pagination: { page: 1, pageSize: 3 },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        }
      }
    },
    'blogPosts',
    options
  );
}

/**
 * Hook for fetching latest blog posts
 */
export function useLatestBlogPosts(pageSize = 3, options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiData(
    '/blog-posts',
    {
      sort: 'publishDate:desc',
      pagination: { page: 1, pageSize },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        }
      }
    },
    'blogPosts',
    options
  );
}

/**
 * Hook for fetching popular blog posts
 */
export function usePopularBlogPosts(pageSize = 3, options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiData(
    '/blog-posts',
    {
      sort: 'viewCount:desc',
      pagination: { page: 1, pageSize },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        }
      }
    },
    'blogPosts',
    options
  );
}

/**
 * Hook for fetching a single blog post by slug
 */
export function useBlogPost(slug: string, options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiData(
    '/blog-posts',
    {
      filters: { slug: { $eq: slug } },
      populate: {
        featuredImage: true,
        author_blogs: {
          populate: {
            profilePicture: true
          }
        },
        categories: true
      }
    },
    'blogPosts',
    options
  );
}

/**
 * Hook for fetching a single clinic by slug
 */
export function useClinic(slug: string, options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiData(
    '/clinics',
    {
      filters: { slug: { $eq: slug } },
      populate: '*'
    },
    'clinics',
    options
  );
}

/**
 * Hook for fetching a single practitioner by slug
 */
export function usePractitioner(slug: string, options?: Omit<UseQueryOptions<any, Error, any>, 'queryKey' | 'queryFn'>) {
  return useStrapiData(
    '/practitioners',
    {
      filters: { slug: { $eq: slug } },
      populate: '*'
    },
    'practitioners',
    options
  );
}
