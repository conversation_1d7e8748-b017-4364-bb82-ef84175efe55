'use client';

import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { AxiosRequestConfig } from 'axios';
import { enhancedFetchFromApi } from '@/lib/enhancedApiUtils';
import { enhancedPostToApi } from '@/lib/enhancedApiUtils';
import { useError } from '@/contexts/ErrorContext';

/**
 * Custom hook for fetching data using React Query
 * 
 * @param endpoint API endpoint to fetch data from
 * @param params Request parameters
 * @param options Additional React Query options
 * @returns Query result with data, loading state, and error
 */
export function useApiQuery<TData = any, TError = Error>(
  endpoint: string,
  params?: AxiosRequestConfig['params'],
  options?: Omit<UseQueryOptions<TData, TError, TData>, 'queryKey' | 'queryFn'>
) {
  const { addErrorLog } = useError();
  
  return useQuery<TData, TError>({
    queryKey: [endpoint, params],
    queryFn: async () => {
      try {
        const options: AxiosRequestConfig = {};
        if (params) {
          options.params = params;
        }
        
        return await enhancedFetchFromApi<TData>(endpoint, options, false);
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        addErrorLog(error, `useApiQuery(${endpoint})`);
        throw error;
      }
    },
    ...options,
  });
}

/**
 * Custom hook for mutations (POST requests) using React Query
 * 
 * @param endpoint API endpoint to post data to
 * @param options Additional React Query mutation options
 * @returns Mutation result with mutate function and status
 */
export function useApiMutation<TData = any, TVariables = any, TError = Error>(
  endpoint: string,
  options?: Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'>
) {
  const { addErrorLog } = useError();
  const queryClient = useQueryClient();
  
  return useMutation<TData, TError, TVariables>({
    mutationFn: async (variables) => {
      try {
        return await enhancedPostToApi<TData>(endpoint, variables);
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        addErrorLog(error, `useApiMutation(${endpoint})`);
        throw error;
      }
    },
    // Invalidate queries by default after successful mutation
    onSuccess: (data, variables, context) => {
      // Call the original onSuccess if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context);
      }
      
      // Invalidate the endpoint query to refetch data
      queryClient.invalidateQueries({ queryKey: [endpoint] });
    },
    ...options,
  });
}
