{"kind": "collectionType", "collectionName": "practitioners", "info": {"singularName": "practitioner", "pluralName": "practitioners", "displayName": "Practitioner", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "bio": {"type": "richtext"}, "qualifications": {"type": "richtext"}, "profilePicture": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "contactInfo": {"type": "component", "repeatable": false, "component": "contact.contact-information"}, "isActive": {"type": "boolean", "default": true, "required": true}, "isFeatured": {"type": "boolean", "default": false, "required": true}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "videoEmbed": {"type": "customField", "customField": "plugin::oembed.oembed"}, "clinics": {"type": "relation", "relation": "manyToMany", "target": "api::clinic.clinic", "mappedBy": "practitioners"}, "services": {"type": "relation", "relation": "manyToMany", "target": "api::service.service", "inversedBy": "practitioners"}, "specialties": {"type": "relation", "relation": "manyToMany", "target": "api::specialty.specialty", "inversedBy": "practitioners"}, "conditions": {"type": "relation", "relation": "manyToMany", "target": "api::condition.condition", "inversedBy": "practitioners"}, "categories": {"type": "relation", "relation": "manyToMany", "target": "api::category.category", "mappedBy": "practitioners"}, "isVerified": {"type": "boolean", "default": false}, "paid": {"type": "boolean", "default": false}}}