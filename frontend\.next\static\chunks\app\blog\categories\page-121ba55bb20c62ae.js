(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3648],{973:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var n=r(5155),l=r(8126),a=r.n(l),i=r(2115),o=r(2112);function u(e){var t,r,l,u,c,s,d,f,m,p,v,h,g,b;let{seo:y,defaultTitle:j="Natural Healing Now - Holistic Health Directory",defaultDescription:x="Find holistic health practitioners and clinics near you. Connect with natural healing professionals to support your wellness journey.",defaultOgImage:w="",pageType:O="website"}=e,P=(null==y?void 0:y.metaTitle)||j,N=(null==y?void 0:y.metaDescription)||x,S=(null==y?void 0:y.metaRobots)||"index, follow",E=(null==y?void 0:y.canonicalURL)||"",k=(null==y?void 0:y.structuredData)||"",R=null==y||null==(t=y.metaSocial)?void 0:t.find(e=>"Facebook"===e.socialNetwork),_=null==y||null==(r=y.metaSocial)?void 0:r.find(e=>"Twitter"===e.socialNetwork),A=(null==R||null==(c=R.image)||null==(u=c.data)||null==(l=u.attributes)?void 0:l.url)||(null==y||null==(f=y.metaImage)||null==(d=f.data)||null==(s=d.attributes)?void 0:s.url)||w,C=(0,o.Rb)(A),D=(null==_||null==(v=_.image)||null==(p=v.data)||null==(m=p.attributes)?void 0:m.url)||(null==y||null==(b=y.metaImage)||null==(g=b.data)||null==(h=g.attributes)?void 0:h.url)||w,M=(0,o.Rb)(D),L=(null==R?void 0:R.title)||P,T=(null==R?void 0:R.description)||N,F=(null==_?void 0:_.title)||P,H=(null==_?void 0:_.description)||N,[W,z]=(0,i.useState)(null);return(0,i.useEffect)(()=>{if(k)try{let e=JSON.parse(k);z(e)}catch(e){console.error("Error parsing structured data:",e)}},[k]),(0,n.jsxs)(a(),{children:[(0,n.jsx)("title",{children:P}),(0,n.jsx)("meta",{name:"description",content:N}),S&&(0,n.jsx)("meta",{name:"robots",content:S}),E&&(0,n.jsx)("link",{rel:"canonical",href:E}),(0,n.jsx)("meta",{property:"og:type",content:O}),(0,n.jsx)("meta",{property:"og:title",content:L}),(0,n.jsx)("meta",{property:"og:description",content:T}),C&&(0,n.jsx)("meta",{property:"og:image",content:C}),(0,n.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,n.jsx)("meta",{name:"twitter:title",content:F}),(0,n.jsx)("meta",{name:"twitter:description",content:H}),M&&(0,n.jsx)("meta",{name:"twitter:image",content:M}),W&&(0,n.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(W)}})]})}},2461:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(2115);let l=n.forwardRef(function(e,t){let{title:r,titleId:l,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},a),r?n.createElement("title",{id:l},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},2596:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=function(){for(var e,t,r=0,n="",l=arguments.length;r<l;r++)(e=arguments[r])&&(t=function e(t){var r,n,l="";if("string"==typeof t||"number"==typeof t)l+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(l&&(l+=" "),l+=n)}else for(n in t)t[n]&&(l&&(l+=" "),l+=n);return l}(e))&&(n&&(n+=" "),n+=t);return n}},2847:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,973)),Promise.resolve().then(r.bind(r,5922)),Promise.resolve().then(r.bind(r,8400))},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>s});var n=r(2115),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=n.createContext&&n.createContext(l),i=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,l,a;n=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(l))in n?Object.defineProperty(n,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(e){return t=>n.createElement(d,o({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:l,size:a,title:u}=e,s=function(e,t){if(null==e)return{};var r,n,l=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(l[r]=e[r])}return l}(e,i),d=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,l,s,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),u&&n.createElement("title",null,u),e.children)};return void 0!==a?n.createElement(a.Consumer,null,e=>t(e)):t(l)}},5695:(e,t,r)=>{"use strict";var n=r(8999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},5922:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(5155),l=r(2461),a=r(9416),i=r(2596),o=r(6874),u=r.n(o),c=r(5695);let s=(e,t)=>t<=7?Array.from({length:t},(e,t)=>t+1):e<=3?[1,2,3,"...",t-1,t]:e>=t-2?[1,2,"...",t-2,t-1,t]:[1,"...",e-1,e,e+1,"...",t];function d(e){let{totalPages:t,currentPage:r}=e,l=(0,c.usePathname)(),a=(0,c.useSearchParams)(),i=void 0!==r?r:Number(a.get("page"))||1,o=e=>{let t=new URLSearchParams(a);return t.set("page",e.toString()),"".concat(l,"?").concat(t.toString())},u=s(i,t);return t<=1?null:(0,n.jsxs)("div",{className:"inline-flex",children:[(0,n.jsx)(m,{direction:"left",href:o(i-1),isDisabled:i<=1}),(0,n.jsx)("div",{className:"flex -space-x-px",children:u.map((e,t)=>{let r;return 0===t&&(r="first"),t===u.length-1&&(r="last"),1===u.length&&(r="single"),"..."===e&&(r="middle"),(0,n.jsx)(f,{href:o(e),page:e,position:r,isActive:i===e},"".concat(e,"-").concat(t))})}),(0,n.jsx)(m,{direction:"right",href:o(i+1),isDisabled:i>=t})]})}function f(e){let{page:t,href:r,isActive:l,position:a}=e,o=(0,i.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===a||"single"===a,"rounded-r-md":"last"===a||"single"===a,"z-10 bg-emerald-600 border-emerald-600 text-white":l,"hover:bg-gray-100":!l&&"middle"!==a,"text-gray-300 pointer-events-none":"middle"===a});return l||"middle"===a?(0,n.jsx)("div",{className:o,children:t}):(0,n.jsx)(u(),{href:r,className:o,children:t})}function m(e){let{href:t,direction:r,isDisabled:o}=e,c=(0,i.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":o,"hover:bg-gray-100":!o,"mr-2 md:mr-4":"left"===r,"ml-2 md:ml-4":"right"===r}),s="left"===r?(0,n.jsx)(l.A,{className:"w-4"}):(0,n.jsx)(a.A,{className:"w-4"});return o?(0,n.jsx)("div",{className:c,children:s}):(0,n.jsx)(u(),{className:c,href:t,children:s})}},8126:(e,t)=>{"use strict";function r(){return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8400:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(5155),l=r(2115),a=r(5695),i=r(351);function o(e){var t;let{placeholder:r,paramName:o="query",icon:u}=e,c=(0,a.useSearchParams)(),s=(0,a.usePathname)(),{replace:d}=(0,a.useRouter)(),f=function(e,t,r){var n=this,a=(0,l.useRef)(null),i=(0,l.useRef)(0),o=(0,l.useRef)(null),u=(0,l.useRef)([]),c=(0,l.useRef)(),s=(0,l.useRef)(),d=(0,l.useRef)(e),f=(0,l.useRef)(!0);d.current=e;var m="undefined"!=typeof window,p=!t&&0!==t&&m;if("function"!=typeof e)throw TypeError("Expected a function");t=+t||0;var v=!!(r=r||{}).leading,h=!("trailing"in r)||!!r.trailing,g="maxWait"in r,b="debounceOnServer"in r&&!!r.debounceOnServer,y=g?Math.max(+r.maxWait||0,t):null;return(0,l.useEffect)(function(){return f.current=!0,function(){f.current=!1}},[]),(0,l.useMemo)(function(){var e=function(e){var t=u.current,r=c.current;return u.current=c.current=null,i.current=e,s.current=d.current.apply(r,t)},r=function(e,t){p&&cancelAnimationFrame(o.current),o.current=p?requestAnimationFrame(e):setTimeout(e,t)},l=function(e){if(!f.current)return!1;var r=e-a.current;return!a.current||r>=t||r<0||g&&e-i.current>=y},j=function(t){return o.current=null,h&&u.current?e(t):(u.current=c.current=null,s.current)},x=function e(){var n=Date.now();if(l(n))return j(n);if(f.current){var o=t-(n-a.current);r(e,g?Math.min(o,y-(n-i.current)):o)}},w=function(){if(m||b){var d=Date.now(),p=l(d);if(u.current=[].slice.call(arguments),c.current=n,a.current=d,p){if(!o.current&&f.current)return i.current=a.current,r(x,t),v?e(a.current):s.current;if(g)return r(x,t),e(a.current)}return o.current||r(x,t),s.current}};return w.cancel=function(){o.current&&(p?cancelAnimationFrame(o.current):clearTimeout(o.current)),i.current=0,u.current=a.current=c.current=o.current=null},w.isPending=function(){return!!o.current},w.flush=function(){return o.current?j(Date.now()):s.current},w},[v,g,t,y,h,p,m,b])}(e=>{console.log("Searching... ".concat(e));let t=new URLSearchParams(c);t.set("page","1"),e?t.set(o,e):t.delete(o),d("".concat(s,"?").concat(t.toString()))},500);return(0,n.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,n.jsx)("label",{htmlFor:o,className:"sr-only",children:"Search"}),(0,n.jsx)("input",{id:o,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:r,onChange:e=>{f(e.target.value)},defaultValue:null==(t=c.get(o))?void 0:t.toString()}),u||(0,n.jsx)(i.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},9416:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(2115);let l=n.forwardRef(function(e,t){let{title:r,titleId:l,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},a),r?n.createElement("title",{id:l},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[844,6874,2112,8441,1684,7358],()=>t(2847)),_N_E=e.O()}]);