(()=>{var e={};e.id=1902,e.ids=[1902],e.modules={513:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,10592))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10592:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\SearchInput.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\SearchInput.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13665:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,68016))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43550:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,dynamic:()=>c,generateMetadata:()=>u});var a=r(37413),s=r(4536),i=r.n(s),n=r(73993),l=r(58446),o=r(10592);let c="force-static",d=process.env.NEXT_PUBLIC_SITE_URL;async function u(){let e="/specialities";return{title:"Explore Holistic Health Specialities | Natural Healing Now",description:"Discover different holistic health specialities like Acupuncture, Naturopathy, Chiropractic, and more. Find clinics offering these services.",alternates:{canonical:d?`${d}${e}`:e}}}function m(){return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-6",children:"Specialties"}),(0,a.jsxs)("div",{className:"text-center py-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Unable to Load Specialties"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"We're having trouble loading our specialties at the moment."}),(0,a.jsx)("p",{className:"text-gray-500 mb-6",children:"Please try again later. In the meantime, you can explore our clinics and practitioners directly."}),(0,a.jsxs)("div",{className:"mt-6 flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(i(),{href:"/clinics",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium",children:"Browse Clinics"}),(0,a.jsx)(i(),{href:"/practitioners",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-4 py-2 rounded-lg font-medium",children:"Find Practitioners"})]})]})]})})})}let g=[{id:"1",name:"Acupuncture",slug:"acupuncture",description:"Traditional Chinese medicine technique involving thin needles.",clinics:[]},{id:"2",name:"Naturopathy",slug:"naturopathy",description:"Natural approach to health and healing.",clinics:[]},{id:"3",name:"Chiropractic",slug:"chiropractic",description:"Focus on diagnosis and treatment of mechanical disorders of the musculoskeletal system.",clinics:[]},{id:"4",name:"Massage Therapy",slug:"massage-therapy",description:"Manual manipulation of soft body tissues to enhance health and wellbeing.",clinics:[]}];async function p(){try{let e="Natural Healing Now";try{let t=await l.$.global.getSettings();console.log("Global settings response structure:",JSON.stringify({hasData:!!t?.data,dataKeys:t?.data?Object.keys(t.data):[],hasAttributes:!!t?.data?.attributes})),t?.data&&(t.data.attributes?(e=t.data.attributes.siteName||e,t.data.attributes.logoLight?.data?.attributes):(e=t.data.siteName||e,t.data.logoLight?.data?.attributes?t.data.logoLight.data.attributes:t.data.logoLight&&t.data.logoLight));let r=await l.$.categories.getFooterCategories();r?.data&&r.data}catch(e){console.error("Error fetching layout data for SpecialitiesPage:",e)}let t=[];if("phase-production-build"===process.env.NEXT_PHASE){console.log("Using static specialties during prerendering"),t=g;return}try{console.log("Attempting to fetch specialties data...");let e=await l.$.specialties.getAll();if(console.log("Specialties response received:",e?`Data exists: ${!!e.data}, Is array: ${Array.isArray(e.data)}, Length: ${e.data?.length||0}`:"No response"),e?.data&&Array.isArray(e.data)&&e.data.length>0){let t=e.data[0];console.log("First specialty item structure:",JSON.stringify({id:t.id,hasAttributes:!!t.attributes,attributesKeys:t.attributes?Object.keys(t.attributes):[],hasSlug:!!t.attributes?.slug,slug:t.attributes?.slug||"no slug",hasDirectName:!!t.name,hasDirectSlug:!!t.slug,directName:t.name||"no direct name",directSlug:t.slug||"no direct slug"},null,2)),console.log("Complete first specialty item:",JSON.stringify(t,null,2)),console.log("All specialty items structure summary:",e.data.map((e,t)=>({index:t,id:e.id,hasAttributes:!!e.attributes,hasSlug:!!(e.attributes?.slug||e.slug),source:e.attributes?.slug?"attributes":e.slug?"direct":"missing"})))}if(e?.data)try{console.log("Raw specialties data structure:",JSON.stringify({isArray:Array.isArray(e.data),length:Array.isArray(e.data)?e.data.length:"not an array",firstItem:Array.isArray(e.data)&&e.data.length>0?Object.keys(e.data[0]).join(", "):"no items"}));let r=Array.isArray(e.data)?e.data:[e.data];console.log(`Processing ${r.length} specialty items`),t=r.filter(e=>e&&e.id).map(e=>{try{let t=e.id?.toString()||"",r=!!e.attributes,a=e.attributes||{};console.log(`Specialty ${t} structure:`,JSON.stringify({hasAttributes:r,directName:e.name?"has direct name":"no direct name",directSlug:e.slug?"has direct slug":"no direct slug",attributesName:a.name?"has attributes.name":"no attributes.name",attributesSlug:a.slug?"has attributes.slug":"no attributes.slug"}));let s=a.name||e.name||"Unnamed Specialty",i=a.slug||e.slug||"";i||(i=s&&"Unnamed Specialty"!==s?s.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""):`specialty-${t}`),console.log(`Processing specialty ID ${t}: name=${s}, slug=${i}`);let n=a.description||e.description||"",l=[];try{if(a.clinics?.data&&Array.isArray(a.clinics.data))l=a.clinics.data.filter(e=>e&&e.id).map(e=>{let t=e.id?.toString()||"",r=e.attributes||{},a=r.name||e.name||"Unnamed Clinic",s=r.slug||e.slug||a.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,"")||`clinic-${t}`;return{id:t,name:a,slug:s}});else if(Array.isArray(e.clinics))l=e.clinics.filter(e=>e&&e.id).map(e=>{let t=e.id?.toString()||"",r=e.name||"Unnamed Clinic",a=e.slug||r.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,"")||`clinic-${t}`;return{id:t,name:r,slug:a}});else if(a.clinics&&!Array.isArray(a.clinics)&&"object"==typeof a.clinics){console.log("Found clinics as direct property object");let e=a.clinics.data||a.clinics;Array.isArray(e)&&(l=e.filter(e=>e&&e.id).map(e=>{let t=e.id?.toString()||"",r=e.attributes||{},a=r.name||e.name||"Unnamed Clinic",s=r.slug||e.slug||`clinic-${t}`;return{id:t,name:a,slug:s}}))}}catch(e){console.error("Error processing clinics for specialty:",e),l=[]}return{id:t,name:s,slug:i,description:n,icon:a.icon?.data?.attributes||e.icon||null,featured_image:a.featuredImage?.data?.attributes||e.featured_image||null,clinics:l}}catch(e){return console.error("Error processing specialty item:",e),null}}).filter(Boolean)}catch(e){console.error("Error processing specialties data:",e),t=[]}}catch(e){console.error("Error fetching specialties:",e),console.log("Using static specialties due to API error"),t=g}return Array.isArray(t)||(console.log("specialities is not an array, using static specialties"),t=g),0===t.length&&(console.log("specialities is empty, using static specialties"),t=g),t=t.map(e=>{if(!e)return console.log("Skipping null/undefined specialty item"),null;try{if(!e.id)return console.log(`Skipping specialty item without ID: ${JSON.stringify(e)}`),null;if(!e.slug){let t=e.name?e.name.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""):`specialty-${e.id}`;return console.log(`Adding missing slug for specialty with id ${e.id}: ${t}`),{...e,slug:t}}return e}catch(e){return console.error(`Error processing specialty item: ${e}`),null}}).filter(e=>{let t=e&&e.id&&e.slug;return t||console.log(`Filtering out invalid specialty item: ${JSON.stringify(e)}`),t}),console.log(`Final processed specialties count: ${t.length}`),t.length>0?console.log(`First processed specialty: id=${t[0].id}, name=${t[0].name}, slug=${t[0].slug}`):(console.log("No specialties after processing, using static specialties"),t=g),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-emerald-600 text-white py-12",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Explore Specialities"}),(0,a.jsx)("p",{className:"text-lg max-w-3xl",children:"Discover different holistic health specialities and find clinics offering these services."})]})}),(0,a.jsx)("div",{className:"bg-white py-8 border-b",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsx)(o.default,{placeholder:"Search specialties...",defaultValue:"",icon:(0,a.jsx)(n.CKj,{className:"text-gray-400"})})})})}),(0,a.jsx)("div",{className:"py-12 bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-8",children:"All Specialities"}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:(()=>{try{if(!Array.isArray(t))return console.error("specialities is not an array:",t),(0,a.jsx)("div",{className:"col-span-full text-center py-12",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Unable to Load Specialties"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"We're having trouble loading our specialties at the moment."}),(0,a.jsx)("p",{className:"text-gray-500",children:"Please try again later. In the meantime, you can explore our clinics and practitioners directly."}),(0,a.jsxs)("div",{className:"mt-6 flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(i(),{href:"/clinics",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium",children:"Browse Clinics"}),(0,a.jsx)(i(),{href:"/practitioners",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-4 py-2 rounded-lg font-medium",children:"Find Practitioners"})]})]})});if(0===t.length)return(0,a.jsx)("div",{className:"col-span-full text-center py-12",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"No Specialties Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"We're currently updating our database of holistic health specialties."}),(0,a.jsx)("p",{className:"text-gray-500",children:"Please check back soon as we continue to expand our offerings. In the meantime, you can explore our clinics and practitioners directly."}),(0,a.jsxs)("div",{className:"mt-6 flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(i(),{href:"/clinics",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium",children:"Browse Clinics"}),(0,a.jsx)(i(),{href:"/practitioners",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-4 py-2 rounded-lg font-medium",children:"Find Practitioners"})]})]})});return t.map(e=>{try{if(!e||!e.id)return console.log(`Skipping specialty with missing id: ${JSON.stringify(e)}`),null;e.slug||(console.log(`Specialty ${e.id} missing slug, generating one from ID`),e.slug=`specialty-${e.id}`);let t=e.name||"Unnamed Specialty";return e.description,Array.isArray(e.clinics)&&e.clinics,(0,a.jsxs)(i(),{href:`/specialities/${e.slug}`,className:"bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow block",children:[(0,a.jsx)("div",{className:"h-32 bg-emerald-100 flex items-center justify-center hover:bg-emerald-200 transition-colors",children:(0,a.jsx)("span",{className:"text-emerald-700 text-2xl font-semibold",children:t&&t.length>0?t.charAt(0):"S"})}),(0,a.jsx)("div",{className:"p-5 text-center",children:(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 hover:text-emerald-700 transition-colors",children:t})})]},e.id)}catch(t){return console.error("Error rendering specialty:",t,e),null}})}catch(e){return console.error("Fatal error rendering specialties:",e),(0,a.jsx)("div",{className:"col-span-full text-center py-12",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8 max-w-2xl mx-auto",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"Unable to Load Specialties"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"We're having trouble loading our specialties at the moment."}),(0,a.jsx)("p",{className:"text-gray-500",children:"Please try again later. In the meantime, you can explore our clinics and practitioners directly."}),(0,a.jsxs)("div",{className:"mt-6 flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(i(),{href:"/clinics",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium",children:"Browse Clinics"}),(0,a.jsx)(i(),{href:"/practitioners",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-4 py-2 rounded-lg font-medium",children:"Find Practitioners"})]})]})})}})()})]})}),(0,a.jsx)("div",{className:"py-12 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Popular Health Conditions"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:["Anxiety","Chronic Pain","Digestive Issues","Stress","Insomnia","Allergies","Fatigue","Headaches"].map((e,t)=>(0,a.jsx)(i(),{href:`/conditions/${e.toLowerCase().replace(" ","-")}`,className:"bg-gray-50 hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors",children:(0,a.jsx)("span",{className:"text-gray-800 font-medium",children:e})},t))})]})}),(0,a.jsx)("div",{className:"py-16 bg-emerald-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-6 text-gray-800",children:"Ready to Start Your Wellness Journey?"}),(0,a.jsx)("p",{className:"text-lg mb-8 max-w-3xl mx-auto text-gray-600",children:"Find clinics and practitioners offering the specialities that match your health needs."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(i(),{href:"/clinics",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold",children:"Find a Clinic"}),(0,a.jsx)(i(),{href:"/practitioners",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-semibold",children:"Find a Practitioner"})]})]})})]})}catch(e){return console.error("Fatal error in SpecialitiesPage:",e),(0,a.jsx)(m,{})}}},50448:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=r(65239),s=r(48088),i=r(31369),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let o={children:["",{children:["specialities",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43550)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\specialities\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\app\\specialities\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/specialities/page",pathname:"/specialities",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68016:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(60687),s=r(43210),i=r(16189),n=r(17019);function l({placeholder:e,paramName:t="query",icon:r}){let l=(0,i.useSearchParams)(),o=(0,i.usePathname)(),{replace:c}=(0,i.useRouter)(),d=function(e,t,r){var a=this,i=(0,s.useRef)(null),n=(0,s.useRef)(0),l=(0,s.useRef)(null),o=(0,s.useRef)([]),c=(0,s.useRef)(),d=(0,s.useRef)(),u=(0,s.useRef)(e),m=(0,s.useRef)(!0);u.current=e;var g="undefined"!=typeof window,p=!t&&0!==t&&g;if("function"!=typeof e)throw TypeError("Expected a function");t=+t||0;var h=!!(r=r||{}).leading,x=!("trailing"in r)||!!r.trailing,f="maxWait"in r,y="debounceOnServer"in r&&!!r.debounceOnServer,b=f?Math.max(+r.maxWait||0,t):null;return(0,s.useEffect)(function(){return m.current=!0,function(){m.current=!1}},[]),(0,s.useMemo)(function(){var e=function(e){var t=o.current,r=c.current;return o.current=c.current=null,n.current=e,d.current=u.current.apply(r,t)},r=function(e,t){p&&cancelAnimationFrame(l.current),l.current=p?requestAnimationFrame(e):setTimeout(e,t)},s=function(e){if(!m.current)return!1;var r=e-i.current;return!i.current||r>=t||r<0||f&&e-n.current>=b},v=function(t){return l.current=null,x&&o.current?e(t):(o.current=c.current=null,d.current)},j=function e(){var a=Date.now();if(s(a))return v(a);if(m.current){var l=t-(a-i.current);r(e,f?Math.min(l,b-(a-n.current)):l)}},N=function(){if(g||y){var u=Date.now(),p=s(u);if(o.current=[].slice.call(arguments),c.current=a,i.current=u,p){if(!l.current&&m.current)return n.current=i.current,r(j,t),h?e(i.current):d.current;if(f)return r(j,t),e(i.current)}return l.current||r(j,t),d.current}};return N.cancel=function(){l.current&&(p?cancelAnimationFrame(l.current):clearTimeout(l.current)),n.current=0,o.current=i.current=c.current=l.current=null},N.isPending=function(){return!!l.current},N.flush=function(){return l.current?v(Date.now()):d.current},N},[h,f,t,b,x,p,g,y])}(e=>{console.log(`Searching... ${e}`);let r=new URLSearchParams(l);r.set("page","1"),e?r.set(t,e):r.delete(t),c(`${o}?${r.toString()}`)},500);return(0,a.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,a.jsx)("label",{htmlFor:t,className:"sr-only",children:"Search"}),(0,a.jsx)("input",{id:t,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:e,onChange:e=>{d(e.target.value)},defaultValue:l.get(t)?.toString()}),r||(0,a.jsx)(n.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,1330,3376,6391,2975,8446,270],()=>r(50448));module.exports=a})();