"use client";

import { useState, FormEvent } from 'react';
import { FiSearch } from 'react-icons/fi';

interface SearchInputCallbackProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  buttonText?: string;
  className?: string;
  buttonClassName?: string;
  initialValue?: string;
}

const SearchInputCallback = ({
  placeholder = 'Search...',
  onSearch,
  buttonText = 'Search',
  className = '',
  buttonClassName = '',
  initialValue = ''
}: SearchInputCallbackProps) => {
  const [searchQuery, setSearchQuery] = useState(initialValue);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };

  return (
    <form onSubmit={handleSubmit} className={`flex w-full ${className}`}>
      <div className="relative flex-grow">
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder={placeholder}
          className="w-full px-10 py-2 text-gray-700 bg-white border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
          aria-label="Search"
        />
        <FiSearch className="absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400" />
      </div>
      <button
        type="submit"
        className={`px-6 py-2 font-medium text-white bg-emerald-600 rounded-r-lg hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 ${buttonClassName}`}
      >
        {buttonText}
      </button>
    </form>
  );
};

export default SearchInputCallback;
