"use client";

// Removed useState, useEffect, getStrapiContent imports as data is now passed via props
import Link from 'next/link';
import Image from 'next/image';
import { FiFacebook, FiTwitter, FiInstagram, FiLinkedin } from 'react-icons/fi';
import { sanitizeUrl, getStrapiMediaPath } from '@/lib/mediaUtils';

// Define the type for the logoLight prop, matching Layout.tsx
interface LogoLightMedia {
  id: number;
  name: string;
  alternativeText?: string | null;
  caption?: string | null;
  width?: number;
  height?: number;
  formats?: any;
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string | null;
  provider: string;
  provider_metadata?: any;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

// Define type for fetched category data
interface FooterCategory {
  id: number;
  attributes: {
    name: string;
    slug: string;
  };
}

interface FooterProps {
  siteName: string;
  logoLight: LogoLightMedia | null;
  footerCategories: FooterCategory[]; // Add footerCategories prop
}

// Accept footerCategories as a prop
const Footer = ({ siteName, logoLight, footerCategories }: FooterProps) => {
  const currentYear = new Date().getFullYear();

  // Using the imported sanitizeUrl function from mediaUtils

  // Get the Strapi API URL from environment variables
  const strapiApiUrl = process.env.NEXT_PUBLIC_API_URL || '';

  // Get the Strapi Media URL from environment variables (or derive it from API URL)
  const strapiMediaUrl = process.env.IMAGE_HOSTNAME ||
    (strapiApiUrl ? strapiApiUrl.replace('strapiapp.com', 'media.strapiapp.com') : '');

  // For Strapi Cloud, construct the media URL correctly
  let logoUrl = null;

  if (logoLight?.url) {
    // Use the new getStrapiMediaPath function to properly handle the URL
    // This function will handle all the edge cases and prevent malformed URLs
    logoUrl = getStrapiMediaPath(logoLight.url);

    // Log the URL in development for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('Footer Logo URL:', {
        original: logoLight.url,
        processed: logoUrl
      });
    }
  }

  // Log the URL in production for debugging
  if (process.env.NODE_ENV === 'production' && logoLight) {
    console.log('Footer Logo URL:', {
      original: logoLight.url,
      processed: logoUrl
    });
  }

  // Removed useEffect and related state variables

  return (
    <footer className="bg-gray-800 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* About */}
          <div>
            {logoUrl ? (
              <Link href="/" className="inline-block mb-4">
                <Image
                  src={logoUrl}
                  alt={logoLight?.alternativeText || siteName}
                  width={logoLight?.width || 150} // Provide default or use actual width
                  height={logoLight?.height || 40} // Provide default or use actual height
                  className="h-12 w-auto" // Adjust styling as needed
                  unoptimized={process.env.NODE_ENV === 'production'} // Use unoptimized in production to avoid issues
                  priority // Prioritize loading the logo as it's important
                />
              </Link>
            ) : (
              <h3 className="text-xl font-semibold mb-4">{siteName}</h3>
            )}
            <p className="text-gray-300 mb-4">
              Connecting you with holistic health practitioners and clinics to support your wellness journey.
            </p>
            <div className="flex space-x-4">
              {/* Assuming these hrefs will eventually be external URLs */}
              <a href="#" target="_blank" rel="nofollow noopener noreferrer" className="text-gray-300 hover:text-white" aria-label="Facebook">
                <FiFacebook size={20} />
              </a>
              <a href="#" target="_blank" rel="nofollow noopener noreferrer" className="text-gray-300 hover:text-white" aria-label="Twitter">
                <FiTwitter size={20} />
              </a>
              <a href="#" target="_blank" rel="nofollow noopener noreferrer" className="text-gray-300 hover:text-white" aria-label="Instagram">
                <FiInstagram size={20} />
              </a>
              <a href="#" target="_blank" rel="nofollow noopener noreferrer" className="text-gray-300 hover:text-white" aria-label="LinkedIn">
                <FiLinkedin size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-300 hover:text-white">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/clinics" className="text-gray-300 hover:text-white">
                  Find a Clinic
                </Link>
              </li>
              <li>
                <Link href="/practitioners" className="text-gray-300 hover:text-white">
                  Find a Practitioner
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-300 hover:text-white">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/about-us" className="text-gray-300 hover:text-white">
                  About Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Categories</h3>
            <ul className="space-y-2">
              {/* Render directly from props, handle empty array case */}
              {footerCategories && footerCategories.length > 0 ? (
                footerCategories.map((category) => (
                  <li key={category.id}>
                    <Link
                      // Use '#' as href if slug is missing due to permission issues
                      href={category.attributes.slug && category.attributes.slug !== '#' ? `/categories/${category.attributes.slug}` : '#'}
                      className="text-gray-300 hover:text-white"
                    >
                      {/* Display name, potentially the default 'Unnamed Category' */}
                      {category.attributes.name}
                    </Link>
                  </li>
                ))
              ) : (
                <li>No categories available.</li> // Display message if no categories fetched/passed
              )}
              {/* Always show the "View All" link */}
              <li>
                <Link href="/categories" className="text-gray-300 hover:text-white">
                  View All Categories
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Contact Us</h3>
            <p className="text-gray-300 mb-2">Have questions or feedback?</p>
            <Link href="/contact" className="text-emerald-400 hover:text-emerald-300 mb-2 block">
              Get in touch with us
            </Link>
            {/* Moved Links */}
            <div className="mt-2 space-y-1">
               <Link href="/privacy" className="text-gray-300 hover:text-white text-sm block">
                  Privacy Policy
               </Link>
               <Link href="/terms" className="text-gray-300 hover:text-white text-sm block">
                  Terms of Service
               </Link>
               <Link href="/affiliate-disclosure" className="text-gray-300 hover:text-white text-sm block">
                  Affiliate Disclosure
               </Link>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; {currentYear} {siteName}. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
