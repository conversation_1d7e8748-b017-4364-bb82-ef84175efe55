(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7800],{1945:(e,r,t)=>{"use strict";t.d(r,{default:()=>c});var n=t(5155),a=t(2115),s=t(6874),i=t.n(s),l=t(6500);function c(e){let{initialConditions:r}=e,[t,s]=(0,a.useState)(r||[]),[c,o]=(0,a.useState)(!0),[u,d]=(0,a.useState)(null);return((0,a.useEffect)(()=>{if(r&&r.length>0){s(r),o(!1);return}!async function(){console.log("Fetching conditions client-side..."),o(!0),d(null);try{let e=await l.$.conditions.getAll({}),r=((null==e?void 0:e.data)||[]).map(e=>e&&e.name&&e.slug?{id:e.id,name:e.name,slug:e.slug}:(console.warn("Skipping invalid condition: ID ".concat(null==e?void 0:e.id)),null)).filter(e=>null!==e);r.length>0?s(r):(console.warn("Client fetched conditions but processed list empty. Using empty list."),s([]))}catch(e){console.error("Client error fetching conditions:",e),d("Failed to load conditions."),s([])}finally{o(!1)}}()},[r]),c)?(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((e,r)=>(0,n.jsx)("div",{className:"bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"},r))}):u?(0,n.jsx)("div",{className:"text-center py-4 text-red-500",children:u}):0===t.length?(0,n.jsx)("div",{className:"text-center py-4 text-gray-500",children:"No health conditions available at the moment."}):(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:t.map(e=>(0,n.jsx)(i(),{href:"/conditions/".concat(e.slug),className:"bg-white hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors",children:(0,n.jsx)("span",{className:"text-gray-800 font-medium",children:e.name})},e.id))})}},2461:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var n=t(2115);let a=n.forwardRef(function(e,r){let{title:t,titleId:a,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},s),t?n.createElement("title",{id:a},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},2596:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=function(){for(var e,r,t=0,n="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,n,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var s=r.length;for(t=0;t<s;t++)r[t]&&(n=e(r[t]))&&(a&&(a+=" "),a+=n)}else for(n in r)r[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=r);return n}},3566:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.bind(t,6926)),Promise.resolve().then(t.bind(t,1945)),Promise.resolve().then(t.bind(t,8609)),Promise.resolve().then(t.bind(t,8400))},4436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>u});var n=t(2115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=n.createContext&&n.createContext(a),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function c(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function o(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?c(Object(t),!0).forEach(function(r){var n,a,s;n=e,a=r,s=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in n?Object.defineProperty(n,a,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[a]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return r=>n.createElement(d,l({attr:o({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,o({key:t},r.attr),e(r.child)))}(e.child))}function d(e){var r=r=>{var t,{attr:a,size:s,title:c}=e,u=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)t=s[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,i),d=s||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,u,{className:t,style:o(o({color:e.color||r.color},r.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==s?n.createElement(s.Consumer,null,e=>r(e)):r(a)}},5695:(e,r,t)=>{"use strict";var n=t(8999);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},5922:(e,r,t)=>{"use strict";t.d(r,{default:()=>d});var n=t(5155),a=t(2461),s=t(9416),i=t(2596),l=t(6874),c=t.n(l),o=t(5695);let u=(e,r)=>r<=7?Array.from({length:r},(e,r)=>r+1):e<=3?[1,2,3,"...",r-1,r]:e>=r-2?[1,2,"...",r-2,r-1,r]:[1,"...",e-1,e,e+1,"...",r];function d(e){let{totalPages:r,currentPage:t}=e,a=(0,o.usePathname)(),s=(0,o.useSearchParams)(),i=void 0!==t?t:Number(s.get("page"))||1,l=e=>{let r=new URLSearchParams(s);return r.set("page",e.toString()),"".concat(a,"?").concat(r.toString())},c=u(i,r);return r<=1?null:(0,n.jsxs)("div",{className:"inline-flex",children:[(0,n.jsx)(f,{direction:"left",href:l(i-1),isDisabled:i<=1}),(0,n.jsx)("div",{className:"flex -space-x-px",children:c.map((e,r)=>{let t;return 0===r&&(t="first"),r===c.length-1&&(t="last"),1===c.length&&(t="single"),"..."===e&&(t="middle"),(0,n.jsx)(m,{href:l(e),page:e,position:t,isActive:i===e},"".concat(e,"-").concat(r))})}),(0,n.jsx)(f,{direction:"right",href:l(i+1),isDisabled:i>=r})]})}function m(e){let{page:r,href:t,isActive:a,position:s}=e,l=(0,i.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===s||"single"===s,"rounded-r-md":"last"===s||"single"===s,"z-10 bg-emerald-600 border-emerald-600 text-white":a,"hover:bg-gray-100":!a&&"middle"!==s,"text-gray-300 pointer-events-none":"middle"===s});return a||"middle"===s?(0,n.jsx)("div",{className:l,children:r}):(0,n.jsx)(c(),{href:t,className:l,children:r})}function f(e){let{href:r,direction:t,isDisabled:l}=e,o=(0,i.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":l,"hover:bg-gray-100":!l,"mr-2 md:mr-4":"left"===t,"ml-2 md:ml-4":"right"===t}),u="left"===t?(0,n.jsx)(a.A,{className:"w-4"}):(0,n.jsx)(s.A,{className:"w-4"});return l?(0,n.jsx)("div",{className:o,children:u}):(0,n.jsx)(c(),{className:o,href:r,children:u})}},6772:(e,r,t)=>{"use strict";t.d(r,{default:()=>c});var n=t(5155),a=t(6874),s=t.n(a),i=t(351),l=t(2515);let c=e=>{var r,t;let{clinic:a,showContactInfo:c=!0,prefetchedData:o=!1}=e,u=o?{pathname:"/clinics/".concat(a.slug),query:{prefetched:"true"}}:"/clinics/".concat(a.slug);return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 flex-grow",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,n.jsx)(s(),{href:u,className:"hover:text-emerald-600",children:a.name})}),a.isVerified&&(0,n.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,n.jsx)(l.AI8,{color:"#009967",size:14}),(0,n.jsx)("span",{children:"VERIFIED"})]}),a.description&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:a.description}),(0,n.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[a.address&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.HzC,{className:"mr-2 text-emerald-500"}),(0,n.jsxs)("span",{children:[a.address.city,", ",a.address.stateProvince]})]}),c&&(null==(r=a.contactInfo)?void 0:r.phoneNumber)&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.QFc,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("span",{children:a.contactInfo.phoneNumber})]}),c&&(null==(t=a.contactInfo)?void 0:t.websiteUrl)&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.VeH,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("a",{href:a.contactInfo.websiteUrl,target:"_blank",rel:"nofollow noopener noreferrer",className:"hover:text-emerald-600",children:"Visit Website"})]})]})]}),(0,n.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,n.jsx)(s(),{href:u,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Details →"})})]})}},6926:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var n=t(5155);t(2115);var a=t(6772),s=t(5922);function i(e){let{clinicsToDisplay:r,totalPages:t,totalCount:i,currentPage:l}=e;return r?(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsxs)("h2",{className:"text-2xl font-bold text-gray-800",children:[i," Clinics Found"]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-gray-600",children:"Sort by:"}),(0,n.jsxs)("select",{className:"border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",children:[(0,n.jsx)("option",{children:"Relevance"}),(0,n.jsx)("option",{children:"Distance"}),(0,n.jsx)("option",{children:"Name (A-Z)"})]})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.length>0?r.map(e=>(0,n.jsx)(a.default,{clinic:e,showContactInfo:!1,prefetchedData:!0},e.id)):(0,n.jsx)("div",{className:"col-span-3 text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:"No clinics found. Please try adjusting your search criteria."})})}),t>1&&(0,n.jsx)("div",{className:"mt-12 flex justify-center",children:(0,n.jsx)(s.default,{totalPages:t,currentPage:l})})]}):(0,n.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Loading clinics or no data available..."})}},8400:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var n=t(5155),a=t(2115),s=t(5695),i=t(351);function l(e){var r;let{placeholder:t,paramName:l="query",icon:c}=e,o=(0,s.useSearchParams)(),u=(0,s.usePathname)(),{replace:d}=(0,s.useRouter)(),m=function(e,r,t){var n=this,s=(0,a.useRef)(null),i=(0,a.useRef)(0),l=(0,a.useRef)(null),c=(0,a.useRef)([]),o=(0,a.useRef)(),u=(0,a.useRef)(),d=(0,a.useRef)(e),m=(0,a.useRef)(!0);d.current=e;var f="undefined"!=typeof window,h=!r&&0!==r&&f;if("function"!=typeof e)throw TypeError("Expected a function");r=+r||0;var g=!!(t=t||{}).leading,p=!("trailing"in t)||!!t.trailing,x="maxWait"in t,v="debounceOnServer"in t&&!!t.debounceOnServer,y=x?Math.max(+t.maxWait||0,r):null;return(0,a.useEffect)(function(){return m.current=!0,function(){m.current=!1}},[]),(0,a.useMemo)(function(){var e=function(e){var r=c.current,t=o.current;return c.current=o.current=null,i.current=e,u.current=d.current.apply(t,r)},t=function(e,r){h&&cancelAnimationFrame(l.current),l.current=h?requestAnimationFrame(e):setTimeout(e,r)},a=function(e){if(!m.current)return!1;var t=e-s.current;return!s.current||t>=r||t<0||x&&e-i.current>=y},b=function(r){return l.current=null,p&&c.current?e(r):(c.current=o.current=null,u.current)},j=function e(){var n=Date.now();if(a(n))return b(n);if(m.current){var l=r-(n-s.current);t(e,x?Math.min(l,y-(n-i.current)):l)}},N=function(){if(f||v){var d=Date.now(),h=a(d);if(c.current=[].slice.call(arguments),o.current=n,s.current=d,h){if(!l.current&&m.current)return i.current=s.current,t(j,r),g?e(s.current):u.current;if(x)return t(j,r),e(s.current)}return l.current||t(j,r),u.current}};return N.cancel=function(){l.current&&(h?cancelAnimationFrame(l.current):clearTimeout(l.current)),i.current=0,c.current=s.current=o.current=l.current=null},N.isPending=function(){return!!l.current},N.flush=function(){return l.current?b(Date.now()):u.current},N},[g,x,r,y,p,h,f,v])}(e=>{console.log("Searching... ".concat(e));let r=new URLSearchParams(o);r.set("page","1"),e?r.set(l,e):r.delete(l),d("".concat(u,"?").concat(r.toString()))},500);return(0,n.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,n.jsx)("label",{htmlFor:l,className:"sr-only",children:"Search"}),(0,n.jsx)("input",{id:l,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:t,onChange:e=>{m(e.target.value)},defaultValue:null==(r=o.get(l))?void 0:r.toString()}),c||(0,n.jsx)(i.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},8609:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});var n=t(5155),a=t(2115),s=t(6874),i=t.n(s),l=t(6500);let c=e=>{var r;if(!e||!e.id)return console.warn("Skipping specialty with missing ID: ".concat(null==e?void 0:e.id)),null;let t=(null==(r=e.id)?void 0:r.toString())||"",n=e.attributes||{},a=n.name||e.name||"Unnamed Specialty",s=n.slug||e.slug||("Unnamed Specialty"!==a?a.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""):"specialty-".concat(t));return{name:a,slug:s}};function o(e){let{initialSpecialties:r}=e,[t,s]=(0,a.useState)(r||[]),[o,u]=(0,a.useState)(!0),[d,m]=(0,a.useState)(null);return((0,a.useEffect)(()=>{if(r&&r.length>0){s(r),u(!1);return}!async function(){console.log("Fetching specialties client-side..."),u(!0),m(null);try{let e=await l.$.specialties.getAll({cache:"force-cache",next:{revalidate:604800,tags:["strapi-specialties","strapi-specialties-all"]}});if((null==e?void 0:e.data)&&Array.isArray(e.data)){let r=e.data.map(c).filter(e=>null!==e);r.length>0?s(r):(console.warn("Client fetched specialties but processed list empty. Using default fallback."),s([{name:"Acupuncture",slug:"acupuncture"},{name:"Naturopathy",slug:"naturopathy"},{name:"Chiropractic",slug:"chiropractic"},{name:"Massage Therapy",slug:"massage-therapy"}]))}else console.warn("Client invalid/empty specialties data from API. Using default fallback."),s([{name:"Acupuncture",slug:"acupuncture"},{name:"Naturopathy",slug:"naturopathy"},{name:"Chiropractic",slug:"chiropractic"},{name:"Massage Therapy",slug:"massage-therapy"}])}catch(e){console.error("Client error fetching specialties:",e),m("Failed to load specialties."),s([{name:"Acupuncture",slug:"acupuncture"},{name:"Naturopathy",slug:"naturopathy"},{name:"Chiropractic",slug:"chiropractic"},{name:"Massage Therapy",slug:"massage-therapy"}])}finally{u(!1)}}()},[r]),o)?(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((e,r)=>(0,n.jsx)("div",{className:"bg-gray-100 border border-gray-200 rounded-lg p-4 text-center animate-pulse h-16"},r))}):d?(0,n.jsx)("div",{className:"text-center py-4 text-red-500",children:d}):0===t.length?(0,n.jsx)("div",{className:"text-center py-4 text-gray-500",children:"No specialties available at the moment."}):(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:t.map(e=>(0,n.jsx)(i(),{href:"/specialities/".concat(e.slug),prefetch:!1,className:"bg-gray-50 hover:bg-emerald-50 border border-gray-200 rounded-lg p-4 text-center transition-colors",children:(0,n.jsx)("span",{className:"text-gray-800 font-medium",children:e.name})},e.slug))})}},9416:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var n=t(2115);let a=n.forwardRef(function(e,r){let{title:t,titleId:a,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":a},s),t?n.createElement("title",{id:a},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})}},e=>{var r=r=>e(e.s=r);e.O(0,[844,5479,6874,6052,5825,8441,1684,7358],()=>r(3566)),_N_E=e.O()}]);