# Strapi Configuration
# --------------------
# The public URL of your Strapi backend API (e.g., http://localhost:1337 or https://your-strapi-domain.com)
# IMPORTANT: In production, this MUST be set to your Strapi Cloud URL (e.g., https://your-project-id.strapiapp.com)
# DO NOT use localhost in production!
NEXT_PUBLIC_API_URL=https://nice-badge-2130241d6c.strapiapp.com

# Alternative variable - both are supported for backward compatibility
NEXT_PUBLIC_STRAPI_API_URL=https://nice-badge-2130241d6c.strapiapp.com

# Media URL specifically for Strapi media assets (images) - typically media.strapiapp.com in production
# If not set, will be automatically derived from the API URL (recommended for Strapi Cloud)
NEXT_PUBLIC_STRAPI_MEDIA_URL=https://nice-badge-2130241d6c.media.strapiapp.com

# Strapi API Token for authenticated requests (needed for content fetching)
# Create this token in your Strapi Admin Panel (Settings -> API Tokens)
STRAPI_API_TOKEN=your_strapi_api_token

# JWT Secret for cookie encryption (should match Strapi's JWT secret)
JWT_SECRET=your_jwt_secret

# Image Optimization Configuration
# -------------------------------
# Control how images are optimized (optional, defaults are used if not specified)
# Set to 'true' to disable image optimization (not recommended)
# NEXT_PUBLIC_DISABLE_IMAGE_OPTIMIZATION=false

# Set to 'true' to enable larger image output for high DPI displays (trade-off: bandwidth)
# NEXT_PUBLIC_HIGH_QUALITY_IMAGES=false

# Advanced Performance Configuration
# ---------------------------------
# Enable performance metrics tracking for caching and image operations (development only)
# NEXT_PUBLIC_CACHE_METRICS=false

# Custom cache duration for static images in seconds (defaults to 604800 - 7 days)
# NEXT_PUBLIC_IMAGE_CACHE_TTL=604800

# Stale-while-revalidate seconds - how long to serve stale content while revalidating
# NEXT_PUBLIC_SWR_LIFETIME=43200

# Google Maps Configuration (Optional)
# ------------------------------------
# If using Google Maps features, provide your API Key
# Enable "Maps JavaScript API" and "Places API" in Google Cloud Console
# NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=

# Next.js Configuration
# ---------------------
# The base URL of your deployed frontend application (used for SEO, sitemaps, password resets, etc.)
# Example: https://your-frontend-domain.com
# If not set, the system will use NEXT_PUBLIC_API_URL with the '.strapiapp.com' part removed
# NEXT_PUBLIC_SITE_URL=https://your-frontend-domain.com

# Site Configuration
# ------------------
# The name of your website, displayed in the header/logo area
NEXT_PUBLIC_SITE_NAME="Natural Healing Now"

# Revalidation Configuration
# -------------------------
# Secret token for on-demand revalidation API endpoint
# This should be a long, random string that matches the token set in your Strapi webhook
# You can use either REVALIDATE_TOKEN or PREVIEW_SECRET (for backward compatibility)
REVALIDATE_TOKEN=your_secure_revalidation_token
PREVIEW_SECRET=your_secure_revalidation_token
