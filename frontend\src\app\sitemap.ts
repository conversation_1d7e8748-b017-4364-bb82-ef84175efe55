import { MetadataRoute } from 'next';
import { getStrapiContent } from '@/lib/strapi';

// Define the site URL from environment variable
// We need an absolute URL for sitemaps to work properly
// For sitemaps, we should use the frontend URL, not the Strapi API URL
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log a warning if SITE_URL is not set
if (!process.env.NEXT_PUBLIC_SITE_URL && !process.env.NEXT_PUBLIC_API_URL) {
  console.warn('WARNING: Neither NEXT_PUBLIC_SITE_URL nor NEXT_PUBLIC_API_URL environment variables are set. Using default vercel.app URL as a placeholder.');
}

// Log the URL being used for debugging
console.log(`Using site URL for main sitemap: ${SITE_URL}`);

// Ensure SITE_URL doesn't have a trailing slash
const normalizedSiteUrl = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;

// Define interfaces for Strapi data types
interface StrapiItem {
  id?: string | number;
  documentId?: string;
  attributes?: {
    slug?: string;
    [key: string]: any;
  };
  slug?: string;
  [key: string]: any;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    // Initialize with static routes
    let sitemapEntries: MetadataRoute.Sitemap = [
      {
        url: `${normalizedSiteUrl}`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0,
      },
      {
        url: `${normalizedSiteUrl}/clinics`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${normalizedSiteUrl}/practitioners`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${normalizedSiteUrl}/categories`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${normalizedSiteUrl}/specialities`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${normalizedSiteUrl}/conditions`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${normalizedSiteUrl}/blog`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${normalizedSiteUrl}/about`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.5,
      },
      {
        url: `${normalizedSiteUrl}/contact`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.5,
      },
      {
        url: `${normalizedSiteUrl}/privacy`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.5,
      },
      {
        url: `${normalizedSiteUrl}/terms`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.5,
      },
      // Add links to the specialized sitemaps
      {
        url: `${normalizedSiteUrl}/sitemap-blog.xml`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      },
      {
        url: `${normalizedSiteUrl}/sitemap-clinics.xml`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      },
      {
        url: `${normalizedSiteUrl}/sitemap-practitioners.xml`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.8,
      },
    ];

    // Blog posts are now in sitemap-blog.xml

    // 2. Fetch blog categories
    const blogCategoriesResponse = await getStrapiContent.blog.getCategories({
      pagination: { pageSize: 100 }
    });

    const blogCategories = blogCategoriesResponse?.data || [];

    const blogCategoryRoutes = blogCategories.map((category: StrapiItem) => {
      const slug = category.attributes?.slug || category.slug;
      return {
        url: `${normalizedSiteUrl}/blog/categories/${slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.6,
      };
    });

    // 3. Fetch blog tags
    const blogTagsResponse = await getStrapiContent.blog.getTags();
    const blogTags = blogTagsResponse?.data || [];

    const blogTagRoutes = blogTags.map((tag: StrapiItem) => {
      const slug = tag.attributes?.slug || tag.slug;
      return {
        url: `${normalizedSiteUrl}/blog/tags/${slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.6,
      };
    });

    // Clinics are now in sitemap-clinics.xml

    // Practitioners are now in sitemap-practitioners.xml

    // 6. Fetch categories
    const categoriesResponse = await getStrapiContent.categories.getAll({
      pagination: { pageSize: 100 }
    });

    const categories = categoriesResponse?.data || [];

    const categoryRoutes = categories.map((category: StrapiItem) => {
      const attributes = category.attributes || category;
      const slug = attributes.slug;

      if (!slug) return null;

      return {
        url: `${normalizedSiteUrl}/categories/${slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.7,
      };
    }).filter(Boolean);

    // 7. Fetch specialties
    const specialtiesResponse = await getStrapiContent.specialties.getAll({
      pagination: { pageSize: 100 }
    });

    const specialties = specialtiesResponse?.data || [];

    const specialtyRoutes = specialties.map((specialty: StrapiItem) => {
      const attributes = specialty.attributes || specialty;
      const slug = attributes.slug;

      if (!slug) return null;

      return {
        url: `${normalizedSiteUrl}/specialities/${slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.7,
      };
    }).filter(Boolean);

    // 8. Fetch conditions
    const conditionsResponse = await getStrapiContent.conditions.getAll({
      pagination: { pageSize: 100 }
    });

    const conditions = conditionsResponse?.data || [];

    const conditionRoutes = conditions.map((condition: StrapiItem) => {
      const attributes = condition.attributes || condition;
      const slug = attributes.slug;

      if (!slug) return null;

      return {
        url: `${normalizedSiteUrl}/conditions/${slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.7,
      };
    }).filter(Boolean);

    // Combine all routes
    sitemapEntries = [
      ...sitemapEntries,
      ...blogCategoryRoutes,
      ...blogTagRoutes,
      ...categoryRoutes,
      ...specialtyRoutes,
      ...conditionRoutes,
    ];

    return sitemapEntries;
  } catch (error) {
    console.error('Error generating sitemap:', error);

    // Return only static routes if there's an error
    return [
      {
        url: `${normalizedSiteUrl}`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0,
      },
      {
        url: `${normalizedSiteUrl}/clinics`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${normalizedSiteUrl}/practitioners`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${normalizedSiteUrl}/blog`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      // Include specialized sitemaps in fallback
      {
        url: `${normalizedSiteUrl}/sitemap-blog.xml`,
        lastModified: new Date(),
      },
      {
        url: `${normalizedSiteUrl}/sitemap-clinics.xml`,
        lastModified: new Date(),
      },
      {
        url: `${normalizedSiteUrl}/sitemap-practitioners.xml`,
        lastModified: new Date(),
      },
    ];
  }
}
