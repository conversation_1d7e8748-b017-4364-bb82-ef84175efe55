# Setting Up Strapi Webhooks for On-Demand Revalidation

This guide explains how to set up webhooks in Strapi to trigger on-demand revalidation in your Next.js application.

## Overview

Webhooks allow Strapi to notify your Next.js application when content changes, so it can revalidate the affected pages. This is more efficient than using a revalidation period, as pages are only revalidated when their content changes.

## Prerequisites

1. A running Strapi instance (local or production)
2. A running Next.js application with the revalidation API route
3. A secure token for authentication

## Step 1: Revalidation Token

A secure revalidation token has already been generated and configured:

```
x-revalidate-token: 3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=
```

## Step 2: Environment Variables Configuration

The token has been added to Vercel environment variables as `PREVIEW_SECRET`. The revalidation API is configured to check both `REVALIDATE_TOKEN` and `PREVIEW_SECRET` for backward compatibility.

## Step 3: Set Up Webhooks in Strapi Admin Panel

1. Log in to your Strapi admin panel
2. Navigate to Settings > Webhooks
3. Click "Create new webhook"

### For Blog Posts

1. Name: `Revalidate Blog Posts`
2. URL: `https://your-nextjs-domain.com/api/revalidate`
3. Headers:
   - `Content-Type`: `application/json`
4. Events:
   - Select all events under "Entry" for the "blog-post" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. Request body:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "blog-post",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

### For Clinics

1. Name: `Revalidate Clinics`
2. URL: `https://your-nextjs-domain.com/api/revalidate`
3. Headers:
   - `Content-Type`: `application/json`
4. Events:
   - Select all events under "Entry" for the "clinic" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. Request body:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "clinic",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

### For Practitioners

1. Name: `Revalidate Practitioners`
2. URL: `https://your-nextjs-domain.com/api/revalidate`
3. Headers:
   - `Content-Type`: `application/json`
4. Events:
   - Select all events under "Entry" for the "practitioner" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. Request body:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "practitioner",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

### For Categories

1. Name: `Revalidate Categories`
2. URL: `https://your-nextjs-domain.com/api/revalidate`
3. Headers:
   - `Content-Type`: `application/json`
4. Events:
   - Select all events under "Entry" for the "category" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. Request body:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "category",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

### For Specialties

1. Name: `Revalidate Specialties`
2. URL: `https://your-nextjs-domain.com/api/revalidate`
3. Headers:
   - `Content-Type`: `application/json`
4. Events:
   - Select all events under "Entry" for the "specialty" content type:
     - `Create entry`
     - `Update entry`
     - `Delete entry`
     - `Publish entry`
     - `Unpublish entry`
5. Request body:
   ```json
   {
     "token": "3ID510+kG34qf9mjSIoa/tgx23NCrC4g+qlwQDjdwbQ=",
     "contentType": "specialty",
     "id": "{{entry.id}}",
     "slug": "{{entry.slug}}"
   }
   ```

## Step 4: Test the Webhooks

1. Make a change to a content item in Strapi
2. Check the Strapi webhook logs to ensure the webhook was triggered
3. Check the Next.js logs to ensure the revalidation API route was called
4. Verify that the affected page was revalidated by checking the updated content

## Troubleshooting

### Webhook Not Triggering

- Check that the webhook is enabled in Strapi
- Verify that the correct events are selected
- Check the Strapi logs for any errors

### Revalidation Not Working

- Check that the token in the webhook matches the token in your Next.js environment variables
- Verify that the revalidation API route is correctly implemented
- Check the Next.js logs for any errors

## Security Considerations

- Keep your revalidation token secure
- Use HTTPS for webhook URLs
- Consider implementing IP filtering in your revalidation API route
