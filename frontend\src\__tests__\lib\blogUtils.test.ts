import {
  generateExcerpt,
  formatDate,
  truncateByWords,
  calculateReadingTime,
  mapStrapiBlogPostToProps
} from '@/lib/blogUtils';

// Mock the mediaUtils module
jest.mock('@/lib/mediaUtils', () => ({
  getStrapiMediaUrl: jest.fn((url) => {
    if (!url) return null;
    if (typeof url === 'string') {
      if (url.startsWith('http')) return url;
      return `http://localhost:1337${url}`;
    }
    return 'http://localhost:1337/mocked-media-url';
  })
}));

// Mock the logger module
jest.mock('@/lib/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

describe('Blog Utilities', () => {
  describe('generateExcerpt', () => {
    test('should return empty string for empty input', () => {
      expect(generateExcerpt('')).toBe('');
      expect(generateExcerpt(null as any)).toBe('');
      expect(generateExcerpt(undefined as any)).toBe('');
    });

    test('should strip HTML tags', () => {
      const html = '<p>This is a <strong>test</strong> paragraph.</p>';
      expect(generateExcerpt(html)).toBe('This is a test paragraph.');
    });

    test('should truncate text to specified length', () => {
      const longText = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies aliquam, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.';
      const excerpt = generateExcerpt(longText, 50);
      expect(excerpt.length).toBeLessThanOrEqual(53); // 50 + '...'
      expect(excerpt.endsWith('...')).toBe(true);
    });

    test('should not truncate text shorter than maxLength', () => {
      const shortText = 'This is a short text.';
      expect(generateExcerpt(shortText, 50)).toBe(shortText);
    });
  });

  describe('formatDate', () => {
    test('should format date in the expected format', () => {
      const date = '2023-06-15T10:30:00Z';
      expect(formatDate(date)).toBe('June 15, 2023');
    });
  });

  describe('truncateByWords', () => {
    test('should return empty string for empty input', () => {
      expect(truncateByWords('', 5)).toBe('');
      expect(truncateByWords(null, 5)).toBe('');
      expect(truncateByWords(undefined, 5)).toBe('');
    });

    test('should truncate text to specified word count', () => {
      const text = 'This is a test sentence with more than five words.';
      expect(truncateByWords(text, 5)).toBe('This is a test sentence...');
    });

    test('should not truncate text with fewer words than maxWords', () => {
      const text = 'Short text.';
      expect(truncateByWords(text, 5)).toBe(text);
    });
  });

  describe('calculateReadingTime', () => {
    test('should calculate reading time based on word count', () => {
      // Assuming 200 words per minute
      const text1000Words = 'word '.repeat(1000);
      // The implementation uses Math.ceil, so 1000/200 = 5, but with spaces it's more words
      expect(calculateReadingTime(text1000Words)).toBeGreaterThanOrEqual(5);

      const text100Words = 'word '.repeat(100);
      expect(calculateReadingTime(text100Words)).toBeGreaterThanOrEqual(1);
    });

    test('should return minimum of 1 minute for very short text', () => {
      const shortText = 'Just a few words.';
      expect(calculateReadingTime(shortText)).toBe(1);
    });
  });

  describe('mapStrapiBlogPostToProps', () => {
    test('should return null for invalid input', () => {
      expect(mapStrapiBlogPostToProps(null)).toBeNull();
      expect(mapStrapiBlogPostToProps(undefined)).toBeNull();
      expect(mapStrapiBlogPostToProps({})).toBeNull();
    });

    test('should map Strapi blog post to expected format', () => {
      const mockStrapiPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        content: '<p>This is test content.</p>',
        excerpt: 'Test excerpt',
        publishDate: '2023-06-15T10:30:00Z',
        featuredImage: { url: '/uploads/featured.jpg' },
        author_blogs: [
          {
            id: '2',
            name: 'Test Author',
            slug: 'test-author',
            profilePicture: { url: '/uploads/profile.jpg' },
            bio: 'Author bio'
          }
        ],
        blog_categories: [
          {
            id: '3',
            name: 'Test Category',
            slug: 'test-category'
          }
        ],
        blog_tags: [
          {
            name: 'test-tag'
          }
        ],
        related_posts: [
          {
            id: '4',
            title: 'Related Post',
            slug: 'related-post',
            excerpt: 'Related excerpt'
          }
        ]
      };

      const result = mapStrapiBlogPostToProps(mockStrapiPost);

      expect(result).toMatchObject({
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        content: '<p>This is test content.</p>',
        excerpt: 'Test excerpt',
        published_at: '2023-06-15T10:30:00Z',
        featured_image: 'http://localhost:1337/mocked-media-url',
        author: {
          id: '2',
          name: 'Test Author',
          slug: 'test-author',
          profile_picture: 'http://localhost:1337/mocked-media-url',
          bio: 'Author bio'
        },
        categories: [
          {
            id: '3',
            name: 'Test Category',
            slug: 'test-category'
          }
        ],
        tags: ['test-tag'],
        related_posts: [
          expect.objectContaining({
            id: '4',
            title: 'Related Post',
            slug: 'related-post',
            excerpt: 'Related excerpt'
          })
        ]
      });

      // Check that reading time is calculated
      expect(result?.reading_time).toBeGreaterThanOrEqual(1);
    });
  });
});
