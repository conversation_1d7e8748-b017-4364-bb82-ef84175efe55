'use strict';

/**
 * post-view controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::post-view.post-view', ({ strapi }) => ({
  /**
   * Get popular posts based on view count
   * @param {Object} ctx - The context object
   */
  async getPopularPosts(ctx) {
    try {
      // Get query parameters
      const { timeframe = 'week', limit = 4 } = ctx.query;
      
      // Parse limit to ensure it's a number
      const parsedLimit = parseInt(limit, 10) || 4;
      
      // Calculate the start date based on the timeframe
      let startDate = new Date();
      switch (timeframe) {
        case 'day':
          startDate.setDate(startDate.getDate() - 1);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'all':
          startDate = new Date(0); // Beginning of time
          break;
        default:
          startDate.setDate(startDate.getDate() - 7); // Default to week
      }
      
      // Format the date for the database query
      const formattedStartDate = startDate.toISOString();
      
      // Get the database connection
      const knex = strapi.db.connection;
      
      // Get the table names (they might have prefixes in production)
      const postViewsTable = strapi.db.config.tableName('post-views');
      const blogPostsTable = strapi.db.config.tableName('blog-posts');
      
      // Log the query parameters for debugging
      console.log(`Getting popular posts since ${formattedStartDate}, limit: ${parsedLimit}`);
      console.log(`Tables: post_views=${postViewsTable}, blog_posts=${blogPostsTable}`);
      
      // Query to get popular posts
      // This is a raw SQL query that:
      // 1. Counts views per post
      // 2. Joins with the blog posts table to get post details
      // 3. Orders by view count (most viewed first)
      // 4. Limits to the specified number of posts
      const query = knex
        .select(
          `${blogPostsTable}.id`,
          `${blogPostsTable}.title`,
          `${blogPostsTable}.slug`,
          `${blogPostsTable}.excerpt`,
          knex.raw(`CONCAT('${process.env.STRAPI_URL || 'http://localhost:1337'}', featured_image.url) as featured_image`),
          `${blogPostsTable}.publishDate as publish_date`,
          knex.raw(`COUNT(${postViewsTable}.id) as view_count`)
        )
        .from(postViewsTable)
        .join(`${blogPostsTable}`, `${postViewsTable}.post`, '=', `${blogPostsTable}.id`)
        .leftJoin('files', 'files.id', '=', `${blogPostsTable}.featuredImage`)
        .leftJoin('files_related_morphs', function() {
          this.on('files_related_morphs.file_id', '=', 'files.id')
            .andOn('files_related_morphs.related_id', '=', `${blogPostsTable}.id`)
            .andOn('files_related_morphs.related_type', '=', knex.raw('?', ['api::blog-post.blog-post']));
        })
        .leftJoin('upload_folders', 'upload_folders.id', '=', 'files.folder_id')
        .where(`${postViewsTable}.timestamp`, '>=', formattedStartDate)
        .groupBy(`${blogPostsTable}.id`)
        .orderBy('view_count', 'desc')
        .limit(parsedLimit);
      
      // Log the SQL query for debugging
      console.log('SQL Query:', query.toString());
      
      // Execute the query
      const popularPosts = await query;
      
      // If no popular posts found, return empty array
      if (!popularPosts || popularPosts.length === 0) {
        // Fallback: get most recent posts if no views data
        const recentPosts = await strapi.entityService.findMany('api::blog-post.blog-post', {
          sort: { publishDate: 'desc' },
          limit: parsedLimit,
          populate: ['featuredImage'],
        });
        
        // Map recent posts to the same format as popular posts
        const mappedRecentPosts = recentPosts.map(post => ({
          id: post.id,
          title: post.title,
          slug: post.slug,
          excerpt: post.excerpt,
          featured_image: post.featuredImage?.url 
            ? `${process.env.STRAPI_URL || 'http://localhost:1337'}${post.featuredImage.url}` 
            : null,
          publish_date: post.publishDate || post.createdAt,
          view_count: 0
        }));
        
        return { data: mappedRecentPosts };
      }
      
      // Return the popular posts
      return { data: popularPosts };
      
    } catch (error) {
      // Log the error
      console.error('Error getting popular posts:', error);
      
      // Return error response
      ctx.throw(500, 'Error getting popular posts');
    }
  }
}));
