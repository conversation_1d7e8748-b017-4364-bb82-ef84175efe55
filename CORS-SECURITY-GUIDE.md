# CORS Security Guide for Natural Healing Now

This document outlines the CORS (Cross-Origin Resource Sharing) security configuration for the Natural Healing Now application.

## CORS Configuration Overview

CORS is a security feature implemented by browsers that restricts web applications from making requests to domains different from the one that served the web page. Proper CORS configuration is essential for:

1. Allowing legitimate cross-origin requests from your frontend to your backend
2. Preventing unauthorized access to your API from malicious websites
3. Ensuring secure handling of credentials and sensitive data

## Current Implementation

The application uses a dynamic CORS configuration in Strapi that:

1. Explicitly allows specific trusted origins (your Vercel domains)
2. Allows Vercel preview deployments via pattern matching
3. Provides more permissive settings in development environments
4. Denies all other origins by default

## Security Best Practices

### 1. Avoid Wildcard Origins in Production

**NEVER** use `*` as an origin in production, especially when credentials are involved. Browsers will not allow requests with credentials to be made to a wildcard origin for security reasons.

```javascript
// INSECURE - DO NOT USE IN PRODUCTION
origin: '*'

// SECURE - Use specific origins
origin: ['https://your-domain.com', 'https://api.your-domain.com']
```

### 2. Use a Function for Dynamic Origin Validation

Using a function for the `origin` option allows for more sophisticated validation logic:

```javascript
origin: (ctx) => {
  const requestOrigin = ctx.request.header.origin;
  
  // Check against allowed origins
  if (allowedOrigins.includes(requestOrigin)) {
    return requestOrigin;
  }
  
  // Deny all other origins
  return false;
}
```

### 3. Credentials and CORS

When `credentials: true` is set, browsers will include cookies and authentication headers in cross-origin requests. This requires:

- The `origin` cannot be `'*'`
- The `origin` must be explicitly specified
- The response must include `Access-Control-Allow-Credentials: true`

Only enable credentials if your application needs to send cookies or authentication headers across origins.

### 4. Limit HTTP Methods and Headers

Only allow the HTTP methods and headers that your application actually needs:

```javascript
methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
headers: ['Content-Type', 'Authorization']
```

### 5. Use Environment-Specific Configurations

Maintain different CORS configurations for development and production environments:

- Development: More permissive to facilitate local testing
- Production: Strict and limited to only necessary origins

## Troubleshooting CORS Issues

If you encounter CORS issues:

1. Check browser console for specific error messages
2. Verify that the origin making the request is in the allowed list
3. Ensure the correct headers are being allowed
4. Check if credentials are being sent and if the CORS configuration supports them
5. Use the Network tab in browser DevTools to inspect the actual request and response headers

## Proxy Solution

As a fallback for situations where direct CORS configuration is not possible or not working, the application includes a server-side proxy in the Next.js application that forwards requests to Strapi. This approach avoids CORS issues entirely since the request to Strapi comes from the same server that hosts the Next.js application.

## Regular Review

Security configurations should be regularly reviewed and updated. As the application evolves, ensure that:

1. Only necessary origins are allowed
2. Deprecated origins are removed
3. Security settings are tested after each update
4. CORS logs are monitored for potential abuse attempts

By following these guidelines, you can maintain a secure CORS configuration that balances accessibility with security.
