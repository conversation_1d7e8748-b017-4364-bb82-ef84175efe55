"use strict";exports.id=7424,exports.ids=[7424],exports.modules={2699:(e,r,t)=>{t.d(r,{default:()=>x});var n=t(60687),a=t(16189),s=t(43210),i=t(26800),l=t(30104),o=t(44725),c=t(7610),d=t(49384);let u=(e,r)=>r<=7?Array.from({length:r},(e,r)=>r+1):e<=3?[1,2,3,"...",r-1,r]:e>=r-2?[1,2,"...",r-2,r-1,r]:[1,"...",e-1,e,e+1,"...",r];function m({totalPages:e}){let r=(0,a.usePathname)(),t=(0,a.useSearchParams)(),i=Number(t.get("page"))||1,l=(0,s.useCallback)(e=>{let n=new URLSearchParams(t.toString());return n.set("page",e.toString()),`${r}?${n.toString()}`},[r,t]),o=(0,s.useCallback)(e=>{if("..."===e)return;let n=new URLSearchParams(t.toString());n.set("page",e.toString()),window.history.pushState(null,"",`${r}?${n.toString()}`),window.dispatchEvent(new CustomEvent("paginationchange",{detail:{page:e}}))},[r,t]),c=u(i,e);return e<=1?null:(0,n.jsxs)("div",{className:"inline-flex",children:[(0,n.jsx)(h,{direction:"left",href:l(i-1),onClick:()=>i>1&&o(i-1),isDisabled:i<=1}),(0,n.jsx)("div",{className:"flex -space-x-px",children:c.map((e,r)=>{let t;return 0===r&&(t="first"),r===c.length-1&&(t="last"),1===c.length&&(t="single"),"..."===e&&(t="middle"),(0,n.jsx)(f,{page:e,href:l(e),onClick:()=>o(e),position:t,isActive:i===e},`${e}-${r}`)})}),(0,n.jsx)(h,{direction:"right",href:l(i+1),onClick:()=>i<e&&o(i+1),isDisabled:i>=e})]})}function f({page:e,href:r,onClick:t,isActive:a,position:s}){let i=(0,d.A)("flex h-10 w-10 items-center justify-center text-sm border",{"rounded-l-md":"first"===s||"single"===s,"rounded-r-md":"last"===s||"single"===s,"z-10 bg-emerald-600 border-emerald-600 text-white":a,"hover:bg-gray-100":!a&&"middle"!==s,"text-gray-300 pointer-events-none":"middle"===s});return a||"middle"===s?(0,n.jsx)("div",{className:i,children:e}):(0,n.jsx)("button",{onClick:t,className:i,children:e})}function h({href:e,onClick:r,direction:t,isDisabled:a}){let s=(0,d.A)("flex h-10 w-10 items-center justify-center rounded-md border",{"pointer-events-none text-gray-300":a,"hover:bg-gray-100":!a,"mr-2 md:mr-4":"left"===t,"ml-2 md:ml-4":"right"===t}),i="left"===t?(0,n.jsx)(o.A,{className:"w-4"}):(0,n.jsx)(c.A,{className:"w-4"});return a?(0,n.jsx)("div",{className:s,children:i}):(0,n.jsx)("button",{onClick:r,className:s,children:i})}function x({clinics:e,practitioners:r,totalPages:t,initialTab:o="clinics"}){let c=(0,a.useSearchParams)(),[d,u]=(0,s.useState)(("practitioners"===c.get("tab")?"practitioners":"clinics")||o);return(0,n.jsxs)("div",{children:["clinics"===d?(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.length>0?e.map(e=>(0,n.jsx)(i.default,{clinic:e,showContactInfo:!1},e.id)):(0,n.jsx)("div",{className:"col-span-full text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:"No clinics found matching your criteria."})})}):(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.length>0?r.map(e=>(0,n.jsx)(l.default,{practitioner:e},e.id)):(0,n.jsx)("div",{className:"col-span-full text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500",children:"No practitioners found matching your criteria."})})}),t>1&&(0,n.jsx)("div",{className:"mt-8 flex justify-center",children:(0,n.jsx)(m,{totalPages:t})})]},d)}},7610:(e,r,t)=>{t.d(r,{A:()=>a});var n=t(43210);let a=n.forwardRef(function({title:e,titleId:r,...t},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?n.createElement("title",{id:r},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},10592:(e,r,t)=>{t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\SearchInput.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\SearchInput.tsx","default")},14007:(e,r,t)=>{t.d(r,{A:()=>i});var n=t(37413),a=t(4536),s=t.n(a);let i=({currentPath:e})=>(0,n.jsx)("div",{className:"py-16 bg-emerald-50",children:(0,n.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold mb-6 text-gray-800",children:"Explore Further"}),(0,n.jsxs)("p",{className:"text-lg mb-8 max-w-3xl mx-auto text-gray-600",children:["Didn't find what you were looking for?",(0,n.jsx)("br",{}),"Explore our complete listings of clinics, practitioners, and categories."]}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:["/clinics"!==e&&(0,n.jsx)(s(),{href:"/clinics",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold",children:"Find All Clinics"}),"/practitioners"!==e&&(0,n.jsx)(s(),{href:"/practitioners",className:"bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-semibold",children:"Find All Practitioners"}),(0,n.jsx)(s(),{href:"/categories",className:"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-semibold",children:"View All Categories"})]})]})})},26800:(e,r,t)=>{t.d(r,{default:()=>o});var n=t(60687),a=t(85814),s=t.n(a),i=t(17019),l=t(20255);let o=({clinic:e,showContactInfo:r=!0,prefetchedData:t=!1})=>{let a=t?{pathname:`/clinics/${e.slug}`,query:{prefetched:"true"}}:`/clinics/${e.slug}`;return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 flex-grow",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,n.jsx)(s(),{href:a,className:"hover:text-emerald-600",children:e.name})}),e.isVerified&&(0,n.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,n.jsx)(l.AI8,{color:"#009967",size:14}),(0,n.jsx)("span",{children:"VERIFIED"})]}),e.description&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.description}),(0,n.jsxs)("div",{className:"space-y-2 text-sm text-gray-500",children:[e.address&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.HzC,{className:"mr-2 text-emerald-500"}),(0,n.jsxs)("span",{children:[e.address.city,", ",e.address.stateProvince]})]}),r&&e.contactInfo?.phoneNumber&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.QFc,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("span",{children:e.contactInfo.phoneNumber})]}),r&&e.contactInfo?.websiteUrl&&(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(i.VeH,{className:"mr-2 text-emerald-500"}),(0,n.jsx)("a",{href:e.contactInfo.websiteUrl,target:"_blank",rel:"nofollow noopener noreferrer",className:"hover:text-emerald-600",children:"Visit Website"})]})]})]}),(0,n.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,n.jsx)(s(),{href:a,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Details →"})})]})}},30104:(e,r,t)=>{t.d(r,{default:()=>c});var n=t(60687),a=t(85814),s=t.n(a),i=t(20255),l=t(43210),o=t(68326);let c=({practitioner:e,prefetchedData:r=!1})=>{let[t,a]=(0,l.useState)(!1),c=(0,l.useRef)(!1);(0,l.useEffect)(()=>{if(c.current)return;let r=(0,o.b3)(e.slug);r&&a(!0),e._hasDetailedData&&!r&&((0,o.tq)(e),a(!0)),c.current=!0},[e]);let d=r||e._hasDetailedData||t?{pathname:`/practitioners/${e.slug}`,query:{prefetched:"true"}}:`/practitioners/${e.slug}`;return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 flex flex-col",children:[(0,n.jsxs)("div",{className:"p-4 flex-grow",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-1",children:(0,n.jsx)(s(),{href:d,className:"hover:text-emerald-600",children:e.name})}),e.isVerified&&(0,n.jsxs)("div",{className:"flex items-center gap-x-1 text-emerald-700 mb-2 text-xs font-medium",children:[(0,n.jsx)(i.AI8,{color:"#009967",size:14}),(0,n.jsx)("span",{children:"VERIFIED"})]}),e.bio&&(0,n.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3",children:e.bio})]}),(0,n.jsx)("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-100 mt-auto",children:(0,n.jsx)(s(),{href:d,className:"text-emerald-600 hover:text-emerald-700 font-medium text-sm",children:"View Profile →"})})]})}},44725:(e,r,t)=>{t.d(r,{A:()=>a});var n=t(43210);let a=n.forwardRef(function({title:e,titleId:r,...t},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?n.createElement("title",{id:r},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},49384:(e,r,t)=>{t.d(r,{A:()=>n});let n=function(){for(var e,r,t=0,n="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,n,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var s=r.length;for(t=0;t<s;t++)r[t]&&(n=e(r[t]))&&(a&&(a+=" "),a+=n)}else for(n in r)r[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=r);return n}},51006:(e,r,t)=>{t.d(r,{default:()=>c});var n=t(60687),a=t(16189),s=t(43210),i=t(17019),l=t(85814),o=t.n(l);function c({slug:e,pageType:r,clinicCount:t,practitionerCount:l,initialTab:c="clinics"}){let d=(0,a.useSearchParams)();(0,a.usePathname)();let u=(0,a.useRouter)(),[m,f]=(0,s.useState)(("practitioners"===d.get("tab")?"practitioners":"clinics")||c),h=t=>n=>{n.preventDefault();let a=new URLSearchParams(d);a.set("tab",t),u.push(`/${r}/${e}?${a.toString()}`),f(t)};return(0,n.jsx)("div",{className:"mb-6 border-b border-gray-200",children:(0,n.jsxs)("nav",{className:"-mb-px flex space-x-8","aria-label":"Tabs",children:[(0,n.jsxs)(o(),{href:`/${r}/${e}?tab=clinics`,className:`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2
            ${"clinics"===m?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,"aria-current":"clinics"===m?"page":void 0,onClick:h("clinics"),children:[(0,n.jsx)(i.V5Y,{className:"h-4 w-4"}),(0,n.jsxs)("span",{children:["categories"===r?"Clinics":"Related Clinics"," (",t,")"]})]}),(0,n.jsxs)(o(),{href:`/${r}/${e}?tab=practitioners`,className:`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2
            ${"practitioners"===m?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,"aria-current":"practitioners"===m?"page":void 0,onClick:h("practitioners"),children:[(0,n.jsx)(i.JXP,{className:"h-4 w-4"}),(0,n.jsxs)("span",{children:["categories"===r?"Practitioners":"Related Practitioners"," (",l,")"]})]})]})})}},68016:(e,r,t)=>{t.d(r,{default:()=>l});var n=t(60687),a=t(43210),s=t(16189),i=t(17019);function l({placeholder:e,paramName:r="query",icon:t}){let l=(0,s.useSearchParams)(),o=(0,s.usePathname)(),{replace:c}=(0,s.useRouter)(),d=function(e,r,t){var n=this,s=(0,a.useRef)(null),i=(0,a.useRef)(0),l=(0,a.useRef)(null),o=(0,a.useRef)([]),c=(0,a.useRef)(),d=(0,a.useRef)(),u=(0,a.useRef)(e),m=(0,a.useRef)(!0);u.current=e;var f="undefined"!=typeof window,h=!r&&0!==r&&f;if("function"!=typeof e)throw TypeError("Expected a function");r=+r||0;var x=!!(t=t||{}).leading,g=!("trailing"in t)||!!t.trailing,p="maxWait"in t,b="debounceOnServer"in t&&!!t.debounceOnServer,v=p?Math.max(+t.maxWait||0,r):null;return(0,a.useEffect)(function(){return m.current=!0,function(){m.current=!1}},[]),(0,a.useMemo)(function(){var e=function(e){var r=o.current,t=c.current;return o.current=c.current=null,i.current=e,d.current=u.current.apply(t,r)},t=function(e,r){h&&cancelAnimationFrame(l.current),l.current=h?requestAnimationFrame(e):setTimeout(e,r)},a=function(e){if(!m.current)return!1;var t=e-s.current;return!s.current||t>=r||t<0||p&&e-i.current>=v},j=function(r){return l.current=null,g&&o.current?e(r):(o.current=c.current=null,d.current)},w=function e(){var n=Date.now();if(a(n))return j(n);if(m.current){var l=r-(n-s.current);t(e,p?Math.min(l,v-(n-i.current)):l)}},y=function(){if(f||b){var u=Date.now(),h=a(u);if(o.current=[].slice.call(arguments),c.current=n,s.current=u,h){if(!l.current&&m.current)return i.current=s.current,t(w,r),x?e(s.current):d.current;if(p)return t(w,r),e(s.current)}return l.current||t(w,r),d.current}};return y.cancel=function(){l.current&&(h?cancelAnimationFrame(l.current):clearTimeout(l.current)),i.current=0,o.current=s.current=c.current=l.current=null},y.isPending=function(){return!!l.current},y.flush=function(){return l.current?j(Date.now()):d.current},y},[x,p,r,v,g,h,f,b])}(e=>{console.log(`Searching... ${e}`);let t=new URLSearchParams(l);t.set("page","1"),e?t.set(r,e):t.delete(r),c(`${o}?${t.toString()}`)},500);return(0,n.jsxs)("div",{className:"relative flex flex-1 flex-shrink-0",children:[(0,n.jsx)("label",{htmlFor:r,className:"sr-only",children:"Search"}),(0,n.jsx)("input",{id:r,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:e,onChange:e=>{d(e.target.value)},defaultValue:l.get(r)?.toString()}),t||(0,n.jsx)(i.CKj,{className:"absolute left-3 top-1/2 h-[18px] w-[18px] -translate-y-1/2 text-gray-400 peer-focus:text-gray-900"})]})}},68326:(e,r,t)=>{t.d(r,{b3:()=>a,tq:()=>n});function n(e){var r;if(!e||!e.id||!e.slug)return;let t=(e.slug,null);t&&(!e._hasDetailedData||t._hasDetailedData)||function(e,r,t=3e5){}(`practitioner_${e.slug}`,0,18e5)}function a(e){var r;return null}},69290:(e,r,t)=>{t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\TabContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\TabContent.tsx","default")},74124:(e,r,t)=>{t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\naturalhealingnow\\\\frontend\\\\src\\\\components\\\\shared\\\\TabSwitcher.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\naturalhealingnow\\frontend\\src\\components\\shared\\TabSwitcher.tsx","default")}};