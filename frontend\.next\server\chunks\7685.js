exports.id=7685,exports.ids=[7685],exports.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},36463:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var n=function(e){return e.DEBUG="debug",e.INFO="info",e.WARN="warn",e.ERROR="error",e}({});let i={enabled:!1,level:"info",prefix:"[NHN]"};function o(e,t,...r){if(!i.enabled)return;let u=Object.values(n),a=u.indexOf(i.level);if(u.indexOf(e)>=a){let n=i.prefix?`${i.prefix} `:"",o=`${n}${t}`;switch(e){case"debug":console.debug(o,...r);break;case"info":console.info(o,...r);break;case"warn":console.warn(o,...r);break;case"error":console.error(o,...r)}}}let u={debug:function(e,...t){o("debug",e,...t)},info:function(e,...t){o("info",e,...t)},warn:function(e,...t){o("warn",e,...t)},error:function(e,...t){o("error",e,...t)},configure:function(e){i={...i,...e}}}},39916:(e,t,r)=>{"use strict";var n=r(97576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"unauthorized")&&r.d(t,{unauthorized:function(){return n.unauthorized}})},48976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},58669:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,68016)),Promise.resolve().then(r.bind(r,2699)),Promise.resolve().then(r.bind(r,51006))},60877:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,10592)),Promise.resolve().then(r.bind(r,69290)),Promise.resolve().then(r.bind(r,74124))},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,u.isNextRouterError)(t)||(0,o.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,a.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),i=r(52637),o=r(51846),u=r(31162),a=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82158:(e,t,r)=>{"use strict";r.d(t,{Jf:()=>f,Rb:()=>p,Z5:()=>d,tz:()=>c});var n=r(36463);let i={NEXT_PUBLIC_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_API_URL:"https://nice-badge-2130241d6c.strapiapp.com",NEXT_PUBLIC_STRAPI_MEDIA_URL:"https://nice-badge-2130241d6c.media.strapiapp.com",NEXT_PUBLIC_SITE_URL:process.env.NEXT_PUBLIC_SITE_URL,IMAGE_HOSTNAME:process.env.IMAGE_HOSTNAME,NODE_ENV:"production"},o=i.NEXT_PUBLIC_STRAPI_API_URL||i.NEXT_PUBLIC_API_URL||("development"===i.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.strapiapp.com"),u=(()=>{if(i.NEXT_PUBLIC_STRAPI_MEDIA_URL)return l(s(i.NEXT_PUBLIC_STRAPI_MEDIA_URL));if(i.IMAGE_HOSTNAME)return l(s(i.IMAGE_HOSTNAME));try{let e=new URL(o);if(e.hostname.endsWith("strapiapp.com"))return`${l(e.protocol)}//${e.hostname.replace("strapiapp.com","media.strapiapp.com")}`;return l(s(o))}catch(e){return"development"===i.NODE_ENV?"http://localhost:1337":"https://nice-badge-2130241d6c.media.strapiapp.com"}})(),a=i.NEXT_PUBLIC_SITE_URL||(i.NEXT_PUBLIC_API_URL&&i.NEXT_PUBLIC_API_URL.includes("strapiapp.com")?i.NEXT_PUBLIC_API_URL.replace(".strapiapp.com",".vercel.app"):"https://naturalhealingnow.vercel.app");function l(e){return e?e.replace(/^http:/,"https:"):e}function s(e){return e&&e.endsWith("/")?e.slice(0,-1):e}function d(e,t={debug:!1}){if(t.debug&&n.Ay.debug("getStrapiMediaUrl input:",{type:typeof e,isNull:null===e,isUndefined:void 0===e,value:e}),!e)return null;let r=null;if("string"==typeof e?r=e:"object"==typeof e&&(r=e.url||e.data?.attributes?.url||e.data?.url||null),!r)return t.debug,n.Ay.warn("Could not extract initial URL from mediaInput in getStrapiMediaUrl",{mediaInput:e}),null;let i=f(r);return i?i.startsWith("http://")||i.startsWith("https://")?i:u?`${u}${i.startsWith("/")?"":"/"}${i}`:(n.Ay.warn("STRAPI_MEDIA_URL is not defined, falling back to EFFECTIVE_STRAPI_URL for getStrapiMediaUrl",{sanitizedUrl:i}),`${o}${i.startsWith("/")?"":"/"}${i}`):(t.debug,n.Ay.warn("URL became empty after sanitization in getStrapiMediaUrl",{originalUrl:r}),null)}function c(e){if(!e||!e.profilePicture)return null;let t=e.profilePicture,r=t.url||t.data?.attributes?.url||t.data?.url||t.formats?.thumbnail?.url;return r?d(r):d(t)}function f(e){let t;if("string"==typeof e&&(e.startsWith("https://")||e.startsWith("http://")||e.startsWith("/")))return e.startsWith("http://")?e.replace(/^http:/,"https:"):e;if(!e)return"";if("object"==typeof e&&e.url&&"string"==typeof e.url)t=e.url;else{if("string"!=typeof e)return n.Ay.warn("Invalid input type for sanitizeUrl. Expected string or object with url property.",{inputType:typeof e}),"";t=e}(t=t.trim()).toLowerCase().startsWith("undefined")&&(t=t.substring(9),n.Ay.info('Removed "undefined" prefix from URL',{original:e,new:t}));let r=o.replace(/^https?:\/\//,"").split("/")[0],i=u.replace(/^https?:\/\//,"").split("/")[0];if(r&&i&&t.includes(r)&&t.includes(i)){let e=RegExp(`(https?://)?(${r})(/*)(https?://)?(${i})`,"gi"),o=`https://${i}`;if(e.test(t)){let u=t;t=t.replace(e,o),n.Ay.info("Fixed concatenated Strapi domains",{original:u,fixed:t,apiDomain:r,mediaDomain:i})}}if(t.includes("https//")){let e=t;t=t.replace(/https\/\//g,"https://"),n.Ay.info("Fixed missing colon in URL (https//)",{original:e,fixed:t})}if(t.startsWith("//")?t=`https:${t}`:(t.includes("media.strapiapp.com")||t.includes(i))&&!t.startsWith("http")?t=`https://${t}`:(t.startsWith("localhost")||t.startsWith(r.split(".")[0]))&&(t=`https://${t}`),t.startsWith("/"))return t;if(t.startsWith("http://")||t.startsWith("https://"))try{return new URL(t),t}catch(e){if(n.Ay.error("URL parsing failed after sanitization attempts",{url:t,error:e}),!t.includes("://")&&!t.includes("."))return t;return""}return u&&t&&!t.includes("://")?(n.Ay.debug("Assuming relative media path, prepending STRAPI_MEDIA_URL",{path:t}),`/${t}`):(n.Ay.warn("sanitizeUrl could not produce a valid absolute or relative URL",{originalInput:e,finalSanitized:t}),t)}function p(e){if(!e)return;let t=f(e);if(t){if(t.startsWith("http://")||t.startsWith("https://"))return t.replace(/^http:/,"https:");if(u){let r=`${u}${t.startsWith("/")?"":"/"}${t}`;return n.Ay.debug("Constructed OG image URL from relative path",{original:e,final:r}),r.replace(/^http:/,"https:")}if(n.Ay.warn("Could not determine OG image URL confidently",{originalUrl:e,processedUrl:t}),o)return`${o}${t.startsWith("/")?"":"/"}${t}`.replace(/^http:/,"https:")}}"development"===i.NODE_ENV&&n.Ay.debug("Media Utils Initialized:",{EFFECTIVE_STRAPI_URL:o,STRAPI_MEDIA_URL:u,SITE_URL:a})},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return u},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return s},permanentRedirect:function(){return l},redirect:function(){return a}});let n=r(52836),i=r(49026),o=r(19121).actionAsyncStorage;function u(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let o=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",o}function a(e,t){var r;throw null!=t||(t=(null==o||null==(r=o.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),u(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=i.RedirectType.replace),u(e,t,n.RedirectStatusCode.PermanentRedirect)}function s(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return i.RedirectType},forbidden:function(){return u.forbidden},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return a.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(86897),i=r(49026),o=r(62765),u=r(48976),a=r(70899),l=r(163);class s extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new s}delete(){throw new s}set(){throw new s}sort(){throw new s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};