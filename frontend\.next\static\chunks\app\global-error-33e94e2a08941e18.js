(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4219],{347:()=>{},3767:(e,s,t)=>{Promise.resolve().then(t.bind(t,8385))},6686:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_d65c78"}},8385:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(5155);t(2115);var n=t(6686),l=t.n(n);function a(e){let{error:s,reset:t}=e;return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:l().className,children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 px-4",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-md p-8",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full bg-red-100",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})})}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-center text-gray-900 mb-2",children:"Something went wrong!"}),(0,r.jsx)("p",{className:"text-gray-600 text-center mb-6",children:"We're sorry, but there was a critical error loading the application."}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("button",{onClick:t,className:"px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors",children:"Try Again"})}),!1]})})})})}t(347)}},e=>{var s=s=>e(e.s=s);e.O(0,[6947,7690,8441,1684,7358],()=>s(3767)),_N_E=e.O()}]);