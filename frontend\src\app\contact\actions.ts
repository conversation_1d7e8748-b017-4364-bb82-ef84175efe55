"use server";

import nodemailer from "nodemailer";

interface FormState {
  message: string;
  success: boolean;
  errors?: Record<string, string[] | undefined>;
}

export async function sendContactMessage(
  prevState: FormState | undefined,
  formData: FormData
): Promise<FormState> {
  const name = formData.get("name") as string;
  const email = formData.get("email") as string;
  const subject = formData.get("subject") as string;
  const message = formData.get("message") as string;

  // Basic validation
  const errors: Record<string, string[]> = {};
  if (!name) {
    errors.name = ["Name is required."];
  }
  if (!email) {
    errors.email = ["Email is required."];
  } else if (!/\S+@\S+\.\S+/.test(email)) {
    errors.email = ["Invalid email address."];
  }
  if (!subject) {
    errors.subject = ["Subject is required."];
  }
  if (!message) {
    errors.message = ["Message is required."];
  }

  if (Object.keys(errors).length > 0) {
    return {
      message: "Form submission failed. Please check the errors below.",
      success: false,
      errors,
    };
  }

  const {
    SMTP_HOST,
    SMTP_PORT,
    SMTP_USER,
    SMTP_PASS,
    CONTACT_FORM_RECIPIENT,
  } = process.env;

  if (
    !SMTP_HOST ||
    !SMTP_PORT ||
    !SMTP_USER ||
    !SMTP_PASS ||
    !CONTACT_FORM_RECIPIENT
  ) {
    console.error("SMTP environment variables are not fully configured.");
    return {
      message:
        "Server configuration error. Could not send message. Please try again later.",
      success: false,
    };
  }

  const transporter = nodemailer.createTransport({
    host: SMTP_HOST,
    port: parseInt(SMTP_PORT, 10),
    secure: parseInt(SMTP_PORT, 10) === 465, // true for 465, false for other ports (like 587)
    auth: {
      user: SMTP_USER,
      pass: SMTP_PASS,
    },
    // Increase timeout if needed, default is 60s
    // connectionTimeout: 1000 * 10, // 10 seconds
    // greetingTimeout: 1000 * 10,
    // socketTimeout: 1000 * 10,
  });

  try {
    await transporter.sendMail({
      from: `"${name}" <${SMTP_USER}>`, // Send from the authenticated user
      replyTo: email, // Set reply-to as the sender's email
      to: CONTACT_FORM_RECIPIENT,
      subject: `New Contact Form: ${subject}`,
      text: `You have received a new message from your website contact form:\n\n
Name: ${name}\n
Email: ${email}\n
Subject: ${subject}\n
Message:\n${message}`,
      html: `<p>You have received a new message from your website contact form:</p>
             <ul>
               <li><strong>Name:</strong> ${name}</li>
               <li><strong>Email:</strong> ${email}</li>
               <li><strong>Subject:</strong> ${subject}</li>
             </ul>
             <p><strong>Message:</strong></p>
             <p>${message.replace(/\n/g, "<br>")}</p>`,
    });

    return {
      message: "Message sent successfully! We will get back to you soon.",
      success: true,
    };
  } catch (error) {
    console.error("Failed to send email:", error);
    // Check for specific error types if needed
    // let errorMessage = "Failed to send message due to a server error. Please try again later.";
    // if (error instanceof Error) {
    //   // You might want to log error.message or parts of it, but be careful not to expose sensitive details
    // }
    return {
      message: "Failed to send message. Please try again later.",
      success: false,
    };
  }
}
