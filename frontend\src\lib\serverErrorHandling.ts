'use server';

import { notFound } from 'next/navigation';
import ServerError from '@/components/shared/ServerError';

interface ErrorHandlingOptions {
  notFoundOn404?: boolean;
  logErrors?: boolean;
  defaultMessage?: string;
}

/**
 * Handles API errors in server components
 * @param fn The async function to execute
 * @param options Error handling options
 * @returns The result of the function or a fallback UI
 */
export async function withErrorHandling<T>(
  fn: () => Promise<T>,
  options: ErrorHandlingOptions = {}
): Promise<T | JSX.Element> {
  const {
    notFoundOn404 = true,
    logErrors = true,
    defaultMessage = 'An error occurred while fetching data',
  } = options;

  try {
    return await fn();
  } catch (error) {
    if (logErrors) {
      console.error('Server component error:', error);
    }

    // Type assertion for error to access response property
    const typedError = error as { response?: { status: number } };

    // Handle 404 errors - use Next.js notFound() function
    if (notFoundOn404 && typedError.response?.status === 404) {
      notFound();
    }

    // Get status code if available
    const statusCode = typedError.response?.status || 500;

    // Get error message with proper type handling
    const message = (error as Error).message || defaultMessage;

    // Return a server error component
    return {
      __html: `<div class="server-error">
        <h1>Error ${statusCode}</h1>
        <p>${message}</p>
      </div>`
    } as unknown as JSX.Element;
  }
}

/**
 * Handles data fetching in server components with proper error handling
 * Returns the data or null if not found (triggering notFound)
 */
export async function fetchWithErrorHandling<T>(
  fetchFn: () => Promise<T | null>,
  notFoundIfNull: boolean = true
): Promise<T> {
  try {
    const data = await fetchFn();

    // If data is null and we want to show a 404, call notFound()
    if (data === null && notFoundIfNull) {
      notFound();
    }

    return data as T;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error; // Let the error.tsx boundary handle it
  }
}

/**
 * Safely executes a function and returns null if it fails
 * Useful for non-critical data fetching in server components
 */
export async function safeExecute<T>(
  fn: () => Promise<T>,
  fallbackValue: T | null = null
): Promise<T | null> {
  try {
    return await fn();
  } catch (error) {
    console.error('Error in safeExecute:', error);
    return fallbackValue;
  }
}
