// This is a simplified setup for testing with real Strapi data
// We're not starting a Strapi instance here, just setting up the test environment
const dotenv = require('dotenv');
const path = require('path');

module.exports = async () => {
  // Load environment variables from .env.test file
  dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });

  // Set up a global configuration for tests
  global.testConfig = {
    apiUrl: process.env.TEST_API_URL || 'http://localhost:1337',
    useRealData: process.env.TEST_WITH_REAL_DATA === 'true',
    // You can add more test configuration here as needed
  };

  console.log('Test setup complete. Using API URL:', global.testConfig.apiUrl);
  console.log('Using real data:', global.testConfig.useRealData);
};
