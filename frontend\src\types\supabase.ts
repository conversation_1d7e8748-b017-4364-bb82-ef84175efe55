export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      authors: {
        Row: {
          bio: string | null
          created_at: string
          email: string | null
          id: string
          name: string
          profile_picture: string | null
          slug: string
          updated_at: string
          website: string | null
        }
        Insert: {
          bio?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name: string
          profile_picture?: string | null
          slug: string
          updated_at?: string
          website?: string | null
        }
        Update: {
          bio?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          profile_picture?: string | null
          slug?: string
          updated_at?: string
          website?: string | null
        }
        Relationships: []
      }
      blog_categories: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          seo: Json | null
          slug: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          seo?: Json | null
          slug: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          seo?: Json | null
          slug?: string
          updated_at?: string
        }
        Relationships: []
      }
      blog_posts: {
        Row: {
          author_id: string | null
          content: string
          created_at: string
          excerpt: string | null
          featured_image: string | null
          id: string
          publish_date: string
          seo: Json | null
          slug: string
          status: string
          title: string
          updated_at: string
        }
        Insert: {
          author_id?: string | null
          content: string
          created_at?: string
          excerpt?: string | null
          featured_image?: string | null
          id?: string
          publish_date: string
          seo?: Json | null
          slug: string
          status?: string
          title: string
          updated_at?: string
        }
        Update: {
          author_id?: string | null
          content?: string
          created_at?: string
          excerpt?: string | null
          featured_image?: string | null
          id?: string
          publish_date?: string
          seo?: Json | null
          slug?: string
          status?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "blog_posts_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "authors"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_posts_categories_links: {
        Row: {
          blog_category_id: string
          blog_post_id: string
        }
        Insert: {
          blog_category_id: string
          blog_post_id: string
        }
        Update: {
          blog_category_id?: string
          blog_post_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "blog_posts_categories_links_blog_category_id_fkey"
            columns: ["blog_category_id"]
            isOneToOne: false
            referencedRelation: "blog_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "blog_posts_categories_links_blog_post_id_fkey"
            columns: ["blog_post_id"]
            isOneToOne: false
            referencedRelation: "blog_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_posts_tags_links: {
        Row: {
          blog_post_id: string
          blog_tag_id: string
        }
        Insert: {
          blog_post_id: string
          blog_tag_id: string
        }
        Update: {
          blog_post_id?: string
          blog_tag_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "blog_posts_tags_links_blog_post_id_fkey"
            columns: ["blog_post_id"]
            isOneToOne: false
            referencedRelation: "blog_posts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "blog_posts_tags_links_blog_tag_id_fkey"
            columns: ["blog_tag_id"]
            isOneToOne: false
            referencedRelation: "blog_tags"
            referencedColumns: ["id"]
          },
        ]
      }
      blog_tags: {
        Row: {
          created_at: string
          id: string
          name: string
          slug: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          slug: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          slug?: string
          updated_at?: string
        }
        Relationships: []
      }
      categories: {
        Row: {
          created_at: string
          description: string | null
          featured_image: string | null
          icon: string | null
          id: string
          name: string
          seo: Json | null
          slug: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          featured_image?: string | null
          icon?: string | null
          id?: string
          name: string
          seo?: Json | null
          slug: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          featured_image?: string | null
          icon?: string | null
          id?: string
          name?: string
          seo?: Json | null
          slug?: string
          updated_at?: string
        }
        Relationships: []
      }
      clinics: {
        Row: {
          address: Json
          appointment_options: string[] | null
          contact_info: Json | null
          created_at: string
          description: string | null
          featured_image: string | null
          gallery: Json | null
          id: string
          is_active: boolean
          is_featured: boolean
          location: Json
          logo: string | null
          name: string
          payment_methods: string[] | null
          seo: Json | null
          slug: string
          updated_at: string
        }
        Insert: {
          address: Json
          appointment_options?: string[] | null
          contact_info?: Json | null
          created_at?: string
          description?: string | null
          featured_image?: string | null
          gallery?: Json | null
          id?: string
          is_active?: boolean
          is_featured?: boolean
          location: Json
          logo?: string | null
          name: string
          payment_methods?: string[] | null
          seo?: Json | null
          slug: string
          updated_at?: string
        }
        Update: {
          address?: Json
          appointment_options?: string[] | null
          contact_info?: Json | null
          created_at?: string
          description?: string | null
          featured_image?: string | null
          gallery?: Json | null
          id?: string
          is_active?: boolean
          is_featured?: boolean
          location?: Json
          logo?: string | null
          name?: string
          payment_methods?: string[] | null
          seo?: Json | null
          slug?: string
          updated_at?: string
        }
        Relationships: []
      }
      clinics_categories_links: {
        Row: {
          category_id: string
          clinic_id: string
        }
        Insert: {
          category_id: string
          clinic_id: string
        }
        Update: {
          category_id?: string
          clinic_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "clinics_categories_links_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clinics_categories_links_clinic_id_fkey"
            columns: ["clinic_id"]
            isOneToOne: false
            referencedRelation: "clinics"
            referencedColumns: ["id"]
          },
        ]
      }
      clinics_conditions_links: {
        Row: {
          clinic_id: string
          condition_id: string
        }
        Insert: {
          clinic_id: string
          condition_id: string
        }
        Update: {
          clinic_id?: string
          condition_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "clinics_conditions_links_clinic_id_fkey"
            columns: ["clinic_id"]
            isOneToOne: false
            referencedRelation: "clinics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clinics_conditions_links_condition_id_fkey"
            columns: ["condition_id"]
            isOneToOne: false
            referencedRelation: "conditions"
            referencedColumns: ["id"]
          },
        ]
      }
      clinics_insurance_providers_links: {
        Row: {
          clinic_id: string
          insurance_provider_id: string
        }
        Insert: {
          clinic_id: string
          insurance_provider_id: string
        }
        Update: {
          clinic_id?: string
          insurance_provider_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "clinics_insurance_providers_links_clinic_id_fkey"
            columns: ["clinic_id"]
            isOneToOne: false
            referencedRelation: "clinics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clinics_insurance_providers_links_insurance_provider_id_fkey"
            columns: ["insurance_provider_id"]
            isOneToOne: false
            referencedRelation: "insurance_providers"
            referencedColumns: ["id"]
          },
        ]
      }
      clinics_practitioners_links: {
        Row: {
          clinic_id: string
          practitioner_id: string
        }
        Insert: {
          clinic_id: string
          practitioner_id: string
        }
        Update: {
          clinic_id?: string
          practitioner_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "clinics_practitioners_links_clinic_id_fkey"
            columns: ["clinic_id"]
            isOneToOne: false
            referencedRelation: "clinics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clinics_practitioners_links_practitioner_id_fkey"
            columns: ["practitioner_id"]
            isOneToOne: false
            referencedRelation: "practitioners"
            referencedColumns: ["id"]
          },
        ]
      }
      clinics_services_links: {
        Row: {
          clinic_id: string
          service_id: string
        }
        Insert: {
          clinic_id: string
          service_id: string
        }
        Update: {
          clinic_id?: string
          service_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "clinics_services_links_clinic_id_fkey"
            columns: ["clinic_id"]
            isOneToOne: false
            referencedRelation: "clinics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clinics_services_links_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      clinics_specialities_links: {
        Row: {
          clinic_id: string
          speciality_id: string
        }
        Insert: {
          clinic_id: string
          speciality_id: string
        }
        Update: {
          clinic_id?: string
          speciality_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "clinics_specialities_links_clinic_id_fkey"
            columns: ["clinic_id"]
            isOneToOne: false
            referencedRelation: "clinics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clinics_specialities_links_speciality_id_fkey"
            columns: ["speciality_id"]
            isOneToOne: false
            referencedRelation: "specialities"
            referencedColumns: ["id"]
          },
        ]
      }
      conditions: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          seo: Json | null
          slug: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          seo?: Json | null
          slug: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          seo?: Json | null
          slug?: string
          updated_at?: string
        }
        Relationships: []
      }
      insurance_providers: {
        Row: {
          created_at: string
          id: string
          name: string
          slug: string
          updated_at: string
          website: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          slug: string
          updated_at?: string
          website?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          slug?: string
          updated_at?: string
          website?: string | null
        }
        Relationships: []
      }
      practitioners: {
        Row: {
          bio: string | null
          contact_info: Json | null
          created_at: string
          id: string
          is_active: boolean
          is_featured: boolean
          name: string
          profile_picture: string | null
          qualifications: string | null
          seo: Json | null
          slug: string
          updated_at: string
        }
        Insert: {
          bio?: string | null
          contact_info?: Json | null
          created_at?: string
          id?: string
          is_active?: boolean
          is_featured?: boolean
          name: string
          profile_picture?: string | null
          qualifications?: string | null
          seo?: Json | null
          slug: string
          updated_at?: string
        }
        Update: {
          bio?: string | null
          contact_info?: Json | null
          created_at?: string
          id?: string
          is_active?: boolean
          is_featured?: boolean
          name?: string
          profile_picture?: string | null
          qualifications?: string | null
          seo?: Json | null
          slug?: string
          updated_at?: string
        }
        Relationships: []
      }
      practitioners_conditions_links: {
        Row: {
          condition_id: string
          practitioner_id: string
        }
        Insert: {
          condition_id: string
          practitioner_id: string
        }
        Update: {
          condition_id?: string
          practitioner_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "practitioners_conditions_links_condition_id_fkey"
            columns: ["condition_id"]
            isOneToOne: false
            referencedRelation: "conditions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "practitioners_conditions_links_practitioner_id_fkey"
            columns: ["practitioner_id"]
            isOneToOne: false
            referencedRelation: "practitioners"
            referencedColumns: ["id"]
          },
        ]
      }
      practitioners_services_links: {
        Row: {
          practitioner_id: string
          service_id: string
        }
        Insert: {
          practitioner_id: string
          service_id: string
        }
        Update: {
          practitioner_id?: string
          service_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "practitioners_services_links_practitioner_id_fkey"
            columns: ["practitioner_id"]
            isOneToOne: false
            referencedRelation: "practitioners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "practitioners_services_links_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
        ]
      }
      practitioners_specialities_links: {
        Row: {
          practitioner_id: string
          speciality_id: string
        }
        Insert: {
          practitioner_id: string
          speciality_id: string
        }
        Update: {
          practitioner_id?: string
          speciality_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "practitioners_specialities_links_practitioner_id_fkey"
            columns: ["practitioner_id"]
            isOneToOne: false
            referencedRelation: "practitioners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "practitioners_specialities_links_speciality_id_fkey"
            columns: ["speciality_id"]
            isOneToOne: false
            referencedRelation: "specialities"
            referencedColumns: ["id"]
          },
        ]
      }
      services: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          seo: Json | null
          slug: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          seo?: Json | null
          slug: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          seo?: Json | null
          slug?: string
          updated_at?: string
        }
        Relationships: []
      }
      specialities: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          seo: Json | null
          slug: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          seo?: Json | null
          slug: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          seo?: Json | null
          slug?: string
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
