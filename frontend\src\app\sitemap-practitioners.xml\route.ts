import { getStrapiContent } from '@/lib/strapi';

// Define the site URL from environment variable
// We need an absolute URL for sitemaps to work properly
// For sitemaps, we should use the frontend URL, not the Strapi API URL
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.naturalhealingnow.com';

// Log a warning if SITE_URL is not set
if (!process.env.NEXT_PUBLIC_SITE_URL && !process.env.NEXT_PUBLIC_API_URL) {
  console.warn('WARNING: Neither NEXT_PUBLIC_SITE_URL nor NEXT_PUBLIC_API_URL environment variables are set. Using default vercel.app URL as a placeholder.');
}

// Log the URL being used for debugging
// console.log(`Using site URL for practitioners sitemap: ${SITE_URL}`);

// Ensure SITE_URL doesn't have a trailing slash
const normalizedSiteUrl = SITE_URL.endsWith('/') ? SITE_URL.slice(0, -1) : SITE_URL;

// Define the page size for pagination - increased to ensure all practitioners are included
const PAGE_SIZE = 1000; // Increased to handle all practitioners in fewer API calls

// Define the type for sitemap entries
interface SitemapEntry {
  url: string;
  lastModified: Date;
  changeFrequency: string;
  priority: number;
}

// This is a Route Handler for Next.js App Router
// See: https://nextjs.org/docs/app/building-your-application/routing/route-handlers
export async function GET(_request: Request): Promise<Response> {
  // Initialize the sitemap array
  let sitemapEntries: SitemapEntry[] = [];

  try {
    console.log('Generating practitioners sitemap...');

    // First, get the total count of practitioners
    const countResponse = await getStrapiContent.practitioners.getAll({
      pagination: { pageSize: 1, page: 1 },
      fields: ['id'], // Only fetch IDs for counting
      sort: ['id:asc'], // Consistent sorting
      filters: { isActive: { $eq: true } }, // Filter by isActive: true
      // publicationState: 'live', // Assuming isActive is the primary determinant
    });

    const totalPractitioners = countResponse?.meta?.pagination?.total || 0;
    const totalPages = Math.ceil(totalPractitioners / PAGE_SIZE);

    console.log(`Found ${totalPractitioners} total practitioners, will fetch in ${totalPages} pages with page size ${PAGE_SIZE}`);
    console.log(`API URL being used: ${process.env.NEXT_PUBLIC_API_URL || 'Not set'}`);
    console.log(`Site URL being used: ${SITE_URL}`);

    // Log pagination details from the count response
    // if (countResponse?.meta?.pagination) {
    //   console.log('Pagination details:', JSON.stringify(countResponse.meta.pagination));
    // }

    // Fetch practitioners page by page
    for (let page = 1; page <= totalPages; page++) {
      console.log(`Fetching practitioners page ${page} of ${totalPages}...`);
      const practitionersResponse = await getStrapiContent.practitioners.getAll({
        pagination: { pageSize: PAGE_SIZE, page },
        fields: ['slug', 'updatedAt', 'createdAt', 'isActive'], // Fetch isActive to confirm
        // Explicitly set sort to ensure consistent ordering
        sort: ['id:asc'], // Sort by ID to ensure we get all practitioners in a consistent order
        filters: { isActive: { $eq: true } }, // Filter by isActive: true
        // publicationState: 'live', // Assuming isActive is the primary determinant
      });

      const practitioners = practitionersResponse?.data || [];

      // Log pagination details from this response - REDUCED
      // if (practitionersResponse?.meta?.pagination) {
      //   console.log(`Pagination details for page ${page}:`, JSON.stringify(practitionersResponse.meta.pagination));
      // }

      // Check if we got fewer practitioners than expected (except for the last page)
      if (practitioners.length < PAGE_SIZE && page < totalPages) {
        console.warn(`Warning: Got only ${practitioners.length} practitioners on page ${page}, expected ${PAGE_SIZE}. This may indicate a pagination issue.`);
      }

      // Log the raw response for debugging - REDUCED
      // console.log(`Raw practitioners response for page ${page}:`,
      //   JSON.stringify({
      //     meta: practitionersResponse?.meta,
      //     dataLength: practitionersResponse?.data?.length,
      //     firstItem: practitionersResponse?.data?.[0] ?
      //       {
      //         id: practitionersResponse.data[0].id,
      //         documentId: practitionersResponse.data[0].documentId,
      //         slug: practitionersResponse.data[0].slug ||
      //               (practitionersResponse.data[0].attributes ? practitionersResponse.data[0].attributes.slug : undefined),
      //         hasAttributes: !!practitionersResponse.data[0].attributes
      //       } :
      //       'none'
      //   })
      // );

      const practitionerRoutes = practitioners.map((practitioner: any) => {
        // Extract the slug - handle both Strapi v4 and v5 response formats
        let slug = null;

        // Direct access (Strapi v5 format)
        if (practitioner.slug) {
          slug = practitioner.slug;
        }
        // Attributes wrapper (Strapi v4 format)
        else if (practitioner.attributes && practitioner.attributes.slug) {
          slug = practitioner.attributes.slug;
        }
        // Try to find slug in any property if not found in standard locations - AVOID HEAVY DEBUGGING IN PROD SITEMAP
        // else {
          // Log the practitioner object to help debug
          // console.log(`Practitioner without standard slug location:`, JSON.stringify(practitioner));

          // Try to find a property that might be the slug
          // for (const key in practitioner) {
          //   if (typeof practitioner[key] === 'string' &&
          //       (key.toLowerCase().includes('slug') ||
          //        (practitioner[key].length > 0 &&
          //         practitioner[key].length < 100 &&
          //         /^[a-z0-9-]+$/.test(practitioner[key])))) {
          //     slug = practitioner[key];
          //     console.log(`Found potential slug in property ${key}: ${slug}`);
          //     break;
          //   }
          // }
        // }

        if (!slug) {
          // console.warn(`Found practitioner without slug: ${JSON.stringify({ // REDUCED LOGGING
          //   id: practitioner.id || 'unknown',
          //   documentId: practitioner.documentId || 'unknown',
          //   keys: Object.keys(practitioner)
          // })}`);
          // console.warn(`Found practitioner without slug. ID: ${practitioner.id || 'unknown'}, DocID: ${practitioner.documentId || 'unknown'}`);
          // Log the full practitioner object if slug is not found to help debug its structure
          // Also log isActive status if available
          console.warn(`Practitioner object for which slug was not found (ID: ${practitioner.id || 'unknown'}, isActive: ${practitioner.isActive ?? 'N/A'}):`, JSON.stringify(practitioner, null, 2));
          return null; // Skip if no slug
        }

        // Handle both direct properties and attributes structure for dates
        const attributes = practitioner.attributes || practitioner;
        const updatedAt = attributes.updatedAt || attributes.createdAt
          ? new Date(attributes.updatedAt || attributes.createdAt)
          : new Date();

        return {
          url: `${normalizedSiteUrl}/practitioners/${slug}`,
          lastModified: updatedAt,
          changeFrequency: 'weekly',
          priority: 0.8,
        };
      }).filter(Boolean); // Filter out null entries

      // Add this batch of practitioners to the sitemap
      sitemapEntries = [...sitemapEntries, ...practitionerRoutes];
      // console.log(`Total entries after page ${page}: ${sitemapEntries.length}`); // REDUCED
    }

    console.log(`Total practitioner entries in sitemap: ${sitemapEntries.length}`);

    // Add the practitioners index page to the sitemap
    sitemapEntries.unshift({
      url: `${normalizedSiteUrl}/practitioners`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    });

    console.log(`Final sitemap entries count (including index page): ${sitemapEntries.length}`);

    // Log a summary of the sitemap generation process
    console.log(`Sitemap generation summary:
    - Total practitioners found: ${totalPractitioners}
    - Total pages fetched: ${totalPages}
    - Page size used: ${PAGE_SIZE}
    - Final sitemap entries: ${sitemapEntries.length}
    - Sitemap URL: ${normalizedSiteUrl}/sitemap-practitioners.xml
    `);

    // Convert the sitemap to XML with consistent formatting
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries.map((entry) => `  <url>
    <loc>${entry.url}</loc>
    <lastmod>${entry.lastModified.toISOString()}</lastmod>
    <changefreq>${entry.changeFrequency}</changefreq>
    <priority>${entry.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

    // Validate the XML structure before returning
    try {
      // Simple validation - check for basic XML structure issues
      if (!xml.startsWith('<?xml') || !xml.includes('<urlset') || !xml.includes('</urlset>')) {
        console.error('Generated XML appears to be malformed');
        throw new Error('Malformed XML');
      }

      // Return the XML with the correct content type
      return new Response(xml, {
        headers: {
          'Content-Type': 'application/xml; charset=utf-8',
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
          'X-Content-Type-Options': 'nosniff',
        },
      });
    } catch (xmlError) {
      console.error('Error validating XML:', xmlError);
      // Fall back to a simpler XML structure
      const fallbackXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${normalizedSiteUrl}/practitioners</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;

      return new Response(fallbackXml, {
        headers: {
          'Content-Type': 'application/xml; charset=utf-8',
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
          'X-Content-Type-Options': 'nosniff',
        },
      });
    }
  } catch (error) {
    console.error('Error generating practitioners sitemap:', error);

    // If we have some entries, use them even if there was an error
    if (sitemapEntries.length > 0) {
      console.log(`Returning partial sitemap with ${sitemapEntries.length} entries despite error`);

      // Add the practitioners index page if it's not already there
      if (!sitemapEntries.some(entry => entry.url === `${normalizedSiteUrl}/practitioners`)) {
        sitemapEntries.unshift({
          url: `${normalizedSiteUrl}/practitioners`,
          lastModified: new Date(),
          changeFrequency: 'daily',
          priority: 0.9,
        });
      }

      const partialXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries.map((entry) => `  <url>
    <loc>${entry.url}</loc>
    <lastmod>${entry.lastModified.toISOString()}</lastmod>
    <changefreq>${entry.changeFrequency}</changefreq>
    <priority>${entry.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

      return new Response(partialXml, {
        headers: {
          'Content-Type': 'application/xml; charset=utf-8',
          'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
          'X-Content-Type-Options': 'nosniff',
        },
      });
    }

    // Return a basic XML in case of error with no entries
    console.log('Returning fallback sitemap with only the practitioners index page');
    const errorXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${normalizedSiteUrl}/practitioners</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;

    return new Response(errorXml, {
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'no-cache', // Don't cache error responses
        'X-Content-Type-Options': 'nosniff',
      },
    });
  }
}
