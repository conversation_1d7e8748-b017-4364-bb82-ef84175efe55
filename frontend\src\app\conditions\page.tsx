import Link from 'next/link';
import { Metadata } from 'next';
import { getStrapiContent } from '@/lib/strapi';
import { getConditions } from '@/lib/serverCache';
import SearchInput from '@/components/shared/SearchInput';
import { FiSearch } from 'react-icons/fi';

// Force static rendering for this route segment
export const dynamic = 'force-static';

// Enable ISR with both time-based and on-demand revalidation
// Use a 1-week revalidation period since conditions rarely change
export const revalidate = 604800; // 1 week in seconds

// Define metadata for the conditions page
export const metadata: Metadata = {
  title: 'Health Conditions - Natural Healing Now',
  description: 'Browse all health conditions and find practitioners and clinics that specialize in treating these conditions.',
};

// Define types for condition data
interface StrapiCondition {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  icon?: any;
  featuredImage?: any;
}

interface TransformedCondition {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  icon?: any;
  featuredImage?: any;
}

// Transform Strapi condition data
function transformConditionData(strapiCondition: StrapiCondition): TransformedCondition | null {
  if (!strapiCondition || !strapiCondition.name || !strapiCondition.slug) {
    console.warn(`Skipping invalid condition: ID ${strapiCondition?.id}`);
    return null;
  }
  
  // Handle both Strapi v4 and v5 response formats
  const attributes = 'attributes' in strapiCondition ? strapiCondition.attributes : strapiCondition;
  
  return {
    id: strapiCondition.id,
    name: attributes.name || strapiCondition.name || 'Unnamed Condition',
    slug: attributes.slug || strapiCondition.slug || `condition-${strapiCondition.id}`,
    description: attributes.description || strapiCondition.description || null,
    icon: attributes.icon || strapiCondition.icon || null,
    featuredImage: attributes.featuredImage || strapiCondition.featuredImage || null
  };
}

// Define the interface for the searchParams object
interface ConditionsPageSearchParams {
  query?: string;
  page?: string;
}

// Main page component
export default async function ConditionsPage({ 
  searchParams 
}: { 
  searchParams?: ConditionsPageSearchParams 
}) {
  // Await searchParams before accessing its properties
  const awaitedSearchParams = await searchParams;
  const query = awaitedSearchParams?.query || '';
  const currentPage = Number(awaitedSearchParams?.page) || 1;
  const pageSize = 12;
  
  // Fetch conditions data from Strapi with proper caching
  let conditions: TransformedCondition[] = [];
  let totalPages = 1;
  
  try {
    // Determine caching strategy based on search parameters
    const hasFilters = query;
    const fetchOptions = hasFilters
      ? { cache: 'no-store' } // Don't cache filtered results
      : {
          next: {
            tags: ['strapi-conditions', 'strapi-conditions-all'],
            revalidate: 604800 // 1 week
          },
          cache: 'force-cache'
        };
    
    // Fetch conditions with pagination and filtering
    const conditionsResponse = await getStrapiContent.conditions.getAll({
      query,
      page: currentPage,
      pageSize,
      ...fetchOptions
    });
    
    if (conditionsResponse?.data && Array.isArray(conditionsResponse.data)) {
      // Transform the data
      conditions = conditionsResponse.data
        .map((condition: any) => transformConditionData(condition as StrapiCondition))
        .filter((condition): condition is TransformedCondition => condition !== null);
      
      // Get total pages from pagination metadata
      totalPages = conditionsResponse.meta?.pagination?.pageCount || 1;
    }
  } catch (error) {
    console.error('Error fetching conditions:', error);
    conditions = [];
    totalPages = 1;
  }
  
  return (
    <>
      {/* Page Header */}
      <div className="bg-emerald-600 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Health Conditions</h1>
          <p className="text-lg max-w-3xl">
            Explore various health conditions and find practitioners and clinics that specialize in treating them naturally.
          </p>
        </div>
      </div>
      
      {/* Search Section */}
      <div className="bg-white py-8 border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <SearchInput
              placeholder="Search conditions..."
              defaultValue={query}
              icon={<FiSearch className="text-gray-400" />}
            />
          </div>
        </div>
      </div>
      
      {/* Conditions Grid */}
      <div className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            All Health Conditions
          </h2>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {conditions.length > 0 ? (
              conditions.map((condition) => (
                <Link
                  key={condition.id}
                  href={`/conditions/${condition.slug}`}
                  className="bg-white hover:bg-emerald-50 border border-gray-200 rounded-lg p-6 shadow-sm transition-colors"
                >
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">{condition.name}</h3>
                  {condition.description && (
                    <p className="text-gray-600 text-sm line-clamp-3">
                      {typeof condition.description === 'string' 
                        ? condition.description 
                        : 'Learn more about this condition'}
                    </p>
                  )}
                </Link>
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">No conditions found. Please check back later or try a different search.</p>
              </div>
            )}
          </div>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-12 flex justify-center">
              <nav className="inline-flex">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Link
                    key={page}
                    href={{
                      pathname: '/conditions',
                      query: {
                        ...(query ? { query } : {}),
                        page,
                      },
                    }}
                    className={`px-4 py-2 border ${
                      currentPage === page
                        ? 'bg-emerald-600 text-white border-emerald-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </Link>
                ))}
              </nav>
            </div>
          )}
        </div>
      </div>
      
      {/* Call to Action */}
      <div className="py-16 bg-emerald-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6 text-gray-800">Find Natural Solutions for Your Health</h2>
          <p className="text-lg mb-8 max-w-3xl mx-auto text-gray-600">
            Connect with practitioners and clinics that specialize in treating your specific health conditions naturally.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/clinics"
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold"
            >
              Find a Clinic
            </Link>
            <Link
              href="/practitioners"
              className="bg-white border border-emerald-600 text-emerald-600 hover:bg-emerald-50 px-6 py-3 rounded-lg font-semibold"
            >
              Find a Practitioner
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
