import type { Schema, Struct } from '@strapi/strapi';

export interface AddressAddress extends Struct.ComponentSchema {
  collectionName: 'components_address_addresses';
  info: {
    description: '';
    displayName: 'Address';
  };
  attributes: {
    city: Schema.Attribute.String;
    country: Schema.Attribute.Enumeration<['Canada', 'USA']> &
      Schema.Attribute.DefaultTo<'USA'>;
    postalCode: Schema.Attribute.String;
    stateProvince: Schema.Attribute.Enumeration<
      [
        'AL',
        'AK',
        'AZ',
        'AR',
        'AS',
        'CA',
        'CO',
        'CT',
        'DE',
        'DC',
        'FL',
        'GA',
        'GU',
        'HI',
        'ID',
        'IL',
        'IN',
        'IA',
        'KS',
        'KY',
        'LA',
        'ME',
        'MD',
        'MA',
        'MI',
        'MN',
        'MS',
        'MO',
        'MT',
        'NE',
        'NV',
        'NH',
        'NJ',
        'NM',
        'NY',
        'NC',
        'ND',
        'MP',
        'OH',
        'OK',
        'OR',
        'PA',
        'PR',
        'RI',
        'SC',
        'SD',
        'TN',
        'TX',
        'TT',
        'UT',
        'VT',
        'VA',
        'VI',
        'WA',
        'WV',
        'WI',
        'WY',
      ]
    >;
    streetAddress: Schema.Attribute.String;
    streetAddress1: Schema.Attribute.String;
  };
}

export interface ContactContactInformation extends Struct.ComponentSchema {
  collectionName: 'components_contact_contact_informations';
  info: {
    displayName: 'Contact Information';
  };
  attributes: {
    emailAddress: Schema.Attribute.Email;
    phoneNumber: Schema.Attribute.String;
    websiteUrl: Schema.Attribute.String;
  };
}

export interface LocationLocationCoordinates extends Struct.ComponentSchema {
  collectionName: 'components_location_location_coordinates';
  info: {
    displayName: 'Location Coordinates';
  };
  attributes: {
    googlePlaceId: Schema.Attribute.String;
    latitude: Schema.Attribute.Decimal;
    longitude: Schema.Attribute.Decimal;
  };
}

export interface LocationOpeningHours extends Struct.ComponentSchema {
  collectionName: 'components_location_opening_hours';
  info: {
    displayName: 'Opening Hours';
    icon: 'clock';
  };
  attributes: {
    closed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    closeTime: Schema.Attribute.Time;
    day: Schema.Attribute.Enumeration<
      [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
      ]
    >;
    openTime: Schema.Attribute.Time;
  };
}

export interface OtherScriptsInstallation extends Struct.ComponentSchema {
  collectionName: 'components_other_scripts_installations';
  info: {
    description: '';
    displayName: 'scriptsInstallation';
  };
  attributes: {
    name: Schema.Attribute.String & Schema.Attribute.Required;
    script: Schema.Attribute.Text & Schema.Attribute.Required;
    type: Schema.Attribute.Enumeration<['head', 'body', 'noscript']> &
      Schema.Attribute.Required;
  };
}

export interface SharedOpenGraph extends Struct.ComponentSchema {
  collectionName: 'components_shared_open_graphs';
  info: {
    displayName: 'openGraph';
    icon: 'project-diagram';
  };
  attributes: {
    ogDescription: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 200;
      }>;
    ogImage: Schema.Attribute.Media<'images'>;
    ogTitle: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 70;
      }>;
    ogType: Schema.Attribute.String;
    ogUrl: Schema.Attribute.String;
  };
}

export interface SharedSeo extends Struct.ComponentSchema {
  collectionName: 'components_shared_seos';
  info: {
    description: '';
    displayName: 'seo';
    icon: 'search';
  };
  attributes: {
    canonicalURL: Schema.Attribute.String;
    keywords: Schema.Attribute.Text;
    metaDescription: Schema.Attribute.String;
    metaImage: Schema.Attribute.Media<'images'>;
    metaRobots: Schema.Attribute.String &
      Schema.Attribute.DefaultTo<'index, follow'>;
    metaTitle: Schema.Attribute.String;
    metaViewport: Schema.Attribute.String;
    openGraph: Schema.Attribute.Component<'shared.open-graph', false>;
    structuredData: Schema.Attribute.JSON;
  };
}

export interface SocialSocialLink extends Struct.ComponentSchema {
  collectionName: 'components_social_social_links';
  info: {
    description: 'Component for individual social media links (repeatable)';
    displayName: 'Social Link';
    icon: 'share-alt';
  };
  attributes: {
    icon: Schema.Attribute.Media<'images'>;
    platform: Schema.Attribute.Enumeration<
      [
        'Facebook',
        'Instagram',
        'Twitter',
        'LinkedIn',
        'YouTube',
        'Pinterest',
        'Other',
      ]
    > &
      Schema.Attribute.Required;
    url: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'address.address': AddressAddress;
      'contact.contact-information': ContactContactInformation;
      'location.location-coordinates': LocationLocationCoordinates;
      'location.opening-hours': LocationOpeningHours;
      'other.scripts-installation': OtherScriptsInstallation;
      'shared.open-graph': SharedOpenGraph;
      'shared.seo': SharedSeo;
      'social.social-link': SocialSocialLink;
    }
  }
}
