import { getStrapiContent } from '@/lib/strapi';
import MarkdownContent from '@/components/blog/MarkdownContent'; // Assuming content is Markdown
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

// Define the expected shape of the About Us data (fields directly under data)
interface AboutUsData {
  id: number;
  title?: string; // Title might be in seo.metaTitle, making this optional
  content: string;
  seo?: any; // Using 'any' for flexibility, refine if SEO structure is known
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  // Add other direct fields if necessary (e.g., documentId)
}

interface StrapiSingleResponse<T> {
  data: T | null;
  meta: object;
}

// Function to generate metadata
export async function generateMetadata(): Promise<Metadata> {
  try {
    const response: StrapiSingleResponse<AboutUsData> = await getStrapiContent.aboutUs.get();
    const aboutUsData = response?.data;

    if (!aboutUsData) {
      return {
        title: 'About Us', // Default title
        description: 'Learn more about our mission and team.', // Default description
      };
    }

    // Data fields are directly on aboutUsData, not attributes
    // The getMetadata helper expects an object with an 'attributes' property containing seo.
    // We need to adapt or pass the seo object directly if the helper supports it.
    // Let's try passing the seo object directly if it exists.
    const seoData = aboutUsData?.seo; // Access seo directly
    const title = seoData?.metaTitle || aboutUsData?.title || 'About Us'; // Prioritize SEO title
    const description = seoData?.metaDescription || 'Learn more about our mission and team.'; // Prioritize SEO description

    // Note: The current getMetadata helper might not work directly with this structure.
    // We are manually extracting title/description for now.
    // If more complex SEO logic is needed, the helper might need adjustment.

    return {
      title: title,
      description: description,
      // Add other metadata fields as needed (e.g., openGraph, twitter) - extracting manually if needed
      // Example: openGraph: { title: seoData?.ogTitle, description: seoData?.ogDescription, ... }
      // Corrected references from seoMetadata to seoData
      ...(seoData?.canonicalURL && { alternates: { canonical: seoData.canonicalURL } }),
      ...(seoData?.metaRobots && { robots: seoData.metaRobots }),
      // Assuming twitter/opengraph data might be nested within seoData based on Strapi SEO plugin structure
      // Adjust these lines if the structure is different
      // ...(seoData?.twitter && { twitter: seoData.twitter }), // Example if twitter data is directly on seoData
      // ...(seoData?.openGraph && { openGraph: seoData.openGraph }), // Example if openGraph data is directly on seoData
    };
  } catch (error) {
    console.error('Error fetching About Us metadata:', error);
    return {
      title: 'About Us',
      description: 'Error loading page information.',
    };
  }
}

// The page component
export default async function AboutUsPage() {
  let aboutUsData: AboutUsData | null = null;

  try {
    const rawResponse = await getStrapiContent.aboutUs.get();
    // Data is directly under rawResponse.data for single types usually
    aboutUsData = rawResponse?.data;
  } catch (error) {
    console.error('Failed to fetch About Us page data:', error);
    // Optionally, render an error state or redirect
  }

  // Initial check if data object itself is null/undefined
  if (!aboutUsData) {
     console.error('About Us data object is null or undefined after fetch.');
     notFound(); // Call notFound immediately if data is missing
  }

  // Check if content exists now that we know aboutUsData is not null
  if (!aboutUsData.content) {
     console.error('About Us data fetched, but content field is missing or empty.');
     // Optionally call notFound() here too if content is absolutely required
     // notFound();
  }
  // If we reach here, aboutUsData is guaranteed to be non-null by the first check.
  // TypeScript should now be satisfied.

  // Use data directly
  const title = aboutUsData.seo?.metaTitle || aboutUsData.title || 'About Us';
  const content = aboutUsData.content; // Safe to access

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Removed H1 title display from page body */}
      <div className="prose lg:prose-xl max-w-none mx-auto">
        {/* Render the markdown content */}
        {content ? (
          <MarkdownContent content={content} />
        ) : (
          <p>About us content is not available at the moment.</p>
        )}
      </div>
    </div>
  );
}
