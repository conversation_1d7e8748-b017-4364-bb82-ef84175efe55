"use client";

import LazyImage from '@/components/shared/LazyImage'; // Changed import
import Link from 'next/link';
import { sanitizeUrl } from '@/lib/mediaUtils';

interface CategoryCardProps {
  category: {
    id: string;
    name: string;
    slug: string;
    description?: string | null;
    icon?: string | null;
    featured_image?: string | null;
  };
}

const CategoryCard = ({ category }: CategoryCardProps) => {
  // Extract image URLs safely, handling both string and object formats
  const extractImageUrl = (imageData: any): string => {
    if (!imageData) return '';

    // If it's an object, try to extract the URL
    if (typeof imageData === 'object') {
      // Try common patterns for URL in Strapi media objects
      if (imageData.url) return imageData.url;
      if (imageData.data?.attributes?.url) return imageData.data.attributes.url;

      // Log if we can't extract a URL
      console.log(`Could not extract URL from image object for ${category.name}:`, imageData);
      return '';
    }

    // If it's already a string, return it
    return imageData;
  };

  // Process the image URLs
  const rawFeaturedImage = extractImageUrl(category.featured_image);
  const rawIconImage = extractImageUrl(category.icon);

  // Sanitize the URLs
  const imageSrc = rawFeaturedImage ? sanitizeUrl(rawFeaturedImage) : '';
  const iconSrc = rawIconImage ? sanitizeUrl(rawIconImage) : '';
  const hasImage = !!imageSrc;

  // Debug logging for image URLs
  console.log(`CategoryCard for ${category.name}:`, {
    originalFeaturedImage: category.featured_image,
    extractedFeaturedImage: rawFeaturedImage,
    sanitizedFeaturedImage: imageSrc,
    originalIcon: category.icon,
    extractedIcon: rawIconImage,
    sanitizedIcon: iconSrc,
    hasImage
  });

  return (
    <Link
      href={`/categories/${category.slug}`}
      className="block group"
    >
      <div className="bg-white rounded-lg shadow-md overflow-hidden transition-transform group-hover:shadow-lg group-hover:-translate-y-1">
        <div className="relative h-40 w-full">
          {hasImage && imageSrc ? (
            <LazyImage
              src={imageSrc}
              alt={category.name}
              width={400} // Aspect ratio hint
              height={300} // Aspect ratio hint
              fillContainer={true}
              className="object-cover" // w-full h-full handled by fillContainer
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority={false}
              showPlaceholder={true} // Added for consistency
              // unoptimized prop is not on LazyImage, loader handles this logic
            />
          ) : (
            // Fallback for when no image is available
            <div className="absolute inset-0 bg-purple-200 flex items-center justify-center">
              <span className="text-purple-700 font-semibold text-xl">
                {category.name.charAt(0)}
              </span>
            </div>
          )}

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />

          {/* Category icon */}
          {category.icon && iconSrc && (
            <div className="absolute top-4 left-4 bg-white p-2 rounded-full shadow-md">
              <div className="relative h-8 w-8"> {/* This div defines the size for the icon */}
                <LazyImage
                  src={iconSrc}
                  alt={`${category.name} icon`}
                  width={32} // Aspect ratio hint
                  height={32} // Aspect ratio hint
                  fillContainer={true} // Make LazyImage fill the h-8 w-8 div
                  className="object-cover rounded-full"
                  sizes="32px"
                  showPlaceholder={false} // Probably not needed for small icons
                  // unoptimized prop is not on LazyImage
                />
              </div>
            </div>
          )}

          {/* Category name */}
          <div className="absolute bottom-0 left-0 right-0 p-4">
            <h3 className="text-xl font-semibold text-white">{category.name}</h3>
          </div>
        </div>

        {category.description && (
          <div className="p-4">
            <p className="text-gray-600 text-sm line-clamp-2">{category.description}</p>
          </div>
        )}

        <div className="px-4 py-3 bg-gray-50 border-t border-gray-100">
          <span className="text-emerald-600 group-hover:text-emerald-700 font-medium text-sm flex items-center">
            Browse Clinics
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </span>
        </div>
      </div>
    </Link>
  );
};

export default CategoryCard;
