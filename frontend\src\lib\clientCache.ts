/**
 * Client-side caching utilities
 * This file provides functions to store and retrieve data on the client side
 */

// Define a type for the cache entry
type CacheEntry<T> = {
  data: T;
  timestamp: number;
  expiry: number;
};

// Define a type for the cache
type Cache = {
  [key: string]: CacheEntry<any>;
};

// Initialize the cache
let cache: Cache = {};

/**
 * Set a value in the cache
 * @param key The key to store the value under
 * @param value The value to store
 * @param ttl Time to live in milliseconds (default: 5 minutes)
 */
export function setCacheValue<T>(key: string, value: T, ttl: number = 5 * 60 * 1000): void {
  if (typeof window === 'undefined') {
    return; // Don't run on the server
  }
  
  const now = Date.now();
  cache[key] = {
    data: value,
    timestamp: now,
    expiry: now + ttl,
  };
  
  // Also store in sessionStorage for persistence across page navigations
  try {
    sessionStorage.setItem(
      `cache_${key}`,
      JSON.stringify({
        data: value,
        timestamp: now,
        expiry: now + ttl,
      })
    );
  } catch (error) {
    console.error('Error storing data in sessionStorage:', error);
  }
}

/**
 * Get a value from the cache
 * @param key The key to retrieve the value for
 * @returns The value, or null if not found or expired
 */
export function getCacheValue<T>(key: string): T | null {
  if (typeof window === 'undefined') {
    return null; // Don't run on the server
  }
  
  // First check the in-memory cache
  const entry = cache[key];
  const now = Date.now();
  
  if (entry && entry.expiry > now) {
    return entry.data as T;
  }
  
  // If not in memory or expired, check sessionStorage
  try {
    const storedEntry = sessionStorage.getItem(`cache_${key}`);
    if (storedEntry) {
      const parsed = JSON.parse(storedEntry) as CacheEntry<T>;
      if (parsed.expiry > now) {
        // Restore to in-memory cache and return
        cache[key] = parsed;
        return parsed.data;
      } else {
        // Remove expired entry
        sessionStorage.removeItem(`cache_${key}`);
      }
    }
  } catch (error) {
    console.error('Error retrieving data from sessionStorage:', error);
  }
  
  return null;
}

/**
 * Clear a specific value from the cache
 * @param key The key to clear
 */
export function clearCacheValue(key: string): void {
  if (typeof window === 'undefined') {
    return; // Don't run on the server
  }
  
  delete cache[key];
  try {
    sessionStorage.removeItem(`cache_${key}`);
  } catch (error) {
    console.error('Error removing data from sessionStorage:', error);
  }
}

/**
 * Clear all values from the cache
 */
export function clearCache(): void {
  if (typeof window === 'undefined') {
    return; // Don't run on the server
  }
  
  cache = {};
  try {
    // Only clear our cache entries, not all sessionStorage
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('cache_')) {
        sessionStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.error('Error clearing data from sessionStorage:', error);
  }
}

/**
 * Store practitioner data in the cache
 * @param practitioner The practitioner data to store
 */
export function cachePractitioner(practitioner: any): void {
  if (!practitioner || !practitioner.id || !practitioner.slug) {
    return; // Don't cache invalid data
  }
  
  // Check if we already have this practitioner in the cache
  const existingData = getCacheValue(`practitioner_${practitioner.slug}`);
  
  // Only update the cache if:
  // 1. We don't have this practitioner cached yet, or
  // 2. The new data has the _hasDetailedData flag and the existing data doesn't
  if (!existingData || 
      (practitioner._hasDetailedData && (!existingData._hasDetailedData))) {
    setCacheValue(`practitioner_${practitioner.slug}`, practitioner, 30 * 60 * 1000); // 30 minutes
  }
}

/**
 * Get practitioner data from the cache
 * @param slug The practitioner slug
 * @returns The practitioner data, or null if not found or expired
 */
export function getCachedPractitioner(slug: string): any | null {
  return getCacheValue(`practitioner_${slug}`);
}