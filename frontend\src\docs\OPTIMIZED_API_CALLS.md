# Optimized API Calls to Strapi

This document explains how to use the optimized data fetching utilities to reduce the number of API calls to Strapi in your Next.js 15 application.

## Table of Contents

1. [Overview](#overview)
2. [Client-Side Data Fetching](#client-side-data-fetching)
3. [Server-Side Data Fetching](#server-side-data-fetching)
4. [Caching Strategies](#caching-strategies)
5. [Best Practices](#best-practices)

## Overview

The optimized data fetching utilities provide several mechanisms to reduce API calls to Strapi:

1. **In-memory caching**: Responses are cached in memory with content-type specific TTLs
2. **Request deduplication**: Prevents duplicate in-flight requests for the same data
3. **Content-type specific caching**: Different cache durations based on how frequently content changes
4. **Prefetching**: Preloads common data that will be needed across multiple pages
5. **Optimized hooks**: Custom hooks for specific content types with appropriate caching

## Client-Side Data Fetching

For client components, use the hooks in `useOptimizedFetch.ts`:

```tsx
'use client';

import { 
  useGlobalSettings, 
  useCategories, 
  useFeaturedClinics 
} from '@/hooks/useOptimizedFetch';

export default function MyComponent() {
  // Use optimized hooks for data fetching
  const { data: globalSettings, isLoading: isLoadingGlobal } = useGlobalSettings();
  const { data: categories, isLoading: isLoadingCategories } = useCategories(5);
  const { data: clinics, isLoading: isLoadingClinics } = useFeaturedClinics(3);
  
  // For custom endpoints, use the generic hook
  const { data, isLoading, error, refetch } = useOptimizedFetch(
    '/custom-endpoint',
    { params: { /* your params */ } },
    5 * 60 * 1000 // 5 minutes TTL
  );
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      {/* Render your data */}
    </div>
  );
}
```

## Server-Side Data Fetching

For server components, use the utilities in `serverOptimizedFetch.ts`:

```tsx
import { 
  getGlobalSettings, 
  getCategories, 
  getClinics 
} from '@/lib/serverOptimizedFetch';

export default async function MyServerComponent() {
  // These calls are automatically cached and deduplicated
  const globalSettings = await getGlobalSettings();
  const categories = await getCategories(5);
  const clinics = await getClinics({ featured: true, pageSize: 3 });
  
  // For custom endpoints, use the generic function
  const customData = await fetchFromServerCached(
    '/custom-endpoint',
    { params: { /* your params */ } },
    5 * 60 * 1000 // 5 minutes TTL
  );
  
  return (
    <div>
      {/* Render your data */}
    </div>
  );
}
```

## Caching Strategies

Different content types have different cache durations based on how frequently they change:

| Content Type | Cache Duration | Notes |
|--------------|----------------|-------|
| Global Settings | 12 hours | Rarely changes |
| Categories | 6 hours | Semi-static |
| Specialties | 6 hours | Semi-static |
| Clinics | 3 hours | Changes occasionally |
| Practitioners | 3 hours | Changes occasionally |
| Blog Posts | 30 minutes | Changes frequently |
| Default | 5 minutes | For other content types |

You can customize these durations in the `optimizedFetch.ts` file.

## Best Practices

1. **Use Content-Type Specific Hooks/Functions**: They have optimized cache settings for each content type.

2. **Prefetch Common Data**: Call `prefetchCommonData()` in your layout or main component to preload data that's needed across multiple pages.

   ```tsx
   useEffect(() => {
     prefetchCommonData();
   }, []);
   ```

3. **Optimize Your Queries**: Only request the fields and relations you need.

   ```tsx
   // Bad: Fetches all fields and relations
   useContentType('blog-posts');
   
   // Good: Only fetches needed fields and relations
   useContentType('blog-posts', {
     params: {
       fields: ['title', 'slug', 'publishDate'],
       populate: {
         featuredImage: true,
         author_blogs: {
           fields: ['firstName', 'lastName']
         }
       }
     }
   });
   ```

4. **Use Server Components When Possible**: They provide better caching with React's `cache` function.

5. **Clear Cache When Needed**: Use `clearCacheEntry()` or `clearCache()` when data changes.

   ```tsx
   import { clearCacheEntry } from '@/lib/optimizedFetch';
   
   // After updating a blog post
   clearCacheEntry('/blog-posts', { filters: { slug: { $eq: 'my-post' } } });
   ```

6. **Batch Related Requests**: Use `batchRequests()` to combine multiple related requests.

   ```tsx
   import { batchRequests } from '@/lib/optimizedFetch';
   
   // Fetch multiple endpoints in parallel
   const [globalSettings, categories, specialties] = await batchRequests([
     { endpoint: '/global-setting', options: { params: { populate: '*' } } },
     { endpoint: '/categories', options: { params: { pagination: { pageSize: 10 } } } },
     { endpoint: '/specialties', options: { params: { pagination: { pageSize: 10 } } } }
   ]);
   ```

By following these practices, you can significantly reduce the number of API calls to Strapi while maintaining a responsive user experience.
