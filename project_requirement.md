Below is the updated project requirement document incorporating the new mapping and location search features. It now details the integration of Google Maps on clinic profiles, as well as interactive map functionality and location input on category pages.

---

# Project Requirement Document
**Project Name:** Holistic Health Directory with Blog, SEO, Map Integration, and Custom Schema
**Version:** 1.2
**Last Updated:** April 25, 2025

---

## 1. Project Overview

The Holistic Health Directory is a web-based platform that connects users with clinics and practitioners specializing in holistic health therapies. In addition to comprehensive directory features, the platform hosts an informative blog, customizable SEO configurations, and supports structured data (JSON-LD) for enhanced search engine visibility. This version includes integration with mapping services to display clinic locations via Google Business Profile maps and interactive maps on category pages, allowing users to search by city or zip code.

---

## 2. Objectives

- **Directory Functionality:**
  Deliver a robust directory enabling users to search, filter, and navigate between clinic and practitioner profiles. Display detailed information including contact details, services offered, accepted payments, insurance, conditions treated, and more.

- **Mapping & Location Search:**
  *(Deferred)* Integrate Google Maps on clinic profiles to showcase their Google Business Profile map. On category pages, provide an interactive map view with drop pins for clinic locations and allow users to search by location (city or zip code).

- **Blog Integration:**
  Host a blog that publishes articles related to holistic health and wellness to drive user engagement and improve SEO.

- **SEO & Structured Data:**
  Utilize the integrated Strapi SEO plugin to manage SEO metadata (title, description, OpenGraph) and structured data (JSON-LD) for all relevant content types.

- **Admin Management:**
  Use Strapi as the headless CMS for managing all directory content, blog posts, SEO configurations, and mapping data.

- **User Authentication:**
  Implement secure authentication and role-based access with Strapi's built-in Users & Permissions plugin.

- **Responsive and Scalable Design:**
  Ensure the platform is mobile-responsive, efficient, and scalable for an increasing number of users and data entries.

---

## 3. Functional Requirements

### 3.1 Directory Features

**Search & Filter:**
- Users must be able to search for clinics or practitioners by name, category, or location.
- *(Advanced Filtering Deferred)* Enable filters based on proximity, services offered, ratings, conditions treated, accepted payment methods, and insurance providers.

**Clinic Profiles:**
- **Basic Information:**
  Display clinic name, address, contact details (phone, email, website), and a list of offered services.
- **Extended Details:**
  Include fields for payments accepted (e.g., cash, credit card, digital wallets), insurances accepted, and conditions treated.
- **Associations:**
  Link associated practitioners and allow navigation to their detailed profiles.
- **Mapping Integration:**
  *(Deferred)* Display an embedded Google Map reflecting the clinic's location via the Google Business Profile.
  *(Deferred)* If available, use the Google Maps API to load the clinic’s map widget, possibly leveraging a unique business ID.
- **SEO:** Managed via the integrated Strapi SEO plugin.

**Practitioner Profiles:**
- **Basic Information:**
  Show practitioner name, qualifications, specialties, and contact information.
- **Associations:**
  Display linked clinic affiliations with clickable navigation to clinic profiles.
- **SEO:** Managed via the integrated Strapi SEO plugin.

**Category Pages:**
- **Listing & Filtering:**
  Dedicated pages for each category (e.g., Acupuncture, Naturopathy) that list clinics and associated practitioners.
- **Mapping & Location Search:**
  *(Deferred)* Integrate an interactive map that displays drop pins for each clinic location.
  *(Deferred)* Include a location search box that permits filtering by city or zip code. When users enter a location, update the map view to display only the corresponding clinic markers.
- **SEO:** Managed via the integrated Strapi SEO plugin.

### 3.2 Blog Features

**Blog Homepage:**
- Present a list of recent blog posts showing titles, featured images, and excerpts.
- Integrate filtering options by category or tag, along with a search function.

**Individual Blog Post Pages:**
- Display complete blog post content with multimedia support (images, videos, embedded content).
- Include related posts at the bottom of each page.
- **SEO:** Managed via the integrated Strapi SEO plugin.

**Categories, Tags & Author Profiles:**
- Enable categorization and tagging of blog posts.
- Display author profiles featuring a bio, image, and a list of posts by the author.

### 3.3 SEO & Structured Data (Managed by Plugin)

SEO metadata (including title, description, OpenGraph tags) and structured data (JSON-LD) for clinics, practitioners, categories, blog posts, and other relevant pages are managed using the integrated Strapi SEO plugin. The frontend will need to query and render this data appropriately.

### 3.4 User Authentication & Admin Management

- **User Roles:**
  - **Admin:** Full control over content management (clinics, practitioners, blog posts, SEO settings, and map-related data).
  - **Clinic/Practitioner Owner:** *(Advanced Permissions Deferred)* Permission to edit only their own profiles. Basic authentication will be implemented first.
- **CMS Integration Using Strapi:**
  Create custom content types to manage clinics, practitioners, blog posts, categories, tags, including multi-select inputs for payment options, insurances, and conditions treated. SEO is handled by the plugin.
- **Authentication:**
  Leverage Strapi's built-in Users & Permissions plugin for basic user login/registration. Advanced role-based access controls are deferred.

---

## 4. Non-Functional Requirements

- **Performance:**
  Optimize asset loading and ensure page load times remain under 2 seconds. Implement Server-Side Rendering (SSR) with Next.js.
- **Scalability:**
  Architect the solution to support growth in content volume and user traffic using Strapi Cloud and caching mechanisms.
- **Security:**
  Secure all endpoints and data through encryption, secure API access, and strict role-based access.
- **SEO Optimization:**
  Use SSR and structured data validation tools (e.g., Google Lighthouse) to guarantee SEO best practices.
- **Responsive Design:**
  Build a consistent, mobile-responsive user experience that works across desktops, tablets, and smartphones.
- **Maintainability:**
  Ensure code is well-documented and modular to facilitate future updates or feature additions.

---

## 5. Technology Stack

- **Frontend:** Next.js
  Optimized for SSR and high-performance user interactions.
- **Backend:** Strapi Cloud
  Provides both content management and authentication services.

- **Mapping API:** Google Maps API
  Integrate Google Maps for dynamic map displays on clinic profiles and interactive maps on category pages.
- **Hosting:**
  - Vercel for the Next.js frontend.
  - Strapi Cloud for backend, content management, authentication, and database.

---

## 6. Database Schema

### Components

First, let's define the reusable components. This promotes consistency and reduces redundancy.

**1. Component: Address**

- `streetAddress1` (Text)
- `streetAddress2` (Text) - Optional
- `city` (Text, Required) or (String)
- `stateProvince` (Text, Enumeration)
- `postalCode` (Text, Required)  or (String)
- `country` (Text, default: "USA")  or (String)

**3. Component: Contact Information**

- `phoneNumber` (Text)
- `emailAddress` (Email)
- `websiteUrl` (Text) - URL validation

**4. Component: Location Coordinates**

- `googlePlaceId` (Text) - Optional
- `latitude` (Decimal, Required for mapping)
- `longitude` (Decimal, Required for mapping)

**5. Component: Social Link** (Set as Repeatable)

- `platform` (Enumeration: Facebook, Instagram, Twitter, LinkedIn, YouTube, Pinterest, Other)
- `url` (Text, Required, URL validation)
- `icon` (Media)

### ### Collection Types

These represent the core data entities you'll have many of.

**1. Clinic**

- `name` (Text, Required)
- `slug` (UID, based on `name`, Required) - For clean URLs.
- `description` (Rich Text) - Overview, history, philosophy.
- `contactInfo` (Component: Contact Information)
- `address` (Component: Address, Required)
- `location` (Component: Location Coordinates, Required)
- `logo` (Media: Single Image)
- `featuredImage` (Media: Single Image)
- `gallery` (Media: Multiple Images)
- `videoEmbed` (oEmbed plugin)
- `isActive` (Boolean, default: true) - To easily publish/unpublish.
- `isFeatured` (Boolean, default: true)
- `appointmentOptions` (Multi-Select Plugin)
    - appointmentOptions
    In-person
    Telehealth (virtual)
    Phone
    Home visits
- `paymentMethod` (Multi-Select Plugin)
  - Cash, Visa, Mastercard, Amazon Pay, Google Pay, Amazon Pay, HSA/FSA Card

- **Relations:**
    - `categories` (Relation: Many-to-Many with Category)
    - `services` (Relation: Many-to-Many with Service)
    - `specialities` (Relation: Many-to-Many with Speciality)
    - `conditionsTreated` (Relation: Many-to-Many with Condition)
    - `insuranceProviders` (Relation: Many-to-Many with Insurance Provider)
- `practitioners` (Relation: Many-to-Many with Practitioner)
- `seo` (Component: SEO) - *Handled by SEO Plugin*

**2. Practitioner**

- `name` (Text, Required)
- `slug` (UID, based on `name`, Required)
- `bio` (Rich Text)
- `qualifications` (Textarea or Rich Text) - e.g., Degrees, Certifications.
- `specialties` (Relation: Many-to-Many with Condition)
- `profilePicture` (Media: Single Image)
- `videoEmbed` (oEmbed plugin)
- `contactInfo` (Component: Contact Information) - Optional, if they prefer contact via clinic.
- `isActive` (Boolean, default: true)
- `isFeatured` (Boolean, default: true)
- **Relations:**
    - `clinics` (Relation: Many-to-Many with Clinic) - Where they practice.
    - `services` (Relation: Many-to-Many with Service) - Services *they* personally offer.
    - `specialities` (Relation: Many-to-Many with Service)
    - `conditionsTreated` (Relation: Many-to-Many with Condition)
- `seo` (Component: SEO) - *Handled by SEO Plugin*

**3. Category (Directory Categories)**

- `name` (Text, Required) - e.g., Acupuncture, Naturopathy.
- `slug` (UID, based on `name`, Required)
- `description` (Rich Text) - Description for the category page.
- `icon` (Media: Single Image) - Optional, for display in lists/menus.
- `featuredImage` (Media: Single Image) - Optional, banner for the category page.
- **Relations:**
    - `clinics` (Relation: Many-to-Many with Clinic) - *Managed via the Clinic entry.*
    - `providers` (Relation: Many-to-Many with Clinic) - *Managed via the Clinic entry.*
- `seo` (Component: SEO) - *Handled by SEO Plugin*

**4. Service (Therapies/Treatments Offered)**

- `name` (Text, Required) - e.g., Massage Therapy, Herbal Medicine.
- `slug` (UID, based on `name`, Required)
- `description` (Rich Text) - Optional.
- **Relations:**
    - `clinics` (Relation: Many-to-Many with Clinic) - *Managed via the Clinic entry.*
    - `practitioners` (Relation: Many-to-Many with Practitioner) - *Managed via the Practitioner entry.*
        - You *can* further customize the Strapi Admin UI (potentially with code or plugins) to make the `practitioners` field on the `Clinic` edit view read-only if you want to strictly enforce this workflow, but the default setup allows editing from both ends while your documentation/guideline suggests using the Practitioner side.

**5. Condition (Conditions Treated)**

- `name` (Text, Required) - e.g., Chronic Pain, Anxiety, Digestive Issues.
- `slug` (UID, based on `name`, Required)
- `description` (Rich Text) - Optional.
- **Relations:**
    - `clinics` (Relation: Many-to-Many with Clinic) - *Managed via the Clinic entry.*
    - `practitioners` (Relation: Many-to-Many with Practitioner) - *Managed via the Practitioner entry.*
- `seo` (Component: SEO) - *Handled by SEO Plugin*

**6. Speciality (Specialities Treated)**

- `name` (Text, Required) - e.g., Chronic Pain, Anxiety, Digestive Issues.
- `slug` (UID, based on `name`, Required)
- `description` (Rich Text) - Optional.
- **Relations:**
    - `clinics` (Relation: Many-to-Many with Clinic) - *Managed via the Clinic entry.*
    - `practitioners` (Relation: Many-to-Many with Practitioner) - *Managed via the Practitioner entry.*
- `seo` (Component: SEO) - *Handled by SEO Plugin*


**7. Insurance Provider**

- `name` (Text, Required) - e.g., Blue Cross Blue Shield, Aetna.
- `slug` (UID, based on `name`, Required)
- `website` (Text) - Optional link to the provider's site.
- **Relations:**
    - `clinics` (Relation: Many-to-Many with Clinic) - *Managed via the Clinic entry.*

**8. Blog Post**

- `title` (Text, Required)
- `slug` (UID, based on `title`, Required)
- `publishDate` (DateTime, Required)
- `featuredImage` (Media: Single Image)
- `excerpt` (Textarea) - Short summary for listings.
- `content` (Rich Text, Required)
- `status` (Enumeration: Draft, Published, Archived; default: Draft)
- **Relations:**
    - `author` (Relation: Many-to-One with Author)
    - `blogCategories` (Relation: Many-to-Many with Blog Category)
    - `blogTags` (Relation: Many-to-Many with Blog Tag)
- `seo` (Component: SEO) - *Handled by SEO Plugin*

**9. Author (Blog Authors)**

- `name` (Text, Required)
- `slug` (UID, based on `name`, Required)
- `profilePicture` (Media: Single Image)
- `bio` (Rich Text)
- `email` (Email) - Optional, maybe kept private.
- `website` (Text) - Optional.
- **Relations:**
    - `blogPosts` (Relation: One-to-Many with Blog Post) - *Managed via the Blog Post entry.*
- `seo` (Component: SEO) - *Handled by SEO Plugin*

**10. Blog Category**

*   `name` (Text, Required)

*   `slug` (UID, based on `name`, Required)

*   `description` (Rich Text) - Optional.

*   **Relations:**

*   `blogPosts` (Relation: Many-to-Many with Blog Post) - *Managed via the Blog Post entry.*

**11. Blog Tag**

*   `name` (Text, Required)

*   `slug` (UID, based on `name`, Required)

*   **Relations:**

*   `blogPosts` (Relation: Many-to-Many with Blog Post) - *Managed via the Blog Post entry.*

### ### Single Types

These are for unique pages or global settings.

**1. Global Settings**

- `siteName` (Text, Required)
- `logoLight` (Media: Single Image)
- `logoDark` (Media: Single Image) - Optional, for theme variations.
- `favicon` (Media: Single Image)
- `defaultSeoTitleSuffix` (Text) - e.g., " | Holistic Health Directory"
- `defaultSeoDescription` (Textarea) - Fallback description.
- `globalContactEmail` (Email) - Main contact for the site.
- `socialMediaLinks` (Component: Social Link, Repeatable) - Global social links for footer/header.

**2. Homepage** (Optional - If you need specific editable content beyond dynamic listings)

- `heroTitle` (Text)
- `heroSubtitle` (Textarea)
- `heroBackgroundImage` (Media: Single Image)
- `callToActionText` (Text)
- `callToActionLink` (Text)
- `featuredContentSection` (Dynamic Zone) - Allows adding different pre-defined components like "Featured Categories", "Featured Clinics", "Intro Text Block", etc.
- `seo` (Component: SEO) - *Handled by SEO Plugin*

**3. Blog Homepage** (Optional - Similar to Homepage, if needed)

- `pageTitle` (Text, default: "Blog")
- `pageDescription` (Rich Text) - Introduction to the blog section.
- `featuredPosts` (Relation: Many-to-Many with Blog Post) - Manually select posts to feature.
- `seo` (Component: SEO) - *Handled by SEO Plugin*

---

## 7. API Endpoints

Design RESTful API endpoints (or GraphQL if preferred) for CRUD operations and filtering. For example:

**Clinics:**
- `GET /clinics` – Return all clinics.
- `GET /clinics/:id` – Return detailed data for a single clinic.
- `POST /clinics` – Create a new clinic (admin only).
- `PUT /clinics/:id` – Update an existing clinic (admin or owner).
- `DELETE /clinics/:id` – Delete a clinic (admin only).

**Additional Endpoints for Practitioners, Categories, and Blog Posts:**
Follow a similar CRUD pattern and include filtering parameters for location, services, and tags where applicable.

For location filtering, particularly on category pages, endpoints should support parameters (e.g., `city`, `zip`) and return clinic records with latitude/longitude coordinates for mapping.
