import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag, revalidatePath } from 'next/cache';

// Ensure the secret token is set in environment variables
const REVALIDATE_SECRET = process.env.STRAPI_REVALIDATE_SECRET || process.env.REVALIDATE_TOKEN || process.env.PREVIEW_SECRET;

export async function POST(request: NextRequest) {
  if (!REVALIDATE_SECRET) {
    console.error('CRITICAL: STRAPI_REVALIDATE_SECRET, REVALIDATE_TOKEN, or PREVIEW_SECRET is not set. Revalidation endpoint for practitioners is disabled.');
    return NextResponse.json({ message: 'Revalidation secret not configured.' }, { status: 500 });
  }

  // Check for the secret token in various possible headers
  const secretFromHeader = request.headers.get('X-Revalidate-Secret') ||
                          (request.headers.get('Authorization')?.replace('Bearer ', '')) ||
                          request.headers.get('x-webhook-secret');

  if (secretFromHeader !== REVALIDATE_SECRET) {
    console.warn('Invalid practitioner revalidation attempt: Incorrect or missing authentication header.');
    console.log('Received headers:', Object.fromEntries(request.headers.entries()));
    return NextResponse.json({ message: 'Invalid token' }, { status: 401 });
  }

  try {
    // Log the raw request for debugging
    console.log('Received webhook request for practitioners revalidation');

    let body;
    try {
      body = await request.json();
      console.log('Webhook payload:', JSON.stringify(body, null, 2));
    } catch (e) {
      console.error('Failed to parse webhook payload:', e);
      return NextResponse.json({ message: 'Invalid JSON payload' }, { status: 400 });
    }

    // Extract data from Strapi webhook payload
    const { event, model, entry } = body;

    // Log the extracted data
    console.log('Webhook event:', event);
    console.log('Webhook model:', model);
    console.log('Webhook entry:', entry ? `ID: ${entry.id}` : 'No entry data');

    // Check if this is a practitioner-related event
    // Strapi 5 might send the model as 'api::practitioner.practitioner' or just 'practitioner'
    const isPractitionerModel = model === 'practitioner' || model === 'api::practitioner.practitioner' ||
                               (typeof model === 'string' && model.includes('practitioner'));

    if (!isPractitionerModel) {
      console.log(`Revalidation skipped: Not a practitioner model. Received: ${model}`);
      return NextResponse.json({ message: `Revalidation skipped: Not a practitioner model. Received: ${model}` }, { status: 200 });
    }

    const tagsToRevalidate: string[] = [];

    // Handle specific practitioner if slug is available
    if (entry && entry.slug) {
      const practitionerSlug = entry.slug;
      const practitionerTag = `strapi-practitioner-${practitionerSlug}`;
      tagsToRevalidate.push(practitionerTag);
      console.log(`Adding tag for specific practitioner: ${practitionerTag} (slug: ${practitionerSlug})`);
    }

    // Always revalidate the list of practitioner slugs
    tagsToRevalidate.push('strapi-practitioners-slugs');
    console.log('Adding tag: strapi-practitioners-slugs');

    // Revalidate general practitioner listing pages
    tagsToRevalidate.push('strapi-practitioners-list');
    console.log('Adding tag: strapi-practitioners-list');

    // Revalidate all paginated practitioner list pages (assuming max 10 pages)
    for (let i = 1; i <= 10; i++) {
      tagsToRevalidate.push(`strapi-practitioners-page-${i}`);
    }
    console.log('Adding tags for paginated practitioner pages (1-10)');

    // Revalidate both by tag and by path for maximum compatibility
    if (tagsToRevalidate.length > 0) {
      console.log('Revalidating tags:', tagsToRevalidate.join(', '));

      // Revalidate by tags
      for (const tag of tagsToRevalidate) {
        try {
          revalidateTag(tag);
          console.log(`Successfully revalidated tag: ${tag}`);
        } catch (error) {
          console.error(`Error revalidating tag ${tag}:`, error);
        }
      }

      // Also revalidate by path for good measure
      try {
        revalidatePath('/practitioners');
        console.log('Successfully revalidated path: /practitioners');
      } catch (error) {
        console.error('Error revalidating path /practitioners:', error);
      }

      return NextResponse.json({
        revalidated: true,
        revalidatedTags: tagsToRevalidate,
        revalidatedPaths: ['/practitioners'],
        timestamp: new Date().toISOString()
      });
    } else {
      console.log('No tags to revalidate.');
      return NextResponse.json({
        revalidated: false,
        message: 'No tags to revalidate.'
      });
    }
  } catch (error: any) {
    console.error('Error during practitioners revalidation:', error);
    return NextResponse.json({
      message: 'Error revalidating practitioners',
      error: error.message
    }, { status: 500 });
  }
}

// Optional: GET handler for testing or manual trigger if needed (secure appropriately)
export async function GET(request: NextRequest) {
    if (!REVALIDATE_SECRET) {
        return NextResponse.json({ message: 'Revalidation secret not configured.' }, { status: 500 });
    }

    const secretFromHeader = request.headers.get('X-Revalidate-Secret');
    const requestUrl = new URL(request.url);
    const tag = requestUrl.searchParams.get('tag');

    if (secretFromHeader !== REVALIDATE_SECRET) {
        const secretFromQuery = requestUrl.searchParams.get('secret');
        if (secretFromQuery !== REVALIDATE_SECRET) {
            console.warn('Invalid GET practitioner revalidation attempt: Incorrect or missing X-Revalidate-Secret header/secret query param.');
            return NextResponse.json({ message: 'Invalid token' }, { status: 401 });
        }
    }

    if (!tag) {
        return NextResponse.json({ message: 'Missing tag parameter for GET revalidation' }, { status: 400 });
    }

    try {
        revalidateTag(tag);
        console.log(`Manual practitioner revalidation successful for tag: ${tag}`);
        return NextResponse.json({ revalidated: true, revalidatedTag: tag, timestamp: new Date().toISOString() });
    } catch (error: any) {
        console.error(`Error during manual practitioner revalidation for tag ${tag}:`, error);
        return NextResponse.json({ message: 'Error revalidating practitioner tag', error: error.message }, { status: 500 });
    }
}
