# API Reference Guide for Local Directory Project

This document provides comprehensive API information for building a local directory using Next.js, Supabase, Strapi, Google Maps API, and Stripe.

## Table of Contents
- [General Troubleshooting Principles](#general-troubleshooting-principles)
- [Supabase API](#supabase-api)
- [Strapi API](#strapi-api)
- [Google Maps API](#google-maps-api)
- [Stripe API](#stripe-api)
- [Next.js API](#nextjs-api)

## General Troubleshooting Principles

1. **Environment Variables:** Double-check variable names (`.env.local` vs. production environment settings). Ensure `NEXT_PUBLIC_` prefixes are used *only* for client-safe keys/URLs. Verify no typos or extra spaces. Restart your Next.js dev server after changing `.env.local`.

2. **Browser DevTools:** Use the Network tab to inspect API requests (URLs, headers, status codes, responses). Use the Console tab for JavaScript errors (often indicating SDK issues or missing keys).

3. **Server Logs:** Check your Next.js terminal output (or production hosting logs) for errors originating from API routes or server-side rendering.

4. **Service Dashboards:** Utilize the logging features within Supabase, Stripe, Google Cloud Platform, and Strapi's server logs for backend insights.

5. **Read Error Messages:** Carefully examine error messages; they often pinpoint the exact problem (e.g., "Invalid API Key", "CORS policy", "Row Level Security violation", "Webhook signature mismatch").

## Supabase API

### Authentication
Supabase provides robust authentication features including email/password, magic links, and OAuth providers.

#### Setup
```bash
npm install @supabase/supabase-js
```

Add your Supabase URL and Anon Key to `.env.local`:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

#### Email and Password Authentication
```javascript
const { error } = await supabase.auth.signIn({ email, password });
if (error) throw error;
```

#### OAuth Authentication
```javascript
const { error } = await supabase.auth.signIn({ provider: 'google' });
if (error) throw error;
```

#### Session Management
Use the `@supabase/ssr` package for server-side session management:
```javascript
import { createMiddlewareSupabaseClient } from '@supabase/auth-helpers-nextjs';

export async function middleware(req) {
  const supabase = createMiddlewareSupabaseClient({ req });
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) return NextResponse.redirect('/login');
}
```

### CRUD Operations

#### Create
```javascript
const { data, error } = await supabase.from('todos').insert([{ name: 'Task 1', completed: false }]);
if (error) throw error;
```

#### Read
```javascript
const { data, error } = await supabase.from('todos').select('*');
if (error) throw error;
```

#### Update
```javascript
const { data, error } = await supabase.from('todos').update({ completed: true }).eq('id', 1);
if (error) throw error;
```

#### Delete
```javascript
const { data, error } = await supabase.from('todos').delete().eq('id', 1);
if (error) throw error;
```

### Real-Time Features
Supabase supports real-time updates using Postgres' `LISTEN/NOTIFY` feature.

```javascript
const channel = supabase.channel('public:todos');
channel.on('postgres_changes', { event: '*', schema: 'public', table: 'todos' }, (payload) => {
  console.log('Change received!', payload);
}).subscribe();
```

### Core Credentials

- **Project URL (`NEXT_PUBLIC_SUPABASE_URL`):**
  - **Format:** `https://<your-project-ref>.supabase.co`
  - **Purpose:** Base endpoint for all API calls. Used client-side and server-side.
  - **Troubleshooting:**
    - Verify the `<your-project-ref>` matches your Supabase project dashboard.
    - Ensure `https://` prefix is present.
    - Check for typos.

- **Anon Key (`NEXT_PUBLIC_SUPABASE_ANON_KEY`):**
  - **Format:** Long JWT string (e.g., `eyJ...`)
  - **Purpose:** Public key for client-side requests. Respects Row Level Security (RLS). Safe to expose.
  - **Troubleshooting:**
    - Verify the key exactly matches the `anon public` key in Supabase settings (Project Settings > API).
    - **Common Issue:** If client-side requests fail (fetching data, auth) but server-side works (using service key), the issue is likely RLS policies blocking the `anon` key, *not* the key itself being wrong. Check your RLS policies in the Supabase SQL Editor or UI.

- **Service Role Key (`SUPABASE_SERVICE_ROLE_KEY`):**
  - **Format:** Long JWT string (e.g., `eyJ...`)
  - **Purpose:** Secret key for server-side operations (Next.js API routes, `getServerSideProps`). Bypasses RLS. **Keep this secret.**
  - **Troubleshooting:**
    - Verify the key exactly matches the `service_role secret` key in Supabase settings.
    - Ensure this variable is *not* prefixed with `NEXT_PUBLIC_` and is only used server-side.
    - If server-side operations fail, check server logs for specific errors from the Supabase client library.

### SDK/Client Initialization

```javascript
// Client-side initialization
import { createClient } from '@supabase/supabase-js';
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Server-side initialization
import { createClient } from '@supabase/supabase-js';
// Option 1: Use Anon key if RLS allows
// const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
// Option 2: Use Service Role key to bypass RLS (use with caution)
const supabaseAdmin = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
```

### Common Connection Issues

- **401 Unauthorized / RLS Violation:** Usually means the RLS policies are blocking the action for the key being used (often the `anon` key). Review and test your policies.
- **Network Errors / Failed to Fetch:** Could be an incorrect Supabase URL, Supabase service outage, or local network issues.
- **Incorrect Data Returned/Missing Data:** Check your query logic (`select()`, `filter()`, etc.). Could also be RLS hiding rows.

### Troubleshooting Tips
1. **Session Expiry**: Use the `@supabase/ssr` package to refresh sessions automatically.
2. **Environment Variables**: Ensure `NEXT_PUBLIC_SUPABASE_URL` and `NEXT_PUBLIC_SUPABASE_ANON_KEY` are correctly set.
3. **Middleware Errors**: Verify the `middleware.ts` file is correctly implemented for session handling.
4. **Where to Find Logs:** Supabase Dashboard > Logs > API / Postgres Logs.

## Strapi API

### REST API
Strapi automatically generates REST API endpoints for each content type created in the Content-Type Builder.

#### Key Endpoints
- `GET /api/:pluralApiId` - Fetch all entries
- `POST /api/:pluralApiId` - Create a new entry
- `GET /api/:pluralApiId/:documentId` - Fetch a specific entry
- `PUT /api/:pluralApiId/:documentId` - Update an entry
- `DELETE /api/:pluralApiId/:documentId` - Delete an entry

#### Query Parameters
Use parameters like `filters`, `sort`, `pagination`, and `populate` to refine results:
```
GET /api/articles?filters[title][$contains]=hello&sort[0]=title:asc&pagination[page]=1&pagination[pageSize]=10
```

### GraphQL API
Strapi provides a GraphQL plugin that adds a GraphQL endpoint to your application.

#### Setup
Install the GraphQL plugin:
```bash
npm run strapi install graphql
```

Access the GraphQL playground at `/graphql`.

#### Example Query
```graphql
query {
  articles {
    data {
      id
      attributes {
        title
        content
      }
    }
  }
}
```

### Authentication
Strapi supports multiple authentication methods:

#### API Tokens
Create API tokens in the Strapi admin panel and use them in your requests:
```javascript
fetch('http://localhost:1337/api/articles', {
  headers: {
    'Authorization': 'Bearer YOUR_API_TOKEN'
  }
})
```

#### JWT Authentication
For user authentication:
```javascript
// Login
const response = await fetch('http://localhost:1337/api/auth/local', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ identifier: '<EMAIL>', password: 'password' })
});
const data = await response.json();
const jwt = data.jwt;

// Use JWT in subsequent requests
fetch('http://localhost:1337/api/articles', {
  headers: {
    'Authorization': `Bearer ${jwt}`
  }
})
```

### Custom Endpoints
Create custom API endpoints to extend Strapi's functionality:

1. Create a controller in `src/api/custom/controllers/custom.js`:
```javascript
module.exports = {
  async customAction(ctx) {
    return { message: 'Custom endpoint working!' };
  }
};
```

2. Define the route in `src/api/custom/routes/custom.js`:
```javascript
module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/custom-endpoint',
      handler: 'custom.customAction',
    }
  ]
};
```

### Core Credentials

- **API Base URL (`NEXT_PUBLIC_STRAPI_API_URL` or `STRAPI_API_URL`):**
  - **Format:** `http://localhost:1337` (local dev) or `https://your-strapi-domain.com` (production).
  - **Purpose:** The root URL where your Strapi API is accessible.
  - **Troubleshooting:**
    - Verify the URL is correct and the Strapi server is running.
    - Check if `/api` needs to be appended to the URL in your fetch requests.
    - **CORS:** If making requests from the browser to a different domain, configure CORS origins in Strapi: `./config/middlewares.js` -> `strapi::cors` settings.

- **API Token (`NEXT_PUBLIC_STRAPI_API_TOKEN` or `STRAPI_API_TOKEN`):**
  - **Format:** Long alphanumeric string.
  - **Purpose:** Authenticates requests to the Strapi API. Permissions are tied to the token.
  - **Troubleshooting:**
    - Verify the token matches the one generated in Strapi Admin > Settings > API Tokens.
    - **Permissions (Very Common Issue):** If you get a 403 Forbidden error, the token lacks permission for the specific action on the specific content type.
    - Ensure the token is sent correctly in the request header, usually `Authorization: Bearer <YOUR_TOKEN>`.

### API Interaction

```javascript
const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL;
const strapiToken = process.env.NEXT_PUBLIC_STRAPI_API_TOKEN; // Assuming public read access

const res = await fetch(`${strapiUrl}/api/listings`, { // Note the /api prefix might be needed
  headers: {
    'Authorization': `Bearer ${strapiToken}`,
  },
});
if (!res.ok) {
   // Handle error, check res.status
   console.error("Strapi fetch failed:", res.status, await res.text());
}
const data = await res.json();
```

### Common Connection Issues

- **403 Forbidden:** Almost always a permissions issue with the API token or the Public role in Strapi Settings > Roles.
- **404 Not Found:** Incorrect URL path or the requested content doesn't exist or isn't published.
- **CORS Errors:** Requires Strapi server configuration.
- **500 Internal Server Error:** Problem on the Strapi server itself. Check Strapi's console logs for detailed errors.
- **Where to Find Logs:** Strapi server console/terminal output.

## Google Maps API

### Maps JavaScript API
The Maps JavaScript API allows you to embed interactive maps into web applications.

#### Integration with Next.js
```javascript
import Script from 'next/script';

export default function MapComponent() {
  return (
    <>
      <Script
        src={`https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=places`}
        strategy="lazyOnload"
      />
      <div id="map" style={{ height: '500px', width: '100%' }}></div>
    </>
  );
}
```

#### Initialize Map
```javascript
useEffect(() => {
  const map = new google.maps.Map(document.getElementById('map'), {
    center: { lat: 37.7749, lng: -122.4194 },
    zoom: 12,
  });

  // Add a marker
  new google.maps.Marker({
    position: { lat: 37.7749, lng: -122.4194 },
    map,
    title: 'San Francisco'
  });
}, []);
```

### Geocoding API
The Geocoding API converts addresses into geographic coordinates and vice versa.

#### Geocoding Example
```javascript
// Server-side API route
export default async function handler(req, res) {
  const { address } = req.query;
  const response = await fetch(
    `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${process.env.GOOGLE_MAPS_API_KEY}`
  );
  const data = await response.json();
  res.status(200).json(data);
}
```

#### Reverse Geocoding Example
```javascript
// Server-side API route
export default async function handler(req, res) {
  const { lat, lng } = req.query;
  const response = await fetch(
    `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${process.env.GOOGLE_MAPS_API_KEY}`
  );
  const data = await response.json();
  res.status(200).json(data);
}
```

### Places API
The Places API provides detailed information about places, including names, addresses, and reviews.

#### Nearby Search Example
```javascript
// Server-side API route
export default async function handler(req, res) {
  const { lat, lng, radius, type } = req.query;
  const response = await fetch(
    `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=${radius}&type=${type}&key=${process.env.GOOGLE_MAPS_API_KEY}`
  );
  const data = await response.json();
  res.status(200).json(data);
}
```

#### Autocomplete Integration
```javascript
import { useEffect, useRef } from 'react';
import Script from 'next/script';

export default function PlacesAutocomplete() {
  const inputRef = useRef(null);

  useEffect(() => {
    if (window.google && inputRef.current) {
      const autocomplete = new google.maps.places.Autocomplete(inputRef.current);
      autocomplete.addListener('place_changed', () => {
        const place = autocomplete.getPlace();
        console.log(place);
      });
    }
  }, []);

  return (
    <>
      <Script
        src={`https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=places`}
        strategy="lazyOnload"
      />
      <input ref={inputRef} type="text" placeholder="Search for a place" />
    </>
  );
}
```

### Core Credentials

- **API Key (`NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`):**
  - **Format:** Starts with `AIza...`
  - **Purpose:** Authenticates most Google Maps Platform requests.
  - **Troubleshooting:**
    - Verify the key exactly matches the one in Google Cloud Console > APIs & Services > Credentials.
    - **API Key Restrictions (Crucial & Common Issue):**
      - *HTTP Referrers:* For client-side usage, you MUST restrict the key to your web domains. Add `yourdomain.com/*` and `localhost:<your_port>/*` (for development).
      - *API Restrictions:* Restrict the key to only the APIs you need.
    - **Billing Enabled:** Ensure the Google Cloud Project linked to the API key has billing enabled.
    - **API Enabled:** Ensure the specific APIs are enabled in Google Cloud Console > APIs & Services > Library.

### SDK/API Usage

- **Client-Side (Maps JavaScript API):**
```html
<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap&libraries=places" async defer></script>
```

- **Server-Side (Geocoding, Places API):**
```javascript
// Using Node.js client library in an API route
import { Client } from "@googlemaps/google-maps-services-js";
const client = new Client({});
const response = await client.geocode({
  params: {
    address: '1600 Amphitheatre Parkway, Mountain View, CA',
    key: process.env.GOOGLE_MAPS_API_KEY // Use server-side variable if key is restricted by IP
  },
  timeout: 1000, // milliseconds
});
```

### Common Connection Issues

- **Map Doesn't Load / Gray Box:** Check browser console! Likely causes: Invalid API Key, Billing Not Enabled, API Not Enabled, Incorrect HTTP Referrer restriction.
- **"This page can't load Google Maps correctly." / Watermarks:** Usually key-related issues or billing. Check console.
- **Geocoding/Places API returning `REQUEST_DENIED`:** Invalid API key, API not enabled for the key, key restriction issue, or quota exceeded.
- **`ZERO_RESULTS`:** The API worked, but no results were found for the query.
- **Quota Exceeded (`OVER_QUERY_LIMIT`):** You've made too many requests. Check usage in Google Cloud Console.
- **Where to Find Logs:** Browser Developer Console (Network, Console), Google Cloud Console (APIs & Services > Dashboard/Credentials > Metrics/Quotas).

### API Keys
API keys are required to authenticate requests to Google Maps APIs.

#### Steps to Create an API Key
1. Go to the [Google Cloud Console](https://console.cloud.google.com/).
2. Navigate to "APIs & Services" > "Credentials."
3. Click "Create Credentials" > "API Key."
4. Restrict the API key to specific APIs and IP addresses for security.

#### Usage Limits
- Maps JavaScript API: 28,000 map loads per month (free tier)
- Geocoding API: 50 requests per second
- Places API: 1,000 requests per day (free tier)

## Stripe API

### Authentication
Stripe uses API keys to authenticate requests.

#### Setup
```bash
npm install stripe @stripe/stripe-js
```

Add your Stripe keys to `.env.local`:
```
NEXT_PUBLIC_STRIPE_PUBLIC_KEY=your_publishable_key
STRIPE_SECRET_KEY=your_secret_key
```

Initialize Stripe on the server:
```javascript
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: '2023-10-16' });
```

### Creating Payments

#### Stripe Checkout
Create a server-side API route to initiate a checkout session:
```javascript
// pages/api/checkout-sessions/create.js
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: { name: 'Product Name' },
            unit_amount: 1000, // Price in cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${req.headers.origin}/success`,
      cancel_url: `${req.headers.origin}/cancel`,
    });
    res.status(200).json({ id: session.id });
  } else {
    res.setHeader('Allow', 'POST');
    res.status(405).end('Method Not Allowed');
  }
}
```

Redirect to Stripe Checkout from the client:
```javascript
import { loadStripe } from '@stripe/stripe-js';
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY);

const handleCheckout = async () => {
  const stripe = await stripePromise;
  const response = await fetch('/api/checkout-sessions/create', { method: 'POST' });
  const session = await response.json();
  await stripe.redirectToCheckout({ sessionId: session.id });
};
```

### Subscriptions
Stripe supports subscription-based billing through its Billing API.

#### Create a Subscription
```javascript
// pages/api/subscriptions/create.js
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      line_items: [{ price: 'price_12345', quantity: 1 }],
      success_url: `${req.headers.origin}/success`,
      cancel_url: `${req.headers.origin}/cancel`,
    });
    res.status(200).json({ id: session.id });
  } else {
    res.setHeader('Allow', 'POST');
    res.status(405).end('Method Not Allowed');
  }
}
```

### Handling Webhooks
Webhooks allow Stripe to notify your application about events such as successful payments or subscription updates.

#### Webhook Handler
```javascript
// pages/api/webhooks/index.js
import Stripe from 'stripe';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Disable body parsing for webhook endpoint
export const config = {
  api: {
    bodyParser: false,
  },
};

// Helper to parse the raw request body
const buffer = async (req) => {
  const chunks = [];
  for await (const chunk of req) {
    chunks.push(typeof chunk === 'string' ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
};

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const buf = await buffer(req);
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    let event;
    try {
      event = stripe.webhooks.constructEvent(buf, sig, endpointSecret);
    } catch (err) {
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object;
        console.log('Payment successful:', session);
        // Update your database here
        break;
      case 'invoice.payment_succeeded':
        const invoice = event.data.object;
        console.log('Subscription payment succeeded:', invoice);
        // Update subscription status in your database
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    res.json({ received: true });
  } else {
    res.setHeader('Allow', 'POST');
    res.status(405).end('Method Not Allowed');
  }
}
```

### Core Credentials

- **Publishable Key (`NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`):**
  - **Format:** `pk_test_...` or `pk_live_...`
  - **Purpose:** Used client-side to tokenize payment details safely.
  - **Troubleshooting:**
    - Verify key matches Stripe Dashboard (Developers > API Keys).
    - Ensure `NEXT_PUBLIC_` prefix is used.
    - Check if you're using the correct mode (test vs. live) matching your backend setup.

- **Secret Key (`STRIPE_SECRET_KEY`):**
  - **Format:** `sk_test_...` or `sk_live_...`
  - **Purpose:** Used server-side to make authenticated calls. **Keep absolutely secret.**
  - **Troubleshooting:**
    - Verify key matches Stripe Dashboard.
    - Ensure this is never exposed client-side (no `NEXT_PUBLIC_` prefix).
    - Check if using correct mode (test vs. live) matching the Publishable Key used on the client.

- **Webhook Signing Secret (`STRIPE_WEBHOOK_SECRET`):**
  - **Format:** `whsec_...`
  - **Purpose:** Used server-side to verify incoming webhook events are genuinely from Stripe.
  - **Troubleshooting:**
    - Verify secret matches the specific webhook endpoint configuration in Stripe Dashboard.
    - **Common Issue: Signature Verification Failed:**
      - Incorrect secret in your environment variables.
      - Your server code is parsing the request body before Stripe's library gets the raw buffer.

### Common Connection Issues

- **401 Unauthorized (Server-Side):** Incorrect Secret Key, or using Test key for Live objects (or vice-versa).
- **400 Bad Request / Invalid Parameter (Server-Side):** Check the parameters you're sending against the Stripe API documentation.
- **Stripe.js Errors (Client-Side):** Incorrect Publishable Key, network issues loading Stripe.js, errors using Elements.
- **Webhook Failures:** Signature verification failed, endpoint returning non-200 status codes, endpoint taking too long to respond.
- **Test vs. Live Mode Mismatches:** Trying to use a test card with live keys, or a test `price_id` with live keys, etc.
- **Where to Find Logs:** Stripe Dashboard (Developers > API Logs, Developers > Events, Developers > Webhooks), Your Server Logs, Browser Developer Console.

### Troubleshooting
1. **Invalid API Key**: Ensure the correct API keys are used for test and live environments.
2. **Webhook Signature Verification Fails**: Verify the raw request body is passed to `stripe.webhooks.constructEvent`.
3. **CORS Errors**: Configure your Next.js API routes to handle CORS properly.

## Next.js API

### API Routes
Next.js provides a powerful way to create server-side API endpoints directly within your application.

#### Basic API Route (Pages Router)
```javascript
// pages/api/hello.js
export default function handler(req, res) {
  res.status(200).json({ message: 'Hello, world!' });
}
```

#### Basic API Route (App Router)
```javascript
// app/api/hello/route.js
export async function GET() {
  return new Response(JSON.stringify({ message: 'Hello, world!' }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  });
}
```

#### Dynamic API Routes
```javascript
// pages/api/users/[id].js
export default function handler(req, res) {
  const { id } = req.query;
  res.status(200).json({ id, name: `User ${id}` });
}

// app/api/users/[id]/route.js
export async function GET(request, { params }) {
  const { id } = params;
  return new Response(JSON.stringify({ id, name: `User ${id}` }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  });
}
```

### Server-Side Rendering (SSR)
Server-Side Rendering allows you to generate HTML on the server for each request.

#### Using getServerSideProps (Pages Router)
```javascript
// pages/index.js
export async function getServerSideProps(context) {
  const res = await fetch('https://api.example.com/data');
  const data = await res.json();
  return { props: { data } };
}

export default function Home({ data }) {
  return (
    <div>
      <h1>Data from API</h1>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
}
```

#### Using Server Components (App Router)
```javascript
// app/page.js
async function getData() {
  const res = await fetch('https://api.example.com/data');
  if (!res.ok) throw new Error('Failed to fetch data');
  return res.json();
}

export default async function Home() {
  const data = await getData();
  return (
    <div>
      <h1>Data from API</h1>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
}
```

### API Integration Best Practices
1. **Use Environment Variables**: Store API keys and sensitive data in `.env.local`.
2. **Error Handling**: Implement try-catch blocks to handle API errors gracefully.
3. **Caching**: Use SWR or React Query for client-side data fetching with caching.
4. **Rate Limiting**: Implement rate limiting for your API routes to prevent abuse.

### Troubleshooting
1. **API Route Not Working**: Ensure the file is in the correct directory and has the correct export.
2. **CORS Errors**: Use a CORS middleware or set appropriate headers in the response.
3. **Data Not Loading**: Check the API endpoint and ensure it's accessible from the server.
4. **Deployment Issues**: Ensure the deployment platform supports serverless functions.

## Dynamic APIs are Asynchronous - Next.js
Learn more about why accessing certain APIs synchronously now warns.

Why This Warning Occurred
Somewhere in your code you used an API that opts into dynamic rendering.

Dynamic APIs are:

The params and searchParams props that get provided to pages, layouts, metadata APIs, and route handlers.
cookies(), draftMode(), and headers() from next/headers
In Next 15, these APIs have been made asynchronous. You can read more about this in the Next.js 15 Upgrade Guide.

For example, the following code will issue a warning:

app/[id]/page.js

function Page({ params }) {
  // direct access of `params.id`.
  return <p>ID: {params.id}</p>
}
This also includes enumerating (e.g. {...params}, or Object.keys(params)) or iterating over the return value of these APIs (e.g. [...headers()] or for (const cookie of cookies()), or explicitly with cookies()[Symbol.iterator]()).

In the version of Next.js that issued this warning, access to these properties is still possible directly but will warn. In future versions, these APIs will be async and direct access will not work as expected.

Possible Ways to Fix It
The next-async-request-api codemod can fix many of these cases automatically:

Terminal

$ npx @next/codemod@canary next-async-request-api .
The codemod cannot cover all cases, so you may need to manually adjust some code.

If the warning occurred on the Server (e.g. a route handler, or a Server Component), you must await the dynamic API to access its properties:

app/[id]/page.js

async function Page({ params }) {
  // asynchronous access of `params.id`.
  const { id } = await params
  return <p>ID: {id}</p>
}
If the warning occurred in a synchronous component (e.g. a Client component), you must use React.use() to unwrap the Promise first:

app/[id]/page.js

'use client'
import * as React from 'react'
 
function Page({ params }) {
  // asynchronous access of `params.id`.
  const { id } = React.use(params)
  return <p>ID: {id}</p>
}
Unmigratable Cases
If Next.js codemod found anything that is not able to be migrated by the codemod, it will leave a comment with @next-codemod-error prefix and the suggested action, for example: In this case, you need to manually await the call to cookies(), and change the function to async. Then refactor the usages of the function to be properly awaited:


export function MyCookiesComponent() {
  const c =
    /* @next-codemod-error Manually await this call and refactor the function to be async */
    cookies()
  return c.get('name')
}
Enforced Migration with Linter
If you didn't address the comments that starting with @next-codemod-error left by the codemod, Next.js will error in both dev and build to enforce you to address the issues. You can review the changes and follow the suggestion in the comments. You can either make the necessary changes and remove the comment, or replace the comment prefix @next-codemod-error with @next-codemod-ignore If there's no action to be taken, the comment prefix @next-codemod-ignore will bypass the build error.


- /* @next-codemod-error <suggested message> */
+ /* @next-codemod-ignore */